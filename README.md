# 🎨 TallyCRM SaaS - Frontend-First CRM for Tally Resellers

## 📋 Project Overview

A comprehensive SaaS-based CRM platform designed specifically for Tally Software Resellers to manage their complete business operations including customer relationships, AMC contracts, service calls, sales tracking, and team management with role-based access control.

**🎨 Frontend-First Development Approach**: This project follows a Frontend-First methodology for rapid UI development, early user feedback, and efficient parallel development between frontend and backend teams.

## 🚀 Tech Stack

- **Frontend**: ReactJS + Vite + Bootstrap 5
- **Backend**: Node.js + Express.js
- **Database**: PostgreSQL with Multi-Tenant Architecture
- **Authentication**: JWT-based with RBAC
- **APIs**: RESTful APIs with mock-to-real transition
- **Additional**: Google Maps API, Excel Import/Export (SheetJS)

## 🏗️ Project Structure

```
tallycrm/
├── frontend/                 # React + Vite frontend application
├── backend/                  # Node.js + Express backend API
├── database/                 # Database scripts and migrations
├── docs/                     # Project documentation
├── shared/                   # Shared utilities and types
├── tests/                    # Integration and E2E tests
├── deployment/               # Deployment configurations
└── Instructions/             # Development instructions and tasks
```

## 🔧 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v14 or higher)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tallycrm
   ```

2. **Install dependencies**
   ```bash
   # Install frontend dependencies
   cd frontend
   npm install

   # Install backend dependencies
   cd ../backend
   npm install
   ```

3. **Environment Setup**
   ```bash
   # Copy environment files
   cp frontend/.env.example frontend/.env
   cp backend/.env.example backend/.env
   ```

4. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb tallycrm_dev
   createdb tallycrm_test

   # Run migrations and seed data (includes admin user creation)
   cd backend
   npm run migrate
   npm run seed:all
   ```

5. **Start Development Servers**
   ```bash
   # Terminal 1: Start backend server
   cd backend
   npm run dev

   # Terminal 2: Start frontend server
   cd frontend
   npm run dev
   ```

## 📚 Documentation

### 📖 User Documentation
- [🚀 Quick Start Guide](docs/user-guides/QUICK_START_GUIDE.md) - Get up and running in 5 minutes
- [📖 User Guide](docs/user-guides/USER_GUIDE.md) - Complete user manual
- [🎯 Features Overview](docs/user-guides/FEATURES_OVERVIEW.md) - All system capabilities
- [📋 Quick Reference](docs/user-guides/QUICK_REFERENCE_GUIDE.md) - Fast access to key information

### 🔧 Technical Documentation
- [📋 Development Instructions](Instructions/main.md) - Development workflow and tasks
- [🎨 Frontend-First Approach](Instructions/FRONTEND_FIRST_APPROACH.md) - Development methodology
- [🏗️ Project Setup](Instructions/PROJECT_SETUP.md) - Initial project configuration
- [🔧 Development Setup](Instructions/DEVELOPMENT_SETUP.md) - Local development environment
- [🗄️ Database Design](Instructions/DATABASE_DESIGN.md) - Database schema and design
- [📡 API Documentation](Instructions/API_DOCUMENTATION.md) - API endpoints and usage

### 🚀 Deployment & Operations
- [🚀 Deployment Guide](docs/deployment/DEPLOYMENT.md) - Production deployment instructions
- [⚙️ Production Setup](docs/deployment/PRODUCTION_SETUP.md) - Production environment configuration
- [🗄️ Database Migration](docs/deployment/DATABASE_MIGRATION.md) - Database migration procedures

### 🆘 Support & Troubleshooting
- [🆘 Troubleshooting Guide](docs/troubleshooting/TROUBLESHOOTING_GUIDE.md) - Common issues and solutions
- [📋 Complete Documentation Index](docs/DOCUMENTATION_INDEX.md) - Navigate all documentation

## 🎯 Key Features

- **Frontend-First Development** with mock APIs for rapid UI development
- **Multi-Tenant Architecture** for multiple Tally resellers
- **Role-Based Access Control (RBAC)** with custom permissions
- **Complete Customer Management** with Address Book
- **AMC Contract Management** with renewal tracking
- **Online & Onsite Service Call Management**
- **Sales Tracking** with Referral Management
- **Comprehensive Master Data Management**
- **Advanced Reporting and Analytics**
- **Excel Import/Export** capabilities
- **Google Maps integration** for location services

## 🔄 Development Workflow

This project follows a **Frontend-First** development approach:

1. **Phase 1**: Foundation & Mock APIs (Weeks 1-2)
2. **Phase 2**: Frontend Development (Weeks 2-4)
3. **Phase 3**: Backend API Implementation (Weeks 4-7)
4. **Phase 4**: Integration & Quality (Weeks 7-9)
5. **Phase 5**: Production Deployment (Weeks 9-10)

## 🤝 Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Run tests: `npm test`
4. Submit a pull request

## 📄 License

This project is proprietary software developed for Tally Software Resellers.

---

**Total Estimated Timeline**: 10 weeks
**Total Estimated Hours**: 924 hours
**Recommended Team Size**: 3-4 developers