import React from 'react';
import { cn } from '../../../utils/helpers';

const Row = React.forwardRef(({
  children,
  className,
  gutter = 'default',
  align = 'stretch',
  justify = 'start',
  ...props
}, ref) => {
  const baseClasses = 'flex flex-wrap';

  const gutters = {
    none: '',
    sm: '-mx-1',
    default: '-mx-3',
    lg: '-mx-4',
    xl: '-mx-6',
  };

  const alignments = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
    baseline: 'items-baseline',
  };

  const justifications = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  return (
    <div
      ref={ref}
      className={cn(
        baseClasses,
        gutters[gutter],
        alignments[align],
        justifications[justify],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

const Col = React.forwardRef(({
  children,
  className,
  xs,
  sm,
  md,
  lg,
  xl,
  auto = false,
  gutter = 'default',
  ...props
}, ref) => {
  const baseClasses = 'flex-shrink-0 w-full';

  const gutters = {
    none: '',
    sm: 'px-1',
    default: 'px-3',
    lg: 'px-4',
    xl: 'px-6',
  };

  // Generate responsive classes
  const responsiveClasses = [];

  if (auto) {
    responsiveClasses.push('w-auto flex-grow-0 flex-shrink-0');
  }

  if (xs) {
    responsiveClasses.push(`w-${xs}/12`);
  }

  if (sm) {
    responsiveClasses.push(`sm:w-${sm}/12`);
  }

  if (md) {
    responsiveClasses.push(`md:w-${md}/12`);
  }

  if (lg) {
    responsiveClasses.push(`lg:w-${lg}/12`);
  }

  if (xl) {
    responsiveClasses.push(`xl:w-${xl}/12`);
  }

  return (
    <div
      ref={ref}
      className={cn(
        baseClasses,
        gutters[gutter],
        responsiveClasses,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});

Row.displayName = 'Row';
Col.displayName = 'Col';

export default Row;
export { Col };
