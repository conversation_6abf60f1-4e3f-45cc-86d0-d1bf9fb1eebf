import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const ServiceCall = sequelize.define('ServiceCall', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    call_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Auto-generated service call number',
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    contact_person_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_contacts',
        key: 'id',
      },
    },
    tss_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_tss',
        key: 'id',
      },
    },
    amc_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_amc',
        key: 'id',
      },
    },
    call_type: {
      type: DataTypes.ENUM('online', 'onsite', 'phone', 'email'),
      allowNull: false,
      defaultValue: 'online',
    },
    call_billing_type: {
      type: DataTypes.ENUM('free_call', 'amc_call', 'per_call'),
      allowNull: true,
      comment: 'Call billing type for the service call (free_call, amc_call, per_call)',
    },
    type_of_call_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'type_of_calls',
        key: 'id',
      },
      comment: 'Reference to TypeOfCall master data (legacy)',
    },
    products_issues_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'products_issues',
        key: 'id',
      },
      comment: 'Reference to ProductsIssues for specific issue types',
    },
    online_call_type_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'online_call_types',
        key: 'id',
      },
      comment: 'Reference to OnlineCallType for online service calls',
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
    },
    nature_of_issue_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'nature_of_issues',
        key: 'id',
      },
    },
    status_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'call_statuses',
        key: 'id',
      },
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
    },
    assigned_to: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: true, // Made optional
      validate: {
        len: [0, 200], // Allow empty strings
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true, // Made optional
    },
    customer_reported_issue: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Issue as reported by customer',
    },
    actual_issue_found: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Actual issue found during investigation',
    },
    solution_provided: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Solution provided to resolve the issue',
    },
    call_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    scheduled_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Scheduled date for onsite visits',
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When work on the call started',
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the call was completed',
    },
    closed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the call was closed',
    },
    estimated_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Estimated hours to resolve',
    },
    actual_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Actual hours spent',
    },
    billable_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Billable hours',
    },
    hourly_rate: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      comment: 'Hourly rate for billing',
    },
    service_charges: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Service charges amount',
    },
    travel_charges: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Travel charges for onsite visits',
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Total billable amount',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this call is billable',
    },
    is_under_amc: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this call is covered under AMC',
    },
    customer_satisfaction: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5,
      },
      comment: 'Customer satisfaction rating (1-5)',
    },
    customer_feedback: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Customer feedback',
    },
    internal_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Internal notes not visible to customer',
    },
    follow_up_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    follow_up_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    follow_up_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    attachments: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of attachment file paths',
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Custom fields for additional data',
    },
    // Additional fields for enhanced service management
    contact_number: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Contact number for this service call',
    },
    tally_serial_number: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally serial number for this service',
    },
    company_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Company name (auto-fetched from customer)',
    },
    designation: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Customer contact designation',
    },
    tally_version: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally version (Gold/Silver)',
    },
    tss_status: {
      type: DataTypes.ENUM('active', 'inactive'),
      allowNull: true,
      comment: 'TSS status',
    },
    tss_expiry: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'TSS expiry date',
    },
    service_location: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Service location address',
    },
    location_coordinates: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'GPS coordinates {lat, lng}',
    },
    google_maps_link: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Google Maps link for navigation',
    },
    call_end_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Call end time (can be fixed or auto-calculated)',
    },
    is_end_time_fixed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether end time is fixed by user',
    },
    charging_type: {
      type: DataTypes.ENUM('hourly', 'fixed'),
      allowNull: true,
      defaultValue: 'fixed',
      comment: 'Service charging type',
    },
    hourly_rate_amount: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      comment: 'Hourly rate amount if charging type is hourly',
    },
    executive_remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Executive remarks and recommendations',
    },
    customer_feedback_type: {
      type: DataTypes.ENUM('happy', 'sad', 'satisfied', 'not_satisfied'),
      allowNull: true,
      comment: 'Customer feedback type',
    },
    customer_feedback_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Customer feedback comments',
    },
    booking_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Booking date for online calls',
    },
    mobile_number_2: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Secondary mobile number',
    },
    time_tracking_history: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of time tracking entries for paused/resumed calls',
    },
    total_time_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Total time spent on the call in minutes',
    },
    total_time_seconds: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Total time spent on the call in seconds (more precise)',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false,
      comment: 'Service call creation timestamp with date and time',
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false,
      comment: 'Service call last update timestamp',
    },
  }, {
    tableName: 'service_calls',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['call_number'],
        unique: true,
      },
      {
        fields: ['customer_id'],
      },
      {
        fields: ['call_type'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['status_id'],
      },
      {
        fields: ['assigned_to'],
      },
      {
        fields: ['created_by'],
      },
      {
        fields: ['call_date'],
      },
      {
        fields: ['scheduled_date'],
      },
      {
        fields: ['is_billable'],
      },
      {
        fields: ['is_under_amc'],
      },
      {
        fields: ['follow_up_required'],
      },
      {
        fields: ['follow_up_date'],
      },
      // Performance optimization indexes for search
      {
        fields: ['subject'],
        name: 'service_calls_subject_idx',
      },
      {
        fields: ['description'],
        name: 'service_calls_description_idx',
      },
      {
        fields: ['tally_serial_number'],
        name: 'service_calls_tally_serial_idx',
      },
      {
        fields: ['call_billing_type'],
        name: 'service_calls_billing_type_idx',
      },
      // Composite indexes for common filter combinations
      {
        fields: ['tenant_id', 'status_id'],
        name: 'service_calls_tenant_status_idx',
      },
      {
        fields: ['tenant_id', 'customer_id'],
        name: 'service_calls_tenant_customer_idx',
      },
      {
        fields: ['tenant_id', 'assigned_to'],
        name: 'service_calls_tenant_assigned_idx',
      },
      {
        fields: ['tenant_id', 'call_date'],
        name: 'service_calls_tenant_date_idx',
      },
      {
        fields: ['tenant_id', 'scheduled_date'],
        name: 'service_calls_tenant_scheduled_idx',
      },
      {
        fields: ['tenant_id', 'priority'],
        name: 'service_calls_tenant_priority_idx',
      },
      {
        fields: ['tenant_id', 'call_billing_type'],
        name: 'service_calls_tenant_billing_idx',
      },
      // Composite index for overdue queries
      {
        fields: ['tenant_id', 'scheduled_date', 'status_id'],
        name: 'service_calls_overdue_idx',
      },
    ],
  });

  // Instance methods
  ServiceCall.prototype.getDuration = function() {
    if (!this.started_at || !this.completed_at) return null;
    const diffTime = new Date(this.completed_at) - new Date(this.started_at);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.isOverdue = function() {
    if (!this.scheduled_date) return false;
    return new Date(this.scheduled_date) < new Date() && !this.completed_at;
  };

  ServiceCall.prototype.getResponseTime = function() {
    if (!this.started_at) return null;
    const diffTime = new Date(this.started_at) - new Date(this.call_date);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.getResolutionTime = function() {
    if (!this.completed_at) return null;
    const diffTime = new Date(this.completed_at) - new Date(this.call_date);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.getPriorityColor = function() {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      critical: '#dc3545',
    };
    return colors[this.priority] || '#6c757d';
  };

  ServiceCall.prototype.calculateTotalAmount = function() {
    let total = 0;
    if (this.service_charges) total += parseFloat(this.service_charges);
    if (this.travel_charges) total += parseFloat(this.travel_charges);
    if (this.billable_hours && this.hourly_rate) {
      total += parseFloat(this.billable_hours) * parseFloat(this.hourly_rate);
    }
    return Math.round(total * 100) / 100;
  };

  // Associations
  ServiceCall.associate = function(models) {
    ServiceCall.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    ServiceCall.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    ServiceCall.belongsTo(models.CustomerContact, {
      foreignKey: 'contact_person_id',
      as: 'contactPerson',
    });

    ServiceCall.belongsTo(models.CustomerTSS, {
      foreignKey: 'tss_id',
      as: 'tss',
    });

    ServiceCall.belongsTo(models.CustomerAMC, {
      foreignKey: 'amc_id',
      as: 'amc',
    });

    ServiceCall.belongsTo(models.NatureOfIssue, {
      foreignKey: 'nature_of_issue_id',
      as: 'natureOfIssue',
    });

    ServiceCall.belongsTo(models.CallStatus, {
      foreignKey: 'status_id',
      as: 'status',
    });

    ServiceCall.belongsTo(models.TypeOfCall, {
      foreignKey: 'type_of_call_id',
      as: 'typeOfCall',
    });

    ServiceCall.belongsTo(models.ProductsIssues, {
      foreignKey: 'products_issues_id',
      as: 'productsIssue',
    });

    ServiceCall.belongsTo(models.OnlineCallType, {
      foreignKey: 'online_call_type_id',
      as: 'onlineCallType',
    });

    ServiceCall.belongsTo(models.Area, {
      foreignKey: 'area_id',
      as: 'area',
    });

    ServiceCall.belongsTo(models.Executive, {
      foreignKey: 'assigned_to',
      as: 'assignedExecutive',
    });

    ServiceCall.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    ServiceCall.hasMany(models.ServiceCallItem, {
      foreignKey: 'service_call_id',
      as: 'items',
    });
  };

  return ServiceCall;
}
