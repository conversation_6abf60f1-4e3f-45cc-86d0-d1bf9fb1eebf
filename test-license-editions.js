// Test script to check the license editions API validation fix
async function testLicenseEditionsAPI() {
    console.log('🧪 Testing License Editions API validation fix...');
    
    try {
        // Test data with empty max_companies field
        const testData = {
            name: 'Test License Edition',
            code: 'TEST_LICENSE',
            description: 'Test license edition for validation',
            max_companies: '', // Empty string - should be converted to null
            max_users: 100,
            is_active: true
        };
        
        console.log('📤 Sending test data:', testData);
        
        // Test creating a license edition with empty max_companies
        const response = await fetch('http://localhost:8082/api/v1/master-data/license-editions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ License Edition created successfully!');
            console.log('📊 Response:', {
                success: result.success,
                message: result.message,
                maxCompanies: result.data?.license_edition?.max_companies,
                maxUsers: result.data?.license_edition?.max_users
            });
            
            // Clean up - delete the test record
            if (result.data?.license_edition?.id) {
                const deleteResponse = await fetch(`http://localhost:8082/api/v1/master-data/license-editions/${result.data.license_edition.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (deleteResponse.ok) {
                    console.log('🗑️ Test record cleaned up successfully');
                } else {
                    console.log('⚠️ Could not clean up test record');
                }
            }
        } else {
            console.error('❌ License Edition creation failed:');
            console.error('Status:', response.status);
            console.error('Response:', result);
        }
        
        // Test updating with empty max_companies
        console.log('\n🔄 Testing update with empty max_companies...');
        
        const updateData = {
            name: 'Updated Test License',
            max_companies: '', // Empty string - should be converted to null
            max_users: ''      // Empty string - should be converted to null
        };
        
        console.log('📤 Sending update data:', updateData);
        
        // Note: This test assumes there's an existing license edition with ID 1
        // In a real test, you'd create a record first, then update it
        const updateResponse = await fetch('http://localhost:8082/api/v1/master-data/license-editions/1', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });
        
        const updateResult = await updateResponse.json();
        
        if (updateResponse.ok) {
            console.log('✅ License Edition updated successfully!');
            console.log('📊 Update Response:', {
                success: updateResult.success,
                message: updateResult.message,
                maxCompanies: updateResult.data?.license_edition?.max_companies,
                maxUsers: updateResult.data?.license_edition?.max_users
            });
        } else {
            console.log('ℹ️ Update test result (may fail if record doesn\'t exist):');
            console.log('Status:', updateResponse.status);
            console.log('Response:', updateResult);
        }
        
    } catch (error) {
        console.error('❌ Error testing API:', error.message);
    }
}

// Run the test
testLicenseEditionsAPI();
