import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { logger } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const customerFilesDir = path.join(uploadsDir, 'customers');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

if (!fs.existsSync(customerFilesDir)) {
  fs.mkdirSync(customerFilesDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Create customer-specific directory
    const customerId = req.params.id || 'temp';
    const customerDir = path.join(customerFilesDir, customerId.toString());
    
    if (!fs.existsSync(customerDir)) {
      fs.mkdirSync(customerDir, { recursive: true });
    }
    
    cb(null, customerDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const originalName = file.originalname;
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    
    // Sanitize filename
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9_-]/g, '_');
    const filename = `${file.fieldname}_${timestamp}_${sanitizedBaseName}${extension}`;
    
    cb(null, filename);
  }
});

// File filter function for regular uploads
const fileFilter = (req, file, cb) => {
  // Allowed file types
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only PDF, DOC, DOCX, JPG, and PNG files are allowed.'), false);
  }
};

// File filter function for import files (Excel/CSV)
const importFileFilter = (req, file, cb) => {
  // Allowed file types for import
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'text/csv' // .csv
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed for import.'), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 10 // Maximum 10 files
  }
});

// Middleware for customer file uploads
export const uploadCustomerFiles = upload.fields([
  { name: 'tdlAddonsFile', maxCount: 1 },
  // Add more file fields here as needed
]);

// Configure multer for import files
const importUpload = multer({
  storage: storage,
  fileFilter: importFileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for import files
    files: 1 // Only one file for import
  }
});

// Middleware for import file uploads
export const uploadImportFile = importUpload.single('file');

// Error handling middleware for multer
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: 'File too large. Maximum size is 5MB.',
        error: 'FILE_TOO_LARGE'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: 'Too many files. Maximum 10 files allowed.',
        error: 'TOO_MANY_FILES'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: 'Unexpected file field.',
        error: 'UNEXPECTED_FILE'
      });
    }
  }

  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: error.message,
      error: 'INVALID_FILE_TYPE'
    });
  }

  logger.error('File upload error:', error);
  return res.status(500).json({
    success: false,
    message: 'File upload failed',
    error: 'UPLOAD_ERROR'
  });
};

// Utility function to delete uploaded files
export const deleteUploadedFiles = (files) => {
  if (!files) return;

  Object.values(files).forEach(fileArray => {
    if (Array.isArray(fileArray)) {
      fileArray.forEach(file => {
        try {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
            logger.info(`Deleted file: ${file.path}`);
          }
        } catch (error) {
          logger.error(`Error deleting file ${file.path}:`, error);
        }
      });
    }
  });
};

// Utility function to move temp files to permanent location
export const moveFilesToCustomerDirectory = (files, customerId) => {
  if (!files || !customerId) return {};

  const movedFiles = {};
  const customerDir = path.join(customerFilesDir, customerId.toString());

  if (!fs.existsSync(customerDir)) {
    fs.mkdirSync(customerDir, { recursive: true });
  }

  Object.entries(files).forEach(([fieldName, fileArray]) => {
    if (Array.isArray(fileArray)) {
      movedFiles[fieldName] = fileArray.map(file => {
        const newPath = path.join(customerDir, file.filename);
        
        try {
          // If file is in temp directory, move it
          if (file.path !== newPath) {
            fs.renameSync(file.path, newPath);
          }
          
          return {
            ...file,
            path: newPath,
            url: `/uploads/customers/${customerId}/${file.filename}`
          };
        } catch (error) {
          logger.error(`Error moving file ${file.path} to ${newPath}:`, error);
          return file;
        }
      });
    }
  });

  return movedFiles;
};

export default upload;
