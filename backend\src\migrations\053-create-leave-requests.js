import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('leave_requests', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Employee requesting leave',
    },
    leave_type_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
      comment: 'Type of leave requested',
    },
    request_number: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Unique request number for tracking',
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Leave start date',
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Leave end date',
    },
    total_days: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      comment: 'Total number of leave days',
    },
    is_half_day: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is a half-day leave',
    },
    half_day_period: {
      type: DataTypes.ENUM('first_half', 'second_half'),
      allowNull: true,
      comment: 'Which half of the day for half-day leave',
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Reason for leave request',
    },
    emergency_contact: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Emergency contact during leave',
    },
    work_handover_to: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Employee to whom work is handed over',
    },
    handover_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Work handover notes',
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'cancelled', 'withdrawn'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Current status of leave request',
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'normal',
      comment: 'Priority of leave request',
    },
    requested_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
      comment: 'User who submitted the request',
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who approved/rejected the request',
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when request was approved/rejected',
    },
    rejection_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for rejection',
    },
    manager_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Comments from manager/approver',
    },
    hr_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Comments from HR',
    },
    documents: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Array of supporting documents',
    },
    applied_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'When the leave was applied',
    },
    is_compensatory: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is compensatory leave',
    },
    compensatory_work_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Date when compensatory work was done',
    },
    auto_approved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this was auto-approved by system',
    },
    notification_sent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether notification has been sent',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('leave_requests', ['tenant_id']);
  await queryInterface.addIndex('leave_requests', ['employee_id']);
  await queryInterface.addIndex('leave_requests', ['leave_type_id']);
  await queryInterface.addIndex('leave_requests', ['status']);
  await queryInterface.addIndex('leave_requests', ['start_date']);
  await queryInterface.addIndex('leave_requests', ['end_date']);
  await queryInterface.addIndex('leave_requests', ['tenant_id', 'request_number'], {
    unique: true,
    name: 'unique_tenant_request_number',
  });
  await queryInterface.addIndex('leave_requests', ['applied_at']);
  await queryInterface.addIndex('leave_requests', ['approved_at']);

  console.log('✅ Created leave_requests table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('leave_requests');
  console.log('✅ Dropped leave_requests table');
};
