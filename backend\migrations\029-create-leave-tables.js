export const up = async (queryInterface, Sequelize) => {
  // Create leave_types table
  await queryInterface.createTable('leave_types', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: Sequelize.STRING(100),
      allowNull: false,
    },
    code: {
      type: Sequelize.STRING(10),
      allowNull: false,
    },
    description: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    annual_quota: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    is_paid: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    requires_approval: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    advance_notice_days: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    max_consecutive_days: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    min_days_per_request: {
      type: Sequelize.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 0.5,
    },
    max_days_per_request: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    carry_forward_allowed: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    max_carry_forward: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    carry_forward_expiry_months: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 12,
    },
    encashment_allowed: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    max_encashment_days: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    applicable_after_months: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    gender_specific: {
      type: Sequelize.ENUM('all', 'male', 'female'),
      allowNull: false,
      defaultValue: 'all',
    },
    requires_document: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    document_required_after_days: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    allow_half_day: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    allow_negative_balance: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    max_negative_balance: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    color_code: {
      type: Sequelize.STRING(7),
      allowNull: false,
      defaultValue: '#3B82F6',
    },
    sort_order: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Create leave_balances table
  await queryInterface.createTable('leave_balances', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    leave_type_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    year: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    allocated_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    used_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    remaining_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    carry_forward_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    encashed_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Create leave_requests table
  await queryInterface.createTable('leave_requests', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    leave_type_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    request_number: {
      type: Sequelize.STRING(50),
      allowNull: false,
      unique: true,
    },
    start_date: {
      type: Sequelize.DATEONLY,
      allowNull: false,
    },
    end_date: {
      type: Sequelize.DATEONLY,
      allowNull: false,
    },
    total_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
    },
    is_half_day: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    half_day_period: {
      type: Sequelize.ENUM('first_half', 'second_half'),
      allowNull: true,
    },
    reason: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    emergency_contact: {
      type: Sequelize.STRING(20),
      allowNull: true,
    },
    work_handover_to: {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    handover_notes: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    status: {
      type: Sequelize.ENUM('pending', 'approved', 'rejected', 'cancelled', 'withdrawn'),
      allowNull: false,
      defaultValue: 'pending',
    },
    priority: {
      type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'normal',
    },
    manager_id: {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    manager_comments: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    approved_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    rejected_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    rejection_reason: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    cancelled_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    cancellation_reason: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('leave_types', ['tenant_id', 'code'], {
    unique: true,
    name: 'leave_types_tenant_code_unique'
  });
  
  await queryInterface.addIndex('leave_balances', ['tenant_id', 'employee_id', 'leave_type_id', 'year'], {
    unique: true,
    name: 'leave_balances_unique'
  });
  
  await queryInterface.addIndex('leave_requests', ['tenant_id', 'employee_id']);
  await queryInterface.addIndex('leave_requests', ['status']);
  await queryInterface.addIndex('leave_requests', ['start_date', 'end_date']);
  await queryInterface.addIndex('leave_requests', ['request_number'], { unique: true });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('leave_requests');
  await queryInterface.dropTable('leave_balances');
  await queryInterface.dropTable('leave_types');
};
