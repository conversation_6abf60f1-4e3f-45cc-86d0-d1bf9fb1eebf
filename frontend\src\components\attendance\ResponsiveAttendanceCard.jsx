import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge, Spinner } from '../ui';
import { Clock, MapPin, Calendar, TrendingUp, AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { attendanceAPI } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';

const ResponsiveAttendanceCard = ({ 
  variant = 'full', // 'full', 'compact', 'mobile'
  showActions = true,
  showStats = true,
  className = ''
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [todayAttendance, setTodayAttendance] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);
  const [checkingOut, setCheckingOut] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    fetchTodayAttendance();
    
    // Update time every minute for compact view, every second for full view
    const interval = variant === 'compact' ? 60000 : 1000;
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, interval);

    // Monitor online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      clearInterval(timeInterval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [variant]);

  const fetchTodayAttendance = async () => {
    try {
      setLoading(true);
      const response = await attendanceAPI.getTodayAttendance();
      setTodayAttendance(response.data);
    } catch (error) {
      console.error('Error fetching today attendance:', error);
      if (variant === 'full') {
        toast.error('Failed to fetch attendance data');
      }
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      setLocationLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          };
          setCurrentLocation(location);
          setLocationLoading(false);
          resolve(location);
        },
        (error) => {
          setLocationLoading(false);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  };

  const handleCheckIn = async () => {
    try {
      setCheckingIn(true);
      
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
        if (variant === 'full' && !confirm('Location access failed. Continue check-in without location?')) {
          return;
        }
      }

      const checkInData = {
        location: location,
        notes: ''
      };

      await attendanceAPI.checkIn(checkInData);
      toast.success('Checked in successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-in error:', error);
      toast.error(error.response?.data?.message || 'Failed to check in');
    } finally {
      setCheckingIn(false);
    }
  };

  const handleCheckOut = async () => {
    try {
      setCheckingOut(true);
      
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
      }

      const checkOutData = {
        location: location,
        notes: ''
      };

      await attendanceAPI.checkOut(checkOutData);
      toast.success('Checked out successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-out error:', error);
      toast.error(error.response?.data?.message || 'Failed to check out');
    } finally {
      setCheckingOut(false);
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return '--:--';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getCurrentHours = () => {
    if (!todayAttendance?.attendanceRecord?.check_in_time) return '0.00';
    
    const checkIn = new Date(todayAttendance.attendanceRecord.check_in_time);
    const now = todayAttendance.attendanceRecord.check_out_time 
      ? new Date(todayAttendance.attendanceRecord.check_out_time)
      : new Date();
    
    const diffMs = now - checkIn;
    const diffHours = diffMs / (1000 * 60 * 60);
    return Math.max(0, diffHours).toFixed(2);
  };

  const getStatusColor = (status) => {
    const colors = {
      present: 'bg-green-100 text-green-800',
      late: 'bg-yellow-100 text-yellow-800',
      absent: 'bg-red-100 text-red-800',
      half_day: 'bg-blue-100 text-blue-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <Card className={`p-4 ${className}`}>
        <div className="flex justify-center items-center h-20">
          <Spinner size="sm" />
        </div>
      </Card>
    );
  }

  const { hasCheckedIn, hasCheckedOut, attendanceRecord } = todayAttendance || {};

  // Mobile variant
  if (variant === 'mobile') {
    return (
      <Card className={`p-4 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-blue-600" />
            <span className="font-medium">Attendance</span>
          </div>
          <div className="flex items-center space-x-1">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
          </div>
        </div>

        <div className="text-center mb-4">
          <div className="text-lg font-bold text-gray-900">
            {currentTime.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}
          </div>
          <div className="text-sm text-gray-600">
            {currentTime.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' })}
          </div>
        </div>

        {showActions && (
          <div className="space-y-2">
            {!hasCheckedIn ? (
              <Button
                onClick={handleCheckIn}
                disabled={checkingIn || locationLoading}
                className="w-full bg-green-600 hover:bg-green-700"
                size="sm"
              >
                {checkingIn ? 'Checking In...' : 'Check In'}
              </Button>
            ) : !hasCheckedOut ? (
              <Button
                onClick={handleCheckOut}
                disabled={checkingOut || locationLoading}
                className="w-full bg-red-600 hover:bg-red-700"
                size="sm"
              >
                {checkingOut ? 'Checking Out...' : 'Check Out'}
              </Button>
            ) : (
              <div className="text-center py-2 text-green-600 font-medium text-sm">
                ✓ Day Complete
              </div>
            )}
          </div>
        )}

        {showStats && attendanceRecord && (
          <div className="mt-4 pt-4 border-t space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">In:</span>
              <span>{formatTime(attendanceRecord.check_in_time)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Out:</span>
              <span>{formatTime(attendanceRecord.check_out_time)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Hours:</span>
              <span className="text-blue-600 font-medium">{getCurrentHours()}h</span>
            </div>
          </div>
        )}
      </Card>
    );
  }

  // Compact variant
  if (variant === 'compact') {
    return (
      <Card className={`p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Clock className="h-8 w-8 text-blue-600" />
            <div>
              <div className="font-medium text-gray-900">
                {hasCheckedIn ? (hasCheckedOut ? 'Completed' : 'Working') : 'Not Started'}
              </div>
              <div className="text-sm text-gray-600">
                {getCurrentHours()} hours today
              </div>
            </div>
          </div>
          
          {showActions && (
            <div>
              {!hasCheckedIn ? (
                <Button
                  onClick={handleCheckIn}
                  disabled={checkingIn}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  Check In
                </Button>
              ) : !hasCheckedOut ? (
                <Button
                  onClick={handleCheckOut}
                  disabled={checkingOut}
                  size="sm"
                  className="bg-red-600 hover:bg-red-700"
                >
                  Check Out
                </Button>
              ) : (
                <Badge className="bg-green-100 text-green-800">Complete</Badge>
              )}
            </div>
          )}
        </div>

        {attendanceRecord && (
          <div className="mt-3 flex justify-between text-sm text-gray-600">
            <span>In: {formatTime(attendanceRecord.check_in_time)}</span>
            <span>Out: {formatTime(attendanceRecord.check_out_time)}</span>
          </div>
        )}
      </Card>
    );
  }

  // Full variant (default)
  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Today's Attendance</h3>
          <p className="text-sm text-gray-600">
            {currentTime.toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">
            {currentTime.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}
          </div>
          <div className="flex items-center justify-end space-x-1 mt-1">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
          </div>
        </div>
      </div>

      {showActions && (
        <div className="mb-6">
          {!hasCheckedIn ? (
            <Button
              onClick={handleCheckIn}
              disabled={checkingIn || locationLoading}
              className="w-full bg-green-600 hover:bg-green-700 py-3"
              size="lg"
            >
              {checkingIn ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Checking In...
                </>
              ) : (
                <>
                  <Clock className="mr-2 h-5 w-5" />
                  Check In
                </>
              )}
            </Button>
          ) : !hasCheckedOut ? (
            <Button
              onClick={handleCheckOut}
              disabled={checkingOut || locationLoading}
              className="w-full bg-red-600 hover:bg-red-700 py-3"
              size="lg"
            >
              {checkingOut ? (
                <>
                  <Spinner size="sm" className="mr-2" />
                  Checking Out...
                </>
              ) : (
                <>
                  <Clock className="mr-2 h-5 w-5" />
                  Check Out
                </>
              )}
            </Button>
          ) : (
            <div className="text-center py-4">
              <div className="text-green-600 font-semibold text-lg">
                ✓ Day Complete
              </div>
              <p className="text-sm text-gray-500 mt-1">
                You have completed your attendance for today
              </p>
            </div>
          )}

          {locationLoading && (
            <div className="flex items-center justify-center mt-3 text-sm text-gray-500">
              <MapPin className="mr-1 h-4 w-4" />
              Getting location...
            </div>
          )}
        </div>
      )}

      {showStats && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-600">Status</div>
              {attendanceRecord ? (
                <Badge className={getStatusColor(attendanceRecord.status)}>
                  {attendanceRecord.status.replace('_', ' ').toUpperCase()}
                </Badge>
              ) : (
                <Badge className="bg-gray-100 text-gray-800">NOT MARKED</Badge>
              )}
            </div>
            
            <div className="text-center">
              <div className="text-sm text-gray-600">Check In</div>
              <div className="font-medium">
                {formatTime(attendanceRecord?.check_in_time)}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-sm text-gray-600">Check Out</div>
              <div className="font-medium">
                {formatTime(attendanceRecord?.check_out_time)}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-sm text-gray-600">Hours Worked</div>
              <div className="font-medium text-blue-600">
                {getCurrentHours()} hrs
              </div>
            </div>
          </div>

          {currentLocation && (
            <div className="mt-4 pt-4 border-t">
              <div className="flex items-center text-sm text-gray-600">
                <MapPin className="mr-1 h-4 w-4" />
                <span>
                  Location: {currentLocation.latitude.toFixed(4)}, {currentLocation.longitude.toFixed(4)}
                  (±{Math.round(currentLocation.accuracy)}m)
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default ResponsiveAttendanceCard;
