import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const OnlineCallType = sequelize.define('OnlineCallType', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
        notEmpty: true,
      },
      comment: 'The call type/issue name',
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: [2, 50],
      },
      comment: 'For grouping similar types (e.g., GST, Installation, Technical Support)',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed description of the call type',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: 'For custom ordering in dropdowns',
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      comment: 'Multi-tenancy support',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'online_call_types',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
      {
        unique: true,
        fields: ['name', 'tenant_id'],
        name: 'unique_name_per_tenant',
      },
    ],
  });

  // Instance methods
  OnlineCallType.prototype.toJSON = function() {
    const values = { ...this.get() };
    return values;
  };

  OnlineCallType.prototype.isActive = function() {
    return this.is_active === true;
  };

  OnlineCallType.prototype.getDisplayName = function() {
    return this.category ? `${this.category} - ${this.name}` : this.name;
  };

  // Class methods
  OnlineCallType.findByCategory = async function(category, tenantId, options = {}) {
    return await this.findAll({
      where: {
        category,
        tenant_id: tenantId,
        is_active: true,
        ...options.where,
      },
      order: [['sort_order', 'ASC'], ['name', 'ASC']],
      ...options,
    });
  };

  OnlineCallType.findActiveByTenant = async function(tenantId, options = {}) {
    return await this.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
        ...options.where,
      },
      order: [['category', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']],
      ...options,
    });
  };

  OnlineCallType.searchByName = async function(searchTerm, tenantId, options = {}) {
    const { Op } = sequelize.Sequelize;
    return await this.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
        [Op.or]: [
          {
            name: {
              [Op.iLike]: `%${searchTerm}%`,
            },
          },
          {
            category: {
              [Op.iLike]: `%${searchTerm}%`,
            },
          },
          {
            description: {
              [Op.iLike]: `%${searchTerm}%`,
            },
          },
        ],
        ...options.where,
      },
      order: [['category', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']],
      ...options,
    });
  };

  OnlineCallType.getCategories = async function(tenantId) {
    const { Op } = sequelize.Sequelize;
    const categories = await this.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
        category: {
          [Op.ne]: null,
        },
      },
      attributes: [
        'category',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      group: ['category'],
      order: [['category', 'ASC']],
      raw: true,
    });

    return categories.map(cat => ({
      name: cat.category,
      count: parseInt(cat.count),
    }));
  };

  OnlineCallType.bulkUpdateStatus = async function(ids, isActive, tenantId) {
    return await this.update(
      { is_active: isActive },
      {
        where: {
          id: ids,
          tenant_id: tenantId,
        },
      }
    );
  };

  OnlineCallType.getDefaultCallTypes = function() {
    return [
      // GST Related
      { name: 'GST 3B Doubts', category: 'GST', sort_order: 1 },
      { name: 'GST CALCULATION ISSUE', category: 'GST', sort_order: 2 },
      { name: 'GST Doubts', category: 'GST', sort_order: 3 },
      { name: 'GST Filing Doubts', category: 'GST', sort_order: 4 },
      { name: 'GST Mismatch', category: 'GST', sort_order: 5 },
      { name: 'GSTR 1 Doubts', category: 'GST', sort_order: 6 },
      { name: 'GSTR 2A', category: 'GST', sort_order: 7 },
      
      // Installation & Setup
      { name: 'New Installation', category: 'Installation', sort_order: 21 },
      { name: 'New Release Update', category: 'Installation', sort_order: 22 },
      { name: 'Re Installation', category: 'Installation', sort_order: 24 },
      
      // Technical Support
      { name: 'Tally Slow Issue', category: 'Technical Support', sort_order: 51 },
      { name: 'Server Issue', category: 'Technical Support', sort_order: 52 },
      { name: 'Remote Access', category: 'Technical Support', sort_order: 55 },
      
      // Data Management
      { name: 'Data Issue', category: 'Data Management', sort_order: 36 },
      { name: 'Data Migration', category: 'Data Management', sort_order: 37 },
      { name: 'Data Restore', category: 'Data Management', sort_order: 38 },
      
      // Mobile App
      { name: 'Mobile App', category: 'Mobile App', sort_order: 69 },
      { name: 'Mobile App Demo', category: 'Mobile App', sort_order: 70 },
      { name: 'Mobile App Sync Issue', category: 'Mobile App', sort_order: 71 },
    ];
  };

  // Hooks
  OnlineCallType.beforeCreate(async (callType) => {
    // Auto-generate sort_order if not provided
    if (!callType.sort_order) {
      const maxSortOrder = await OnlineCallType.max('sort_order', {
        where: {
          tenant_id: callType.tenant_id,
          category: callType.category,
        },
      });
      callType.sort_order = (maxSortOrder || 0) + 1;
    }
  });

  OnlineCallType.beforeUpdate(async (callType) => {
    callType.updated_at = new Date();
  });

  // Associations
  OnlineCallType.associate = function(models) {
    OnlineCallType.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    // Association with ServiceCall for online call types
    OnlineCallType.hasMany(models.ServiceCall, {
      foreignKey: 'online_call_type_id',
      as: 'serviceCalls',
    });
  };

  return OnlineCallType;
}
