import { useState, useCallback, useRef } from 'react';
import { apiService } from '../services/api';

/**
 * Hook for server-side product search
 * Provides debounced search functionality for product selection components
 */
export const useProductSearch = () => {
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(null);

  // Timeout ref for proper debouncing
  const timeoutRef = useRef(null);

  // Debounced search function
  const searchProducts = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const response = await apiService.get('/master-data/tally-products', {
        params: {
          search: searchTerm,
          limit: 20, // Limit results for performance
          page: 1,
          sortBy: 'name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data?.tallyproduct) {
        // Transform product data for SearchableSelect
        const products = response.data.data.tallyproduct.map(product => ({
          id: product.id,
          name: product.name || 'Unknown Product',
          description: product.description || '',
          category: product.category || 'N/A',
          version: product.version || 'N/A',
          // Include original product data
          originalData: product
        }));

        setSearchResults(products);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Product search error:', error);
      setSearchError(error.message || 'Failed to search products');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Properly debounced search with timeout management
  const debouncedSearch = useCallback((searchTerm) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      searchProducts(searchTerm);
    }, 150); // 150ms debounce - fast but prevents infinite loops
  }, [searchProducts]);

  // Reset search state
  const resetSearch = useCallback(() => {
    setSearchResults(null);
    setIsSearching(false);
    setSearchError(null);
  }, []);

  return {
    searchResults,
    isSearching,
    searchError,
    searchProducts: debouncedSearch,
    resetSearch
  };
};

export default useProductSearch;
