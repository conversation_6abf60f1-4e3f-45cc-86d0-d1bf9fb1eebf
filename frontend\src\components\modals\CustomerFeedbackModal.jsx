import React, { useState } from 'react';
import { FaTimes, FaClock, FaRupeeSign, FaComments, FaSmile, FaFrown, FaMeh, FaThumbsUp } from 'react-icons/fa';

const CustomerFeedbackModal = ({ isOpen, onClose, onSubmit, serviceData }) => {
  const [feedbackData, setFeedbackData] = useState({
    type: '',
    comments: ''
  });

  const feedbackOptions = [
    { value: 'happy', label: 'Happy', icon: FaSmile, color: 'text-gray-700' },
    { value: 'satisfied', label: 'Satisfied', icon: FaThumbsUp, color: 'text-gray-700' },
    { value: 'sad', label: 'Sad', icon: FaFrown, color: 'text-gray-700' },
    { value: 'not_satisfied', label: 'Not Satisfied', icon: FaMeh, color: 'text-gray-700' }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!feedbackData.type) {
      alert('Please select a feedback type');
      return;
    }
    onSubmit(feedbackData);
  };

  const handleInputChange = (field, value) => {
    setFeedbackData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <FaComments className="mr-3 text-gray-900" />
            Service Completed - Customer Feedback
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FaTimes className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Service Summary */}
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Service Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center bg-white p-3 rounded-lg shadow-sm border border-gray-200">
              <FaClock className="text-gray-600 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Total Time Spent</p>
                <p className="font-semibold text-gray-800">{serviceData?.totalHours || 0} hours</p>
              </div>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow-sm border border-gray-200">
              <FaRupeeSign className="text-gray-600 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Total Amount</p>
                <p className="font-semibold text-gray-800">₹{serviceData?.totalAmount?.toFixed(2) || '0.00'}</p>
              </div>
            </div>
            <div className="flex items-center bg-white p-3 rounded-lg shadow-sm border border-gray-200">
              <FaComments className="text-gray-600 mr-3" />
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <p className="font-semibold text-gray-800">Completed</p>
              </div>
            </div>
          </div>

          {/* Executive Remarks */}
          {serviceData?.executiveRemarks && (
            <div className="mt-4 bg-white p-4 rounded-lg shadow-sm">
              <h4 className="font-semibold text-gray-800 mb-2">Executive Remarks & Recommendations</h4>
              <p className="text-gray-700 text-sm">{serviceData.executiveRemarks}</p>
            </div>
          )}
        </div>

        {/* Feedback Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">How was your service experience?</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {feedbackOptions.map((option) => {
                const IconComponent = option.icon;
                return (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleInputChange('type', option.value)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      feedbackData.type === option.value
                        ? 'bg-gray-100 border-gray-400 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex flex-col items-center space-y-2">
                      <IconComponent className={`h-8 w-8 ${option.color}`} />
                      <span className="text-sm font-medium text-gray-700">{option.label}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Comments Section */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Comments (Optional)
            </label>
            <textarea
              value={feedbackData.comments}
              onChange={(e) => handleInputChange('comments', e.target.value)}
              placeholder="Please share any additional feedback or suggestions..."
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus-primary resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              Your feedback helps us improve our services. This will be used for future email communications.
            </p>
          </div>

          {/* Future Use Notice */}
          <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">Prem Infotech Official Social Media</h4>
            <p className="text-sm text-gray-700">
              This feedback will be used for future email communications and service improvements.
              We respect your privacy and follow our Privacy Policy and Terms and Conditions.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Skip Feedback
            </button>
            <button
              type="submit"
              disabled={!feedbackData.type}
              className="px-6 py-2 btn-primary rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              <FaComments className="mr-2" />
              Submit Feedback
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerFeedbackModal;
