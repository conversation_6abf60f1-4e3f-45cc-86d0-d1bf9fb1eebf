import React from 'react';
import classNames from 'classnames';

const Spinner = ({
  size = 'md',
  color = 'purple',
  className = '',
  center = false,
  centerType = 'default',
  ...props
}) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
  };

  const colorClasses = {
    purple: 'spinner-primary',
    blue: 'border-blue-600',
    green: 'border-green-600',
    red: 'border-red-600',
    yellow: 'border-yellow-600',
    gray: 'border-gray-600',
    white: 'border-white',
  };

  const centerClasses = {
    default: 'loading-center',
    full: 'loading-center-full',
    modal: 'loading-center-modal',
    inline: 'loading-center-inline'
  };

  const spinnerClasses = classNames(
    'animate-spin rounded-full border-2 border-t-transparent',
    sizeClasses[size],
    colorClasses[color],
    className
  );

  if (center) {
    return (
      <div className={centerClasses[centerType]}>
        <div className={spinnerClasses} {...props}>
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={spinnerClasses} {...props}>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export { Spinner };
