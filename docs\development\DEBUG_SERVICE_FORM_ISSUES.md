# Service Form Issues - Debug & Fix Guide

## Issues Identified

### 1. TSS Status Incorrect Display
**Problem**: Customer shows "Inactive" TSS status despite having active TSS record in database
**Status**: 🔍 DEBUGGING ADDED

### 2. Type of Call Dropdown Not Populating  
**Problem**: Searching "AMC" shows no results
**Status**: 🔧 FIXED - Data extraction corrected

### 3. Call Status Dropdown Missing Values
**Problem**: No call statuses appearing in dropdown
**Status**: 🔧 FIXED - Data extraction corrected

## Debugging Steps Added

### 1. Enhanced Console Logging
Added comprehensive debug logging to track:
- Master data fetching and response structure
- Customer TSS data retrieval and processing
- TSS status determination logic

### 2. Master Data API Response Fix
**Issue Found**: Master data controller returns data with lowercase model names
- `typeofcalls` → `typeofcall` 
- `callstatuses` → `callstatus`
- `designations` → `designation`
- `tallyproducts` → `tallyproduct`

**Fix Applied**: Updated data extraction in EnhancedServiceForm.jsx

### 3. TSS Status Logic Enhancement
**Enhanced Logic**:
- Check both `status` field and `expiry_date`
- Consider TSS inactive if expired even if status is "active"
- Added detailed logging for TSS determination process

## Testing Instructions

### 1. Open Browser Console
1. Navigate to http://localhost:3004/services/new
2. Open browser developer tools (F12)
3. Go to Console tab

### 2. Check Master Data Loading
Look for console logs:
```
🔍 Master Data Debug Info: {...}
🔍 Master Data Debug: {customers: X, executives: Y, typeOfCalls: Z, callStatuses: W}
🔍 Type of Calls Sample: [...]
🔍 Call Statuses Sample: [...]
```

### 3. Test Customer Selection
1. Click on "Customer Name" field
2. Search for a customer
3. Select a customer
4. Check console for:
```
🔍 Customer Debug Info: {...}
🔍 TSS Status Determination: {...}
```

### 4. Test Specific Customer TSS Status
In browser console, run:
```javascript
// Replace 'customer-id-here' with actual customer ID
window.debugCustomerTSS('customer-id-here')
```

### 5. Test Dropdown Functionality
1. **Type of Call**: Click dropdown, type "AMC" - should show AMC-related options
2. **Call Status**: Click dropdown, type "Open" - should show status options

## Expected Console Output

### Master Data Debug (Success)
```
🔍 Master Data Debug Info: {
  typeOfCallsTableExists: true,
  typeOfCallModelExists: true,
  typeOfCallCount: 10,
  availableModels: [...]
}

🔍 Master Data Debug: {
  customers: 50,
  executives: 10,
  typeOfCalls: 8,
  callStatuses: 9,
  designations: 15,
  tallyProducts: 5
}
```

### Customer TSS Debug (Success)
```
🔍 Customer Debug Info: {
  customerId: "uuid-here",
  customerName: "POWER PANDI",
  tssDetailsArray: [{...}],
  tssDetailsCount: 1,
  firstTssRecord: {...}
}

🔍 TSS Status Determination: {
  dbStatus: "active",
  expiryDate: "2025-06-20",
  isActive: true,
  isNotExpired: true,
  finalStatus: "active"
}
```

## Common Issues & Solutions

### Issue: Master Data Not Loading
**Symptoms**: typeOfCalls: 0, callStatuses: 0
**Solutions**:
1. Check backend is running on port 8080
2. Verify database tables exist
3. Check API permissions

### Issue: TSS Status Always Inactive
**Symptoms**: All customers show "Inactive" TSS
**Debug Steps**:
1. Check if customer has tssDetails array
2. Verify TSS record status field
3. Check expiry date logic

### Issue: Dropdowns Empty
**Symptoms**: SearchableSelect shows no options
**Debug Steps**:
1. Verify master data arrays have content
2. Check SearchableSelect props
3. Verify search field configuration

## API Endpoints to Test

### Master Data Endpoints
```
GET /api/master-data/debug
GET /api/master-data/type-of-calls
GET /api/master-data/call-statuses
GET /api/executives
```

### Customer Endpoint
```
GET /api/customers/:id?includeRelations=true
```

## Database Verification

### Check TSS Records
```sql
SELECT c.company_name, ct.status, ct.expiry_date, ct.created_at
FROM customers c
LEFT JOIN customer_tss ct ON c.id = ct.customer_id
WHERE c.company_name = 'POWER PANDI';
```

### Check Master Data
```sql
SELECT COUNT(*) as count, 'type_of_calls' as table_name FROM type_of_calls
UNION ALL
SELECT COUNT(*) as count, 'call_statuses' as table_name FROM call_statuses;
```

## Next Steps

1. **Test in Browser**: Open service form and check console logs
2. **Verify Master Data**: Ensure all dropdowns populate correctly
3. **Test TSS Logic**: Select customers with known TSS status
4. **Report Findings**: Document any remaining issues with console output

## Quick Fixes Applied

✅ **Master Data Extraction**: Fixed lowercase model name issue
✅ **Debug Logging**: Added comprehensive console logging
✅ **TSS Logic**: Enhanced status determination with expiry check
✅ **Error Handling**: Improved error reporting for master data

## Files Modified

- `frontend/src/pages/services/EnhancedServiceForm.jsx`
  - Fixed master data extraction
  - Added debug logging
  - Enhanced TSS status logic
  - Added customer debug function
