import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  console.log('🔄 Populating call_billing_type for existing service calls...');

  try {
    // First, check how many service calls have null call_billing_type
    const nullCount = await queryInterface.sequelize.query(
      `SELECT COUNT(*) as count FROM service_calls WHERE call_billing_type IS NULL`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    console.log(`Found ${nullCount[0].count} service calls with null call_billing_type`);

    if (parseInt(nullCount[0].count) > 0) {
      // Strategy 1: Set calls with AMC to 'amc_call'
      const amcResult = await queryInterface.sequelize.query(`
        UPDATE service_calls
        SET call_billing_type = 'amc_call',
            updated_at = NOW()
        WHERE call_billing_type IS NULL
        AND (amc_id IS NOT NULL OR is_under_amc = true)
      `);
      console.log(`✅ Updated ${amcResult[1]} service calls to 'amc_call' (based on AMC)`);

      // Strategy 2: Set calls with service charges > 0 to 'per_call'
      const paidResult = await queryInterface.sequelize.query(`
        UPDATE service_calls
        SET call_billing_type = 'per_call',
            updated_at = NOW()
        WHERE call_billing_type IS NULL
        AND (service_charges > 0 OR total_amount > 0)
      `);
      console.log(`✅ Updated ${paidResult[1]} service calls to 'per_call' (based on charges)`);

      // Strategy 3: Set remaining calls to 'free_call'
      const freeResult = await queryInterface.sequelize.query(`
        UPDATE service_calls
        SET call_billing_type = 'free_call',
            updated_at = NOW()
        WHERE call_billing_type IS NULL
      `);
      console.log(`✅ Updated ${freeResult[1]} service calls to 'free_call' (default)`);
      
      // Log the distribution after update
      const distribution = await queryInterface.sequelize.query(`
        SELECT 
          call_billing_type,
          COUNT(*) as count
        FROM service_calls 
        GROUP BY call_billing_type
        ORDER BY call_billing_type
      `, { type: queryInterface.sequelize.QueryTypes.SELECT });

      console.log('📊 Call Call type distribution after update:');
      distribution.forEach(row => {
        console.log(`  ${row.call_billing_type || 'NULL'}: ${row.count}`);
      });
    } else {
      console.log('✅ All service calls already have call_billing_type populated');
    }

  } catch (error) {
    console.error('❌ Error populating call_billing_type:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  console.log('🔄 Reverting call_billing_type population...');
  
  try {
    // Set all call_billing_type back to null
    // Note: This is a destructive operation, use with caution
    await queryInterface.sequelize.query(`
      UPDATE service_calls
      SET call_billing_type = NULL,
          updated_at = NOW()
      WHERE call_billing_type = 'free_call'
    `);
    
    console.log('✅ Reverted call_billing_type to null for default values');
  } catch (error) {
    console.error('❌ Error reverting call_billing_type:', error);
    throw error;
  }
};
