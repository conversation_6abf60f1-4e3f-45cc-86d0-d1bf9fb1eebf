import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Customer = sequelize.define('Customer', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    customer_code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 20],
      },
    },
    company_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    display_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Short display name for the customer',
    },
    industry_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'industries',
        key: 'id',
      },
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
    },
    assigned_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    customer_type: {
      type: DataTypes.ENUM('prospect', 'customer', 'inactive', 'blacklisted'),
      allowNull: false,
      defaultValue: 'prospect',
    },
    business_type: {
      type: DataTypes.ENUM('proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'),
      allowNull: true,
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    contact_person: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Primary contact person name',
    },
    designation: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Designation of the contact person',
    },
    alternate_phone: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Alternate phone number',
    },
    gst_number: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        customGstValidation(value) {
          if (value && value.trim() !== '') {
            if (value.length !== 15) {
              throw new Error('GST number must be exactly 15 characters');
            }
            if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value)) {
              throw new Error('Invalid GST number format');
            }
          }
        },
      },
    },
    pan_number: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        customPanValidation(value) {
          if (value && value.trim() !== '') {
            if (value.length !== 10) {
              throw new Error('PAN number must be exactly 10 characters');
            }
            if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
              throw new Error('Invalid PAN number format');
            }
          }
        },
      },
    },
    annual_turnover: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    employee_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    credit_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    payment_terms: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_account_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_ifsc_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Geographic coordinates for mapping',
    },
    lead_source: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Source of the lead (referral, website, etc.)',
    },
    referred_by: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Name of the person who referred this customer',
    },
    first_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    last_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    next_follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Custom fields for additional data',
    },
    tally_version: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally software version',
    },
    tally_serial_number: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally serial number',
    },
    license_type: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally license type',
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Tally installation date',
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Complete address',
    },
    pincode: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Postal/PIN code',
    },
    notification_sms: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable SMS notifications for this customer',
    },
    notification_email: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable email notifications for this customer',
    },
    notification_whatsapp: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable WhatsApp notifications for this customer',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'customers',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'customer_code'],
        unique: true,
      },
      {
        fields: ['company_name'],
      },
      {
        fields: ['customer_type'],
      },
      {
        fields: ['industry_id'],
      },
      {
        fields: ['area_id'],
      },
      {
        fields: ['assigned_executive_id'],
      },
      {
        fields: ['gst_number'],
      },
      {
        fields: ['pan_number'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['created_by'],
      },
    ],
  });

  // Instance methods
  Customer.prototype.getFullAddress = function() {
    const parts = [
      this.address_line_1,
      this.address_line_2,
      this.city,
      this.state,
      this.country,
      this.postal_code,
    ].filter(Boolean);
    return parts.join(', ');
  };

  Customer.prototype.isProspect = function() {
    return this.customer_type === 'prospect';
  };

  Customer.prototype.isCustomer = function() {
    return this.customer_type === 'customer';
  };

  Customer.prototype.getCreditUtilization = async function() {
    // This would calculate credit utilization based on outstanding invoices
    // Implementation would require invoice/payment models
    return 0;
  };

  // Associations
  Customer.associate = function(models) {
    Customer.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Customer.belongsTo(models.Industry, {
      foreignKey: 'industry_id',
      as: 'industry',
    });

    Customer.belongsTo(models.Area, {
      foreignKey: 'area_id',
      as: 'area',
    });

    Customer.belongsTo(models.Executive, {
      foreignKey: 'assigned_executive_id',
      as: 'assignedExecutive',
    });

    Customer.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    Customer.hasMany(models.CustomerContact, {
      foreignKey: 'customer_id',
      as: 'contacts',
    });

    Customer.hasMany(models.CustomerTSS, {
      foreignKey: 'customer_id',
      as: 'tssDetails',
    });

    Customer.hasMany(models.CustomerAMC, {
      foreignKey: 'customer_id',
      as: 'amcContracts',
    });

    Customer.hasMany(models.ServiceCall, {
      foreignKey: 'customer_id',
      as: 'serviceCalls',
    });

    Customer.hasMany(models.Sale, {
      foreignKey: 'customer_id',
      as: 'sales',
    });

    // TODO: Create Referral model
    // Customer.hasMany(models.Referral, {
    //   foreignKey: 'customer_id',
    //   as: 'referrals',
    // });
  };

  return Customer;
}
