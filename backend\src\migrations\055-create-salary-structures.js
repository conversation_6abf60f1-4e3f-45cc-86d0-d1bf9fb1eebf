import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('salary_structures', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Employee for this salary structure',
    },
    structure_name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Name/title of salary structure',
    },
    basic_salary: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Basic salary amount',
    },
    
    // Allowances
    hra: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'House Rent Allowance',
    },
    transport_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Transport/Conveyance Allowance',
    },
    medical_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Medical Allowance',
    },
    special_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Special Allowance',
    },
    performance_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Performance-based Allowance',
    },
    other_allowances: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other custom allowances as JSON object',
    },
    
    // Deductions
    pf_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee PF contribution',
    },
    pf_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer PF contribution',
    },
    esi_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee ESI contribution',
    },
    esi_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer ESI contribution',
    },
    professional_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Professional Tax',
    },
    income_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Income Tax (TDS)',
    },
    other_deductions: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other custom deductions as JSON object',
    },
    
    // Calculation settings
    overtime_rate_per_hour: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Overtime rate per hour',
    },
    late_deduction_per_hour: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction per hour for late arrival',
    },
    absent_deduction_per_day: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction per day for absence',
    },
    
    // Calculation methods
    pf_calculation_method: {
      type: DataTypes.ENUM('percentage', 'fixed'),
      allowNull: false,
      defaultValue: 'percentage',
      comment: 'How PF is calculated',
    },
    pf_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 12.00,
      comment: 'PF percentage if using percentage method',
    },
    esi_calculation_method: {
      type: DataTypes.ENUM('percentage', 'fixed'),
      allowNull: false,
      defaultValue: 'percentage',
      comment: 'How ESI is calculated',
    },
    esi_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 1.75,
      comment: 'ESI percentage if using percentage method',
    },
    
    // Validity
    effective_from: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date from which this structure is effective',
    },
    effective_to: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Date until which this structure is effective',
    },
    
    // Status
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this structure is currently active',
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is the default structure for the employee',
    },
    
    // Approval
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who approved this structure',
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When this structure was approved',
    },
    
    // Metadata
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes about this structure',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
      comment: 'User who created this structure',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('salary_structures', ['tenant_id']);
  await queryInterface.addIndex('salary_structures', ['employee_id']);
  await queryInterface.addIndex('salary_structures', ['effective_from']);
  await queryInterface.addIndex('salary_structures', ['effective_to']);
  await queryInterface.addIndex('salary_structures', ['is_active']);
  await queryInterface.addIndex('salary_structures', ['is_default']);
  await queryInterface.addIndex('salary_structures', ['tenant_id', 'employee_id', 'effective_from'], {
    name: 'idx_employee_structure_date',
  });

  console.log('✅ Created salary_structures table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('salary_structures');
  console.log('✅ Dropped salary_structures table');
};
