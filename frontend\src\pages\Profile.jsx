import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>er, Card, CardBody, CardHeader, Button } from '../components/ui';
import { FaUser, FaEdit, FaSave, FaCamera, FaPhone, FaEnvelope, FaMapMarkerAlt, FaCalendar, FaBriefcase, FaGraduationCap, FaUpload, FaTrash } from 'react-icons/fa';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';
import LoadingScreen from '../components/ui/LoadingScreen';

const Profile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [profileImagePreview, setProfileImagePreview] = useState(null);

  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    designation: '',
    department: '',
    joinDate: '',
    experience: '',
    education: '',
    bio: '',
    profilePicture: '',
    skills: [],
    achievements: []
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [errors, setErrors] = useState({});

  // Fetch profile data from API
  useEffect(() => {
    fetchProfileData();
  }, []);

  const fetchProfileData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/profile');

      if (response.data?.success) {
        const userData = response.data.data.user;
        setProfile({
          firstName: userData.first_name || '',
          lastName: userData.last_name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          address: userData.address || '',
          designation: userData.designation || 'Employee',
          department: userData.department || 'General',
          joinDate: userData.created_at ? userData.created_at.split('T')[0] : '',
          experience: userData.experience || '',
          education: userData.education || '',
          bio: userData.bio || 'No bio available',
          profilePicture: userData.avatar_url || '',
          skills: userData.skills || ['CRM Systems', 'Customer Relations', 'Sales Management'],
          achievements: userData.achievements || ['Team Player', 'Goal Oriented', 'Customer Focused']
        });

        if (userData.avatar_url) {
          setProfileImagePreview(userData.avatar_url);
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setProfile(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // First name validation
    if (!profile.firstName || profile.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    } else if (profile.firstName.trim().length > 50) {
      newErrors.firstName = 'First name must not exceed 50 characters';
    }

    // Last name validation
    if (!profile.lastName || profile.lastName.trim().length < 1) {
      newErrors.lastName = 'Last name must be at least 1 character';
    } else if (profile.lastName.trim().length > 50) {
      newErrors.lastName = 'Last name must not exceed 50 characters';
    }

    // Phone validation
    if (profile.phone && (profile.phone.trim().length < 10 || profile.phone.trim().length > 20)) {
      newErrors.phone = 'Phone must be between 10 and 20 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePassword = () => {
    const newErrors = {};

    if (passwordData.newPassword && passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (passwordData.newPassword && passwordData.newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters long';
    }

    setErrors(prev => ({ ...prev, ...newErrors }));
    return Object.keys(newErrors).length === 0;
  };

  const handleProfileImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSave = async () => {
    // Validate form before saving
    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      setLoading(true);

      // Prepare profile data
      const profileData = {
        first_name: profile.firstName.trim(),
        last_name: profile.lastName.trim(),
        phone: profile.phone?.trim() || '',
        address: profile.address || '',
        bio: profile.bio || '',
        designation: profile.designation || '',
        department: profile.department || '',
        experience: profile.experience || '',
        education: profile.education || '',
      };

      // Handle profile image upload if provided
      if (profileImage) {
        const formData = new FormData();
        Object.keys(profileData).forEach(key => {
          formData.append(key, profileData[key]);
        });
        formData.append('profile_image', profileImage);

        await apiService.put('/profile', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      } else {
        await apiService.put('/profile', profileData);
      }

      toast.success('Profile updated successfully');
      setIsEditing(false);
      setProfileImage(null);
      setErrors({});

      // Refresh profile data
      await fetchProfileData();
    } catch (error) {
      console.error('Error saving profile:', error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to update profile');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async () => {
    if (!validatePassword()) {
      return;
    }

    if (!passwordData.currentPassword) {
      toast.error('Current password is required');
      return;
    }

    try {
      setLoading(true);
      await apiService.post('/profile/change-password', {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword,
      });

      toast.success('Password changed successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setErrors({});
    } catch (error) {
      console.error('Error changing password:', error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to change password');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setProfileImage(null);
    setProfileImagePreview(profile.profilePicture);
    // Reset form to original values
    fetchProfileData();
  };

  return (
    <Container className="py-6">
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Profile</h1>
        <p className="text-gray-600 text-sm sm:text-base">Manage your personal information and preferences</p>
      </div>

      {loading && (
        <LoadingScreen
          title="Loading Profile..."
          subtitle="Fetching your personal information and settings"
          variant="page"
        />
      )}

      {!loading && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card className="stats-card-primary border-0 shadow-lg">
              <CardBody className="p-6 text-center">
                <div className="relative inline-block mb-4">
                  <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto overflow-hidden">
                    {profileImagePreview ? (
                      <img
                        src={profileImagePreview}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <FaUser className="h-12 w-12 text-white" />
                    )}
                  </div>
                  {isEditing && (
                    <div className="absolute bottom-0 right-0">
                      <label className="cursor-pointer">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleProfileImageChange}
                          className="hidden"
                          disabled={loading}
                        />
                        <div className="bg-white text-purple-600 rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200">
                          <FaCamera className="h-3 w-3" />
                        </div>
                      </label>
                    </div>
                  )}
                </div>
                <h3 className="text-xl font-bold mb-1">{profile.firstName} {profile.lastName}</h3>
                <p className="text-purple-100 mb-4">{profile.designation}</p>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-center">
                    <FaEnvelope className="mr-2 h-4 w-4" />
                    <span className="text-purple-100">{profile.email}</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <FaPhone className="mr-2 h-4 w-4" />
                    <span className="text-purple-100">{profile.phone}</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <FaBriefcase className="mr-2 h-4 w-4" />
                    <span className="text-purple-100">{profile.department}</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <FaCalendar className="mr-2 h-4 w-4" />
                    <span className="text-purple-100">Joined {new Date(profile.joinDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="mt-6">
                  <Button
                    variant="outline"
                    className="border-white/30 text-white hover:bg-white/10 hover:border-white/50"
                    onClick={() => setIsEditing(!isEditing)}
                    disabled={loading}
                  >
                    <FaEdit className="mr-2" />
                    {isEditing ? 'Cancel Edit' : 'Edit Profile'}
                  </Button>
                </div>
              </CardBody>
            </Card>

            {/* Quick Stats */}
            <Card className="bg-white border-0 shadow-lg mt-6">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <h5 className="text-lg font-semibold text-gray-900 mb-0">Quick Stats</h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Experience</span>
                    <span className="font-semibold text-purple-600">{profile.experience}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Education</span>
                    <span className="font-semibold text-purple-600">{profile.education}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Skills</span>
                    <span className="font-semibold text-purple-600">{profile.skills.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Achievements</span>
                    <span className="font-semibold text-purple-600">{profile.achievements.length}</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Information */}
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <div className="flex justify-between items-center">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0">Personal Information</h5>
                  {isEditing && (
                    <div className="space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="btn-theme-primary border-0"
                        size="sm"
                        onClick={handleSave}
                        disabled={loading}
                      >
                        <FaSave className="mr-2" />
                        {loading ? 'Saving...' : 'Save'}
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardBody className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    {isEditing ? (
                      <div>
                        <input
                          type="text"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                            errors.firstName ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={profile.firstName}
                          onChange={(e) => handleInputChange('firstName', e.target.value)}
                          disabled={loading}
                        />
                        {errors.firstName && (
                          <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-900 py-3">{profile.firstName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    {isEditing ? (
                      <div>
                        <input
                          type="text"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                            errors.lastName ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={profile.lastName}
                          onChange={(e) => handleInputChange('lastName', e.target.value)}
                          disabled={loading}
                        />
                        {errors.lastName && (
                          <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-900 py-3">{profile.lastName}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                        value={profile.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                      />
                    ) : (
                      <p className="text-gray-900 py-3">{profile.email}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    {isEditing ? (
                      <div>
                        <input
                          type="tel"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                            errors.phone ? 'border-red-500' : 'border-gray-300'
                          }`}
                          value={profile.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          disabled={loading}
                        />
                        {errors.phone && (
                          <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-900 py-3">{profile.phone}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    {isEditing ? (
                      <textarea
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                        rows="3"
                        value={profile.address}
                        onChange={(e) => handleInputChange('address', e.target.value)}
                      />
                    ) : (
                      <p className="text-gray-900 py-3">{profile.address}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                    {isEditing ? (
                      <textarea
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                        rows="4"
                        value={profile.bio}
                        onChange={(e) => handleInputChange('bio', e.target.value)}
                      />
                    ) : (
                      <p className="text-gray-900 py-3">{profile.bio}</p>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Change Password Section */}
            <Card className="bg-white border-0 shadow-lg">
              <CardHeader className="border-b border-gray-200 px-6 py-4">
                <h5 className="text-lg font-semibold text-gray-900 mb-0">Change Password</h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                    <input
                      type="password"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                        errors.currentPassword ? 'border-red-500' : 'border-gray-300'
                      }`}
                      value={passwordData.currentPassword}
                      onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                      disabled={loading}
                    />
                    {errors.currentPassword && (
                      <p className="text-red-500 text-sm mt-1">{errors.currentPassword}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <input
                      type="password"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                        errors.newPassword ? 'border-red-500' : 'border-gray-300'
                      }`}
                      value={passwordData.newPassword}
                      onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                      disabled={loading}
                    />
                    {errors.newPassword && (
                      <p className="text-red-500 text-sm mt-1">{errors.newPassword}</p>
                    )}
                    <p className="text-gray-500 text-sm mt-1">Password must be at least 8 characters long</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                    <input
                      type="password"
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 ${
                        errors.confirmPassword ? 'border-red-500' : 'border-gray-300'
                      }`}
                      value={passwordData.confirmPassword}
                      onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                      disabled={loading}
                    />
                    {errors.confirmPassword && (
                      <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>
                    )}
                  </div>
                  <div className="flex items-end">
                    <Button
                      className="btn-theme-primary border-0"
                      onClick={handleChangePassword}
                      disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                    >
                      {loading ? 'Changing...' : 'Change Password'}
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Skills & Achievements */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-0 shadow-lg">
                <CardHeader className="border-b border-gray-200 px-6 py-4">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0">Skills</h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="flex flex-wrap gap-2">
                    {profile.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </CardBody>
              </Card>

              <Card className="bg-white border-0 shadow-lg">
                <CardHeader className="border-b border-gray-200 px-6 py-4">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0">Achievements</h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-2">
                    {profile.achievements.map((achievement, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-2 h-2 bg-purple-600 rounded-full mr-3"></div>
                        <span className="text-gray-700">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default Profile;
