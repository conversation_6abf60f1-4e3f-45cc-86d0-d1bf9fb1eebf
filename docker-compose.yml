version: '3.8'

services:
  # PostgreSQL Database
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: tallycrm-postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_DB: tallycrm_dev
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: postgres123
  #     POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #     - ./database/scripts:/docker-entrypoint-initdb.d
  #   networks:
  #     - tallycrm-network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U postgres -d tallycrm_dev"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

  # Redis (for sessions and caching)
  redis:
    image: redis:7-alpine
    container_name: tallycrm-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tallycrm-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: backend-dev
    container_name: tallycrm-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: tallycrm_dev
      DB_USERNAME: postgres
      DB_PASSWORD: postgres123
      DB_DIALECT: postgres
      REDIS_HOST: redis
      REDIS_PORT: 6379
      FRONTEND_URL: http://localhost:3000
      CORS_ORIGINS: http://localhost:3000,http://localhost:5173
      ENABLE_CORS: true
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - tallycrm-network
    depends_on:
      # postgres:
      #   condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React App
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: frontend-dev
    container_name: tallycrm-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api/v1
      VITE_DEV_PORT: 3000
      VITE_DEV_HOST: 0.0.0.0
    ports:
      - "3005:3000"
    volumes:
      - ./frontend:/app/frontend
      - /app/frontend/node_modules
    networks:
      - tallycrm-network
    depends_on:
      - backend
    stdin_open: true
    tty: true

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  tallycrm-network:
    driver: bridge
