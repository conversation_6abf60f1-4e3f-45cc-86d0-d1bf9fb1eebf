// Test script to verify customer status update functionality
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:8082/api/v1';

async function testCustomerStatusUpdate() {
    console.log('🧪 Testing Customer Status Update Functionality...\n');
    
    try {
        // Step 1: Get a list of customers to test with
        console.log('📋 Step 1: Fetching customers...');
        const customersResponse = await fetch(`${API_BASE}/customers?limit=5`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            }
        });
        
        if (!customersResponse.ok) {
            throw new Error(`Failed to fetch customers: ${customersResponse.status}`);
        }
        
        const customersData = await customersResponse.json();
        const customers = customersData.data?.customers || [];
        
        if (customers.length === 0) {
            console.log('❌ No customers found to test with');
            return;
        }
        
        const testCustomer = customers[0];
        console.log(`✅ Found test customer: ${testCustomer.company_name} (ID: ${testCustomer.id})`);
        console.log(`   Current status: customer_type="${testCustomer.customer_type}", is_active=${testCustomer.is_active}`);
        
        // Step 2: Test updating customer to inactive status
        console.log('\n🔄 Step 2: Testing update to inactive status...');
        
        const updateToInactiveData = {
            customer_type: 'inactive',
            is_active: false
        };
        
        const updateResponse = await fetch(`${API_BASE}/customers/${testCustomer.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(updateToInactiveData)
        });
        
        if (!updateResponse.ok) {
            const errorData = await updateResponse.json();
            console.log('❌ Update failed:', errorData);
            return;
        }
        
        const updateResult = await updateResponse.json();
        console.log('✅ Update successful:', {
            customer_type: updateResult.data?.customer?.customer_type,
            is_active: updateResult.data?.customer?.is_active
        });
        
        // Step 3: Verify the update by fetching the customer again
        console.log('\n🔍 Step 3: Verifying the update...');
        
        const verifyResponse = await fetch(`${API_BASE}/customers/${testCustomer.id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            }
        });
        
        if (!verifyResponse.ok) {
            throw new Error(`Failed to verify customer: ${verifyResponse.status}`);
        }
        
        const verifyData = await verifyResponse.json();
        const updatedCustomer = verifyData.data?.customer;
        
        console.log('📊 Verification result:', {
            customer_type: updatedCustomer.customer_type,
            is_active: updatedCustomer.is_active
        });
        
        // Check if the update was successful
        const isUpdateSuccessful = updatedCustomer.customer_type === 'inactive' && updatedCustomer.is_active === false;
        
        if (isUpdateSuccessful) {
            console.log('✅ Customer status update is working correctly!');
        } else {
            console.log('❌ Customer status update failed - values not updated properly');
        }
        
        // Step 4: Test updating back to active status
        console.log('\n🔄 Step 4: Testing update back to active status...');
        
        const updateToActiveData = {
            customer_type: 'customer',
            is_active: true
        };
        
        const updateBackResponse = await fetch(`${API_BASE}/customers/${testCustomer.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(updateToActiveData)
        });
        
        if (!updateBackResponse.ok) {
            const errorData = await updateBackResponse.json();
            console.log('❌ Update back to active failed:', errorData);
            return;
        }
        
        const updateBackResult = await updateBackResponse.json();
        console.log('✅ Update back to active successful:', {
            customer_type: updateBackResult.data?.customer?.customer_type,
            is_active: updateBackResult.data?.customer?.is_active
        });
        
        // Step 5: Test filtering by status
        console.log('\n🔍 Step 5: Testing status filtering...');
        
        // Test filtering for active customers
        const activeFilterResponse = await fetch(`${API_BASE}/customers?customerType=customer&limit=3`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            }
        });
        
        if (activeFilterResponse.ok) {
            const activeFilterData = await activeFilterResponse.json();
            const activeCustomers = activeFilterData.data?.customers || [];
            console.log(`✅ Active customers filter: Found ${activeCustomers.length} customers`);
            
            // Verify all returned customers are active
            const allActive = activeCustomers.every(c => c.customer_type === 'customer');
            console.log(`   All results are active: ${allActive ? '✅' : '❌'}`);
        }
        
        // Test filtering for inactive customers
        const inactiveFilterResponse = await fetch(`${API_BASE}/customers?customerType=inactive&limit=3`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            }
        });
        
        if (inactiveFilterResponse.ok) {
            const inactiveFilterData = await inactiveFilterResponse.json();
            const inactiveCustomers = inactiveFilterData.data?.customers || [];
            console.log(`✅ Inactive customers filter: Found ${inactiveCustomers.length} customers`);
            
            // Verify all returned customers are inactive
            const allInactive = inactiveCustomers.every(c => c.customer_type === 'inactive');
            console.log(`   All results are inactive: ${allInactive ? '✅' : '❌'}`);
        }
        
        console.log('\n🎉 Customer status update test completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.stack) {
            console.error('Stack trace:', error.stack);
        }
    }
}

// Run the test
testCustomerStatusUpdate();
