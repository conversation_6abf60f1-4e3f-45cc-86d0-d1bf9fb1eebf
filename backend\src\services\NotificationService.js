import models from '../models/index.js';
import emailService from './emailService.js';
import whatsappService from './whatsappService.js';
import { logger } from '../utils/logger.js';

class NotificationService {
  constructor() {
    this.emailService = emailService;
    this.whatsappService = whatsappService;
  }

  /**
   * Get notification settings for a tenant
   * @param {number} tenantId - Tenant ID
   * @returns {Object} Notification settings
   */
  async getNotificationSettings(tenantId) {
    try {
      let settings = await models.NotificationSettings.findOne({
        where: { tenant_id: tenantId }
      });

      // Create default settings if none exist
      if (!settings) {
        settings = await models.NotificationSettings.create({
          tenant_id: tenantId
        });
      }

      return settings;
    } catch (error) {
      logger.error('Error getting notification settings:', error);

      // ENHANCED: Return default settings object when database query fails
      // This prevents notification failures due to database schema issues
      logger.warn('Returning default notification settings due to database error');
      return {
        tenant_id: tenantId,
        service_created: true,
        service_started: true,
        service_completed: true,
        service_cancelled: false,
        service_on_hold: false,
        service_pending: false,
        service_follow_up: false,
        service_onsite: true,
        service_on_process: true,
        email_enabled: true,
        whatsapp_enabled: true,
        sms_enabled: false,
        push_enabled: false,
        business_hours_only: false,
        business_hours_start: '09:00:00',
        business_hours_end: '18:00:00'
      };
    }
  }

  /**
   * Update notification settings for a tenant
   * @param {number} tenantId - Tenant ID
   * @param {Object} settingsData - Settings to update
   * @returns {Object} Updated settings
   */
  async updateNotificationSettings(tenantId, settingsData) {
    try {
      const [settings] = await models.NotificationSettings.upsert({
        tenant_id: tenantId,
        ...settingsData
      });

      return settings;
    } catch (error) {
      logger.error('Error updating notification settings:', error);
      throw error;
    }
  }

  /**
   * Check if notification should be sent for a specific event
   * @param {number} tenantId - Tenant ID
   * @param {string} eventType - Type of event (service_created, service_started, etc.)
   * @returns {boolean} Whether to send notification
   */
  async shouldSendNotification(tenantId, eventType) {
    try {
      const settings = await this.getNotificationSettings(tenantId);

      // Map event types to settings fields
      const eventMapping = {
        'service_created': 'service_created',
        'service_started': 'service_started',
        'service_in_progress': 'service_started', // Map to started
        'service_completed': 'service_completed',
        'service_cancelled': 'service_cancelled',
        'service_on_hold': 'service_on_hold',
        'service_pending': 'service_pending',
        'service_follow_up': 'service_follow_up',
        'service_onsite': 'service_onsite',
        'service_on_process': 'service_on_process',
      };

      const settingField = eventMapping[eventType];
      if (!settingField) {
        logger.warn(`Unknown event type: ${eventType}`);
        return false;
      }

      return settings[settingField] && (settings.email_enabled || settings.whatsapp_enabled);
    } catch (error) {
      logger.error('Error checking notification settings:', error);

      // ENHANCED: Default to enabled for critical notifications when database issues occur
      // This ensures service creation and completion emails are sent even if notification settings fail
      const criticalEvents = ['service_created', 'service_completed'];
      if (criticalEvents.includes(eventType)) {
        logger.warn(`Defaulting to enabled for critical event: ${eventType} due to settings error`);
        return true;
      }

      return false; // Default to not sending for non-critical events if error occurs
    }
  }

  /**
   * Send service notification to customer
   * @param {Object} serviceData - Service call data
   * @param {Object} customerData - Customer data
   * @param {string} eventType - Type of event
   * @param {Object} additionalData - Additional data for template
   */
  async sendServiceNotification(serviceData, customerData, eventType, additionalData = {}) {
    try {
      const tenantId = serviceData.tenant_id;
      
      // Check if notification should be sent
      const shouldSend = await this.shouldSendNotification(tenantId, eventType);
      if (!shouldSend) {
        logger.info(`Notification disabled for event: ${eventType}, tenant: ${tenantId}`);
        return { success: true, message: 'Notification disabled' };
      }

      // Check business hours if enabled
      const settings = await this.getNotificationSettings(tenantId);
      if (settings.business_hours_only && !this.isBusinessHours(settings)) {
        logger.info(`Notification delayed - outside business hours for tenant: ${tenantId}`);
        // TODO: Implement delayed notification queue
        return { success: true, message: 'Notification delayed - outside business hours' };
      }

      // Prepare email data
      const emailData = {
        tenantId: tenantId, // Add tenantId for template lookup
        serviceNumber: serviceData.service_number || serviceData.id,
        customerName: customerData.company_name || customerData.name,
        eventType,
        serviceType: serviceData.service_type,
        status: serviceData.status?.name || serviceData.status,
        scheduledDate: serviceData.scheduled_date,
        description: serviceData.description,
        typeOfCall: serviceData.type_of_call?.name || serviceData.type_of_call,
        createdDate: serviceData.created_at ? new Date(serviceData.created_at).toLocaleDateString() : '',
        completedDate: serviceData.completed_at ? new Date(serviceData.completed_at).toLocaleDateString() : '',
        executiveName: serviceData.executive?.name || serviceData.executive_name || '',
        ...additionalData
      };

      // Send notifications (email and WhatsApp)
      const results = await this.sendNotifications(customerData, eventType, emailData, settings);

      logger.info('Service notification sent:', {
        serviceId: serviceData.id,
        customerId: customerData.id,
        eventType,
        emailSuccess: results.email?.success,
        whatsappSuccess: results.whatsapp?.success
      });

      return {
        success: results.email?.success || results.whatsapp?.success,
        email: results.email,
        whatsapp: results.whatsapp
      };
    } catch (error) {
      logger.error('Error sending service notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send both email and WhatsApp notifications
   * @param {Object} customerData - Customer data
   * @param {string} eventType - Event type
   * @param {Object} emailData - Email template data
   * @param {Object} settings - Notification settings
   */
  async sendNotifications(customerData, eventType, emailData, settings) {
    const results = {};

    logger.info('🔍 Notification settings check:', {
      email_enabled: settings.email_enabled,
      whatsapp_enabled: settings.whatsapp_enabled,
      eventType,
      customerId: customerData.id
    });

    // Send email notification if enabled
    if (settings.email_enabled) {
      try {
        logger.info('📧 Sending email notification...');
        results.email = await this.sendEmailNotification(customerData, eventType, emailData);
      } catch (error) {
        logger.error('Email notification failed:', error);
        results.email = { success: false, error: error.message };
      }
    } else {
      logger.info('📧 Email notification disabled');
    }

    // Send WhatsApp notification if enabled
    if (settings.whatsapp_enabled) {
      try {
        logger.info('📱 Sending WhatsApp notification...');
        results.whatsapp = await this.sendWhatsAppNotification(customerData, eventType, emailData);
        logger.info('📱 WhatsApp notification result:', results.whatsapp);
      } catch (error) {
        logger.error('WhatsApp notification failed:', error);
        results.whatsapp = { success: false, error: error.message };
      }
    } else {
      logger.info('📱 WhatsApp notification disabled');
    }

    return results;
  }

  /**
   * Send WhatsApp notification
   * @param {Object} customerData - Customer data
   * @param {string} eventType - Event type
   * @param {Object} serviceData - Service data
   */
  async sendWhatsAppNotification(customerData, eventType, serviceData) {
    try {
      logger.info('🔍 WhatsApp notification details:', {
        eventType,
        customerId: customerData.id,
        customerName: customerData.name,
        customerPhone: customerData.phone,
        customerMobile: customerData.mobile,
        serviceNumber: serviceData.serviceNumber,
        whatsappServiceAvailable: !!this.whatsappService
      });

      let result;

      switch (eventType) {
        case 'service_created':
          logger.info('📱 Calling sendServiceCreatedMessage...');
          result = await this.whatsappService.sendServiceCreatedMessage(serviceData, customerData);
          break;
        case 'service_completed':
          logger.info('📱 Calling sendServiceCompletedMessage...');
          result = await this.whatsappService.sendServiceCompletedMessage(serviceData, customerData);
          break;
        case 'service_started':
        case 'service_in_progress':
        case 'service_on_hold':
        case 'service_pending':
        case 'service_follow_up':
        case 'service_onsite':
        case 'service_on_process':
        case 'service_cancelled':
          logger.info('📱 Calling sendServiceStatusUpdateMessage...');
          result = await this.whatsappService.sendServiceStatusUpdateMessage(serviceData, customerData, eventType);
          break;
        default:
          logger.info('📱 Calling sendServiceStatusUpdateMessage (default)...');
          result = await this.whatsappService.sendServiceStatusUpdateMessage(serviceData, customerData, eventType);
      }

      logger.info('📱 WhatsApp service result:', result);
      return result;
    } catch (error) {
      logger.error('Error sending WhatsApp notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send email notification
   * @param {Object} customerData - Customer data
   * @param {string} eventType - Event type
   * @param {Object} emailData - Email template data
   */
  async sendEmailNotification(customerData, eventType, emailData) {
    try {
      if (!customerData.email) {
        logger.warn('Customer email not available for notification');
        return { success: false, message: 'Customer email not available' };
      }

      // Generate subject and template based on event type using database templates
      const { subject, template } = await this.generateEmailContent(eventType, emailData);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: customerData.email,
        subject,
        html: template
      };

      // Use existing email service
      const result = await this.emailService.sendServiceNotificationEmail(
        emailData,
        customerData,
        eventType
      );

      return result;
    } catch (error) {
      logger.error('Error sending email notification:', error);
      throw error;
    }
  }

  /**
   * Generate email content based on event type using database templates
   * @param {string} eventType - Event type
   * @param {Object} data - Template data
   * @returns {Object} Subject and template
   */
  async generateEmailContent(eventType, data) {
    try {
      // Import email template controller functions
      const { getTemplateContent, isTemplateEnabled } = await import('../controllers/emailTemplateController.js');

      // Map event types to template keys
      const templateKeyMap = {
        service_created: 'service_created',
        service_completed: 'service_completed',
        customer_welcome: 'welcome_email',
        expiry_notification: 'expiry_notification',
        renewal_reminder: 'renewal_reminder',
        renewal_urgent: 'renewal_urgent',
        renewal_overdue: 'renewal_overdue',
        attendance_reminder: 'attendance_reminder',
        late_arrival_alert: 'late_arrival_alert',
        absence_alert: 'absence_alert',
        leave_request_notification: 'leave_request_notification',
        leave_approved: 'leave_approved',
        leave_rejected: 'leave_rejected'
      };

      const templateKey = templateKeyMap[eventType];

      if (templateKey && data.tenantId) {
        // Check if template is enabled
        const enabled = await isTemplateEnabled(data.tenantId, templateKey);

        if (enabled) {
          // Get template content from database
          const templateContent = await getTemplateContent(data.tenantId, templateKey);

          if (templateContent) {
            // Replace template variables with actual data
            const subject = this.replaceTemplateVariables(templateContent.subject, data);
            const template = this.replaceTemplateVariables(templateContent.content, data);

            return { subject, template };
          }
        }
      }

      // Fallback to hardcoded templates if database template not found or disabled
      return this.getFallbackEmailContent(eventType, data);
    } catch (error) {
      logger.error('Error generating email content from database templates:', error);
      // Fallback to hardcoded templates on error
      return this.getFallbackEmailContent(eventType, data);
    }
  }

  /**
   * Fallback email content generation (original method)
   */
  getFallbackEmailContent(eventType, data) {
    const templates = {
      service_created: {
        subject: `Service Request Created - ${data.serviceNumber}`,
        template: this.getServiceCreatedTemplate(data)
      },
      service_started: {
        subject: `Service Started - ${data.serviceNumber}`,
        template: this.getServiceStartedTemplate(data)
      },
      service_completed: {
        subject: `Service Completed - Feedback Required - ${data.serviceNumber}`,
        template: this.getServiceCompletedTemplate(data)
      },
      service_cancelled: {
        subject: `Service Cancelled - ${data.serviceNumber}`,
        template: this.getServiceCancelledTemplate(data)
      },
      service_on_hold: {
        subject: `Service On Hold - ${data.serviceNumber}`,
        template: this.getServiceOnHoldTemplate(data)
      },
      attendance_reminder: {
        subject: 'Daily Attendance Reminder',
        template: this.getAttendanceReminderTemplate(data)
      },
      late_arrival_alert: {
        subject: `Late Arrival Alert - ${data.employeeName}`,
        template: this.getLateArrivalAlertTemplate(data)
      },
      absence_alert: {
        subject: `Absence Alert - ${data.employeeName}`,
        template: this.getAbsenceAlertTemplate(data)
      },
      leave_request_notification: {
        subject: `Leave Request - ${data.employeeName} - ${data.requestNumber}`,
        template: this.getLeaveRequestTemplate(data)
      },
      leave_approved: {
        subject: `Leave Approved - ${data.requestNumber}`,
        template: this.getLeaveApprovedTemplate(data)
      },
      leave_rejected: {
        subject: `Leave Rejected - ${data.requestNumber}`,
        template: this.getLeaveRejectedTemplate(data)
      }
    };

    return templates[eventType] || {
      subject: `Service Update - ${data.serviceNumber}`,
      template: this.getGenericServiceTemplate(data)
    };
  }

  /**
   * Replace template variables with actual data
   */
  replaceTemplateVariables(template, data) {
    let result = template;

    // Replace common variables
    const variables = {
      // Service notification variables
      '{{customer_name}}': data.customerName || data.customer_name || '',
      '{{company_name}}': data.company_name || data.customerName || '',
      '{{service_number}}': data.serviceNumber || '',
      '{{type_of_call}}': data.typeOfCall || '',
      '{{created_date}}': data.createdDate || '',
      '{{completed_date}}': data.completedDate || '',
      '{{executive_name}}': data.executiveName || '',
      '{{service_type}}': data.serviceType || data.service_type || '',
      '{{expiry_date}}': data.expiryDate || data.expiry_date || '',
      '{{days_remaining}}': data.daysRemaining || data.days_remaining || '0',

      // Renewal notification variables
      '{{renewal_type}}': data.renewal_type || data.renewalType || '',
      '{{days_overdue}}': data.days_overdue || '0',
      '{{renewal_amount}}': data.renewal_amount || data.renewalAmount || '',
      '{{contact_email}}': data.contact_email || process.env.SMTP_USER || '<EMAIL>',
      '{{days_before_expiry}}': data.days_before_expiry || data.daysBeforeExpiry || '',
      '{{is_overdue}}': data.is_overdue || false,
      '{{is_urgent}}': data.is_urgent || false,
    };

    // Replace all variables in the template
    Object.entries(variables).forEach(([variable, value]) => {
      result = result.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
    });

    // Handle conditional blocks (basic implementation)
    // Remove {{#if}} blocks where condition is false or empty
    result = result.replace(/\{\{#if\s+([^}]+)\}\}(.*?)\{\{\/if\}\}/gs, (match, condition, content) => {
      const conditionValue = variables[`{{${condition.trim()}}}`];
      if (conditionValue && conditionValue !== '' && conditionValue !== '0' && conditionValue !== 0) {
        return content;
      }
      return '';
    });

    return result;
  }

  /**
   * Check if current time is within business hours
   * @param {Object} settings - Notification settings
   * @returns {boolean} Whether it's business hours
   */
  isBusinessHours(settings) {
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 8);
    
    return currentTime >= settings.business_hours_start && 
           currentTime <= settings.business_hours_end;
  }

  // Email template methods
  getServiceCreatedTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Service Request Created</h2>
        <p>Dear ${data.customerName},</p>
        <p>Your service request has been created successfully.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Service Details:</strong><br>
          Service Number: ${data.serviceNumber}<br>
          Status: ${data.status}<br>
          ${data.scheduledDate ? `Scheduled Date: ${data.scheduledDate}<br>` : ''}
          ${data.description ? `Description: ${data.description}` : ''}
        </div>
        <p>We will keep you updated on the progress of your service request.</p>
        <p>Best regards,<br>Prem Infotech Support Team</p>
      </div>
    `;
  }

  getServiceStartedTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">Service Started</h2>
        <p>Dear ${data.customerName},</p>
        <p>Your service request is now in progress.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Service Details:</strong><br>
          Service Number: ${data.serviceNumber}<br>
          Status: ${data.status}<br>
          Service Type: ${data.serviceType || 'N/A'}
        </div>
        <p>Our technician is working on your request and will update you on completion.</p>
        <p>Best regards,<br>Prem Infotech Support Team</p>
      </div>
    `;
  }

  getServiceCompletedTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <div style="background-color: #2f69b3; color: white; padding: 30px 20px; text-align: center;">
          <h1 style="margin: 0; font-size: 24px;">Service Completed</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px;">Prem Infotech Tally Care</p>
        </div>

        <div style="padding: 30px 20px; background-color: #ffffff;">
          <p style="margin: 10px 0;">Dear Sir / Madam,</p>
          <p style="margin: 10px 0;">Greetings from Prem Infotech Tally Care !</p>
          <p style="margin: 10px 0;">Your Query has been resolved. We hope that your recent interaction with our support executive was pleasant. In an effort to provide you with seamless assistance and serve you better, we would love to learn about your support experience.</p>

          <div style="background-color: #e3f2fd; padding: 15px; border-left: 4px solid #2f69b3; margin: 15px 0;">
            <p style="margin: 5px 0;"><strong>Service Number:</strong> ${data.serviceNumber}</p>
            <p style="margin: 5px 0;"><strong>Status:</strong> Completed</p>
            <p style="margin: 5px 0;"><strong>Completed At:</strong> ${new Date().toLocaleString()}</p>
          </div>

          <p style="text-align: center; margin: 25px 0;">
            <a href="https://forms.gle/SKcZ4PHTseE2bsoh6"
               style="background-color: #2f69b3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;"
               target="_blank">
              📝 Kindly provide feedback
            </a>
          </p>

          <p style="margin: 10px 0;">Thank you For Choosing Prem Infotech Tally.</p>

          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p style="margin: 5px 0; font-weight: bold;">Contact Information:</p>
            <p style="margin: 5px 0;">📞 Our Call Booking No : 9367 343434 / 9047 343434.</p>
            <p style="margin: 5px 0;">📞 For any Grievance/Complaints 98437 43434 .</p>
            <p style="margin: 5px 0;">🌐 www.preminfotech.in</p>
          </div>

          <div style="font-size: 11px; color: #666; margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <p style="margin: 5px 0; font-weight: bold;">TERMS :</p>
            <p style="margin: 5px 0; line-height: 1.4;">This information is treated as record for customer appraisal. This will applicable for all our Support Type [e.g. Per Call, Tally AMC, etc]. This document will be sent to you via email/whatsapp. If you reply with or without any dispute means, then it will be considered as it is else it will treated this call as fully satisfied and accepted by your end. We will produce this document as record for review purpose if you required</p>
          </div>
        </div>

        <div style="padding: 20px; text-align: center; font-size: 12px; color: #666; background-color: #f8f9fa;">
          <p style="margin: 5px 0;">Thank you for choosing Prem Infotech Tally Care!</p>
          <p style="margin: 5px 0; color: #2f69b3; font-weight: bold;">Your satisfaction is our priority</p>
        </div>
      </div>
    `;
  }

  getServiceCancelledTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Service Cancelled</h2>
        <p>Dear ${data.customerName},</p>
        <p>Your service request has been cancelled.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Service Details:</strong><br>
          Service Number: ${data.serviceNumber}<br>
          Status: Cancelled
        </div>
        <p>If you have any questions or need to reschedule, please contact our support team.</p>
        <p>Best regards,<br>Prem Infotech Support Team</p>
      </div>
    `;
  }

  getServiceOnHoldTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #d97706;">Service On Hold</h2>
        <p>Dear ${data.customerName},</p>
        <p>Your service request has been temporarily put on hold.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Service Details:</strong><br>
          Service Number: ${data.serviceNumber}<br>
          Status: On Hold
        </div>
        <p>We will resume work on your request shortly and keep you updated.</p>
        <p>Best regards,<br>Prem Infotech Support Team</p>
      </div>
    `;
  }

  getGenericServiceTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Service Update</h2>
        <p>Dear ${data.customerName},</p>
        <p>There has been an update to your service request.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Service Details:</strong><br>
          Service Number: ${data.serviceNumber}<br>
          Status: ${data.status}
        </div>
        <p>Best regards,<br>Prem Infotech Support Team</p>
      </div>
    `;
  }

  // Attendance notification templates
  getAttendanceReminderTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Daily Attendance Reminder</h2>
        <p>Dear ${data.employeeName},</p>
        <p>This is a friendly reminder to mark your attendance for today.</p>
        <div style="background: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <strong>Reminder Details:</strong><br>
          Employee Code: ${data.employeeCode}<br>
          Date: ${data.date}<br>
          Reminder Time: ${data.reminderTime}
        </div>
        <p>Please log in to the system and mark your check-in for today.</p>
        <p>Best regards,<br>HR Team</p>
      </div>
    `;
  }

  getLateArrivalAlertTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #d97706;">Late Arrival Alert</h2>
        <p>Dear Manager,</p>
        <p>This is to inform you that an employee has arrived late today.</p>
        <div style="background: #fef3c7; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #d97706;">
          <strong>Late Arrival Details:</strong><br>
          Employee: ${data.employeeName} (${data.employeeCode})<br>
          Date: ${data.date}<br>
          Check-in Time: ${new Date(data.checkInTime).toLocaleTimeString()}<br>
          Late by: ${data.lateMinutes} minutes
        </div>
        <p>Please take appropriate action as per company policy.</p>
        <p>Best regards,<br>Attendance System</p>
      </div>
    `;
  }

  getAbsenceAlertTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Absence Alert</h2>
        <p>Dear Manager,</p>
        <p>This is to inform you that an employee is absent today without prior notice.</p>
        <div style="background: #fee2e2; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc2626;">
          <strong>Absence Details:</strong><br>
          Employee: ${data.employeeName} (${data.employeeCode})<br>
          Date: ${data.absentDate}<br>
          Status: Absent (No check-in recorded)
        </div>
        <p>Please follow up with the employee and take appropriate action as per company policy.</p>
        <p>Best regards,<br>Attendance System</p>
      </div>
    `;
  }

  getLeaveRequestTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Leave Request</h2>
        <p>Dear Manager,</p>
        <p>A new leave request has been submitted and requires your approval.</p>
        <div style="background: #e0f2fe; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <strong>Leave Request Details:</strong><br>
          Employee: ${data.employeeName} (${data.employeeCode})<br>
          Request Number: ${data.requestNumber}<br>
          Leave Type: ${data.leaveType}<br>
          Start Date: ${new Date(data.startDate).toLocaleDateString()}<br>
          End Date: ${new Date(data.endDate).toLocaleDateString()}<br>
          Total Days: ${data.totalDays}<br>
          Priority: ${data.priority}<br>
          Reason: ${data.reason}
        </div>
        <p>Please review and approve/reject this leave request at your earliest convenience.</p>
        <p>Best regards,<br>Leave Management System</p>
      </div>
    `;
  }

  getLeaveApprovedTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">Leave Request Approved</h2>
        <p>Dear ${data.employeeName},</p>
        <p>Your leave request has been approved.</p>
        <div style="background: #d1fae5; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #059669;">
          <strong>Approved Leave Details:</strong><br>
          Request Number: ${data.requestNumber}<br>
          Leave Type: ${data.leaveType}<br>
          Start Date: ${new Date(data.startDate).toLocaleDateString()}<br>
          End Date: ${new Date(data.endDate).toLocaleDateString()}<br>
          Total Days: ${data.totalDays}<br>
          Approved by: ${data.approverName}
          ${data.managerComments ? `<br>Comments: ${data.managerComments}` : ''}
        </div>
        <p>Please ensure proper handover of your responsibilities before going on leave.</p>
        <p>Best regards,<br>HR Team</p>
      </div>
    `;
  }

  getLeaveRejectedTemplate(data) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">Leave Request Rejected</h2>
        <p>Dear ${data.employeeName},</p>
        <p>We regret to inform you that your leave request has been rejected.</p>
        <div style="background: #fee2e2; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc2626;">
          <strong>Rejected Leave Details:</strong><br>
          Request Number: ${data.requestNumber}<br>
          Leave Type: ${data.leaveType}<br>
          Start Date: ${new Date(data.startDate).toLocaleDateString()}<br>
          End Date: ${new Date(data.endDate).toLocaleDateString()}<br>
          Total Days: ${data.totalDays}<br>
          Rejected by: ${data.approverName}<br>
          Reason: ${data.rejectionReason}
          ${data.managerComments ? `<br>Comments: ${data.managerComments}` : ''}
        </div>
        <p>If you have any questions about this decision, please contact your manager or HR department.</p>
        <p>Best regards,<br>HR Team</p>
      </div>
    `;
  }
}

export const notificationService = new NotificationService();
export default NotificationService;
