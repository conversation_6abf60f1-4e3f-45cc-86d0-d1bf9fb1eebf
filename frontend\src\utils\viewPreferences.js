import React from 'react';

/**
 * View Preferences Utility
 * Manages localStorage-based view preferences for table/card views across pages
 */

const VIEW_PREFERENCE_PREFIX = 'viewPreference_';

/**
 * Get view preference for a specific page
 * @param {string} pageName - The page identifier (e.g., 'customers', 'services', 'leads')
 * @param {string} defaultView - Default view if no preference is stored ('table' or 'card')
 * @returns {string} The preferred view mode
 */
export const getViewPreference = (pageName, defaultView = 'table') => {
  try {
    const key = `${VIEW_PREFERENCE_PREFIX}${pageName}`;
    const stored = localStorage.getItem(key);
    return stored || defaultView;
  } catch (error) {
    console.warn('Failed to get view preference:', error);
    return defaultView;
  }
};

/**
 * Set view preference for a specific page
 * @param {string} pageName - The page identifier
 * @param {string} viewMode - The view mode to store ('table' or 'card')
 */
export const setViewPreference = (pageName, viewMode) => {
  try {
    const key = `${VIEW_PREFERENCE_PREFIX}${pageName}`;
    localStorage.setItem(key, viewMode);
  } catch (error) {
    console.warn('Failed to set view preference:', error);
  }
};

/**
 * Clear view preference for a specific page
 * @param {string} pageName - The page identifier
 */
export const clearViewPreference = (pageName) => {
  try {
    const key = `${VIEW_PREFERENCE_PREFIX}${pageName}`;
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to clear view preference:', error);
  }
};

/**
 * Clear all view preferences
 */
export const clearAllViewPreferences = () => {
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(VIEW_PREFERENCE_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Failed to clear all view preferences:', error);
  }
};

/**
 * Get all view preferences
 * @returns {Object} Object with page names as keys and view modes as values
 */
export const getAllViewPreferences = () => {
  try {
    const preferences = {};
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(VIEW_PREFERENCE_PREFIX)) {
        const pageName = key.replace(VIEW_PREFERENCE_PREFIX, '');
        preferences[pageName] = localStorage.getItem(key);
      }
    });
    return preferences;
  } catch (error) {
    console.warn('Failed to get all view preferences:', error);
    return {};
  }
};

/**
 * Hook for managing view preferences in React components
 * @param {string} pageName - The page identifier
 * @param {string} defaultView - Default view mode
 * @returns {Array} [viewMode, setViewMode] - Current view mode and setter function
 */
export const useViewPreference = (pageName, defaultView = 'table') => {
  const [viewMode, setViewModeState] = React.useState(() => 
    getViewPreference(pageName, defaultView)
  );

  const setViewMode = React.useCallback((newViewMode) => {
    setViewModeState(newViewMode);
    setViewPreference(pageName, newViewMode);
  }, [pageName]);

  return [viewMode, setViewMode];
};

// Page name constants for consistency
export const PAGE_NAMES = {
  CUSTOMERS: 'customers',
  SERVICES: 'services',
  LEADS: 'leads',
  MASTERS: 'masters',
  SERVICE_REPORTS_OVERVIEW: 'serviceReports_overview',
  SERVICE_REPORTS_SERVICES: 'serviceReports_services',
  SERVICE_REPORTS_CALLS: 'serviceReports_calls',
  CUSTOMER_REPORTS: 'customerReports',
  SALES_REPORTS: 'salesReports',
  AMC_REPORTS: 'amcReports'
};
