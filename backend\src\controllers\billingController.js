import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Get billing history
 */
export const getBillingHistory = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { page = 1, limit = 10 } = req.query;

    const offset = (page - 1) * limit;

    const { count, rows: invoices } = await models.Invoice.findAndCountAll({
      where: { tenant_id: tenantId },
      include: [
        {
          model: models.Subscription,
          as: 'subscription',
          include: [
            {
              model: models.SubscriptionPlan,
              as: 'plan',
            },
          ],
        },
        {
          model: models.Payment,
          as: 'payments',
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    res.json({
      success: true,
      data: {
        invoices,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Get billing history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve billing history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get invoice details
 */
export const getInvoice = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const tenantId = req.user.tenant.id;

    const invoice = await models.Invoice.findOne({
      where: {
        id: invoiceId,
        tenant_id: tenantId,
      },
      include: [
        {
          model: models.Subscription,
          as: 'subscription',
          include: [
            {
              model: models.SubscriptionPlan,
              as: 'plan',
            },
          ],
        },
        {
          model: models.Payment,
          as: 'payments',
        },
        {
          model: models.Tenant,
          as: 'tenant',
        },
      ],
    });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found',
      });
    }

    res.json({
      success: true,
      data: { invoice },
    });
  } catch (error) {
    logger.error('Get invoice error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve invoice',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Download invoice PDF
 */
export const downloadInvoice = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const tenantId = req.user.tenant.id;

    const invoice = await models.Invoice.findOne({
      where: {
        id: invoiceId,
        tenant_id: tenantId,
      },
    });

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found',
      });
    }

    // If Stripe invoice exists, redirect to Stripe PDF
    if (invoice.stripe_invoice_id) {
      const stripeInvoice = await stripe.invoices.retrieve(invoice.stripe_invoice_id);
      if (stripeInvoice.invoice_pdf) {
        return res.redirect(stripeInvoice.invoice_pdf);
      }
    }

    // TODO: Generate PDF locally if no Stripe PDF available
    res.status(501).json({
      success: false,
      message: 'PDF generation not implemented yet',
    });
  } catch (error) {
    logger.error('Download invoice error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download invoice',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get payment methods
 */
export const getPaymentMethods = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
    });

    if (!subscription || !subscription.stripe_customer_id) {
      return res.json({
        success: true,
        data: { paymentMethods: [] },
      });
    }

    // Get payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: subscription.stripe_customer_id,
      type: 'card',
    });

    res.json({
      success: true,
      data: { paymentMethods: paymentMethods.data },
    });
  } catch (error) {
    logger.error('Get payment methods error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve payment methods',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Add payment method
 */
export const addPaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId } = req.body;
    const tenantId = req.user.tenant.id;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
    });

    if (!subscription || !subscription.stripe_customer_id) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found',
      });
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: subscription.stripe_customer_id,
    });

    // Set as default payment method
    await stripe.customers.update(subscription.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    logger.info('Payment method added:', {
      tenantId,
      paymentMethodId,
      customerId: subscription.stripe_customer_id,
    });

    res.json({
      success: true,
      message: 'Payment method added successfully',
    });
  } catch (error) {
    logger.error('Add payment method error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add payment method',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Remove payment method
 */
export const removePaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    const tenantId = req.user.tenant.id;

    // Detach payment method
    await stripe.paymentMethods.detach(paymentMethodId);

    logger.info('Payment method removed:', {
      tenantId,
      paymentMethodId,
    });

    res.json({
      success: true,
      message: 'Payment method removed successfully',
    });
  } catch (error) {
    logger.error('Remove payment method error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove payment method',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get billing summary
 */
export const getBillingSummary = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { months = 12 } = req.query;

    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    // Get payment totals
    const paymentSummary = await models.Payment.getTotalByTenant(
      tenantId,
      startDate,
      endDate
    );

    // Get upcoming invoice
    const upcomingInvoice = await models.Invoice.findOne({
      where: {
        tenant_id: tenantId,
        status: 'open',
        due_date: {
          [models.Sequelize.Op.gte]: new Date(),
        },
      },
      order: [['due_date', 'ASC']],
    });

    // Get overdue invoices
    const overdueInvoices = await models.Invoice.findAll({
      where: {
        tenant_id: tenantId,
        status: 'open',
        due_date: {
          [models.Sequelize.Op.lt]: new Date(),
        },
      },
      order: [['due_date', 'ASC']],
    });

    res.json({
      success: true,
      data: {
        paymentSummary,
        upcomingInvoice,
        overdueInvoices,
        overdueCount: overdueInvoices.length,
        overdueAmount: overdueInvoices.reduce((sum, inv) => sum + parseFloat(inv.amount_remaining), 0),
      },
    });
  } catch (error) {
    logger.error('Get billing summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve billing summary',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
