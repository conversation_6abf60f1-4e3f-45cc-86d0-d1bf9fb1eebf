<!DOCTYPE html>
<html>
<head>
    <title>Test Overdue Logic</title>
</head>
<body>
    <h1>Test Overdue Logic</h1>
    <div id="results"></div>

    <script>
        // Simulate the overdue logic from the frontend
        function testOverdueLogic() {
            const results = document.getElementById('results');
            
            // Test data - simulating services from the database
            const testServices = [
                {
                    id: 1,
                    call_number: 'SC-OVERDUE-001',
                    scheduled_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
                    status: { name: 'Pending', category: 'open' }
                },
                {
                    id: 2,
                    call_number: 'SC-OVERDUE-002',
                    scheduled_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                    status: { name: 'Pending', category: 'open' }
                },
                {
                    id: 3,
                    call_number: 'SC-NORMAL-001',
                    scheduled_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days future
                    status: { name: 'Pending', category: 'open' }
                },
                {
                    id: 4,
                    call_number: 'SC-COMPLETED-001',
                    scheduled_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                    status: { name: 'Completed', category: 'closed' }
                }
            ];

            results.innerHTML = '<h2>Test Results:</h2>';

            testServices.forEach(service => {
                // Frontend overdue logic
                const today = new Date();
                today.setHours(23, 59, 59, 999);

                let scheduledDate = null;
                if (service.scheduled_date) {
                    scheduledDate = new Date(service.scheduled_date);
                    if (!isNaN(scheduledDate.getTime())) {
                        scheduledDate.setHours(23, 59, 59, 999);
                    } else {
                        scheduledDate = null;
                    }
                }

                const isOverdue = scheduledDate &&
                    scheduledDate < today &&
                    (service.status?.category === 'open' ||
                     service.status?.name?.toLowerCase().includes('open') ||
                     service.status?.name?.toLowerCase().includes('pending') ||
                     service.status?.name?.toLowerCase().includes('new'));

                const daysOverdue = isOverdue ? Math.ceil((new Date() - new Date(service.scheduled_date)) / (1000 * 60 * 60 * 24)) : 0;

                results.innerHTML += `
                    <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                        <h3>${service.call_number}</h3>
                        <p><strong>Scheduled Date:</strong> ${service.scheduled_date}</p>
                        <p><strong>Status:</strong> ${service.status.name} (${service.status.category})</p>
                        <p><strong>Today:</strong> ${today.toISOString()}</p>
                        <p><strong>Scheduled Date Parsed:</strong> ${scheduledDate ? scheduledDate.toISOString() : 'null'}</p>
                        <p><strong>Is Overdue:</strong> ${isOverdue ? `YES (${daysOverdue} days)` : 'NO'}</p>
                        <p><strong>Status Check:</strong> ${service.status?.category === 'open' || service.status?.name?.toLowerCase().includes('open') || service.status?.name?.toLowerCase().includes('pending')}</p>
                    </div>
                `;
            });
        }

        // Run the test when page loads
        testOverdueLogic();
    </script>
</body>
</html>
