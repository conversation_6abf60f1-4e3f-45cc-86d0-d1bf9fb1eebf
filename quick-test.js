const fetch = require('node-fetch');

async function quickTest() {
  try {
    console.log('Testing backend health...');
    const healthResponse = await fetch('http://localhost:3001/health');
    const healthData = await healthResponse.json();
    console.log('Health check:', healthData);
    
    console.log('\nTesting auth...');
    const authResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    const authData = await authResponse.json();
    console.log('Auth result:', authData.success ? 'Success' : 'Failed');
    
    if (authData.success) {
      console.log('✅ Backend is working correctly!');
    } else {
      console.log('❌ Auth failed:', authData.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

quickTest();
