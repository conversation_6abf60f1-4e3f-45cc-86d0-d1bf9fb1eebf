import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import api from '../../services/api';
import { Dropdown, DropdownItem, DropdownDivider } from '../../components/ui';
import {
  FaChartBar,
  FaChartLine,
  FaChartPie,
  FaUsers,
  FaRupeeSign,
  FaTools,
  FaCalendar,
  FaDownload,
  FaEye,
  FaFilter,
  FaSearch,
  FaEllipsisV,
  FaClock,
  FaChartArea
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const ReportsList = () => {
  const navigate = useNavigate();
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState([]);
  const [reportStats, setReportStats] = useState({
    customers: { total: 0, summary: {} },
    serviceCalls: { total: 0, summary: {} },
    sales: { total: 0, summary: {} },
    amcs: { total: 0, summary: {} }
  });

  // Fetch reports data from API
  useEffect(() => {
    fetchReportsData();
  }, []);

  const fetchReportsData = async () => {
    try {
      setLoading(true);

      // Fetch data from all report endpoints
      const [customersRes, serviceCallsRes, salesRes, amcsRes] = await Promise.all([
        api.get('/reports/customers').catch(() => ({ data: { data: { customers: [], summary: {} } } })),
        api.get('/reports/service-calls').catch(() => ({ data: { data: { serviceCalls: [], summary: {} } } })),
        api.get('/reports/sales').catch(() => ({ data: { data: { sales: [], summary: {} } } })),
        api.get('/reports/amc').catch(() => ({ data: { data: { amcs: [], summary: {} } } }))
      ]);

      // Update report stats
      setReportStats({
        customers: {
          total: customersRes.data?.data?.customers?.length || 0,
          summary: customersRes.data?.data?.summary || {}
        },
        serviceCalls: {
          total: serviceCallsRes.data?.data?.serviceCalls?.length || 0,
          summary: serviceCallsRes.data?.data?.summary || {}
        },
        sales: {
          total: salesRes.data?.data?.sales?.length || 0,
          summary: salesRes.data?.data?.summary || {}
        },
        amcs: {
          total: amcsRes.data?.data?.amcs?.length || 0,
          summary: amcsRes.data?.data?.summary || {}
        }
      });

      // Generate dynamic reports based on available data
      const dynamicReports = [
        {
          id: 1,
          name: 'Customer Reports',
          description: `Comprehensive customer analysis with ${customersRes.data?.data?.customers?.length || 0} customers`,
          category: 'customers',
          type: 'Table',
          icon: <FaUsers className="text-primary-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          endpoint: '/reports/customers'
        },
        {
          id: 2,
          name: 'Service Analytics',
          description: `Advanced service analytics with call volume trends, response time analysis, technician performance, and resolution metrics`,
          category: 'analytics',
          type: 'Analytics',
          icon: <FaChartArea className="text-purple-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          route: '/reports/service-analytics',
          isSpecial: true
        },
        {
          id: 6,
          name: 'Service Reports Suite',
          description: `Comprehensive service reports including request tracking, aging analysis, technician performance, TAT reports, and customer satisfaction metrics`,
          category: 'services',
          type: 'Report Suite',
          icon: <FaTools className="text-blue-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          route: '/reports/service-reports',
          isSpecial: true
        },
        {
          id: 3,
          name: 'Service Call Reports',
          description: `Service call analysis with ${serviceCallsRes.data?.data?.serviceCalls?.length || 0} service calls`,
          category: 'services',
          type: 'Dashboard',
          icon: <FaTools className="text-cyan-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          route: '/reports/service-call-reports'
        },
        {
          id: 4,
          name: 'Sales Reports',
          description: `Sales performance analysis with ${salesRes.data?.data?.sales?.length || 0} sales records`,
          category: 'sales',
          type: 'Chart',
          icon: <FaChartLine className="text-green-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          endpoint: '/reports/sales'
        },
        {
          id: 5,
          name: 'AMC Reports',
          description: `AMC contract analysis with ${amcsRes.data?.data?.amcs?.length || 0} contracts`,
          category: 'financial',
          type: 'Chart',
          icon: <FaRupeeSign className="text-yellow-600" />,
          lastGenerated: new Date().toISOString().split('T')[0],
          frequency: 'Real-time',
          status: 'active',
          endpoint: '/reports/amc'
        }
      ];

      setReports(dynamicReports);
    } catch (error) {
      console.error('Error fetching reports data:', error);
      toast.error('Failed to load reports data');
      setReports([]);
    } finally {
      setLoading(false);
    }
  };

  const reportCategories = [
    { id: 'all', name: 'All Reports', icon: <FaChartBar />, count: reports.length },
    { id: 'analytics', name: 'Service Analytics', icon: <FaChartArea />, count: reports.filter(r => r.category === 'analytics').length },
    { id: 'sales', name: 'Sales Reports', icon: <FaRupeeSign />, count: reports.filter(r => r.category === 'sales').length },
    { id: 'customers', name: 'Customer Reports', icon: <FaUsers />, count: reports.filter(r => r.category === 'customers').length },
    { id: 'services', name: 'Service Reports', icon: <FaTools />, count: reports.filter(r => r.category === 'services').length },
    { id: 'financial', name: 'Financial Reports', icon: <FaChartLine />, count: reports.filter(r => r.category === 'financial').length }
  ];

  // Filter reports based on category and search
  const filteredReports = reports.filter(report => {
    const matchesCategory = activeCategory === 'all' || report.category === activeCategory;
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const getStatusBadge = (status) => {
    const badgeClass = status === 'active'
      ? 'bg-green-100 text-green-800'
      : 'bg-gray-100 text-gray-800';
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}`}>
        {status.toUpperCase()}
      </span>
    );
  };

  const getFrequencyBadge = (frequency) => {
    const badgeColors = {
      'Daily': 'bg-blue-100 text-blue-800',
      'Weekly': 'bg-primary-100 text-primary-800',
      'Monthly': 'bg-yellow-100 text-yellow-800',
      'Quarterly': 'bg-gray-100 text-gray-800',
      'Real-time': 'bg-green-100 text-green-800'
    };
    const badgeClass = badgeColors[frequency] || 'bg-gray-100 text-gray-800';
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}`}>
        {frequency}
      </span>
    );
  };

  const getTypeBadge = (type) => {
    const badgeColors = {
      'Chart': 'bg-green-100 text-green-800',
      'Table': 'bg-blue-100 text-blue-800',
      'Dashboard': 'badge-primary',
      'Analytics': 'bg-purple-100 text-purple-800',
      'Survey': 'bg-yellow-100 text-yellow-800'
    };
    const badgeClass = badgeColors[type] || 'bg-gray-100 text-gray-800';
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badgeClass}`}>
        {type}
      </span>
    );
  };

  const handleViewReport = async (report) => {
    try {
      // Handle special analytics route
      if (report.route) {
        navigate(report.route);
        return;
      }

      if (report.endpoint) {
        toast.success(`Loading ${report.name}...`);

        // Navigate to dedicated report view based on category
        switch (report.category) {
          case 'customers':
            navigate('/reports/customer-reports');
            break;
          case 'sales':
            navigate('/reports/sales-reports');
            break;
          case 'financial':
            navigate('/reports/amc-reports');
            break;
          default:
            // For other reports, fetch and display the data
            const response = await api.get(report.endpoint);
            console.log(`${report.name} data:`, response.data);
            toast.info('Report data loaded. Check console for details.');
        }
      }
    } catch (error) {
      console.error('Error loading report:', error);
      toast.error('Failed to load report');
    }
  };

  const handleExportReport = async (report, format = 'csv') => {
    try {
      toast.loading(`Exporting ${report.name}...`);

      const response = await api.get(report.endpoint);
      const data = response.data?.data;

      if (!data) {
        toast.error('No data available for export');
        return;
      }

      let exportData = [];
      let filename = '';

      // Format data based on report type
      switch (report.category) {
        case 'customers':
          exportData = data.customers?.map(customer => ({
            'Customer Name': customer.name || customer.company_name,
            'Contact Person': customer.contactPerson || customer.contact_person,
            'Email': customer.email,
            'Phone': customer.phone,
            'City': customer.city,
            'State': customer.state,
            'Status': customer.status || (customer.is_active ? 'Active' : 'Inactive'),
            'Created Date': new Date(customer.created_at || customer.createdAt).toLocaleDateString()
          })) || [];
          filename = 'customers_report';
          break;

        case 'services':
          exportData = data.serviceCalls?.map(service => ({
            'Service Number': service.serviceNumber || service.call_number,
            'Customer': service.customer || service.customer_name,
            'Type': service.type || service.call_type,
            'Status': service.status,
            'Priority': service.priority,
            'Assigned To': service.assignedTo || service.assigned_executive?.name,
            'Created Date': new Date(service.created_at || service.createdAt).toLocaleDateString(),
            'Scheduled Date': service.scheduled_date ? new Date(service.scheduled_date).toLocaleDateString() : 'N/A'
          })) || [];
          filename = 'services_report';
          break;

        case 'sales':
          exportData = data.sales?.map(sale => ({
            'Sale Number': sale.saleNumber || sale.sale_number,
            'Customer': sale.customer || sale.customer_name,
            'Sales Executive': sale.salesExecutive?.name || sale.sales_executive?.name,
            'Amount': sale.total_amount || sale.amount,
            'Status': sale.status,
            'Sale Date': new Date(sale.sale_date || sale.saleDate).toLocaleDateString(),
            'Created Date': new Date(sale.created_at || sale.createdAt).toLocaleDateString()
          })) || [];
          filename = 'sales_report';
          break;

        case 'financial':
          exportData = data.amcs?.map(amc => ({
            'AMC Number': amc.amcNumber || amc.amc_number,
            'Customer': amc.customer || amc.customer_name,
            'Start Date': new Date(amc.start_date || amc.startDate).toLocaleDateString(),
            'End Date': new Date(amc.end_date || amc.endDate).toLocaleDateString(),
            'Amount': amc.amount || amc.total_amount,
            'Status': amc.status,
            'Payment Status': amc.payment_status || amc.paymentStatus
          })) || [];
          filename = 'amc_report';
          break;

        default:
          toast.error('Export not supported for this report type');
          return;
      }

      if (exportData.length === 0) {
        toast.error('No data available for export');
        return;
      }

      // Convert to CSV
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} records successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export report');
    }
  };

  const handleExportAll = async () => {
    try {
      toast.loading('Exporting all reports...');

      // Export all available reports
      for (const report of reports) {
        await handleExportReport(report);
        // Add a small delay between exports
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast.success('All reports exported successfully');
    } catch (error) {
      console.error('Export all error:', error);
      toast.error('Failed to export all reports');
    }
  };

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="grid grid-cols-12 gap-4 mb-6">
        <div className="col-span-12">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex-1">
              <h2 className="mb-0">Reports & Analytics</h2>
              <p className="text-gray-600">Generate and view business reports and analytics</p>
            </div>
            <div className="flex flex-wrap gap-2 w-full sm:w-auto">
              <button
                className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
                style={{
                  borderColor: '#15579e',
                  color: '#15579e'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#f8fafc';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                }}
              >
                <FaFilter className="mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Advanced Filters</span>
                <span className="sm:hidden">Filters</span>
              </button>
              <button
                className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white flex-1 sm:flex-none justify-center"
                style={{
                  backgroundColor: '#15579e',
                  borderColor: '#15579e'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#0f4682';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#15579e';
                }}
                onClick={handleExportAll}
                disabled={reports.length === 0}
              >
                <FaDownload className="mr-1 sm:mr-2" />
                <span className="hidden sm:inline">Export All ({reports.length})</span>
                <span className="sm:hidden">Export ({reports.length})</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Total Customers</p>
                <p className="text-3xl font-bold text-white">{loading ? '...' : reportStats.customers.total}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaUsers size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Service Calls</p>
                <p className="text-3xl font-bold text-white">{loading ? '...' : reportStats.serviceCalls.total}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaTools size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Sales Records</p>
                <p className="text-3xl font-bold text-white">{loading ? '...' : reportStats.sales.total}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaChartLine size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">AMC Contracts</p>
                <p className="text-3xl font-bold text-white">{loading ? '...' : reportStats.amcs.total}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaRupeeSign size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-12 gap-4">
        {/* Categories Sidebar */}
        <div className="lg:col-span-3 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h5 className="text-lg font-semibold text-gray-900 mb-0">Report Categories</h5>
            </div>
            <div className="p-2">
              <nav className="space-y-1">
                {reportCategories.map(category => (
                  <button
                    key={category.id}
                    className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                      activeCategory === category.id
                        ? 'border-l-4'
                        : 'text-black hover:bg-gray-50'
                    }`}
                    style={activeCategory === category.id ? {
                      backgroundColor: '#f0f4f8',
                      color: '#15579e',
                      borderLeftColor: '#15579e'
                    } : {}}
                    onClick={() => setActiveCategory(category.id)}
                  >
                    <div className="flex items-center">
                      <span className={`mr-3 ${activeCategory === category.id ? '' : 'text-gray-400'}`}
                        style={activeCategory === category.id ? { color: '#15579e' } : {}}
                      >
                        {category.icon}
                      </span>
                      <span>{category.name}</span>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      activeCategory === category.id
                        ? ''
                        : 'bg-gray-100 text-gray-600'
                    }`}
                    style={activeCategory === category.id ? {
                      backgroundColor: '#e1eaf2',
                      color: '#15579e'
                    } : {}}
                    >
                      {category.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Reports List */}
        <div className="lg:col-span-9">
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 text-sm"
                style={{
                  '--tw-ring-color': '#15579e'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#15579e';
                  e.target.style.boxShadow = '0 0 0 2px rgba(21, 87, 158, 0.2)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
                placeholder="Search reports by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Reports Grid */}
          <div className="grid grid-cols-12 gap-4">
            {loading ? (
              <div className="col-span-12">
                <LoadingScreen
                  title="Loading Reports..."
                  subtitle="Please wait while we fetch your reports and analytics"
                  variant="page"
                />
              </div>
            ) : filteredReports.map(report => (
              <div key={report.id} className="lg:col-span-6 mb-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow h-full">
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center">
                        <div className="p-2 bg-gray-100 rounded-lg mr-3">
                          {report.icon}
                        </div>
                        <div>
                          <h6 className="text-lg font-semibold text-gray-900 mb-1">{report.name}</h6>
                          <div className="flex items-center text-xs text-gray-500">
                            <FaClock className="mr-1" />
                            Last updated: {new Date(report.lastGenerated).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <Dropdown
                        trigger={
                          <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                            <FaEllipsisV className="w-4 h-4" />
                          </button>
                        }
                        align="right"
                      >
                        <DropdownItem
                          icon={<FaEye />}
                          onClick={() => handleViewReport(report)}
                        >
                          View Report
                        </DropdownItem>
                        <DropdownItem
                          icon={<FaDownload />}
                          onClick={() => handleExportReport(report, 'csv')}
                        >
                          Export CSV
                        </DropdownItem>
                        <DropdownItem
                          icon={<FaDownload />}
                          onClick={() => handleExportReport(report, 'excel')}
                        >
                          Export Excel
                        </DropdownItem>
                        <DropdownDivider />
                        <DropdownItem
                          icon={<FaCalendar />}
                          onClick={() => toast.info('Schedule feature coming soon!')}
                        >
                          Schedule Report
                        </DropdownItem>
                      </Dropdown>
                    </div>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">{report.description}</p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {getTypeBadge(report.type)}
                      {getFrequencyBadge(report.frequency)}
                      {getStatusBadge(report.status)}
                    </div>

                    <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                      <div className="text-xs text-gray-500">
                        Generated {new Date(report.lastGenerated).toLocaleDateString()}
                      </div>
                      <button
                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors"
                        style={{
                          backgroundColor: '#15579e',
                          borderColor: '#15579e'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = '#0f4682';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = '#15579e';
                        }}
                        onClick={() => handleViewReport(report)}
                      >
                        <FaEye className="w-4 h-4 mr-2" />
                        View Report
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {!loading && filteredReports.length === 0 && (
            <div className="col-span-12 text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <FaChartBar className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
              <p className="text-gray-600 mb-4">Try adjusting your search criteria or category filter.</p>
              <button
                className="inline-flex items-center px-4 py-2 text-sm font-medium border rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors"
                style={{
                  color: '#15579e',
                  backgroundColor: '#f0f4f8',
                  borderColor: '#e1eaf2'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#e1eaf2';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#f0f4f8';
                }}
                onClick={() => {
                  setSearchTerm('');
                  setActiveCategory('all');
                }}
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportsList;
