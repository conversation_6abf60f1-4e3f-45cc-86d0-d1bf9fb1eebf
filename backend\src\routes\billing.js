import express from 'express';
import { authenticate, requireTenant } from '../middleware/auth.js';
import {
  getBillingHistory,
  getInvoice,
  downloadInvoice,
  getPaymentMethods,
  addPaymentMethod,
  removePaymentMethod,
  getBillingSummary,
} from '../controllers/billingController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);
router.use(requireTenant);

/**
 * @swagger
 * /api/billing/history:
 *   get:
 *     summary: Get billing history
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Billing history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     invoices:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Invoice'
 *                     pagination:
 *                       type: object
 */
router.get('/history', getBillingHistory);

/**
 * @swagger
 * /api/billing/summary:
 *   get:
 *     summary: Get billing summary
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           default: 12
 *         description: Number of months for summary
 *     responses:
 *       200:
 *         description: Billing summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 */
router.get('/summary', getBillingSummary);

/**
 * @swagger
 * /api/billing/invoices/{invoiceId}:
 *   get:
 *     summary: Get invoice details
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: invoiceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     invoice:
 *                       $ref: '#/components/schemas/Invoice'
 */
router.get('/invoices/:invoiceId', getInvoice);

/**
 * @swagger
 * /api/billing/invoices/{invoiceId}/download:
 *   get:
 *     summary: Download invoice PDF
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: invoiceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice PDF
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       302:
 *         description: Redirect to Stripe PDF
 */
router.get('/invoices/:invoiceId/download', downloadInvoice);

/**
 * @swagger
 * /api/billing/payment-methods:
 *   get:
 *     summary: Get payment methods
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of payment methods
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     paymentMethods:
 *                       type: array
 */
router.get('/payment-methods', getPaymentMethods);

/**
 * @swagger
 * /api/billing/payment-methods:
 *   post:
 *     summary: Add payment method
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - paymentMethodId
 *             properties:
 *               paymentMethodId:
 *                 type: string
 *                 description: Stripe payment method ID
 *     responses:
 *       200:
 *         description: Payment method added
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
router.post('/payment-methods', addPaymentMethod);

/**
 * @swagger
 * /api/billing/payment-methods/{paymentMethodId}:
 *   delete:
 *     summary: Remove payment method
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: paymentMethodId
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment method ID
 *     responses:
 *       200:
 *         description: Payment method removed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */
router.delete('/payment-methods/:paymentMethodId', removePaymentMethod);

export default router;
