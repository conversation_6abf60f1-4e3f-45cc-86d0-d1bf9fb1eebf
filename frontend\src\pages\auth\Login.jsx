import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';
import { Button, Input, Label, Alert, Spinner } from '../../components/ui';
import { redirectManager } from '../../utils/redirectManager';

const Login = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading: authLoading } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const hasRedirected = useRef(false);

  // Redirect to dashboard if already authenticated on page load
  useEffect(() => {
    if (isAuthenticated && !authLoading && !hasRedirected.current) {
      console.log('User already authenticated, redirecting to dashboard...');
      hasRedirected.current = true;
      redirectManager.safeRedirect(navigate, '/dashboard');
    }
  }, [isAuthenticated, authLoading, navigate]); // Include dependencies

  // Cleanup redirect manager on unmount
  useEffect(() => {
    return () => {
      redirectManager.reset();
    };
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Prevent multiple submissions
    if (loading || authLoading) return;

    setLoading(true);
    setError('');

    try {
      console.log('Submitting login form for:', formData.email);

      // Call the login function from useAuth hook
      const result = await login({
        email: formData.email,
        password: formData.password
      });

      console.log('Login result:', result);

      if (result.success) {
        console.log('Login successful, redirecting to dashboard');
        toast.success('Login successful! Redirecting...');

        // Use redirect manager to prevent navigation throttling
        hasRedirected.current = true;
        redirectManager.safeRedirect(navigate, '/dashboard');
      } else {
        console.error('Login failed:', result.error);
        setError(result.error || 'Login failed. Please try again.');
        toast.error(result.error || 'Login failed. Please try again.');
      }
    } catch (err) {
      console.error('Login error:', err);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      const { message } = ErrorHandler.parseApiError(err, 'Unable to log in. Please check your credentials and try again.');

      setError(message);
      ErrorHandler.showError(err, 'Unable to log in. Please check your credentials and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading spinner during auth check
  if (authLoading) {
    return (
      <div className="flex justify-center items-center" style={{ minHeight: '300px' }}>
        <div className="text-center">
          <Spinner size="lg" className="mb-3" />
          <p className="text-gray-500">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Login - Preminfo CRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Welcome Message */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome Back!</h2>
          <p className="text-gray-600">Please sign in to your account to continue</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="danger" className="mb-4 rounded-lg border-l-4 border-red-500 bg-red-50 p-4">
              <div className="flex items-center">
                <i className="bi bi-exclamation-triangle text-red-500 mr-2"></i>
                <span className="text-red-700">{error}</span>
              </div>
            </Alert>
          )}

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center">
              <i className="bi bi-envelope mr-2 text-gray-500"></i>
              Email Address
            </Label>
            <div className="relative">
              <Input
                id="email"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email address"
                required
                autoComplete="email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              />
            </div>
          </div>

          {/* Password Field */}
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-semibold text-gray-700 flex items-center">
              <i className="bi bi-lock mr-2 text-gray-500"></i>
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter your password"
                required
                autoComplete="current-password"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-gray-50 focus:bg-white"
              />
            </div>
          </div>

          {/* Remember Me & Forgot Password */}
          <div className="flex items-center justify-between">
            <label className="flex items-center cursor-pointer group">
              <input
                type="checkbox"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
              />
              <span className="ml-2 text-sm text-gray-700 group-hover:text-gray-900 transition-colors duration-200">
                Remember me
              </span>
            </label>
            <Link
              to="/auth/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-700 font-medium no-underline transition-colors duration-200 hover:underline"
            >
              Forgot password?
            </Link>
          </div>

          {/* Sign In Button */}
          <Button
            type="submit"
            variant="primary"
            className="w-full py-3 px-4 btn-theme-primary font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200 focus:ring-4"
            style={{ '--tw-ring-color': 'rgba(var(--primary-rgb), 0.3)' }}
            disabled={loading || authLoading}
          >
            {loading || authLoading ? (
              <div className="flex items-center justify-center">
                <Spinner size="sm" className="mr-2" />
                <span>{loading ? 'Signing in...' : 'Loading...'}</span>
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <i className="bi bi-box-arrow-in-right mr-2"></i>
                <span>Sign In</span>
              </div>
            )}
          </Button>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">New to TallyCRM?</span>
            </div>
          </div>


          {/* Sign Up Link */}
          <div className="text-center">
            <Link
              to="/auth/register"
              className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium no-underline transition-colors duration-200 hover:underline"
            >
              <i className="bi bi-person-plus mr-2"></i>
              Create your account
            </Link>
          </div>
        </form>
      </div>
    </>
  );
};

export default Login;
