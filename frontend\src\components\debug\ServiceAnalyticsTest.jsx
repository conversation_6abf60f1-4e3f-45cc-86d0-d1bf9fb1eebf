import React, { useState, useEffect } from 'react';
import apiService from '../../services/api';

const ServiceAnalyticsTest = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const testServiceAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🧪 Testing Service Analytics API...');
      console.log('🔍 API Base URL:', apiService.defaults.baseURL);

      // Check authentication
      const token = localStorage.getItem('tallycrm_token');
      console.log('🔐 Auth Token Present:', !!token);
      console.log('🔐 Token Length:', token ? token.length : 0);
      if (token) {
        console.log('🔐 Token Preview:', token.substring(0, 20) + '...');
      }

      const response = await apiService.get('/reports/service-analytics', {
        params: {
          dateRange: 30,
          groupBy: 'day'
        }
      });

      console.log('✅ Service Analytics API Response:', response.data);
      setAnalyticsData(response.data);
      
    } catch (err) {
      console.error('❌ Service Analytics API Error:', err);
      console.error('❌ Error Response:', err.response?.data);
      console.error('❌ Error Status:', err.response?.status);
      setError(`${err.message} (Status: ${err.response?.status || 'Unknown'})`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testServiceAnalytics();
  }, []);

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-lg font-bold mb-4">Service Analytics API Test</h2>
      
      <div className="mb-4 p-3 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Authentication Status</h3>
        <p><strong>Token Present:</strong> {localStorage.getItem('tallycrm_token') ? '✅ Yes' : '❌ No'}</p>
        <p><strong>API Base URL:</strong> {apiService.defaults.baseURL}</p>
      </div>

      <button
        onClick={testServiceAnalytics}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        disabled={loading}
      >
        {loading ? 'Testing...' : 'Test API'}
      </button>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>Error:</strong> {error}
          {error.includes('401') && (
            <div className="mt-2 text-sm">
              <strong>Authentication Issue:</strong> Please make sure you're logged in.
              <br />
              <strong>Admin Credentials:</strong> <EMAIL> / Admin@123
            </div>
          )}
        </div>
      )}

      {analyticsData && (
        <div className="space-y-4">
          <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <strong>Success!</strong> API returned data
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="p-3 bg-blue-50 rounded">
              <h3 className="font-semibold">Free Calls</h3>
              <p className="text-2xl font-bold text-green-600">
                {analyticsData.data?.summary?.freeCalls || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-50 rounded">
              <h3 className="font-semibold">AMC Calls</h3>
              <p className="text-2xl font-bold text-blue-600">
                {analyticsData.data?.summary?.amcCalls || 0}
              </p>
            </div>
            <div className="p-3 bg-blue-50 rounded">
              <h3 className="font-semibold">Paid Calls</h3>
              <p className="text-2xl font-bold text-orange-600">
                {analyticsData.data?.summary?.paidCalls || 0}
              </p>
            </div>
          </div>

          <details className="mt-4">
            <summary className="cursor-pointer font-semibold">Raw API Response</summary>
            <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
              {JSON.stringify(analyticsData, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default ServiceAnalyticsTest;
