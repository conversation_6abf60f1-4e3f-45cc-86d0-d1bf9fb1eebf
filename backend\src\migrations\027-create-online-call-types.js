import { DataTypes } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';

export const up = async (queryInterface) => {
  // Create online_call_types table
  await queryInterface.createTable('online_call_types', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'The call type/issue name',
    },
    category: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: 'For grouping similar types (e.g., GST, Installation, Technical Support)',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed description of the call type',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'For custom ordering in dropdowns',
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Multi-tenancy support',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for performance
  await queryInterface.addIndex('online_call_types', ['tenant_id']);
  await queryInterface.addIndex('online_call_types', ['category']);
  await queryInterface.addIndex('online_call_types', ['is_active']);
  await queryInterface.addIndex('online_call_types', ['sort_order']);
  await queryInterface.addIndex('online_call_types', ['name', 'tenant_id'], {
    unique: true,
    name: 'unique_name_per_tenant'
  });

  // Get the default tenant ID (assuming there's at least one tenant)
  const tenants = await queryInterface.sequelize.query(
    'SELECT id FROM tenants LIMIT 1',
    { type: queryInterface.sequelize.QueryTypes.SELECT }
  );

  if (tenants.length === 0) {
    console.warn('No tenants found. Skipping default data insertion.');
    return;
  }

  const defaultTenantId = tenants[0].id;

  // Insert default online call types with categories
  const defaultCallTypes = [
    // GST Related
    { name: 'GST 3B Doubts', category: 'GST', sort_order: 1 },
    { name: 'GST CALCULATION ISSUE', category: 'GST', sort_order: 2 },
    { name: 'GST Doubts', category: 'GST', sort_order: 3 },
    { name: 'GST Filing Doubts', category: 'GST', sort_order: 4 },
    { name: 'GST Mismatch', category: 'GST', sort_order: 5 },
    { name: 'GSTR 1 Doubts', category: 'GST', sort_order: 6 },
    { name: 'GSTR 2A', category: 'GST', sort_order: 7 },
    { name: 'GSTR 2A Doubts', category: 'GST', sort_order: 8 },
    { name: 'GSTR 2 Doubts', category: 'GST', sort_order: 9 },
    { name: 'GSTR 2B Demo', category: 'GST', sort_order: 10 },
    { name: 'GSTR 2B', category: 'GST', sort_order: 11 },
    { name: 'GSTR2B Excel Import', category: 'GST', sort_order: 12 },
    { name: '2B Import to Tally', category: 'GST', sort_order: 13 },
    { name: 'GST Portal Filing Doubts', category: 'GST', sort_order: 14 },
    { name: 'Gst RCM Filing Doubts', category: 'GST', sort_order: 15 },
    { name: 'GstR Doubts', category: 'GST', sort_order: 16 },
    { name: 'GST Update', category: 'GST', sort_order: 17 },
    { name: 'Rcm Entry Doubts', category: 'GST', sort_order: 18 },
    { name: 'HSN Code Issue', category: 'GST', sort_order: 19 },
    { name: 'Hsn Code Issue', category: 'GST', sort_order: 20 },

    // Installation & Setup
    { name: 'New Installation', category: 'Installation', sort_order: 21 },
    { name: 'New Release Update', category: 'Installation', sort_order: 22 },
    { name: 'Old Release Installation', category: 'Installation', sort_order: 23 },
    { name: 'Re Installation', category: 'Installation', sort_order: 24 },
    { name: 'Reinstallation', category: 'Installation', sort_order: 25 },
    { name: 'SU Instalation', category: 'Installation', sort_order: 26 },
    { name: 'MU Instalation', category: 'Installation', sort_order: 27 },
    { name: 'SU Installation', category: 'Installation', sort_order: 28 },
    { name: 'Auditor Licence Instalation', category: 'Installation', sort_order: 29 },

    // License & Activation
    { name: 'License Reactivation', category: 'License', sort_order: 30 },
    { name: 'License Surrender', category: 'License', sort_order: 31 },
    { name: 'Server 9 Activation', category: 'License', sort_order: 32 },
    { name: 'TRIAL PACK ACTIVATION', category: 'License', sort_order: 33 },
    { name: 'Tally Server Licence', category: 'License', sort_order: 34 },
    { name: 'Tally Server Trail', category: 'License', sort_order: 35 },

    // Data Management
    { name: 'Data Issue', category: 'Data Management', sort_order: 36 },
    { name: 'Data Migration', category: 'Data Management', sort_order: 37 },
    { name: 'Data Restore', category: 'Data Management', sort_order: 38 },
    { name: 'Data Splitting', category: 'Data Management', sort_order: 39 },
    { name: 'Data Synchronization', category: 'Data Management', sort_order: 40 },
    { name: 'Data Backup', category: 'Data Management', sort_order: 41 },
    { name: 'Data Configuration', category: 'Data Management', sort_order: 42 },
    { name: 'Data Rewrite', category: 'Data Management', sort_order: 43 },
    { name: 'Data Merge', category: 'Data Management', sort_order: 44 },
    { name: 'Data Entry Work', category: 'Data Management', sort_order: 45 },
    { name: 'Tally Data Recovery', category: 'Data Management', sort_order: 46 },
    { name: 'Rewrite', category: 'Data Management', sort_order: 47 },

    // Technical Support
    { name: 'Crack Version', category: 'Technical Support', sort_order: 48 },
    { name: 'Crack Version Issue', category: 'Technical Support', sort_order: 49 },
    { name: 'Tally Crack', category: 'Technical Support', sort_order: 50 },
    { name: 'Tally Slow Issue', category: 'Technical Support', sort_order: 51 },
    { name: 'Server Issue', category: 'Technical Support', sort_order: 52 },
    { name: 'Server Changing', category: 'Technical Support', sort_order: 53 },
    { name: 'Security Control', category: 'Technical Support', sort_order: 54 },
    { name: 'Remote Access', category: 'Technical Support', sort_order: 55 },
    { name: 'Remote User', category: 'Technical Support', sort_order: 56 },
    { name: 'Browser Access', category: 'Technical Support', sort_order: 57 },
    { name: 'Browser Access Doubts', category: 'Technical Support', sort_order: 58 },
    { name: 'Web Broswer Doubts', category: 'Technical Support', sort_order: 59 },
    { name: 'Password Recovery', category: 'Technical Support', sort_order: 60 },
    { name: 'Possword Change', category: 'Technical Support', sort_order: 61 },

    // Email & Communication
    { name: 'Email Configuration', category: 'Email & Communication', sort_order: 62 },
    { name: 'Email Doubts', category: 'Email & Communication', sort_order: 63 },
    { name: 'Mail Id Change', category: 'Email & Communication', sort_order: 64 },
    { name: 'Email ID Change', category: 'Email & Communication', sort_order: 65 },
    { name: 'SMS Issue', category: 'Email & Communication', sort_order: 66 },
    { name: 'SMS', category: 'Email & Communication', sort_order: 67 },
    { name: 'WhatsApp', category: 'Email & Communication', sort_order: 68 },

    // Mobile App
    { name: 'Mobile App', category: 'Mobile App', sort_order: 69 },
    { name: 'Mobile App Demo', category: 'Mobile App', sort_order: 70 },
    { name: 'Mobile App Sync Issue', category: 'Mobile App', sort_order: 71 },
    { name: 'Mobile App Payment', category: 'Mobile App', sort_order: 72 },
    { name: 'Mob App Issuse', category: 'Mobile App', sort_order: 73 },
    { name: 'Tally Mobile Application', category: 'Mobile App', sort_order: 74 },

    // Printing & Configuration
    { name: 'Cheque Print', category: 'Printing & Configuration', sort_order: 75 },
    { name: 'Print Configure', category: 'Printing & Configuration', sort_order: 76 },
    { name: 'Logo Configuration', category: 'Printing & Configuration', sort_order: 77 },
    { name: 'Proxy Configuration', category: 'Printing & Configuration', sort_order: 78 },
    { name: 'Configure Setting', category: 'Printing & Configuration', sort_order: 79 },

    // Import/Export
    { name: 'Excel to Tally Import', category: 'Import/Export', sort_order: 80 },
    { name: 'Excel to Tally Import Doubts', category: 'Import/Export', sort_order: 81 },
    { name: 'Export and Import', category: 'Import/Export', sort_order: 82 },
    { name: 'Export Import', category: 'Import/Export', sort_order: 83 },
    { name: 'Bank Statment Import', category: 'Import/Export', sort_order: 84 },
    { name: 'Tally to Excel Export', category: 'Import/Export', sort_order: 85 },
    { name: 'PDF to Tally Import', category: 'Import/Export', sort_order: 86 },

    // E-Invoice & Digital
    { name: 'E Invoice', category: 'E-Invoice & Digital', sort_order: 87 },
    { name: 'E Invoice Doubts', category: 'E-Invoice & Digital', sort_order: 88 },
    { name: 'E Invoice Config', category: 'E-Invoice & Digital', sort_order: 89 },
    { name: 'Digital Sign', category: 'E-Invoice & Digital', sort_order: 90 },
    { name: 'E-Way Bill Doubts', category: 'E-Invoice & Digital', sort_order: 91 },

    // AMC & TSS
    { name: 'AMC- Multi User', category: 'AMC & TSS', sort_order: 92 },
    { name: 'AMC- Single User', category: 'AMC & TSS', sort_order: 93 },
    { name: 'Amc Renewal', category: 'AMC & TSS', sort_order: 94 },
    { name: 'Amc Renewal Issue', category: 'AMC & TSS', sort_order: 95 },
    { name: 'Amc Requirement', category: 'AMC & TSS', sort_order: 96 },
    { name: 'AMC SU Payment', category: 'AMC & TSS', sort_order: 97 },
    { name: 'AMC MU Payment', category: 'AMC & TSS', sort_order: 98 },
    { name: 'TSS SU', category: 'AMC & TSS', sort_order: 99 },
    { name: 'TSS MU', category: 'AMC & TSS', sort_order: 100 },
    { name: 'Tss MU', category: 'AMC & TSS', sort_order: 101 },
    { name: 'Tss SU', category: 'AMC & TSS', sort_order: 102 },
    { name: 'TSS SU Payment', category: 'AMC & TSS', sort_order: 103 },
    { name: 'TSS MU Payment', category: 'AMC & TSS', sort_order: 104 },
    { name: 'TSS OLD', category: 'AMC & TSS', sort_order: 105 },
    { name: 'Tss Renewal', category: 'AMC & TSS', sort_order: 106 },
    { name: 'TSS Auditor', category: 'AMC & TSS', sort_order: 107 },

    // Proposals & Upgrades
    { name: 'SU Proposal', category: 'Proposals & Upgrades', sort_order: 108 },
    { name: 'MU Proposal', category: 'Proposals & Upgrades', sort_order: 109 },
    { name: 'TSS SU Proposal', category: 'Proposals & Upgrades', sort_order: 110 },
    { name: 'TSS MU Proposal', category: 'Proposals & Upgrades', sort_order: 111 },
    { name: 'SU to Mu Upgrade', category: 'Proposals & Upgrades', sort_order: 112 },
    { name: 'Muli User Upgrade', category: 'Proposals & Upgrades', sort_order: 113 },

    // Demos & Training
    { name: 'Single User Demo', category: 'Demos & Training', sort_order: 114 },
    { name: 'Multi User Demo', category: 'Demos & Training', sort_order: 115 },
    { name: 'Tally Demo', category: 'Demos & Training', sort_order: 116 },
    { name: 'Payroll Demo', category: 'Demos & Training', sort_order: 117 },
    { name: 'Garments Module Demo', category: 'Demos & Training', sort_order: 118 },
    { name: 'Prime Demo TSS', category: 'Demos & Training', sort_order: 119 },
    { name: 'Prime Demo Pack', category: 'Demos & Training', sort_order: 120 },
    { name: 'Prime Demo', category: 'Demos & Training', sort_order: 121 },
    { name: '3.0 Demo', category: 'Demos & Training', sort_order: 122 },
    { name: 'Tally Cloud Demo', category: 'Demos & Training', sort_order: 123 },
    { name: 'Tally Training', category: 'Demos & Training', sort_order: 124 },
    { name: 'Training', category: 'Demos & Training', sort_order: 125 },

    // Accounting & Finance
    { name: 'Bank Doubts', category: 'Accounting & Finance', sort_order: 126 },
    { name: 'Bank Reconsilation', category: 'Accounting & Finance', sort_order: 127 },
    { name: 'Reconcilation Isuue', category: 'Accounting & Finance', sort_order: 128 },
    { name: 'Ledger Creation Doubts', category: 'Accounting & Finance', sort_order: 129 },
    { name: 'Ledger Opening Balance', category: 'Accounting & Finance', sort_order: 130 },
    { name: 'Opening Balance', category: 'Accounting & Finance', sort_order: 131 },
    { name: 'Opening Stock Issue', category: 'Accounting & Finance', sort_order: 132 },
    { name: 'Balance Sheet Doubts', category: 'Accounting & Finance', sort_order: 133 },
    { name: 'Trail Balance', category: 'Accounting & Finance', sort_order: 134 },
    { name: 'Outstanding Report', category: 'Accounting & Finance', sort_order: 135 },
    { name: 'Statement Issue', category: 'Accounting & Finance', sort_order: 136 },
    { name: 'Statement', category: 'Accounting & Finance', sort_order: 137 },
    { name: 'INTEREST CALCULATION ISSUE', category: 'Accounting & Finance', sort_order: 138 },
    { name: 'Discount Calculation Issue', category: 'Accounting & Finance', sort_order: 139 },
    { name: 'Adujestment Issue', category: 'Accounting & Finance', sort_order: 140 },
    { name: 'Multi Currency', category: 'Accounting & Finance', sort_order: 141 },
    { name: 'Cost Centre', category: 'Accounting & Finance', sort_order: 142 },
    { name: 'Budget', category: 'Accounting & Finance', sort_order: 143 },

    // TDS & TCS
    { name: 'TDS Doubts', category: 'TDS & TCS', sort_order: 144 },
    { name: 'TCS Doubt', category: 'TDS & TCS', sort_order: 145 },

    // Vouchers & Entries
    { name: 'Voucher Class Doubts', category: 'Vouchers & Entries', sort_order: 146 },
    { name: 'Voucher Entry Doubts', category: 'Vouchers & Entries', sort_order: 147 },
    { name: 'Voucher Entry', category: 'Vouchers & Entries', sort_order: 148 },
    { name: 'Voucher Type Alteration', category: 'Vouchers & Entries', sort_order: 149 },
    { name: 'Optional Voucher', category: 'Vouchers & Entries', sort_order: 150 },

    // Inventory & Stock
    { name: 'Stock Item Issue', category: 'Inventory & Stock', sort_order: 151 },
    { name: 'Stock Summary Reports Issue', category: 'Inventory & Stock', sort_order: 152 },
    { name: 'Stock Reports', category: 'Inventory & Stock', sort_order: 153 },
    { name: 'Closing Stock', category: 'Inventory & Stock', sort_order: 154 },

    // Billing & Invoicing
    { name: 'Invoice Isssue', category: 'Billing & Invoicing', sort_order: 155 },
    { name: 'Invoice', category: 'Billing & Invoicing', sort_order: 156 },
    { name: 'Billing Issue', category: 'Billing & Invoicing', sort_order: 157 },
    { name: 'Bill Wise Doubts', category: 'Billing & Invoicing', sort_order: 158 },
    { name: 'GST Bill', category: 'Billing & Invoicing', sort_order: 159 },
    { name: 'Service Bill', category: 'Billing & Invoicing', sort_order: 160 },
    { name: 'Debit /Credit Note Doubts', category: 'Billing & Invoicing', sort_order: 161 },
    { name: 'Bill Courier', category: 'Billing & Invoicing', sort_order: 162 },

    // Orders & Sales
    { name: 'Purchase Order', category: 'Orders & Sales', sort_order: 163 },
    { name: 'Sales Order', category: 'Orders & Sales', sort_order: 164 },
    { name: 'Sales/purchase Order Reports', category: 'Orders & Sales', sort_order: 165 },
    { name: 'Sales Register Reports', category: 'Orders & Sales', sort_order: 166 },
    { name: 'Quatation Formate', category: 'Orders & Sales', sort_order: 167 },
    { name: 'Price List', category: 'Orders & Sales', sort_order: 168 },
    { name: 'Exempt Sales', category: 'Orders & Sales', sort_order: 169 },

    // Cloud & Backup
    { name: 'Tally Cloud', category: 'Cloud & Backup', sort_order: 170 },
    { name: 'Tally Cloud Backup', category: 'Cloud & Backup', sort_order: 171 },
    { name: 'Tally Cloud Issues', category: 'Cloud & Backup', sort_order: 172 },
    { name: 'Tally Cloud Renewal', category: 'Cloud & Backup', sort_order: 173 },
    { name: 'Cloud Storage', category: 'Cloud & Backup', sort_order: 174 },
    { name: 'Cloud Storage Remove', category: 'Cloud & Backup', sort_order: 175 },
    { name: 'Autobackup', category: 'Cloud & Backup', sort_order: 176 },
    { name: 'Backup Issue', category: 'Cloud & Backup', sort_order: 177 },
    { name: 'Google Drive Backup', category: 'Cloud & Backup', sort_order: 178 },

    // TDL & Addons
    { name: 'Free Addons', category: 'TDL & Addons', sort_order: 179 },
    { name: 'Addons', category: 'TDL & Addons', sort_order: 180 },
    { name: 'TDL File Expiry', category: 'TDL & Addons', sort_order: 181 },
    { name: 'TDL Doubts', category: 'TDL & Addons', sort_order: 182 },
    { name: 'TDL File  Addedd', category: 'TDL & Addons', sort_order: 183 },
    { name: 'Account TDL Issue', category: 'TDL & Addons', sort_order: 184 },
    { name: 'Account TDL Config', category: 'TDL & Addons', sort_order: 185 },
    { name: 'SU + TDL', category: 'TDL & Addons', sort_order: 186 },
    { name: 'Conntact Us TDL', category: 'TDL & Addons', sort_order: 187 },
    { name: 'Whats App TDL', category: 'TDL & Addons', sort_order: 188 },
    { name: 'SMS TDL', category: 'TDL & Addons', sort_order: 189 },

    // Payments & Billing
    { name: 'Payment', category: 'Payments & Billing', sort_order: 190 },

    // General Support
    { name: 'Customization', category: 'General Support', sort_order: 191 },
    { name: 'Customaization', category: 'General Support', sort_order: 192 },
    { name: 'General Doubts', category: 'General Support', sort_order: 193 },
    { name: 'Tally Doubts', category: 'General Support', sort_order: 194 },
    { name: 'Prime Doubts', category: 'General Support', sort_order: 195 },
    { name: 'Garments Module Doubts', category: 'General Support', sort_order: 196 },
    { name: 'Payroll Doubts', category: 'General Support', sort_order: 197 },

    // Miscellaneous
    { name: 'Monthly Visit', category: 'Miscellaneous', sort_order: 198 },
    { name: 'Client Connecting', category: 'Miscellaneous', sort_order: 199 },
    { name: 'Client Connection', category: 'Miscellaneous', sort_order: 200 },
    { name: 'Period Changing', category: 'Miscellaneous', sort_order: 201 },
    { name: 'Grid Line', category: 'Miscellaneous', sort_order: 202 },
    { name: 'Group Creation', category: 'Miscellaneous', sort_order: 203 },
    { name: 'Unused Ledger Deletion', category: 'Miscellaneous', sort_order: 204 },
    { name: 'VAT & CST Doubts Cleared', category: 'Miscellaneous', sort_order: 205 },
    { name: 'Dc Doubts', category: 'Miscellaneous', sort_order: 206 },
    { name: 'Jobwork Doubt', category: 'Miscellaneous', sort_order: 207 },
    { name: 'Rental Tally', category: 'Miscellaneous', sort_order: 208 },
    { name: 'Flo Biz', category: 'Miscellaneous', sort_order: 209 },
    { name: 'TVU', category: 'Miscellaneous', sort_order: 210 },
    { name: 'Tally Virtual User', category: 'Miscellaneous', sort_order: 211 },
    { name: 'Ts Plus', category: 'Miscellaneous', sort_order: 212 },
    { name: 'LCP Request', category: 'Miscellaneous', sort_order: 213 },
    { name: 'Tally.Server 9 South Asia', category: 'Miscellaneous', sort_order: 214 },
    { name: 'Tally Developer 2.7', category: 'Miscellaneous', sort_order: 215 },
    { name: 'Tally Implementation', category: 'Miscellaneous', sort_order: 216 },
    { name: 'Part Time Accounts', category: 'Miscellaneous', sort_order: 217 },
    { name: 'Internal Audit', category: 'Miscellaneous', sort_order: 218 },
    { name: 'Edit Log Enable', category: 'Miscellaneous', sort_order: 219 },
    { name: 'Edit Log Diable', category: 'Miscellaneous', sort_order: 220 },
    { name: 'Edit Log Configure', category: 'Miscellaneous', sort_order: 221 },
    { name: 'Edit Log Disable', category: 'Miscellaneous', sort_order: 222 },
    { name: '3.0 Update', category: 'Miscellaneous', sort_order: 223 },
    { name: '3.0 Remove', category: 'Miscellaneous', sort_order: 224 },
    { name: 'Prime Updation', category: 'Miscellaneous', sort_order: 225 },
    { name: 'Barcode', category: 'Miscellaneous', sort_order: 226 },
    { name: 'Capsules Update', category: 'Miscellaneous', sort_order: 227 },
    { name: 'Multi User Free Services', category: 'Miscellaneous', sort_order: 228 },
    { name: 'Service Issue', category: 'Miscellaneous', sort_order: 229 },
    { name: 'Tally.Net Password', category: 'Miscellaneous', sort_order: 230 },
  ];

  // Prepare data for bulk insert
  const callTypesData = defaultCallTypes.map(callType => ({
    id: uuidv4(),
    name: callType.name,
    category: callType.category,
    description: `${callType.category} - ${callType.name}`,
    is_active: true,
    sort_order: callType.sort_order,
    tenant_id: defaultTenantId,
    created_at: new Date(),
    updated_at: new Date(),
  }));

  // Insert in batches to avoid query size limits
  const batchSize = 50;
  for (let i = 0; i < callTypesData.length; i += batchSize) {
    const batch = callTypesData.slice(i, i + batchSize);
    await queryInterface.bulkInsert('online_call_types', batch);
  }

  console.log(`✅ Inserted ${callTypesData.length} online call types`);
};

export const down = async (queryInterface) => {
  // Drop indexes first
  await queryInterface.removeIndex('online_call_types', 'unique_name_per_tenant');
  await queryInterface.removeIndex('online_call_types', ['sort_order']);
  await queryInterface.removeIndex('online_call_types', ['is_active']);
  await queryInterface.removeIndex('online_call_types', ['category']);
  await queryInterface.removeIndex('online_call_types', ['tenant_id']);
  
  // Drop table
  await queryInterface.dropTable('online_call_types');
};
