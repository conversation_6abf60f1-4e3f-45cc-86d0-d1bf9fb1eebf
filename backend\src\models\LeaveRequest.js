import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LeaveRequest = sequelize.define('LeaveRequest', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Employee requesting leave',
    },
    leave_type_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      comment: 'Type of leave requested',
    },
    request_number: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Unique request number for tracking',
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Leave start date',
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Leave end date',
    },
    total_days: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      comment: 'Total number of leave days',
    },
    is_half_day: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is a half-day leave',
    },
    half_day_period: {
      type: DataTypes.ENUM('first_half', 'second_half'),
      allowNull: true,
      comment: 'Which half of the day for half-day leave',
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Reason for leave request',
    },
    emergency_contact: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Emergency contact during leave',
    },
    work_handover_to: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Employee to whom work is handed over',
    },
    handover_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Work handover notes',
    },
    status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'cancelled', 'withdrawn'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Current status of leave request',
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'normal',
      comment: 'Priority of leave request',
    },
    requested_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who submitted the request',
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who approved/rejected the request',
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when request was approved/rejected',
    },
    rejection_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for rejection',
    },
    manager_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Comments from manager/approver',
    },
    hr_comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Comments from HR',
    },
    documents: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Array of supporting documents',
    },
    applied_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'When the leave was applied',
    },
    is_compensatory: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is compensatory leave',
    },
    compensatory_work_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Date when compensatory work was done',
    },
    auto_approved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this was auto-approved by system',
    },
    notification_sent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether notification has been sent',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'leave_requests',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['employee_id'],
      },
      {
        fields: ['leave_type_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['start_date'],
      },
      {
        fields: ['end_date'],
      },
      {
        fields: ['tenant_id', 'request_number'],
        unique: true,
        name: 'unique_tenant_request_number',
      },
      {
        fields: ['applied_at'],
      },
      {
        fields: ['approved_at'],
      },
    ],
  });

  // Instance methods
  LeaveRequest.prototype.calculateWorkingDays = function(holidays = []) {
    const start = new Date(this.start_date);
    const end = new Date(this.end_date);
    let workingDays = 0;
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const dayOfWeek = date.getDay();
      const dateString = date.toISOString().split('T')[0];
      
      // Skip weekends (assuming Saturday=6, Sunday=0)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        // Skip holidays
        if (!holidays.includes(dateString)) {
          workingDays++;
        }
      }
    }
    
    return this.is_half_day ? workingDays * 0.5 : workingDays;
  };

  LeaveRequest.prototype.isOverlapping = function(otherRequest) {
    const thisStart = new Date(this.start_date);
    const thisEnd = new Date(this.end_date);
    const otherStart = new Date(otherRequest.start_date);
    const otherEnd = new Date(otherRequest.end_date);
    
    return thisStart <= otherEnd && thisEnd >= otherStart;
  };

  LeaveRequest.prototype.canBeApproved = function() {
    return this.status === 'pending';
  };

  LeaveRequest.prototype.canBeCancelled = function() {
    return ['pending', 'approved'].includes(this.status);
  };

  LeaveRequest.prototype.isExpired = function() {
    const today = new Date();
    const startDate = new Date(this.start_date);
    return startDate < today && this.status === 'pending';
  };

  // Class methods
  LeaveRequest.generateRequestNumber = function(tenantId, year) {
    const prefix = 'LR';
    const yearSuffix = year.toString().slice(-2);
    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}${yearSuffix}${timestamp}`;
  };

  LeaveRequest.getStatusOptions = function() {
    return [
      { value: 'pending', label: 'Pending', color: '#FFA726' },
      { value: 'approved', label: 'Approved', color: '#66BB6A' },
      { value: 'rejected', label: 'Rejected', color: '#EF5350' },
      { value: 'cancelled', label: 'Cancelled', color: '#BDBDBD' },
      { value: 'withdrawn', label: 'Withdrawn', color: '#9E9E9E' },
    ];
  };

  LeaveRequest.getPriorityOptions = function() {
    return [
      { value: 'low', label: 'Low', color: '#81C784' },
      { value: 'normal', label: 'Normal', color: '#64B5F6' },
      { value: 'high', label: 'High', color: '#FFB74D' },
      { value: 'urgent', label: 'Urgent', color: '#E57373' },
    ];
  };

  // Associations
  LeaveRequest.associate = function(models) {
    LeaveRequest.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    LeaveRequest.belongsTo(models.Executive, {
      foreignKey: 'employee_id',
      as: 'employee',
    });

    LeaveRequest.belongsTo(models.LeaveType, {
      foreignKey: 'leave_type_id',
      as: 'leaveType',
    });

    LeaveRequest.belongsTo(models.User, {
      foreignKey: 'requested_by',
      as: 'requester',
    });

    LeaveRequest.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver',
    });

    LeaveRequest.belongsTo(models.Executive, {
      foreignKey: 'work_handover_to',
      as: 'handoverEmployee',
    });
  };

  return LeaveRequest;
}
