import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import {
  <PERSON>aCog,
  FaUser,
  FaShieldAlt,
  FaBell,
  FaEnvelope,
  FaDatabase,
  FaPalette,
  FaGlobe,
  FaSave,
  FaEdit,
  FaCheck,
  FaTimes
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const SettingsList = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [editingField, setEditingField] = useState(null);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    // General Settings (from tenant)
    companyName: '',
    companyEmail: '',
    companyPhone: '',
    companyAddress: '',
    timezone: 'Asia/Kolkata',
    dateFormat: 'DD/MM/YYYY',
    currency: 'INR',
    language: 'English',

    // User Preferences
    defaultDashboard: 'overview',
    itemsPerPage: 10,
    autoSave: true,
    showNotifications: true,
    darkMode: false,

    // Security Settings (from system)
    sessionTimeout: 30,
    passwordExpiry: 90,
    twoFactorAuth: false,
    loginAttempts: 5,

    // Notification Settings (from user)
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,
    monthlyReports: true,

    // Email Settings (from tenant)
    smtpServer: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '••••••••',
    emailSignature: 'Best regards,\nTallyCRM Team',

    // Database Settings (from system)
    backupFrequency: 'daily',
    retentionPeriod: 365,
    autoBackup: true,

    // Appearance Settings (from tenant)
    primaryColor: '#007bff',
    secondaryColor: '#6c757d',
    logoUrl: '/assets/logo.png',
    favicon: '/assets/favicon.ico'
  });

  // Fetch settings data from API
  useEffect(() => {
    fetchSettingsData();
  }, []);

  const fetchSettingsData = async () => {
    try {
      setLoading(true);

      // Fetch data from different settings endpoints
      const [tenantRes, userRes, systemRes] = await Promise.all([
        apiService.get('/settings/tenant').catch(() => ({ data: { data: { tenant: {} } } })),
        apiService.get('/settings/user').catch(() => ({ data: { data: { user: {} } } })),
        apiService.get('/settings/system').catch(() => ({ data: { data: { settings: {} } } }))
      ]);

      const tenantData = tenantRes.data?.data?.tenant || {};
      const userData = userRes.data?.data?.user || {};
      const systemData = systemRes.data?.data?.settings || {};

      // Merge API data with current settings
      setSettings(prev => ({
        ...prev,
        // General Settings from tenant
        companyName: tenantData.name || prev.companyName,
        companyEmail: tenantData.email || prev.companyEmail,
        companyPhone: tenantData.phone || prev.companyPhone,
        companyAddress: tenantData.address || prev.companyAddress,
        timezone: tenantData.timezone || prev.timezone,
        dateFormat: tenantData.date_format || prev.dateFormat,
        currency: tenantData.currency || prev.currency,
        logoUrl: tenantData.logo_url || prev.logoUrl,

        // User Preferences
        language: userData.language || prev.language,
        emailNotifications: userData.email_notifications ?? prev.emailNotifications,
        showNotifications: userData.notifications_enabled ?? prev.showNotifications,
        darkMode: userData.theme === 'dark',

        // Security Settings from system
        sessionTimeout: systemData.session_timeout ? systemData.session_timeout / 60 : prev.sessionTimeout,
        passwordExpiry: systemData.password_policy?.min_length || prev.passwordExpiry,
        loginAttempts: systemData.max_login_attempts || prev.loginAttempts,
      }));

    } catch (error) {
      console.error('Error fetching settings data:', error);
      toast.error('Failed to load settings data');
    } finally {
      setLoading(false);
    }
  };

  const settingsTabs = [
    { id: 'general', name: 'General', icon: <FaCog /> },
    { id: 'user', name: 'User Preferences', icon: <FaUser /> },
    { id: 'security', name: 'Security', icon: <FaShieldAlt /> },
    { id: 'notifications', name: 'Notifications', icon: <FaBell /> },
    { id: 'email', name: 'Email', icon: <FaEnvelope /> },
    { id: 'database', name: 'Database', icon: <FaDatabase /> },
    { id: 'appearance', name: 'Appearance', icon: <FaPalette /> }
  ];

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async (key) => {
    try {
      const value = settings[key];
      let endpoint = '';
      let payload = {};

      // Determine which API endpoint to use based on the setting
      if (['companyName', 'companyEmail', 'companyPhone', 'companyAddress', 'timezone', 'dateFormat', 'currency', 'logoUrl', 'primaryColor', 'secondaryColor', 'favicon'].includes(key)) {
        // Tenant settings
        endpoint = '/settings/tenant';
        const fieldMap = {
          companyName: 'name',
          companyEmail: 'email',
          companyPhone: 'phone',
          companyAddress: 'address',
          dateFormat: 'date_format',
          logoUrl: 'logo_url'
        };
        const apiKey = fieldMap[key] || key;
        payload = { [apiKey]: value };
      } else if (['language', 'emailNotifications', 'showNotifications', 'darkMode'].includes(key)) {
        // User preferences
        endpoint = '/settings/user';
        const fieldMap = {
          emailNotifications: 'email_notifications',
          showNotifications: 'notifications_enabled',
          darkMode: 'theme'
        };
        const apiKey = fieldMap[key] || key;
        const apiValue = key === 'darkMode' ? (value ? 'dark' : 'light') : value;
        payload = { [apiKey]: apiValue };
      } else {
        // System settings (admin only) - for now just show success
        setEditingField(null);
        toast.success('Setting updated successfully');
        return;
      }

      const response = await apiService.put(endpoint, payload);

      if (response.data?.success) {
        setEditingField(null);
        toast.success('Setting updated successfully');
      } else {
        toast.error(response.data?.message || 'Failed to update setting');
      }
    } catch (error) {
      console.error('Error updating setting:', error);
      toast.error('Failed to update setting');
    }
  };

  const handleCancel = () => {
    setEditingField(null);
  };

  const renderSettingField = (key, label, type = 'text', options = null) => {
    const isEditing = editingField === key;
    const value = settings[key];

    return (
      <div className="mb-6 p-6 rounded-2xl border-2 transition-all duration-200 shadow-lg hover:shadow-xl form-section-background form-section-border">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <label className="block text-sm font-bold mb-2 flex items-center form-label-primary">
              <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2" style={{ backgroundColor: 'var(--primary-color)' }}>
                <span className="text-xs" style={{ color: 'var(--primary-text)' }}>⚙️</span>
              </div>
              {label}
            </label>
            <p className="text-xs px-3 py-1 rounded-full inline-block" style={{
              color: 'var(--primary-color)',
              backgroundColor: 'rgba(var(--primary-rgb), 0.1)'
            }}
            >
              {type === 'checkbox' ? '🔘 Toggle this setting on or off' :
                type === 'color' ? '🎨 Choose a color value' :
                  type === 'select' ? '📋 Select from available options' :
                    '✏️ Enter a value for this setting'}
            </p>
          </div>
          {!isEditing ? (
            <button
              className="inline-flex items-center px-4 py-2 text-xs font-bold border border-transparent rounded-xl focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
              style={{
                backgroundColor: 'var(--primary-color)',
                color: 'var(--primary-text)',
                '--tw-ring-color': 'var(--primary-color)'
              }}
              onClick={() => setEditingField(key)}
            >
              <FaEdit className="w-3 h-3 mr-1" />
              Edit
            </button>
          ) : (
            <div className="flex gap-2">
              <button
                className="inline-flex items-center px-4 py-2 text-xs font-bold text-white bg-green-500 border border-transparent rounded-xl hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                onClick={() => handleSave(key)}
              >
                <FaCheck className="w-3 h-3 mr-1" />
                Save
              </button>
              <button
                className="inline-flex items-center px-4 py-2 text-xs font-bold text-gray-600 bg-gradient-to-r from-gray-100 to-gray-200 border border-gray-300 rounded-xl hover:from-gray-200 hover:to-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                onClick={handleCancel}
              >
                <FaTimes className="w-3 h-3 mr-1" />
                Cancel
              </button>
            </div>
          )}
        </div>

        <div className="mt-3">
          {!isEditing ? (
            <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
              {type === 'checkbox' ? (
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                    value ? 'border-2' : 'bg-gray-200 border-gray-300'
                  }`}
                  style={value ? {
                    backgroundColor: 'var(--primary-color)',
                    borderColor: 'var(--primary-color)'
                  } : {}}
                  >
                    {value && <FaCheck className="w-2 h-2" style={{ color: 'var(--primary-text)' }} />}
                  </div>
                  <span className={`text-sm font-medium ${value ? '' : 'text-gray-600'}`}
                    style={value ? { color: 'var(--primary-color)' } : {}}
                  >
                    {value ? 'Enabled' : 'Disabled'}
                  </span>
                </div>
              ) : type === 'color' ? (
                <div className="flex items-center">
                  <div
                    className="w-8 h-8 rounded-md border border-gray-300 mr-3 shadow-sm"
                    style={{ backgroundColor: value }}
                  >
                  </div>
                  <span className="text-sm text-gray-700 font-mono bg-gray-100 px-2 py-1 rounded">{value}</span>
                </div>
              ) : type === 'password' ? (
                <span className="text-sm text-gray-700 font-mono">••••••••</span>
              ) : (
                <span className="text-sm text-gray-700">{value || 'Not set'}</span>
              )}
            </div>
          ) : (
            <div>
              {type === 'select' ? (
                <select
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                >
                  {options?.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : type === 'textarea' ? (
                <textarea
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-vertical"
                  rows="4"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                  placeholder={`Enter ${label.toLowerCase()}...`}
                />
              ) : type === 'checkbox' ? (
                <div className="flex items-center">
                  <input
                    className="h-4 w-4 border-gray-300 rounded checkbox-primary"
                    type="checkbox"
                    checked={value}
                    onChange={(e) => handleSettingChange(key, e.target.checked)}
                  />
                  <label className="ml-3 text-sm text-gray-700">
                    {value ? 'Enabled' : 'Disabled'}
                  </label>
                </div>
              ) : type === 'color' ? (
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    className="h-10 w-16 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={value}
                    onChange={(e) => handleSettingChange(key, e.target.value)}
                  />
                  <input
                    type="text"
                    className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono"
                    value={value}
                    onChange={(e) => handleSettingChange(key, e.target.value)}
                    placeholder="#000000"
                  />
                </div>
              ) : type === 'number' ? (
                <input
                  type="number"
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  value={value}
                  onChange={(e) => handleSettingChange(key, parseInt(e.target.value) || 0)}
                  placeholder={`Enter ${label.toLowerCase()}...`}
                />
              ) : (
                <input
                  type={type}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  value={value}
                  onChange={(e) => handleSettingChange(key, e.target.value)}
                  placeholder={`Enter ${label.toLowerCase()}...`}
                />
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">General Settings</h3>
              <p className="text-sm text-gray-600">Configure basic company information and regional preferences</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('companyName', 'Company Name')}
              {renderSettingField('companyEmail', 'Company Email', 'email')}
              {renderSettingField('companyPhone', 'Company Phone', 'tel')}
              {renderSettingField('companyAddress', 'Company Address', 'textarea')}
              {renderSettingField('timezone', 'Timezone', 'select', [
                { value: 'Asia/Kolkata', label: 'Asia/Kolkata (IST)' },
                { value: 'UTC', label: 'UTC' },
                { value: 'America/New_York', label: 'America/New_York (EST)' }
              ])}
              {renderSettingField('dateFormat', 'Date Format', 'select', [
                { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
              ])}
              {renderSettingField('currency', 'Default Currency', 'select', [
                { value: 'INR', label: 'Indian Rupee (INR)' },
                { value: 'USD', label: 'US Dollar (USD)' },
                { value: 'EUR', label: 'Euro (EUR)' }
              ])}
              {renderSettingField('language', 'Language', 'select', [
                { value: 'English', label: 'English' },
                { value: 'Hindi', label: 'Hindi' },
                { value: 'Marathi', label: 'Marathi' }
              ])}
            </div>
          </div>
        );

      case 'user':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">User Preferences</h3>
              <p className="text-sm text-gray-600">Customize your personal experience and interface preferences</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('defaultDashboard', 'Default Dashboard', 'select', [
                { value: 'overview', label: 'Overview' },
                { value: 'sales', label: 'Sales Dashboard' },
                { value: 'services', label: 'Services Dashboard' }
              ])}
              {renderSettingField('itemsPerPage', 'Items Per Page', 'number')}
              {renderSettingField('autoSave', 'Auto Save', 'checkbox')}
              {renderSettingField('showNotifications', 'Show Notifications', 'checkbox')}
              {renderSettingField('darkMode', 'Dark Mode', 'checkbox')}
            </div>
          </div>
        );

      case 'security':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Security Settings</h3>
              <p className="text-sm text-gray-600">Configure security policies and authentication requirements</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('sessionTimeout', 'Session Timeout (minutes)', 'number')}
              {renderSettingField('passwordExpiry', 'Password Expiry (days)', 'number')}
              {renderSettingField('twoFactorAuth', 'Two-Factor Authentication', 'checkbox')}
              {renderSettingField('loginAttempts', 'Max Login Attempts', 'number')}
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Notification Settings</h3>
              <p className="text-sm text-gray-600">Control how and when you receive notifications</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('emailNotifications', 'Email Notifications', 'checkbox')}
              {renderSettingField('smsNotifications', 'SMS Notifications', 'checkbox')}
              {renderSettingField('pushNotifications', 'Push Notifications', 'checkbox')}
              {renderSettingField('weeklyReports', 'Weekly Reports', 'checkbox')}
              {renderSettingField('monthlyReports', 'Monthly Reports', 'checkbox')}
            </div>
          </div>
        );

      case 'email':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Email Settings</h3>
              <p className="text-sm text-gray-600">Configure SMTP settings for outgoing emails</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('smtpServer', 'SMTP Server')}
              {renderSettingField('smtpPort', 'SMTP Port', 'number')}
              {renderSettingField('smtpUsername', 'SMTP Username')}
              {renderSettingField('smtpPassword', 'SMTP Password', 'password')}
              {renderSettingField('emailSignature', 'Email Signature', 'textarea')}
            </div>
          </div>
        );

      case 'database':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Database Settings</h3>
              <p className="text-sm text-gray-600">Configure backup and maintenance settings</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('backupFrequency', 'Backup Frequency', 'select', [
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' },
                { value: 'monthly', label: 'Monthly' }
              ])}
              {renderSettingField('retentionPeriod', 'Retention Period (days)', 'number')}
              {renderSettingField('autoBackup', 'Auto Backup', 'checkbox')}
            </div>
          </div>
        );

      case 'appearance':
        return (
          <div>
            <div className="mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Appearance Settings</h3>
              <p className="text-sm text-gray-600">Customize the visual appearance and branding</p>
            </div>
            <div className="space-y-6">
              {renderSettingField('primaryColor', 'Primary Color', 'color')}
              {renderSettingField('secondaryColor', 'Secondary Color', 'color')}
              {renderSettingField('logoUrl', 'Logo URL')}
              {renderSettingField('favicon', 'Favicon URL')}
            </div>
          </div>
        );

      default:
        return <div>Select a settings category</div>;
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Settings..."
        subtitle="Please wait while we fetch your preferences"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold mb-2 flex items-center text-primary-dynamic">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                    <FaCog className="text-xl" style={{ color: 'var(--primary-text)' }} />
                  </div>
                  Settings & Preferences
                </h2>
                <p className="text-lg" style={{ color: 'rgba(var(--primary-text-rgb), 0.8)' }}>Manage system settings and user preferences</p>
              </div>
              <button className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl border border-transparent transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 bg-white hover:bg-gray-50 shadow-lg"
                style={{ color: 'var(--primary-color)' }}
              >
                <FaSave className="mr-2" />
                Save All Changes
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Enhanced Settings Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-xl form-section-border overflow-hidden">
              <div className="px-6 py-4 form-section-header">
                <h5 className="text-lg font-bold mb-0 flex items-center text-primary-dynamic">
                  <FaGlobe className="mr-2" />
                  Categories
                </h5>
              </div>
              <div className="p-3">
                <nav className="space-y-2">
                  {settingsTabs.map((tab, index) => {
                    return (
                      <button
                        key={tab.id}
                        className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 transform hover:scale-105 ${
                          activeTab === tab.id
                            ? 'shadow-lg'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 border border-gray-200'
                        }`}
                        style={activeTab === tab.id ? {
                          backgroundColor: 'var(--primary-color)',
                          color: 'var(--primary-text)'
                        } : {}}
                        onClick={() => setActiveTab(tab.id)}
                      >
                        <span className={`mr-3 text-lg ${activeTab === tab.id ? '' : 'text-gray-400'}`}
                          style={activeTab === tab.id ? { color: 'var(--primary-text)' } : {}}
                        >
                          {tab.icon}
                        </span>
                        <span className="font-medium">{tab.name}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>

          {/* Enhanced Settings Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-2xl shadow-xl form-section-border overflow-hidden">
              <div className="p-8">
                {renderTabContent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsList;
