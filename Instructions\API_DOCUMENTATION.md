# 📡 SaaS CRM for Tally Resellers - API Documentation

## 📋 API Overview

### Base Configuration
- **Base URL**: `http://localhost:3000/api` (Development)
- **Production URL**: `https://your-domain.com/api`
- **API Version**: v1
- **Content Type**: `application/json`
- **Authentication**: JWT <PERSON>
- **Rate Limiting**: 100 requests per 15 minutes per IP

### Response Format
All API responses follow a consistent format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {},
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": ["Field 'email' is required"]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔐 Authentication Endpoints

### POST /auth/register
Register a new organization and admin user.

**Request Body:**
```json
{
  "organization": {
    "name": "ABC Tally Solutions",
    "subdomain": "abc-tally",
    "contactEmail": "<EMAIL>",
    "contactPhone": "+91-9876543210"
  },
  "admin": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "username": "johndoe",
    "password": "SecurePass123!"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Organization registered successfully",
  "data": {
    "organization": {
      "id": "uuid",
      "name": "ABC Tally Solutions",
      "subdomain": "abc-tally"
    },
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "Admin"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token"
    }
  }
}
```

### POST /auth/login
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "organizationSubdomain": "abc-tally"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "Admin",
      "permissions": ["customers:read", "customers:write"]
    },
    "organization": {
      "id": "uuid",
      "name": "ABC Tally Solutions"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token"
    }
  }
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token"
}
```

### POST /auth/logout
Logout user and invalidate tokens.

**Headers:**
```
Authorization: Bearer jwt_token
```

### POST /auth/forgot-password
Request password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### POST /auth/reset-password
Reset password with token.

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "NewSecurePass123!"
}
```

## 👥 User Management Endpoints

### GET /users
Get list of users in organization.

**Headers:**
```
Authorization: Bearer jwt_token
```

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search by name or email
- `role` (string): Filter by role
- `status` (string): Filter by status (active/inactive)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "username": "johndoe",
      "role": {
        "id": "uuid",
        "name": "Admin"
      },
      "isActive": true,
      "lastLoginAt": "2024-01-15T10:30:00Z",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "totalPages": 1
  }
}
```

### POST /users
Create new user.

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "username": "janesmith",
  "password": "SecurePass123!",
  "roleId": "uuid",
  "phone": "+91-**********"
}
```

### GET /users/:id
Get user details by ID.

### PUT /users/:id
Update user information.

### DELETE /users/:id
Soft delete user.

### PUT /users/:id/permissions
Update user permissions.

**Request Body:**
```json
{
  "permissions": [
    {
      "moduleName": "customers",
      "canView": true,
      "canAdd": true,
      "canEdit": true,
      "canDelete": false,
      "canExport": true
    }
  ]
}
```

## 📋 Masters Management Endpoints

### License Editions

#### GET /masters/licence-editions
Get all license editions.

#### POST /masters/licence-editions
Create new license edition.

**Request Body:**
```json
{
  "name": "Platinum Plus",
  "description": "Premium Tally license with advanced features"
}
```

#### PUT /masters/licence-editions/:id
Update license edition.

#### DELETE /masters/licence-editions/:id
Delete license edition.

### Designations

#### GET /masters/designations
Get all designations.

#### POST /masters/designations
Create new designation.

**Request Body:**
```json
{
  "name": "CFO",
  "description": "Chief Financial Officer",
  "isMandatory": true
}
```

### Tally Products

#### GET /masters/tally-products
Get all Tally products.

#### POST /masters/tally-products
Create new Tally product.

**Request Body:**
```json
{
  "name": "Tally Prime Enterprise",
  "description": "Enterprise version of Tally Prime",
  "price": 25000.00
}
```

### Staff Roles

#### GET /masters/staff-roles
Get all staff roles.

#### POST /masters/staff-roles
Create new staff role.

### Executives

#### GET /masters/executives
Get all executives.

#### POST /masters/executives
Create new executive.

**Request Body:**
```json
{
  "name": "Rajesh Kumar",
  "email": "<EMAIL>",
  "phone": "+91-**********",
  "roleId": "uuid",
  "userId": "uuid"
}
```

### Areas

#### GET /masters/areas
Get all areas.

#### POST /masters/areas
Create new area.

### Industries

#### GET /masters/industries
Get all industries.

### Nature of Issues

#### GET /masters/nature-of-issues
Get all nature of issues.

### Additional Services

#### GET /masters/additional-services
Get all additional services.

### Call Statuses

#### GET /masters/call-statuses
Get all call statuses.

## 👥 Customer Management Endpoints

### GET /customers
Get list of customers.

**Query Parameters:**
- `page`, `limit`: Pagination
- `search`: Search by customer name or Tally serial
- `location`: Filter by location
- `product`: Filter by product
- `status`: Filter by profile status
- `executive`: Filter by follow-up executive

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "customerName": "ABC Enterprises",
      "tallySerialNo": "TS123456",
      "product": {
        "id": "uuid",
        "name": "Tally Prime"
      },
      "licenceEdition": {
        "id": "uuid",
        "name": "Gold"
      },
      "location": {
        "id": "uuid",
        "name": "Chennai"
      },
      "profileStatus": "FOLLOW UP",
      "followUpExecutive": {
        "id": "uuid",
        "name": "Rajesh Kumar"
      },
      "gstNo": "33AAAAA0000A1Z5",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### POST /customers
Create new customer.

**Request Body:**
```json
{
  "customerName": "XYZ Industries",
  "tallySerialNo": "TS789012",
  "productId": "uuid",
  "licenceEditionId": "uuid",
  "locationId": "uuid",
  "profileStatus": "FOLLOW UP",
  "followUpExecutiveId": "uuid",
  "latitude": 13.0827,
  "longitude": 80.2707,
  "gstNo": "33BBBBB0000B1Z5",
  "tdlAddons": "Payroll, Inventory",
  "remarks": "Potential for upgrade",
  "contacts": [
    {
      "designationId": "uuid",
      "contactPerson": "Mr. Sharma",
      "phone": "+91-9876543213",
      "email": "<EMAIL>",
      "isPrimary": true,
      "mobileNumbers": [
        {
          "mobileNumber": "+91-9876543213",
          "isPrimary": true
        }
      ]
    }
  ],
  "tss": {
    "status": "Active",
    "expiryDate": "2024-12-31",
    "adminEmail": "<EMAIL>"
  },
  "amc": {
    "amcStatus": "YES",
    "fromDate": "2024-01-01",
    "toDate": "2024-12-31",
    "renewalDate": "2024-11-30",
    "noOfVisits": 12,
    "currentYearAmount": 15000.00,
    "lastYearAmount": 12000.00
  },
  "additionalServices": ["uuid1", "uuid2"]
}
```

### GET /customers/:id
Get customer details by ID.

### PUT /customers/:id
Update customer information.

### DELETE /customers/:id
Soft delete customer.

### GET /customers/:id/contacts
Get customer contacts.

### POST /customers/:id/contacts
Add new contact to customer.

### PUT /customers/:id/contacts/:contactId
Update customer contact.

### DELETE /customers/:id/contacts/:contactId
Delete customer contact.

## 🛠️ Services Management Endpoints

### GET /service-calls
Get list of service calls.

**Query Parameters:**
- `page`, `limit`: Pagination
- `customer`: Filter by customer
- `executive`: Filter by assigned executive
- `status`: Filter by call status
- `type`: Filter by call type (AMC Call, Free Call, Per Call)
- `serviceType`: Filter by service type (Online, Onsite)
- `dateFrom`, `dateTo`: Filter by date range

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "customer": {
        "id": "uuid",
        "customerName": "ABC Enterprises",
        "tallySerialNumber": "TS123456"
      },
      "contactPerson": {
        "id": "uuid",
        "contactPerson": "Mr. Sharma",
        "phone": "+91-9876543213"
      },
      "callType": "AMC Call",
      "serviceType": "Online",
      "bookingTime": "2024-01-15T09:00:00Z",
      "startTime": "2024-01-15T09:30:00Z",
      "endTime": "2024-01-15T10:30:00Z",
      "callStatus": {
        "id": "uuid",
        "name": "Completed"
      },
      "assignedExecutive": {
        "id": "uuid",
        "name": "Rajesh Kumar"
      },
      "natureOfIssue": {
        "id": "uuid",
        "name": "Installation Support"
      },
      "serviceCharges": 500.00,
      "remarks": "Issue resolved successfully"
    }
  ]
}
```

### POST /service-calls
Create new service call.

**Request Body:**
```json
{
  "customerId": "uuid",
  "contactPersonId": "uuid",
  "contactMobileId": "uuid",
  "tallySerialNumber": "TS123456",
  "callType": "AMC Call",
  "serviceType": "Online",
  "bookingTime": "2024-01-15T09:00:00Z",
  "startTime": "2024-01-15T09:30:00Z",
  "endTime": "2024-01-15T10:30:00Z",
  "callStatusId": "uuid",
  "assignedExecutiveId": "uuid",
  "natureOfIssueId": "uuid",
  "serviceCharges": 500.00,
  "remarks": "Customer needs help with GST setup",
  "followUpDate": "2024-01-20"
}
```

### GET /service-calls/:id
Get service call details by ID.

### PUT /service-calls/:id
Update service call.

### DELETE /service-calls/:id
Delete service call.

### PUT /service-calls/:id/status
Update service call status.

**Request Body:**
```json
{
  "callStatusId": "uuid",
  "remarks": "Issue resolved",
  "endTime": "2024-01-15T10:30:00Z"
}
```

## 💰 Sales Management Endpoints

### GET /sales
Get list of sales.

**Query Parameters:**
- `page`, `limit`: Pagination
- `customer`: Filter by customer
- `product`: Filter by product
- `dateFrom`, `dateTo`: Filter by sale date range
- `referralCode`: Filter by referral code

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "customer": {
        "id": "uuid",
        "customerName": "ABC Enterprises"
      },
      "saleDate": "2024-01-15",
      "product": {
        "id": "uuid",
        "name": "Tally Prime",
        "price": 18000.00
      },
      "amount": 18000.00,
      "referralName": "Partner Solutions",
      "referralCode": "PS001",
      "followUpDate": "2024-02-15",
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### POST /sales
Create new sale.

**Request Body:**
```json
{
  "customerId": "uuid",
  "customerName": "XYZ Industries",
  "saleDate": "2024-01-15",
  "productId": "uuid",
  "amount": 25000.00,
  "referralName": "Tech Partners",
  "referralCode": "TP002",
  "followUpDate": "2024-03-15"
}
```

### GET /sales/:id
Get sale details by ID.

### PUT /sales/:id
Update sale information.

### DELETE /sales/:id
Delete sale.

## 📊 Reports & Analytics Endpoints

### GET /reports/dashboard
Get dashboard summary data.

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalCustomers": 150,
      "activeAMCs": 85,
      "pendingServiceCalls": 12,
      "monthlyRevenue": 125000.00
    },
    "charts": {
      "salesTrend": [...],
      "serviceCallsByStatus": [...],
      "customersByLocation": [...]
    }
  }
}
```

### GET /reports/customers
Generate customer reports.

### GET /reports/services
Generate service call reports.

### GET /reports/sales
Generate sales reports.

### GET /reports/amc
Generate AMC reports.

### POST /reports/export
Export reports to Excel.

**Request Body:**
```json
{
  "reportType": "customers",
  "filters": {
    "dateFrom": "2024-01-01",
    "dateTo": "2024-01-31",
    "location": "uuid"
  },
  "format": "xlsx"
}
```

## 📝 Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AUTHENTICATION_REQUIRED` | Authentication token required |
| `AUTHORIZATION_FAILED` | Insufficient permissions |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `DUPLICATE_RESOURCE` | Resource already exists |
| `TENANT_MISMATCH` | Resource belongs to different organization |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `SERVER_ERROR` | Internal server error |

## 🔒 Rate Limiting

- **Global Limit**: 100 requests per 15 minutes per IP
- **Authentication Endpoints**: 5 requests per minute
- **File Upload Endpoints**: 10 requests per hour
- **Export Endpoints**: 5 requests per hour

## 📚 SDK and Examples

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth token to requests
api.interceptors.request.use(config => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Get customers
const getCustomers = async (page = 1, limit = 10) => {
  try {
    const response = await api.get('/customers', {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching customers:', error.response.data);
    throw error;
  }
};
```

### React Hook Example
```javascript
import { useState, useEffect } from 'react';
import { customerService } from '../services/customerService';

export const useCustomers = (filters = {}) => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        const data = await customerService.getCustomers(filters);
        setCustomers(data.data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();
  }, [filters]);

  return { customers, loading, error };
};
```

**Next Steps**: Review the complete module documentation in the [modules](modules/) folder for detailed implementation requirements.
