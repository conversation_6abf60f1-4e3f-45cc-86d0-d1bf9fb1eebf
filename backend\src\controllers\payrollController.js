import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get salary structure for an employee
 */
export const getSalaryStructure = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { employee_id } = req.query;
    const employeeId = employee_id || req.user.executive_id;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role => 
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canViewAll && employeeId !== req.user.executive_id) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to view this employee\'s salary structure'
      });
    }

    const salaryStructure = await models.SalaryStructure.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employeeId,
        is_active: true,
        is_default: true
      },
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
        }
      ]
    });

    if (!salaryStructure) {
      return res.status(404).json({
        success: false,
        message: 'No active salary structure found for this employee'
      });
    }

    // Calculate derived values
    const structureData = {
      ...salaryStructure.toJSON(),
      gross_salary: salaryStructure.calculateGrossSalary(),
      total_deductions: salaryStructure.calculateTotalDeductions(),
      net_salary: salaryStructure.calculateNetSalary(),
      daily_wage: salaryStructure.calculateDailyWage(),
      hourly_wage: salaryStructure.calculateHourlyWage()
    };

    res.json({
      success: true,
      data: { salaryStructure: structureData }
    });

  } catch (error) {
    logger.error('Get salary structure error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch salary structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Create or update salary structure
 */
export const createSalaryStructure = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const salaryData = req.body;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canManage = userRoles.some(role => 
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canManage) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to manage salary structures'
      });
    }

    // Validate employee exists
    const employee = await models.Executive.findOne({
      where: {
        id: salaryData.employee_id,
        tenant_id: tenantId,
        is_active: true
      }
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Deactivate existing default structure if creating a new default
    if (salaryData.is_default) {
      await models.SalaryStructure.update(
        { is_default: false },
        {
          where: {
            tenant_id: tenantId,
            employee_id: salaryData.employee_id,
            is_default: true
          }
        }
      );
    }

    // Create new salary structure
    const salaryStructure = await models.SalaryStructure.create({
      ...salaryData,
      tenant_id: tenantId,
      created_by: req.user.id
    });

    // Log the creation
    logger.info('Salary structure created:', {
      structureId: salaryStructure.id,
      employeeId: salaryData.employee_id,
      createdBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Salary structure created successfully',
      data: { salaryStructure }
    });

  } catch (error) {
    logger.error('Create salary structure error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create salary structure',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Process monthly payroll
 */
export const processPayroll = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { month, year, employee_ids } = req.body;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canProcess = userRoles.some(role => 
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canProcess) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to process payroll'
      });
    }

    // Validate month and year
    if (month < 1 || month > 12) {
      return res.status(400).json({
        success: false,
        message: 'Invalid month. Must be between 1 and 12'
      });
    }

    const currentYear = new Date().getFullYear();
    if (year < currentYear - 1 || year > currentYear + 1) {
      return res.status(400).json({
        success: false,
        message: 'Invalid year'
      });
    }

    // Get employees to process
    const whereClause = {
      tenant_id: tenantId,
      is_active: true,
      attendance_tracking_enabled: true
    };

    if (employee_ids && employee_ids.length > 0) {
      whereClause.id = { [Op.in]: employee_ids };
    }

    const employees = await models.Executive.findAll({
      where: whereClause,
      include: [
        {
          model: models.SalaryStructure,
          as: 'salaryStructures',
          where: {
            is_active: true,
            is_default: true
          },
          required: true
        }
      ]
    });

    if (employees.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No employees found with active salary structures'
      });
    }

    const payrollResults = [];
    const errors = [];

    // Calculate pay period
    const payPeriodStart = new Date(year, month - 1, 1);
    const payPeriodEnd = new Date(year, month, 0);

    for (const employee of employees) {
      try {
        // Check if payroll already exists
        const existingPayroll = await models.PayrollRecord.findOne({
          where: {
            tenant_id: tenantId,
            employee_id: employee.id,
            month: month,
            year: year
          }
        });

        if (existingPayroll) {
          errors.push({
            employee_id: employee.id,
            employee_name: employee.getFullName(),
            error: 'Payroll already exists for this period'
          });
          continue;
        }

        // Get attendance summary for the month
        const attendanceRecords = await models.AttendanceRecord.findAll({
          where: {
            tenant_id: tenantId,
            employee_id: employee.id,
            date: {
              [Op.between]: [payPeriodStart, payPeriodEnd]
            }
          }
        });

        // Calculate attendance summary
        const totalWorkingDays = attendanceRecords.length;
        let presentDays = 0;
        let absentDays = 0;
        let leaveDays = 0;
        let holidayDays = 0;
        let totalOvertimeHours = 0;
        let totalLateHours = 0;

        attendanceRecords.forEach(record => {
          if (record.is_holiday) {
            holidayDays++;
          } else {
            switch (record.status) {
              case 'present':
              case 'late':
              case 'work_from_home':
                presentDays += 1;
                break;
              case 'half_day':
                presentDays += 0.5;
                break;
              case 'absent':
                absentDays += 1;
                break;
              case 'on_leave':
                leaveDays += 1;
                break;
            }
          }

          totalOvertimeHours += parseFloat(record.overtime_hours || 0);
          
          // Calculate late hours (simplified - you might want more complex logic)
          if (record.status === 'late' && record.check_in_time) {
            // This is a simplified calculation
            totalLateHours += 0.5; // Assume 30 minutes late on average
          }
        });

        const salaryStructure = employee.salaryStructures[0];
        
        // Calculate salary components
        const attendanceRatio = totalWorkingDays > 0 ? presentDays / totalWorkingDays : 0;
        const basicSalary = salaryStructure.basic_salary * attendanceRatio;
        const hra = salaryStructure.hra * attendanceRatio;
        const transportAllowance = salaryStructure.transport_allowance * attendanceRatio;
        const medicalAllowance = salaryStructure.medical_allowance * attendanceRatio;
        const specialAllowance = salaryStructure.special_allowance * attendanceRatio;
        const performanceAllowance = salaryStructure.performance_allowance * attendanceRatio;
        const overtimeAmount = totalOvertimeHours * salaryStructure.overtime_rate_per_hour;

        const grossSalary = basicSalary + hra + transportAllowance + medicalAllowance + 
                           specialAllowance + performanceAllowance + overtimeAmount;

        // Calculate deductions
        const pfEmployee = salaryStructure.calculatePF(basicSalary);
        const esiEmployee = salaryStructure.calculateESI(grossSalary);
        const lateDeduction = totalLateHours * salaryStructure.late_deduction_per_hour;
        const absentDeduction = absentDays * salaryStructure.absent_deduction_per_day;

        const totalDeductions = pfEmployee + esiEmployee + salaryStructure.professional_tax + 
                               salaryStructure.income_tax + lateDeduction + absentDeduction;

        const netSalary = grossSalary - totalDeductions;

        // Generate payroll number
        const payrollNumber = models.PayrollRecord.generatePayrollNumber(tenantId, month, year);

        // Create payroll record
        const payrollRecord = await models.PayrollRecord.create({
          tenant_id: tenantId,
          employee_id: employee.id,
          salary_structure_id: salaryStructure.id,
          payroll_number: payrollNumber,
          month: month,
          year: year,
          pay_period_start: payPeriodStart,
          pay_period_end: payPeriodEnd,
          total_working_days: totalWorkingDays,
          present_days: presentDays,
          absent_days: absentDays,
          leave_days: leaveDays,
          holiday_days: holidayDays,
          overtime_hours: totalOvertimeHours,
          late_hours: totalLateHours,
          basic_salary: basicSalary,
          hra: hra,
          transport_allowance: transportAllowance,
          medical_allowance: medicalAllowance,
          special_allowance: specialAllowance,
          performance_allowance: performanceAllowance,
          overtime_amount: overtimeAmount,
          gross_salary: grossSalary,
          pf_employee: pfEmployee,
          esi_employee: esiEmployee,
          professional_tax: salaryStructure.professional_tax,
          income_tax: salaryStructure.income_tax,
          late_deduction: lateDeduction,
          absent_deduction: absentDeduction,
          total_deductions: totalDeductions,
          net_salary: netSalary,
          pf_employer: salaryStructure.pf_employer,
          esi_employer: salaryStructure.esi_employer,
          status: 'calculated',
          calculated_at: new Date(),
          calculated_by: req.user.id
        });

        payrollResults.push({
          employee_id: employee.id,
          employee_name: employee.getFullName(),
          payroll_record: payrollRecord,
          net_salary: netSalary
        });

      } catch (employeeError) {
        logger.error(`Payroll processing error for employee ${employee.id}:`, employeeError);
        errors.push({
          employee_id: employee.id,
          employee_name: employee.getFullName(),
          error: employeeError.message
        });
      }
    }

    // Log the payroll processing
    logger.info('Payroll processed:', {
      month: month,
      year: year,
      processedCount: payrollResults.length,
      errorCount: errors.length,
      processedBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: `Payroll processed for ${payrollResults.length} employees`,
      data: {
        processed: payrollResults,
        errors: errors,
        summary: {
          total_employees: employees.length,
          processed_count: payrollResults.length,
          error_count: errors.length,
          total_net_salary: payrollResults.reduce((sum, result) => sum + parseFloat(result.net_salary), 0)
        }
      }
    });

  } catch (error) {
    logger.error('Process payroll error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process payroll',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get payroll records
 */
export const getPayrollRecords = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      month,
      year,
      status,
      page = 1,
      limit = 50,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const whereClause = { tenant_id: tenantId };

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role =>
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canViewAll) {
      // Regular employee can only see their own payroll
      whereClause.employee_id = req.user.executive_id;
    } else if (employee_id) {
      whereClause.employee_id = employee_id;
    }

    if (month) {
      whereClause.month = month;
    }

    if (year) {
      whereClause.year = year;
    }

    if (status) {
      whereClause.status = status;
    }

    const offset = (page - 1) * limit;

    const { count, rows: payrollRecords } = await models.PayrollRecord.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
        },
        {
          model: models.SalaryStructure,
          as: 'salaryStructure',
          attributes: ['id', 'structure_name']
        },
        {
          model: models.User,
          as: 'calculator',
          attributes: ['id', 'first_name', 'last_name']
        },
        {
          model: models.User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name']
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        payrollRecords,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Get payroll records error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll records',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Approve payroll record
 */
export const approvePayroll = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canApprove = userRoles.some(role =>
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canApprove) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to approve payroll'
      });
    }

    const payrollRecord = await models.PayrollRecord.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
        status: 'calculated'
      }
    });

    if (!payrollRecord) {
      return res.status(404).json({
        success: false,
        message: 'Payroll record not found or already processed'
      });
    }

    await payrollRecord.update({
      status: 'approved',
      approved_by: req.user.id,
      approved_at: new Date()
    });

    logger.info('Payroll approved:', {
      payrollId: id,
      employeeId: payrollRecord.employee_id,
      approvedBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Payroll approved successfully',
      data: { payrollRecord }
    });

  } catch (error) {
    logger.error('Approve payroll error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve payroll',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Mark payroll as paid
 */
export const markPayrollPaid = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;
    const { payment_method, payment_reference } = req.body;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canMarkPaid = userRoles.some(role =>
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canMarkPaid) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to mark payroll as paid'
      });
    }

    const payrollRecord = await models.PayrollRecord.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
        status: 'approved'
      }
    });

    if (!payrollRecord) {
      return res.status(404).json({
        success: false,
        message: 'Payroll record not found or not approved'
      });
    }

    await payrollRecord.update({
      status: 'paid',
      paid_at: new Date(),
      payment_method: payment_method,
      payment_reference: payment_reference
    });

    logger.info('Payroll marked as paid:', {
      payrollId: id,
      employeeId: payrollRecord.employee_id,
      paymentMethod: payment_method,
      markedBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Payroll marked as paid successfully',
      data: { payrollRecord }
    });

  } catch (error) {
    logger.error('Mark payroll paid error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark payroll as paid',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get payroll summary/reports
 */
export const getPayrollReports = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { month, year, department, report_type = 'summary' } = req.query;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewReports = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    if (!canViewReports) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to view payroll reports'
      });
    }

    const whereClause = { tenant_id: tenantId };

    if (month) {
      whereClause.month = month;
    }

    if (year) {
      whereClause.year = year;
    }

    const includeClause = [
      {
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department'],
        where: department ? { department: department } : {}
      }
    ];

    const payrollRecords = await models.PayrollRecord.findAll({
      where: whereClause,
      include: includeClause
    });

    let reportData = {};

    if (report_type === 'summary') {
      reportData = {
        total_employees: payrollRecords.length,
        total_gross_salary: payrollRecords.reduce((sum, record) => sum + parseFloat(record.gross_salary), 0),
        total_deductions: payrollRecords.reduce((sum, record) => sum + parseFloat(record.total_deductions), 0),
        total_net_salary: payrollRecords.reduce((sum, record) => sum + parseFloat(record.net_salary), 0),
        total_overtime_amount: payrollRecords.reduce((sum, record) => sum + parseFloat(record.overtime_amount), 0),
        status_breakdown: payrollRecords.reduce((acc, record) => {
          acc[record.status] = (acc[record.status] || 0) + 1;
          return acc;
        }, {}),
        department_breakdown: payrollRecords.reduce((acc, record) => {
          const dept = record.employee.department || 'Unknown';
          if (!acc[dept]) {
            acc[dept] = {
              count: 0,
              total_gross: 0,
              total_net: 0
            };
          }
          acc[dept].count++;
          acc[dept].total_gross += parseFloat(record.gross_salary);
          acc[dept].total_net += parseFloat(record.net_salary);
          return acc;
        }, {})
      };
    } else if (report_type === 'detailed') {
      reportData = {
        records: payrollRecords,
        summary: {
          total_records: payrollRecords.length,
          total_gross_salary: payrollRecords.reduce((sum, record) => sum + parseFloat(record.gross_salary), 0),
          total_net_salary: payrollRecords.reduce((sum, record) => sum + parseFloat(record.net_salary), 0)
        }
      };
    }

    res.json({
      success: true,
      data: {
        report_type: report_type,
        period: { month, year },
        department: department,
        ...reportData
      }
    });

  } catch (error) {
    logger.error('Get payroll reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
