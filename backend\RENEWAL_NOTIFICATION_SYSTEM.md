# Renewal Notification System Documentation

## Overview

The TallyCRM Renewal Notification System is a comprehensive automated solution for managing and sending renewal reminders for various services including AMC (Annual Maintenance Contract), TSS (Tally Software Support), licenses, and other maintenance contracts.

## Features

### ✅ **Core Features**
- **Automated Scheduling**: Automatically schedules notifications based on configurable reminder intervals
- **Multiple Templates**: Different email templates for various scenarios (standard, urgent, overdue)
- **Flexible Configuration**: Customizable reminder intervals (2, 5, 7, 30 days, etc.)
- **Multi-Channel Support**: Email, SMS, and WhatsApp notifications (email fully implemented)
- **Duplicate Prevention**: Prevents duplicate notifications using unique constraints
- **Retry Mechanism**: Automatic retry for failed notifications with configurable limits
- **History Tracking**: Complete audit trail of all sent notifications
- **Manual Triggers**: <PERSON><PERSON> can manually trigger notifications and schedule generation

### 📧 **Email Templates**
1. **Standard Reminder** (`renewal_reminder`): Professional reminder with service details
2. **Urgent Reminder** (`renewal_urgent`): High-priority template for 2-3 days before expiry
3. **Overdue Notice** (`renewal_overdue`): Post-expiry notification for service restoration

### 🔄 **Automated Jobs**
- **Daily Notifications**: Processes pending notifications at 9:00 AM IST
- **Weekly Schedule Generation**: Creates new notification schedules every Sunday at 6:00 AM IST
- **Failed Notification Retry**: Retries failed notifications every 2 hours
- **Monthly Cleanup**: Removes old notification records on the 1st of each month
- **Daily Health Check**: System health monitoring at 12:00 PM IST

## System Architecture

### Database Models

#### 1. **NotificationSchedule**
Tracks individual notification schedules and their status.

```sql
CREATE TABLE notification_schedules (
  id UUID PRIMARY KEY,
  tenant_id UUID NOT NULL,
  customer_id UUID NOT NULL,
  renewal_type ENUM('amc', 'tss', 'license', 'maintenance', 'support'),
  renewal_record_id UUID NOT NULL,
  expiry_date DATE NOT NULL,
  notify_at DATE NOT NULL,
  days_before_expiry INTEGER NOT NULL,
  notification_type ENUM('reminder', 'overdue') DEFAULT 'reminder',
  status ENUM('scheduled', 'sent', 'failed', 'cancelled') DEFAULT 'scheduled',
  sent_at TIMESTAMP NULL,
  email_sent BOOLEAN DEFAULT FALSE,
  sms_sent BOOLEAN DEFAULT FALSE,
  whatsapp_sent BOOLEAN DEFAULT FALSE,
  email_message_id VARCHAR(255),
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  created_by UUID,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. **RenewalNotificationSettings**
Configures notification intervals and channels for different renewal types.

```sql
CREATE TABLE renewal_notification_settings (
  id UUID PRIMARY KEY,
  tenant_id UUID NOT NULL,
  field_name VARCHAR(100) NOT NULL,
  reminder_days JSON NOT NULL,
  notification_channels JSON DEFAULT '["email"]',
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Services

#### 1. **RenewalNotificationService**
Core service handling notification logic, scheduling, and email generation.

**Key Methods:**
- `scheduleAllRenewalNotifications()`: Schedules notifications for all tenants
- `processPendingNotifications()`: Processes and sends pending notifications
- `sendRenewalNotification()`: Sends individual renewal notifications
- `cleanupOldSchedules()`: Removes old notification records

#### 2. **ScheduledJobService**
Manages automated cron jobs for the renewal notification system.

**Scheduled Jobs:**
- Daily notification processing (9:00 AM IST)
- Weekly schedule generation (Sunday 6:00 AM IST)
- Failed notification retry (every 2 hours)
- Monthly cleanup (1st of month, 2:00 AM IST)
- Daily health check (12:00 PM IST)

## API Endpoints

### Renewal Settings Management

#### GET `/api/renewal-notifications/settings`
Retrieve all renewal notification settings for the current tenant.

#### POST `/api/renewal-notifications/settings`
Create a new renewal notification setting.

**Request Body:**
```json
{
  "field_name": "amc_expiry_date",
  "reminder_days": [30, 15, 7, 2],
  "notification_channels": ["email"],
  "is_active": true
}
```

#### PUT `/api/renewal-notifications/settings/{id}`
Update an existing renewal notification setting.

#### DELETE `/api/renewal-notifications/settings/{id}`
Delete a renewal notification setting.

### Statistics and Monitoring

#### GET `/api/renewal-notifications/stats`
Get renewal notification statistics including pending and upcoming renewals.

#### GET `/api/renewal-notifications/history`
Get notification history with pagination and filtering.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status (scheduled, sent, failed, cancelled)
- `renewal_type`: Filter by renewal type (amc, tss, license, etc.)
- `customer_id`: Filter by customer

#### GET `/api/renewal-notifications/history/{id}`
Get detailed information about a specific notification.

### Manual Operations

#### POST `/api/renewal-notifications/trigger`
Manually trigger renewal notification processing.

**Request Body:**
```json
{
  "date": "2024-06-20"  // Optional, defaults to today
}
```

#### POST `/api/renewal-notifications/schedule`
Manually trigger schedule generation for the current tenant.

#### GET `/api/renewal-notifications/jobs/status`
Get the status of all scheduled jobs.

## Configuration

### Default Reminder Intervals

The system comes with default reminder intervals for different renewal types:

```javascript
const defaultSettings = [
  {
    field_name: 'amc_expiry_date',
    reminder_days: [30, 15, 7, 2],
    notification_channels: ['email'],
    is_active: true,
  },
  {
    field_name: 'tss_expiry_date',
    reminder_days: [30, 15, 7, 2],
    notification_channels: ['email'],
    is_active: true,
  }
];
```

### Email Template Variables

All email templates support the following variables:

- `{{customer_name}}`: Customer company name
- `{{company_name}}`: Customer company name
- `{{renewal_type}}`: Type of renewal (AMC, TSS, etc.)
- `{{expiry_date}}`: Service expiry date (DD/MM/YYYY format)
- `{{days_remaining}}`: Days until expiry
- `{{days_overdue}}`: Days since expiry (for overdue notifications)
- `{{renewal_amount}}`: Renewal amount (if available)
- `{{contact_email}}`: Support contact email
- `{{service_type}}`: Service type
- `{{is_overdue}}`: Boolean indicating if service is overdue
- `{{is_urgent}}`: Boolean indicating if notification is urgent

### Environment Variables

Required environment variables for the renewal notification system:

```env
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM_NAME=Prem Infotech Support
EMAIL_FROM=<EMAIL>

# Database Configuration
DB_HOST=your_db_host
DB_NAME=tallycrm_production
DB_USER=your_db_user
DB_PASS=your_db_password

# Application Configuration
NODE_ENV=production
APP_URL=https://your-domain.com
```

## Installation and Setup

### 1. Database Migration

Run the migration to create the notification schedule table:

```bash
npm run migrate
```

### 2. Install Dependencies

The system requires the `node-cron` package:

```bash
npm install node-cron
```

### 3. Initialize Default Settings

Default renewal notification settings are automatically created when a tenant first accesses the settings endpoint.

### 4. Start the Server

The scheduled jobs are automatically initialized when the server starts:

```bash
npm start
```

## Usage Guide

### For Administrators

#### 1. **Configure Reminder Intervals**
- Access the renewal settings through the API
- Set custom reminder intervals for different renewal types
- Enable/disable notifications for specific services

#### 2. **Monitor Notifications**
- Check notification statistics regularly
- Review failed notifications and retry if needed
- Monitor system health through job status endpoint

#### 3. **Manual Operations**
- Trigger immediate notification processing when needed
- Regenerate schedules after configuration changes
- Review notification history for audit purposes

### For Developers

#### 1. **Adding New Renewal Types**
To add support for new renewal types:

1. Update the `renewal_type` enum in the NotificationSchedule model
2. Add corresponding logic in `getRenewalDataForSetting()` method
3. Create appropriate email templates
4. Update API validation rules

#### 2. **Customizing Email Templates**
Email templates can be customized through the email template management system:

1. Access templates via the notification settings API
2. Modify template content using the supported variables
3. Test templates using the manual trigger functionality

#### 3. **Extending Notification Channels**
To add SMS or WhatsApp support:

1. Implement the respective service classes
2. Update the NotificationService to handle new channels
3. Modify the notification scheduling logic
4. Update the database schema to track new channel statuses

## Troubleshooting

### Common Issues

#### 1. **Notifications Not Sending**
- Check email configuration and credentials
- Verify notification settings are active
- Review failed notification logs
- Ensure scheduled jobs are running

#### 2. **Duplicate Notifications**
- The system has built-in duplicate prevention
- Check for multiple schedule generation runs
- Verify unique constraints are in place

#### 3. **Missing Notifications**
- Check if renewal data exists and is properly formatted
- Verify expiry dates are set correctly
- Ensure notification settings cover the required intervals

#### 4. **Performance Issues**
- Monitor database query performance
- Consider indexing optimization for large datasets
- Review scheduled job frequency if needed

### Logging

The system provides comprehensive logging for troubleshooting:

- Notification processing logs
- Email sending status
- Scheduled job execution logs
- Error messages with stack traces

### Health Monitoring

Use the health check endpoint to monitor system status:

```bash
GET /api/renewal-notifications/jobs/status
```

This returns the status of all scheduled jobs and can help identify issues with the automated system.

## Security Considerations

- All API endpoints require authentication
- Tenant isolation ensures data privacy
- Email templates are sanitized to prevent XSS
- Database queries use parameterized statements
- Sensitive information is logged appropriately

## Performance Optimization

- Database indexes on frequently queried fields
- Batch processing for large notification volumes
- Configurable retry limits to prevent infinite loops
- Automatic cleanup of old records
- Efficient query patterns for renewal data retrieval

## Future Enhancements

- SMS notification integration
- WhatsApp Business API integration
- Advanced template customization UI
- Real-time notification status dashboard
- Integration with calendar systems
- Mobile push notifications
- Advanced analytics and reporting
