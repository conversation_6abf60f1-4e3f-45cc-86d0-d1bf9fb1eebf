task1:

task2:



task3:
in the dashbord common search input searching only the customers not services like if i enter the govindaraji that's showing only the customer list or service list at a time what if user needs govindaraji customer and with there services, in that time the customer and services also should listing out here so you should show both and some time the user needs govindaraji customer, servides, executives also but here current scenariio is showing only one at a time i need all are (issue: still this showing only the customers only if i searc customer name. but the acceptance cretiria is if i search customer name the customer name related services also should show like customer details, service details by the customer, executives for that customer assigned first priority is customer second is service third is executives)


task4:
if the service cal time is the amc call the amc expiry , renewal , next visit this types of data should show in the view service form page and what are all the place we are seeing that service full details in that plase i need this amc details shoud be show (issue: in the service view form still not showin if the call type is amc call the renewal, expiry, next visit this types of details not showing see. if the call type is changed free call to amc at anywhere the pop up should show before confirming that the amc renwal , next renewal, expiry this types of details should get from the user ok)


task5:
in th http://localhost:3004/reports/service-reports?tab=services service report pag services section table i wanna remov ethe priority column in fron end , and service id is SER-018 ok but this 36575b34-163d-426d-bad1-1fc48f11f6ee
 not needed in the service id column please remove this and make the amount secion rupees symbol instead of $ symbol and completed data not showing properly and hours spend not showing amount not showing fix these issues  (issue: you are doing good but every complete services have a spending hours if not please show 00:00 but 90% of services have a spendin hours but here showing only N/A)