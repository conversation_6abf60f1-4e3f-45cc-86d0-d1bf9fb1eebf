import React, { useState } from 'react';
import { FaArrowLeft, FaCog, FaTools } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import CallStatusesManagement from '../../components/masters/CallStatusesManagement';
import ProductsIssuesManagement from '../../components/masters/ProductsIssuesManagement';

const CallStatusesPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('call-statuses');

  const handleBack = () => {
    navigate('/masters');
  };

  const tabs = [
    {
      id: 'call-statuses',
      name: 'Call Statuses',
      icon: <FaCog className="w-4 h-4" />,
      description: 'Manage service call statuses and workflow'
    },
    {
      id: 'products-issues',
      name: 'Products/Issues',
      icon: <FaTools className="w-4 h-4" />,
      description: 'Manage product types and issue categories'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-17">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft className="mr-2" />
                <span>Back to Masters</span>
              </button>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-xl mt-4 font-semibold text-gray-900">
                  Call Management
                </h1>
                <p className="text-sm text-gray-500">
                  Manage call statuses and product/issue types
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

     

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'call-statuses' && <CallStatusesManagement />}
        {activeTab === 'products-issues' && <ProductsIssuesManagement />}
      </div>
    </div>
  );
};

export default CallStatusesPage;
