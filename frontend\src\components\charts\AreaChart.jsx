/**
 * Area Chart Component
 * Responsive area chart using Recharts for trend visualization with filled areas
 */

import React from 'react';
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { CHART_COLORS, CHART_THEMES } from './chartThemes';
import { formatNumber, formatDate } from './chartUtils';

const AreaChart = ({
  data = [],
  areas = [], // Array of area configurations: [{ dataKey, name, color, fillOpacity }]
  xAxisKey = 'date',
  height = 300,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  curved = true,
  stacked = false,
  fillOpacity = 0.3,
  strokeWidth = 2,
  formatters = {}, // Custom formatters for tooltip values
  theme = 'default',
  className = '',
  ...props
}) => {
  // Default area configuration if none provided
  const defaultAreas = areas.length > 0 ? areas : [
    { dataKey: 'value', name: 'Value', color: CHART_COLORS.primary[0] }
  ];

  // Theme configuration
  const currentTheme = CHART_THEMES[theme] || CHART_THEMES.default;

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 mb-2">
          {formatDate(label, 'short')}
        </p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between space-x-4 mb-1">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600">{entry.name}:</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatters[entry.dataKey] 
                ? formatters[entry.dataKey](entry.value)
                : formatNumber(entry.value)
              }
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 text-sm">No data available for chart</p>
      </div>
    );
  }

  return (
    <div className={className} {...props}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsAreaChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <defs>
            {defaultAreas.map((area, index) => {
              const color = area.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length];
              return (
                <linearGradient 
                  key={`gradient-${area.dataKey}`}
                  id={`gradient-${area.dataKey}`} 
                  x1="0" 
                  y1="0" 
                  x2="0" 
                  y2="1"
                >
                  <stop 
                    offset="5%" 
                    stopColor={color} 
                    stopOpacity={area.fillOpacity || fillOpacity} 
                  />
                  <stop 
                    offset="95%" 
                    stopColor={color} 
                    stopOpacity={0.1} 
                  />
                </linearGradient>
              );
            })}
          </defs>
          
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={currentTheme.gridColor}
              opacity={0.5}
            />
          )}
          
          <XAxis 
            dataKey={xAxisKey}
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatDate(value, 'short')}
          />
          
          <YAxis 
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatNumber(value, 'compact')}
          />
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {showLegend && (
            <Legend 
              wrapperStyle={{ 
                fontSize: currentTheme.fontSize,
                color: currentTheme.textColor 
              }}
            />
          )}
          
          {defaultAreas.map((area, index) => {
            const color = area.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length];
            return (
              <Area
                key={area.dataKey}
                type={curved ? "monotone" : "linear"}
                dataKey={area.dataKey}
                name={area.name}
                stackId={stacked ? "1" : area.dataKey}
                stroke={color}
                strokeWidth={area.strokeWidth || strokeWidth}
                fill={`url(#gradient-${area.dataKey})`}
                connectNulls={false}
              />
            );
          })}
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AreaChart;
