import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, <PERSON>ner, Modal } from '../ui';
import { Plus, Calendar, Clock, FileText, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { leaveAPI } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';
import LeaveRequestForm from './LeaveRequestForm';
import LeaveCalendar from './LeaveCalendar';

const LeaveManagement = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [leaveRequests, setLeaveRequests] = useState([]);
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [leaveTypes, setLeaveTypes] = useState([]);
  const [showRequestForm, setShowRequestForm] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    year: new Date().getFullYear()
  });

  useEffect(() => {
    fetchData();
  }, [filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [requestsRes, balanceRes, typesRes] = await Promise.all([
        leaveAPI.getRequests({ 
          status: filters.status !== 'all' ? filters.status : undefined,
          page: 1,
          limit: 50 
        }),
        leaveAPI.getBalance({ year: filters.year }),
        leaveAPI.getTypes()
      ]);

      setLeaveRequests(requestsRes.data.leaveRequests);
      setLeaveBalance(balanceRes.data.leaveBalances);
      setLeaveTypes(typesRes.data.leaveTypes);
    } catch (error) {
      console.error('Error fetching leave data:', error);
      toast.error('Failed to fetch leave data');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestSubmit = async (requestData) => {
    try {
      await leaveAPI.submitRequest(requestData);
      toast.success('Leave request submitted successfully!');
      setShowRequestForm(false);
      fetchData();
    } catch (error) {
      console.error('Error submitting leave request:', error);
      toast.error(error.response?.data?.message || 'Failed to submit leave request');
    }
  };

  const handleCancelRequest = async (requestId) => {
    if (!confirm('Are you sure you want to cancel this leave request?')) {
      return;
    }

    try {
      await leaveAPI.cancelRequest(requestId);
      toast.success('Leave request cancelled successfully!');
      fetchData();
    } catch (error) {
      console.error('Error cancelling leave request:', error);
      toast.error(error.response?.data?.message || 'Failed to cancel leave request');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      withdrawn: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalBalance = () => {
    return leaveBalance.reduce((total, balance) => total + balance.remaining_days, 0);
  };

  const getUsedBalance = () => {
    return leaveBalance.reduce((total, balance) => total + balance.used_days, 0);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leave Management</h1>
          <p className="text-gray-600">Manage your leave requests and balance</p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowCalendar(true)}
            variant="outline"
            className="flex items-center"
          >
            <Calendar className="mr-2 h-4 w-4" />
            Calendar View
          </Button>
          <Button
            onClick={() => setShowRequestForm(true)}
            className="flex items-center"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Request
          </Button>
        </div>
      </div>

      {/* Leave Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Balance</p>
              <p className="text-2xl font-bold text-blue-600">{getTotalBalance()}</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Used This Year</p>
              <p className="text-2xl font-bold text-red-600">{getUsedBalance()}</p>
            </div>
            <Clock className="h-8 w-8 text-red-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Requests</p>
              <p className="text-2xl font-bold text-yellow-600">
                {leaveRequests.filter(req => req.status === 'pending').length}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-yellow-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Approved Requests</p>
              <p className="text-2xl font-bold text-green-600">
                {leaveRequests.filter(req => req.status === 'approved').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </Card>
      </div>

      {/* Leave Balance Details */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Leave Balance Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {leaveBalance.map((balance) => (
            <div key={balance.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{balance.leaveType.name}</h4>
                <Badge className="bg-blue-100 text-blue-800">
                  {balance.leaveType.code}
                </Badge>
              </div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Allocated:</span>
                  <span>{balance.allocated_days}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Used:</span>
                  <span className="text-red-600">{balance.used_days}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Remaining:</span>
                  <span className="text-green-600 font-medium">{balance.remaining_days}</span>
                </div>
                {balance.carry_forward_days > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Carry Forward:</span>
                    <span className="text-blue-600">{balance.carry_forward_days}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Year
            </label>
            <select
              value={filters.year}
              onChange={(e) => setFilters({ ...filters, year: parseInt(e.target.value) })}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value={new Date().getFullYear()}>{new Date().getFullYear()}</option>
              <option value={new Date().getFullYear() - 1}>{new Date().getFullYear() - 1}</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Leave Requests */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Leave Requests</h3>
        
        {leaveRequests.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No leave requests</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating a new leave request.
            </p>
            <div className="mt-6">
              <Button onClick={() => setShowRequestForm(true)}>
                <Plus className="mr-2 h-4 w-4" />
                New Request
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {leaveRequests.map((request) => (
              <div key={request.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(request.status)}
                    <div>
                      <h4 className="font-medium">{request.leaveType.name}</h4>
                      <p className="text-sm text-gray-600">
                        {formatDate(request.start_date)} - {formatDate(request.end_date)}
                        ({request.total_days} days)
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge className={getStatusColor(request.status)}>
                      {request.status.toUpperCase()}
                    </Badge>
                    
                    {request.status === 'pending' && (
                      <Button
                        onClick={() => handleCancelRequest(request.id)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
                
                <div className="mt-3">
                  <p className="text-sm text-gray-600">
                    <strong>Reason:</strong> {request.reason}
                  </p>
                  {request.manager_comments && (
                    <p className="text-sm text-gray-600 mt-1">
                      <strong>Manager Comments:</strong> {request.manager_comments}
                    </p>
                  )}
                  {request.rejection_reason && (
                    <p className="text-sm text-red-600 mt-1">
                      <strong>Rejection Reason:</strong> {request.rejection_reason}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Modals */}
      {showRequestForm && (
        <Modal
          isOpen={showRequestForm}
          onClose={() => setShowRequestForm(false)}
          title="New Leave Request"
          size="lg"
        >
          <LeaveRequestForm
            leaveTypes={leaveTypes}
            leaveBalance={leaveBalance}
            onSubmit={handleRequestSubmit}
            onCancel={() => setShowRequestForm(false)}
          />
        </Modal>
      )}

      {showCalendar && (
        <Modal
          isOpen={showCalendar}
          onClose={() => setShowCalendar(false)}
          title="Leave Calendar"
          size="xl"
        >
          <LeaveCalendar onClose={() => setShowCalendar(false)} />
        </Modal>
      )}
    </div>
  );
};

export default LeaveManagement;
