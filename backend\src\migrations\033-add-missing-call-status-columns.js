import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  console.log('🔄 Adding missing columns to call_statuses table...');

  try {
    // Check current table structure
    const tableDescription = await queryInterface.describeTable('call_statuses');
    console.log('Current call_statuses columns:', Object.keys(tableDescription));

    // Add missing columns if they don't exist
    const columnsToAdd = [
      {
        name: 'is_final',
        definition: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: false,
          comment: 'Whether this is a final status (no further changes allowed)',
        }
      },
      {
        name: 'requires_approval',
        definition: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: false,
          comment: 'Whether changing to this status requires approval',
        }
      },
      {
        name: 'auto_close_after_days',
        definition: {
          type: DataTypes.INTEGER,
          allowNull: true,
          comment: 'Auto-close call after specified days in this status',
        }
      },
      {
        name: 'is_billable',
        definition: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          allowNull: false,
          comment: 'Whether calls in this status are billable',
        }
      },
      {
        name: 'is_active',
        definition: {
          type: DataTypes.BOOLEAN,
          defaultValue: true,
          allowNull: false,
        }
      },
      {
        name: 'is_default',
        definition: {
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          allowNull: false,
          comment: 'Whether this is a default status that cannot be edited or deleted',
        }
      },
      {
        name: 'sort_order',
        definition: {
          type: DataTypes.INTEGER,
          defaultValue: 0,
          allowNull: false,
        }
      }
    ];

    for (const column of columnsToAdd) {
      if (!tableDescription[column.name]) {
        console.log(`Adding column: ${column.name}`);
        await queryInterface.addColumn('call_statuses', column.name, column.definition);
      } else {
        console.log(`Column ${column.name} already exists`);
      }
    }

    // Add indexes for the new columns
    const indexesToAdd = [
      { fields: ['is_final'], name: 'call_statuses_is_final' },
      { fields: ['is_active'], name: 'call_statuses_is_active' },
      { fields: ['is_default'], name: 'call_statuses_is_default' },
      { fields: ['sort_order'], name: 'call_statuses_sort_order' }
    ];

    for (const index of indexesToAdd) {
      try {
        await queryInterface.addIndex('call_statuses', index.fields, { name: index.name });
        console.log(`Added index: ${index.name}`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`Index ${index.name} already exists`);
        } else {
          console.error(`Error adding index ${index.name}:`, error.message);
        }
      }
    }

    console.log('✅ Successfully added missing columns to call_statuses table');
  } catch (error) {
    console.error('❌ Error adding missing columns:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  console.log('🔄 Removing added columns from call_statuses table...');

  const columnsToRemove = [
    'is_final',
    'requires_approval', 
    'auto_close_after_days',
    'is_billable',
    'is_active',
    'is_default',
    'sort_order'
  ];

  const indexesToRemove = [
    'call_statuses_is_final',
    'call_statuses_is_active', 
    'call_statuses_is_default',
    'call_statuses_sort_order'
  ];

  // Remove indexes first
  for (const indexName of indexesToRemove) {
    try {
      await queryInterface.removeIndex('call_statuses', indexName);
      console.log(`Removed index: ${indexName}`);
    } catch (error) {
      console.log(`Index ${indexName} might not exist or already removed`);
    }
  }

  // Remove columns
  for (const columnName of columnsToRemove) {
    try {
      await queryInterface.removeColumn('call_statuses', columnName);
      console.log(`Removed column: ${columnName}`);
    } catch (error) {
      console.log(`Column ${columnName} might not exist or already removed`);
    }
  }

  console.log('✅ Successfully removed added columns from call_statuses table');
};
