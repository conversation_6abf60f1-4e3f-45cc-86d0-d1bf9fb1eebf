import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge } from '../ui';
import { Plus, Edit, Trash2, Settings, Clock, Users, MapPin } from 'lucide-react';
import { toast } from 'react-hot-toast';

const AttendanceConfiguration = () => {
  const [loading, setLoading] = useState(true);
  const [policies, setPolicies] = useState([]);
  const [settings, setSettings] = useState(null);
  const [showPolicyForm, setShowPolicyForm] = useState(false);
  const [showSettingsForm, setShowSettingsForm] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API calls
      setPolicies([
        {
          id: '1',
          name: 'Standard Office Policy',
          shift_start_time: '09:00:00',
          shift_end_time: '18:00:00',
          grace_period_minutes: 15,
          minimum_hours_full_day: 8.0,
          overtime_threshold_hours: 8.0,
          is_default: true,
          is_active: true
        },
        {
          id: '2',
          name: 'Flexible Hours Policy',
          shift_start_time: '10:00:00',
          shift_end_time: '19:00:00',
          grace_period_minutes: 30,
          minimum_hours_full_day: 8.0,
          overtime_threshold_hours: 9.0,
          is_default: false,
          is_active: true
        }
      ]);

      setSettings({
        enable_gps_tracking: true,
        enable_face_recognition: false,
        enable_biometric: false,
        send_daily_reminders: true,
        reminder_time: '08:45:00',
        send_late_arrival_alerts: true,
        send_absence_alerts: true,
        require_manager_approval: false,
        auto_approve_threshold_minutes: 30,
        max_check_in_distance: 100
      });
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch configuration data');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePolicy = async (policyData) => {
    try {
      // Mock API call - replace with actual implementation
      console.log('Saving policy:', policyData);
      toast.success('Policy saved successfully!');
      setShowPolicyForm(false);
      setEditingPolicy(null);
      fetchData();
    } catch (error) {
      console.error('Error saving policy:', error);
      toast.error('Failed to save policy');
    }
  };

  const handleSaveSettings = async (settingsData) => {
    try {
      // Mock API call - replace with actual implementation
      console.log('Saving settings:', settingsData);
      setSettings(settingsData);
      toast.success('Settings saved successfully!');
      setShowSettingsForm(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    }
  };

  const handleDeletePolicy = async (policyId) => {
    if (!confirm('Are you sure you want to delete this policy?')) {
      return;
    }

    try {
      // Mock API call - replace with actual implementation
      console.log('Deleting policy:', policyId);
      toast.success('Policy deleted successfully!');
      fetchData();
    } catch (error) {
      console.error('Error deleting policy:', error);
      toast.error('Failed to delete policy');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Attendance Configuration</h1>
          <p className="text-gray-600">Manage attendance policies and system settings</p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowSettingsForm(true)}
            variant="outline"
            className="flex items-center"
          >
            <Settings className="mr-2 h-4 w-4" />
            System Settings
          </Button>
          <Button
            onClick={() => setShowPolicyForm(true)}
            className="flex items-center"
          >
            <Plus className="mr-2 h-4 w-4" />
            New Policy
          </Button>
        </div>
      </div>

      {/* System Settings Overview */}
      {settings && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Current System Settings</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium">GPS Tracking</p>
                <Badge className={settings.enable_gps_tracking ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                  {settings.enable_gps_tracking ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Clock className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium">Daily Reminders</p>
                <Badge className={settings.send_daily_reminders ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                  {settings.send_daily_reminders ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Users className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium">Manager Approval</p>
                <Badge className={settings.require_manager_approval ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                  {settings.require_manager_approval ? 'Required' : 'Not Required'}
                </Badge>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Attendance Policies */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Attendance Policies</h3>
          <Button
            onClick={() => setShowPolicyForm(true)}
            size="sm"
            className="flex items-center"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Policy
          </Button>
        </div>

        <div className="space-y-4">
          {policies.map((policy) => (
            <div key={policy.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div>
                    <h4 className="font-medium flex items-center space-x-2">
                      <span>{policy.name}</span>
                      {policy.is_default && (
                        <Badge className="bg-blue-100 text-blue-800">Default</Badge>
                      )}
                      <Badge className={policy.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {policy.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </h4>
                    <div className="text-sm text-gray-600 mt-1">
                      <span>Shift: {policy.shift_start_time} - {policy.shift_end_time}</span>
                      <span className="mx-2">•</span>
                      <span>Grace: {policy.grace_period_minutes} min</span>
                      <span className="mx-2">•</span>
                      <span>Min Hours: {policy.minimum_hours_full_day}h</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => {
                      setEditingPolicy(policy);
                      setShowPolicyForm(true);
                    }}
                    variant="outline"
                    size="sm"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  {!policy.is_default && (
                    <Button
                      onClick={() => handleDeletePolicy(policy.id)}
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
              
              <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Overtime Threshold:</span>
                  <span className="ml-1 font-medium">{policy.overtime_threshold_hours}h</span>
                </div>
                <div>
                  <span className="text-gray-600">Full Day Hours:</span>
                  <span className="ml-1 font-medium">{policy.minimum_hours_full_day}h</span>
                </div>
                <div>
                  <span className="text-gray-600">Grace Period:</span>
                  <span className="ml-1 font-medium">{policy.grace_period_minutes} min</span>
                </div>
                <div>
                  <span className="text-gray-600">Status:</span>
                  <span className={`ml-1 font-medium ${policy.is_active ? 'text-green-600' : 'text-gray-600'}`}>
                    {policy.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Policy Form Modal */}
      {showPolicyForm && (
        <Modal
          isOpen={showPolicyForm}
          onClose={() => {
            setShowPolicyForm(false);
            setEditingPolicy(null);
          }}
          title={editingPolicy ? 'Edit Attendance Policy' : 'New Attendance Policy'}
          size="lg"
        >
          <PolicyForm
            policy={editingPolicy}
            onSave={handleSavePolicy}
            onCancel={() => {
              setShowPolicyForm(false);
              setEditingPolicy(null);
            }}
          />
        </Modal>
      )}

      {/* Settings Form Modal */}
      {showSettingsForm && (
        <Modal
          isOpen={showSettingsForm}
          onClose={() => setShowSettingsForm(false)}
          title="System Settings"
          size="lg"
        >
          <SettingsForm
            settings={settings}
            onSave={handleSaveSettings}
            onCancel={() => setShowSettingsForm(false)}
          />
        </Modal>
      )}
    </div>
  );
};

// Policy Form Component
const PolicyForm = ({ policy, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: policy?.name || '',
    shift_start_time: policy?.shift_start_time || '09:00',
    shift_end_time: policy?.shift_end_time || '18:00',
    grace_period_minutes: policy?.grace_period_minutes || 15,
    minimum_hours_full_day: policy?.minimum_hours_full_day || 8.0,
    overtime_threshold_hours: policy?.overtime_threshold_hours || 8.0,
    is_default: policy?.is_default || false,
    is_active: policy?.is_active !== undefined ? policy.is_active : true
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Policy Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full border border-gray-300 rounded-md px-3 py-2"
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Shift Start Time *
          </label>
          <input
            type="time"
            value={formData.shift_start_time}
            onChange={(e) => setFormData({ ...formData, shift_start_time: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Shift End Time *
          </label>
          <input
            type="time"
            value={formData.shift_end_time}
            onChange={(e) => setFormData({ ...formData, shift_end_time: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Grace Period (minutes)
          </label>
          <input
            type="number"
            value={formData.grace_period_minutes}
            onChange={(e) => setFormData({ ...formData, grace_period_minutes: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="0"
            max="60"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Minimum Hours (Full Day)
          </label>
          <input
            type="number"
            step="0.5"
            value={formData.minimum_hours_full_day}
            onChange={(e) => setFormData({ ...formData, minimum_hours_full_day: parseFloat(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="1"
            max="12"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Overtime Threshold (hours)
        </label>
        <input
          type="number"
          step="0.5"
          value={formData.overtime_threshold_hours}
          onChange={(e) => setFormData({ ...formData, overtime_threshold_hours: parseFloat(e.target.value) })}
          className="w-full border border-gray-300 rounded-md px-3 py-2"
          min="1"
          max="12"
        />
      </div>

      <div className="space-y-2">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.is_default}
            onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">Set as default policy</span>
        </label>

        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">Active</span>
        </label>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Policy
        </Button>
      </div>
    </form>
  );
};

// Settings Form Component
const SettingsForm = ({ settings, onSave, onCancel }) => {
  const [formData, setFormData] = useState(settings);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h4 className="text-md font-medium text-gray-900 mb-3">Tracking Settings</h4>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.enable_gps_tracking}
              onChange={(e) => setFormData({ ...formData, enable_gps_tracking: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Enable GPS tracking for check-in/out</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.enable_face_recognition}
              onChange={(e) => setFormData({ ...formData, enable_face_recognition: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Enable face recognition</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.enable_biometric}
              onChange={(e) => setFormData({ ...formData, enable_biometric: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Enable biometric device integration</span>
          </label>
        </div>
      </div>

      <div>
        <h4 className="text-md font-medium text-gray-900 mb-3">Notification Settings</h4>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.send_daily_reminders}
              onChange={(e) => setFormData({ ...formData, send_daily_reminders: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Send daily attendance reminders</span>
          </label>

          {formData.send_daily_reminders && (
            <div className="ml-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reminder Time
              </label>
              <input
                type="time"
                value={formData.reminder_time}
                onChange={(e) => setFormData({ ...formData, reminder_time: e.target.value })}
                className="border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
          )}

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.send_late_arrival_alerts}
              onChange={(e) => setFormData({ ...formData, send_late_arrival_alerts: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Send late arrival alerts to managers</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.send_absence_alerts}
              onChange={(e) => setFormData({ ...formData, send_absence_alerts: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Send absence alerts to managers</span>
          </label>
        </div>
      </div>

      <div>
        <h4 className="text-md font-medium text-gray-900 mb-3">Approval Settings</h4>
        <div className="space-y-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.require_manager_approval}
              onChange={(e) => setFormData({ ...formData, require_manager_approval: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Require manager approval for manual entries</span>
          </label>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Auto-approve threshold (minutes)
            </label>
            <input
              type="number"
              value={formData.auto_approve_threshold_minutes}
              onChange={(e) => setFormData({ ...formData, auto_approve_threshold_minutes: parseInt(e.target.value) })}
              className="border border-gray-300 rounded-md px-3 py-2"
              min="0"
              max="120"
            />
            <p className="text-xs text-gray-500 mt-1">
              Manual entries within this threshold will be auto-approved
            </p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="text-md font-medium text-gray-900 mb-3">Location Settings</h4>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Maximum check-in distance (meters)
          </label>
          <input
            type="number"
            value={formData.max_check_in_distance}
            onChange={(e) => setFormData({ ...formData, max_check_in_distance: parseInt(e.target.value) })}
            className="border border-gray-300 rounded-md px-3 py-2"
            min="10"
            max="1000"
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum allowed distance from office for check-in
          </p>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Settings
        </Button>
      </div>
    </form>
  );
};

export default AttendanceConfiguration;
