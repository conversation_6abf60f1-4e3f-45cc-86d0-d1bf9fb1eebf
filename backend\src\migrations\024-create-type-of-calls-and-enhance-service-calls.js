import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  // Check if type_of_calls table already exists
  const tableExists = await queryInterface.tableExists('type_of_calls');

  if (!tableExists) {
    // Create type_of_calls table
    await queryInterface.createTable('type_of_calls', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('amc', 'tss', 'support', 'maintenance', 'training', 'other'),
      allowNull: false,
      defaultValue: 'support',
    },
    service_type: {
      type: DataTypes.ENUM('onsite', 'online', 'both'),
      allowNull: false,
      defaultValue: 'both',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    default_duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

    // Add indexes for type_of_calls with proper error handling
    const indexesToCreate = [
      { fields: ['code'], options: { unique: true, name: 'type_of_calls_code' } },
      { fields: ['is_active'], options: { name: 'type_of_calls_is_active' } },
      { fields: ['category'], options: { name: 'type_of_calls_category' } },
      { fields: ['service_type'], options: { name: 'type_of_calls_service_type' } },
      { fields: ['sort_order'], options: { name: 'type_of_calls_sort_order' } }
    ];

    for (const index of indexesToCreate) {
      try {
        // Check if index exists first
        const indexExists = await queryInterface.sequelize.query(
          `SELECT indexname FROM pg_indexes WHERE tablename = 'type_of_calls' AND indexname = '${index.options.name}'`,
          { type: queryInterface.sequelize.QueryTypes.SELECT }
        );

        if (indexExists.length === 0) {
          await queryInterface.addIndex('type_of_calls', index.fields, index.options);
        }
      } catch (error) {
        if (error.original?.code !== '42P07') { // Index already exists
          throw error;
        }
      }
    }
  }

  // Add new columns to service_calls table with error handling
  const columnsToAdd = [
    {
      name: 'type_of_call_id',
      definition: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'type_of_calls',
          key: 'id',
        },
      }
    },
    {
      name: 'contact_number',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'tally_serial_number',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'company_name',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'designation',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'tally_version',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'tss_status',
      definition: {
        type: DataTypes.ENUM('active', 'inactive'),
        allowNull: true,
      }
    },
    {
      name: 'tss_expiry',
      definition: {
        type: DataTypes.DATE,
        allowNull: true,
      }
    },
    {
      name: 'service_location',
      definition: {
        type: DataTypes.TEXT,
        allowNull: true,
      }
    },
    {
      name: 'location_coordinates',
      definition: {
        type: DataTypes.JSONB,
        defaultValue: {},
      }
    },
    {
      name: 'google_maps_link',
      definition: {
        type: DataTypes.TEXT,
        allowNull: true,
      }
    },
    {
      name: 'call_end_time',
      definition: {
        type: DataTypes.DATE,
        allowNull: true,
      }
    },
    {
      name: 'is_end_time_fixed',
      definition: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      }
    },
    {
      name: 'charging_type',
      definition: {
        type: DataTypes.ENUM('hourly', 'fixed'),
        allowNull: true,
        defaultValue: 'fixed',
      }
    },
    {
      name: 'hourly_rate_amount',
      definition: {
        type: DataTypes.DECIMAL(8, 2),
        allowNull: true,
      }
    },
    {
      name: 'executive_remarks',
      definition: {
        type: DataTypes.TEXT,
        allowNull: true,
      }
    },
    {
      name: 'customer_feedback_type',
      definition: {
        type: DataTypes.ENUM('happy', 'sad', 'satisfied', 'not_satisfied'),
        allowNull: true,
      }
    },
    {
      name: 'customer_feedback_comments',
      definition: {
        type: DataTypes.TEXT,
        allowNull: true,
      }
    },
    {
      name: 'booking_date',
      definition: {
        type: DataTypes.DATE,
        allowNull: true,
      }
    },
    {
      name: 'mobile_number_2',
      definition: {
        type: DataTypes.STRING,
        allowNull: true,
      }
    },
    {
      name: 'time_tracking_history',
      definition: {
        type: DataTypes.JSONB,
        defaultValue: [],
      }
    }
  ];

  // Add columns with error handling
  for (const column of columnsToAdd) {
    try {
      const tableDescription = await queryInterface.describeTable('service_calls');
      if (!tableDescription[column.name]) {
        await queryInterface.addColumn('service_calls', column.name, column.definition);
      }
    } catch (error) {
      if (error.original?.code !== '42701') { // Column already exists
        throw error;
      }
    }
  }

  // Add foreign key constraint with error handling
  try {
    await queryInterface.addConstraint('service_calls', {
      fields: ['type_of_call_id'],
      type: 'foreign key',
      name: 'fk_service_calls_type_of_call',
      references: {
        table: 'type_of_calls',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  } catch (error) {
    if (error.original?.code !== '42710') { // Constraint already exists
      throw error;
    }
  }

  // Insert default type of calls data if table is empty
  const existingData = await queryInterface.sequelize.query(
    'SELECT COUNT(*) as count FROM type_of_calls',
    { type: queryInterface.sequelize.QueryTypes.SELECT }
  );

  if (existingData[0].count === '0') {
    await queryInterface.bulkInsert('type_of_calls', [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      name: 'AMC Visit',
      code: 'AMC_VISIT',
      description: 'Annual Maintenance Contract visit',
      category: 'amc',
      service_type: 'onsite',
      is_billable: false,
      default_duration: 120,
      requires_approval: false,
      sort_order: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      name: 'TSS Visit',
      code: 'TSS_VISIT',
      description: 'Tally Software Services visit',
      category: 'tss',
      service_type: 'onsite',
      is_billable: false,
      default_duration: 90,
      requires_approval: false,
      sort_order: 2,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      name: 'Computer Repair',
      code: 'COMPUTER_REPAIR',
      description: 'Computer hardware/software repair',
      category: 'support',
      service_type: 'onsite',
      is_billable: true,
      default_duration: 180,
      requires_approval: false,
      sort_order: 3,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      name: 'Online Support',
      code: 'ONLINE_SUPPORT',
      description: 'Remote online support session',
      category: 'support',
      service_type: 'online',
      is_billable: true,
      default_duration: 60,
      requires_approval: false,
      sort_order: 4,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440005',
      name: 'Training Session',
      code: 'TRAINING',
      description: 'Software training session',
      category: 'training',
      service_type: 'both',
      is_billable: true,
      default_duration: 240,
      requires_approval: true,
      sort_order: 5,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440006',
      name: 'Installation',
      code: 'INSTALLATION',
      description: 'Software installation and setup',
      category: 'support',
      service_type: 'onsite',
      is_billable: true,
      default_duration: 120,
      requires_approval: false,
      sort_order: 6,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ]);
  }
};

export const down = async (queryInterface, Sequelize) => {
  // Remove foreign key constraint with error handling
  try {
    await queryInterface.removeConstraint('service_calls', 'fk_service_calls_type_of_call');
  } catch (error) {
    // Constraint might not exist, continue
  }

  // Remove added columns from service_calls
  const columnsToRemove = [
    'type_of_call_id',
    'contact_number',
    'tally_serial_number',
    'company_name',
    'designation',
    'tally_version',
    'tss_status',
    'tss_expiry',
    'service_location',
    'location_coordinates',
    'google_maps_link',
    'call_end_time',
    'is_end_time_fixed',
    'charging_type',
    'hourly_rate_amount',
    'executive_remarks',
    'customer_feedback_type',
    'customer_feedback_comments',
    'booking_date',
    'mobile_number_2',
    'time_tracking_history',
  ];

  for (const column of columnsToRemove) {
    try {
      const tableDescription = await queryInterface.describeTable('service_calls');
      if (tableDescription[column]) {
        await queryInterface.removeColumn('service_calls', column);
      }
    } catch (error) {
      // Column might not exist, continue
    }
  }

  // Drop type_of_calls table with error handling
  try {
    const tableExists = await queryInterface.tableExists('type_of_calls');
    if (tableExists) {
      await queryInterface.dropTable('type_of_calls');
    }
  } catch (error) {
    // Table might not exist, continue
  }
};
