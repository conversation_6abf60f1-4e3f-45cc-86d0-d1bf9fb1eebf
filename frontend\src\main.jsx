import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { HelmetProvider } from 'react-helmet-async';
import { ErrorBoundary } from 'react-error-boundary';

// Tailwind CSS
import './styles/tailwind.css';

// Custom styles
import './styles/index.css';
import './styles/variables.css';

// Main App component
import App from './App.jsx';

// Error Fallback Component
import ErrorFallback from './components/common/ErrorFallback.jsx';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Error handler for ErrorBoundary
const errorHandler = (error, errorInfo) => {
  console.error('Application Error:', error, errorInfo);

  // Check if it's an authentication error
  if (error.message && (
    error.message.includes('Invalid access token') ||
    error.message.includes('Token expired') ||
    error.message.includes('Authentication required')
  )) {
    // Clear auth data and redirect to login
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    if (!window.location.pathname.includes('/auth/login')) {
      window.location.href = '/auth/login';
    }

  }

  // Here you can send error to logging service
};

ReactDOM.createRoot(document.getElementById('root')).render(
  // Temporarily disable StrictMode to prevent double API calls in development
  // <React.StrictMode>
  <ErrorBoundary FallbackComponent={ErrorFallback} onError={errorHandler}>
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <App />
        </BrowserRouter>
        {import.meta.env.VITE_ENABLE_REACT_QUERY_DEVTOOLS === 'true' && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </HelmetProvider>
  </ErrorBoundary>
  // </React.StrictMode>
);
