/**
 * Dashboard Helper Utilities
 * Provides smart data grouping and information architecture improvements
 */

/**
 * Groups service calls by customer to reduce redundancy
 * @param {Array} serviceCalls - Array of service call objects
 * @returns {Array} Grouped service calls with customer information consolidated
 */
export const groupServiceCallsByCustomer = (serviceCalls) => {
  if (!Array.isArray(serviceCalls) || serviceCalls.length === 0) {
    return [];
  }

  const grouped = serviceCalls.reduce((acc, call) => {
    const customerId = call.customer?.id || 'unknown';
    const customerName = call.customer?.company_name || call.customer?.display_name || 'Unknown Customer';
    
    if (!acc[customerId]) {
      acc[customerId] = {
        customer: {
          id: customerId,
          name: customerName,
          email: call.customer?.email,
          phone: call.customer?.phone
        },
        calls: [],
        totalCalls: 0,
        openCalls: 0,
        completedCalls: 0,
        lastCallDate: null
      };
    }
    
    acc[customerId].calls.push(call);
    acc[customerId].totalCalls++;
    
    // Count status types
    const status = call.status?.name?.toLowerCase() || '';
    if (status.includes('completed')) {
      acc[customerId].completedCalls++;
    } else if (status.includes('open') || status.includes('pending') || status.includes('new')) {
      acc[customerId].openCalls++;
    }
    
    // Track latest call date
    const callDate = new Date(call.created_at || call.call_date);
    if (!acc[customerId].lastCallDate || callDate > acc[customerId].lastCallDate) {
      acc[customerId].lastCallDate = callDate;
    }
    
    return acc;
  }, {});

  // Convert to array and sort by last call date
  return Object.values(grouped).sort((a, b) => 
    new Date(b.lastCallDate) - new Date(a.lastCallDate)
  );
};

/**
 * Consolidates identical or similar service call descriptions
 * @param {Array} serviceCalls - Array of service call objects
 * @returns {Array} Service calls with consolidated descriptions
 */
export const consolidateServiceCallDescriptions = (serviceCalls) => {
  if (!Array.isArray(serviceCalls)) return [];

  return serviceCalls.map(call => {
    let subject = call.subject || call.description || '';
    
    // Remove redundant customer name from subject if it exists
    const customerName = call.customer?.company_name || call.customer?.display_name || '';
    if (customerName && subject.toLowerCase().includes(customerName.toLowerCase())) {
      subject = subject.replace(new RegExp(customerName, 'gi'), '').trim();
      subject = subject.replace(/^(service call for|call for|support for)\s*/i, '').trim();
    }
    
    // Standardize common patterns
    if (!subject || subject.toLowerCase() === 'service call' || subject.toLowerCase().includes('service call for')) {
      subject = call.call_type?.name || 'General support';
    }
    
    return {
      ...call,
      consolidatedSubject: subject || 'General support'
    };
  });
};

/**
 * Creates activity summary with smart grouping
 * @param {Array} serviceCalls - Recent service calls
 * @param {Array} customers - Recent customers
 * @returns {Object} Organized activity data
 */
export const createActivitySummary = (serviceCalls = [], customers = []) => {
  const today = new Date();
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

  const categorizeByDate = (items, dateField = 'created_at') => {
    return items.reduce((acc, item) => {
      const itemDate = new Date(item[dateField]);
      
      if (itemDate.toDateString() === today.toDateString()) {
        acc.today.push(item);
      } else if (itemDate.toDateString() === yesterday.toDateString()) {
        acc.yesterday.push(item);
      } else if (itemDate >= thisWeek) {
        acc.thisWeek.push(item);
      } else {
        acc.older.push(item);
      }
      
      return acc;
    }, { today: [], yesterday: [], thisWeek: [], older: [] });
  };

  const serviceCallsByDate = categorizeByDate(serviceCalls);
  const customersByDate = categorizeByDate(customers);

  return {
    serviceCalls: serviceCallsByDate,
    customers: customersByDate,
    summary: {
      totalToday: serviceCallsByDate.today.length + customersByDate.today.length,
      totalYesterday: serviceCallsByDate.yesterday.length + customersByDate.yesterday.length,
      totalThisWeek: serviceCallsByDate.thisWeek.length + customersByDate.thisWeek.length
    }
  };
};

/**
 * Generates status distribution data for charts
 * @param {Array} serviceCalls - Array of service calls
 * @returns {Object} Status distribution data
 */
export const getStatusDistribution = (serviceCalls = []) => {
  const distribution = serviceCalls.reduce((acc, call) => {
    const status = call.status?.name || 'Unknown';
    const normalizedStatus = status.toLowerCase();
    
    // Group similar statuses
    let category;
    if (normalizedStatus.includes('completed')) {
      category = 'Completed';
    } else if (normalizedStatus.includes('cancelled') || normalizedStatus.includes('canceled')) {
      category = 'Cancelled';
    } else if (normalizedStatus.includes('pending') || normalizedStatus.includes('open')) {
      category = 'Pending';
    } else if (normalizedStatus.includes('progress') || normalizedStatus.includes('working')) {
      category = 'In Progress';
    } else if (normalizedStatus.includes('hold')) {
      category = 'On Hold';
    } else {
      category = 'Other';
    }
    
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {});

  // Convert to chart-friendly format
  return Object.entries(distribution).map(([status, count]) => ({
    status,
    count,
    percentage: serviceCalls.length > 0 ? Math.round((count / serviceCalls.length) * 100) : 0
  }));
};

/**
 * Generates priority distribution data
 * @param {Array} serviceCalls - Array of service calls
 * @returns {Object} Priority distribution data
 */
export const getPriorityDistribution = (serviceCalls = []) => {
  const distribution = serviceCalls.reduce((acc, call) => {
    const priority = call.priority || 'Medium';
    acc[priority] = (acc[priority] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(distribution).map(([priority, count]) => ({
    priority,
    count,
    percentage: serviceCalls.length > 0 ? Math.round((count / serviceCalls.length) * 100) : 0
  }));
};

/**
 * Removes duplicate customer information and consolidates display
 * @param {Array} customers - Array of customer objects
 * @returns {Array} Deduplicated customers with enhanced display info
 */
export const consolidateCustomerInfo = (customers = []) => {
  const seen = new Set();
  
  return customers.filter(customer => {
    // Create a unique key based on email or phone or company name
    const uniqueKey = customer.email || customer.phone || customer.company_name || customer.id;
    
    if (seen.has(uniqueKey)) {
      return false;
    }
    
    seen.add(uniqueKey);
    return true;
  }).map(customer => ({
    ...customer,
    displayName: customer.company_name || customer.display_name || 'Unknown Company',
    primaryContact: customer.email || customer.phone || 'No contact info',
    initials: (customer.company_name || customer.display_name || 'U').charAt(0).toUpperCase()
  }));
};

/**
 * Formats date ranges for better readability
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted relative date
 */
export const formatRelativeDate = (dateString) => {
  if (!dateString) return 'Unknown date';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) return 'Today';
  if (diffDays === 1) return 'Yesterday';
  if (diffDays <= 7) return `${diffDays} days ago`;
  if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
  if (diffDays <= 365) return `${Math.ceil(diffDays / 30)} months ago`;
  
  return date.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Creates executive assignment summary
 * @param {Array} serviceCalls - Array of service calls
 * @returns {Object} Executive workload summary
 */
export const getExecutiveWorkload = (serviceCalls = []) => {
  const workload = serviceCalls.reduce((acc, call) => {
    const executiveName = call.assigned_to?.name || call.executive?.name || 'Unassigned';
    const executiveId = call.assigned_to?.id || call.executive?.id || 'unassigned';
    
    if (!acc[executiveId]) {
      acc[executiveId] = {
        name: executiveName,
        totalCalls: 0,
        openCalls: 0,
        completedCalls: 0,
        overdueCallsCount: 0
      };
    }
    
    acc[executiveId].totalCalls++;
    
    const status = call.status?.name?.toLowerCase() || '';
    if (status.includes('completed')) {
      acc[executiveId].completedCalls++;
    } else {
      acc[executiveId].openCalls++;
    }
    
    // Check if overdue (simplified logic)
    const scheduledDate = new Date(call.scheduled_date || call.created_at);
    const now = new Date();
    if (scheduledDate < now && !status.includes('completed')) {
      acc[executiveId].overdueCallsCount++;
    }
    
    return acc;
  }, {});

  return Object.values(workload).sort((a, b) => b.totalCalls - a.totalCalls);
};
