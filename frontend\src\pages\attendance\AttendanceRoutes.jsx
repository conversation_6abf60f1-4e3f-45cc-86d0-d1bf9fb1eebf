import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import AttendanceDashboard from '../../components/attendance/AttendanceDashboard';
import MobileAttendance from '../../components/attendance/MobileAttendance';
import AttendanceReports from '../../components/attendance/AttendanceReports';
import AttendanceNavigation from '../../components/attendance/AttendanceNavigation';
import LeaveManagement from '../../components/leave/LeaveManagement';
import PayrollManagement from '../../components/payroll/PayrollManagement';

const AttendanceRoutes = () => {
  return (
    <AttendanceNavigation>
      <Routes>
        <Route index element={<AttendanceDashboard />} />
        <Route path="dashboard" element={<AttendanceDashboard />} />
        <Route path="checkin" element={<MobileAttendance />} />
        <Route path="reports" element={<AttendanceReports />} />
        <Route path="leaves" element={<LeaveManagement />} />
        <Route path="payroll" element={<PayrollManagement />} />
        <Route path="team" element={<div className="p-6"><h2 className="text-2xl font-bold">Team Management</h2><p>Team management functionality coming soon...</p></div>} />
        <Route path="config" element={<div className="p-6"><h2 className="text-2xl font-bold">Attendance Configuration</h2><p>Configuration settings coming soon...</p></div>} />
        <Route path="*" element={<Navigate to="/attendance" replace />} />
      </Routes>
    </AttendanceNavigation>
  );
};

export default AttendanceRoutes;
