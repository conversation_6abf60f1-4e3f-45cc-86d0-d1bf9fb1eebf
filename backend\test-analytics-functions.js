/**
 * Test Analytics Functions
 * Test the analytics functions directly without HTTP requests
 */

import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

async function testAnalyticsFunctions() {
  try {
    console.log('🧪 Testing Analytics Functions...\n');

    // Find existing tenant or use first available
    const testTenant = await models.Tenant.findOne({
      where: { status: 'active' }
    });

    if (!testTenant) {
      console.log('❌ No active tenant found. Please create a tenant first.');
      return;
    }

    console.log(`✅ Using tenant: ${testTenant.name} (ID: ${testTenant.id})`);

    // Test analytics functions directly
    console.log('\n🧪 Testing analytics functions directly...');
    
    const { 
      getCustomerAnalytics,
      getServiceAnalytics,
      getFinancialAnalytics,
      getExecutiveAnalytics
    } = await import('./src/controllers/tenantAnalyticsController.js');

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    console.log(`📅 Date range: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}\n`);

    // Test customer analytics function
    console.log('📊 Customer Analytics Function:');
    try {
      const customerAnalytics = await getCustomerAnalytics(testTenant.id, startDate, endDate);
      console.log('✅ Customer analytics function works');
      console.log(`   - Total customers: ${customerAnalytics.totalCustomers}`);
      console.log(`   - New customers: ${customerAnalytics.newCustomers}`);
      console.log(`   - Customer types: ${customerAnalytics.customerTypeDistribution.length} categories`);
      console.log(`   - Acquisition trend: ${customerAnalytics.acquisitionTrend.length} data points`);
      console.log(`   - Industries: ${customerAnalytics.customersByIndustry.length} categories`);
      console.log(`   - Areas: ${customerAnalytics.customersByArea.length} categories`);
    } catch (error) {
      console.log(`❌ Customer analytics function error: ${error.message}`);
    }

    // Test service analytics function
    console.log('\n🔧 Service Analytics Function:');
    try {
      const serviceAnalytics = await getServiceAnalytics(testTenant.id, startDate, endDate);
      console.log('✅ Service analytics function works');
      console.log(`   - Total service calls: ${serviceAnalytics.totalServiceCalls}`);
      console.log(`   - Service calls in period: ${serviceAnalytics.serviceCallsInPeriod}`);
      console.log(`   - Status distribution: ${serviceAnalytics.serviceCallsByStatus.length} categories`);
      console.log(`   - Priority distribution: ${serviceAnalytics.serviceCallsByPriority.length} categories`);
      console.log(`   - Service calls trend: ${serviceAnalytics.serviceCallsTrend.length} data points`);
      console.log(`   - Avg resolution time: ${serviceAnalytics.avgResolutionTimeHours} hours`);
    } catch (error) {
      console.log(`❌ Service analytics function error: ${error.message}`);
    }

    // Test financial analytics function
    console.log('\n💰 Financial Analytics Function:');
    try {
      const financialAnalytics = await getFinancialAnalytics(testTenant.id, startDate, endDate);
      console.log('✅ Financial analytics function works');
      console.log(`   - Total revenue: ₹${financialAnalytics.totalRevenue.toLocaleString()}`);
      console.log(`   - Revenue in period: ₹${financialAnalytics.revenueInPeriod.toLocaleString()}`);
      console.log(`   - Revenue trend: ${financialAnalytics.revenueTrend.length} data points`);
      console.log(`   - AMC distribution: ${financialAnalytics.amcStatusDistribution.length} categories`);
    } catch (error) {
      console.log(`❌ Financial analytics function error: ${error.message}`);
    }

    // Test executive analytics function
    console.log('\n👔 Executive Analytics Function:');
    try {
      const executiveAnalytics = await getExecutiveAnalytics(testTenant.id, startDate, endDate);
      console.log('✅ Executive analytics function works');
      console.log(`   - Service calls by executive: ${executiveAnalytics.serviceCallsByExecutive.length} executives`);
      console.log(`   - Executive performance: ${executiveAnalytics.executivePerformance.length} performance records`);
    } catch (error) {
      console.log(`❌ Executive analytics function error: ${error.message}`);
    }

    // Test comprehensive analytics
    console.log('\n📈 Comprehensive Analytics Function:');
    try {
      const { getTenantAnalytics } = await import('./src/controllers/tenantAnalyticsController.js');
      
      // Mock request object
      const mockReq = {
        user: { tenant: { id: testTenant.id } },
        query: { period: '30d' }
      };
      
      // Mock response object
      let responseData = null;
      const mockRes = {
        json: (data) => { responseData = data; },
        status: (code) => ({ json: (data) => { responseData = { status: code, ...data }; } })
      };
      
      await getTenantAnalytics(mockReq, mockRes);
      
      if (responseData && responseData.success) {
        console.log('✅ Comprehensive analytics function works');
        console.log(`   - Period: ${responseData.data.period}`);
        console.log(`   - Customer metrics: Available`);
        console.log(`   - Service metrics: Available`);
        console.log(`   - Financial metrics: Available`);
        console.log(`   - Executive metrics: Available`);
        console.log(`   - Trend data: Available`);
      } else {
        console.log('❌ Comprehensive analytics function failed');
        console.log(`   - Response: ${JSON.stringify(responseData)}`);
      }
    } catch (error) {
      console.log(`❌ Comprehensive analytics function error: ${error.message}`);
    }

    console.log('\n🎉 Analytics Functions Test Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Analytics functions are working correctly');
    console.log('✅ Database queries are executing successfully');
    console.log('✅ Data aggregation is functioning properly');
    console.log('\n🚀 Next Steps:');
    console.log('1. Test the frontend analytics pages');
    console.log('2. Verify charts are displaying data correctly');
    console.log('3. Test the analytics API endpoints with proper authentication');

  } catch (error) {
    console.error('❌ Analytics functions test failed:', error);
    logger.error('Analytics functions test error:', error);
  } finally {
    // Close database connection
    await models.sequelize.close();
  }
}

// Run the test
testAnalyticsFunctions();
