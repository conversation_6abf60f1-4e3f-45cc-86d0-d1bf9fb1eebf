import express from 'express';
import { body } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import {
  getNotificationSettings,
  updateNotificationSettings,
  testNotification,
  getNotificationEventTypes,
  getNotificationTemplates,
  getNotificationTemplateById,
  createNotificationTemplate,
  getTemplateVariables
} from '../controllers/notificationController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * /notifications/settings:
 *   get:
 *     summary: Get notification settings
 *     description: Retrieve notification settings for the current tenant including all event type configurations
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Notification settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         settings:
 *                           type: object
 *                           properties:
 *                             service_created:
 *                               type: boolean
 *                               example: true
 *                             service_started:
 *                               type: boolean
 *                               example: true
 *                             service_completed:
 *                               type: boolean
 *                               example: true
 *                             service_cancelled:
 *                               type: boolean
 *                               example: false
 *                             service_on_hold:
 *                               type: boolean
 *                               example: true
 *                             service_pending:
 *                               type: boolean
 *                               example: true
 *                             service_follow_up:
 *                               type: boolean
 *                               example: true
 *                             service_onsite:
 *                               type: boolean
 *                               example: true
 *                             service_on_process:
 *                               type: boolean
 *                               example: true
 *             example:
 *               success: true
 *               message: "Notification settings retrieved successfully"
 *               data:
 *                 settings:
 *                   service_created: true
 *                   service_started: true
 *                   service_completed: true
 *                   service_cancelled: false
 *                   service_on_hold: true
 *                   service_pending: true
 *                   service_follow_up: true
 *                   service_onsite: true
 *                   service_on_process: true
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/settings', [
  requirePermission('notifications.read')
], getNotificationSettings);

/**
 * @swagger
 * /notifications/settings:
 *   put:
 *     summary: Update notification settings
 *     description: Update notification settings for the current tenant to enable/disable specific event notifications
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               service_created:
 *                 type: boolean
 *                 description: Enable notifications when service calls are created
 *                 example: true
 *               service_started:
 *                 type: boolean
 *                 description: Enable notifications when service calls are started
 *                 example: true
 *               service_completed:
 *                 type: boolean
 *                 description: Enable notifications when service calls are completed
 *                 example: true
 *               service_cancelled:
 *                 type: boolean
 *                 description: Enable notifications when service calls are cancelled
 *                 example: false
 *               service_on_hold:
 *                 type: boolean
 *                 description: Enable notifications when service calls are put on hold
 *                 example: true
 *               service_pending:
 *                 type: boolean
 *                 description: Enable notifications when service calls are pending
 *                 example: true
 *               service_follow_up:
 *                 type: boolean
 *                 description: Enable notifications for follow-up service calls
 *                 example: true
 *               service_onsite:
 *                 type: boolean
 *                 description: Enable notifications for onsite service calls
 *                 example: true
 *               service_on_process:
 *                 type: boolean
 *                 description: Enable notifications when service calls are in process
 *                 example: true
 *           example:
 *             service_created: true
 *             service_started: true
 *             service_completed: true
 *             service_cancelled: false
 *             service_on_hold: true
 *     responses:
 *       200:
 *         description: Notification settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         settings:
 *                           type: object
 *                           description: Updated notification settings
 *             example:
 *               success: true
 *               message: "Notification settings updated successfully"
 *               data:
 *                 settings:
 *                   service_created: true
 *                   service_started: true
 *                   service_completed: true
 *                   service_cancelled: false
 *                   service_on_hold: true
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.put('/settings', [
  requirePermission('notifications.write'),
  // Validate boolean fields
  body('service_created').optional().isBoolean().withMessage('service_created must be a boolean'),
  body('service_started').optional().isBoolean().withMessage('service_started must be a boolean'),
  body('service_completed').optional().isBoolean().withMessage('service_completed must be a boolean'),
  body('service_cancelled').optional().isBoolean().withMessage('service_cancelled must be a boolean'),
  body('service_on_hold').optional().isBoolean().withMessage('service_on_hold must be a boolean'),
  body('service_pending').optional().isBoolean().withMessage('service_pending must be a boolean'),
  body('service_follow_up').optional().isBoolean().withMessage('service_follow_up must be a boolean'),
  body('service_onsite').optional().isBoolean().withMessage('service_onsite must be a boolean'),
  body('service_on_process').optional().isBoolean().withMessage('service_on_process must be a boolean'),
  body('email_enabled').optional().isBoolean().withMessage('email_enabled must be a boolean'),
  body('sms_enabled').optional().isBoolean().withMessage('sms_enabled must be a boolean'),
  body('push_enabled').optional().isBoolean().withMessage('push_enabled must be a boolean'),
  body('business_hours_only').optional().isBoolean().withMessage('business_hours_only must be a boolean'),
  // Validate numeric fields
  body('notification_delay_minutes').optional().isInt({ min: 0, max: 1440 }).withMessage('notification_delay_minutes must be between 0 and 1440'),
  // Validate time fields
  body('business_hours_start').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).withMessage('business_hours_start must be in HH:MM:SS format'),
  body('business_hours_end').optional().matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).withMessage('business_hours_end must be in HH:MM:SS format'),
  validate
], updateNotificationSettings);

/**
 * @swagger
 * /notifications/test:
 *   post:
 *     summary: Send test notification
 *     description: Send a test notification email to verify notification system functionality
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customerEmail
 *             properties:
 *               customerEmail:
 *                 type: string
 *                 format: email
 *                 description: Email address to send test notification to
 *                 example: "<EMAIL>"
 *               eventType:
 *                 type: string
 *                 enum: [
 *                   service_created,
 *                   service_started,
 *                   service_completed,
 *                   service_cancelled,
 *                   service_on_hold,
 *                   service_pending,
 *                   service_follow_up,
 *                   service_onsite,
 *                   service_on_process
 *                 ]
 *                 description: Type of notification event to test
 *                 example: "service_created"
 *           example:
 *             customerEmail: "<EMAIL>"
 *             eventType: "service_created"
 *     responses:
 *       200:
 *         description: Test notification sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         emailSent:
 *                           type: boolean
 *                           example: true
 *                         recipient:
 *                           type: string
 *                           example: "<EMAIL>"
 *                         eventType:
 *                           type: string
 *                           example: "service_created"
 *             example:
 *               success: true
 *               message: "Test notification sent successfully"
 *               data:
 *                 emailSent: true
 *                 recipient: "<EMAIL>"
 *                 eventType: "service_created"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/test', [
  requirePermission('notifications.test'),
  body('customerEmail').isEmail().withMessage('Valid customer email is required'),
  body('eventType').optional().isIn([
    'service_created',
    'service_started', 
    'service_completed',
    'service_cancelled',
    'service_on_hold',
    'service_pending',
    'service_follow_up',
    'service_onsite',
    'service_on_process'
  ]).withMessage('Invalid event type'),
  validate
], testNotification);

/**
 * @route   GET /api/notifications/event-types
 * @desc    Get available notification event types
 * @access  Private (requires notifications.read permission)
 */
router.get('/event-types', [
  requirePermission('notifications.read')
], getNotificationEventTypes);

// Notification Template Routes
router.get('/templates', [
  requirePermission('notifications.read')
], getNotificationTemplates);

router.get('/templates/:id', [
  requirePermission('notifications.read')
], getNotificationTemplateById);

router.post('/templates', [
  requirePermission('notifications.write'),
  body('name').notEmpty().withMessage('Template name is required'),
  body('type').isIn(['new_lead', 'new_customer', 'service_call_created', 'service_call_completed', 'renewal_reminder', 'custom']).withMessage('Invalid template type'),
  body('channel').isIn(['sms', 'email', 'whatsapp']).withMessage('Invalid notification channel'),
  body('content').notEmpty().withMessage('Template content is required'),
  body('subject').optional().isLength({ max: 200 }).withMessage('Subject must be less than 200 characters'),
  body('is_active').optional().isBoolean().withMessage('is_active must be a boolean'),
  body('is_default').optional().isBoolean().withMessage('is_default must be a boolean'),
  validate
], createNotificationTemplate);

router.get('/template-variables/:type', [
  requirePermission('notifications.read')
], getTemplateVariables);

export default router;
