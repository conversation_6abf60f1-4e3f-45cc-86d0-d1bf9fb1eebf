import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const SalaryStructure = sequelize.define('SalaryStructure', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Employee for this salary structure',
    },
    structure_name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Name/title of salary structure',
    },
    basic_salary: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Basic salary amount',
    },
    
    // Allowances
    hra: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'House Rent Allowance',
    },
    transport_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Transport/Conveyance Allowance',
    },
    medical_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Medical Allowance',
    },
    special_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Special Allowance',
    },
    performance_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Performance-based Allowance',
    },
    other_allowances: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other custom allowances as JSON object',
    },
    
    // Deductions
    pf_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee PF contribution',
    },
    pf_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer PF contribution',
    },
    esi_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee ESI contribution',
    },
    esi_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer ESI contribution',
    },
    professional_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Professional Tax',
    },
    income_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Income Tax (TDS)',
    },
    other_deductions: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other custom deductions as JSON object',
    },
    
    // Calculation settings
    overtime_rate_per_hour: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Overtime rate per hour',
    },
    late_deduction_per_hour: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction per hour for late arrival',
    },
    absent_deduction_per_day: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction per day for absence',
    },
    
    // Calculation methods
    pf_calculation_method: {
      type: DataTypes.ENUM('percentage', 'fixed'),
      allowNull: false,
      defaultValue: 'percentage',
      comment: 'How PF is calculated',
    },
    pf_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 12.00,
      comment: 'PF percentage if using percentage method',
    },
    esi_calculation_method: {
      type: DataTypes.ENUM('percentage', 'fixed'),
      allowNull: false,
      defaultValue: 'percentage',
      comment: 'How ESI is calculated',
    },
    esi_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 1.75,
      comment: 'ESI percentage if using percentage method',
    },
    
    // Validity
    effective_from: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date from which this structure is effective',
    },
    effective_to: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Date until which this structure is effective',
    },
    
    // Status
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this structure is currently active',
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is the default structure for the employee',
    },
    
    // Approval
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who approved this structure',
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When this structure was approved',
    },
    
    // Metadata
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes about this structure',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who created this structure',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'salary_structures',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['employee_id'],
      },
      {
        fields: ['effective_from'],
      },
      {
        fields: ['effective_to'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['is_default'],
      },
      {
        fields: ['tenant_id', 'employee_id', 'effective_from'],
        name: 'idx_employee_structure_date',
      },
    ],
  });

  // Instance methods
  SalaryStructure.prototype.calculateGrossSalary = function() {
    const allowances = this.hra + this.transport_allowance + this.medical_allowance + 
                      this.special_allowance + this.performance_allowance;
    
    let otherAllowancesTotal = 0;
    if (this.other_allowances) {
      otherAllowancesTotal = Object.values(this.other_allowances).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    }
    
    return this.basic_salary + allowances + otherAllowancesTotal;
  };

  SalaryStructure.prototype.calculateTotalDeductions = function() {
    const standardDeductions = this.pf_employee + this.esi_employee + 
                              this.professional_tax + this.income_tax;
    
    let otherDeductionsTotal = 0;
    if (this.other_deductions) {
      otherDeductionsTotal = Object.values(this.other_deductions).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    }
    
    return standardDeductions + otherDeductionsTotal;
  };

  SalaryStructure.prototype.calculateNetSalary = function() {
    return this.calculateGrossSalary() - this.calculateTotalDeductions();
  };

  SalaryStructure.prototype.calculatePF = function(basicSalary = null) {
    const salary = basicSalary || this.basic_salary;
    
    if (this.pf_calculation_method === 'percentage') {
      return (salary * this.pf_percentage) / 100;
    }
    return this.pf_employee;
  };

  SalaryStructure.prototype.calculateESI = function(grossSalary = null) {
    const salary = grossSalary || this.calculateGrossSalary();
    
    if (this.esi_calculation_method === 'percentage') {
      return (salary * this.esi_percentage) / 100;
    }
    return this.esi_employee;
  };

  SalaryStructure.prototype.calculateDailyWage = function(workingDaysInMonth = 30) {
    return this.calculateGrossSalary() / workingDaysInMonth;
  };

  SalaryStructure.prototype.calculateHourlyWage = function(workingHoursPerDay = 8, workingDaysInMonth = 30) {
    return this.calculateDailyWage(workingDaysInMonth) / workingHoursPerDay;
  };

  SalaryStructure.prototype.isEffectiveOn = function(date) {
    const checkDate = new Date(date);
    const fromDate = new Date(this.effective_from);
    const toDate = this.effective_to ? new Date(this.effective_to) : null;
    
    return checkDate >= fromDate && (!toDate || checkDate <= toDate);
  };

  // Class methods
  SalaryStructure.createDefaultStructure = function(employeeId, basicSalary, tenantId, createdBy) {
    return {
      tenant_id: tenantId,
      employee_id: employeeId,
      structure_name: 'Default Structure',
      basic_salary: basicSalary,
      hra: basicSalary * 0.4, // 40% of basic
      transport_allowance: 1600,
      medical_allowance: 1250,
      special_allowance: basicSalary * 0.1, // 10% of basic
      performance_allowance: 0,
      pf_employee: basicSalary * 0.12, // 12% of basic
      pf_employer: basicSalary * 0.12,
      esi_employee: 0,
      esi_employer: 0,
      professional_tax: 200,
      income_tax: 0,
      overtime_rate_per_hour: (basicSalary / 30 / 8) * 2, // Double the hourly rate
      late_deduction_per_hour: basicSalary / 30 / 8,
      absent_deduction_per_day: basicSalary / 30,
      pf_calculation_method: 'percentage',
      pf_percentage: 12.00,
      esi_calculation_method: 'percentage',
      esi_percentage: 1.75,
      effective_from: new Date(),
      is_active: true,
      is_default: true,
      created_by: createdBy,
    };
  };

  // Associations
  SalaryStructure.associate = function(models) {
    SalaryStructure.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    SalaryStructure.belongsTo(models.Executive, {
      foreignKey: 'employee_id',
      as: 'employee',
    });

    SalaryStructure.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    SalaryStructure.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver',
    });

    SalaryStructure.hasMany(models.PayrollRecord, {
      foreignKey: 'salary_structure_id',
      as: 'payrollRecords',
    });
  };

  return SalaryStructure;
}
