
TASK 1: Optimized Filter UI (Responsive Design)
User Story:

As a user, I want the leads filter section to have a clean, professional UI/UX that is fully responsive from a 24-inch desktop monitor to small mobile devices, so that I can filter leads efficiently across any device.

Acceptance Criteria:

Apply responsive grid system using Tailwind with grid-cols, gap, w-full, and breakpoint utilities.

Ensure padding, spacing, and alignment are optimized for all viewports.

The layout should avoid overflow and maintain consistency with the rest of the application.

TASK 2: Dropdown in Table Action Overflow Fix
User Story:

As a user, when I open the action dropdown in the leads table, I want it to appear outside the table scroll area so that it’s not cut off or hidden inside the table’s scrollable content.

Acceptance Criteria:

Position dropdown menu using absolute positioning and z-index to overlay outside table scroll container.

Ensure dropdown does not cause horizontal or vertical scroll within the table.

Dropdown should be fully accessible and scroll independently if content overflows.

TASK 3: Lead Creation Timestamp and Follow-Up Logic
User Story 1:

As an executive, I want the lead creation date to be automatically captured as a timestamp when the lead is submitted, so I don't have to manually enter the lead date.

User Story 2:

As an executive, I want to enter a “Follow Up Date” for when I plan to contact the lead again.

User Story 3:

As a user, I want to view the “Lead Created Timestamp” in the lead view form so I know when the lead was added.

Acceptance Criteria:

Remove manual Lead Date field.

Auto-generate created_at timestamp on lead creation.

Include Follow-Up Date input in the lead form.

Show created_at timestamp on lead view page.

TASK 4: Full-Width Add Lead Form
User Story:

As a user, I want the “Add New Lead” form to utilize the full width of the screen (as other modules do), so the form appears consistent and takes full advantage of space.

Acceptance Criteria:

Replace max-w-4xl mx-auto with w-full or appropriate layout styling.

Maintain responsive design.

Match styling with modules like customers, service calls, etc.

TASK 5: Lead Contact History & Conversion to Customer
User Story 1:

As an executive, I want to log every time I contact a lead, including timestamp and who contacted them, so we can track lead engagement history.

User Story 2:

As an executive, I want to convert a lead to a customer via an action button, and during conversion, I should be prompted to enter the Tally Serial Number.

Acceptance Criteria:

In Lead Edit or Action dropdown:

Add section to log contacts: timestamp, executive name.

Support multiple contact logs per lead.

Add “Convert to Customer” button:

Prompt for Tally Serial Number via modal.

On submission:

Move lead data to customers table.

Keep track that this lead was converted.

TASK 6: Custom Notification System
User Story 1:

As an admin, I want to customize notification templates (SMS, Email, WhatsApp) for different events (e.g., new lead, customer creation, service updates), so I can control communication tone and content.

User Story 2:

As a user, I want to configure which notification types a customer can receive (SMS, Email, WhatsApp) so that we respect their preferences.

User Story 3:

As an admin, I want to send renewal notifications for various expiry dates (TSS, AMC, Tally Cloud, etc.), based on user-defined reminder windows (e.g., 2/5/7 days before expiry), so customers are reminded in advance.

Acceptance Criteria:

🔹 Customer Preferences

In customer form (add/edit/pop-up), add checkboxes:

SMS ✅

Email ✅

WhatsApp ✅

🔹 Notification Templates Page (Settings)

Add customizable templates for:

New Lead

New Customer

Service Call Created

Service Status Changed

Lead Follow-up

Renewals (TSS, AMC, etc.)

Support for Email, SMS, and WhatsApp.

Validate content according to platform policies (character limits, opt-out keywords, etc.).

🔹 Renewal Notification Logic

Admin can set renewal reminder offset (e.g., X days before expiry).

Changing this offset will only affect new schedules, not previously scheduled messages.

System checks all expiry fields:

TSS, AMC, Tally Software Version, Tally Cloud, Tally on Mobile, WhatsApp, Website, Training, Support, Backup, Security, Compliance, Performance

Store and track notification history per customer.

🔁 General Notes
Ensure backend supports all the new fields and timestamp logs.

Use background jobs (cron or queue) to send scheduled notifications.

Ensure proper indexing on expiry_date fields for efficient renewal checks.

Store notification history and logs for audit and debugging.

 How to Handle Renewal Notification Lead Time Changes Safely
Here’s a safe and scalable approach:

✅ 1. Store Each Notification Schedule Separately
For each expiry (e.g., TSS), store a row in a notification_schedule table:

sql
Copy
Edit
customer_id
expiry_type        -- e.g., 'TSS'
expiry_date
notify_at          -- expiry_date - X days
scheduled_by       -- admin-defined offset (2, 5, 7...)
status             -- pending/sent/cancelled
created_at
This way, old schedules are preserved even if the offset is changed later.

✅ 2. Only Apply New Offsets to Future Schedules
Rule: When the admin changes the renewal offset, apply the new value only to future expiry dates or newly added customers.

Add a settings_version or timestamp to track when offset was changed.

Any existing scheduled rows with an older version remain untouched.

When generating new schedules:

Check latest offset.

If schedule exists, skip.

If not, generate with new notify_at.

✅ 3. Avoid Duplicates or Misses
Use a UNIQUE (customer_id, expiry_type, notify_at) constraint in the notification_schedule table.

Add a background job to clean up old/invalid scheduled rows if expiry dates or customer data change.

✅ 4. Sample Behavior Flow
Let’s say:

On June 19, Admin sets renewal lead time = 5 days

Customer A's TSS expiry = June 25 → Notify at June 20 ✅

Admin changes setting to 2 days on June 20

Customer B's TSS expiry = June 25 → Notify at June 23 ✅

Customer A’s June 20 notification remains scheduled and is not deleted or changed

✅ Final Safety Checks
Always validate: if notify_at > today() before scheduling.

Prevent deletion of future notifications unless expiry is changed manually.

Add logs and tracking for notification generation and delivery (audit trail).