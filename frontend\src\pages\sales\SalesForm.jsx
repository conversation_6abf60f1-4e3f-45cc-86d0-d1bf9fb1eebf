import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import { useCustomerSearch } from '../../hooks/useCustomerSearch';
import { FaSave, FaTimes, FaUser, FaRupeeSign, FaCalendar, FaChartLine } from 'react-icons/fa';
import SearchableSelect from '../../components/ui/SearchableSelect';
import LoadingScreen from '../../components/ui/LoadingScreen';

const SalesForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [salesTeam, setSalesTeam] = useState([]);
  const [products, setProducts] = useState([]);

  // Customer search hook for server-side search
  const { searchResults, isSearching, searchCustomers } = useCustomerSearch();

  const [formData, setFormData] = useState({
    // Lead Information
    leadNumber: '',
    customerId: '',
    customerName: '',
    contactPerson: '',
    email: '',
    phone: '',

    // Product/Service
    productId: '',
    productType: '',
    description: '',

    // Sales Information
    stage: 'lead',
    status: 'active',
    priority: 'medium',
    source: '',
    assignedTo: '',

    // Financial
    expectedValue: '',
    probability: 50,
    currency: 'INR',

    // Timeline
    expectedCloseDate: '',

    // Additional
    requirements: '',
    notes: '',

    // Competition
    competitors: '',
    competitiveAdvantage: ''
  });

  const [errors, setErrors] = useState({});

  // Fetch dropdown data from API
  useEffect(() => {

    fetchCustomers();
    fetchSalesTeam();
    fetchProducts();

    if (isEdit) {
      fetchSale();
    } else {
      // Generate lead number for new leads
      const leadNumber = `LEAD-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, leadNumber }));
    }
  }, [isEdit, id]);

  const fetchCustomers = async () => {
    try {
      const response = await apiService.get('/customers');
      if (response.data?.success && response.data?.data?.customers) {
        setCustomers(response.data.data.customers.map(customer => ({
          id: customer.id,
          name: customer.company_name,
          contactPerson: customer.contact_person,
          email: customer.email,
          phone: customer.phone
        })));
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
    }
  };

  const fetchSalesTeam = async () => {
    try {
      const response = await apiService.get('/executives');
      if (response.data?.success && response.data?.data?.executives) {
        setSalesTeam(response.data.data.executives
          .filter(executive => executive.department === 'sales')
          .map(executive => ({
            id: executive.id,
            name: `${executive.first_name} ${executive.last_name}`,
            specialization: executive.designation || 'Sales Executive'
          })));
      }
    } catch (error) {
      console.error('Error fetching sales team:', error);
      setSalesTeam([]);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await apiService.get('/master-data/tally-products');
      if (response.data?.success && response.data?.data) {
        setProducts(response.data.data.map(product => ({
          id: product.id,
          name: product.name,
          type: product.category,
          basePrice: product.price || 0
        })));
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts([]);
    }
  };

  const fetchSale = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/sales/${id}`);
      if (response.data?.success && response.data?.data) {
        const sale = response.data.data;
        setFormData({
          leadNumber: sale.sale_number || `LEAD-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
          customerId: sale.customer_id || '',
          customerName: sale.customer?.company_name || '',
          contactPerson: sale.customer?.contact_person || '',
          email: sale.customer?.email || '',
          phone: sale.customer?.phone || '',
          productId: sale.product_id || '',
          productType: sale.product?.type || '',
          description: sale.description || '',
          stage: sale.stage || 'lead',
          status: sale.status || 'active',
          priority: sale.priority || 'medium',
          source: sale.source || '',
          assignedTo: sale.sales_executive_id || '',
          expectedValue: sale.total_amount || '',
          probability: sale.probability || 50,
          currency: sale.currency || 'INR',
          expectedCloseDate: sale.expected_close_date ? sale.expected_close_date.split('T')[0] : '',
          requirements: sale.requirements || '',
          notes: sale.remarks || '',
          competitors: sale.competitors || '',
          competitiveAdvantage: sale.competitive_advantage || ''
        });
      }
    } catch (error) {
      console.error('Error fetching sale:', error);
      toast.error('Failed to load sales data');
      navigate('/sales');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Auto-fill customer details when customer is selected
    if (name === 'customerId' && value) {
      const selectedCustomer = customers.find(c => c.id === value);
      if (selectedCustomer) {
        setFormData(prev => ({
          ...prev,
          customerName: selectedCustomer.name,
          contactPerson: selectedCustomer.contactPerson,
          email: selectedCustomer.email,
          phone: selectedCustomer.phone
        }));
      }
    }

    // Auto-fill product details when product is selected
    if (name === 'productId' && value) {
      const selectedProduct = products.find(p => p.id === value);
      if (selectedProduct) {
        setFormData(prev => ({
          ...prev,
          productType: selectedProduct.type,
          expectedValue: selectedProduct.basePrice.toString()
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields
    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.contactPerson.trim()) newErrors.contactPerson = 'Contact person is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone is required';
    if (!formData.productId) newErrors.productId = 'Product/Service is required';
    if (!formData.assignedTo) newErrors.assignedTo = 'Sales person assignment is required';
    if (!formData.expectedValue.trim()) newErrors.expectedValue = 'Expected value is required';
    if (!formData.expectedCloseDate) newErrors.expectedCloseDate = 'Expected close date is required';

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Phone validation
    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Amount validation
    if (formData.expectedValue && (isNaN(formData.expectedValue) || parseFloat(formData.expectedValue) <= 0)) {
      newErrors.expectedValue = 'Please enter a valid amount';
    }

    // Probability validation
    if (formData.probability < 0 || formData.probability > 100) {
      newErrors.probability = 'Probability must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API - only include non-empty values
      const salesData = {};

      // Required fields
      if (formData.customerId) salesData.customer_id = formData.customerId;
      if (formData.expectedValue) salesData.total_amount = parseFloat(formData.expectedValue);

      // Optional fields - only include if they have values
      if (formData.assignedTo) salesData.sales_executive_id = formData.assignedTo;
      if (formData.description) salesData.description = formData.description;
      if (formData.notes) salesData.remarks = formData.notes;

      // Default values
      salesData.sale_type = 'new';
      salesData.sale_date = new Date().toISOString().split('T')[0];
      salesData.paid_amount = 0;
      salesData.status = formData.stage === 'closed-won' ? 'confirmed' : 'draft';

      let response;
      if (isEdit) {
        response = await apiService.put(`/sales/${id}`, salesData);
      } else {
        response = await apiService.post('/sales', salesData);
      }

      if (response.data?.success) {
        toast.success(isEdit ? 'Sales opportunity updated successfully' : 'Sales opportunity created successfully');
        navigate('/sales');
      } else {
        toast.error(response.data?.message || 'Failed to save sales opportunity');
      }
    } catch (error) {
      console.error('Error saving sales opportunity:', error);
      toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/sales');
  };

  if (loading && isEdit) {
    return (
      <LoadingScreen
        title="Loading Sales Form..."
        subtitle="Fetching sales opportunity data for editing"
        variant="modal"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mr-3 sm:mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                    <FaChartLine className="text-lg sm:text-xl" style={{ color: 'var(--primary-text)' }} />
                  </div>
                  <span className="hidden sm:inline">{isEdit ? 'Edit Sales Opportunity' : 'New Sales Opportunity'}</span>
                  <span className="sm:hidden">{isEdit ? 'Edit Sale' : 'New Sale'}</span>
                </h2>
                <p className="text-sm sm:text-base lg:text-lg" style={{ color: 'rgba(var(--primary-text-rgb), 0.8)' }}>
                  <span className="hidden sm:inline">{isEdit ? 'Update sales opportunity details and track progress' : 'Create a new sales lead or opportunity to drive revenue'}</span>
                  <span className="sm:hidden">{isEdit ? 'Update opportunity details' : 'Create new sales lead'}</span>
                </p>
              </div>
              <div className="flex flex-wrap gap-2 w-full sm:w-auto">
                <button
                  type="button"
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border-2 border-white border-opacity-30 text-xs sm:text-sm font-medium rounded-xl text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm flex-1 sm:flex-none justify-center"
                  onClick={handleCancel}
                >
                  <FaTimes className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Cancel</span>
                  <span className="sm:hidden">Cancel</span>
                </button>
                <button
                  type="submit"
                  form="salesForm"
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border border-transparent text-xs sm:text-sm font-medium rounded-xl text-theme-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg flex-1 sm:flex-none justify-center"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-theme-600 mr-1 sm:mr-2"></div>
                      <span className="hidden sm:inline">{isEdit ? 'Updating...' : 'Creating...'}</span>
                      <span className="sm:hidden">{isEdit ? 'Updating...' : 'Creating...'}</span>
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">{isEdit ? 'Update Opportunity' : 'Create Opportunity'}</span>
                      <span className="sm:hidden">{isEdit ? 'Update' : 'Create'}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form id="salesForm" onSubmit={handleSubmit} className="space-y-8">
          {/* Customer Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-theme-100">
            <div className="header-gradient px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaUser className="h-4 w-4 text-white" />
                </div>
                Customer Information
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-theme-50">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Lead Number */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Lead Number</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-gray-50 transition-all duration-200 sm:text-sm"
                    name="leadNumber"
                    value={formData.leadNumber}
                    readOnly
                    placeholder="Auto-generated"
                  />
                </div>

                {/* Customer */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">Customer *</label>
                  <SearchableSelect
                    options={customers}
                    value={formData.customerId}
                    onChange={(value) => handleInputChange({ target: { name: 'customerId', value } })}
                    placeholder="Search customers..."
                    searchFields={['name', 'company_name', 'phone', 'customer_code', 'tally_serial_number']}
                    displayField="name"
                    valueField="id"
                    error={!!errors.customerId}
                    minSearchLength={2}
                    className={`${
                      errors.customerId
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    // Server-side search props
                    onSearch={searchCustomers}
                    isSearching={isSearching}
                    searchResults={searchResults}
                    renderOption={(customer, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer ${isHighlighted ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                        <div className="font-medium text-gray-900">{customer.company_name || customer.name}</div>
                        <div className="text-sm text-gray-500">{customer.phone}</div>
                        <div className="text-xs text-gray-400">{customer.customer_code}</div>
                      </div>
                    )}
                  />
                  {errors.customerId && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerId}</p>}
                </div>

                {/* Contact Person */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">Contact Person *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.contactPerson
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                    }`}
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleInputChange}
                    placeholder="Enter contact person name"
                  />
                  {errors.contactPerson && <p className="mt-2 text-sm text-red-600 font-medium">{errors.contactPerson}</p>}
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Email *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.email
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                  />
                  {errors.email && <p className="mt-2 text-sm text-red-600 font-medium">{errors.email}</p>}
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Phone *</label>
                  <input
                    type="tel"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.phone
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300'
                    }`}
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="+91 9876543210"
                  />
                  {errors.phone && <p className="mt-2 text-sm text-red-600 font-medium">{errors.phone}</p>}
                </div>

                {/* Lead Source */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">Lead Source</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-teal-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300 transition-all duration-200 sm:text-sm"
                    name="source"
                    value={formData.source}
                    onChange={handleInputChange}
                  >
                    <option value="">Select source</option>
                    <option value="Website">🌐 Website</option>
                    <option value="Referral">👥 Referral</option>
                    <option value="Cold Call">📞 Cold Call</option>
                    <option value="Trade Show">🏢 Trade Show</option>
                    <option value="Social Media">📱 Social Media</option>
                    <option value="Advertisement">📺 Advertisement</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Product & Sales Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-pink-100">
            <div className="bg-gradient-to-r from-pink-500 to-purple-500 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center">
                <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                  <FaChartLine className="h-4 w-4 text-white" />
                </div>
                Product & Sales Information
              </h3>
            </div>
            <div className="p-6 bg-gradient-to-br from-white to-pink-50">
              <div className="grid grid-cols-12 gap-4">
                <div className="md:col-span-6 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Product/Service *</label>
                  <select
                    className={`form-select ${errors.productId ? 'is-invalid' : ''}`}
                    name="productId"
                    value={formData.productId}
                    onChange={handleInputChange}
                  >
                    <option value="">Select product/service</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.name} ({product.type})
                      </option>
                    ))}
                  </select>
                  {errors.productId && <div className="invalid-feedback">{errors.productId}</div>}
                </div>

                <div className="md:col-span-6 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Product Type</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    name="productType"
                    value={formData.productType}
                    readOnly
                  />
                </div>

                <div className="col-span-12 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="Describe the product/service requirements"
                  >
                  </textarea>
                </div>

                <div className="md:col-span-6 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sales Stage</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    name="stage"
                    value={formData.stage}
                    onChange={handleInputChange}
                  >
                    <option value="lead">Lead</option>
                    <option value="qualification">Qualification</option>
                    <option value="proposal">Proposal</option>
                    <option value="negotiation">Negotiation</option>
                    <option value="closed-won">Closed Won</option>
                    <option value="closed-lost">Closed Lost</option>
                  </select>
                </div>

                <div className="md:col-span-6 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    name="priority"
                    value={formData.priority}
                    onChange={handleInputChange}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>

                <div className="col-span-12 mb-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Assigned To *</label>
                  <select
                    className={`form-select ${errors.assignedTo ? 'is-invalid' : ''}`}
                    name="assignedTo"
                    value={formData.assignedTo}
                    onChange={handleInputChange}
                  >
                    <option value="">Select sales person</option>
                    {salesTeam.map(person => (
                      <option key={person.id} value={person.id}>
                        {person.name} ({person.specialization})
                      </option>
                    ))}
                  </select>
                  {errors.assignedTo && <div className="invalid-feedback">{errors.assignedTo}</div>}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-12 gap-4">
            {/* Financial Information */}
            <div className="lg:col-span-6 mb-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0">
                    <FaRupeeSign className="mr-2" />
                    Financial Information
                  </h5>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-12 gap-4">
                    <div className="md:col-span-8 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Expected Value *</label>
                      <input
                        type="number"
                        className={`form-control ${errors.expectedValue ? 'is-invalid' : ''}`}
                        name="expectedValue"
                        value={formData.expectedValue}
                        onChange={handleInputChange}
                        placeholder="0"
                        min="0"
                        step="0.01"
                      />
                      {errors.expectedValue && <div className="invalid-feedback">{errors.expectedValue}</div>}
                    </div>

                    <div className="md:col-span-4 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                      <select
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        name="currency"
                        value={formData.currency}
                        onChange={handleInputChange}
                      >
                        <option value="INR">INR</option>
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                      </select>
                    </div>

                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Probability (%)</label>
                      <div className="flex items-center">
                        <input
                          type="range"
                          className="form-range mr-3"
                          name="probability"
                          value={formData.probability}
                          onChange={handleInputChange}
                          min="0"
                          max="100"
                          step="5"
                        />
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-600">{formData.probability}%</span>
                      </div>
                    </div>

                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Expected Close Date *</label>
                      <input
                        type="date"
                        className={`form-control ${errors.expectedCloseDate ? 'is-invalid' : ''}`}
                        name="expectedCloseDate"
                        value={formData.expectedCloseDate}
                        onChange={handleInputChange}
                        min={new Date().toISOString().split('T')[0]}
                      />
                      {errors.expectedCloseDate && <div className="invalid-feedback">{errors.expectedCloseDate}</div>}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="lg:col-span-6 mb-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0">Additional Information</h5>
                </div>
                <div className="p-4">
                  <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Requirements</label>
                      <textarea
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        name="requirements"
                        value={formData.requirements}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="Customer requirements and specifications"
                      >
                      </textarea>
                    </div>

                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                      <textarea
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        rows="3"
                        placeholder="Additional notes and comments"
                      >
                      </textarea>
                    </div>

                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Competitors</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        name="competitors"
                        value={formData.competitors}
                        onChange={handleInputChange}
                        placeholder="Competing vendors or solutions"
                      />
                    </div>

                    <div className="col-span-12 mb-3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Competitive Advantage</label>
                      <textarea
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        name="competitiveAdvantage"
                        value={formData.competitiveAdvantage}
                        onChange={handleInputChange}
                        rows="2"
                        placeholder="Our advantages over competitors"
                      >
                      </textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SalesForm;
