# TallyCRM Production Setup Guide

## Admin Credentials

### Default Admin User
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`
- **Role**: Administrator (Full Access)
- **Tenant**: Default Tenant

## Database Setup

### Production Database Configuration
- **Host**: ****
- **Port**: 5432
- **Database**: *****
- **Username**: postgres
- **Password**: *****
- **SSL Mode**: Disabled

## Fresh Database Setup (New Database)

### Prerequisites
1. Ensure PostgreSQL database is created and accessible
2. Update `.env` file with correct database credentials
3. Verify database connection works

### Step-by-Step Fresh Database Setup

#### Option 1: Fresh Database Setup Script (Recommended for New DB)
```bash
# Navigate to backend directory
cd backend

# Run fresh database setup (step-by-step with detailed logging)
npm run setup:fresh
```

#### Option 2: Complete Automated Setup (For Existing Setup)
```bash
# Navigate to backend directory
cd backend

# Run complete production setup (migrations + seeding + cleanup)
npm run production:setup
```

#### Option 3: Manual Step-by-Step Setup
```bash
# Navigate to backend directory
cd backend

# Step 1: Test database connection
node test-pg-connection.js

# Step 2: Run all database migrations
npm run migrate

# Step 3: Seed all production data
npm run seed:all

# Step 4: Clean up test files (optional)
npm run cleanup:production
```

#### Option 4: Individual Seeding (Advanced)
```bash
# Run migrations first
npm run migrate

# Then seed in order:
npm run seed:admin      # Admin user (<EMAIL>)
npm run seed:plans      # Subscription plans
npm run seed:master     # Master data (products, areas, etc.)
npm run seed:sample     # Sample data (optional)
```

### Fresh Database Reset (If Needed)
```bash
# WARNING: This will delete all data!
npm run db:reset
```

## Production Deployment Steps

### 1. Database Migration & Seeding
```bash
# Run all migrations
npm run migrate

# Seed all production data (includes admin user)
npm run seed:all

# Or run complete production setup
npm run production:setup
```

### 2. Individual Seeding Options
```bash
# Seed admin user only
npm run seed:admin

# Seed subscription plans only
npm run seed:plans

# Seed master data only
npm run seed:master

# Seed sample data only (optional)
npm run seed:sample
```

### 3. Production Cleanup
```bash
# Remove test files and development artifacts
npm run cleanup:production
```

## What Gets Seeded

### Admin User & Tenant
- ✅ Default admin user (<EMAIL>)
- ✅ Default tenant with enterprise settings
- ✅ Admin role and permissions
- ✅ Executive record for admin user

### Subscription Plans (SaaS)
- ✅ Starter Plan (₹999/month)
- ✅ Professional Plan (₹2999/month)
- ✅ Business Plan (₹4999/month)
- ✅ Enterprise Plan (₹9999/month)

### Master Data
- ✅ 4 License Editions (Silver, Gold, Prime Silver, Prime Gold)
- ✅ 6 Tally Products (ERP 9, TallyPrime, Training, Services)
- ✅ 9 Designations (MD, GM, SM, TM, SE, TE, ACC, DEO, OA)
- ✅ 6 Staff Roles (Sales Executive, Technical Support, etc.)
- ✅ 10 Industries (Manufacturing, Trading, Services, IT, etc.)
- ✅ 20 Areas (Bangalore, Mumbai, Delhi, Chennai - 5 zones each)
- ✅ 7 Nature of Issues (Installation, Data, Performance, etc.)
- ✅ 6 Call Statuses (Open, In Progress, Resolved, etc.)
- ✅ 6 Additional Services (Data Migration, Custom Reports, etc.)

### Sample Data (Optional)
- ✅ Sample customers and contacts (if demo tenant exists)
- ✅ Sample service calls and sales data

## Files Removed in Production

### Test Files Removed
- All `test-*.js` files
- `basic-test.js`, `quick-test.js`
- `src/test/` directory
- `coverage/` directory
- `jest.config.js`

### Development Scripts Removed
- `create-test-*.js` files
- `fix-*.js` files
- `verify-passwords.js`
- Development-only database files

### Files Kept for Production Maintenance
- ✅ `create-admin.js` (backup admin creation)
- ✅ `fresh-setup.js` (database setup)
- ✅ `test-pg-connection.js` (connection testing)
- ✅ `src/seeders/` (all seeders)
- ✅ `src/migrations/` (all migrations)
- ✅ `scripts/` (setup scripts)

## Production Environment Variables

### Required Environment Variables
```env
NODE_ENV=production
DB_HOST=**************
DB_PORT=5432
DB_NAME=tallyerp
DB_USERNAME=postgres
DB_PASSWORD=
DB_DIALECT=postgres
DATABASE_URL=postgresql://postgres
```

### Security Settings
```env
JWT_SECRET=your_production_jwt_secret_here
SESSION_SECRET=your_production_session_secret_here
BCRYPT_SALT_ROUNDS=12
ENABLE_RATE_LIMITING=true
```

## Verification Steps

### 1. Test Database Connection
```bash
node test-pg-connection.js
```

### 2. Verify Admin Login
- Navigate to login page
- Use credentials: `<EMAIL>` / `Admin@123`
- Verify full admin access

### 3. Check Seeded Data
- Verify master data is populated
- Check subscription plans are available
- Confirm all dropdowns have data

## Backup & Recovery

### Database Backup
```bash
pg_dump -h ************** -p 5432 -U postgres -d tallyerp > backup.sql
```

### Restore from Backup
```bash
psql -h ************** -p 5432 -U postgres -d tallyerp < backup.sql
```

## Monitoring & Maintenance

### Health Check
- Endpoint: `/health`
- Monitors database connectivity
- Returns system status

### Log Files
- Location: `logs/` directory
- Rotation: Daily
- Retention: 14 days

## Security Considerations

### Production Security Checklist
- ✅ Strong JWT secrets configured
- ✅ Rate limiting enabled
- ✅ CORS properly configured
- ✅ SSL/HTTPS enabled (recommended)
- ✅ Database credentials secured
- ✅ Test files removed
- ✅ Debug logging disabled

## Troubleshooting Fresh Database Setup

### Common Seeding Issues & Solutions

#### 1. Database Connection Failed
```bash
# Test connection first
node test-pg-connection.js

# Check these in .env file:
DB_HOST=
DB_PORT=
DB_NAME=
DB_USERNAME=
DB_PASSWORD=
```

#### 2. Migration Errors
```bash
# Check if database exists
psql -h ************** -U postgres -l

# Create database if missing
psql -h ************** -U postgres -c "CREATE DATABASE tallyerp;"

# Run migrations again
npm run migrate
```

#### 3. Seeding Fails with "Table doesn't exist"
```bash
# Ensure migrations run first
npm run migrate

# Then run seeding
npm run seed:all
```

#### 4. "Admin user already exists" Error
```bash
# Check existing users
psql -h ************** -U postgres -d tallyerp -c "SELECT email FROM users;"

# If you want to recreate, reset database
npm run db:reset
```

#### 5. Seeding Partially Completes
```bash
# Check which data was seeded
psql -h ************** -U postgres -d tallyerp -c "
SELECT 'users' as table_name, count(*) as count FROM users
UNION ALL
SELECT 'license_editions', count(*) FROM license_editions
UNION ALL
SELECT 'tally_products', count(*) FROM tally_products
UNION ALL
SELECT 'industries', count(*) FROM industries;"

# Re-run specific seeders if needed
npm run seed:admin
npm run seed:plans
npm run seed:master
```

#### 6. Permission Denied Errors
```bash
# Check database user permissions
psql -h ************** -U postgres -d tallyerp -c "
SELECT grantee, privilege_type
FROM information_schema.role_table_grants
WHERE table_name='users';"

# Grant permissions if needed (run as superuser)
psql -h ************** -U postgres -d tallyerp -c "
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;"
```

### Fresh Database Verification Checklist

After seeding, verify these items:

```bash
# 1. Check admin user exists
psql -h ************** -U postgres -d tallyerp -c "
SELECT email, first_name, last_name, is_active
FROM users WHERE email='<EMAIL>';"

# 2. Check master data counts
psql -h ************** -U postgres -d tallyerp -c "
SELECT 'License Editions' as data_type, count(*) as count FROM license_editions WHERE is_active=true
UNION ALL
SELECT 'Tally Products', count(*) FROM tally_products WHERE is_active=true
UNION ALL
SELECT 'Industries', count(*) FROM industries WHERE is_active=true
UNION ALL
SELECT 'Areas', count(*) FROM areas WHERE is_active=true
UNION ALL
SELECT 'Designations', count(*) FROM designations WHERE is_active=true;"

# 3. Test admin login
curl -X POST http://localhost:3002/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin@123"}'
```

### Expected Data Counts After Fresh Seeding

| Data Type | Expected Count |
|-----------|----------------|
| Admin Users | 1 |
| Subscription Plans | 4 |
| License Editions | 4 |
| Tally Products | 6 |
| Industries | 10 |
| Areas | 20 |
| Designations | 9 |
| Staff Roles | 6 |
| Nature of Issues | 7 |
| Call Statuses | 6 |
| Additional Services | 6 |

## Support & Troubleshooting

### Quick Fixes for Common Issues
1. **Database Connection Failed**: Check network connectivity and credentials
2. **Seeding Errors**: Ensure database is empty or use `npm run db:reset`
3. **Admin Login Failed**: Verify user was created with `npm run seed:admin`
4. **Missing Tables**: Run `npm run migrate` before seeding
5. **Partial Data**: Check logs and re-run specific seeders

### Emergency Database Reset
```bash
# DANGER: This deletes ALL data!
npm run db:reset

# Then setup fresh database
npm run production:setup
```

### Contact Information
- **System Administrator**: [To be filled]
- **Database Administrator**: [To be filled]
- **Technical Support**: [To be filled]

---

**Status**: ✅ PRODUCTION READY  
**Last Updated**: December 2024  
**Version**: 1.0.0
