import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Generic controller for master data operations
 */
class MasterDataController {
  constructor(modelName, modelDisplayName) {
    this.model = models[modelName];
    this.modelName = modelName;
    this.modelDisplayName = modelDisplayName || modelName;
  }

  /**
   * Preprocess data to handle empty values for optional integer fields
   */
  preprocessData = (data) => {
    const processedData = { ...data };

    // List of fields that should be converted from empty strings to null
    const optionalFields = ['max_companies', 'max_users', 'sort_order', 'hsn_code', 'version', 'unit'];

    optionalFields.forEach(field => {
      if (processedData.hasOwnProperty(field)) {
        const value = processedData[field];
        // Convert empty strings, empty values, or whitespace-only strings to null
        if (value === '' || value === null || value === undefined ||
            (typeof value === 'string' && value.trim() === '')) {
          processedData[field] = null;
        }
      }
    });

    return processedData;
  };

  /**
   * Get all records with pagination and search
   */
  getAll = async (req, res) => {
    try {
      const {
        page = 1,
        limit = 50,
        search,
        isActive,
        sortBy = 'sort_order',
        sortOrder = 'ASC',
      } = req.query;

      const offset = (page - 1) * limit;
      const where = {};

      // Apply search filter
      if (search) {
        where[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } },
        ];
        
        // Add description search if model has description field
        if (this.model.rawAttributes.description) {
          where[Op.or].push({ description: { [Op.iLike]: `%${search}%` } });
        }
      }

      // Apply active filter
      if (isActive !== undefined) {
        where.is_active = isActive === 'true';
      }

      const { count, rows: records } = await this.model.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]],
      });

      res.json({
        success: true,
        data: {
          [this.modelName.toLowerCase()]: records,
          pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(count / limit),
            totalItems: count,
            itemsPerPage: parseInt(limit),
          },
        },
      });

    } catch (error) {
      logger.error(`Get ${this.modelDisplayName} error:`, error);

      // Check if it's a table doesn't exist error
      if (error.name === 'SequelizeDatabaseError' && error.message.includes('does not exist')) {
        return res.status(500).json({
          success: false,
          message: `Database table for ${this.modelDisplayName.toLowerCase()} does not exist. Please run migrations.`,
          error: process.env.NODE_ENV === 'development' ? {
            message: error.message,
            hint: 'Run: npm run migrate',
            table: this.modelName.toLowerCase() + 's'
          } : undefined,
        });
      }

      res.status(500).json({
        success: false,
        message: `Failed to fetch ${this.modelDisplayName.toLowerCase()}`,
        error: process.env.NODE_ENV === 'development' ? {
          message: error.message,
          name: error.name,
          stack: error.stack
        } : undefined,
      });
    }
  };

  /**
   * Get record by ID
   */
  getById = async (req, res) => {
    try {
      const { id } = req.params;

      const record = await this.model.findByPk(id);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: `${this.modelDisplayName} not found`,
        });
      }

      res.json({
        success: true,
        data: { [this.modelName.toLowerCase()]: record },
      });

    } catch (error) {
      logger.error(`Get ${this.modelDisplayName} by ID error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to fetch ${this.modelDisplayName.toLowerCase()}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }
  };

  /**
   * Create new record
   */
  create = async (req, res) => {
    try {
      const recordData = this.preprocessData(req.body);

      // Check if code already exists
      if (recordData.code) {
        const existingRecord = await this.model.findOne({
          where: { code: recordData.code },
        });

        if (existingRecord) {
          return res.status(409).json({
            success: false,
            message: `${this.modelDisplayName} with this code already exists`,
          });
        }
      }

      const record = await this.model.create(recordData);

      logger.info(`${this.modelDisplayName} created successfully:`, {
        id: record.id,
        code: record.code,
        name: record.name,
        createdBy: req.user?.id,
      });

      res.status(201).json({
        success: true,
        message: `${this.modelDisplayName} created successfully`,
        data: { [this.modelName.toLowerCase()]: record },
      });

    } catch (error) {
      logger.error(`Create ${this.modelDisplayName} error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to create ${this.modelDisplayName.toLowerCase()}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }
  };

  /**
   * Update record
   */
  update = async (req, res) => {
    try {
      const { id } = req.params;
      const updateData = this.preprocessData(req.body);

      const record = await this.model.findByPk(id);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: `${this.modelDisplayName} not found`,
        });
      }

      // Check if record is a default record and prevent editing core fields
      if (record.is_default) {
        // For default records, only allow updating certain fields
        const allowedFields = ['description', 'color', 'is_active'];
        const restrictedFields = Object.keys(updateData).filter(field => !allowedFields.includes(field));

        if (restrictedFields.length > 0) {
          return res.status(409).json({
            success: false,
            message: `Cannot modify ${restrictedFields.join(', ')} for default ${this.modelDisplayName.toLowerCase()}. Only description, color, and active status can be updated.`,
          });
        }
      }

      // Check if code is being changed and if it already exists
      if (updateData.code && updateData.code !== record.code) {
        const existingRecord = await this.model.findOne({
          where: {
            code: updateData.code,
            id: { [Op.ne]: id },
          },
        });

        if (existingRecord) {
          return res.status(409).json({
            success: false,
            message: `${this.modelDisplayName} with this code already exists`,
          });
        }
      }

      await record.update(updateData);

      logger.info(`${this.modelDisplayName} updated successfully:`, {
        id: record.id,
        code: record.code,
        updatedBy: req.user?.id,
      });

      res.json({
        success: true,
        message: `${this.modelDisplayName} updated successfully`,
        data: { [this.modelName.toLowerCase()]: record },
      });

    } catch (error) {
      logger.error(`Update ${this.modelDisplayName} error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to update ${this.modelDisplayName.toLowerCase()}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }
  };

  /**
   * Delete record
   */
  delete = async (req, res) => {
    try {
      const { id } = req.params;

      const record = await this.model.findByPk(id);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: `${this.modelDisplayName} not found`,
        });
      }

      // Check if record is being used (if it's a system record or default record)
      if (record.is_system || record.is_default) {
        return res.status(409).json({
          success: false,
          message: `Cannot delete ${record.is_system ? 'system' : 'default'} ${this.modelDisplayName.toLowerCase()}`,
        });
      }

      await record.destroy();

      logger.info(`${this.modelDisplayName} deleted successfully:`, {
        id: record.id,
        code: record.code,
        deletedBy: req.user?.id,
      });

      res.json({
        success: true,
        message: `${this.modelDisplayName} deleted successfully`,
      });

    } catch (error) {
      logger.error(`Delete ${this.modelDisplayName} error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to delete ${this.modelDisplayName.toLowerCase()}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }
  };

  /**
   * Toggle active status
   */
  toggleActive = async (req, res) => {
    try {
      const { id } = req.params;

      const record = await this.model.findByPk(id);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: `${this.modelDisplayName} not found`,
        });
      }

      await record.update({ is_active: !record.is_active });

      logger.info(`${this.modelDisplayName} status toggled:`, {
        id: record.id,
        code: record.code,
        isActive: record.is_active,
        updatedBy: req.user?.id,
      });

      res.json({
        success: true,
        message: `${this.modelDisplayName} status updated successfully`,
        data: { [this.modelName.toLowerCase()]: record },
      });

    } catch (error) {
      logger.error(`Toggle ${this.modelDisplayName} status error:`, error);
      res.status(500).json({
        success: false,
        message: `Failed to update ${this.modelDisplayName.toLowerCase()} status`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }
  };
}

// Create controller instances for each master data model
export const licenseEditionController = new MasterDataController('LicenseEdition', 'License Edition');
export const designationController = new MasterDataController('Designation', 'Designation');
export const tallyProductController = new MasterDataController('TallyProduct', 'Tally Product');
export const staffRoleController = new MasterDataController('StaffRole', 'Staff Role');
export const industryController = new MasterDataController('Industry', 'Industry');
export const areaController = new MasterDataController('Area', 'Area');
export const natureOfIssueController = new MasterDataController('NatureOfIssue', 'Nature of Issue');
export const additionalServiceController = new MasterDataController('AdditionalService', 'Additional Service');
export const callStatusController = new MasterDataController('CallStatus', 'Call Status');
export const typeOfCallController = new MasterDataController('TypeOfCall', 'Type of Call');
