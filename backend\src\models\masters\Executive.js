import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Executive = sequelize.define('Executive', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'Link to user account if executive has system access',
    },
    employee_code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 20],
      },
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50],
      },
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50],
      },
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [10, 15],
      },
    },
    alternate_phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: {
          args: [10, 15],
          msg: 'Alternate phone must be between 10 and 15 characters'
        },
        isValidPhone(value) {
          if (value && value.trim() === '') {
            throw new Error('Alternate phone cannot be empty string');
          }
        }
      },
    },
    designation_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'designations',
        key: 'id',
      },
    },
    staff_role_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'staff_roles',
        key: 'id',
      },
    },
    department: {
      type: DataTypes.ENUM('sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'),
      allowNull: false,
      defaultValue: 'support',
    },
    date_of_joining: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Date of joining must be a valid date'
        }
      },
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      validate: {
        isDate: {
          msg: 'Date of birth must be a valid date'
        }
      },
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    emergency_contact_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    emergency_contact_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    salary: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    commission_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Commission percentage for sales',
    },
    target_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Monthly/Annual target amount',
    },
    skills: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of skills and certifications',
    },
    areas_covered: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of areas/territories covered',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    profile_image: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    name: {
      type: DataTypes.VIRTUAL,
      get() {
        return `${this.first_name || ''} ${this.last_name || ''}`.trim();
      },
      set(value) {
        throw new Error('Do not try to set the `name` value!');
      }
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'executives',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'employee_code'],
        unique: true,
      },
      {
        fields: ['user_id'],
      },
      {
        fields: ['department'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['email'],
      },
      {
        fields: ['phone'],
      },
    ],
  });

  // Instance methods
  Executive.prototype.getFullName = function() {
    return `${this.first_name} ${this.last_name}`;
  };

  Executive.prototype.getAge = function() {
    if (!this.date_of_birth) return null;
    const today = new Date();
    const birthDate = new Date(this.date_of_birth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  Executive.prototype.getExperience = function() {
    if (!this.date_of_joining) return null;
    const today = new Date();
    const joinDate = new Date(this.date_of_joining);
    const diffTime = Math.abs(today - joinDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    return { years, months, totalDays: diffDays };
  };

  // Associations
  Executive.associate = function(models) {
    Executive.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Executive.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user',
    });

    Executive.belongsTo(models.Designation, {
      foreignKey: 'designation_id',
      as: 'designation',
    });

    Executive.belongsTo(models.StaffRole, {
      foreignKey: 'staff_role_id',
      as: 'staffRole',
    });

    Executive.hasMany(models.ServiceCall, {
      foreignKey: 'assigned_to',
      as: 'assignedServiceCalls',
    });

    Executive.hasMany(models.Sale, {
      foreignKey: 'sales_executive_id',
      as: 'sales',
    });

    Executive.hasMany(models.Customer, {
      foreignKey: 'assigned_executive_id',
      as: 'assignedCustomers',
    });

    // Attendance Management Associations
    Executive.belongsTo(models.AttendancePolicy, {
      foreignKey: 'attendance_policy_id',
      as: 'attendancePolicy',
    });

    Executive.hasMany(models.AttendanceRecord, {
      foreignKey: 'employee_id',
      as: 'attendanceRecords',
    });

    Executive.hasMany(models.LeaveRequest, {
      foreignKey: 'employee_id',
      as: 'leaveRequests',
    });

    Executive.hasMany(models.LeaveBalance, {
      foreignKey: 'employee_id',
      as: 'leaveBalances',
    });

    Executive.hasMany(models.SalaryStructure, {
      foreignKey: 'employee_id',
      as: 'salaryStructures',
    });

    Executive.hasMany(models.PayrollRecord, {
      foreignKey: 'employee_id',
      as: 'payrollRecords',
    });

    // Work handover relationships
    Executive.hasMany(models.LeaveRequest, {
      foreignKey: 'work_handover_to',
      as: 'handoverRequests',
    });
  };

  return Executive;
}
