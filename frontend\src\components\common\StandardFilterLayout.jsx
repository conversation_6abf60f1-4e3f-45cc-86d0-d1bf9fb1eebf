import React from 'react';
import { FaSearch, FaFilter } from 'react-icons/fa';

/**
 * Standardized filter layout component for consistent filter UI across all pages
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Filter components to render
 * @param {string} props.searchValue - Current search value
 * @param {function} props.onSearchChange - Search change handler
 * @param {string} props.searchPlaceholder - Search input placeholder
 * @param {boolean} props.showSearch - Whether to show search input (default: true)
 * @param {React.ReactNode} props.leftActions - Additional actions on the left side
 * @param {React.ReactNode} props.rightActions - Additional actions on the right side
 * @param {string} props.className - Additional CSS classes
 */
const StandardFilterLayout = ({
  children,
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'Search...',
  showSearch = true,
  leftActions,
  rightActions,
  className = ''
}) => {
  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <div className="flex justify-between items-center px-6 py-4">
        {/* Left side - Search and Left Actions */}
        <div className="flex items-center gap-4 flex-1">
          {leftActions}
          
          {showSearch && (
            <div className="relative max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                placeholder={searchPlaceholder}
                value={searchValue}
                onChange={(e) => onSearchChange && onSearchChange(e.target.value)}
              />
            </div>
          )}
        </div>

        {/* Center - Filter Controls */}
        <div className="flex items-center gap-3">
          {children}
        </div>

        {/* Right side - Right Actions */}
        <div className="flex items-center gap-3">
          {rightActions}
        </div>
      </div>
    </div>
  );
};

/**
 * Standard filter dropdown component
 */
export const FilterDropdown = ({
  value,
  onChange,
  options = [],
  placeholder = 'Select...',
  icon: Icon,
  className = ''
}) => {
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {Icon && <Icon className="text-gray-400" size={14} />}
      <select
        value={value}
        onChange={(e) => onChange && onChange(e.target.value)}
        className="px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]"
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
};

/**
 * Standard date filter component
 */
export const DateFilter = ({
  value,
  onChange,
  placeholder = 'Select date',
  className = ''
}) => {
  return (
    <input
      type="date"
      value={value}
      onChange={(e) => onChange && onChange(e.target.value)}
      className={`px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
      placeholder={placeholder}
    />
  );
};

export default StandardFilterLayout;
