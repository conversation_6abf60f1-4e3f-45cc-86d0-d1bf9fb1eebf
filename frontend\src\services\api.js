import axios from 'axios';
import { API_CONFIG, STORAGE_KEYS, FEATURES } from '../utils/constants';
import { addRetryInterceptor, checkApiHealth } from '../utils/axiosRetry';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
  // Production-specific configurations
  validateStatus: function (status) {
    return status >= 200 && status < 300; // default
  },
  maxRedirects: 5,
  // Retry configuration for production
  retry: 3,
  retryDelay: 1000,
});

// Add retry logic for production
if (process.env.NODE_ENV === 'production') {
  addRetryInterceptor(api, {
    retries: 3,
    retryDelay: 1000,
    onRetry: (retryCount, error, delay) => {
      console.warn(`API Retry ${retryCount}: ${error.message} (waiting ${delay}ms)`);
    }
  });
}

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Helper function to clear auth data and redirect to login
const clearAuthAndRedirect = () => {
  console.log('Clearing authentication data and redirecting to login');

  // Clear all possible token storage keys
  localStorage.removeItem(STORAGE_KEYS.TOKEN);
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);

  // Also clear any legacy keys that might exist
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('user');
  localStorage.removeItem('auth-storage');

  // Avoid redirect loop if already on login page
  if (!window.location.pathname.includes('/auth/login')) {
    window.location.href = '/auth/login';
  }
};

// Helper function to check if error indicates invalid token
const isInvalidTokenError = (error) => {
  const { response } = error;
  if (!response) return false;

  // Check for 401 status
  if (response.status === 401) return true;

  // Check for specific error codes in response data
  const { data } = response;
  if (data && (
    data.code === 'INVALID_TOKEN' ||
    data.code === 'TOKEN_EXPIRED' ||
    data.code === 'AUTH_REQUIRED' ||
    data.message === 'Invalid access token' ||
    data.message === 'Token expired' ||
    data.message === 'Authentication required'
  )) {
    return true;
  }

  return false;
};

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Enhanced error logging for production debugging
    if (process.env.NODE_ENV === 'production') {
      console.error('API Error:', {
        url: originalRequest?.url,
        method: originalRequest?.method,
        status: error.response?.status,
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
      });
    }

    // Handle network errors (connection issues, timeouts, etc.)
    if (!error.response) {
      // Network error, timeout, or CORS issue
      if (error.code === 'ECONNABORTED') {
        console.error('Request timeout:', error.message);
      } else if (error.message === 'Network Error') {
        console.error('Network error - possible CORS or connection issue');
      } else if (error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED')) {
        // Connection refused - likely backend not ready yet
        console.warn('Backend server not ready yet, retrying in 2 seconds...');
        // For startup connection issues, wait and retry once
        if (!originalRequest._startupRetry) {
          originalRequest._startupRetry = true;
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(api(originalRequest));
            }, 2000);
          }).catch(() => {
            // If retry fails, just log and continue
            console.warn('Backend still not ready after retry, skipping request');
            return Promise.reject(new Error('Backend not ready'));
          });
        }
      }
      return Promise.reject(error);
    }

    // Handle invalid token errors
    if (isInvalidTokenError(error) && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (refreshToken) {
          const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { token } = response.data;
          localStorage.setItem(STORAGE_KEYS.TOKEN, token);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }
        // No refresh token, redirect to login
        clearAuthAndRedirect();
        return Promise.reject(error);

      } catch (refreshError) {
        // Refresh failed, redirect to login
        console.error('Token refresh failed:', refreshError);
        clearAuthAndRedirect();
        return Promise.reject(refreshError);
      }
    }

    // Handle other authentication errors that should redirect to login
    if (error.response?.status === 403 && error.response?.data?.code === 'ACCESS_DENIED') {
      clearAuthAndRedirect();
      return Promise.reject(error);
    }

    // Handle server errors (5xx)
    if (error.response?.status >= 500) {
      console.error('Server error:', error.response.status, error.response.data);
    }

    return Promise.reject(error);
  }
);

// Mock API responses when ENABLE_MOCK_API is true
const mockResponses = {
  // Auth endpoints
  'POST /auth/login': {
    data: {
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token',
      user: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete'],
      },
    },
  },
  'POST /auth/refresh': {
    data: {
      token: 'new-mock-jwt-token',
    },
  },
  'POST /auth/logout': {
    data: { message: 'Logged out successfully' },
  },

  // Dashboard endpoints
  'GET /dashboard/stats': {
    data: {
      totalCustomers: 1234,
      activeServices: 89,
      monthlyRevenue: 245000,
      pendingTasks: 23,
    },
  },

  // Customer endpoints
  'GET /customers': {
    data: {
      customers: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        pages: 0,
      },
    },
  },
  'POST /customers': {
    data: {
      success: true,
      message: 'Customer created successfully',
      data: {
        customer: {
          id: 'mock-customer-id',
          company_name: 'Mock Company',
          customer_code: 'MOCK001',
          created_at: new Date().toISOString(),
        }
      }
    },
  },
};

// Mock API function
const mockApiCall = (method, url) => {
  const key = `${method.toUpperCase()} ${url}`;
  const mockResponse = mockResponses[key];

  if (mockResponse) {
    return Promise.resolve({ data: mockResponse.data });
  }

  return Promise.reject(new Error(`Mock API: No mock response found for ${key}`));
};

// API service functions
export const apiService = {
  // Generic HTTP methods
  get: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('GET', url);
    }
    return api.get(url, config);
  },

  post: (url, data = {}, config = {}) => {
    console.log(`apiService.post called: ${url}`, data);
    if (FEATURES.ENABLE_MOCK_API) {
      console.log('Using mock API for POST request');
      return mockApiCall('POST', url);
    }
    console.log('Making real API POST request');
    return api.post(url, data, config);
  },

  put: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PUT', url);
    }
    return api.put(url, data, config);
  },

  patch: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PATCH', url);
    }
    return api.patch(url, data, config);
  },

  delete: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('DELETE', url);
    }
    return api.delete(url, config);
  },

  // File upload
  upload: (url, formData, config = {}) => {
    const uploadConfig = {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data',
      },
    };

    if (FEATURES.ENABLE_MOCK_API) {
      return Promise.resolve({ data: { message: 'File uploaded successfully' } });
    }

    return api.post(url, formData, uploadConfig);
  },

  // Health check and diagnostics
  healthCheck: async () => {
    try {
      const response = await api.get('/health');
      return {
        healthy: true,
        data: response.data,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('API health check failed:', error);
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  },

  // Check API connectivity
  checkConnectivity: async () => {
    return await checkApiHealth(API_CONFIG.BASE_URL);
  },

  // Get API status for debugging
  getApiStatus: () => {
    return {
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      mockEnabled: FEATURES.ENABLE_MOCK_API,
      timestamp: new Date().toISOString()
    };
  }
};

// Auth API
export const authAPI = {
  login: (credentials) => apiService.post('/auth/login', credentials),
  logout: () => apiService.post('/auth/logout'),
  refresh: (refreshToken) => apiService.post('/auth/refresh', { refreshToken }),
  register: (userData) => apiService.post('/auth/register', userData),
  forgotPassword: (email) => apiService.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => apiService.post('/auth/reset-password', { token, password }),
  verifyEmail: (token) => apiService.post('/auth/verify-email', { token }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: () => apiService.get('/dashboard/stats'),
  getRecentActivities: () => apiService.get('/dashboard/activities'),
  getChartData: (type) => apiService.get(`/dashboard/charts/${type}`),
};

// Executive API
export const executiveAPI = {
  // Get all executives
  getAll: (params = {}) => apiService.get('/executives', { params }),

  // Get executive by ID
  getById: (id) => apiService.get(`/executives/${id}`),

  // Create new executive
  create: (data) => apiService.post('/executives', data),

  // Update existing executive
  update: (id, data) => apiService.put(`/executives/${id}`, data),

  // Delete executive
  delete: (id) => apiService.delete(`/executives/${id}`),

  // Get executive statistics
  getStats: () => apiService.get('/executives/stats'),

  // Get active executives for dropdowns
  getActiveExecutives: () => apiService.get('/executives', {
    params: {
      isActive: true,
      limit: 100,
      sortBy: 'first_name',
      sortOrder: 'ASC'
    }
  }),
};

// Master Data API
export const masterDataAPI = {
  // Industries
  getIndustries: (params = {}) => apiService.get('/master-data/industries', { params }),
  getIndustryById: (id) => apiService.get(`/master-data/industries/${id}`),
  createIndustry: (data) => apiService.post('/master-data/industries', data),
  updateIndustry: (id, data) => apiService.put(`/master-data/industries/${id}`, data),
  deleteIndustry: (id) => apiService.delete(`/master-data/industries/${id}`),

  // Areas
  getAreas: (params = {}) => apiService.get('/master-data/areas', { params }),
  getAreaById: (id) => apiService.get(`/master-data/areas/${id}`),
  createArea: (data) => apiService.post('/master-data/areas', data),
  updateArea: (id, data) => apiService.put(`/master-data/areas/${id}`, data),
  deleteArea: (id) => apiService.delete(`/master-data/areas/${id}`),

  // License Editions
  getLicenseEditions: (params = {}) => apiService.get('/master-data/license-editions', { params }),
  getLicenseEditionById: (id) => apiService.get(`/master-data/license-editions/${id}`),
  createLicenseEdition: (data) => apiService.post('/master-data/license-editions', data),
  updateLicenseEdition: (id, data) => apiService.put(`/master-data/license-editions/${id}`, data),
  deleteLicenseEdition: (id) => apiService.delete(`/master-data/license-editions/${id}`),

  // Tally Products
  getTallyProducts: (params = {}) => apiService.get('/master-data/tally-products', { params }),
  getTallyProductById: (id) => apiService.get(`/master-data/tally-products/${id}`),
  createTallyProduct: (data) => apiService.post('/master-data/tally-products', data),
  updateTallyProduct: (id, data) => apiService.put(`/master-data/tally-products/${id}`, data),
  deleteTallyProduct: (id) => apiService.delete(`/master-data/tally-products/${id}`),

  // Executives
  getExecutives: (params = {}) => apiService.get('/executives', { params }),
  getExecutiveById: (id) => apiService.get(`/executives/${id}`),
  createExecutive: (data) => apiService.post('/executives', data),
  updateExecutive: (id, data) => apiService.put(`/executives/${id}`, data),
  deleteExecutive: (id) => apiService.delete(`/executives/${id}`),

  // Additional Services
  getAdditionalServices: (params = {}) => apiService.get('/master-data/additional-services', { params }),
  getAdditionalServiceById: (id) => apiService.get(`/master-data/additional-services/${id}`),
  createAdditionalService: (data) => apiService.post('/master-data/additional-services', data),
  updateAdditionalService: (id, data) => apiService.put(`/master-data/additional-services/${id}`, data),
  deleteAdditionalService: (id) => apiService.delete(`/master-data/additional-services/${id}`),

  // Type of Calls
  getTypeOfCalls: (params = {}) => apiService.get('/master-data/type-of-calls', { params }),
  getTypeOfCallById: (id) => apiService.get(`/master-data/type-of-calls/${id}`),
  createTypeOfCall: (data) => apiService.post('/master-data/type-of-calls', data),
  updateTypeOfCall: (id, data) => apiService.put(`/master-data/type-of-calls/${id}`, data),
  deleteTypeOfCall: (id) => apiService.delete(`/master-data/type-of-calls/${id}`),

  // Call Statuses
  getCallStatuses: (params = {}) => apiService.get('/master-data/call-statuses', { params }),
  getCallStatusById: (id) => apiService.get(`/master-data/call-statuses/${id}`),
  createCallStatus: (data) => apiService.post('/master-data/call-statuses', data),
  updateCallStatus: (id, data) => apiService.put(`/master-data/call-statuses/${id}`, data),
  deleteCallStatus: (id) => apiService.delete(`/master-data/call-statuses/${id}`),

  // Products/Issues
  getProductsIssues: (params = {}) => apiService.get('/master-data/products-issues', { params }),
  getProductsIssuesById: (id) => apiService.get(`/master-data/products-issues/${id}`),
  createProductsIssues: (data) => apiService.post('/master-data/products-issues', data),
  updateProductsIssues: (id, data) => apiService.put(`/master-data/products-issues/${id}`, data),
  deleteProductsIssues: (id) => apiService.delete(`/master-data/products-issues/${id}`),

  // Designations
  getDesignations: (params = {}) => apiService.get('/master-data/designations', { params }),
  getDesignationById: (id) => apiService.get(`/master-data/designations/${id}`),
  createDesignation: (data) => apiService.post('/master-data/designations', data),
  updateDesignation: (id, data) => apiService.put(`/master-data/designations/${id}`, data),
  deleteDesignation: (id) => apiService.delete(`/master-data/designations/${id}`),

  // Search across all master data
  searchMasterData: (query, type = null) => {
    const params = { search: query };
    if (type) params.type = type;
    return apiService.get('/master-data/search', { params });
  }
};

// Customer API
export const customerAPI = {
  // Get all customers with advanced filtering
  getAll: (params = {}) => apiService.get('/customers', { params }),

  // Get customer by ID with optional relations and stats
  getById: (id, options = {}) => {
    const { includeRelations = true, includeStats = false } = options;
    return apiService.get(`/customers/${id}`, {
      params: { includeRelations, includeStats }
    });
  },

  // Create new customer with comprehensive validation
  create: (data) => {
    console.log('customerAPI.create called with data:', data);
    // Frontend validation before sending to backend
    const validatedData = customerAPI.validateCustomerData(data);
    console.log('Validated data:', validatedData);
    console.log('Making POST request to /customers');
    return apiService.post('/customers', validatedData);
  },

  // Update existing customer
  update: (id, data) => {
    const validatedData = customerAPI.validateCustomerData(data, false);
    return apiService.put(`/customers/${id}`, validatedData);
  },

  // Delete customer with options
  delete: (id, options = {}) => {
    const { force = false, reason } = options;
    const params = {};
    if (force) params.force = force;
    if (reason) params.reason = reason;

    return apiService.delete(`/customers/${id}`, { params });
  },

  // Get customer statistics
  getStats: (filters = {}) => apiService.get('/customers/stats', { params: filters }),

  // Get individual customer statistics (services and payments)
  getStatistics: (id) => apiService.get(`/customers/${id}/statistics`),

  // Search customers
  search: (query, filters = {}) => {
    return apiService.get('/customers', {
      params: { search: query, ...filters }
    });
  },

  // Validate customer data on frontend
  validateCustomerData: (data, isCreate = true) => {
    const validatedData = { ...data };

    // Clean and format data
    if (validatedData.company_name) {
      validatedData.company_name = validatedData.company_name.trim();
    }

    if (validatedData.customer_code) {
      validatedData.customer_code = validatedData.customer_code.toUpperCase().trim();
    }

    if (validatedData.email) {
      validatedData.email = validatedData.email.toLowerCase().trim();
    }

    if (validatedData.gst_number) {
      validatedData.gst_number = validatedData.gst_number.toUpperCase().replace(/\s/g, '');
    }

    if (validatedData.pan_number) {
      validatedData.pan_number = validatedData.pan_number.toUpperCase().replace(/\s/g, '');
    }

    // Remove empty strings and convert to null
    Object.keys(validatedData).forEach(key => {
      if (validatedData[key] === '') {
        validatedData[key] = null;
      }
    });

    return validatedData;
  },

  // Frontend validation rules
  validationRules: {
    company_name: {
      required: true,
      minLength: 2,
      maxLength: 200,
      pattern: /^[a-zA-Z0-9\s\-\.\&\(\)]+$/,
      message: 'Company name must be 2-200 characters and contain only letters, numbers, spaces, and basic punctuation'
    },
    customer_code: {
      required: false,
      minLength: 2,
      maxLength: 20,
      pattern: /^[A-Z0-9]+$/,
      message: 'Customer code must be 2-20 characters and contain only uppercase letters and numbers'
    },
    contact_person: {
      required: false,
      minLength: 2,
      maxLength: 100,
      pattern: /^[a-zA-Z\s\.]+$/,
      message: 'Contact person must be 2-100 characters and contain only letters, spaces, and periods'
    },
    email: {
      required: false,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Please provide a valid email address'
    },
    phone: {
      required: false,
      pattern: /^[\+]?[1-9][\d]{0,15}$/,
      message: 'Please provide a valid phone number'
    },
    gst_number: {
      required: false,
      length: 15,
      pattern: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
      message: 'GST number must be exactly 15 characters in valid format'
    },
    pan_number: {
      required: false,
      length: 10,
      pattern: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
      message: 'PAN number must be exactly 10 characters in valid format'
    }
  },

  // Validate single field
  validateField: (fieldName, value, isRequired = false) => {
    const rule = customerAPI.validationRules[fieldName];
    if (!rule) return { isValid: true };

    // Check if required
    if (isRequired && (!value || value.trim() === '')) {
      return { isValid: false, message: `${fieldName} is required` };
    }

    // Skip validation if field is empty and not required
    if (!value || value.trim() === '') {
      return { isValid: true };
    }

    // Check length constraints
    if (rule.minLength && value.length < rule.minLength) {
      return { isValid: false, message: rule.message };
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return { isValid: false, message: rule.message };
    }

    if (rule.length && value.length !== rule.length) {
      return { isValid: false, message: rule.message };
    }

    // Check pattern
    if (rule.pattern && !rule.pattern.test(value)) {
      return { isValid: false, message: rule.message };
    }

    return { isValid: true };
  },

  // Validate entire form
  validateForm: (data, requiredFields = []) => {
    const errors = {};

    // Check required fields
    requiredFields.forEach(field => {
      if (!data[field] || data[field].trim() === '') {
        errors[field] = `${field.replace('_', ' ')} is required`;
      }
    });

    // Validate each field
    Object.keys(data).forEach(field => {
      if (customerAPI.validationRules[field]) {
        const validation = customerAPI.validateField(
          field,
          data[field],
          requiredFields.includes(field)
        );

        if (!validation.isValid) {
          errors[field] = validation.message;
        }
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

// Lead API
export const leadAPI = {
  // Get all leads with advanced filtering
  getAll: (params = {}) => apiService.get('/leads', { params }),

  // Get lead by ID
  getById: (id) => apiService.get(`/leads/${id}`),

  // Create new lead with validation
  create: (data) => {
    console.log('leadAPI.create called with data:', data);
    const validatedData = leadAPI.validateLeadData(data);
    console.log('Validated data:', validatedData);
    return apiService.post('/leads', validatedData);
  },

  // Update existing lead
  update: (id, data) => {
    const validatedData = leadAPI.validateLeadData(data, false);
    return apiService.put(`/leads/${id}`, validatedData);
  },

  // Delete lead
  delete: (id) => apiService.delete(`/leads/${id}`),

  // Get lead statistics
  getStats: () => apiService.get('/leads/stats'),

  // Search leads
  search: (query, filters = {}) => {
    return apiService.get('/leads', {
      params: { search: query, ...filters }
    });
  },

  // Get lead contact history
  getContactHistory: (leadId, params = {}) => {
    return apiService.get(`/leads/${leadId}/contact-history`, { params });
  },

  // Add contact history entry
  addContactHistory: (leadId, data) => {
    return apiService.post(`/leads/${leadId}/contact-history`, data);
  },

  // Convert lead to customer
  convertToCustomer: (leadId, data) => {
    return apiService.post(`/leads/${leadId}/convert`, data);
  },

  // Validate lead data on frontend
  validateLeadData: (data, isCreate = true) => {
    const validatedData = { ...data };

    // Clean and format data
    if (validatedData.customer_name) {
      validatedData.customer_name = validatedData.customer_name.trim();
    }

    if (validatedData.products) {
      validatedData.products = validatedData.products.trim();
    }

    if (validatedData.contact_no) {
      validatedData.contact_no = validatedData.contact_no.trim();
    }

    if (validatedData.ref_contact_no) {
      validatedData.ref_contact_no = validatedData.ref_contact_no.trim();
    }

    if (validatedData.executive) {
      validatedData.executive = validatedData.executive.trim();
    }

    if (validatedData.status) {
      validatedData.status = validatedData.status.trim();
    }

    if (validatedData.ref_name) {
      validatedData.ref_name = validatedData.ref_name.trim();
    }

    // Convert amounts to numbers
    if (validatedData.amount) {
      validatedData.amount = parseFloat(validatedData.amount);
    }

    if (validatedData.ref_amount) {
      validatedData.ref_amount = parseFloat(validatedData.ref_amount);
    }

    // Remove empty strings and convert to null
    Object.keys(validatedData).forEach(key => {
      if (validatedData[key] === '') {
        validatedData[key] = null;
      }
    });

    return validatedData;
  },

  // Frontend validation rules
  validationRules: {
    customer_name: {
      required: false,
      maxLength: 200,
      message: 'Customer name must be less than 200 characters'
    },
    products: {
      required: false,
      message: 'Products field is optional'
    },
    amount: {
      required: false,
      min: 0,
      message: 'Amount must be a positive number'
    },
    contact_no: {
      required: false,
      maxLength: 20,
      pattern: /^[\+]?[\d\s\-\(\)]+$/,
      message: 'Please provide a valid phone number (max 20 characters)'
    },
    country_code: {
      required: false,
      maxLength: 10,
      pattern: /^\+\d{1,4}$/,
      message: 'Country code must be in format +XX (max 10 characters)'
    },
    status: {
      required: false,
      maxLength: 50,
      message: 'Status must be less than 50 characters'
    },
    remarks: {
      required: false,
      message: 'Remarks field is optional'
    },
    executive: {
      required: false,
      maxLength: 100,
      message: 'Executive name must be less than 100 characters'
    },
    ref_name: {
      required: false,
      maxLength: 100,
      message: 'Reference name must be less than 100 characters'
    },
    ref_contact_no: {
      required: false,
      maxLength: 20,
      pattern: /^[\+]?[\d\s\-\(\)]+$/,
      message: 'Please provide a valid reference phone number (max 20 characters)'
    },
    ref_country_code: {
      required: false,
      maxLength: 10,
      pattern: /^\+\d{1,4}$/,
      message: 'Reference country code must be in format +XX (max 10 characters)'
    },
    ref_amount: {
      required: false,
      min: 0,
      message: 'Reference amount must be a positive number'
    }
  },

  // Validate single field
  validateField: (fieldName, value, isRequired = false) => {
    const rule = leadAPI.validationRules[fieldName];
    if (!rule) return { isValid: true };

    // Check if required
    if (isRequired && (!value || value.toString().trim() === '')) {
      return { isValid: false, message: `${fieldName.replace('_', ' ')} is required` };
    }

    // Skip validation if field is empty and not required
    if (!value || value.toString().trim() === '') {
      return { isValid: true };
    }

    // Check length constraints
    if (rule.maxLength && value.toString().length > rule.maxLength) {
      return { isValid: false, message: rule.message };
    }

    // Check numeric constraints
    if (rule.min !== undefined && parseFloat(value) < rule.min) {
      return { isValid: false, message: rule.message };
    }

    // Check pattern
    if (rule.pattern && !rule.pattern.test(value.toString())) {
      return { isValid: false, message: rule.message };
    }

    return { isValid: true };
  },

  // Validate entire form
  validateForm: (data, requiredFields = []) => {
    const errors = {};

    // Check required fields
    requiredFields.forEach(field => {
      if (!data[field] || data[field].toString().trim() === '') {
        errors[field] = `${field.replace('_', ' ')} is required`;
      }
    });

    // Validate each field
    Object.keys(data).forEach(field => {
      if (leadAPI.validationRules[field]) {
        const validation = leadAPI.validateField(
          field,
          data[field],
          requiredFields.includes(field)
        );

        if (!validation.isValid) {
          errors[field] = validation.message;
        }
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};

// Service Call API
export const serviceCallAPI = {
  // Get all service calls with filtering
  getAll: (params = {}) => apiService.get('/service-calls', { params }),

  // Get service call by ID
  getById: (id, options = {}) => {
    const { includeRelations = true } = options;
    return apiService.get(`/service-calls/${id}`, {
      params: { includeRelations }
    });
  },

  // Create new service call
  create: (data) => {
    console.log('serviceCallAPI.create called with data:', data);
    return apiService.post('/service-calls', data);
  },

  // Update existing service call
  update: (id, data) => {
    console.log('serviceCallAPI.update called with data:', data);
    return apiService.put(`/service-calls/${id}`, data);
  },

  // Delete service call
  delete: (id, options = {}) => {
    const { reason } = options;
    const params = {};
    if (reason) params.reason = reason;
    return apiService.delete(`/service-calls/${id}`, { params });
  },

  // Get service call statistics
  getStats: (filters = {}) => apiService.get('/service-calls/stats', { params: filters }),

  // Search service calls
  search: (query, filters = {}) => {
    return apiService.get('/service-calls', {
      params: { search: query, ...filters }
    });
  },

  // Get customer by serial number
  getCustomerBySerial: (serialNumber) => {
    console.log('serviceCallAPI.getCustomerBySerial called with:', serialNumber);
    return apiService.get(`/customers/serial/${serialNumber}`);
  }
};

// Notification API
export const notificationAPI = {
  // Get notification templates
  getTemplates: (params = {}) => {
    return apiService.get('/notifications/templates', { params });
  },

  // Get notification template by ID
  getTemplateById: (id) => {
    return apiService.get(`/notifications/templates/${id}`);
  },

  // Create notification template
  createTemplate: (data) => {
    return apiService.post('/notifications/templates', data);
  },

  // Update notification template
  updateTemplate: (id, data) => {
    return apiService.put(`/notifications/templates/${id}`, data);
  },

  // Delete notification template
  deleteTemplate: (id) => {
    return apiService.delete(`/notifications/templates/${id}`);
  },

  // Get template variables for a type
  getTemplateVariables: (type) => {
    return apiService.get(`/notifications/template-variables/${type}`);
  },

  // Get notification settings
  getSettings: () => {
    return apiService.get('/notifications/settings');
  },

  // Update notification settings
  updateSettings: (data) => {
    return apiService.put('/notifications/settings', data);
  },

  // Test notification
  testNotification: (data) => {
    return apiService.post('/notifications/test', data);
  },

  // Get event types
  getEventTypes: () => {
    return apiService.get('/notifications/event-types');
  },
};

// Attendance API
export const attendanceAPI = {
  // Check in
  checkIn: (data) => {
    return apiService.post('/attendance/check-in', data);
  },

  // Check out
  checkOut: (data) => {
    return apiService.post('/attendance/check-out', data);
  },

  // Get today's attendance
  getTodayAttendance: () => {
    return apiService.get('/attendance/today');
  },

  // Get attendance records
  getRecords: (params) => {
    return apiService.get('/attendance/records', { params });
  },

  // Create manual entry
  createManualEntry: (data) => {
    return apiService.post('/attendance/manual-entry', data);
  },

  // Update attendance record
  updateRecord: (id, data) => {
    return apiService.put(`/attendance/records/${id}`, data);
  },

  // Get attendance summary
  getSummary: (params) => {
    return apiService.get('/attendance/summary', { params });
  },
};

// Leave API
export const leaveAPI = {
  // Get leave types
  getTypes: () => {
    return apiService.get('/leaves/types');
  },

  // Submit leave request
  submitRequest: (data) => {
    return apiService.post('/leaves/request', data);
  },

  // Get leave requests
  getRequests: (params) => {
    return apiService.get('/leaves/requests', { params });
  },

  // Approve leave request
  approveRequest: (id, data) => {
    return apiService.put(`/leaves/requests/${id}/approve`, data);
  },

  // Reject leave request
  rejectRequest: (id, data) => {
    return apiService.put(`/leaves/requests/${id}/reject`, data);
  },

  // Cancel leave request
  cancelRequest: (id) => {
    return apiService.put(`/leaves/requests/${id}/cancel`);
  },

  // Get leave balance
  getBalance: (params) => {
    return apiService.get('/leaves/balance', { params });
  },

  // Get leave calendar
  getCalendar: (params) => {
    return apiService.get('/leaves/calendar', { params });
  },
};

// Payroll API
export const payrollAPI = {
  // Get salary structure
  getSalaryStructure: (params) => {
    return apiService.get('/payroll/salary-structure', { params });
  },

  // Create salary structure
  createSalaryStructure: (data) => {
    return apiService.post('/payroll/salary-structure', data);
  },

  // Process payroll
  processPayroll: (data) => {
    return apiService.post('/payroll/process', data);
  },

  // Get payroll records
  getRecords: (params) => {
    return apiService.get('/payroll/records', { params });
  },

  // Approve payroll
  approvePayroll: (id) => {
    return apiService.put(`/payroll/records/${id}/approve`);
  },

  // Mark payroll as paid
  markPaid: (id, data) => {
    return apiService.put(`/payroll/records/${id}/mark-paid`, data);
  },

  // Get payroll reports
  getReports: (params) => {
    return apiService.get('/payroll/reports', { params });
  },
};

export default api;
