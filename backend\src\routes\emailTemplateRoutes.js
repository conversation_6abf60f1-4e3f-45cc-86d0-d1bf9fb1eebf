import express from 'express';
import { 
  getEmailTemplates, 
  updateEmailTemplate, 
  getEmailTemplate 
} from '../controllers/emailTemplateController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @route   GET /api/settings/email-templates
 * @desc    Get all email templates for the current tenant
 * @access  Private
 */
router.get('/', getEmailTemplates);

/**
 * @route   GET /api/settings/email-templates/:templateKey
 * @desc    Get a specific email template
 * @access  Private
 */
router.get('/:templateKey', getEmailTemplate);

/**
 * @route   PUT /api/settings/email-templates/:templateKey
 * @desc    Update a specific email template
 * @access  Private
 */
router.put('/:templateKey', updateEmailTemplate);

export default router;
