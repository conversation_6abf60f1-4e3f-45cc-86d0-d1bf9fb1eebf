import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest as validate, resolveCustomerFromSerial, mapServiceCallFields } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getServiceCalls,
  getServiceCallById,
  createServiceCall,
  updateServiceCall,
  deleteServiceCall,
  getServiceCallStats,
  getServiceCallFilters,
  getTimeTrackingSummary,
  getTimeTrackingStats,
  getTimerStatus,
  getServiceCallTimerHistory,
  testEmail,
  getEmailLogs,
  clearEmailLogs,
  getEmailSendStatusLog,
  getEmailSendStatistics,
  debugDatabase,
} from '../controllers/serviceCallController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @swagger
 * /service-calls:
 *   get:
 *     summary: Get all service calls
 *     description: Retrieve a paginated list of service calls with comprehensive filtering and sorting options
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *           default: 10
 *         description: Number of service calls per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search term for service number, subject, or description
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by customer ID
 *       - in: query
 *         name: statusId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by status ID
 *       - in: query
 *         name: assignedTo
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by assigned user ID
 *       - in: query
 *         name: callType
 *         schema:
 *           type: string
 *           enum: [online, onsite, phone, email]
 *         description: Filter by call type
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: Filter by priority level
 *       - in: query
 *         name: isUnderAmc
 *         schema:
 *           type: boolean
 *         description: Filter by AMC status
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, call_date, scheduled_date, priority]
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC, asc, desc]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Service calls retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCalls:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/ServiceCall'
 *             example:
 *               success: true
 *               message: "Service calls retrieved successfully"
 *               data:
 *                 serviceCalls:
 *                   - id: "123e4567-e89b-12d3-a456-************"
 *                     service_number: "SER-001"
 *                     customer_name: "ABC Enterprises Pvt Ltd"
 *                     contact_number: "+91-9876543210"
 *                     call_type: "AMC Call"
 *                     status: "Open"
 *                     timer_status: "stopped"
 *                     total_time_seconds: 0
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 total: 50
 *                 totalPages: 5
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/', [
  requirePermission('service_calls.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit must be between 1 and 1000'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('statusId')
    .optional()
    .isUUID()
    .withMessage('Status ID must be a valid UUID'),
  query('assignedTo')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  query('callType')
    .optional()
    .isIn(['online', 'onsite', 'phone', 'email'])
    .withMessage('Invalid call type'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  query('isUnderAmc')
    .optional()
    .isBoolean()
    .withMessage('isUnderAmc must be a boolean'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'call_date', 'scheduled_date', 'priority'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
  // New filter parameters
  query('callBillingType')
    .optional()
    .isIn(['free_call', 'amc_call', 'per_call'])
    .withMessage('Invalid call billing type'),
  query('isOverdue')
    .optional()
    .isBoolean()
    .withMessage('isOverdue must be a boolean'),
  query('statusCategory')
    .optional()
    .isIn(['open', 'in_progress', 'resolved', 'closed', 'cancelled'])
    .withMessage('Invalid status category'),
  query('scheduledDateFrom')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date from must be a valid date'),
  query('scheduledDateTo')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date to must be a valid date'),
  validate,
], getServiceCalls);

/**
 * @swagger
 * /service-calls/stats:
 *   get:
 *     summary: Get service call statistics
 *     description: Retrieve comprehensive statistics about service calls including counts, status breakdowns, and performance metrics
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Service call statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         totalCalls:
 *                           type: integer
 *                           example: 150
 *                         callsByStatus:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               status:
 *                                 type: string
 *                                 example: "Open"
 *                               category:
 *                                 type: string
 *                                 example: "active"
 *                               color:
 *                                 type: string
 *                                 example: "#007bff"
 *                               count:
 *                                 type: integer
 *                                 example: 25
 *                         callsByType:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               call_billing_type:
 *                                 type: string
 *                                 example: "amc call"
 *                               count:
 *                                 type: integer
 *                                 example: 80
 *                         recentCalls:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/ServiceCall'
 *                         monthlyGrowth:
 *                           type: object
 *                           properties:
 *                             current:
 *                               type: integer
 *                               example: 15
 *                             previous:
 *                               type: integer
 *                               example: 12
 *                             percentage:
 *                               type: number
 *                               example: 25.0
 *             example:
 *               success: true
 *               message: "Service call statistics retrieved successfully"
 *               data:
 *                 totalCalls: 150
 *                 callsByStatus:
 *                   - status: "Open"
 *                     category: "active"
 *                     color: "#007bff"
 *                     count: 25
 *                   - status: "In Progress"
 *                     category: "active"
 *                     color: "#28a745"
 *                     count: 30
 *                 callsByType:
 *                   - call_billing_type: "amc call"
 *                     count: 80
 *                   - call_billing_type: "free call"
 *                     count: 45
 *                 monthlyGrowth:
 *                   current: 15
 *                   previous: 12
 *                   percentage: 25.0
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/stats', [
  requirePermission('service_calls.read'),
], getServiceCallStats);

/**
 * @route   GET /api/service-calls/filters
 * @desc    Get filter options for service calls
 * @access  Private (requires service_calls.read permission)
 */
router.get('/filters', [
  requirePermission('service_calls.read'),
], getServiceCallFilters);

/**
 * @route   GET /api/service-calls/filter-options
 * @desc    Get filter options for service calls (alias for /filters)
 * @access  Private (requires service_calls.read permission)
 */
router.get('/filter-options', [
  requirePermission('service_calls.read'),
], getServiceCallFilters);

/**
 * @route   GET /api/service-calls/time-tracking/stats
 * @desc    Get time tracking statistics
 * @access  Private (requires service_calls.read permission)
 */
router.get('/time-tracking/stats', [
  requirePermission('service_calls.read'),
], getTimeTrackingStats);

/**
 * @route   GET /api/service-calls/debug
 * @desc    Debug database status
 * @access  Private (requires service_calls.read permission)
 */
router.get('/debug', [
  requirePermission('service_calls.read'),
], debugDatabase);

/**
 * @swagger
 * /service-calls/{id}:
 *   get:
 *     summary: Get service call by ID
 *     description: Retrieve detailed information about a specific service call including timer status and history
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Service call ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Service call retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCall:
 *                           allOf:
 *                             - $ref: '#/components/schemas/ServiceCall'
 *                             - type: object
 *                               properties:
 *                                 customer:
 *                                   $ref: '#/components/schemas/Customer'
 *                                 status_details:
 *                                   type: object
 *                                   properties:
 *                                     name:
 *                                       type: string
 *                                       example: "Open"
 *                                     category:
 *                                       type: string
 *                                       example: "active"
 *                                     color:
 *                                       type: string
 *                                       example: "#007bff"
 *             example:
 *               success: true
 *               message: "Service call retrieved successfully"
 *               data:
 *                 serviceCall:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   service_number: "SER-001"
 *                   customer_name: "ABC Enterprises Pvt Ltd"
 *                   contact_number: "+91-9876543210"
 *                   call_type: "AMC Call"
 *                   status: "Open"
 *                   timer_status: "stopped"
 *                   total_time_seconds: 3600
 *                   customer:
 *                     id: "456e7890-e89b-12d3-a456-************"
 *                     company_name: "ABC Enterprises Pvt Ltd"
 *                     contact_person: "John Doe"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Service call not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Service call not found"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/:id', [
  requirePermission('service_calls.read'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], getServiceCallById);

/**
 * @swagger
 * /service-calls:
 *   post:
 *     summary: Create new service call
 *     description: Create a new service call with auto-generated service number and optional timer functionality
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceCallCreateRequest'
 *           example:
 *             customer_id: "123e4567-e89b-12d3-a456-************"
 *             contact_number: "+91-9876543210"
 *             call_type: "AMC Call"
 *             status: "Open"
 *             subject: "Tally installation issue"
 *             description: "Customer facing issues with Tally installation on new system"
 *             scheduled_date: "2024-01-15"
 *             products_issues_id: "456e7890-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: Service call created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCall:
 *                           $ref: '#/components/schemas/ServiceCall'
 *             example:
 *               success: true
 *               message: "Service call created successfully"
 *               data:
 *                 serviceCall:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   service_number: "SER-001"
 *                   customer_name: "ABC Enterprises Pvt Ltd"
 *                   contact_number: "+91-9876543210"
 *                   call_type: "AMC Call"
 *                   status: "Open"
 *                   timer_status: "stopped"
 *                   total_time_seconds: 0
 *                   created_at: "2024-01-15T10:30:00Z"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       422:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Validation failed"
 *               errors:
 *                 customer_id: "Customer ID is required"
 *                 contact_number: "Contact number is required"
 *                 call_type: "Call type must be one of: Free Call, AMC Call, Paid Call"
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/', [
  requirePermission('service_calls.create'),
  // Map frontend field names to backend field names
  mapServiceCallFields,
  // Resolve customer from serial number if provided
  resolveCustomerFromSerial,
  // Validate customer_serial_number OR customer_id (one must be provided)
  body('customer_serial_number')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer serial number must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer serial number must contain only uppercase letters and numbers'),
  body('customer_id')
    .optional()
    .custom((value, { req }) => {
      // Either customer_serial_number or customer_id must be provided
      if (!value && !req.body.customer_serial_number) {
        throw new Error('Either Customer Serial Number or Customer ID is required');
      }

      // If customer_id is provided, validate it
      if (value) {
        // Allow both UUID and integer IDs for backward compatibility
        if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          return true; // Valid UUID
        }
        if (typeof value === 'string' && value.match(/^\d+$/)) {
          return true; // Valid integer ID as string
        }
        if (typeof value === 'number' && Number.isInteger(value)) {
          return true; // Valid integer ID
        }
        throw new Error('Customer ID must be a valid UUID or integer');
      }
      return true;
    }),
  // Make subject and description optional
  body('subject')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Subject must not exceed 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must not exceed 2000 characters'),
  // Streamlined backend validations - focus on critical business logic
  body('call_number')
    .optional()
    .trim(),
  body('contact_person_id')
    .optional()
    .custom((value) => {
      if (!value) return true;
      // Basic UUID validation for critical fields
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      throw new Error('Contact person ID must be a valid UUID');
    }),
  body('tss_id')
    .optional()
    .custom((value) => {
      if (!value) return true;
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      throw new Error('TSS ID must be a valid UUID');
    }),
  body('amc_id')
    .optional()
    .custom((value) => {
      if (!value) return true;
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      throw new Error('AMC ID must be a valid UUID');
    }),
  // Remove detailed format validations - handled by frontend
  body('call_type').optional(),
  body('call_billing_type')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '') {
        return true; // Allow empty values
      }

      // Handle both underscore and space formats for user convenience
      const normalizedValue = value.toLowerCase().replace(/\s+/g, '_');

      if (!['free_call', 'amc_call', 'per_call'].includes(normalizedValue)) {
        throw new Error('Please select a valid call type from the dropdown (Free Call, AMC Call, or Per Call)');
      }
      return true;
    })
    .customSanitizer((value) => {
      if (!value || value === '') {
        return value;
      }
      // Normalize the value to use underscores
      return value.toLowerCase().replace(/\s+/g, '_');
    }),
  body('priority').optional(),
  body('nature_of_issue_id')
    .optional()
    .custom((value) => {
      if (!value) return true;
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      throw new Error('Nature of issue ID must be a valid UUID');
    }),
  body('area_id')
    .optional()
    .custom((value) => {
      if (!value) return true;
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      throw new Error('Area ID must be a valid UUID');
    }),
  body('assigned_to')
    .optional()
    .custom((value) => {
      if (!value) return true;
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true;
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true;
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true;
      }
      throw new Error('Assigned to must be a valid UUID or integer');
    }),
  // Remove detailed format validations - handled by frontend
  body('scheduled_date').optional(),
  body('estimated_hours').optional(),
  body('is_billable').optional(),
  body('items').optional(),
  validate,
], createServiceCall);

/**
 * @swagger
 * /service-calls/{id}:
 *   put:
 *     summary: Update service call
 *     description: Update service call details including status changes that affect timer functionality
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Service call ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ServiceCallUpdateRequest'
 *           example:
 *             contact_number: "+91-9876543210"
 *             call_type: "AMC Call"
 *             status: "In Progress"
 *             subject: "Tally installation issue - Updated"
 *             description: "Customer facing issues with Tally installation on new system - Issue escalated"
 *             scheduled_date: "2024-01-16"
 *             products_issues_id: "456e7890-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Service call updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCall:
 *                           $ref: '#/components/schemas/ServiceCall'
 *                         timerUpdate:
 *                           type: object
 *                           properties:
 *                             timer_status:
 *                               type: string
 *                               example: "running"
 *                             status_changed:
 *                               type: boolean
 *                               example: true
 *                             previous_status:
 *                               type: string
 *                               example: "Open"
 *                             new_status:
 *                               type: string
 *                               example: "In Progress"
 *             example:
 *               success: true
 *               message: "Service call updated successfully"
 *               data:
 *                 serviceCall:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   service_number: "SER-001"
 *                   status: "In Progress"
 *                   timer_status: "running"
 *                   total_time_seconds: 3600
 *                 timerUpdate:
 *                   timer_status: "running"
 *                   status_changed: true
 *                   previous_status: "Open"
 *                   new_status: "In Progress"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Service call not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Service call not found"
 *               errors: {}
 *       422:
 *         description: Validation failed or invalid status transition
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Cannot update completed service call"
 *               errors:
 *                 status: "Service call is already completed and cannot be modified"
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.put('/:id', [
  requirePermission('service_calls.update'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  body('subject')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Subject must be less than 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Description must be less than 2000 characters'),
  body('customer_reported_issue')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Customer reported issue must be less than 2000 characters'),
  body('actual_issue_found')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Actual issue found must be less than 2000 characters'),
  body('solution_provided')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Solution provided must be less than 2000 characters'),
  body('status_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Status ID must be a valid UUID'),
  body('assigned_to')
    .optional()
    .custom((value) => {
      if (!value) return true; // Optional field
      // Allow both UUID and integer IDs for backward compatibility
      if (typeof value === 'string' && value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        return true; // Valid UUID
      }
      if (typeof value === 'string' && value.match(/^\d+$/)) {
        return true; // Valid integer ID as string
      }
      if (typeof value === 'number' && Number.isInteger(value)) {
        return true; // Valid integer ID
      }
      throw new Error('Assigned to must be a valid UUID or integer');
    }),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('call_billing_type')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '') {
        return true; // Allow empty values
      }
      if (!['free_call', 'amc_call', 'per_call'].includes(value)) {
        throw new Error('Call billing type must be one of: free_call, amc_call, per_call');
      }
      return true;
    }),
  body('scheduled_date')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid date'),
  body('started_at')
    .optional()
    .isISO8601()
    .withMessage('Started at must be a valid date'),
  body('completed_at')
    .optional()
    .isISO8601()
    .withMessage('Completed at must be a valid date'),
  body('actual_hours')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Actual hours must be a valid decimal number'),
  body('billable_hours')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Billable hours must be a valid decimal number'),
  body('hourly_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Hourly rate must be a valid decimal number'),
  body('service_charges')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Service charges must be a valid decimal number'),
  body('travel_charges')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Travel charges must be a valid decimal number'),
  body('customer_satisfaction')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Customer satisfaction must be between 1 and 5'),
  body('customer_feedback')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Customer feedback must be less than 1000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters'),
  body('follow_up_required')
    .optional()
    .isBoolean()
    .withMessage('Follow up required must be a boolean'),
  body('follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Follow up date must be a valid date'),
  body('follow_up_notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Follow up notes must be less than 1000 characters'),
  validate,
], updateServiceCall);

/**
 * @swagger
 * /service-calls/{id}:
 *   delete:
 *     summary: Delete service call
 *     description: Permanently delete a service call and all associated timer history. This action cannot be undone.
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Service call ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Service call deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         deletedServiceCall:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                               example: "123e4567-e89b-12d3-a456-************"
 *                             service_number:
 *                               type: string
 *                               example: "SER-001"
 *                             customer_name:
 *                               type: string
 *                               example: "ABC Enterprises Pvt Ltd"
 *                             total_time_seconds:
 *                               type: integer
 *                               example: 3600
 *                               description: "Total time spent on this service call before deletion"
 *             example:
 *               success: true
 *               message: "Service call deleted successfully"
 *               data:
 *                 deletedServiceCall:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   service_number: "SER-001"
 *                   customer_name: "ABC Enterprises Pvt Ltd"
 *                   total_time_seconds: 3600
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Service call not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Service call not found"
 *               errors: {}
 *       409:
 *         description: Cannot delete service call
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Cannot delete service call with active timer"
 *               errors:
 *                 timer_status: "Service call has an active timer. Please stop the timer before deleting."
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.delete('/:id', [
  requirePermission('service_calls.delete'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], deleteServiceCall);

/**
 * @swagger
 * /service-calls/test-email:
 *   post:
 *     summary: Test email functionality
 *     description: Send a test email to verify email service configuration and notification system
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - to
 *               - subject
 *             properties:
 *               to:
 *                 type: string
 *                 format: email
 *                 description: Recipient email address
 *                 example: "<EMAIL>"
 *               subject:
 *                 type: string
 *                 description: Email subject line
 *                 example: "TallyCRM Test Email"
 *               message:
 *                 type: string
 *                 description: Optional custom message
 *                 example: "This is a test email from TallyCRM system"
 *           example:
 *             to: "<EMAIL>"
 *             subject: "TallyCRM Test Email"
 *             message: "This is a test email from TallyCRM system"
 *     responses:
 *       200:
 *         description: Test email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         emailSent:
 *                           type: boolean
 *                           example: true
 *                         recipient:
 *                           type: string
 *                           example: "<EMAIL>"
 *                         messageId:
 *                           type: string
 *                           example: "msg_123456789"
 *                         timestamp:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-15T10:30:00Z"
 *             example:
 *               success: true
 *               message: "Test email sent successfully"
 *               data:
 *                 emailSent: true
 *                 recipient: "<EMAIL>"
 *                 messageId: "msg_123456789"
 *                 timestamp: "2024-01-15T10:30:00Z"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       422:
 *         description: Email validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Email validation failed"
 *               errors:
 *                 to: "Invalid email address format"
 *                 subject: "Subject is required"
 *       500:
 *         description: Email service error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Failed to send test email"
 *               errors:
 *                 email_service: "SMTP server connection failed"
 */
router.post('/test-email', [
  requirePermission('service_calls.create'),
], testEmail);

/**
 * @swagger
 * /service-calls/email-logs:
 *   get:
 *     summary: Get email logs
 *     description: Retrieve email sending logs with success/failure details
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Number of log entries to retrieve
 *     responses:
 *       200:
 *         description: Email logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       timestamp:
 *                         type: string
 *                       type:
 *                         type: string
 *                       recipient:
 *                         type: string
 *                       subject:
 *                         type: string
 *                       success:
 *                         type: boolean
 *                       details:
 *                         type: object
 */
router.get('/email-logs', [
  requirePermission('service_calls.read'),
], getEmailLogs);

/**
 * @swagger
 * /service-calls/email-logs:
 *   delete:
 *     summary: Clear email logs
 *     description: Clear all email sending logs
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email logs cleared successfully
 */
router.delete('/email-logs', [
  requirePermission('service_calls.create'),
], clearEmailLogs);

/**
 * @swagger
 * /service-calls/email-send-status:
 *   get:
 *     summary: Get email send status log
 *     description: Retrieve human-readable email send status log
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: lines
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of log entries to retrieve
 *     responses:
 *       200:
 *         description: Email send status log retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     log:
 *                       type: string
 *                     entries:
 *                       type: array
 *                       items:
 *                         type: string
 *                     total:
 *                       type: integer
 */
router.get('/email-send-status', [
  requirePermission('service_calls.read'),
], getEmailSendStatusLog);

/**
 * @swagger
 * /service-calls/email-statistics:
 *   get:
 *     summary: Get email send statistics
 *     description: Retrieve email sending statistics and success rates
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Email statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     successful:
 *                       type: integer
 *                     failed:
 *                       type: integer
 *                     successRate:
 *                       type: string
 *                     byType:
 *                       type: object
 */
router.get('/email-statistics', [
  requirePermission('service_calls.read'),
], getEmailSendStatistics);

/**
 * @route   GET /api/service-calls/:id/time-tracking
 * @desc    Get time tracking summary for a service call
 * @access  Private (requires service_calls.read permission)
 */
router.get('/:id/time-tracking', [
  requirePermission('service_calls.read'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], getTimeTrackingSummary);

/**
 * @swagger
 * /service-calls/{id}/timer-status:
 *   get:
 *     summary: Get timer status for a service call
 *     description: Retrieve real-time timer status including current state, accumulated time, and session information
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Service call ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Timer status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/TimerStatus'
 *             example:
 *               success: true
 *               message: "Timer status retrieved successfully"
 *               data:
 *                 service_call_id: "123e4567-e89b-12d3-a456-************"
 *                 timer_status: "running"
 *                 total_time_seconds: 3600
 *                 formatted_time: "1 hour 0 minutes 0 seconds"
 *                 current_session_start: "2024-01-15T10:30:00Z"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Service call not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Service call not found"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/:id/timer-status', [
  requirePermission('service_calls.read'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], getTimerStatus);

/**
 * @swagger
 * /service-calls/{id}/timer-history:
 *   get:
 *     summary: Get timer history for a service call
 *     description: Retrieve comprehensive timer history including all sessions, events, and detailed tracking information
 *     tags: [Service Calls]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Service call ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Timer history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         service_call_id:
 *                           type: string
 *                           format: uuid
 *                         total_time_seconds:
 *                           type: integer
 *                           example: 7200
 *                         formatted_total_time:
 *                           type: string
 *                           example: "2 hours 0 minutes 0 seconds"
 *                         session_count:
 *                           type: integer
 *                           example: 3
 *                         history:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               action:
 *                                 type: string
 *                                 enum: [started, paused, resumed, completed]
 *                                 example: "started"
 *                               timestamp:
 *                                 type: string
 *                                 format: date-time
 *                                 example: "2024-01-15T10:30:00Z"
 *                               duration:
 *                                 type: integer
 *                                 example: 1800
 *                                 description: "Duration in seconds for this session"
 *                               cumulative_total:
 *                                 type: integer
 *                                 example: 1800
 *                                 description: "Total accumulated time up to this point"
 *                               user_id:
 *                                 type: string
 *                                 format: uuid
 *                               user_name:
 *                                 type: string
 *                                 example: "John Doe"
 *             example:
 *               success: true
 *               message: "Timer history retrieved successfully"
 *               data:
 *                 service_call_id: "123e4567-e89b-12d3-a456-************"
 *                 total_time_seconds: 7200
 *                 formatted_total_time: "2 hours 0 minutes 0 seconds"
 *                 session_count: 3
 *                 history:
 *                   - action: "started"
 *                     timestamp: "2024-01-15T10:30:00Z"
 *                     duration: 1800
 *                     cumulative_total: 1800
 *                     user_name: "John Doe"
 *                   - action: "paused"
 *                     timestamp: "2024-01-15T11:00:00Z"
 *                     duration: 0
 *                     cumulative_total: 1800
 *                     user_name: "John Doe"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Service call not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Service call not found"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/:id/timer-history', [
  requirePermission('service_calls.read'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], getServiceCallTimerHistory);

export default router;
