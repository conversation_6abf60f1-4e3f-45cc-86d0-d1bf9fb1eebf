import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { leadAPI } from '../../services/api';
import {
  FaEdit,
  FaTrash,
  FaArrowLeft,
  FaUser,
  FaPhone,
  FaRupeeSign,
  FaCalendar,
  FaUserTie,
  FaBuilding,
  FaClipboardList,
  FaStickyNote,
  FaExchangeAlt,
  FaHistory,
  FaUserPlus,
  FaCheckCircle,
  FaExclamationTriangle
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';
import ContactHistoryModal from '../../components/leads/ContactHistoryModal';
import ConvertToCustomerModal from '../../components/leads/ConvertToCustomerModal';

const LeadDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [lead, setLead] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showContactHistory, setShowContactHistory] = useState(false);
  const [showConvertModal, setShowConvertModal] = useState(false);

  // Fetch lead data
  useEffect(() => {
    if (id) {
      fetchLeadData();
    }
  }, [id]);

  const fetchLeadData = async () => {
    try {
      setLoading(true);
      const response = await leadAPI.getById(id);
      
      if (response.data?.success) {
        setLead(response.data.data.lead);
      } else {
        toast.error('Lead not found');
        navigate('/leads');
      }
    } catch (error) {
      console.error('Error fetching lead:', error);
      toast.error('Failed to load lead data');
      navigate('/leads');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this lead? This action cannot be undone.')) {
      try {
        const response = await leadAPI.delete(id);
        if (response.data?.success) {
          toast.success('Lead deleted successfully');
          navigate('/leads');
        } else {
          toast.error(response.data?.message || 'Failed to delete lead');
        }
      } catch (error) {
        console.error('Error deleting lead:', error);

        // Handle specific error cases
        if (error.response?.data?.errors?.general) {
          toast.error(error.response.data.errors.general);
        } else if (error.response?.data?.message) {
          toast.error(error.response.data.message);
        } else if (error.response?.status === 400) {
          toast.error('This lead cannot be deleted. It may have been converted to a customer.');
        } else {
          toast.error('Failed to delete lead. Please try again.');
        }
      }
    }
  };

  const handleContactAdded = () => {
    // Refresh lead data to update follow-up date if it was changed
    fetchLeadData();
  };

  const handleConversionSuccess = (conversionData) => {
    // Update lead state to reflect conversion
    setLead(prev => ({
      ...prev,
      converted_to_customer_id: conversionData.customer.id,
      conversion_date: new Date().toISOString(),
    }));
    toast.success(`Lead converted to customer successfully! Customer ID: ${conversionData.customer.id}`);
  };

  const getStatusBadge = (status) => {
    // Handle both object and string status values
    let statusValue = 'New';
    if (typeof status === 'object' && status !== null) {
      statusValue = status.name || status.status || 'New';
    } else if (typeof status === 'string') {
      statusValue = status;
    }
    const statusString = String(statusValue).toLowerCase();

    const badgeConfig = {
      'new': { bg: 'bg-success-100', text: 'text-success-800', icon: '🆕' },
      'follow up': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '📞' },
      'call not attended': { bg: 'bg-secondary-100', text: 'text-secondary-800', icon: '📵' },
      'interested': { bg: 'bg-theme-100', text: 'text-theme-800', icon: '👍' },
      'not interested': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '👎' },
      'converted': { bg: 'bg-success-200', text: 'text-success-900', icon: '✅' },
      'lost': { bg: 'bg-danger-200', text: 'text-danger-900', icon: '❌' }
    };

    const config = badgeConfig[statusString] || badgeConfig.new;
    const displayText = statusString.replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-sm font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const formatAmount = (amount) => {
    if (!amount) return 'Not specified';
    return `₹${parseFloat(amount).toLocaleString()}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatPhoneNumber = (countryCode, phoneNumber) => {
    if (!phoneNumber) return 'Not provided';
    const code = countryCode || '+91';
    return `${code} ${phoneNumber}`;
  };

  const isConverted = () => {
    const statusValue = typeof lead?.status === 'object' ? JSON.stringify(lead.status) : String(lead?.status || '');
    return lead?.converted_to_customer_id || statusValue.toLowerCase() === 'converted';
  };

  const isFollowUpOverdue = () => {
    if (!lead?.follow_up_date) return false;
    const followUpDate = new Date(lead.follow_up_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return followUpDate < today;
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (!lead) {
    return (
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardBody className="p-8 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Lead not found</h3>
            <p className="text-gray-500 mb-4">The lead you're looking for doesn't exist or has been deleted.</p>
            <Link
              to="/leads"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-theme-600 hover:bg-theme-700"
            >
              <FaArrowLeft className="mr-2 h-4 w-4" />
              Back to Leads
            </Link>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <button
              onClick={() => navigate('/leads')}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <FaArrowLeft className="h-5 w-5" />
            </button>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              {lead.customer_name || 'Unnamed Lead'}
            </h1>
          </div>
          <div className="ml-12">
            <p className="text-gray-600 mb-3">
              {typeof lead.products === 'object' ? 'Complex Products' : (lead.products || 'No products specified')}
            </p>
            <div className="flex flex-wrap items-center gap-3">
              {getStatusBadge(lead.status)}
              {isConverted() && (
                <span className="inline-flex items-center px-3 py-1.5 text-sm font-bold rounded-full bg-blue-100 text-blue-800">
                  <FaExchangeAlt className="mr-1 h-3 w-3" />
                  Converted
                </span>
              )}
              {isFollowUpOverdue() && (
                <span className="inline-flex items-center px-3 py-1.5 text-sm font-bold rounded-full bg-red-100 text-red-800">
                  <FaCalendar className="mr-1 h-3 w-3" />
                  Follow-up Overdue
                </span>
              )}
              <span className="text-sm text-gray-600 flex items-center">
                <FaCalendar className="mr-1 h-3 w-3" />
                Created {formatDate(lead.created_at)}
              </span>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Link
            to={`/leads/${lead.id}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
          >
            <FaEdit className="mr-2 h-4 w-4" />
            Edit
          </Link>

          <button
            onClick={() => setShowContactHistory(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
          >
            <FaHistory className="mr-2 h-4 w-4" />
            Contact History
          </button>

          {!isConverted() && (
            <button
              onClick={() => setShowConvertModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700"
            >
              <FaUserPlus className="mr-2 h-4 w-4" />
              Convert to Customer
            </button>
          )}

          <button
            onClick={handleDelete}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700"
            disabled={isConverted()}
          >
            <FaTrash className="mr-2 h-4 w-4" />
            Delete
          </button>
        </div>
      </div>

      {/* Lead Information Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaUser className="mr-2 h-5 w-5 text-theme-600" />
              Basic Information
            </h3>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Customer Name:</label>
                  <p className="text-gray-900">{lead.customer_name || 'Not specified'}</p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Products/Services:</label>
                <p className="text-gray-900">{typeof lead.products === 'object' ? 'Complex Products' : (lead.products || 'Not specified')}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-700">Amount:</label>
                <p className="text-gray-900 font-semibold">{formatAmount(lead.amount)}</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaPhone className="mr-2 h-5 w-5 text-theme-600" />
              Contact Information
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Contact Number:</label>
                <p className="text-gray-900">{formatPhoneNumber(lead.country_code, lead.contact_no)}</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Management Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaClipboardList className="mr-2 h-5 w-5 text-theme-600" />
              Lead Management
            </h3>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Status:</label>
                  <div className="mt-1">{getStatusBadge(lead.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Assigned Executive:</label>
                  <p className="text-gray-900">{typeof lead.executive === 'object' ? 'Complex Executive' : (lead.executive || 'Not assigned')}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Contacted Executive:</label>
                  <p className="text-gray-900">{typeof lead.contacted_executive === 'object' ? 'Complex Executive' : (lead.contacted_executive || 'Not specified')}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Follow Up Date:</label>
                <p className={`text-gray-900 ${isFollowUpOverdue() ? 'text-red-600 font-semibold' : ''}`}>
                  {formatDate(lead.follow_up_date)}
                  {isFollowUpOverdue() && ' (Overdue)'}
                </p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Reference Information */}
        {(lead.ref_name || lead.ref_contact_no || lead.ref_amount) && (
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaUserTie className="mr-2 h-5 w-5 text-theme-600" />
                Reference Information
              </h3>

              <div className="space-y-4">
                {lead.ref_name && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Reference Name:</label>
                    <p className="text-gray-900">{lead.ref_name}</p>
                  </div>
                )}

                {lead.ref_contact_no && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Reference Contact:</label>
                    <p className="text-gray-900">{formatPhoneNumber(lead.ref_country_code, lead.ref_contact_no)}</p>
                  </div>
                )}

                {lead.ref_amount && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Reference Amount:</label>
                    <p className="text-gray-900 font-semibold">{formatAmount(lead.ref_amount)}</p>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        )}
      </div>

      {/* Remarks */}
      {lead.remarks && (
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaStickyNote className="mr-2 h-5 w-5 text-theme-600" />
              Remarks
            </h3>

            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-900 whitespace-pre-wrap">{lead.remarks}</p>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Conversion Information */}
      {isConverted() && (
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaExchangeAlt className="mr-2 h-5 w-5 text-green-600" />
              Conversion Information
            </h3>

            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <FaExchangeAlt className="h-4 w-4 text-green-600" />
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">
                    This lead has been successfully converted to a customer.
                  </p>
                  {lead.conversion_date && (
                    <p className="text-sm text-green-600">
                      Converted on {formatDate(lead.conversion_date)}
                    </p>
                  )}
                  {lead.converted_to_customer_id && (
                    <Link
                      to={`/customers/${lead.converted_to_customer_id}`}
                      className="text-sm text-green-600 hover:text-green-800 underline"
                    >
                      View Customer Profile →
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Audit Information */}
      <Card>
        <CardBody className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FaClipboardList className="mr-2 h-5 w-5 text-theme-600" />
            Audit Information
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <label className="font-medium text-gray-700">Created:</label>
              <p className="text-gray-900">{formatDate(lead.created_at)}</p>
              {lead.creator && (
                <p className="text-gray-600">
                  by {lead.creator.first_name} {lead.creator.last_name}
                </p>
              )}
            </div>

            <div>
              <label className="font-medium text-gray-700">Last Updated:</label>
              <p className="text-gray-900">{formatDate(lead.updated_at)}</p>
              {lead.updater && (
                <p className="text-gray-600">
                  by {lead.updater.first_name} {lead.updater.last_name}
                </p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Modals */}
      <ContactHistoryModal
        isOpen={showContactHistory}
        onClose={() => setShowContactHistory(false)}
        leadId={lead.id}
        onContactAdded={handleContactAdded}
      />

      <ConvertToCustomerModal
        isOpen={showConvertModal}
        onClose={() => setShowConvertModal(false)}
        lead={lead}
        onConversionSuccess={handleConversionSuccess}
      />
    </div>
  );
};

export default LeadDetails;
