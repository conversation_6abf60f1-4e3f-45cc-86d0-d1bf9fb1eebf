# Service Call Timer "On Hold" Functionality Fixes

## Problem Summary

The service call timer had critical issues with "On Hold" status transitions:

1. **Timer continued running during "On Hold"** - Backend timer kept calculating elapsed time even when status was "On Hold"
2. **Lost accumulated time on resume** - When resuming from hold, the timer calculation was incorrect
3. **Incorrect time display during hold** - Frontend showed 00:00:00 during hold instead of accumulated time
4. **Backend-Frontend mismatch** - Backend continued running while frontend showed paused state

## Root Cause Analysis

### Backend Issues:
1. **`pauseTimer` function** - Calculated session duration but didn't update the service call's `total_time_seconds` field with accumulated time
2. **`startTimer` function** - Started new sessions without considering previously accumulated time from paused sessions
3. **Missing pause state detection** - No way to detect if a timer was currently paused

### Frontend Issues:
1. **Timer display logic** - Only checked for "In Progress" status, ignored "On Hold" status
2. **Missing accumulated time calculation** - Didn't use the real-time accumulated time from backend
3. **No visual indicators for paused state** - No UI feedback for paused timers

## Solution Implementation

### Backend Changes

#### 1. Enhanced `pauseTimer` Function (`backend/src/services/timeTrackingService.js`)
```javascript
static async pauseTimer(serviceCall, timeEntry, timeHistory) {
  // ... existing logic ...

  // Calculate total time including this paused session
  const totalTimeSeconds = this.calculateTotalTimeInSeconds(timeHistory, timeEntry);
  const totalTimeMinutes = Math.round(totalTimeSeconds / 60);

  // Update service call with accumulated time up to this pause
  await serviceCall.update({
    total_time_minutes: totalTimeMinutes,
    total_time_seconds: totalTimeSeconds
  });

  timeHistory.push(timeEntry);
}
```

#### 2. Fixed `startTimer` Function for Resume Logic
```javascript
static async startTimer(serviceCall, timeEntry, timeHistory) {
  timeEntry.action = 'timer_start';
  timeEntry.start_time = new Date();

  // Check if this is a resume from pause or first time start
  const isResumingFromPause = this.isTimerPaused(timeHistory);

  if (isResumingFromPause) {
    // When resuming from pause, adjust started_at to account for accumulated time
    const accumulatedSeconds = serviceCall.total_time_seconds || 0;
    const adjustedStartTime = new Date(timeEntry.start_time.getTime() - (accumulatedSeconds * 1000));
    await serviceCall.update({ started_at: adjustedStartTime });
  } else {
    // First time starting - set started_at to current time
    await serviceCall.update({ started_at: timeEntry.start_time });
  }

  timeHistory.push(timeEntry);
}
```

#### 3. Added Helper Functions
- **`isTimerPaused(timeHistory)`** - Detects if timer is currently paused
- **`getCurrentAccumulatedTime(serviceCall)`** - Gets real-time accumulated time including active sessions

#### 4. Enhanced `getTimeTrackingSummary` Function
- Added `current_accumulated_time` field for real-time time tracking
- Added `is_timer_paused` flag for frontend state detection
- Added `current_time_formatted` for display purposes

### Frontend Changes

#### 1. Fixed Timer Display Logic (`frontend/src/pages/services/ServiceList.jsx`)
```javascript
const getElapsedTime = () => {
  // For "On Hold" status, always show stored accumulated time (should not increment)
  if (isOnHold) {
    return totalSeconds; // Static accumulated time from completed sessions
  }

  // For "In Progress" status, calculate real-time elapsed time
  if (isInProgress) {
    if (service.startedAt) {
      const startTime = new Date(service.startedAt);
      const totalElapsedSinceStart = Math.floor((currentTime - startTime) / 1000);

      // Backend adjusts started_at to account for accumulated time, so this gives total time
      return Math.max(0, totalElapsedSinceStart);
    }

    return currentAccumulatedTime || totalSeconds || 0;
  }

  return totalSeconds;
};

// Show paused time for "On Hold" status (even if 0)
if (isOnHold) {
  return (
    <div className="text-xs text-orange-600 flex items-center">
      <FaPause className="mr-1 h-3 w-3" />
      <span className="font-mono">{formatTimeDisplay(displayTime)}</span>
      <span className="ml-1 text-orange-500">paused</span>
    </div>
  );
}
```

#### 2. Enhanced Service Details Timer (`frontend/src/pages/services/ServiceDetails.jsx`)
- Added "On Hold" status detection and display
- Added visual indicators for paused state (orange color scheme)
- Added "Resume" button for services on hold
- Updated status mapping to include 'on-hold': 'ON_HOLD'

#### 3. Added Status Action Buttons
- **"Hold" button** - Appears when service is "In Progress"
- **"Resume" button** - Appears when service is "On Hold"
- Proper color coding (orange for hold, blue for resume)

## Expected Behavior After Fixes

### 1. Putting Service "On Hold"
- Timer pauses immediately
- Accumulated time from current session is saved to database
- Frontend shows paused time with orange pause icon
- "Resume" button becomes available

### 2. Resuming from "On Hold"
- Timer continues from previously accumulated time
- No time is lost from the paused session
- Frontend shows running timer with blue play icon
- Real-time timer updates every second

### 3. Time Display During "On Hold"
- Shows accumulated time from all completed sessions
- Time remains static (doesn't increment)
- Orange color indicates paused state
- Format: "HH:MM:SS paused"

### 4. Time Calculation Accuracy
- Only counts active "In Progress" time
- Excludes time spent "On Hold"
- Maintains precision to seconds level
- Preserves time across multiple pause/resume cycles

## Testing Scenarios

### Scenario 1: Basic Pause/Resume
1. Start service (status: "In Progress") - Timer starts at 00:00:00
2. Wait 5 minutes - Timer shows 00:05:00
3. Put on hold (status: "On Hold") - Timer pauses at 00:05:00
4. Wait 2 minutes - Timer still shows 00:05:00 (paused)
5. Resume (status: "In Progress") - Timer continues from 00:05:00
6. Wait 3 minutes - Timer shows 00:08:00

### Scenario 2: Multiple Pause/Resume Cycles
1. Work for 10 minutes, pause for 5 minutes, work for 15 minutes
2. Expected total active time: 25 minutes
3. Expected total elapsed time: 30 minutes
4. Timer should show 00:25:00 of active work time

### Scenario 3: Complete from "On Hold"
1. Service is paused at 00:15:00
2. Complete directly from "On Hold" status
3. Final time should be 00:15:00 (no additional time added)

## Files Modified

### Backend Files:
- `backend/src/services/timeTrackingService.js` - Enhanced timer logic
- Timer calculation functions updated
- Added pause state detection

### Frontend Files:
- `frontend/src/pages/services/ServiceList.jsx` - Updated timer display
- `frontend/src/pages/services/ServiceDetails.jsx` - Added hold/resume functionality
- Enhanced status detection and action buttons

## Benefits

1. **Accurate Time Tracking** - Only counts actual work time, excludes hold periods
2. **Better User Experience** - Clear visual feedback for timer states
3. **Data Integrity** - No time loss during pause/resume cycles
4. **Improved Workflow** - Proper hold/resume functionality for service management
5. **Real-time Updates** - Live timer display with second-level precision
