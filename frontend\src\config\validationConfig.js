/**
 * Validation Configuration
 * Defines which validations should be handled on frontend vs backend
 */

/**
 * Frontend Validation Rules
 * These validations provide immediate feedback to users
 */
export const frontendValidations = {
  // Service Call Form
  serviceCall: {
    customerSerialNumber: {
      type: 'customerSerial',
      required: true,
      label: 'Customer Serial Number'
    },
    subject: {
      type: 'text',
      required: false,
      max: 200,
      label: 'Subject'
    },
    description: {
      type: 'text',
      required: false,
      max: 2000,
      label: 'Description'
    },
    serviceNumber: {
      type: 'serviceNumber',
      required: false,
      label: 'Service Number'
    },
    estimatedHours: {
      type: 'number',
      required: false,
      min: 0,
      max: 24,
      label: 'Estimated Hours'
    },
    scheduledDate: {
      type: 'date',
      required: false,
      label: 'Scheduled Date'
    },
    contactPersonId: {
      type: 'text',
      required: false,
      label: 'Contact Person'
    },
    callType: {
      type: 'select',
      required: false,
      options: ['online', 'onsite', 'phone', 'email'],
      label: 'Call Type'
    },
    priority: {
      type: 'select',
      required: false,
      options: ['low', 'medium', 'high', 'critical'],
      label: 'Priority'
    }
  },

  // Customer Form
  customer: {
    companyName: {
      type: 'text',
      required: true,
      min: 2,
      max: 200,
      label: 'Company Name'
    },
    tallySerialNo: {
      type: 'customerSerial',
      required: true,
      label: 'Tally Serial Number'
    },
    adminEmail: {
      type: 'email',
      required: true,
      label: 'Admin Email'
    },
    mdContactPerson: {
      type: 'text',
      required: true,
      min: 2,
      max: 100,
      label: 'MD Contact Person'
    },
    mdPhoneNo: {
      type: 'phone',
      required: true,
      label: 'MD Phone Number'
    },
    mdEmail: {
      type: 'email',
      required: false,
      label: 'MD Email'
    },
    // Professional Contacts - All Optional
    officeEmail: {
      type: 'email',
      required: false,
      label: 'Office Email (Optional)'
    },
    auditorName: {
      type: 'text',
      required: false,
      label: 'Auditor Name (Optional)'
    },
    auditorNo: {
      type: 'phone',
      required: false,
      label: 'Auditor Number (Optional)'
    },
    auditorEmail: {
      type: 'email',
      required: false,
      label: 'Auditor Email (Optional)'
    },
    taxConsultantName: {
      type: 'text',
      required: false,
      label: 'Tax Consultant Name (Optional)'
    },
    taxConsultantNo: {
      type: 'phone',
      required: false,
      label: 'Tax Consultant Number (Optional)'
    },
    taxConsultantEmail: {
      type: 'email',
      required: false,
      label: 'Tax Consultant Email (Optional)'
    },
    itName: {
      type: 'text',
      required: false,
      label: 'IT Name (Optional)'
    },
    itNo: {
      type: 'phone',
      required: false,
      label: 'IT Number (Optional)'
    },
    itEmail: {
      type: 'email',
      required: false,
      label: 'IT Email (Optional)'
    },
    // Business Information - Now Optional
    area: {
      type: 'text',
      required: false,
      label: 'Area (Optional)'
    },
    executiveName: {
      type: 'text',
      required: false,
      label: 'Executive Name (Optional)'
    },
    gstNo: {
      type: 'gst',
      required: false,
      label: 'GST Number'
    },
    panNumber: {
      type: 'pan',
      required: false,
      label: 'PAN Number'
    },
    website: {
      type: 'url',
      required: false,
      label: 'Website'
    },
    creditLimit: {
      type: 'number',
      required: false,
      min: 0,
      label: 'Credit Limit'
    },
    creditDays: {
      type: 'number',
      required: false,
      min: 0,
      max: 365,
      label: 'Credit Days'
    },
    annualRevenue: {
      type: 'number',
      required: false,
      min: 0,
      label: 'Annual Revenue'
    },
    employeeCount: {
      type: 'number',
      required: false,
      min: 1,
      label: 'Employee Count'
    }
  },

  // Employee Form
  employee: {
    firstName: {
      type: 'text',
      required: true,
      min: 2,
      max: 50,
      label: 'First Name'
    },
    lastName: {
      type: 'text',
      required: true,
      min: 2,
      max: 50,
      label: 'Last Name'
    },
    email: {
      type: 'email',
      required: true,
      label: 'Email'
    },
    phone: {
      type: 'phone',
      required: true,
      label: 'Phone Number'
    },
    employeeCode: {
      type: 'text',
      required: true,
      min: 2,
      max: 20,
      pattern: /^[A-Z0-9]+$/,
      label: 'Employee Code'
    },
    dateOfBirth: {
      type: 'date',
      required: false,
      label: 'Date of Birth'
    },
    dateOfJoining: {
      type: 'date',
      required: false,
      label: 'Date of Joining'
    }
  }
};

/**
 * Backend Validation Rules
 * These are critical validations that must be enforced on the server
 */
export const backendValidations = {
  // Data integrity validations
  dataIntegrity: [
    'unique_constraints',
    'foreign_key_constraints',
    'database_constraints'
  ],

  // Business logic validations
  businessLogic: [
    'customer_exists',
    'amc_contract_validity',
    'service_call_limits',
    'permission_checks',
    'tenant_isolation',
    'status_transitions'
  ],

  // Security validations
  security: [
    'authentication',
    'authorization',
    'input_sanitization',
    'sql_injection_prevention',
    'xss_prevention'
  ],

  // Critical data validations
  critical: [
    'customer_serial_uniqueness',
    'email_format_final_check',
    'financial_calculations',
    'audit_trail_integrity'
  ]
};

/**
 * Validation Strategy Configuration
 */
export const validationStrategy = {
  // Move these validations from backend to frontend
  moveToFrontend: [
    'field_length_validations',
    'format_validations',
    'required_field_checks',
    'data_type_validations',
    'regex_pattern_validations',
    'range_validations'
  ],

  // Keep these validations on backend
  keepOnBackend: [
    'uniqueness_constraints',
    'business_rule_validations',
    'security_validations',
    'database_integrity_checks',
    'cross_table_validations',
    'permission_validations'
  ],

  // Dual validation (both frontend and backend)
  dualValidation: [
    'email_format',
    'required_fields',
    'data_existence_checks'
  ]
};

/**
 * Form Field Configurations
 */
export const fieldConfigurations = {
  // Service Call Form Fields
  serviceCallFields: [
    'customerSerialNumber',
    'subject',
    'description',
    'serviceNumber',
    'contactPersonId',
    'callType',
    'priority',
    'scheduledDate',
    'estimatedHours'
  ],

  // Customer Form Fields
  customerFields: [
    'companyName',
    'tallySerialNo',
    'adminEmail',
    'mdContactPerson',
    'mdPhoneNo',
    'mdEmail',
    // Professional Contacts - All Optional
    'officeEmail',
    'auditorName',
    'auditorNo',
    'auditorEmail',
    'taxConsultantName',
    'taxConsultantNo',
    'taxConsultantEmail',
    'itName',
    'itNo',
    'itEmail',
    // Business Information - Now Optional
    'area',
    'executiveName',
    'gstNo',
    'panNumber',
    'website',
    'creditLimit',
    'creditDays',
    'annualRevenue',
    'employeeCount'
  ],

  // Employee Form Fields
  employeeFields: [
    'firstName',
    'lastName',
    'email',
    'phone',
    'employeeCode',
    'dateOfBirth',
    'dateOfJoining'
  ]
};

/**
 * Error Message Templates
 */
export const errorMessages = {
  required: (fieldName) => `${fieldName} is required to continue`,
  minLength: (fieldName, min) => `${fieldName} must be at least ${min} characters long`,
  maxLength: (fieldName, max) => `${fieldName} cannot exceed ${max} characters`,
  invalidFormat: (fieldName) => `Please enter a valid ${fieldName} in the correct format`,
  invalidEmail: 'Please enter a valid email address (e.g., <EMAIL>)',
  invalidPhone: 'Please enter a valid 10-digit phone number without spaces or special characters',
  invalidGST: 'Please enter a valid 15-character GST number (e.g., 22AAAAA0000A1Z5)',
  invalidPAN: 'Please enter a valid 10-character PAN number (e.g., **********)',
  invalidCustomerSerial: 'Customer serial number can only contain uppercase letters and numbers (e.g., CUST001)',
  invalidServiceNumber: 'Service number must follow the format SER-001 or SE-003S',
  invalidURL: 'Please enter a valid website URL starting with http:// or https://',
  invalidDate: 'Please enter a valid date in the format DD/MM/YYYY',
  invalidNumber: (fieldName) => `${fieldName} must be a valid number`,
  numberRange: (fieldName, min, max) => `${fieldName} must be between ${min} and ${max}`,
  customerNotFound: (serialNumber) => `No customer found with serial number "${serialNumber}". Please check the number and try again.`,
  duplicateEntry: (fieldName) => `This ${fieldName} already exists in the system. Please use a different value.`,
  serverError: 'Unable to process your request at the moment. Please try again in a few seconds.',
  networkError: 'Connection problem detected. Please check your internet connection and try again.',

  // Field-specific enhanced messages
  companyNameRequired: 'Company name is required for business customers',
  contactPersonRequired: 'Please provide at least one contact person',
  addressRequired: 'Complete address information is required',
  phoneRequired: 'Phone number is required for communication',
  emailRequired: 'Email address is required for notifications and updates',

  // Service call specific messages
  customerRequired: 'Please select a customer before creating the service call',
  callTypeRequired: 'Please specify the type of service call',
  callBillingTypeInvalid: 'Please select a valid call type from the dropdown (Free Call, AMC Call, or Per Call)',
  descriptionRequired: 'Please provide a description of the issue or service needed',
  statusRequired: 'Please select a status for this service call',

  // Date validation messages
  futureDateRequired: 'Please select a future date for scheduling',
  pastDateNotAllowed: 'Past dates are not allowed for this field',
  invalidDateRange: 'End date must be after the start date',

  // File upload messages
  fileRequired: 'Please select a file to upload',
  fileSizeExceeded: 'File size must be less than 10MB',
  invalidFileFormat: 'Only PDF, DOC, DOCX, and image files are allowed'
};

/**
 * User-Friendly Error Messages for Common API Errors
 */
export const userFriendlyMessages = {
  // Service Call Errors
  serviceCallLocked: 'This service call cannot be modified because it has already been completed.',
  serviceCallNotFound: 'The requested service call could not be found. It may have been deleted or you may not have permission to view it.',
  invalidStatusChange: 'The status change you requested is not allowed. Please check the current status and try again.',

  // Authentication Errors
  sessionExpired: 'Your session has expired. Please log in again to continue.',
  invalidCredentials: 'The email or password you entered is incorrect. Please check your credentials and try again.',
  accessDenied: 'You do not have permission to perform this action. Please contact your administrator if you believe this is an error.',

  // Form Validation Errors
  validationFailed: 'Please review the form and correct the highlighted errors before submitting.',
  requiredFieldsMissing: 'Some required fields are empty. Please fill in all required information to continue.',
  invalidFormData: 'Some information in the form is not valid. Please check the highlighted fields and correct any errors.',

  // Data Errors
  recordNotFound: 'The requested record could not be found. It may have been deleted or moved.',
  duplicateRecord: 'A record with this information already exists. Please check for duplicates.',

  // File Upload Errors
  fileUploadFailed: 'The file could not be uploaded. Please check the file size and format, then try again.',
  fileTooLarge: 'The file you selected is too large. Please choose a smaller file.',
  invalidFileType: 'The file type you selected is not supported. Please choose a different file.',

  // Network Errors
  connectionError: 'Unable to connect to the server. Please check your internet connection and try again.',
  serverError: 'The server encountered an error while processing your request. Please try again in a few moments.',
  timeoutError: 'The request took too long to complete. Please try again.',

  // Generic Fallbacks
  unknownError: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
  operationFailed: 'The operation could not be completed. Please try again.',

  // Deletion Restriction Messages
  customerDeletionBlocked: 'Cannot delete customer because they have active service calls. Please complete or transfer all services before deletion.',
  leadDeletionBlocked: 'Cannot delete this lead because it has been converted to a customer. Please manage the customer record instead of deleting the original lead.',
  customerDeletionWithDetails: (serviceCallsCount, salesCount, quotationsCount) => {
    const details = [];
    if (serviceCallsCount > 0) details.push(`${serviceCallsCount} service call${serviceCallsCount > 1 ? 's' : ''}`);
    if (salesCount > 0) details.push(`${salesCount} sales record${salesCount > 1 ? 's' : ''}`);
    if (quotationsCount > 0) details.push(`${quotationsCount} quotation${quotationsCount > 1 ? 's' : ''}`);

    return `Cannot delete customer because they have ${details.join(', ')}. Please complete or transfer all related records before deletion.`;
  },

  // Success Messages
  operationSuccess: 'Operation completed successfully.',
  saveSuccess: 'Your changes have been saved successfully.',
  deleteSuccess: 'The record has been deleted successfully.',
  updateSuccess: 'The record has been updated successfully.',
  createSuccess: 'The record has been created successfully.'
};

/**
 * Convert technical error messages to user-friendly ones
 */
export const getUserFriendlyMessage = (error, fallbackMessage = null) => {
  if (!error) return fallbackMessage || userFriendlyMessages.unknownError;

  // If error is a string, check for known patterns
  const errorMessage = typeof error === 'string' ? error : error.message || '';
  const lowerMessage = errorMessage.toLowerCase();

  // Service call specific errors
  if (lowerMessage.includes('service call is already completed and locked') ||
      lowerMessage.includes('cannot update status') && lowerMessage.includes('completed') ||
      lowerMessage.includes('already completed and locked')) {
    return userFriendlyMessages.serviceCallLocked;
  }

  if (lowerMessage.includes('service call not found')) {
    return userFriendlyMessages.serviceCallNotFound;
  }

  // Call billing type validation error
  if (lowerMessage.includes('call billing type must be one of') ||
      lowerMessage.includes('please select a valid call type from the dropdown')) {
    return errorMessages.callBillingTypeInvalid;
  }

  // Authentication errors
  if (lowerMessage.includes('session expired') || lowerMessage.includes('token expired')) {
    return userFriendlyMessages.sessionExpired;
  }

  if (lowerMessage.includes('invalid credentials') || lowerMessage.includes('authentication failed')) {
    return userFriendlyMessages.invalidCredentials;
  }

  if (lowerMessage.includes('access denied') || lowerMessage.includes('permission denied') ||
      lowerMessage.includes('unauthorized')) {
    return userFriendlyMessages.accessDenied;
  }

  // Validation errors
  if (lowerMessage.includes('validation failed') || lowerMessage.includes('validation error')) {
    return userFriendlyMessages.validationFailed;
  }

  // Network errors
  if (lowerMessage.includes('network error') || lowerMessage.includes('connection failed')) {
    return userFriendlyMessages.connectionError;
  }

  if (lowerMessage.includes('timeout') || lowerMessage.includes('request timeout')) {
    return userFriendlyMessages.timeoutError;
  }

  // File upload errors
  if (lowerMessage.includes('file too large') || lowerMessage.includes('file size')) {
    return userFriendlyMessages.fileTooLarge;
  }

  if (lowerMessage.includes('invalid file type') || lowerMessage.includes('file format')) {
    return userFriendlyMessages.invalidFileType;
  }

  // Generic server errors
  if (lowerMessage.includes('internal server error') || lowerMessage.includes('server error')) {
    return userFriendlyMessages.serverError;
  }

  // Record not found
  if (lowerMessage.includes('not found') || lowerMessage.includes('does not exist')) {
    return userFriendlyMessages.recordNotFound;
  }

  // Duplicate errors
  if (lowerMessage.includes('already exists') || lowerMessage.includes('duplicate')) {
    return userFriendlyMessages.duplicateRecord;
  }

  // Deletion restriction errors
  if (lowerMessage.includes('cannot delete customer with existing related records') ||
      lowerMessage.includes('cannot delete customer') && lowerMessage.includes('service calls')) {
    return userFriendlyMessages.customerDeletionBlocked;
  }

  if (lowerMessage.includes('cannot delete converted lead') ||
      lowerMessage.includes('lead has been converted') ||
      (lowerMessage.includes('cannot delete') && lowerMessage.includes('converted'))) {
    return userFriendlyMessages.leadDeletionBlocked;
  }

  // If we have a custom fallback message, use it
  if (fallbackMessage) {
    return fallbackMessage;
  }

  // If the original message is already user-friendly (doesn't contain technical terms), use it
  const technicalTerms = ['sql', 'database', 'sequelize', 'constraint', 'foreign key', 'null', 'undefined', 'stack trace'];
  const hasTechnicalTerms = technicalTerms.some(term => lowerMessage.includes(term));

  if (!hasTechnicalTerms && errorMessage.length > 0 && errorMessage.length < 200) {
    return errorMessage;
  }

  // Default fallback
  return userFriendlyMessages.unknownError;
};

/**
 * Validation Timing Configuration
 */
export const validationTiming = {
  // Validate on input change (real-time)
  realTime: [
    'customerSerialNumber',
    'email',
    'phone',
    'gstNo',
    'panNumber'
  ],

  // Validate on field blur
  onBlur: [
    'subject',
    'description',
    'companyName',
    'firstName',
    'lastName'
  ],

  // Validate on form submit
  onSubmit: [
    'all_fields'
  ]
};

export default {
  frontendValidations,
  backendValidations,
  validationStrategy,
  fieldConfigurations,
  errorMessages,
  validationTiming
};
