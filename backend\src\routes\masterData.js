import express from 'express';
import { body, query, param } from 'express-validator';
import { Op } from 'sequelize';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import models from '../models/index.js';
import {
  licenseEdition<PERSON>ontroller,
  designationController,
  tallyProductController,
  staffRoleController,
  industryController,
  areaController,
  natureOfIssueController,
  additionalServiceController,
  callStatusController,
  typeOfCallController,
} from '../controllers/masterDataController.js';
import * as productsIssuesController from '../controllers/productsIssuesController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Common validation rules
const commonQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must be less than 100 characters'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['name', 'code', 'sort_order', 'created_at'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
];

const commonBodyValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('code')
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9_]+$/)
    .withMessage('Code must contain only uppercase letters, numbers, and underscores'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
];

const idValidation = [
  param('id')
    .isUUID()
    .withMessage('ID must be a valid UUID'),
];

/**
 * @swagger
 * components:
 *   schemas:
 *     MasterDataEntity:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           example: "123e4567-e89b-12d3-a456-************"
 *         name:
 *           type: string
 *           example: "Sample Entity"
 *         description:
 *           type: string
 *           nullable: true
 *           example: "Description of the entity"
 *         is_active:
 *           type: boolean
 *           example: true
 *         sort_order:
 *           type: integer
 *           example: 1
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     MasterDataCreateRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           example: "New Entity"
 *         description:
 *           type: string
 *           maxLength: 500
 *           nullable: true
 *           example: "Description of the new entity"
 *         is_active:
 *           type: boolean
 *           default: true
 *           example: true
 *         sort_order:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *           example: 1
 *     MasterDataUpdateRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           example: "Updated Entity"
 *         description:
 *           type: string
 *           maxLength: 500
 *           nullable: true
 *           example: "Updated description"
 *         is_active:
 *           type: boolean
 *           example: true
 *         sort_order:
 *           type: integer
 *           minimum: 0
 *           example: 2
 */

// Helper function to create CRUD routes for a master data entity
const createMasterDataRoutes = (path, controller, permission, additionalValidation = []) => {
  /**
   * @swagger
   * /master-data/{entity}:
   *   get:
   *     summary: Get all entities
   *     description: Retrieve a paginated list of master data entities with optional filtering and sorting
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type (e.g., areas, industries, call-statuses)
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: Number of items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *           maxLength: 100
   *         description: Search term for name or description
   *       - in: query
   *         name: isActive
   *         schema:
   *           type: boolean
   *         description: Filter by active status
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *           enum: [name, created_at, updated_at, sort_order]
   *           default: sort_order
   *         description: Field to sort by
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [ASC, DESC, asc, desc]
   *           default: ASC
   *         description: Sort order
   *     responses:
   *       200:
   *         description: Entities retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/PaginatedResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         entities:
   *                           type: array
   *                           items:
   *                             $ref: '#/components/schemas/MasterDataEntity'
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.get(`/${path}`, [
    requirePermission(`${permission}.read`),
    ...commonQueryValidation,
    validate,
  ], controller.getAll);

  /**
   * @swagger
   * /master-data/{entity}/{id}:
   *   get:
   *     summary: Get entity by ID
   *     description: Retrieve a specific master data entity by its ID
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Entity ID
   *     responses:
   *       200:
   *         description: Entity retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         entity:
   *                           $ref: '#/components/schemas/MasterDataEntity'
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       404:
   *         $ref: '#/components/responses/404'
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.get(`/${path}/:id`, [
    requirePermission(`${permission}.read`),
    ...idValidation,
    validate,
  ], controller.getById);

  /**
   * @swagger
   * /master-data/{entity}:
   *   post:
   *     summary: Create new entity
   *     description: Create a new master data entity
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/MasterDataCreateRequest'
   *     responses:
   *       201:
   *         description: Entity created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         entity:
   *                           $ref: '#/components/schemas/MasterDataEntity'
   *       400:
   *         $ref: '#/components/responses/400'
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       422:
   *         $ref: '#/components/responses/422'
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.post(`/${path}`, [
    requirePermission(`${permission}.create`),
    ...commonBodyValidation,
    ...additionalValidation,
    validate,
  ], controller.create);

  /**
   * @swagger
   * /master-data/{entity}/{id}:
   *   put:
   *     summary: Update entity
   *     description: Update an existing master data entity
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Entity ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/MasterDataUpdateRequest'
   *     responses:
   *       200:
   *         description: Entity updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         entity:
   *                           $ref: '#/components/schemas/MasterDataEntity'
   *       400:
   *         $ref: '#/components/responses/400'
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       404:
   *         $ref: '#/components/responses/404'
   *       422:
   *         $ref: '#/components/responses/422'
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.put(`/${path}/:id`, [
    requirePermission(`${permission}.update`),
    ...idValidation,
    ...commonBodyValidation.map(rule => rule.optional()),
    ...additionalValidation.map(rule => rule.optional()),
    validate,
  ], controller.update);

  /**
   * @swagger
   * /master-data/{entity}/{id}:
   *   delete:
   *     summary: Delete entity
   *     description: Delete a master data entity (soft delete by default)
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Entity ID
   *     responses:
   *       200:
   *         description: Entity deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         deletedEntity:
   *                           type: object
   *                           properties:
   *                             id:
   *                               type: string
   *                               format: uuid
   *                             name:
   *                               type: string
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       404:
   *         $ref: '#/components/responses/404'
   *       409:
   *         description: Cannot delete entity
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ErrorResponse'
   *             example:
   *               success: false
   *               message: "Cannot delete entity as it is being used"
   *               errors: {}
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.delete(`/${path}/:id`, [
    requirePermission(`${permission}.delete`),
    ...idValidation,
    validate,
  ], controller.delete);

  /**
   * @swagger
   * /master-data/{entity}/{id}/toggle-active:
   *   patch:
   *     summary: Toggle entity active status
   *     description: Toggle the active/inactive status of a master data entity
   *     tags: [Master Data]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: entity
   *         required: true
   *         schema:
   *           type: string
   *         description: Entity type
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Entity ID
   *     responses:
   *       200:
   *         description: Entity status toggled successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/SuccessResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       type: object
   *                       properties:
   *                         entity:
   *                           $ref: '#/components/schemas/MasterDataEntity'
   *                         previousStatus:
   *                           type: boolean
   *                         newStatus:
   *                           type: boolean
   *       401:
   *         $ref: '#/components/responses/401'
   *       403:
   *         $ref: '#/components/responses/403'
   *       404:
   *         $ref: '#/components/responses/404'
   *       500:
   *         $ref: '#/components/responses/500'
   */
  router.patch(`/${path}/:id/toggle-active`, [
    requirePermission(`${permission}.update`),
    ...idValidation,
    validate,
  ], controller.toggleActive);
};

// License Editions
createMasterDataRoutes('license-editions', licenseEditionController, 'master_data', [
  body('version')
    .optional()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Version must be between 1 and 20 characters'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('annual_maintenance_charge')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Annual maintenance charge must be a valid decimal number'),
  body('max_companies')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const intValue = parseInt(value);
      if (isNaN(intValue) || intValue < 1) {
        throw new Error('Max companies must be a positive integer');
      }
      return true;
    }),
  body('max_users')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const intValue = parseInt(value);
      if (isNaN(intValue) || intValue < 1) {
        throw new Error('Max users must be a positive integer');
      }
      return true;
    }),
]);

// Designations
createMasterDataRoutes('designations', designationController, 'master_data', [
  body('level')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Level must be between 1 and 10'),
  body('department')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Department must be between 2 and 50 characters'),
]);

// Tally Products
createMasterDataRoutes('tally-products', tallyProductController, 'master_data', [
  body('category')
    .optional()
    .isIn(['software', 'hardware', 'service', 'addon', 'training'])
    .withMessage('Invalid category'),
  body('version')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Version must be between 1 and 20 characters'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('cost_price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Cost price must be a valid decimal number'),
  body('hsn_code')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const trimmedValue = String(value).trim();
      if (trimmedValue.length < 4 || trimmedValue.length > 8) {
        throw new Error('HSN code must be between 4 and 8 characters');
      }
      return true;
    }),
  body('gst_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('GST rate must be a valid decimal number'),
  body('unit')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 10 })
    .withMessage('Unit must be between 1 and 10 characters'),
  body('is_service')
    .optional()
    .isBoolean()
    .withMessage('isService must be a boolean'),
]);

// Staff Roles
createMasterDataRoutes('staff-roles', staffRoleController, 'master_data', [
  body('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('level')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Level must be between 1 and 10'),
]);

// Industries
createMasterDataRoutes('industries', industryController, 'master_data', [
  body('category')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const trimmedValue = value.trim();
      if (trimmedValue.length < 2 || trimmedValue.length > 50) {
        throw new Error('Category must be between 2 and 50 characters');
      }
      return true;
    }),
]);

// Areas
createMasterDataRoutes('areas', areaController, 'master_data', [
  body('city')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters'),
  body('state')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('State must be between 2 and 50 characters'),
  body('country')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Country must be between 2 and 50 characters'),
]);

// Nature of Issues
createMasterDataRoutes('nature-of-issues', natureOfIssueController, 'master_data', [
  body('category')
    .optional()
    .isIn(['technical', 'functional', 'data', 'installation', 'training', 'other'])
    .withMessage('Invalid category'),
  body('severity')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid severity'),
  body('estimated_resolution_time')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const intValue = parseInt(value);
      if (isNaN(intValue) || intValue < 1) {
        throw new Error('Estimated resolution time must be a positive integer');
      }
      return true;
    }),
  body('requires_onsite')
    .optional()
    .isBoolean()
    .withMessage('requiresOnsite must be a boolean'),
]);

// Additional Services
createMasterDataRoutes('additional-services', additionalServiceController, 'master_data', [
  body('category')
    .optional()
    .isIn(['training', 'customization', 'integration', 'support', 'consulting', 'other'])
    .withMessage('Invalid category'),
  body('service_type')
    .optional()
    .isIn(['onetime', 'recurring', 'hourly', 'project'])
    .withMessage('Invalid service type'),
  body('price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Price must be a valid decimal number'),
  body('cost_price')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Cost price must be a valid decimal number'),
  body('duration_hours')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      const intValue = parseInt(value);
      if (isNaN(intValue) || intValue < 1) {
        throw new Error('Duration hours must be a positive integer');
      }
      return true;
    }),
  body('gst_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('GST rate must be a valid decimal number'),
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('requiresApproval must be a boolean'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
]);

// Call Statuses
createMasterDataRoutes('call-statuses', callStatusController, 'master_data', [
  body('category')
    .optional()
    .isIn(['open', 'in_progress', 'resolved', 'closed', 'cancelled'])
    .withMessage('Invalid category'),
  body('color')
    .optional()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex color code'),
  body('is_final')
    .optional()
    .isBoolean()
    .withMessage('isFinal must be a boolean'),
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('requiresApproval must be a boolean'),
  body('auto_close_after_days')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      const intValue = parseInt(value);
      if (isNaN(intValue) || intValue < 1) {
        throw new Error('Auto close after days must be a positive integer');
      }
      return true;
    }),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
  body('is_default')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
]);

// Type of Calls
createMasterDataRoutes('type-of-calls', typeOfCallController, 'master_data', [
  body('category')
    .optional()
    .isIn(['amc', 'tss', 'support', 'maintenance', 'training', 'other'])
    .withMessage('Invalid category'),
  body('service_type')
    .optional()
    .isIn(['onsite', 'online', 'both'])
    .withMessage('Invalid service type'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
  body('default_duration')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Default duration must be a positive integer'),
  body('requires_approval')
    .optional()
    .isBoolean()
    .withMessage('requiresApproval must be a boolean'),
]);

// Products/Issues - Using dedicated controller
const productsIssuesValidation = [
  body('category')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters'),
];

// Products/Issues CRUD routes
// Note: Specific routes (search, categories) must come before parameterized routes (:id)

/**
 * @swagger
 * /master-data/products-issues/search:
 *   get:
 *     summary: Search products and issues
 *     description: Search for products and issues with optional filtering by category
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search query for name or description
 *         example: "installation"
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Filter by category
 *         example: "technical"
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Maximum number of results
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         results:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/MasterDataEntity'
 *                         total:
 *                           type: integer
 *                           example: 25
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/products-issues/search', [
  requirePermission('master_data.read'),
  query('q')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search query must be less than 100 characters'),
  query('category')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validate,
], productsIssuesController.searchProductsIssues);

/**
 * @swagger
 * /master-data/products-issues/categories:
 *   get:
 *     summary: Get product/issue categories
 *     description: Retrieve all available categories for products and issues
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         categories:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                                 example: "technical"
 *                               count:
 *                                 type: integer
 *                                 example: 15
 *             example:
 *               success: true
 *               message: "Categories retrieved successfully"
 *               data:
 *                 categories:
 *                   - name: "technical"
 *                     count: 15
 *                   - name: "functional"
 *                     count: 8
 *                   - name: "installation"
 *                     count: 12
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/products-issues/categories', [
  requirePermission('master_data.read'),
  validate,
], productsIssuesController.getCategories);

/**
 * @swagger
 * /master-data/products-issues:
 *   get:
 *     summary: Get all products and issues
 *     description: Retrieve a paginated list of products and issues with optional filtering
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search term for name or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: Products and issues retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedResponse'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/products-issues', [
  requirePermission('master_data.read'),
  ...commonQueryValidation,
  validate,
], productsIssuesController.getProductsIssues);

/**
 * @swagger
 * /master-data/products-issues/{id}:
 *   get:
 *     summary: Get product/issue by ID
 *     description: Retrieve a specific product or issue by its ID
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Product/Issue ID
 *     responses:
 *       200:
 *         description: Product/Issue retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         productIssue:
 *                           $ref: '#/components/schemas/MasterDataEntity'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         $ref: '#/components/responses/404'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/products-issues/:id', [
  requirePermission('master_data.read'),
  ...idValidation,
  validate,
], productsIssuesController.getProductsIssuesById);

/**
 * @swagger
 * /master-data/products-issues:
 *   post:
 *     summary: Create new product/issue
 *     description: Create a new product or issue entry
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 example: "Tally Installation Issue"
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 nullable: true
 *                 example: "Common issues during Tally software installation"
 *               category:
 *                 type: string
 *                 maxLength: 50
 *                 example: "technical"
 *               is_active:
 *                 type: boolean
 *                 default: true
 *                 example: true
 *               sort_order:
 *                 type: integer
 *                 minimum: 0
 *                 default: 0
 *                 example: 1
 *           example:
 *             name: "Tally Installation Issue"
 *             description: "Common issues during Tally software installation"
 *             category: "technical"
 *             is_active: true
 *             sort_order: 1
 *     responses:
 *       201:
 *         description: Product/Issue created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         productIssue:
 *                           $ref: '#/components/schemas/MasterDataEntity'
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/products-issues', [
  requirePermission('master_data.create'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  ...productsIssuesValidation,
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  validate,
], productsIssuesController.createProductsIssues);

/**
 * @swagger
 * /master-data/products-issues/{id}:
 *   put:
 *     summary: Update product/issue
 *     description: Update an existing product or issue entry
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Product/Issue ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 example: "Updated Tally Installation Issue"
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 nullable: true
 *                 example: "Updated description for installation issues"
 *               category:
 *                 type: string
 *                 maxLength: 50
 *                 example: "technical"
 *               is_active:
 *                 type: boolean
 *                 example: true
 *               sort_order:
 *                 type: integer
 *                 minimum: 0
 *                 example: 2
 *     responses:
 *       200:
 *         description: Product/Issue updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         productIssue:
 *                           $ref: '#/components/schemas/MasterDataEntity'
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         $ref: '#/components/responses/404'
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.put('/products-issues/:id', [
  requirePermission('master_data.update'),
  ...idValidation,
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters'),
  ...productsIssuesValidation.map(rule => rule.optional()),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  validate,
], productsIssuesController.updateProductsIssues);

/**
 * @swagger
 * /master-data/products-issues/{id}:
 *   delete:
 *     summary: Delete product/issue
 *     description: Delete a product or issue entry (soft delete by default)
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Product/Issue ID
 *     responses:
 *       200:
 *         description: Product/Issue deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         deletedProductIssue:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                             name:
 *                               type: string
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         $ref: '#/components/responses/404'
 *       409:
 *         description: Cannot delete product/issue
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Cannot delete product/issue as it is being used in service calls"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.delete('/products-issues/:id', [
  requirePermission('master_data.delete'),
  ...idValidation,
  validate,
], productsIssuesController.deleteProductsIssues);

/**
 * @swagger
 * /master-data/products-issues/{id}/toggle-active:
 *   patch:
 *     summary: Toggle product/issue active status
 *     description: Toggle the active/inactive status of a product or issue entry
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Product/Issue ID
 *     responses:
 *       200:
 *         description: Product/Issue status toggled successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         productIssue:
 *                           $ref: '#/components/schemas/MasterDataEntity'
 *                         previousStatus:
 *                           type: boolean
 *                         newStatus:
 *                           type: boolean
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         $ref: '#/components/responses/404'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.patch('/products-issues/:id/toggle-active', [
  requirePermission('master_data.update'),
  ...idValidation,
  validate,
], productsIssuesController.toggleActiveProductsIssues);

/**
 * @swagger
 * /master-data/test-route:
 *   get:
 *     summary: Test master data routes
 *     description: Test endpoint to verify master data routes are working
 *     tags: [Master Data]
 *     responses:
 *       200:
 *         description: Test route working
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Master data test route is working!"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/test-route', (req, res) => {
  res.json({
    message: 'Master data test route is working!',
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /master-data/test-products-issues:
 *   post:
 *     summary: Test products/issues POST endpoint
 *     description: Test endpoint to verify products/issues POST functionality
 *     tags: [Master Data]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               test:
 *                 type: string
 *                 example: "test data"
 *     responses:
 *       200:
 *         description: Test POST endpoint working
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Products/Issues POST test route is working!"
 *                 body:
 *                   type: object
 *                   description: "Request body that was sent"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.post('/test-products-issues', (req, res) => {
  res.json({
    message: 'Products/Issues POST test route is working!',
    body: req.body,
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /master-data/online-call-types:
 *   get:
 *     summary: Get online call types
 *     description: Retrieve all online call types with pagination
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Online call types retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         onlineCallTypes:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/MasterDataEntity'
 *                         pagination:
 *                           $ref: '#/components/schemas/PaginationMeta'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/online-call-types', requirePermission('master_data.read'), async (req, res) => {
  try {
    res.json({
      status: 'success',
      message: 'Online call types endpoint working!',
      data: {
        onlineCallTypes: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
      }
    });
  } catch (error) {
    res.status(500).json({ status: 'error', message: error.message });
  }
});

/**
 * @swagger
 * /master-data/online-call-types/test:
 *   get:
 *     summary: Test online call types endpoint
 *     description: Test endpoint to verify online call types functionality
 *     tags: [Master Data]
 *     responses:
 *       200:
 *         description: Test endpoint working
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Online call types test route is working!"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/online-call-types/test', (req, res) => {
  res.json({
    message: 'Online call types test route is working!',
    timestamp: new Date().toISOString()
  });
});

/**
 * @swagger
 * /master-data/online-call-types/search:
 *   get:
 *     summary: Search online call types
 *     description: Search for online call types with optional filtering
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search query
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Maximum number of results
 *     responses:
 *       200:
 *         description: Search results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/MasterDataEntity'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/online-call-types/search', requirePermission('master_data.read'), async (req, res) => {
  try {
    res.json({
      status: 'success',
      message: 'Online call types search endpoint working!',
      data: []
    });
  } catch (error) {
    res.status(500).json({ status: 'error', message: error.message });
  }
});

/**
 * @swagger
 * /master-data/online-call-types/categories:
 *   get:
 *     summary: Get online call type categories
 *     description: Retrieve all available categories for online call types
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "technical"
 *                           count:
 *                             type: integer
 *                             example: 5
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/online-call-types/categories', requirePermission('master_data.read'), async (req, res) => {
  try {
    res.json({
      status: 'success',
      message: 'Online call types categories endpoint working!',
      data: []
    });
  } catch (error) {
    res.status(500).json({ status: 'error', message: error.message });
  }
});



/**
 * @swagger
 * /master-data/debug:
 *   get:
 *     summary: Debug master data status
 *     description: Debug endpoint to check master data models and database status
 *     tags: [Master Data]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Debug information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         typeOfCallsTableExists:
 *                           type: boolean
 *                           example: true
 *                         typeOfCallModelExists:
 *                           type: boolean
 *                           example: true
 *                         typeOfCallCount:
 *                           type: integer
 *                           example: 5
 *                         typeOfCallError:
 *                           type: string
 *                           nullable: true
 *                           example: null
 *                         availableModels:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["Area", "Industry", "CallStatus"]
 *                         timestamp:
 *                           type: string
 *                           format: date-time
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/debug', requirePermission('master_data.read'), async (req, res) => {
  try {
    const models = (await import('../models/index.js')).default;

    // Check if TypeOfCall table exists
    const typeOfCallsExists = await models.sequelize.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'type_of_calls')",
      { type: models.sequelize.QueryTypes.SELECT }
    );

    // Check available models
    const availableModels = Object.keys(models).filter(key =>
      key !== 'sequelize' && key !== 'Sequelize'
    );

    // Try to count TypeOfCall records
    let typeOfCallCount = 0;
    let typeOfCallError = null;
    try {
      if (models.TypeOfCall) {
        typeOfCallCount = await models.TypeOfCall.count();
      }
    } catch (error) {
      typeOfCallError = error.message;
    }

    res.json({
      success: true,
      data: {
        typeOfCallsTableExists: typeOfCallsExists[0]?.exists || false,
        typeOfCallModelExists: !!models.TypeOfCall,
        typeOfCallCount,
        typeOfCallError,
        availableModels,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Master data debug failed',
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack
      } : undefined
    });
  }
});

export default router;
