/**
 * Line Chart Component
 * Responsive line chart using Recharts for trend analysis and time series data
 */

import React from 'react';
import {
  LineChart as RechartsLine<PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { CHART_COLORS, CHART_THEMES } from './chartThemes';
import { formatNumber, formatDate, generateTooltipContent } from './chartUtils';

const LineChart = ({
  data = [],
  lines = [], // Array of line configurations: [{ dataKey, name, color, strokeWidth }]
  xAxisKey = 'date',
  height = 300,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  curved = true,
  strokeWidth = 2,
  dotSize = 4,
  formatters = {}, // Custom formatters for tooltip values
  theme = 'default',
  className = '',
  ...props
}) => {
  // Default line configuration if none provided
  const defaultLines = lines.length > 0 ? lines : [
    { dataKey: 'value', name: 'Value', color: CHART_COLORS.primary[0] }
  ];

  // Theme configuration
  const currentTheme = CHART_THEMES[theme] || CHART_THEMES.default;

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 mb-2">
          {formatDate(label, 'short')}
        </p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between space-x-4 mb-1">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600">{entry.name}:</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatters[entry.dataKey] 
                ? formatters[entry.dataKey](entry.value)
                : formatNumber(entry.value)
              }
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Custom dot component for active points
  const CustomDot = (props) => {
    const { cx, cy, fill } = props;
    return (
      <circle 
        cx={cx} 
        cy={cy} 
        r={dotSize} 
        fill={fill} 
        stroke="#fff" 
        strokeWidth={2}
        className="drop-shadow-sm"
      />
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 text-sm">No data available for chart</p>
      </div>
    );
  }

  return (
    <div className={className} {...props}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsLineChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={currentTheme.gridColor}
              opacity={0.5}
            />
          )}
          
          <XAxis 
            dataKey={xAxisKey}
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatDate(value, 'short')}
          />
          
          <YAxis 
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatNumber(value, 'compact')}
          />
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {showLegend && (
            <Legend 
              wrapperStyle={{ 
                fontSize: currentTheme.fontSize,
                color: currentTheme.textColor 
              }}
            />
          )}
          
          {defaultLines.map((line, index) => (
            <Line
              key={line.dataKey}
              type={curved ? "monotone" : "linear"}
              dataKey={line.dataKey}
              name={line.name}
              stroke={line.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length]}
              strokeWidth={line.strokeWidth || strokeWidth}
              dot={<CustomDot />}
              activeDot={{ 
                r: dotSize + 2, 
                stroke: line.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length],
                strokeWidth: 2,
                fill: '#fff'
              }}
              connectNulls={false}
            />
          ))}
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default LineChart;
