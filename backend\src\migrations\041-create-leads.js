import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create leads table
  await queryInterface.createTable('leads', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Lead date',
    },
    customer_name: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200],
      },
      comment: 'Potential customer name',
    },
    products: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Products of interest',
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Potential deal amount',
    },
    contact_no: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 20],
      },
      comment: 'Contact phone number without country code',
    },
    country_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '+91',
      validate: {
        len: [0, 10],
      },
      comment: 'Country code for contact number',
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50],
      },
      comment: 'Lead status',
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional remarks or notes',
    },
    executive: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100],
      },
      comment: 'Assigned executive name',
    },
    follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Next follow up date',
    },
    ref_name: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100],
      },
      comment: 'Reference person name',
    },
    ref_contact_no: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 20],
      },
      comment: 'Reference contact number without country code',
    },
    ref_country_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '+91',
      validate: {
        len: [0, 10],
      },
      comment: 'Country code for reference contact number',
    },
    ref_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Reference amount',
    },
    converted_to_customer_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Customer ID if lead was converted',
    },
    conversion_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Date when lead was converted to customer',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    updated_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  });

  // Add indexes for better performance
  await queryInterface.addIndex('leads', ['tenant_id']);
  await queryInterface.addIndex('leads', ['status']);
  await queryInterface.addIndex('leads', ['executive']);
  await queryInterface.addIndex('leads', ['follow_up_date']);
  await queryInterface.addIndex('leads', ['created_by']);
  await queryInterface.addIndex('leads', ['converted_to_customer_id']);
  await queryInterface.addIndex('leads', ['date']);
  await queryInterface.addIndex('leads', ['created_at']);

  console.log('✅ Created leads table with indexes');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('leads');
  console.log('✅ Dropped leads table');
};
