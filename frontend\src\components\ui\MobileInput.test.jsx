/**
 * Test file for MobileInput component
 * This file demonstrates the functionality and validates the implementation
 */

import React, { useState } from 'react';
import MobileInput from './MobileInput';
import { hasPhoneDigits, validationRules } from '../../utils/validation';

// Test component to demonstrate MobileInput functionality
const MobileInputTest = () => {
  const [testValues, setTestValues] = useState({
    required: '',
    optional: '',
    withError: '',
    readOnly: '+91 9876543210',
    disabled: '*************'
  });

  const [errors, setErrors] = useState({});

  const handleChange = (field) => (e) => {
    const value = e.target.value;
    setTestValues(prev => ({ ...prev, [field]: value }));
    
    // Test validation
    if (field === 'required' && value) {
      const validation = validationRules.phone(value, true);
      setErrors(prev => ({
        ...prev,
        [field]: validation.isValid ? '' : validation.message
      }));
    }
  };

  const testValidationLogic = () => {
    console.log('🧪 Testing validation logic:');
    
    const testCases = [
      { value: '+91 9876543210', expected: true, description: 'Valid Indian number' },
      { value: '*************', expected: true, description: 'Valid US number' },
      { value: '+44 20 7946 0958', expected: true, description: 'Valid UK number' },
      { value: '9876543210', expected: true, description: 'Valid Indian number without country code' },
      { value: '+91', expected: false, description: 'Only country code' },
      { value: '', expected: false, description: 'Empty value' },
      { value: 'invalid', expected: false, description: 'Invalid format' }
    ];

    testCases.forEach(testCase => {
      const result = validationRules.phone(testCase.value, false);
      const passed = result.isValid === testCase.expected;
      console.log(`${passed ? '✅' : '❌'} ${testCase.description}: ${testCase.value} -> ${result.isValid ? 'Valid' : 'Invalid'}`);
      if (!passed) {
        console.log(`   Expected: ${testCase.expected}, Got: ${result.isValid}`);
        console.log(`   Message: ${result.message}`);
      }
    });
  };

  const testHasPhoneDigits = () => {
    console.log('🧪 Testing hasPhoneDigits function:');
    
    const testCases = [
      { value: '+91 9876543210', expected: true, description: 'Full phone number' },
      { value: '+91', expected: false, description: 'Only country code' },
      { value: '', expected: false, description: 'Empty string' },
      { value: '   ', expected: false, description: 'Only spaces' },
      { value: '123', expected: true, description: 'Just digits' }
    ];

    testCases.forEach(testCase => {
      const result = hasPhoneDigits(testCase.value);
      const passed = result === testCase.expected;
      console.log(`${passed ? '✅' : '❌'} ${testCase.description}: "${testCase.value}" -> ${result}`);
    });
  };

  return (
    <div className="p-6 max-w-2xl mx-auto space-y-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">MobileInput Component Test</h2>
      
      <div className="space-y-4">
        {/* Required Field Test */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Required Field Test *
          </label>
          <MobileInput
            value={testValues.required}
            onChange={handleChange('required')}
            placeholder="Enter required phone number"
            error={!!errors.required}
            required
            className="w-full"
          />
          {errors.required && (
            <p className="mt-1 text-sm text-red-600">{errors.required}</p>
          )}
        </div>

        {/* Optional Field Test */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Optional Field Test
          </label>
          <MobileInput
            value={testValues.optional}
            onChange={handleChange('optional')}
            placeholder="Enter optional phone number"
            className="w-full"
          />
        </div>

        {/* Read-only Test */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Read-only Test
          </label>
          <MobileInput
            value={testValues.readOnly}
            onChange={handleChange('readOnly')}
            placeholder="Read-only field"
            readOnly
            className="w-full"
          />
        </div>

        {/* Disabled Test */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Disabled Test
          </label>
          <MobileInput
            value={testValues.disabled}
            onChange={handleChange('disabled')}
            placeholder="Disabled field"
            disabled
            className="w-full"
          />
        </div>
      </div>

      {/* Test Buttons */}
      <div className="flex space-x-4 pt-4">
        <button
          onClick={testValidationLogic}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Test Validation Logic
        </button>
        <button
          onClick={testHasPhoneDigits}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
        >
          Test hasPhoneDigits
        </button>
      </div>

      {/* Current Values Display */}
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-medium text-gray-800 mb-2">Current Values:</h3>
        <pre className="text-sm text-gray-600">
          {JSON.stringify(testValues, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default MobileInputTest;
