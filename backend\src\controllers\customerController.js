import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';
import { emailService } from '../services/emailService.js';

/**
 * Get all customers with pagination and filters
 */
export const getCustomers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
      isActive,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      // New filter parameters
      amcStatus,
      amcExpiryDate,
      tssStatus,
      tssExpiryDate,
      licenseEdition,
      productType,
      tdlAddons,
      tdlExpiryDate,
      autoBackup,
      autoBackupExpiryDate,
      cloudUser,
      cloudUserExpiryDate,
      mobileApp,
      mobileAppExpiryDate,
      whatsappGroup,
      whatsappExpiryDate,
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply filters - Enhanced search for customer name functionality (updated)
    if (search) {
      // Minimum 2 characters required for search (as per user requirements)
      if (search.length >= 2) {
        where[Op.or] = [
          // Primary search fields for customer name (as requested by user)
          { company_name: { [Op.iLike]: `%${search}%` } },
          { contact_person: { [Op.iLike]: `%${search}%` } },
          { display_name: { [Op.iLike]: `%${search}%` } },
          // Additional search fields for comprehensive search
          { customer_code: { [Op.iLike]: `%${search}%` } },
          { tally_serial_number: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { phone: { [Op.iLike]: `%${search}%` } },
          { gst_number: { [Op.iLike]: `%${search}%` } },
          { city: { [Op.iLike]: `%${search}%` } },
          { state: { [Op.iLike]: `%${search}%` } },
          // Basic search fields only (removed JSON field searches to fix 400 error)
          // Note: Removed customer_type search to avoid ENUM ILIKE issues
          // Users typically don't search for "prospect" or "customer" in the search box
        ];
      } else {
        // If search term is less than 2 characters, return no results
        where.id = null; // This will return no results
      }
    }

    if (customerType) {
      where.customer_type = customerType;
    }

    if (industryId) {
      where.industry_id = industryId;
    }

    if (areaId) {
      where.area_id = areaId;
    }

    if (assignedExecutiveId) {
      where.assigned_executive_id = assignedExecutiveId;
    }

    if (isActive !== undefined) {
      where.is_active = isActive === 'true';
    }

    // Apply new filters (temporarily commented out JSON field filters to fix 400 error)
    // TODO: Fix JSON field query syntax for PostgreSQL
    /*
    if (amcStatus && amcStatus !== 'all') {
      where['custom_fields.amc_status'] = amcStatus;
    }

    if (amcExpiryDate) {
      where['custom_fields.amc_expiry_date'] = { [Op.lte]: amcExpiryDate };
    }

    if (tssStatus && tssStatus !== 'all') {
      where['custom_fields.tss_status'] = tssStatus;
    }

    if (tssExpiryDate) {
      where['custom_fields.tss_expiry_date'] = { [Op.lte]: tssExpiryDate };
    }

    if (licenseEdition && licenseEdition !== 'all') {
      where['custom_fields.license_edition_id'] = licenseEdition;
    }

    if (productType && productType !== 'all') {
      where['custom_fields.product_id'] = productType;
    }

    if (tdlAddons && tdlAddons !== 'all') {
      where['custom_fields.tdl_addons'] = tdlAddons === 'true';
    }

    if (tdlExpiryDate) {
      where['custom_fields.tdl_expiry_date'] = { [Op.lte]: tdlExpiryDate };
    }

    if (autoBackup && autoBackup !== 'all') {
      where['custom_fields.auto_backup'] = autoBackup === 'true';
    }

    if (autoBackupExpiryDate) {
      where['custom_fields.auto_backup_expiry_date'] = { [Op.lte]: autoBackupExpiryDate };
    }

    if (cloudUser && cloudUser !== 'all') {
      where['custom_fields.cloud_user'] = cloudUser === 'true';
    }

    if (cloudUserExpiryDate) {
      where['custom_fields.cloud_user_expiry_date'] = { [Op.lte]: cloudUserExpiryDate };
    }
    */

    // More JSON field filters (temporarily commented out)
    /*
    if (mobileApp && mobileApp !== 'all') {
      where['custom_fields.mobile_app'] = mobileApp === 'true';
    }

    if (mobileAppExpiryDate) {
      where['custom_fields.mobile_app_expiry_date'] = { [Op.lte]: mobileAppExpiryDate };
    }

    if (whatsappGroup && whatsappGroup !== 'all') {
      where['custom_fields.whatsapp_group'] = whatsappGroup === 'true';
    }

    if (whatsappExpiryDate) {
      where['custom_fields.whatsapp_expiry_date'] = { [Op.lte]: whatsappExpiryDate };
    }
    */

    const { count, rows: customers } = await models.Customer.findAndCountAll({
      where,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name', 'code'],
          required: false, // Make it optional
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name', 'code', 'city', 'state'],
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false, // Make it optional
          where: {
            tenant_id: req.user.tenant.id // Only apply tenant filter to Executive
          }
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false, // Make it optional
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]], // Use dynamic sorting from request parameters
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer by ID
 */
export const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.Industry,
          as: 'industry',
          required: false, // Make it optional
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          required: false, // Make it optional
          where: {
            tenant_id: req.user.tenant.id // Only apply tenant filter to Executive
          },
          include: [
            {
              model: models.Designation,
              as: 'designation',
              required: false,
            },
          ],
        },
        {
          model: models.CustomerContact,
          as: 'contacts',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerTSS,
          as: 'tssDetails',
          include: [
            {
              model: models.LicenseEdition,
              as: 'licenseEdition',
            },
          ],
        },
        {
          model: models.CustomerAMC,
          as: 'amcContracts',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Enhance custom_fields with product and license edition names
    const customFields = customer.custom_fields || {};

    // Fetch product name if product_id exists
    if (customFields.product_id) {
      try {
        const product = await models.TallyProduct.findByPk(customFields.product_id);
        if (product) {
          customFields.product_name = product.name;
        }
      } catch (error) {
        logger.warn('Error fetching product details:', error);
      }
    }

    // Fetch license edition name if license_edition_id exists
    if (customFields.license_edition_id) {
      try {
        const licenseEdition = await models.LicenseEdition.findByPk(customFields.license_edition_id);
        if (licenseEdition) {
          customFields.license_edition_name = licenseEdition.name;
        }
      } catch (error) {
        logger.warn('Error fetching license edition details:', error);
      }
    }

    // Update the customer object with enhanced custom_fields
    customer.custom_fields = customFields;

    res.json({
      success: true,
      data: { customer },
    });

  } catch (error) {
    logger.error('Get customer by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new customer
 */
export const createCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    // Handle multipart form data (with files) or regular JSON
    let customerData;
    if (req.body.customerData) {
      // If customerData is a string (from FormData), parse it
      customerData = typeof req.body.customerData === 'string'
        ? JSON.parse(req.body.customerData)
        : req.body.customerData;
    } else {
      // Regular JSON request
      customerData = req.body;
    }

    customerData = {
      ...customerData,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // Task 1: Convert customer name to uppercase (if provided)
    if (customerData.company_name && customerData.company_name.trim()) {
      customerData.company_name = customerData.company_name.toUpperCase();
    }

    // Generate customer code if not provided
    if (!customerData.customer_code) {
      const lastCustomer = await models.Customer.findOne({
        where: { tenant_id: req.user.tenant.id },
        order: [['created_at', 'DESC']],
        transaction,
      });

      const nextNumber = lastCustomer ?
        parseInt(lastCustomer.customer_code.replace(/\D/g, '')) + 1 : 1;
      customerData.customer_code = `CUST${nextNumber.toString().padStart(4, '0')}`;
    }

    // Check if customer code already exists
    const existingCustomer = await models.Customer.findOne({
      where: {
        tenant_id: req.user.tenant.id,
        customer_code: customerData.customer_code,
      },
      transaction,
    });

    if (existingCustomer) {
      await transaction.rollback();
      return res.status(409).json({
        success: false,
        message: 'Customer code already exists',
        errors: {
          customer_code: 'This customer code is already in use'
        }
      });
    }

    // Task 1: Check if customer name already exists (make it unique) - only if company name is provided
    if (customerData.company_name && customerData.company_name.trim()) {
      const existingCustomerName = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          company_name: customerData.company_name,
        },
        transaction,
      });

      if (existingCustomerName) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: `Customer name already exists with this tally serial NO: ${existingCustomerName.customer_code || existingCustomerName.tally_serial_number}`,
          errors: {
            company_name: `Customer name already exists with this tally serial NO: ${existingCustomerName.customer_code || existingCustomerName.tally_serial_number}`
          }
        });
      }
    }

    // Allow duplicate emails - removed email uniqueness validation

    // Check for duplicate emails in custom fields
    if (customerData.custom_fields) {
      const emailFields = [
        { field: 'admin_email', label: 'adminEmail' },
        { field: 'md_email', label: 'mdEmail' },
        { field: 'office_email', label: 'officeEmail' },
        { field: 'auditor_email', label: 'auditorEmail' },
        { field: 'tax_consultant_email', label: 'taxConsultantEmail' },
        { field: 'it_email', label: 'itEmail' }
      ];

      // Allow duplicate emails in custom fields - removed email uniqueness validation
    }

    // GST number can be duplicate, so no uniqueness check needed

    // Check if PAN number already exists (if provided)
    if (customerData.pan_number) {
      const existingPAN = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          pan_number: customerData.pan_number,
        },
        transaction,
      });

      if (existingPAN) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'PAN number already exists',
          errors: {
            pan_number: 'This PAN number is already registered'
          }
        });
      }
    }

    // Validate foreign key references
    if (customerData.industry_id) {
      const industry = await models.Industry.findOne({
        where: { id: customerData.industry_id }, // Remove tenant_id filter for Industry (master data)
        transaction,
      });
      if (!industry) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid industry selected',
          errors: {
            industry_id: 'Selected industry does not exist'
          }
        });
      }
    }

    if (customerData.area_id) {
      const area = await models.Area.findOne({
        where: { id: customerData.area_id, is_active: true },
        transaction,
      });
      if (!area) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid area selected',
          errors: {
            area_id: 'Selected area does not exist'
          }
        });
      }
    }

    if (customerData.assigned_executive_id) {
      const executive = await models.Executive.findOne({
        where: { id: customerData.assigned_executive_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!executive) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid executive selected',
          errors: {
            assigned_executive_id: 'Selected executive does not exist'
          }
        });
      }
    }

    const customer = await models.Customer.create(customerData, { transaction });

    // Handle file uploads if present
    if (req.files) {
      const fileData = {};

      // Process TDL & Addons file
      if (req.files.tdlAddonsFile && req.files.tdlAddonsFile[0]) {
        const file = req.files.tdlAddonsFile[0];
        fileData.tdl_addons_file = {
          filename: file.filename,
          originalName: file.originalname,
          path: file.path,
          size: file.size,
          mimetype: file.mimetype,
          url: `/uploads/customers/${customer.id}/${file.filename}`
        };
      }

      // Update customer with file information if any files were uploaded
      if (Object.keys(fileData).length > 0) {
        const updatedCustomFields = {
          ...customer.custom_fields,
          ...fileData
        };

        await customer.update({
          custom_fields: updatedCustomFields
        }, { transaction });
      }
    }

    // Fetch the created customer with associations
    const createdCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
          required: false, // Make it optional
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          required: false, // Make it optional
          where: {
            tenant_id: req.user.tenant.id // Only apply tenant filter to Executive
          }
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false, // Make it optional
        },
      ],
      transaction,
    });

    await transaction.commit();

    logger.info('Customer created successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      companyName: customer.company_name,
      createdBy: req.user.id,
      tenantId: req.user.tenant.id,
    });

    // Send welcome email to customer if email is provided
    if (createdCustomer.email) {
      try {
        console.log('📧 Sending welcome email to new customer:', {
          customerId: createdCustomer.id,
          customerCode: createdCustomer.customer_code,
          companyName: createdCustomer.company_name,
          email: createdCustomer.email
        });

        const customerEmailData = {
          id: createdCustomer.id,
          customer_code: createdCustomer.customer_code,
          company_name: createdCustomer.company_name,
          contact_person: createdCustomer.contact_person,
          phone: createdCustomer.phone,
          email: createdCustomer.email
        };

        const emailResult = await emailService.sendCustomerWelcomeEmail(customerEmailData);

        if (emailResult.success) {
          console.log('✅ Customer welcome email sent successfully:', {
            messageId: emailResult.messageId,
            customerEmail: createdCustomer.email
          });

          logger.info('Customer welcome email sent:', {
            customerId: createdCustomer.id,
            customerEmail: createdCustomer.email,
            messageId: emailResult.messageId
          });
        } else {
          console.log('❌ Customer welcome email failed:', emailResult.message);
          logger.warn('Customer welcome email failed:', {
            customerId: createdCustomer.id,
            customerEmail: createdCustomer.email,
            error: emailResult.error
          });
        }
      } catch (emailError) {
        console.error('❌ Customer welcome email error:', emailError);
        logger.error('Customer welcome email error:', {
          customerId: createdCustomer.id,
          customerEmail: createdCustomer.email,
          error: emailError.message,
          stack: emailError.stack
        });
        // Don't fail customer creation if email fails
      }
    } else {
      console.log('ℹ️ No email provided for customer - skipping welcome email');
    }

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer: createdCustomer },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Create customer error:', error);

    // Handle specific database errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = {};
      error.errors.forEach(err => {
        validationErrors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0]?.path || 'field';
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
        errors: {
          [field]: `This ${field} is already in use`
        }
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update customer
 */
export const updateCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;

    // Handle multipart form data (with files) or regular JSON
    let updateData;
    if (req.body.customerData) {
      // If customerData is a string (from FormData), parse it
      updateData = typeof req.body.customerData === 'string'
        ? JSON.parse(req.body.customerData)
        : req.body.customerData;
    } else {
      // Regular JSON request
      updateData = req.body;
    }

    updateData = {
      ...updateData,
      updated_by: req.user.id,
    };

    // Task 1: Convert customer name to uppercase (if provided)
    if (updateData.company_name && updateData.company_name.trim()) {
      updateData.company_name = updateData.company_name.toUpperCase();
    }

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer code is being changed and if it already exists
    if (updateData.customer_code && updateData.customer_code !== customer.customer_code) {
      const existingCustomer = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          customer_code: updateData.customer_code,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingCustomer) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'Customer code already exists',
          errors: {
            customer_code: 'This customer code is already in use'
          }
        });
      }
    }

    // Task 1: Check if customer name is being changed and if it already exists (only if company name is provided)
    if (updateData.company_name && updateData.company_name.trim() && updateData.company_name !== customer.company_name) {
      const existingCustomerName = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          company_name: updateData.company_name,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingCustomerName) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: `Customer name already exists with this tally serial NO: ${existingCustomerName.customer_code || existingCustomerName.tally_serial_number}`,
          errors: {
            company_name: `Customer name already exists with this tally serial NO: ${existingCustomerName.customer_code || existingCustomerName.tally_serial_number}`
          }
        });
      }
    }

    // Allow duplicate emails - removed email uniqueness validation for updates

    // Allow duplicate emails in custom fields during update - removed email uniqueness validation

    // GST number can be duplicate, so no uniqueness check needed

    // Check if PAN number is being changed and if it already exists
    if (updateData.pan_number && updateData.pan_number !== customer.pan_number) {
      const existingPAN = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          pan_number: updateData.pan_number,
          id: { [Op.ne]: id },
        },
        transaction,
      });

      if (existingPAN) {
        await transaction.rollback();
        return res.status(409).json({
          success: false,
          message: 'PAN number already exists',
          errors: {
            pan_number: 'This PAN number is already registered'
          }
        });
      }
    }

    // Validate foreign key references
    if (updateData.industry_id) {
      const industry = await models.Industry.findOne({
        where: { id: updateData.industry_id }, // Remove tenant_id filter for Industry (master data)
        transaction,
      });
      if (!industry) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid industry selected',
          errors: {
            industry_id: 'Selected industry does not exist'
          }
        });
      }
    }

    if (updateData.area_id) {
      const area = await models.Area.findOne({
        where: { id: updateData.area_id, is_active: true },
        transaction,
      });
      if (!area) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid area selected',
          errors: {
            area_id: 'Selected area does not exist'
          }
        });
      }
    }

    if (updateData.assigned_executive_id) {
      const executive = await models.Executive.findOne({
        where: { id: updateData.assigned_executive_id, tenant_id: req.user.tenant.id },
        transaction,
      });
      if (!executive) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid executive selected',
          errors: {
            assigned_executive_id: 'Selected executive does not exist'
          }
        });
      }
    }

    await customer.update(updateData, { transaction });

    // Handle file uploads if present
    if (req.files) {
      const fileData = {};

      // Process TDL & Addons file
      if (req.files.tdlAddonsFile && req.files.tdlAddonsFile[0]) {
        const file = req.files.tdlAddonsFile[0];
        fileData.tdl_addons_file = {
          filename: file.filename,
          originalName: file.originalname,
          path: file.path,
          size: file.size,
          mimetype: file.mimetype,
          url: `/uploads/customers/${customer.id}/${file.filename}`
        };
      }

      // Update customer with file information if any files were uploaded
      if (Object.keys(fileData).length > 0) {
        const updatedCustomFields = {
          ...customer.custom_fields,
          ...fileData
        };

        await customer.update({
          custom_fields: updatedCustomFields
        }, { transaction });
      }
    }

    // Fetch updated customer with associations
    const updatedCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
          required: false, // Make it optional
        },
        {
          model: models.Area,
          as: 'area',
          required: false, // Make it optional
          where: null, // Don't apply any where conditions
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          required: false, // Make it optional
          where: {
            tenant_id: req.user.tenant.id // Only apply tenant filter to Executive
          }
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false, // Make it optional
        },
      ],
      transaction,
    });

    await transaction.commit();

    logger.info('Customer updated successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer: updatedCustomer },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Update customer error:', {
      error: error.message,
      stack: error.stack,
      name: error.name,
      customerId: req.params.id,
      updateData: JSON.stringify(req.body, null, 2)
    });

    // Handle specific database errors
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = {};
      error.errors.forEach(err => {
        validationErrors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validationErrors,
      });
    }

    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = error.errors[0]?.path || 'field';
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
        errors: {
          [field]: `This ${field} is already in use`
        }
      });
    }

    if (error.name === 'SequelizeForeignKeyConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Invalid reference to related data',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete customer
 */
export const deleteCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const { force = false, reason } = req.query;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!customer) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer has related records
    // Note: Invoice model is for subscription billing, not customer invoices
    const [serviceCallsCount, salesCount, quotationsCount] = await Promise.all([
      models.ServiceCall?.count({ where: { customer_id: id }, transaction }) || 0,
      models.Sale?.count({ where: { customer_id: id }, transaction }) || 0,
      models.Quotation?.count({ where: { customer_id: id }, transaction }) || 0,
    ]);

    const hasRelatedRecords = serviceCallsCount > 0 || salesCount > 0 || quotationsCount > 0;

    if (hasRelatedRecords && !force) {
      await transaction.rollback();

      // Create user-friendly error message based on what records exist
      const relatedRecords = [];
      if (serviceCallsCount > 0) relatedRecords.push(`${serviceCallsCount} service call${serviceCallsCount > 1 ? 's' : ''}`);
      if (salesCount > 0) relatedRecords.push(`${salesCount} sales record${salesCount > 1 ? 's' : ''}`);
      if (quotationsCount > 0) relatedRecords.push(`${quotationsCount} quotation${quotationsCount > 1 ? 's' : ''}`);

      const userFriendlyMessage = `Cannot delete customer because they have ${relatedRecords.join(', ')}. Please complete or transfer all related records before deletion.`;

      return res.status(409).json({
        success: false,
        message: userFriendlyMessage,
        details: {
          serviceCallsCount,
          salesCount,
          quotationsCount,
        },
        suggestion: 'Complete or transfer all related records, or use force=true to perform soft delete instead',
        actionRequired: 'Please review and handle all related records before attempting deletion'
      });
    }

    if (force && hasRelatedRecords) {
      // Soft delete - mark as inactive instead of permanent deletion
      await customer.update({
        is_active: false,
        deleted_at: new Date(),
        deleted_by: req.user.id,
        deletion_reason: reason || 'Force deleted due to related records',
      }, { transaction });

      await transaction.commit();

      logger.info('Customer soft deleted successfully:', {
        customerId: customer.id,
        customerCode: customer.customer_code,
        deletedBy: req.user.id,
        reason: reason || 'Force deleted due to related records',
        relatedRecords: {
          serviceCallsCount,
          salesCount,
          quotationsCount,
        }
      });

      return res.json({
        success: true,
        message: 'Customer deactivated successfully (soft delete)',
        data: {
          type: 'soft_delete',
          reason: reason || 'Force deleted due to related records',
          relatedRecords: {
            serviceCallsCount,
            salesCount,
            quotationsCount,
          }
        }
      });
    }

    // Hard delete - permanent removal
    await customer.destroy({ transaction });
    await transaction.commit();

    logger.info('Customer permanently deleted successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      deletedBy: req.user.id,
      reason: reason || 'No related records found',
    });

    res.json({
      success: true,
      message: 'Customer deleted permanently',
      data: {
        type: 'hard_delete',
        reason: reason || 'No related records found'
      }
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Delete customer error:', error);

    res.status(500).json({
      success: false,
      message: 'Failed to delete customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer statistics
 */
export const getCustomerStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total customers (all active records)
      models.Customer.count({
        where: { tenant_id: tenantId, is_active: true },
      }),

      // Active customers (customer_type = 'customer')
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          customer_type: 'customer'
        },
      }),

      // Inactive customers (customer_type = 'inactive')
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          customer_type: 'inactive'
        },
      }),

      // Customers by type (for detailed breakdown)
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        attributes: [
          'customer_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['customer_type'],
        raw: true,
      }),

      // Recent customers (last 30 days)
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          created_at: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // Total revenue from AMC contracts (simplified)
      models.CustomerAMC.findOne({
        where: {
          tenant_id: tenantId,
          is_active: true
        },
        attributes: [
          [models.sequelize.fn('COALESCE',
            models.sequelize.fn('SUM', models.sequelize.col('contract_value')),
            0
          ), 'totalAmcRevenue']
        ],
        raw: true,
      }).catch(() => ({ totalAmcRevenue: 0 })),

      // Placeholder for payment revenue (simplified for now)
      Promise.resolve({ totalPayments: 0 }),

      // Customers by industry
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        include: [
          {
            model: models.Industry,
            as: 'industry',
            attributes: ['name'],
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('Customer.id')), 'count'],
        ],
        group: ['industry.id', 'industry.name'],
        raw: true,
      }),
    ]);

    const [
      totalCustomers,
      activeCustomers,
      inactiveCustomers,
      customersByType,
      recentCustomers,
      amcRevenue,
      paymentRevenue,
      customersByIndustry
    ] = stats;

    // Calculate total revenue
    const totalAmcRevenue = parseFloat(amcRevenue?.totalAmcRevenue || 0);
    const totalPaymentRevenue = parseFloat(paymentRevenue?.totalPayments || 0);
    const totalRevenue = totalAmcRevenue + totalPaymentRevenue;



    res.json({
      success: true,
      data: {
        totalCustomers,
        activeCustomers,
        inactiveCustomers,
        recentCustomers,
        totalRevenue,
        revenueBreakdown: {
          amcRevenue: totalAmcRevenue,
          paymentRevenue: totalPaymentRevenue
        },
        customersByType: customersByType.reduce((acc, item) => {
          acc[item.customer_type] = parseInt(item.count);
          return acc;
        }, {}),
        customersByIndustry: customersByIndustry.map(item => ({
          industry: item['industry.name'] || 'Unknown',
          count: parseInt(item.count),
        })),
      },
    });

  } catch (error) {
    logger.error('Get customer stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get individual customer statistics (services and payments)
 */
export const getCustomerStatistics = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    // Verify customer exists and belongs to tenant
    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: tenantId,
        is_active: true,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Calculate service statistics
    const serviceStats = await Promise.all([
      // Total services count
      models.ServiceCall.count({
        where: {
          customer_id: id,
          tenant_id: tenantId,
        },
      }),

      // Services by status
      models.ServiceCall.findAll({
        where: {
          customer_id: id,
          tenant_id: tenantId,
        },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'code', 'category'],
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
        ],
        group: ['status.id', 'status.name', 'status.code', 'status.category'],
        raw: true,
      }),

      // Total revenue (sum of service charges and total amounts)
      models.ServiceCall.findOne({
        where: {
          customer_id: id,
          tenant_id: tenantId,
        },
        attributes: [
          [
            models.sequelize.fn(
              'COALESCE',
              models.sequelize.fn('SUM',
                models.sequelize.fn('COALESCE', models.sequelize.col('service_charges'), 0)
              ),
              0
            ),
            'total_service_charges'
          ],
          [
            models.sequelize.fn(
              'COALESCE',
              models.sequelize.fn('SUM',
                models.sequelize.fn('COALESCE', models.sequelize.col('total_amount'), 0)
              ),
              0
            ),
            'total_amount_sum'
          ],
        ],
        raw: true,
      }),

      // Last service date
      models.ServiceCall.findOne({
        where: {
          customer_id: id,
          tenant_id: tenantId,
        },
        attributes: ['call_date', 'created_at'],
        order: [['call_date', 'DESC']],
        raw: true,
      }),

      // Average service value and total time spent
      models.ServiceCall.findOne({
        where: {
          customer_id: id,
          tenant_id: tenantId,
        },
        attributes: [
          [
            models.sequelize.fn(
              'COALESCE',
              models.sequelize.fn('AVG',
                models.sequelize.fn('COALESCE', models.sequelize.col('service_charges'), 0)
              ),
              0
            ),
            'avg_service_value'
          ],
          [
            models.sequelize.fn(
              'COALESCE',
              models.sequelize.fn('SUM',
                models.sequelize.fn('COALESCE', models.sequelize.col('total_time_minutes'), 0)
              ),
              0
            ),
            'total_time_minutes'
          ],
          [
            models.sequelize.fn(
              'COALESCE',
              models.sequelize.fn('SUM',
                models.sequelize.fn('COALESCE', models.sequelize.col('actual_hours'), 0)
              ),
              0
            ),
            'total_hours_spent'
          ],
        ],
        raw: true,
      }),
    ]);

    const [
      totalServices,
      servicesByStatus,
      revenueData,
      lastServiceData,
      averageData,
    ] = serviceStats;

    // Process service status breakdown
    const statusBreakdown = {
      completed: 0,
      pending: 0,
      in_progress: 0,
      cancelled: 0,
      on_hold: 0,
      others: 0,
    };

    servicesByStatus.forEach(item => {
      const statusCode = item['status.code']?.toLowerCase();
      const count = parseInt(item.count);

      switch (statusCode) {
        case 'completed':
          statusBreakdown.completed = count;
          break;
        case 'pending':
          statusBreakdown.pending = count;
          break;
        case 'progress':
        case 'in_progress':
          statusBreakdown.in_progress = count;
          break;
        case 'cancelled':
          statusBreakdown.cancelled = count;
          break;
        case 'on_hold':
        case 'hold':
          statusBreakdown.on_hold = count;
          break;
        default:
          statusBreakdown.others += count;
          break;
      }
    });

    // Calculate revenue metrics
    const totalRevenue = Math.max(
      parseFloat(revenueData?.total_service_charges || 0),
      parseFloat(revenueData?.total_amount_sum || 0)
    );

    // For now, assume all revenue is paid (we can enhance this later with payment tracking)
    const totalPaid = totalRevenue;
    const pendingAmount = 0; // This would come from actual payment records

    // Calculate average service value
    const avgServiceValue = totalServices > 0 ? (totalRevenue / totalServices) : 0;

    // Format last service date
    const lastServiceDate = lastServiceData?.call_date || lastServiceData?.created_at;
    const formattedLastServiceDate = lastServiceDate
      ? new Date(lastServiceDate).toLocaleDateString('en-IN')
      : 'N/A';

    // Calculate total time spent
    const totalTimeMinutes = parseInt(averageData?.total_time_minutes || 0);
    const totalHoursSpent = parseFloat(averageData?.total_hours_spent || 0);

    const statistics = {
      services: {
        totalServices,
        statusBreakdown,
        lastServiceDate: formattedLastServiceDate,
        totalTimeMinutes,
        totalHoursSpent: parseFloat(totalHoursSpent.toFixed(2)),
      },
      payments: {
        totalRevenue: parseFloat(totalRevenue.toFixed(2)),
        totalPaid: parseFloat(totalPaid.toFixed(2)),
        pendingAmount: parseFloat(pendingAmount.toFixed(2)),
        averageServiceValue: parseFloat(avgServiceValue.toFixed(2)),
      },
      summary: {
        totalServices,
        completedServices: statusBreakdown.completed,
        pendingServices: statusBreakdown.pending,
        inProgressServices: statusBreakdown.in_progress,
        totalRevenue: parseFloat(totalRevenue.toFixed(2)),
        pendingAmount: parseFloat(pendingAmount.toFixed(2)),
        lastServiceDate: formattedLastServiceDate,
      },
    };

    res.json({
      success: true,
      data: statistics,
    });

  } catch (error) {
    logger.error('Get customer statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Find customer by serial number (customer_code)
 */
export const getCustomerBySerialNumber = async (req, res) => {
  try {
    const { serialNumber } = req.params;

    if (!serialNumber || serialNumber.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Customer serial number must be at least 2 characters long'
      });
    }

    const tenantId = req.user.tenant.id;

    // Search for customer by customer_code (serial number)
    const customer = await models.Customer.findOne({
      where: {
        tenant_id: tenantId,
        customer_code: serialNumber.trim().toUpperCase()
      },
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name', 'code'],
          required: false,
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name', 'code', 'city', 'state'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found with the provided serial number'
      });
    }

    res.json({
      success: true,
      data: { customer }
    });

  } catch (error) {
    logger.error('Get customer by serial number error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer by serial number',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Search MD contact information for auto-fill functionality
 */
export const searchMDContact = async (req, res) => {
  try {
    const { name } = req.query;

    if (!name || name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'MD contact name must be at least 2 characters long'
      });
    }

    const tenantId = req.user.tenant.id;

    // Search for existing customers with similar MD contact person name
    const customers = await models.Customer.findAll({
      where: {
        tenant_id: tenantId,
        'custom_fields.md_contact_person': {
          [Op.iLike]: `%${name.trim()}%`
        }
      },
      attributes: ['custom_fields'],
      limit: 1,
      order: [['updated_at', 'DESC']]
    });

    if (customers.length > 0 && customers[0].custom_fields?.md_phone_no) {
      return res.json({
        success: true,
        data: {
          mdPhoneNo: customers[0].custom_fields.md_phone_no,
          mdEmail: customers[0].custom_fields.md_email || null
        }
      });
    }

    return res.json({
      success: false,
      message: 'No matching MD contact found'
    });

  } catch (error) {
    console.error('Error searching MD contact:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search MD contact',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Preview customer import data
 */
export const previewCustomerImport = async (req, res) => {
  try {
    logger.info('Preview customer import request received', {
      hasFile: !!req.file,
      fileDetails: req.file ? {
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        path: req.file.path
      } : null,
      userId: req.user?.id,
      tenantId: req.user?.tenant?.id
    });

    if (!req.file) {
      logger.warn('No file uploaded in import request');
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    logger.info('Processing Excel file:', req.file.path);
    const XLSX = await import('xlsx');
    const workbook = XLSX.default.readFile(req.file.path);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.default.utils.sheet_to_json(worksheet);

    logger.info('Excel data extracted:', {
      sheetName,
      rowCount: data.length,
      firstRowKeys: data.length > 0 ? Object.keys(data[0]) : []
    });

    // Clean up uploaded file
    const fs = await import('fs');
    fs.default.unlinkSync(req.file.path);

    if (data.length === 0) {
      logger.warn('Excel file is empty');
      return res.status(400).json({
        success: false,
        message: 'File is empty or has no valid data'
      });
    }

    // Validate and process data
    logger.info('Starting data validation...');
    const tenantId = req.user.tenant?.id || req.user.tenantId;

    if (!tenantId) {
      logger.error('No tenant ID found for user:', {
        userId: req.user.id,
        userObject: req.user
      });
      return res.status(400).json({
        success: false,
        message: 'User tenant information is missing. Please contact administrator.'
      });
    }

    const validationResults = await validateImportData(data, tenantId, false);
    logger.info('Validation completed:', {
      validRows: validationResults.validRows,
      errorRows: validationResults.errorRows,
      duplicateRows: validationResults.duplicateRows
    });

    // Get column information from the first row
    const columnInfo = data.length > 0 ? Object.keys(data[0]) : [];

    // Define column mapping
    const columnMapping = getColumnMapping();

    res.json({
      success: true,
      data: {
        totalRows: data.length,
        validRows: validationResults.validRows,
        errorRows: validationResults.errorRows,
        duplicateRows: validationResults.duplicateRows,
        preview: validationResults.preview,
        errors: validationResults.errors,
        columns: columnInfo,
        columnMapping: columnMapping,
        validationRules: getValidationRules()
      }
    });

  } catch (error) {
    logger.error('Preview customer import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to preview import data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Execute customer import
 */
export const executeCustomerImport = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    const XLSX = await import('xlsx');
    const workbook = XLSX.default.readFile(req.file.path);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.default.utils.sheet_to_json(worksheet);

    // Clean up uploaded file
    const fs = await import('fs');
    fs.default.unlinkSync(req.file.path);

    if (data.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'File is empty or has no valid data'
      });
    }

    // Validate data again
    const tenantId = req.user.tenant?.id || req.user.tenantId;

    if (!tenantId) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'User tenant information is missing. Please contact administrator.'
      });
    }

    const forceImportErrors = req.body.forceImportErrors === 'true';
    const validationResults = await validateImportData(data, tenantId, forceImportErrors);

    if (validationResults.validRows === 0 && !forceImportErrors) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'No valid rows to import',
        errors: validationResults.errors
      });
    }

    // Import data (valid data or all data if force import)
    const dataToImport = forceImportErrors ? validationResults.allData : validationResults.validData;
    const importResults = await importCustomerData(
      dataToImport,
      req.user,
      transaction,
      forceImportErrors
    );

    await transaction.commit();

    res.json({
      success: true,
      data: {
        successCount: importResults.successCount,
        errorCount: importResults.errorCount,
        skippedCount: importResults.skippedCount,
        errors: importResults.errors,
        successDetails: importResults.successDetails
      }
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Execute customer import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Export customers data to CSV/Excel format
 */
export const exportCustomers = async (req, res) => {
  try {
    const {
      format = 'csv',
      search,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
      isActive,
      // Filter parameters
      amcStatus,
      amcExpiryDate,
      tssStatus,
      tssExpiryDate,
      licenseEdition,
      productType,
      tdlAddons,
      tdlExpiryDate,
      autoBackup,
      autoBackupExpiryDate,
      cloudUser,
      cloudUserExpiryDate,
      mobileApp,
      mobileAppExpiryDate,
      whatsappGroup,
      whatsappExpiryDate,
    } = req.query;

    // Build where conditions (same as getCustomers)
    const whereConditions = {
      tenant_id: req.user.tenant.id,
    };

    if (search) {
      whereConditions[Op.or] = [
        { company_name: { [Op.iLike]: `%${search}%` } },
        { customer_code: { [Op.iLike]: `%${search}%` } },
        { contact_person: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } },
        { tally_serial_number: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (customerType) {
      whereConditions.customer_type = customerType;
    }

    if (industryId) {
      whereConditions.industry_id = industryId;
    }

    if (areaId) {
      whereConditions.area_id = areaId;
    }

    if (assignedExecutiveId) {
      whereConditions.assigned_executive_id = assignedExecutiveId;
    }

    if (isActive !== undefined) {
      whereConditions.is_active = isActive === 'true';
    }

    // Add custom fields filters
    const customFieldsFilters = {};

    if (amcStatus && amcStatus !== 'all') {
      customFieldsFilters['amc_status'] = amcStatus;
    }

    if (tssStatus && tssStatus !== 'all') {
      customFieldsFilters['tss_status'] = tssStatus;
    }

    if (licenseEdition && licenseEdition !== 'all') {
      customFieldsFilters['license_edition'] = licenseEdition;
    }

    if (productType && productType !== 'all') {
      customFieldsFilters['product_type'] = productType;
    }

    // Add custom fields filters to where conditions
    if (Object.keys(customFieldsFilters).length > 0) {
      Object.keys(customFieldsFilters).forEach(key => {
        whereConditions[`custom_fields.${key}`] = customFieldsFilters[key];
      });
    }

    // Fetch all customers with complete data (no pagination for export)
    const customers = await models.Customer.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
      order: [['created_at', 'DESC']],
    });

    // Transform data to match import template structure
    const exportData = customers.map(customer => {
      const customFields = customer.custom_fields || {};

      return {
        // Core Mandatory Fields
        'Company Name*': customer.company_name || '',
        'Customer Code*': customer.customer_code || '',
        'Email*': customer.email || '',
        'Phone*': customer.phone || '',
        'Tally Serial Number*': customer.tally_serial_number || '',

        // Optional Basic Fields
        'Display Name': customer.display_name || '',
        'Customer Type': customer.customer_type || '',
        'Business Type': customer.business_type || '',

        // Address Information
        'Address Line 1': customer.address_line_1 || '',
        'Address Line 2': customer.address_line_2 || '',
        'City': customer.city || '',
        'State': customer.state || '',
        'Country': customer.country || '',
        'PIN Code': customer.postal_code || customer.pincode || '',

        // Business Details
        'GST Number': customer.gst_number || '',
        'PAN Number': customer.pan_number || '',
        'Annual Turnover': customer.annual_turnover || '',
        'Employee Count': customer.employee_count || '',
        'Credit Limit': customer.credit_limit || '',
        'Credit Days': customer.credit_days || '',
        'Payment Terms': customer.payment_terms || '',

        // Banking Information
        'Bank Name': customer.bank_name || '',
        'Bank Account Number': customer.bank_account_number || '',
        'Bank IFSC Code': customer.bank_ifsc_code || '',

        // Contact Information (from custom_fields) - Match import template exactly
        'Admin Email*': customFields.admin_email || '',
        'MD Contact Person*': customFields.md_contact_person || '',
        'MD Phone Number*': customFields.md_phone_no || '',
        'MD Email*': customFields.md_email || '',
        'Office Contact Person*': customFields.office_contact_person || '',
        'Office Mobile Number*': customFields.office_mobile_no || '',
        'Office Email*': customFields.office_email || '',

        // Professional Contacts (from custom_fields) - Match import template exactly
        'Auditor Name*': customFields.auditor_name || '',
        'Auditor Number*': customFields.auditor_number || '',
        'Auditor Email*': customFields.auditor_email || '',
        'Tax Consultant Name*': customFields.tax_consultant_name || '',
        'Tax Consultant Number*': customFields.tax_consultant_number || '',
        'Tax Consultant Email*': customFields.tax_consultant_email || '',
        'IT Name': customFields.it_name || '',
        'IT Number': customFields.it_number || '',
        'IT Email': customFields.it_email || '',

        // Business Information (from custom_fields and relationships) - Match import template exactly
        'Area*': customer.area?.name || customFields.area || '',
        'Industry': customer.industry?.name || customFields.industry || '',
        'Number of Tally Users*': customFields.number_of_tally_users || '',
        'Executive Name*': customer.assignedExecutive ?
          `${customer.assignedExecutive.first_name} ${customer.assignedExecutive.last_name}`.trim() :
          customFields.executive_name || '',
        'Status*': customFields.status || (customer.is_active ? 'ACTIVE' : 'INACTIVE'),
        'Profile Status': customFields.profile_status || '',
        'Customer Status': customFields.customer_status || '',

        // Tally Information
        'Tally Version': customer.tally_version || '',
        'License Type': customer.license_type || '',
        'License Edition': customFields.license_edition || '',
        'Product': customFields.product || '',
        'Product Type': customFields.product_type || '',

        // Service Information (from custom_fields)
        'AMC Status': customFields.amc_status || '',
        'AMC Expiry Date': customFields.amc_expiry_date || '',
        'TSS Status': customFields.tss_status || '',
        'TSS Expiry Date': customFields.tss_expiry_date || '',
        'TDL Addons': customFields.tdl_addons || '',
        'TDL Expiry Date': customFields.tdl_expiry_date || '',
        'Auto Backup': customFields.auto_backup || '',
        'Auto Backup Expiry Date': customFields.auto_backup_expiry_date || '',
        'Cloud User': customFields.cloud_user || '',
        'Cloud User Expiry Date': customFields.cloud_user_expiry_date || '',
        'Mobile App': customFields.mobile_app || '',
        'Mobile App Expiry Date': customFields.mobile_app_expiry_date || '',
        'WhatsApp Group': customFields.whatsapp_group || '',
        'WhatsApp Expiry Date': customFields.whatsapp_expiry_date || '',

        // Additional Information
        'Website': customer.website || '',
        'Lead Source': customer.lead_source || '',
        'Referred By': customer.referred_by || '',
        'First Contact Date': customer.first_contact_date || '',
        'Last Contact Date': customer.last_contact_date || '',
        'Next Follow Up Date': customer.next_follow_up_date || '',
        'Installation Date': customer.installation_date || '',
        'Notes': customer.notes || '',
        'Tags': Array.isArray(customer.tags) ? customer.tags.join(', ') : '',

        // System Information
        'Created Date': customer.created_at ? new Date(customer.created_at).toLocaleDateString() : '',
        'Created By': customer.creator ?
          `${customer.creator.first_name} ${customer.creator.last_name}`.trim() : '',
        'Updated Date': customer.updated_at ? new Date(customer.updated_at).toLocaleDateString() : '',
        'Active': customer.is_active ? 'Yes' : 'No',

        // Notification Settings
        'SMS Notifications': customer.notification_sms ? 'Yes' : 'No',
        'Email Notifications': customer.notification_email ? 'Yes' : 'No',
        'WhatsApp Notifications': customer.notification_whatsapp ? 'Yes' : 'No',
      };
    });

    if (format === 'json') {
      return res.json({
        success: true,
        data: {
          customers: exportData,
          totalCount: exportData.length,
          exportedAt: new Date().toISOString(),
        },
      });
    }

    // Generate CSV content
    if (exportData.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No customers found to export',
      });
    }

    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => {
          const value = row[header] || '';
          // Escape quotes and wrap in quotes if contains comma, quote, or newline
          const stringValue = value.toString();
          if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return `"${stringValue.replace(/"/g, '""')}"`;
          }
          return stringValue;
        }).join(',')
      )
    ].join('\n');

    // Set response headers for file download
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `customers_export_${timestamp}.csv`;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Pragma', 'no-cache');

    res.send(csvContent);

  } catch (error) {
    logger.error('Export customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Helper function to validate import data
const validateImportData = async (data, tenantId, forceImport = false) => {
  const errors = [];
  const validData = [];
  const allData = []; // For force import
  const preview = [];
  let validRows = 0;
  let errorRows = 0;
  let duplicateRows = 0;

  // Get existing customer codes to check for duplicates
  const existingCustomers = await models.Customer.findAll({
    where: { tenant_id: tenantId },
    attributes: ['customer_code', 'company_name'],
    raw: true
  });

  const existingCodes = new Set(existingCustomers.map(c => c.customer_code?.toLowerCase()));
  const existingNames = new Set(existingCustomers.map(c => c.company_name?.toLowerCase()));

  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNumber = i + 2; // Excel row number (accounting for header)
    const rowErrors = [];
    let hasErrors = false;

    // Required field validation - only the core mandatory fields
    const companyName = row['Company Name*'] || row.company_name;
    const customerCode = row['Customer Code*'] || row.customer_code;
    const email = row['Email*'] || row.email;
    const phone = row['Phone*'] || row.phone;
    const tallySerialNumber = row['Tally Serial Number*'] || row.tally_serial_number;

    if (!companyName || (typeof companyName === 'string' && companyName.trim() === '')) {
      rowErrors.push('Company Name is required');
      hasErrors = true;
    }

    if (!customerCode || (typeof customerCode === 'string' && customerCode.trim() === '')) {
      rowErrors.push('Customer Code is required');
      hasErrors = true;
    }

    if (!email || (typeof email === 'string' && email.trim() === '')) {
      rowErrors.push('Email is required');
      hasErrors = true;
    }

    if (!phone || (typeof phone === 'string' && phone.trim() === '')) {
      rowErrors.push('Phone is required');
      hasErrors = true;
    }

    if (!tallySerialNumber || (typeof tallySerialNumber === 'string' && tallySerialNumber.trim() === '')) {
      rowErrors.push('Tally Serial Number is required');
      hasErrors = true;
    }

    // Check for duplicates
    if (customerCode && typeof customerCode === 'string' && existingCodes.has(customerCode.toLowerCase())) {
      rowErrors.push(`Customer Code '${customerCode}' already exists`);
      hasErrors = true;
      duplicateRows++;
    }

    if (companyName && typeof companyName === 'string' && existingNames.has(companyName.toLowerCase())) {
      rowErrors.push(`Company Name '${companyName}' already exists`);
      hasErrors = true;
      duplicateRows++;
    }

    // Email validation for required email
    if (email && typeof email === 'string' && !/\S+@\S+\.\S+/.test(email)) {
      rowErrors.push('Invalid email format');
      hasErrors = true;
    }

    // Validate optional email fields
    const optionalEmails = [
      { field: 'Admin Email', value: row['Admin Email'] },
      { field: 'MD Email', value: row['MD Email'] },
      { field: 'Office Email', value: row['Office Email'] },
      { field: 'Auditor Email', value: row['Auditor Email'] },
      { field: 'Tax Consultant Email', value: row['Tax Consultant Email'] },
      { field: 'IT Email', value: row['IT Email'] }
    ];

    optionalEmails.forEach(({ field, value }) => {
      if (value && typeof value === 'string' && value.trim() && !/\S+@\S+\.\S+/.test(value)) {
        rowErrors.push(`Invalid ${field} format`);
        hasErrors = true;
      }
    });

    // GST validation
    const gstNumber = row['GST Number'] || row.gst_number;
    if (gstNumber && typeof gstNumber === 'string' && gstNumber.length !== 15) {
      rowErrors.push('GST Number must be 15 characters');
      hasErrors = true;
    }

    // PAN validation
    const panNumber = row['PAN Number'] || row.pan_number;
    if (panNumber && typeof panNumber === 'string' && panNumber.length !== 10) {
      rowErrors.push('PAN Number must be 10 characters');
      hasErrors = true;
    }

    // Always add to allData for force import option
    allData.push({
      ...row,
      rowNumber,
      hasErrors,
      errors: rowErrors
    });

    if (hasErrors) {
      errorRows++;
      rowErrors.forEach(error => {
        errors.push({
          row: rowNumber,
          message: error,
          field: 'validation'
        });
      });

      // If force import, also add to validData
      if (forceImport) {
        validRows++;
        validData.push({
          ...row,
          rowNumber,
          hasErrors,
          errors: rowErrors
        });
      }
    } else {
      validRows++;
      validData.push({
        ...row,
        rowNumber
      });
    }

    // Add to preview (first 20 rows)
    if (preview.length < 20) {
      preview.push({
        data: row,
        hasErrors,
        errors: rowErrors
      });
    }
  }

  return {
    validRows,
    errorRows,
    duplicateRows,
    validData,
    allData,
    preview,
    errors
  };
};

// Helper function to import customer data
const importCustomerData = async (validData, user, transaction, forceImport = false) => {
  const results = {
    successCount: 0,
    errorCount: 0,
    skippedCount: 0,
    errors: [],
    successDetails: []
  };

  for (const row of validData) {
    try {
      // Skip rows with validation errors unless force import
      if (row.hasErrors && !forceImport) {
        results.skippedCount++;
        results.errors.push({
          row: row.rowNumber,
          message: 'Skipped due to validation errors: ' + (row.errors?.join(', ') || 'Unknown error')
        });
        continue;
      }
      // Build customer data according to the actual customer model structure
      const customerData = {
        // Required fields
        tenant_id: user.tenant?.id || user.tenantId,
        company_name: row['Company Name*'] || row.company_name,
        customer_code: row['Customer Code*'] || row.customer_code,
        display_name: row['Display Name'] || row['Company Name*'] || row.company_name,
        email: row['Email*'] || row.email,
        phone: row['Phone*'] || row.phone,
        tally_serial_number: row['Tally Serial Number*'] || row.tally_serial_number,

        // Optional basic fields
        customer_type: row['Customer Type'] || 'customer',
        business_type: row['Business Type'] || 'private_limited',

        // Address fields
        address_line_1: row['Address Line 1'] || null,
        city: row.City || null,
        state: row.State || null,
        country: row.Country || 'India',
        postal_code: row['PIN Code'] || null,
        gst_number: row['GST Number'] || null,
        pan_number: row['PAN Number'] || null,

        // System fields
        is_active: true,
        created_by: user.id,
        updated_by: user.id,

        // Custom fields JSON structure
        custom_fields: {
          // Profile status
          profile_status: row['Profile Status'] || 'FOLLOW UP',
          customer_status: row['Customer Status'] || 'ACTIVE',

          // Contact information
          admin_email: row['Admin Email'] || '',
          md_contact_person: row['MD Contact Person'] || '',
          md_phone_no: row['MD Phone Number'] || '',
          md_email: row['MD Email'] || '',
          office_contact_person: row['Office Contact Person'] || '',
          office_mobile_no: row['Office Mobile Number'] || '',
          office_email: row['Office Email'] || '',

          // Professional contacts
          auditor_name: row['Auditor Name'] || '',
          auditor_no: row['Auditor Number'] || '',
          auditor_email: row['Auditor Email'] || '',
          tax_consultant_name: row['Tax Consultant Name'] || '',
          tax_consultant_no: row['Tax Consultant Number'] || '',
          tax_consultant_email: row['Tax Consultant Email'] || '',

          // IT contact
          it_name: row['IT Name'] || '',
          it_no: row['IT Number'] || '',
          it_email: row['IT Email'] || '',

          // Business details
          area: row.Area || '',
          pin_code: row['PIN Code'] || '',
          state_country: row.State ? `${row.State}, ${row.Country || 'India'}` : '',
          no_of_tally_users: row['Number of Tally Users'] ? parseInt(row['Number of Tally Users']) : null,
          executive_name: row['Executive Name'] || '',
          status: row.Status || '',

          // Address book - create from primary contact
          address_book: [
            {
              type: 'owner',
              contact_person: row['MD Contact Person'] || row['Company Name*'] || row.company_name,
              mobile_numbers: [row['Phone*'] || row.phone],
              phone: row['Phone*'] || row.phone,
              email: row['Email*'] || row.email,
              is_mandatory: false
            }
          ],

          // Default service fields
          tss_status: 'NO',
          tss_expiry_date: null,
          amc_status: 'NO',
          amc_from_date: null,
          amc_to_date: null,
          renewal_date: null,
          no_of_visits: null,
          current_amc_amount: null,
          last_year_amc_amount: null,
          tdl_addons: false,
          tdl_addons_expiry_date: null,
          whatsapp_telegram_group: false,
          whatsapp_telegram_group_expiry_date: null,
          auto_backup: false,
          auto_backup_expiry_date: null,
          cloud_user: false,
          cloud_user_expiry_date: null,
          mobile_app: false,
          mobile_app_expiry_date: null
        }
      };

      const customer = await models.Customer.create(customerData, { transaction });

      results.successCount++;
      results.successDetails.push({
        company_name: customer.company_name,
        customer_code: customer.customer_code,
        id: customer.id
      });

    } catch (error) {
      results.errorCount++;
      results.errors.push({
        row: row.rowNumber,
        message: error.message || 'Failed to create customer'
      });
    }
  }

  return results;
};

// Helper function to get column mapping information
const getColumnMapping = () => {
  return {
    // Core mandatory fields (database level)
    'Company Name*': {
      dbField: 'company_name',
      required: true,
      description: 'Company or business name',
      dataType: 'string',
      maxLength: 200,
      transformation: 'none'
    },
    'Customer Code*': {
      dbField: 'customer_code',
      required: true,
      description: 'Unique customer identifier',
      dataType: 'string',
      maxLength: 20,
      transformation: 'none'
    },
    'Display Name': {
      dbField: 'display_name',
      required: false,
      description: 'Display name (defaults to company name)',
      dataType: 'string',
      maxLength: 200,
      transformation: 'none'
    },
    'Email*': {
      dbField: 'email',
      required: true,
      description: 'Primary contact email',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'Phone*': {
      dbField: 'phone',
      required: true,
      description: 'Primary contact phone number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'Tally Serial Number*': {
      dbField: 'tally_serial_number',
      required: true,
      description: 'Tally software serial number',
      dataType: 'string',
      maxLength: 50,
      transformation: 'none'
    },
    // Optional basic fields
    'Customer Type': {
      dbField: 'customer_type',
      required: false,
      description: 'Customer type',
      dataType: 'enum',
      allowedValues: ['prospect', 'customer', 'inactive', 'blacklisted'],
      defaultValue: 'customer',
      transformation: 'lowercase'
    },
    'Business Type': {
      dbField: 'business_type',
      required: false,
      description: 'Type of business entity',
      dataType: 'enum',
      allowedValues: ['proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'],
      defaultValue: 'private_limited',
      transformation: 'lowercase'
    },

    // Address fields
    'Address Line 1': {
      dbField: 'address_line_1',
      required: false,
      description: 'Primary address line',
      dataType: 'string',
      maxLength: 255,
      transformation: 'none'
    },
    'City': {
      dbField: 'city',
      required: false,
      description: 'City name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'State': {
      dbField: 'state',
      required: false,
      description: 'State or province',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Country': {
      dbField: 'country',
      required: false,
      description: 'Country name',
      dataType: 'string',
      maxLength: 100,
      defaultValue: 'India',
      transformation: 'none'
    },
    'PIN Code': {
      dbField: 'postal_code',
      required: false,
      description: 'Postal or ZIP code',
      dataType: 'string',
      maxLength: 10,
      transformation: 'none'
    },
    'GST Number': {
      dbField: 'gst_number',
      required: false,
      description: 'GST registration number',
      dataType: 'string',
      exactLength: 15,
      transformation: 'uppercase'
    },
    'PAN Number': {
      dbField: 'pan_number',
      required: false,
      description: 'PAN card number',
      dataType: 'string',
      exactLength: 10,
      transformation: 'uppercase'
    },
    // Custom fields (stored in custom_fields JSON)
    'Admin Email': {
      dbField: 'custom_fields.admin_email',
      required: false,
      description: 'Admin email address',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'MD Contact Person': {
      dbField: 'custom_fields.md_contact_person',
      required: false,
      description: 'Managing Director contact person',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'MD Phone Number': {
      dbField: 'custom_fields.md_phone_no',
      required: false,
      description: 'Managing Director phone number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'MD Email': {
      dbField: 'custom_fields.md_email',
      required: false,
      description: 'Managing Director email',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'Office Contact Person': {
      dbField: 'custom_fields.office_contact_person',
      required: false,
      description: 'Office contact person',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Office Mobile Number': {
      dbField: 'custom_fields.office_mobile_no',
      required: false,
      description: 'Office mobile number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'Office Email': {
      dbField: 'custom_fields.office_email',
      required: false,
      description: 'Office email address',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'Auditor Name': {
      dbField: 'custom_fields.auditor_name',
      required: false,
      description: 'Auditor name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Auditor Number': {
      dbField: 'custom_fields.auditor_no',
      required: false,
      description: 'Auditor phone number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'Auditor Email': {
      dbField: 'custom_fields.auditor_email',
      required: false,
      description: 'Auditor email address',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'Tax Consultant Name': {
      dbField: 'custom_fields.tax_consultant_name',
      required: false,
      description: 'Tax consultant name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Tax Consultant Number': {
      dbField: 'custom_fields.tax_consultant_no',
      required: false,
      description: 'Tax consultant phone number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'Tax Consultant Email': {
      dbField: 'custom_fields.tax_consultant_email',
      required: false,
      description: 'Tax consultant email address',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'IT Name': {
      dbField: 'custom_fields.it_name',
      required: false,
      description: 'IT contact person name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'IT Number': {
      dbField: 'custom_fields.it_no',
      required: false,
      description: 'IT contact phone number',
      dataType: 'phone',
      maxLength: 20,
      transformation: 'none'
    },
    'IT Email': {
      dbField: 'custom_fields.it_email',
      required: false,
      description: 'IT contact email address',
      dataType: 'email',
      maxLength: 255,
      transformation: 'lowercase'
    },
    'Area': {
      dbField: 'custom_fields.area',
      required: false,
      description: 'Business area or region',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Number of Tally Users': {
      dbField: 'custom_fields.no_of_tally_users',
      required: false,
      description: 'Number of Tally software users',
      dataType: 'integer',
      minValue: 1,
      transformation: 'none'
    },
    'Executive Name': {
      dbField: 'custom_fields.executive_name',
      required: false,
      description: 'Assigned sales executive name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Status': {
      dbField: 'custom_fields.status',
      required: false,
      description: 'Customer status',
      dataType: 'string',
      maxLength: 50,
      transformation: 'uppercase'
    },
    'Profile Status': {
      dbField: 'custom_fields.profile_status',
      required: false,
      description: 'Customer profile status',
      dataType: 'string',
      maxLength: 50,
      defaultValue: 'FOLLOW UP',
      transformation: 'uppercase'
    },
    'Customer Status': {
      dbField: 'custom_fields.customer_status',
      required: false,
      description: 'Overall customer status',
      dataType: 'string',
      maxLength: 50,
      defaultValue: 'ACTIVE',
      transformation: 'uppercase'
    },
    'Address Line 1': {
      dbField: 'address_line_1',
      required: false,
      description: 'Primary address line',
      dataType: 'string',
      maxLength: 255,
      transformation: 'none'
    },
    'Address Line 2': {
      dbField: 'address_line_2',
      required: false,
      description: 'Secondary address line',
      dataType: 'string',
      maxLength: 255,
      transformation: 'none'
    },
    'City': {
      dbField: 'city',
      required: false,
      description: 'City name',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'State': {
      dbField: 'state',
      required: false,
      description: 'State or province',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Country': {
      dbField: 'country',
      required: false,
      description: 'Country name',
      dataType: 'string',
      maxLength: 100,
      defaultValue: 'India',
      transformation: 'none'
    },
    'PIN Code': {
      dbField: 'postal_code',
      required: false,
      description: 'Postal or ZIP code',
      dataType: 'string',
      maxLength: 10,
      transformation: 'none'
    },
    'GST Number': {
      dbField: 'gst_number',
      required: false,
      description: 'GST registration number',
      dataType: 'string',
      exactLength: 15,
      transformation: 'uppercase'
    },
    'PAN Number': {
      dbField: 'pan_number',
      required: false,
      description: 'PAN card number',
      dataType: 'string',
      exactLength: 10,
      transformation: 'uppercase'
    },
    'Industry': {
      dbField: 'industry',
      required: false,
      description: 'Business industry type',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Product': {
      dbField: 'product',
      required: false,
      description: 'Tally product type',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'License Edition': {
      dbField: 'license_edition',
      required: false,
      description: 'Tally license edition',
      dataType: 'string',
      maxLength: 50,
      transformation: 'none'
    },
    'Business Type': {
      dbField: 'business_type',
      required: false,
      description: 'Type of business entity',
      dataType: 'string',
      maxLength: 100,
      defaultValue: 'private_limited',
      transformation: 'none'
    },
    'Annual Turnover': {
      dbField: 'annual_turnover',
      required: false,
      description: 'Annual business turnover',
      dataType: 'decimal',
      transformation: 'none'
    },
    'Employee Count': {
      dbField: 'employee_count',
      required: false,
      description: 'Number of employees',
      dataType: 'integer',
      minValue: 0,
      transformation: 'none'
    },
    'Website': {
      dbField: 'website',
      required: false,
      description: 'Company website URL',
      dataType: 'url',
      maxLength: 255,
      transformation: 'none'
    },
    'Lead Source': {
      dbField: 'lead_source',
      required: false,
      description: 'Source of the lead',
      dataType: 'string',
      maxLength: 100,
      transformation: 'none'
    },
    'Profile Status': {
      dbField: 'profile_status',
      required: false,
      description: 'Customer profile status',
      dataType: 'string',
      maxLength: 50,
      transformation: 'none'
    },
    'Customer Status': {
      dbField: 'customer_status',
      required: false,
      description: 'Overall customer status',
      dataType: 'string',
      maxLength: 50,
      transformation: 'none'
    },
    'Notes': {
      dbField: 'notes',
      required: false,
      description: 'Additional notes or comments',
      dataType: 'text',
      maxLength: 1000,
      transformation: 'none'
    }
  };
};

// Helper function to get validation rules
const getValidationRules = () => {
  return {
    required_fields: [
      'Company Name*',
      'Customer Code*',
      'Email*',
      'Phone*',
      'Tally Serial Number*'
    ],
    email_fields: [
      'Email*',
      'Admin Email',
      'MD Email',
      'Office Email',
      'Auditor Email',
      'Tax Consultant Email',
      'IT Email'
    ],
    phone_fields: [
      'Phone*',
      'MD Phone Number',
      'Office Mobile Number',
      'Auditor Number',
      'Tax Consultant Number',
      'IT Number'
    ],
    length_validations: {
      'GST Number': { exact: 15 },
      'PAN Number': { exact: 10 },
      'Company Name*': { max: 200 },
      'Customer Code*': { max: 20 }
    },
    duplicate_checks: [
      'Company Name*',
      'Customer Code*'
    ],
    data_transformations: {
      'Company Name*': 'uppercase',
      'Tally Serial Number*': 'uppercase',
      'GST Number': 'uppercase',
      'PAN Number': 'uppercase',
      'Status*': 'uppercase'
    }
  };
};
