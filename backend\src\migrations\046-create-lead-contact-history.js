/**
 * Migration: Create lead contact history table
 * - Track all contact attempts with leads
 * - Store timestamp, executive, and notes for each contact
 */

import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    // Create lead_contact_history table
    await queryInterface.createTable('lead_contact_history', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tenant_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      lead_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'leads',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to the lead',
      },
      executive_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'executives',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'Executive who made the contact',
      },
      contact_type: {
        type: DataTypes.ENUM('phone', 'email', 'meeting', 'whatsapp', 'other'),
        allowNull: false,
        defaultValue: 'phone',
        comment: 'Type of contact made',
      },
      contact_date: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'When the contact was made',
      },
      duration_minutes: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Duration of contact in minutes (for calls/meetings)',
      },
      outcome: {
        type: DataTypes.ENUM('interested', 'not_interested', 'callback_requested', 'meeting_scheduled', 'converted', 'no_response', 'other'),
        allowNull: true,
        comment: 'Outcome of the contact',
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Notes about the contact',
      },
      next_follow_up: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'When to follow up next',
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    }, { transaction });

    // Add indexes for better performance
    await queryInterface.addIndex('lead_contact_history', ['tenant_id'], { transaction });
    await queryInterface.addIndex('lead_contact_history', ['lead_id'], { transaction });
    await queryInterface.addIndex('lead_contact_history', ['executive_id'], { transaction });
    await queryInterface.addIndex('lead_contact_history', ['contact_date'], { transaction });
    await queryInterface.addIndex('lead_contact_history', ['created_by'], { transaction });
    await queryInterface.addIndex('lead_contact_history', ['next_follow_up'], { transaction });

    await transaction.commit();
    console.log('✅ Created lead_contact_history table with indexes');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error creating lead_contact_history table:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    await queryInterface.dropTable('lead_contact_history', { transaction });
    await transaction.commit();
    console.log('✅ Dropped lead_contact_history table');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error dropping lead_contact_history table:', error);
    throw error;
  }
};
