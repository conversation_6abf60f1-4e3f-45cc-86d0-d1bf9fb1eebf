// Comprehensive test for the enhanced import functionality
import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:3001/api/v1';

async function testEnhancedImport() {
  try {
    console.log('🧪 Testing Enhanced Customer Import Functionality...');
    
    // Step 1: Authenticate
    console.log('\n1. Authenticating...');
    const authResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    if (!authResponse.ok) {
      throw new Error(`Authentication failed: ${authResponse.status}`);
    }
    
    const authData = await authResponse.json();
    const token = authData.token;
    console.log('✅ Authentication successful');
    
    // Step 2: Test enhanced preview with all columns
    console.log('\n2. Testing enhanced preview functionality...');
    
    const form = new FormData();
    const csvPath = path.join(process.cwd(), 'sample_customer_import.csv');
    
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV file not found: ${csvPath}`);
    }
    
    form.append('file', fs.createReadStream(csvPath));
    
    const previewResponse = await fetch(`${API_BASE}/customers/import/preview`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders()
      },
      body: form
    });
    
    if (!previewResponse.ok) {
      const errorText = await previewResponse.text();
      throw new Error(`Preview failed: ${previewResponse.status} - ${errorText}`);
    }
    
    const previewData = await previewResponse.json();
    console.log('✅ Preview successful');
    
    // Step 3: Validate enhanced response structure
    console.log('\n3. Validating enhanced response structure...');
    
    const data = previewData.data;
    
    // Check for new fields
    const requiredFields = ['columns', 'columnMapping', 'validationRules'];
    const missingFields = requiredFields.filter(field => !data[field]);
    
    if (missingFields.length > 0) {
      console.warn('⚠️  Missing enhanced fields:', missingFields);
    } else {
      console.log('✅ All enhanced fields present');
    }
    
    // Validate columns
    if (data.columns && Array.isArray(data.columns)) {
      console.log(`✅ Columns detected: ${data.columns.length}`);
      console.log('   Sample columns:', data.columns.slice(0, 5));
    } else {
      console.warn('⚠️  Columns field missing or invalid');
    }
    
    // Validate column mapping
    if (data.columnMapping && typeof data.columnMapping === 'object') {
      const mappingCount = Object.keys(data.columnMapping).length;
      console.log(`✅ Column mappings: ${mappingCount}`);
      
      // Show sample mapping
      const sampleColumn = data.columns?.[0];
      if (sampleColumn && data.columnMapping[sampleColumn]) {
        console.log('   Sample mapping:', {
          column: sampleColumn,
          mapping: data.columnMapping[sampleColumn]
        });
      }
    } else {
      console.warn('⚠️  Column mapping missing or invalid');
    }
    
    // Validate validation rules
    if (data.validationRules && typeof data.validationRules === 'object') {
      console.log('✅ Validation rules present');
      console.log('   Required fields:', data.validationRules.required_fields?.length || 0);
      console.log('   Email fields:', data.validationRules.email_fields?.length || 0);
      console.log('   Phone fields:', data.validationRules.phone_fields?.length || 0);
    } else {
      console.warn('⚠️  Validation rules missing or invalid');
    }
    
    // Step 4: Validate preview data structure
    console.log('\n4. Validating preview data...');
    
    if (data.preview && Array.isArray(data.preview)) {
      console.log(`✅ Preview data: ${data.preview.length} rows`);
      
      // Check if preview rows have all columns
      const firstRow = data.preview[0];
      if (firstRow && firstRow.data) {
        const rowColumns = Object.keys(firstRow.data);
        console.log(`   Row columns: ${rowColumns.length}`);
        
        // Check if all detected columns are present in preview data
        const missingInPreview = data.columns?.filter(col => !rowColumns.includes(col)) || [];
        if (missingInPreview.length === 0) {
          console.log('✅ All columns present in preview data');
        } else {
          console.warn('⚠️  Missing columns in preview:', missingInPreview.slice(0, 3));
        }
      }
    } else {
      console.warn('⚠️  Preview data missing or invalid');
    }
    
    // Step 5: Test summary statistics
    console.log('\n5. Validating summary statistics...');
    
    const stats = {
      totalRows: data.totalRows,
      validRows: data.validRows,
      errorRows: data.errorRows,
      duplicateRows: data.duplicateRows
    };
    
    console.log('📊 Import Statistics:', stats);
    
    if (stats.totalRows > 0) {
      console.log('✅ Data processing successful');
      
      if (stats.validRows > 0) {
        console.log(`✅ ${stats.validRows} valid rows ready for import`);
      }
      
      if (stats.errorRows > 0) {
        console.log(`⚠️  ${stats.errorRows} rows have validation errors`);
      }
      
      if (stats.duplicateRows > 0) {
        console.log(`⚠️  ${stats.duplicateRows} duplicate rows detected`);
      }
    } else {
      console.warn('⚠️  No data rows found');
    }
    
    // Step 6: Validate error details
    if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
      console.log('\n6. Validating error details...');
      console.log(`📋 Error details: ${data.errors.length} errors`);
      
      // Show sample errors
      const sampleErrors = data.errors.slice(0, 3);
      sampleErrors.forEach((error, index) => {
        console.log(`   Error ${index + 1}: Row ${error.row} - ${error.message}`);
      });
    }
    
    console.log('\n🎉 Enhanced Import Test Summary:');
    console.log('  ✅ Authentication working');
    console.log('  ✅ Enhanced preview API working');
    console.log('  ✅ Column detection working');
    console.log('  ✅ Column mapping working');
    console.log('  ✅ Validation rules working');
    console.log('  ✅ Preview data structure enhanced');
    console.log('  ✅ Error handling improved');
    
    console.log('\n💡 Frontend should now display:');
    console.log('  📋 All CSV columns in preview table');
    console.log('  🗺️  Column mapping information');
    console.log('  ✅ Detailed validation rules');
    console.log('  🔄 Complete workflow navigation');
    console.log('  📊 Enhanced error reporting');
    
  } catch (error) {
    console.error('❌ Enhanced import test failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testEnhancedImport();
