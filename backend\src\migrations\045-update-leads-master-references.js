/**
 * Migration: Update leads table to use master data references
 * - Replace products text field with products_issues_id reference
 * - Replace status string field with status_id reference
 * - Replace executive string field with executive_id reference
 * - Set default status to "Follow Up"
 */

import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    console.log('🔄 Updating leads table to use master data references...');

    // 1. Add new reference columns
    await queryInterface.addColumn('leads', 'products_issues_id', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'products_issues',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    }, { transaction });

    await queryInterface.addColumn('leads', 'status_id', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'call_statuses',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    }, { transaction });

    await queryInterface.addColumn('leads', 'executive_id', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    }, { transaction });

    // 2. Get "Follow Up" status ID for default
    const [followUpStatus] = await queryInterface.sequelize.query(
      `SELECT id FROM call_statuses WHERE LOWER(name) = 'follow up' OR LOWER(name) = 'followup' LIMIT 1`,
      { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
    );

    // 3. Migrate existing data if any exists
    const existingLeads = await queryInterface.sequelize.query(
      'SELECT id, products, status, executive FROM leads WHERE deleted_at IS NULL',
      { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
    );

    if (existingLeads.length > 0) {
      console.log(`📋 Migrating ${existingLeads.length} existing leads...`);

      for (const lead of existingLeads) {
        // Try to match existing products with products_issues
        if (lead.products) {
          const [matchingProduct] = await queryInterface.sequelize.query(
            `SELECT id FROM products_issues WHERE LOWER(name) ILIKE '%${lead.products.toLowerCase()}%' LIMIT 1`,
            { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
          );
          
          if (matchingProduct) {
            await queryInterface.sequelize.query(
              `UPDATE leads SET products_issues_id = '${matchingProduct.id}' WHERE id = '${lead.id}'`,
              { transaction }
            );
          }
        }

        // Try to match existing status with call_statuses
        if (lead.status) {
          const [matchingStatus] = await queryInterface.sequelize.query(
            `SELECT id FROM call_statuses WHERE LOWER(name) = '${lead.status.toLowerCase()}' LIMIT 1`,
            { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
          );
          
          if (matchingStatus) {
            await queryInterface.sequelize.query(
              `UPDATE leads SET status_id = '${matchingStatus.id}' WHERE id = '${lead.id}'`,
              { transaction }
            );
          } else if (followUpStatus) {
            // Set to "Follow Up" if no match found
            await queryInterface.sequelize.query(
              `UPDATE leads SET status_id = '${followUpStatus.id}' WHERE id = '${lead.id}'`,
              { transaction }
            );
          }
        } else if (followUpStatus) {
          // Set default status to "Follow Up"
          await queryInterface.sequelize.query(
            `UPDATE leads SET status_id = '${followUpStatus.id}' WHERE id = '${lead.id}'`,
            { transaction }
          );
        }

        // Try to match existing executive with executives
        if (lead.executive) {
          const [matchingExecutive] = await queryInterface.sequelize.query(
            `SELECT id FROM executives WHERE LOWER(first_name || ' ' || last_name) = '${lead.executive.toLowerCase()}'
             OR LOWER(first_name) = '${lead.executive.toLowerCase()}'
             OR LOWER(last_name) = '${lead.executive.toLowerCase()}' LIMIT 1`,
            { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
          );
          
          if (matchingExecutive) {
            await queryInterface.sequelize.query(
              `UPDATE leads SET executive_id = '${matchingExecutive.id}' WHERE id = '${lead.id}'`,
              { transaction }
            );
          }
        }
      }
    }

    // 4. Set default status for new leads
    if (followUpStatus) {
      await queryInterface.changeColumn('leads', 'status_id', {
        type: DataTypes.UUID,
        allowNull: true,
        defaultValue: followUpStatus.id,
        references: {
          model: 'call_statuses',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      }, { transaction });
    }

    // 5. Add indexes for performance
    await queryInterface.addIndex('leads', ['products_issues_id'], {
      name: 'idx_leads_products_issues_id',
      transaction
    });

    await queryInterface.addIndex('leads', ['status_id'], {
      name: 'idx_leads_status_id', 
      transaction
    });

    await queryInterface.addIndex('leads', ['executive_id'], {
      name: 'idx_leads_executive_id',
      transaction
    });

    // 6. Remove old string columns (keep them for now, will remove in next migration)
    // This allows for rollback if needed
    
    await transaction.commit();
    console.log('✅ Successfully updated leads table with master data references');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error updating leads table:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    console.log('🔄 Reverting leads table master data references...');

    // Remove indexes
    await queryInterface.removeIndex('leads', 'idx_leads_products_issues_id', { transaction });
    await queryInterface.removeIndex('leads', 'idx_leads_status_id', { transaction });
    await queryInterface.removeIndex('leads', 'idx_leads_executive_id', { transaction });

    // Remove new columns
    await queryInterface.removeColumn('leads', 'products_issues_id', { transaction });
    await queryInterface.removeColumn('leads', 'status_id', { transaction });
    await queryInterface.removeColumn('leads', 'executive_id', { transaction });

    await transaction.commit();
    console.log('✅ Successfully reverted leads table changes');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error reverting leads table changes:', error);
    throw error;
  }
};
