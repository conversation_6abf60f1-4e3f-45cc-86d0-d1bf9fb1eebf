import { logger } from '../utils/logger.js';

/**
 * Attendance-specific permission middleware
 * Extends the base permission system with attendance module permissions
 */

// Define attendance permissions
export const ATTENDANCE_PERMISSIONS = {
  // Basic attendance permissions
  ATTENDANCE_READ: 'attendance.read',
  ATTENDANCE_WRITE: 'attendance.write',
  ATTENDANCE_DELETE: 'attendance.delete',
  
  // Self-service permissions
  ATTENDANCE_SELF_CHECKIN: 'attendance.self.checkin',
  ATTENDANCE_SELF_VIEW: 'attendance.self.view',
  ATTENDANCE_SELF_EDIT: 'attendance.self.edit',
  
  // Team management permissions
  ATTENDANCE_TEAM_VIEW: 'attendance.team.view',
  ATTENDANCE_TEAM_EDIT: 'attendance.team.edit',
  ATTENDANCE_TEAM_APPROVE: 'attendance.team.approve',
  
  // Leave management permissions
  LEAVE_READ: 'leave.read',
  LEAVE_WRITE: 'leave.write',
  LEAVE_DELETE: 'leave.delete',
  LEAVE_SELF_REQUEST: 'leave.self.request',
  LEAVE_SELF_VIEW: 'leave.self.view',
  LEAVE_TEAM_VIEW: 'leave.team.view',
  LEAVE_APPROVE: 'leave.approve',
  LEAVE_REJECT: 'leave.reject',
  
  // Payroll permissions
  PAYROLL_READ: 'payroll.read',
  PAYROLL_WRITE: 'payroll.write',
  PAYROLL_PROCESS: 'payroll.process',
  PAYROLL_APPROVE: 'payroll.approve',
  PAYROLL_SELF_VIEW: 'payroll.self.view',
  
  // Configuration permissions
  ATTENDANCE_CONFIG: 'attendance.config',
  LEAVE_CONFIG: 'leave.config',
  PAYROLL_CONFIG: 'payroll.config',
  
  // Reporting permissions
  ATTENDANCE_REPORTS: 'attendance.reports',
  LEAVE_REPORTS: 'leave.reports',
  PAYROLL_REPORTS: 'payroll.reports',
  
  // Admin permissions
  ATTENDANCE_ADMIN: 'attendance.admin',
  LEAVE_ADMIN: 'leave.admin',
  PAYROLL_ADMIN: 'payroll.admin'
};

// Role-based permission mapping
export const ROLE_PERMISSIONS = {
  employee: [
    ATTENDANCE_PERMISSIONS.ATTENDANCE_SELF_CHECKIN,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_SELF_VIEW,
    ATTENDANCE_PERMISSIONS.LEAVE_SELF_REQUEST,
    ATTENDANCE_PERMISSIONS.LEAVE_SELF_VIEW,
    ATTENDANCE_PERMISSIONS.PAYROLL_SELF_VIEW
  ],
  
  team_lead: [
    // Employee permissions
    ...ROLE_PERMISSIONS?.employee || [],
    // Team permissions
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_VIEW,
    ATTENDANCE_PERMISSIONS.LEAVE_TEAM_VIEW,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_REPORTS,
    ATTENDANCE_PERMISSIONS.LEAVE_REPORTS
  ],
  
  manager: [
    // Team lead permissions
    ...ROLE_PERMISSIONS?.team_lead || [],
    // Manager permissions
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_EDIT,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_APPROVE,
    ATTENDANCE_PERMISSIONS.LEAVE_APPROVE,
    ATTENDANCE_PERMISSIONS.LEAVE_REJECT,
    ATTENDANCE_PERMISSIONS.PAYROLL_READ,
    ATTENDANCE_PERMISSIONS.PAYROLL_REPORTS
  ],
  
  hr: [
    // All attendance and leave permissions
    ATTENDANCE_PERMISSIONS.ATTENDANCE_READ,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_WRITE,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_VIEW,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_EDIT,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_APPROVE,
    ATTENDANCE_PERMISSIONS.LEAVE_READ,
    ATTENDANCE_PERMISSIONS.LEAVE_WRITE,
    ATTENDANCE_PERMISSIONS.LEAVE_APPROVE,
    ATTENDANCE_PERMISSIONS.LEAVE_REJECT,
    ATTENDANCE_PERMISSIONS.LEAVE_TEAM_VIEW,
    ATTENDANCE_PERMISSIONS.PAYROLL_READ,
    ATTENDANCE_PERMISSIONS.PAYROLL_WRITE,
    ATTENDANCE_PERMISSIONS.PAYROLL_PROCESS,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_CONFIG,
    ATTENDANCE_PERMISSIONS.LEAVE_CONFIG,
    ATTENDANCE_PERMISSIONS.ATTENDANCE_REPORTS,
    ATTENDANCE_PERMISSIONS.LEAVE_REPORTS,
    ATTENDANCE_PERMISSIONS.PAYROLL_REPORTS
  ],
  
  admin: [
    // All permissions
    ...Object.values(ATTENDANCE_PERMISSIONS)
  ]
};

/**
 * Check if user has specific attendance permission
 */
export const hasAttendancePermission = (user, permission) => {
  try {
    // Super admin has all permissions
    if (user.is_super_admin) {
      return true;
    }

    // Check if user has the specific permission
    if (user.permissions && user.permissions.includes(permission)) {
      return true;
    }

    // Check role-based permissions
    if (user.roles) {
      for (const role of user.roles) {
        const rolePermissions = ROLE_PERMISSIONS[role.slug] || ROLE_PERMISSIONS[role];
        if (rolePermissions && rolePermissions.includes(permission)) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    logger.error('Error checking attendance permission:', error);
    return false;
  }
};

/**
 * Middleware to require specific attendance permission
 */
export const requireAttendancePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      if (!hasAttendancePermission(req.user, permission)) {
        logger.warn('Access denied for attendance permission:', {
          userId: req.user.id,
          permission: permission,
          userRoles: req.user.roles,
          userPermissions: req.user.permissions
        });

        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions for this action',
          required_permission: permission
        });
      }

      next();
    } catch (error) {
      logger.error('Error in attendance permission middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  };
};

/**
 * Middleware to check if user can access employee data
 * Allows access to own data or team data based on permissions
 */
export const canAccessEmployeeData = (req, res, next) => {
  try {
    const { employee_id } = req.params;
    const requestedEmployeeId = employee_id || req.query.employee_id;

    // If no specific employee requested, allow (will be filtered by permissions later)
    if (!requestedEmployeeId) {
      return next();
    }

    // Allow access to own data
    if (req.user.executive_id && req.user.executive_id.toString() === requestedEmployeeId.toString()) {
      return next();
    }

    // Check if user has team permissions
    if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_VIEW) ||
        hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.LEAVE_TEAM_VIEW)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: 'You can only access your own attendance data'
    });
  } catch (error) {
    logger.error('Error in employee data access check:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check if user can modify attendance data
 */
export const canModifyAttendanceData = (req, res, next) => {
  try {
    const { employee_id } = req.params;
    const requestedEmployeeId = employee_id || req.body.employee_id;

    // Allow modification of own data for self-service actions
    if (req.user.executive_id && req.user.executive_id.toString() === requestedEmployeeId?.toString()) {
      if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_SELF_CHECKIN) ||
          hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_SELF_EDIT)) {
        return next();
      }
    }

    // Check if user has team modification permissions
    if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_EDIT)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: 'You do not have permission to modify this attendance data'
    });
  } catch (error) {
    logger.error('Error in attendance modification check:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check leave approval permissions
 */
export const canApproveLeave = (req, res, next) => {
  try {
    if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.LEAVE_APPROVE)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: 'You do not have permission to approve leave requests'
    });
  } catch (error) {
    logger.error('Error in leave approval check:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to check payroll access permissions
 */
export const canAccessPayroll = (req, res, next) => {
  try {
    const { employee_id } = req.params;
    const requestedEmployeeId = employee_id || req.query.employee_id;

    // Allow access to own payroll data
    if (req.user.executive_id && 
        requestedEmployeeId && 
        req.user.executive_id.toString() === requestedEmployeeId.toString()) {
      if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.PAYROLL_SELF_VIEW)) {
        return next();
      }
    }

    // Check if user has payroll permissions
    if (hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.PAYROLL_READ)) {
      return next();
    }

    return res.status(403).json({
      success: false,
      message: 'You do not have permission to access payroll data'
    });
  } catch (error) {
    logger.error('Error in payroll access check:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Middleware to filter data based on user permissions
 */
export const filterDataByPermissions = (req, res, next) => {
  try {
    // Add permission context to request for use in controllers
    req.attendancePermissions = {
      canViewTeam: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_VIEW),
      canEditTeam: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_TEAM_EDIT),
      canApproveLeave: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.LEAVE_APPROVE),
      canProcessPayroll: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.PAYROLL_PROCESS),
      canViewReports: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_REPORTS),
      isAdmin: hasAttendancePermission(req.user, ATTENDANCE_PERMISSIONS.ATTENDANCE_ADMIN),
      employeeId: req.user.executive_id
    };

    next();
  } catch (error) {
    logger.error('Error in permission filtering:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export default {
  ATTENDANCE_PERMISSIONS,
  ROLE_PERMISSIONS,
  hasAttendancePermission,
  requireAttendancePermission,
  canAccessEmployeeData,
  canModifyAttendanceData,
  canApproveLeave,
  canAccessPayroll,
  filterDataByPermissions
};
