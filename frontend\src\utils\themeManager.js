/**
 * Theme Manager Utility
 * Handles dynamic theme color application across the entire application
 */

class ThemeManager {
  constructor() {
    this.root = document.documentElement;
    this.defaultPrimaryColor = '#2f69b3';
    this.init();
  }

  /**
   * Initialize theme manager
   */
  init() {
    // Apply default theme if no theme is set
    const savedColor = localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
    this.applyTheme(savedColor);
  }

  /**
   * Initialize theme with database color (called from Settings component)
   */
  initWithDatabaseColor(databaseColor) {
    if (databaseColor && databaseColor !== this.defaultPrimaryColor) {
      this.applyTheme(databaseColor);
    } else {
      // Fallback to localStorage or default
      const savedColor = localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
      this.applyTheme(savedColor);
    }
  }

  /**
   * Convert hex to RGB values
   */
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  /**
   * Convert RGB to hex
   */
  rgbToHex(r, g, b) {
    const toHex = (n) => {
      const hex = Math.round(n).toString(16);
      return hex.length === 1 ? `0${hex}` : hex;
    };
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  }

  /**
   * Adjust color brightness
   */
  adjustBrightness(hex, percent) {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const factor = percent / 100;
    const newR = Math.max(0, Math.min(255, rgb.r + (rgb.r * factor)));
    const newG = Math.max(0, Math.min(255, rgb.g + (rgb.g * factor)));
    const newB = Math.max(0, Math.min(255, rgb.b + (rgb.b * factor)));

    return this.rgbToHex(newR, newG, newB);
  }

  /**
   * Calculate luminance of a color (0-1, where 0 is darkest and 1 is lightest)
   */
  calculateLuminance(r, g, b) {
    // Convert RGB to relative luminance using WCAG formula
    const rsRGB = r / 255;
    const gsRGB = g / 255;
    const bsRGB = b / 255;

    const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
    const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
    const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

    return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
  }

  /**
   * Determine if a color is light or dark
   */
  isLightColor(hexColor) {
    const rgb = this.hexToRgb(hexColor);
    if (!rgb) return false;

    const luminance = this.calculateLuminance(rgb.r, rgb.g, rgb.b);
    return luminance > 0.5; // Threshold for light vs dark
  }

  /**
   * Get contrasting text color (black or white) for a given background color
   */
  getContrastingTextColor(backgroundColor) {
    return this.isLightColor(backgroundColor) ? '#000000' : '#ffffff';
  }

  /**
   * Generate complete color palette from primary color
   */
  generatePalette(primaryColor) {
    const rgb = this.hexToRgb(primaryColor);
    if (!rgb) return {};

    // Calculate contrasting text color
    const textColor = this.getContrastingTextColor(primaryColor);
    const isLight = this.isLightColor(primaryColor);

    // Generate full color scale (50-950) for Tailwind compatibility
    const colorScale = this.generateColorScale(primaryColor);

    return {
      primary: primaryColor,
      primaryDark: this.adjustBrightness(primaryColor, -20),
      primaryDarker: this.adjustBrightness(primaryColor, -40),
      primaryLight: this.adjustBrightness(primaryColor, 20),
      primaryLighter: this.adjustBrightness(primaryColor, 40),
      primaryRgb: `${rgb.r}, ${rgb.g}, ${rgb.b}`,
      primaryText: textColor,
      primaryTextRgb: textColor === '#ffffff' ? '255, 255, 255' : '0, 0, 0',
      isLightTheme: isLight,
      // Additional theme-consistent colors for icons and backgrounds
      iconBackground: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
      iconBackgroundHover: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`,
      iconColor: primaryColor,
      badgeBackground: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
      alertBackground: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
      // Full color scale for Tailwind
      colorScale,
    };
  }

  /**
   * Generate a complete color scale (50-950) from a base color
   */
  generateColorScale(baseColor) {
    const rgb = this.hexToRgb(baseColor);
    if (!rgb) return {};

    // Convert to HSL for better color manipulation
    const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);

    const scale = {};
    const steps = [
      { key: '50', lightness: 95, saturation: 0.3 },
      { key: '100', lightness: 90, saturation: 0.4 },
      { key: '200', lightness: 80, saturation: 0.5 },
      { key: '300', lightness: 70, saturation: 0.6 },
      { key: '400', lightness: 60, saturation: 0.7 },
      { key: '500', lightness: 50, saturation: 1.0 }, // Base color
      { key: '600', lightness: 45, saturation: 1.0 },
      { key: '700', lightness: 40, saturation: 1.0 },
      { key: '800', lightness: 30, saturation: 1.0 },
      { key: '900', lightness: 20, saturation: 1.0 },
      { key: '950', lightness: 10, saturation: 1.0 },
    ];

    steps.forEach(step => {
      const adjustedHsl = {
        h: hsl.h,
        s: Math.min(100, hsl.s * step.saturation),
        l: step.lightness
      };
      const adjustedRgb = this.hslToRgb(adjustedHsl.h, adjustedHsl.s, adjustedHsl.l);
      scale[step.key] = `${adjustedRgb.r}, ${adjustedRgb.g}, ${adjustedRgb.b}`;
    });

    return scale;
  }

  /**
   * Apply theme colors to CSS custom properties
   */
  applyTheme(primaryColor) {
    const palette = this.generatePalette(primaryColor);
    const rgb = this.hexToRgb(primaryColor);

    // Set CSS custom properties for colors
    this.root.style.setProperty('--primary-color', palette.primary);
    this.root.style.setProperty('--primary-dark', palette.primaryDark);
    this.root.style.setProperty('--primary-darker', palette.primaryDarker);
    this.root.style.setProperty('--primary-light', palette.primaryLight);
    this.root.style.setProperty('--primary-lighter', palette.primaryLighter);
    this.root.style.setProperty('--primary-rgb', palette.primaryRgb);

    // Set CSS custom properties for text colors
    this.root.style.setProperty('--primary-text', palette.primaryText);
    this.root.style.setProperty('--primary-text-rgb', palette.primaryTextRgb);
    this.root.style.setProperty('--is-light-theme', palette.isLightTheme ? '1' : '0');

    // Set solid colors instead of gradients
    this.root.style.setProperty('--sidebar-background', palette.primary);
    this.root.style.setProperty('--button-background', palette.primary);
    this.root.style.setProperty('--auth-background', palette.primary);

    // Set theme-consistent icon and background colors
    this.root.style.setProperty('--theme-icon-bg', palette.iconBackground);
    this.root.style.setProperty('--theme-icon-bg-hover', palette.iconBackgroundHover);
    this.root.style.setProperty('--theme-icon-color', palette.iconColor);
    this.root.style.setProperty('--theme-badge-bg', palette.badgeBackground);
    this.root.style.setProperty('--theme-alert-bg', palette.alertBackground);

    // Set theme-consistent content backgrounds
    if (rgb) {
      this.root.style.setProperty('--theme-content-bg', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.08)`);
      this.root.style.setProperty('--theme-modal-bg', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.05)`);
      this.root.style.setProperty('--theme-card-bg', `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.03)`);
    }

    // Set Tailwind-compatible color scale variables
    if (palette.colorScale) {
      Object.entries(palette.colorScale).forEach(([key, value]) => {
        this.root.style.setProperty(`--primary-${key}`, value);
        this.root.style.setProperty(`--theme-${key}`, value);
      });
    }

    // Save to localStorage
    localStorage.setItem('primaryColor', primaryColor);

    // Dispatch custom event for components that need to react to theme changes
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { primaryColor, palette }
    }));
  }

  /**
   * Get current primary color
   */
  getCurrentPrimaryColor() {
    return localStorage.getItem('primaryColor') || this.defaultPrimaryColor;
  }

  /**
   * Reset to default theme
   */
  resetTheme() {
    this.applyTheme(this.defaultPrimaryColor);
  }

  /**
   * Get predefined color presets
   */
  getColorPresets() {
    return [
      { name: 'Purple', color: '#1d5795' },
      { name: 'Blue', color: '#3b82f6' },
      { name: 'Green', color: '#10b981' },
      { name: 'Red', color: '#ef4444' },
      { name: 'Orange', color: '#f97316' },
      { name: 'Pink', color: '#ec4899' },
      { name: 'Indigo', color: '#6366f1' },
      { name: 'Teal', color: '#14b8a6' },
      { name: 'Yellow', color: '#eab308' },
      { name: 'Slate', color: '#64748b' },
      { name: 'Emerald', color: '#059669' },
      { name: 'Rose', color: '#f43f5e' },
      { name: 'Cyan', color: '#06b6d4' },
      { name: 'Lime', color: '#84cc16' },
      { name: 'Amber', color: '#f59e0b' },
      { name: 'Violet', color: '#8b5cf6' },
      // Light colors to test dynamic text
      { name: 'Light Blue', color: '#bfdbfe' },
      { name: 'Light Green', color: '#bbf7d0' },
      { name: 'Light Yellow', color: '#fef3c7' },
      { name: 'Light Pink', color: '#fce7f3' },
      { name: 'Light Gray', color: '#f3f4f6' },
      { name: 'White', color: '#ffffff' }
    ];
  }

  /**
   * Convert RGB to HSL
   */
  rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100 };
  }

  /**
   * Convert HSL to RGB
   */
  hslToRgb(h, s, l) {
    h /= 360;
    s /= 100;
    l /= 100;

    const hue2rgb = (p, q, t) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1 / 3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1 / 3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  }
}

// Create singleton instance
const themeManager = new ThemeManager();

export default themeManager;
