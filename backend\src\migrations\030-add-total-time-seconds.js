import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  try {
    console.log('Adding total_time_seconds field to service_calls table...');
    
    // Check if total_time_seconds column exists
    const tableDescription = await queryInterface.describeTable('service_calls');
    
    if (!tableDescription.total_time_seconds) {
      await queryInterface.addColumn('service_calls', 'total_time_seconds', {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
        comment: 'Total time spent on the call in seconds (more precise)',
      });
      
      console.log('✅ Added total_time_seconds column to service_calls');
    } else {
      console.log('⏭️ total_time_seconds column already exists');
    }

    // Add index for total_time_seconds for performance
    try {
      await queryInterface.addIndex('service_calls', ['total_time_seconds'], {
        name: 'service_calls_total_time_seconds'
      });
      console.log('✅ Added index for total_time_seconds');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
      console.log('⏭️ Index for total_time_seconds already exists');
    }

  } catch (error) {
    console.error('❌ Error adding total_time_seconds field:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  try {
    // Remove index
    try {
      await queryInterface.removeIndex('service_calls', 'service_calls_total_time_seconds');
      console.log('✅ Removed index for total_time_seconds');
    } catch (error) {
      console.log('⏭️ Index for total_time_seconds might not exist');
    }

    // Remove total_time_seconds column
    try {
      const tableDescription = await queryInterface.describeTable('service_calls');
      if (tableDescription.total_time_seconds) {
        await queryInterface.removeColumn('service_calls', 'total_time_seconds');
        console.log('✅ Removed total_time_seconds column');
      }
    } catch (error) {
      console.log('⏭️ total_time_seconds column might not exist');
    }
  } catch (error) {
    console.error('❌ Error removing total_time_seconds field:', error);
    throw error;
  }
};
