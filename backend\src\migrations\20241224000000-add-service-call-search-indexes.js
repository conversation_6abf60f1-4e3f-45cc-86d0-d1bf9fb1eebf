import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
    try {
      // Add search performance indexes for service calls
      
      // Single column indexes for search fields
      await queryInterface.addIndex('service_calls', ['subject'], {
        name: 'service_calls_subject_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['description'], {
        name: 'service_calls_description_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tally_serial_number'], {
        name: 'service_calls_tally_serial_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['call_billing_type'], {
        name: 'service_calls_billing_type_idx',
        concurrently: true,
      });

      // Composite indexes for common filter combinations
      await queryInterface.addIndex('service_calls', ['tenant_id', 'status_id'], {
        name: 'service_calls_tenant_status_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'customer_id'], {
        name: 'service_calls_tenant_customer_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'assigned_to'], {
        name: 'service_calls_tenant_assigned_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'call_date'], {
        name: 'service_calls_tenant_date_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'scheduled_date'], {
        name: 'service_calls_tenant_scheduled_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'priority'], {
        name: 'service_calls_tenant_priority_idx',
        concurrently: true,
      });

      await queryInterface.addIndex('service_calls', ['tenant_id', 'call_billing_type'], {
        name: 'service_calls_tenant_billing_idx',
        concurrently: true,
      });

      // Composite index for overdue queries
      await queryInterface.addIndex('service_calls', ['tenant_id', 'scheduled_date', 'status_id'], {
        name: 'service_calls_overdue_idx',
        concurrently: true,
      });

      console.log('✅ Service call search indexes created successfully');
    } catch (error) {
      console.error('❌ Error creating service call search indexes:', error);
      throw error;
    }
};

export const down = async (queryInterface, Sequelize) => {
    try {
      // Remove all the indexes we created
      const indexesToRemove = [
        'service_calls_subject_idx',
        'service_calls_description_idx',
        'service_calls_tally_serial_idx',
        'service_calls_billing_type_idx',
        'service_calls_tenant_status_idx',
        'service_calls_tenant_customer_idx',
        'service_calls_tenant_assigned_idx',
        'service_calls_tenant_date_idx',
        'service_calls_tenant_scheduled_idx',
        'service_calls_tenant_priority_idx',
        'service_calls_tenant_billing_idx',
        'service_calls_overdue_idx',
      ];

      for (const indexName of indexesToRemove) {
        try {
          await queryInterface.removeIndex('service_calls', indexName);
        } catch (error) {
          console.warn(`⚠️ Could not remove index ${indexName}:`, error.message);
        }
      }

      console.log('✅ Service call search indexes removed successfully');
    } catch (error) {
      console.error('❌ Error removing service call search indexes:', error);
      throw error;
    }
};
