import { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import themeManager from '../utils/themeManager';

/**
 * Hook to manage user preferences including theme colors
 */
export const useUserPreferences = (isAuthenticated) => {
  const [preferences, setPreferences] = useState({
    primaryColor: '',
    emailNotifications: true,
    pushNotifications: true,
    loaded: false
  });
  const [loading, setLoading] = useState(false);

  // Load user preferences when authenticated
  useEffect(() => {
    if (isAuthenticated && !preferences.loaded) {
      loadUserPreferences();
    }
  }, [isAuthenticated, preferences.loaded]);

  const loadUserPreferences = async () => {
    try {
      setLoading(true);
      console.log('📖 Loading user preferences...');

      const response = await apiService.get('/settings/user');
      const userPreferences = response.data?.data?.user || {};

      console.log('📥 User preferences response:', userPreferences);
      console.log('🎨 Primary color from database:', userPreferences.primary_color);

      const primaryColor = userPreferences.primary_color || '';

      setPreferences({
        primaryColor,
        emailNotifications: userPreferences.email_notifications !== false,
        pushNotifications: userPreferences.notifications_enabled !== false,
        loaded: true
      });

      // Apply theme color immediately only if there's a saved color
      if (primaryColor) {
        console.log('🎨 Applying theme color from preferences:', primaryColor);
        themeManager.applyTheme(primaryColor);
      } else {
        console.log('🎨 No saved theme color found in database');
      }
      console.log('✅ User preferences loaded successfully');

    } catch (error) {
      console.error('Error loading user preferences:', error);
      // Don't apply any theme on error, let user choose
      setPreferences(prev => ({ ...prev, loaded: true }));
    } finally {
      setLoading(false);
    }
  };

  const updatePreferences = async (newPreferences) => {
    try {
      setLoading(true);

      console.log('🎨 Updating user preferences:', newPreferences);

      // Update in database
      const requestData = {
        primary_color: newPreferences.primaryColor,
        email_notifications: newPreferences.emailNotifications,
        notifications_enabled: newPreferences.pushNotifications,
      };

      console.log('📤 Sending request data:', requestData);

      const response = await apiService.put('/settings/user', requestData);

      console.log('📥 Response received:', response);

      // Update local state
      setPreferences(prev => ({
        ...prev,
        ...newPreferences
      }));

      // Apply theme if color changed
      if (newPreferences.primaryColor) {
        console.log('🎨 Applying theme color:', newPreferences.primaryColor);
        themeManager.applyTheme(newPreferences.primaryColor);
      }

      console.log('✅ User preferences updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating user preferences:', error);
      console.error('Error details:', error.response?.data || error.message);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const updateThemeColor = async (color) => {
    return updatePreferences({ primaryColor: color });
  };

  return {
    preferences,
    loading,
    loadUserPreferences,
    updatePreferences,
    updateThemeColor
  };
};
