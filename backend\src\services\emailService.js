import nodemailer from 'nodemailer';
import { logger } from '../utils/logger.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class EmailService {
  constructor() {
    this.transporter = null;
    this.isEnabled = false;
    this.emailLogPath = path.join(__dirname, '../../logs/email.log');
    this.emailSendLogPath = path.join(__dirname, '../../logs/email-send-status.log');
    this.initializeTransporter();
    this.ensureLogDirectory();
  }

  /**
   * Ensure email log directory exists
   */
  ensureLogDirectory() {
    try {
      const logDir = path.dirname(this.emailLogPath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    } catch (error) {
      logger.error('Failed to create email log directory:', error);
    }
  }

  /**
   * Log email attempt with detailed information
   */
  logEmailAttempt(type, recipient, subject, success, details = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      type,
      recipient,
      subject,
      success,
      details,
      messageId: details.messageId || null,
      error: details.error || null
    };

    // Log to console
    if (success) {
      logger.info(`📧 Email sent successfully: ${type}`, {
        recipient,
        subject,
        messageId: details.messageId
      });
    } else {
      logger.error(`❌ Email failed: ${type}`, {
        recipient,
        subject,
        error: details.error
      });
    }

    // Log to detailed email log file
    try {
      const logLine = JSON.stringify(logEntry) + '\n';
      fs.appendFileSync(this.emailLogPath, logLine);
    } catch (error) {
      logger.error('Failed to write to email log file:', error);
    }

    // Log to simplified email send status log
    this.logEmailSendStatus(type, recipient, subject, success, details);
  }

  /**
   * Log email send status to a simplified, human-readable log file
   */
  logEmailSendStatus(type, recipient, subject, success, details = {}) {
    try {
      const timestamp = new Date().toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      const status = success ? '✅ SUCCESS' : '❌ FAILED';
      const serviceNumber = details.serviceNumber || 'N/A';
      const messageId = details.messageId || 'N/A';
      const errorMsg = details.error || '';

      let logLine = `[${timestamp}] ${status} | ${type.toUpperCase()}\n`;
      logLine += `  📧 To: ${recipient}\n`;
      logLine += `  📋 Subject: ${subject}\n`;
      logLine += `  🔢 Service: ${serviceNumber}\n`;

      if (success) {
        logLine += `  📨 Message ID: ${messageId}\n`;
        logLine += `  📤 Email Source: ${details.emailSource || 'main_email'}\n`;
      } else {
        logLine += `  ❌ Error: ${errorMsg}\n`;
      }

      logLine += `  ${'='.repeat(80)}\n\n`;

      fs.appendFileSync(this.emailSendLogPath, logLine);
    } catch (error) {
      logger.error('Failed to write to email send status log:', error);
    }
  }

  initializeTransporter() {
    // Email configuration using environment variables
    const mailSettings = {
      host: process.env.SMTP_HOST || "server40.hostingraja.org",
      port: parseInt(process.env.SMTP_PORT) || 25,
      secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || "<EMAIL>",
        pass: process.env.SMTP_PASS || "Cloud@2020"
      },
      tls: {
        rejectUnauthorized: false
      },
      connectionTimeout: 60000,
      greetingTimeout: 30000,
      socketTimeout: 60000
    };

    // AWS SES specific configuration adjustments
    if (process.env.SMTP_HOST && process.env.SMTP_HOST.includes('amazonaws.com')) {
      mailSettings.secure = false; // AWS SES uses STARTTLS on port 587
      mailSettings.requireTLS = true;
      mailSettings.tls = {
        rejectUnauthorized: false,
        ciphers: 'SSLv3'
      };
    }

    try {
      // EMAIL FUNCTIONALITY ENABLED FOR PRODUCTION
      this.transporter = nodemailer.createTransport(mailSettings);
      this.isEnabled = true;

      logger.info('Email service initialized successfully', {
        host: mailSettings.host,
        port: mailSettings.port,
        secure: mailSettings.secure,
        user: mailSettings.auth.user
      });

      // FOR DEVELOPMENT - UNCOMMENT BELOW TO DISABLE EMAIL FUNCTIONALITY
      // this.isEnabled = false;
      // logger.info('Email service disabled for development');

    } catch (error) {
      logger.error('Failed to initialize email service:', error);
      this.isEnabled = false;
    }
  }

  async sendServiceCompletionEmail(serviceData, customerData, feedbackData) {
    const emailType = 'service_completion';
    const subject = `Service Completed - ${serviceData.serviceNumber}`;

    if (!this.isEnabled) {
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: 'Email service disabled for development'
      });
      return { success: true, message: 'Email service disabled for development' };
    }

    try {
      const emailTemplate = this.generateServiceCompletionTemplate(serviceData, customerData, feedbackData);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: customerData.email,
        subject,
        html: emailTemplate,
        attachments: []
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      this.logEmailAttempt(emailType, customerData.email, subject, true, {
        messageId: result.messageId,
        serviceNumber: serviceData.serviceNumber,
        response: result.response
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Service completion email sent successfully'
      };

    } catch (error) {
      // Log failed email
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: error.message,
        serviceNumber: serviceData.serviceNumber,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        message: 'Failed to send service completion email'
      };
    }
  }

  async sendServiceReminderEmail(serviceData, customerData) {
    if (!this.isEnabled) {
      logger.info('Email service disabled - would have sent service reminder email');
      return { success: true, message: 'Email service disabled for development' };
    }

    try {
      const emailTemplate = this.generateServiceReminderTemplate(serviceData, customerData);
      
      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: customerData.email,
        subject: `Service Reminder - ${serviceData.serviceNumber}`,
        html: emailTemplate
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Service reminder email sent successfully:', {
        serviceNumber: serviceData.serviceNumber,
        customerEmail: customerData.email,
        messageId: result.messageId
      });

      return { 
        success: true, 
        messageId: result.messageId,
        message: 'Service reminder email sent successfully'
      };

    } catch (error) {
      logger.error('Failed to send service reminder email:', error);
      return { 
        success: false, 
        error: error.message,
        message: 'Failed to send service reminder email'
      };
    }
  }

  /**
   * Send service notification email for various events
   * @param {Object} serviceData - Service data
   * @param {Object} customerData - Customer data
   * @param {string} eventType - Type of event (service_created, service_started, etc.)
   * @returns {Object} Result object
   */
  async sendServiceNotificationEmail(serviceData, customerData, eventType) {
    const emailType = `service_notification_${eventType}`;
    const subject = this.getNotificationSubject(serviceData, eventType);

    if (!this.isEnabled) {
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: 'Email service disabled for development',
        eventType
      });
      return {
        success: true,
        message: 'Email service disabled for development'
      };
    }

    try {
      const emailTemplate = this.generateServiceNotificationTemplate(serviceData, customerData, eventType);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: customerData.email,
        subject,
        html: emailTemplate
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      this.logEmailAttempt(emailType, customerData.email, subject, true, {
        messageId: result.messageId,
        serviceNumber: serviceData.serviceNumber,
        eventType,
        response: result.response
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Service notification email sent successfully'
      };
    } catch (error) {
      // Log failed email
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: error.message,
        serviceNumber: serviceData.serviceNumber,
        eventType,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        message: 'Failed to send service notification email'
      };
    }
  }

  /**
   * Get notification subject based on event type
   */
  getNotificationSubject(serviceData, eventType) {
    const subjects = {
      service_created: `Service Request Created - ${serviceData.serviceNumber}`,
      service_started: `Service Started - ${serviceData.serviceNumber}`,
      service_in_progress: `Service In Progress - ${serviceData.serviceNumber}`,
      service_completed: `Service Completed - ${serviceData.serviceNumber}`,
      service_cancelled: `Service Cancelled - ${serviceData.serviceNumber}`,
      service_on_hold: `Service On Hold - ${serviceData.serviceNumber}`,
      service_pending: `Service Pending - ${serviceData.serviceNumber}`,
      service_follow_up: `Service Follow Up Required - ${serviceData.serviceNumber}`,
      service_onsite: `Service Onsite - ${serviceData.serviceNumber}`,
      service_on_process: `Service On Process - ${serviceData.serviceNumber}`,
    };

    return subjects[eventType] || `Service Update - ${serviceData.serviceNumber}`;
  }

  /**
   * Generate service notification template based on event type
   */
  generateServiceNotificationTemplate(serviceData, customerData, eventType) {
    const eventTitles = {
      service_created: '📝 Service Request Created',
      service_started: '🚀 Service Started',
      service_in_progress: '⚡ Service In Progress',
      service_completed: '✅ Service Completed',
      service_cancelled: '❌ Service Cancelled',
      service_on_hold: '⏸️ Service On Hold',
      service_pending: '⏳ Service Pending',
      service_follow_up: '🔄 Service Follow Up Required',
      service_onsite: '🏢 Service Onsite',
      service_on_process: '⚙️ Service On Process',
    };

    const eventColors = {
      service_created: '#2563eb',
      service_started: '#059669',
      service_in_progress: '#059669',
      service_completed: '#059669',
      service_cancelled: '#dc2626',
      service_on_hold: '#d97706',
      service_pending: '#d97706',
      service_follow_up: '#1d5795',
      service_onsite: '#0891b2',
      service_on_process: '#059669',
    };

    const title = eventTitles[eventType] || '📋 Service Update';
    const color = eventColors[eventType] || '#2563eb';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title} - ${serviceData.serviceNumber}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: ${color}; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .service-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid ${color}; }
            .footer { text-align: center; margin-top: 20px; padding: 15px; color: #666; font-size: 12px; }
            .btn { display: inline-block; padding: 10px 20px; background: ${color}; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${title}</h1>
                <p>Prem Infotech - Service Notification</p>
            </div>

            <div class="content">
                <p>Dear ${customerData.company_name || customerData.name},</p>

                <p>${this.getEventMessage(eventType)}</p>

                <div class="service-details">
                    <h3>📋 Service Details</h3>
                    <p><strong>Service Number:</strong> ${serviceData.serviceNumber || serviceData.service_number || serviceData.id}</p>
                    <p><strong>Status:</strong> ${serviceData.status?.name || serviceData.status}</p>
                    ${serviceData.service_type ? `<p><strong>Service Type:</strong> ${serviceData.service_type}</p>` : ''}
                    ${serviceData.scheduled_date ? `<p><strong>Scheduled Date:</strong> ${serviceData.scheduled_date}</p>` : ''}
                    ${serviceData.description ? `<p><strong>Description:</strong> ${serviceData.description}</p>` : ''}
                </div>

                <p>${this.getEventFooterMessage(eventType)}</p>

                <div style="text-align: center;">
                    <a href="tel:+919876543210" class="btn">📞 Call Support</a>
                    <a href="mailto:${process.env.EMAIL_FROM}" class="btn">✉️ Email Support</a>
                </div>
            </div>

            <div class="footer">
                <p><strong>Prem Infotech</strong><br>
                Your Trusted Tally Partner<br>
                📧 ${process.env.EMAIL_FROM} | 📞 +91 98765 43210</p>

                <p><em>This email was sent regarding your service request. Please keep this for your records.</em></p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  getEventMessage(eventType) {
    const messages = {
      service_created: 'Your service request has been created successfully and is now in our system.',
      service_started: 'Your service request is now being worked on by our technical team.',
      service_in_progress: 'Your service request is currently in progress.',
      service_completed: 'Your service request has been completed successfully.',
      service_cancelled: 'Your service request has been cancelled.',
      service_on_hold: 'Your service request has been temporarily put on hold.',
      service_pending: 'Your service request is currently pending.',
      service_follow_up: 'Your service request requires follow up action.',
      service_onsite: 'Our technician is now onsite for your service request.',
      service_on_process: 'Your service request is currently being processed.',
    };

    return messages[eventType] || 'There has been an update to your service request.';
  }

  getEventFooterMessage(eventType) {
    const footerMessages = {
      service_created: 'We will keep you updated on the progress of your service request.',
      service_started: 'Our technician will update you on completion.',
      service_in_progress: 'We will notify you once the service is completed.',
      service_completed: 'Thank you for choosing our services. Please feel free to contact us if you need any further assistance.',
      service_cancelled: 'If you have any questions or need to reschedule, please contact our support team.',
      service_on_hold: 'We will resume work on your request shortly and keep you updated.',
      service_pending: 'We will process your request as soon as possible.',
      service_follow_up: 'Our team will contact you shortly for the required follow up.',
      service_onsite: 'Please ensure you are available for the onsite service.',
      service_on_process: 'We will keep you updated on the progress.',
    };

    return footerMessages[eventType] || 'If you have any questions, please contact our support team.';
  }

  generateServiceCompletionTemplate(serviceData, customerData, feedbackData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Service Completion - ${serviceData.serviceNumber}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .service-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #667eea; }
            .feedback-section { background: #e8f4fd; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .footer { text-align: center; margin-top: 20px; padding: 15px; color: #666; font-size: 12px; }
            .btn { display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Service Completed Successfully!</h1>
                <p>Thank you for choosing Prem Infotech</p>
            </div>
            
            <div class="content">
                <p>Dear ${customerData.name},</p>
                
                <p>We're pleased to inform you that your service request has been completed successfully.</p>
                
                <div class="service-details">
                    <h3>📋 Service Details</h3>
                    <p><strong>Service Number:</strong> ${serviceData.serviceNumber}</p>
                    <p><strong>Service Type:</strong> ${serviceData.serviceType}</p>
                    <p><strong>Total Time Spent:</strong> ${serviceData.totalHours} hours</p>
                    <p><strong>Total Amount:</strong> ₹${serviceData.totalAmount}</p>
                    <p><strong>Executive:</strong> ${serviceData.executiveName}</p>
                    ${serviceData.executiveRemarks ? `<p><strong>Executive Remarks:</strong> ${serviceData.executiveRemarks}</p>` : ''}
                </div>
                
                ${feedbackData ? `
                <div class="feedback-section">
                    <h3>💬 Your Feedback</h3>
                    <p><strong>Rating:</strong> ${feedbackData.type}</p>
                    ${feedbackData.comments ? `<p><strong>Comments:</strong> ${feedbackData.comments}</p>` : ''}
                    <p><em>Thank you for your valuable feedback!</em></p>
                </div>
                ` : ''}
                
                <p>If you have any questions or need further assistance, please don't hesitate to contact us.</p>
                
                <div style="text-align: center;">
                    <a href="tel:+919876543210" class="btn">📞 Call Support</a>
                    <a href="mailto:${process.env.EMAIL_FROM}" class="btn">✉️ Email Support</a>
                </div>
            </div>

            <div class="footer">
                <p><strong>Prem Infotech</strong><br>
                Your Trusted Tally Partner<br>
                📧 ${process.env.EMAIL_FROM} | 📞 +91 98765 43210</p>
                
                <p><em>This email was sent regarding your service request. Please keep this for your records.</em></p>
                
                <p style="font-size: 10px; color: #999;">
                    This email is governed by our Privacy Policy and Terms and Conditions.<br>
                    If you no longer wish to receive these emails, please contact us.
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  generateServiceReminderTemplate(serviceData, customerData) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Service Reminder - ${serviceData.serviceNumber}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .reminder-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #ffa726; }
            .footer { text-align: center; margin-top: 20px; padding: 15px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔔 Service Reminder</h1>
                <p>Prem Infotech - Service Notification</p>
            </div>
            
            <div class="content">
                <p>Dear ${customerData.name},</p>
                
                <p>This is a friendly reminder about your upcoming service appointment.</p>
                
                <div class="reminder-details">
                    <h3>📅 Service Details</h3>
                    <p><strong>Service Number:</strong> ${serviceData.serviceNumber}</p>
                    <p><strong>Scheduled Date:</strong> ${serviceData.scheduledDate}</p>
                    <p><strong>Service Type:</strong> ${serviceData.serviceType}</p>
                    <p><strong>Executive:</strong> ${serviceData.executiveName}</p>
                </div>
                
                <p>Please ensure you're available at the scheduled time. If you need to reschedule, please contact us as soon as possible.</p>
            </div>
            
            <div class="footer">
                <p><strong>Prem Infotech</strong><br>
                📧 ${process.env.EMAIL_FROM} | 📞 +91 98765 43210</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  async testEmailConnection() {
    if (!this.isEnabled) {
      return { success: false, message: 'Email service is disabled for development' };
    }

    try {
      await this.transporter.verify();
      logger.info('Email connection test successful');
      return { success: true, message: 'Email connection test successful' };
    } catch (error) {
      logger.error('Email connection test failed:', error);
      return { success: false, message: 'Email connection test failed', error: error.message };
    }
  }

  /**
   * Get email logs
   */
  getEmailLogs(limit = 100) {
    try {
      if (!fs.existsSync(this.emailLogPath)) {
        return { success: true, logs: [], message: 'No email logs found' };
      }

      const logContent = fs.readFileSync(this.emailLogPath, 'utf8');
      const lines = logContent.trim().split('\n').filter(line => line.trim());

      const logs = lines
        .slice(-limit) // Get last N entries
        .map(line => {
          try {
            return JSON.parse(line);
          } catch (error) {
            return null;
          }
        })
        .filter(log => log !== null)
        .reverse(); // Most recent first

      return { success: true, logs, total: logs.length };
    } catch (error) {
      logger.error('Failed to read email logs:', error);
      return { success: false, error: error.message, logs: [] };
    }
  }

  /**
   * Clear email logs
   */
  clearEmailLogs() {
    try {
      if (fs.existsSync(this.emailLogPath)) {
        fs.writeFileSync(this.emailLogPath, '');
      }
      if (fs.existsSync(this.emailSendLogPath)) {
        fs.writeFileSync(this.emailSendLogPath, '');
      }
      return { success: true, message: 'Email logs cleared successfully' };
    } catch (error) {
      logger.error('Failed to clear email logs:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get email send status log (human-readable format)
   */
  getEmailSendStatusLog(lines = 50) {
    try {
      if (!fs.existsSync(this.emailSendLogPath)) {
        return { success: true, log: 'No email send status log found', lines: [] };
      }

      const logContent = fs.readFileSync(this.emailSendLogPath, 'utf8');

      if (!logContent.trim()) {
        return { success: true, log: 'Email send status log is empty', lines: [] };
      }

      // Split by double newlines to get individual log entries
      const entries = logContent.trim().split('\n\n').filter(entry => entry.trim());

      // Get last N entries
      const recentEntries = entries.slice(-lines).reverse(); // Most recent first

      return {
        success: true,
        log: recentEntries.join('\n\n'),
        lines: recentEntries,
        total: entries.length
      };
    } catch (error) {
      logger.error('Failed to read email send status log:', error);
      return { success: false, error: error.message, log: '', lines: [] };
    }
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(userData) {
    const emailType = 'user_welcome';
    const subject = `Welcome to PremInfoTech - Your Account is Ready!`;

    if (!this.isEnabled) {
      this.logEmailAttempt(emailType, userData.email, subject, false, {
        error: 'Email service disabled for development'
      });
      return { success: true, message: 'Email service disabled for development' };
    }

    try {
      const emailTemplate = this.generateWelcomeEmailTemplate(userData);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: userData.email,
        subject,
        html: emailTemplate
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      this.logEmailAttempt(emailType, userData.email, subject, true, {
        messageId: result.messageId,
        userId: userData.id,
        response: result.response
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Welcome email sent successfully'
      };

    } catch (error) {
      // Log failed email
      this.logEmailAttempt(emailType, userData.email, subject, false, {
        error: error.message,
        userId: userData.id,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        message: 'Failed to send welcome email'
      };
    }
  }

  /**
   * Send welcome email to new customer
   */
  async sendCustomerWelcomeEmail(customerData) {
    const emailType = 'customer_welcome';
    const subject = `Welcome to PremInfoTech - Your Tally Support Partner!`;

    if (!this.isEnabled) {
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: 'Email service disabled for development'
      });
      return { success: true, message: 'Email service disabled for development' };
    }

    try {
      const emailTemplate = this.generateCustomerWelcomeEmailTemplate(customerData);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: customerData.email,
        subject,
        html: emailTemplate
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      this.logEmailAttempt(emailType, customerData.email, subject, true, {
        messageId: result.messageId,
        customerName: customerData.company_name || customerData.customer_code,
        response: result.response
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Customer welcome email sent successfully'
      };

    } catch (error) {
      // Log failed email
      this.logEmailAttempt(emailType, customerData.email, subject, false, {
        error: error.message,
        customerName: customerData.company_name || customerData.customer_code,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        message: 'Failed to send customer welcome email'
      };
    }
  }

  /**
   * Send unsubscribe/account deletion email
   */
  async sendUnsubscribeEmail(userData) {
    const emailType = 'user_unsubscribe';
    const subject = `Account Deactivated - Thank You for Using PremInfoTech`;

    if (!this.isEnabled) {
      this.logEmailAttempt(emailType, userData.email, subject, false, {
        error: 'Email service disabled for development'
      });
      return { success: true, message: 'Email service disabled for development' };
    }

    try {
      const emailTemplate = this.generateUnsubscribeEmailTemplate(userData);

      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || 'Prem Infotech Support'}" <${process.env.EMAIL_FROM}>`,
        to: userData.email,
        subject,
        html: emailTemplate
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Log successful email
      this.logEmailAttempt(emailType, userData.email, subject, true, {
        messageId: result.messageId,
        userId: userData.id,
        response: result.response
      });

      return {
        success: true,
        messageId: result.messageId,
        message: 'Unsubscribe email sent successfully'
      };

    } catch (error) {
      // Log failed email
      this.logEmailAttempt(emailType, userData.email, subject, false, {
        error: error.message,
        userId: userData.id,
        stack: error.stack
      });

      return {
        success: false,
        error: error.message,
        message: 'Failed to send unsubscribe email'
      };
    }
  }

  /**
   * Generate customer welcome email template
   */
  generateCustomerWelcomeEmailTemplate(customerData) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <div style="background: linear-gradient(135deg, #2f69b3 0%, #1e4a73 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to PremInfoTech!</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your Trusted Tally Support Partner</p>
        </div>

        <div style="padding: 30px;">
          <p style="font-size: 18px; color: #333; margin-bottom: 20px;">Dear ${customerData.company_name || customerData.customer_code},</p>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Welcome to <strong>PremInfoTech</strong>! We're delighted to have you as our valued customer. Thank you for choosing us as your Tally support partner.
          </p>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #2f69b3;">
            <h3 style="margin-top: 0; color: #2f69b3;">📋 Your Customer Details</h3>
            <p style="margin: 5px 0;"><strong>Company:</strong> ${customerData.company_name || customerData.customer_code}</p>
            <p style="margin: 5px 0;"><strong>Customer Code:</strong> ${customerData.customer_code}</p>
            ${customerData.contact_person ? `<p style="margin: 5px 0;"><strong>Contact Person:</strong> ${customerData.contact_person}</p>` : ''}
            ${customerData.phone ? `<p style="margin: 5px 0;"><strong>Phone:</strong> ${customerData.phone}</p>` : ''}
            <p style="margin: 5px 0;"><strong>Email:</strong> ${customerData.email}</p>
          </div>

          <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #155724;">🚀 What's Next?</h3>
            <p style="color: #155724; margin-bottom: 15px;">As our customer, you now have access to:</p>
            <ul style="margin: 10px 0; padding-left: 20px; color: #155724;">
              <li style="margin: 8px 0;">Priority Tally support and troubleshooting</li>
              <li style="margin: 8px 0;">Expert assistance with Tally installation and configuration</li>
              <li style="margin: 8px 0;">Regular software updates and maintenance</li>
              <li style="margin: 8px 0;">Dedicated customer support team</li>
              <li style="margin: 8px 0;">Training and consultation services</li>
            </ul>
          </div>

          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #856404;">📞 Need Support?</h3>
            <p style="color: #856404; margin-bottom: 10px;">Our expert team is ready to assist you:</p>
            <p style="margin: 5px 0; color: #856404;"><strong>📧 Email:</strong> ${process.env.EMAIL_FROM}</p>
            <p style="margin: 5px 0; color: #856404;"><strong>📱 Phone:</strong> +91 9876543210</p>
            <p style="margin: 5px 0; color: #856404;"><strong>🕒 Support Hours:</strong> Monday to Saturday, 9:00 AM - 6:00 PM</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.APP_URL || 'https://tallycrm.cloudstier.com'}"
               style="background: #2f69b3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              🌐 Visit Our Portal
            </a>
          </div>

          <p style="font-size: 16px; color: #333; text-align: center; margin-top: 30px;">
            <strong>Thank you for choosing PremInfoTech!</strong><br>
            <em>Your Success is Our Priority</em>
          </p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666;">
          <p style="margin: 0;">This is an automated welcome message from PremInfoTech TallyCRM system.</p>
          <p style="margin: 5px 0 0 0;">© ${new Date().getFullYear()} PremInfoTech. All rights reserved.</p>
          <p style="margin: 5px 0 0 0;">
            <a href="${process.env.APP_URL}/unsubscribe?email=${encodeURIComponent(customerData.email)}"
               style="color: #666; text-decoration: none;">Unsubscribe from emails</a>
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Generate welcome email template
   */
  generateWelcomeEmailTemplate(userData) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <div style="background: linear-gradient(135deg, #2f69b3 0%, #1e4a73 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to PremInfoTech!</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your Tally Support Partner</p>
        </div>

        <div style="padding: 30px;">
          <p style="font-size: 18px; color: #333; margin-bottom: 20px;">Dear ${userData.first_name} ${userData.last_name},</p>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Welcome to <strong>PremInfoTech</strong>! We're excited to have you join our community of Tally users who trust us for their accounting software support needs.
          </p>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #2f69b3;">
            <h3 style="margin-top: 0; color: #2f69b3;">🚀 Your Account Details</h3>
            <p style="margin: 5px 0;"><strong>Name:</strong> ${userData.first_name} ${userData.last_name}</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> ${userData.email}</p>
            <p style="margin: 5px 0;"><strong>Phone:</strong> ${userData.phone || 'Not provided'}</p>
            <p style="margin: 5px 0;"><strong>Account Created:</strong> ${new Date().toLocaleDateString('en-IN')}</p>
          </div>

          <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin-top: 0; color: #1976d2;">📋 What You Can Do Now</h3>
            <ul style="margin: 10px 0; padding-left: 20px; color: #555;">
              <li style="margin: 8px 0;">Create and manage service requests</li>
              <li style="margin: 8px 0;">Track your Tally support tickets</li>
              <li style="margin: 8px 0;">Access our knowledge base and resources</li>
              <li style="margin: 8px 0;">Get priority support from our expert team</li>
              <li style="margin: 8px 0;">Manage your customer information and history</li>
            </ul>
          </div>

          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #856404;">📞 Need Help Getting Started?</h3>
            <p style="margin: 5px 0; color: #856404;">Our support team is here to help you:</p>
            <p style="margin: 5px 0; color: #856404;"><strong>Email:</strong> ${process.env.SMTP_USER}</p>
            <p style="margin: 5px 0; color: #856404;"><strong>Phone:</strong> +91-XXXXXXXXXX</p>
            <p style="margin: 5px 0; color: #856404;"><strong>Support Hours:</strong> Monday to Saturday, 9:00 AM to 6:00 PM</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.APP_URL || 'http://168.220.245.22:5373'}"
               style="background: #2f69b3; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              🚀 Access Your Dashboard
            </a>
          </div>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            Thank you for choosing PremInfoTech for your Tally support needs. We're committed to providing you with the best service experience possible.
          </p>

          <p style="margin-top: 30px;">
            Best regards,<br>
            <strong>The PremInfoTech Team</strong><br>
            <em>Your Trusted Tally Support Partner</em>
          </p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666;">
          <p style="margin: 0;">This is an automated welcome message from PremInfoTech TallyCRM system.</p>
          <p style="margin: 5px 0 0 0;">© ${new Date().getFullYear()} PremInfoTech. All rights reserved.</p>
          <p style="margin: 5px 0 0 0;">
            <a href="${process.env.APP_URL}/unsubscribe?email=${encodeURIComponent(userData.email)}"
               style="color: #666; text-decoration: none;">Unsubscribe from emails</a>
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Generate unsubscribe email template
   */
  generateUnsubscribeEmailTemplate(userData) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
        <div style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">👋 Account Deactivated</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Thank You for Using PremInfoTech</p>
        </div>

        <div style="padding: 30px;">
          <p style="font-size: 18px; color: #333; margin-bottom: 20px;">Dear ${userData.first_name} ${userData.last_name},</p>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            We're sorry to see you go! Your account with <strong>PremInfoTech</strong> has been successfully deactivated as requested.
          </p>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #6c757d;">
            <h3 style="margin-top: 0; color: #6c757d;">📋 Account Summary</h3>
            <p style="margin: 5px 0;"><strong>Name:</strong> ${userData.first_name} ${userData.last_name}</p>
            <p style="margin: 5px 0;"><strong>Email:</strong> ${userData.email}</p>
            <p style="margin: 5px 0;"><strong>Deactivated On:</strong> ${new Date().toLocaleDateString('en-IN')}</p>
            <p style="margin: 5px 0;"><strong>Account Duration:</strong> Thank you for being with us!</p>
          </div>

          <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #856404;">📝 What Happens Next?</h3>
            <ul style="margin: 10px 0; padding-left: 20px; color: #856404;">
              <li style="margin: 8px 0;">Your account data has been securely archived</li>
              <li style="margin: 8px 0;">You will no longer receive service notifications</li>
              <li style="margin: 8px 0;">Your service history is preserved for compliance</li>
              <li style="margin: 8px 0;">You can reactivate your account anytime by contacting us</li>
            </ul>
          </div>

          <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0;">
            <h3 style="margin-top: 0; color: #1976d2;">🔄 Want to Come Back?</h3>
            <p style="margin: 5px 0; color: #1976d2;">
              We'd love to have you back! If you change your mind or need Tally support in the future,
              simply contact us and we'll reactivate your account with all your previous data intact.
            </p>
            <p style="margin: 15px 0 5px 0; color: #1976d2;"><strong>Contact us:</strong></p>
            <p style="margin: 5px 0; color: #1976d2;"><strong>Email:</strong> ${process.env.SMTP_USER}</p>
            <p style="margin: 5px 0; color: #1976d2;"><strong>Phone:</strong> +91-XXXXXXXXXX</p>
          </div>

          <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #155724;">💚 Thank You!</h3>
            <p style="margin: 5px 0; color: #155724;">
              Thank you for trusting PremInfoTech with your Tally support needs. It was our pleasure
              serving you, and we hope our service helped make your accounting processes smoother.
            </p>
          </div>

          <p style="font-size: 16px; line-height: 1.6; color: #555;">
            We appreciate the time you spent with us and wish you all the best in your future endeavors.
          </p>

          <p style="margin-top: 30px;">
            Best regards,<br>
            <strong>The PremInfoTech Team</strong><br>
            <em>Your Former Tally Support Partner</em>
          </p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #666;">
          <p style="margin: 0;">This is an automated account deactivation confirmation from PremInfoTech TallyCRM system.</p>
          <p style="margin: 5px 0 0 0;">© ${new Date().getFullYear()} PremInfoTech. All rights reserved.</p>
          <p style="margin: 5px 0 0 0; font-style: italic;">
            You have been unsubscribed from all future emails as requested.
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Get email send statistics
   */
  getEmailSendStatistics() {
    try {
      if (!fs.existsSync(this.emailLogPath)) {
        return { success: true, stats: { total: 0, successful: 0, failed: 0 } };
      }

      const logContent = fs.readFileSync(this.emailLogPath, 'utf8');
      const lines = logContent.trim().split('\n').filter(line => line.trim());

      let total = 0;
      let successful = 0;
      let failed = 0;
      let byType = {};

      lines.forEach(line => {
        try {
          const entry = JSON.parse(line);
          total++;

          if (entry.success) {
            successful++;
          } else {
            failed++;
          }

          // Count by type
          const type = entry.type || 'unknown';
          byType[type] = (byType[type] || 0) + 1;
        } catch (error) {
          // Skip invalid JSON lines
        }
      });

      return {
        success: true,
        stats: {
          total,
          successful,
          failed,
          successRate: total > 0 ? ((successful / total) * 100).toFixed(2) + '%' : '0%',
          byType
        }
      };
    } catch (error) {
      logger.error('Failed to get email statistics:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create singleton instance
const emailService = new EmailService();

export default emailService;
export { emailService };
