/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Custom breakpoints
      screens: {
        'xs': '475px',
        'laptop': '1025px',
        '3xl': '1920px', // For 24-inch monitors and larger
      },
      // Dynamic color palette using CSS variables - all colors derive from primary theme color
      colors: {
        // Dynamic primary colors - these change based on user selection
        primary: {
          50: 'rgb(var(--primary-50) / <alpha-value>)',
          100: 'rgb(var(--primary-100) / <alpha-value>)',
          200: 'rgb(var(--primary-200) / <alpha-value>)',
          300: 'rgb(var(--primary-300) / <alpha-value>)',
          400: 'rgb(var(--primary-400) / <alpha-value>)',
          500: 'rgb(var(--primary-500) / <alpha-value>)',
          600: 'rgb(var(--primary-600) / <alpha-value>)',
          700: 'rgb(var(--primary-700) / <alpha-value>)',
          800: 'rgb(var(--primary-800) / <alpha-value>)',
          900: 'rgb(var(--primary-900) / <alpha-value>)',
          950: 'rgb(var(--primary-950) / <alpha-value>)',
          DEFAULT: 'var(--primary-color)',
        },
        // Theme-aware colors that adapt to primary color
        theme: {
          50: 'rgb(var(--theme-50) / <alpha-value>)',
          100: 'rgb(var(--theme-100) / <alpha-value>)',
          200: 'rgb(var(--theme-200) / <alpha-value>)',
          300: 'rgb(var(--theme-300) / <alpha-value>)',
          400: 'rgb(var(--theme-400) / <alpha-value>)',
          500: 'rgb(var(--theme-500) / <alpha-value>)',
          600: 'rgb(var(--theme-600) / <alpha-value>)',
          700: 'rgb(var(--theme-700) / <alpha-value>)',
          800: 'rgb(var(--theme-800) / <alpha-value>)',
          900: 'rgb(var(--theme-900) / <alpha-value>)',
          DEFAULT: 'var(--primary-color)',
          text: 'var(--primary-text)',
          bg: 'var(--theme-content-bg)',
          'icon-bg': 'var(--theme-icon-bg)',
          'icon-color': 'var(--theme-icon-color)',
          'card-bg': 'var(--theme-card-bg)',
          'modal-bg': 'var(--theme-modal-bg)',
        },
        // Neutral semantic colors - minimal color scheme
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#6c757d',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          DEFAULT: '#6c757d',
        },
        // Neutral colors
        light: '#f8f9fa',
        dark: '#212529',
        muted: '#6c757d',
      },
      // Custom font family
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      // Custom spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      // Custom border radius
      borderRadius: {
        'sm': '0.25rem',
        'DEFAULT': '0.375rem',
        'lg': '0.5rem',
      },
      // Custom shadows
      boxShadow: {
        'sm': '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)',
        'DEFAULT': '0 0.5rem 1rem rgba(0, 0, 0, 0.15)',
        'lg': '0 1rem 3rem rgba(0, 0, 0, 0.175)',
      },
      // Custom transitions
      transitionDuration: {
        '150': '0.15s',
        '300': '0.3s',
        '500': '0.5s',
      },
      // Layout dimensions
      width: {
        'sidebar': '220px',
        'sidebar-collapsed': '64px',
      },
      height: {
        'header': '60px',
        'footer': '50px',
      },
      margin: {
        'sidebar': '220px',
        'sidebar-collapsed': '64px',
      },
      // Z-index scale
      zIndex: {
        'dropdown': '1000',
        'sticky': '1020',
        'fixed': '1030',
        'modal-backdrop': '1040',
        'modal': '1050',
        'popover': '1060',
        'tooltip': '1070',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}

