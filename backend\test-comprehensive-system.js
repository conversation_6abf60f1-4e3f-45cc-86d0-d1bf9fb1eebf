import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';
import { Op } from 'sequelize';
import moment from 'moment';

/**
 * Comprehensive system testing for TallyCRM renewal notification system
 */
async function comprehensiveSystemTest() {
  try {
    console.log('🧪 Starting comprehensive system testing...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    if (!tenant) {
      console.error('❌ No active tenant found. Please create a tenant first.');
      return;
    }

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);

    // Test results tracking
    const testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      tests: []
    };

    // Helper function to record test results
    const recordTest = (testName, passed, details = '') => {
      testResults.totalTests++;
      if (passed) {
        testResults.passedTests++;
        console.log(`   ✅ ${testName}`);
      } else {
        testResults.failedTests++;
        console.log(`   ❌ ${testName}: ${details}`);
      }
      testResults.tests.push({ name: testName, passed, details });
    };

    return testResults;

  } catch (error) {
    console.error('❌ Error in comprehensive system test:', error);
    throw error;
  }
}

/**
 * Test 1: Manual notification processing trigger
 */
async function testManualNotificationTrigger() {
  try {
    console.log('\n🔄 Test 1: Manual notification processing trigger...');

    const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
    const renewalService = new RenewalNotificationService();
    await renewalService.initialize();

    // Test manual trigger
    const result = await renewalService.processPendingNotifications();
    
    console.log(`   📊 Processing result: ${result.processed} processed, ${result.successful} successful, ${result.failed} failed`);
    
    return {
      success: true,
      result,
      testName: 'Manual notification trigger',
      passed: result.processed >= 0, // Should not throw error
    };

  } catch (error) {
    console.error('   ❌ Manual notification trigger failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Manual notification trigger',
      passed: false,
    };
  }
}

/**
 * Test 2: Schedule generation
 */
async function testScheduleGeneration() {
  try {
    console.log('\n📅 Test 2: Schedule generation...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
    const renewalService = new RenewalNotificationService();
    await renewalService.initialize();

    // Test schedule generation
    const scheduledCount = await renewalService.scheduleRenewalNotificationsForTenant(tenant.id);
    
    console.log(`   📊 Schedule generation result: ${scheduledCount} notifications scheduled`);
    
    return {
      success: true,
      scheduledCount,
      testName: 'Schedule generation',
      passed: scheduledCount >= 0,
    };

  } catch (error) {
    console.error('   ❌ Schedule generation failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Schedule generation',
      passed: false,
    };
  }
}

/**
 * Test 3: Create test customers with various expiry scenarios
 */
async function testCustomerScenarios() {
  try {
    console.log('\n👥 Test 3: Customer scenarios with various expiry dates...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    const user = await models.User.findOne({
      where: { tenant_id: tenant.id },
      attributes: ['id']
    });

    // Create test customers with different scenarios
    const testScenarios = [
      {
        code: 'PAST001',
        name: 'Past Due Customer',
        expiryDays: -5, // 5 days overdue
        description: 'Customer with expired service'
      },
      {
        code: 'URG001',
        name: 'Urgent Customer',
        expiryDays: 2, // 2 days remaining
        description: 'Customer requiring urgent notification'
      },
      {
        code: 'WEEK001',
        name: 'Weekly Customer',
        expiryDays: 7, // 7 days remaining
        description: 'Customer in weekly reminder range'
      },
      {
        code: 'MONTH001',
        name: 'Monthly Customer',
        expiryDays: 30, // 30 days remaining
        description: 'Customer in monthly reminder range'
      }
    ];

    const createdCustomers = [];
    const createdAMCs = [];

    for (const scenario of testScenarios) {
      try {
        // Create customer
        const customer = await models.Customer.create({
          tenant_id: tenant.id,
          customer_code: scenario.code,
          company_name: scenario.name,
          email: `${scenario.code.toLowerCase()}@test.com`,
          phone: '+91-9876543210',
          is_active: true,
          created_by: user?.id,
        });

        createdCustomers.push(customer);

        // Create TSS record first (required for AMC)
        const tss = await models.CustomerTSS.create({
          customer_id: customer.id,
          tss_number: `TSS-${scenario.code}`,
          start_date: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          expiry_date: moment().add(scenario.expiryDays, 'days').format('YYYY-MM-DD'),
          renewal_date: moment().add(scenario.expiryDays, 'days').format('YYYY-MM-DD'),
          amount: 15000,
          status: 'active',
          is_active: true,
          created_by: user?.id,
        });

        // Create AMC record with expiry date
        const expiryDate = moment().add(scenario.expiryDays, 'days').format('YYYY-MM-DD');
        const amc = await models.CustomerAMC.create({
          customer_id: customer.id,
          tss_id: tss.id,
          amc_number: `AMC-${scenario.code}`,
          start_date: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          end_date: expiryDate,
          renewal_date: expiryDate,
          contract_value: 25000,
          status: 'active',
          is_active: true,
          created_by: user?.id,
        });

        createdAMCs.push(amc);

        console.log(`   ✅ Created ${scenario.description}: ${customer.company_name} (expires ${expiryDate})`);

      } catch (error) {
        console.error(`   ❌ Failed to create ${scenario.description}:`, error.message);
      }
    }

    return {
      success: true,
      customersCreated: createdCustomers.length,
      amcsCreated: createdAMCs.length,
      testName: 'Customer scenarios creation',
      passed: createdCustomers.length === testScenarios.length,
    };

  } catch (error) {
    console.error('   ❌ Customer scenarios test failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Customer scenarios creation',
      passed: false,
    };
  }
}

/**
 * Test 4: Template selection logic verification
 */
async function testTemplateSelection() {
  try {
    console.log('\n🎨 Test 4: Template selection logic verification...');

    const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
    const renewalService = new RenewalNotificationService();

    const testCases = [
      { daysRemaining: 30, expected: 'renewal_reminder' },
      { daysRemaining: 7, expected: 'renewal_reminder' },
      { daysRemaining: 3, expected: 'renewal_urgent' },
      { daysRemaining: 2, expected: 'renewal_urgent' },
      { daysRemaining: 1, expected: 'renewal_urgent' },
      { daysRemaining: 0, expected: 'renewal_urgent' },
      { daysRemaining: -1, expected: 'renewal_overdue' },
      { daysRemaining: -5, expected: 'renewal_overdue' },
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
      const mockNotificationSchedule = {
        expiry_date: moment().add(testCase.daysRemaining, 'days').format('YYYY-MM-DD'),
        days_before_expiry: Math.max(0, testCase.daysRemaining)
      };

      const selectedTemplate = renewalService.getTemplateType(mockNotificationSchedule);
      const isCorrect = selectedTemplate === testCase.expected;
      
      if (isCorrect) {
        passedTests++;
        console.log(`   ✅ Days ${testCase.daysRemaining}: ${selectedTemplate}`);
      } else {
        console.log(`   ❌ Days ${testCase.daysRemaining}: Expected ${testCase.expected}, got ${selectedTemplate}`);
      }
    }

    console.log(`   📊 Template selection tests: ${passedTests}/${totalTests} passed`);

    return {
      success: true,
      passedTests,
      totalTests,
      testName: 'Template selection logic',
      passed: passedTests === totalTests,
    };

  } catch (error) {
    console.error('   ❌ Template selection test failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Template selection logic',
      passed: false,
    };
  }
}

/**
 * Test 5: Duplicate notification prevention
 */
async function testDuplicatePrevention() {
  try {
    console.log('\n🚫 Test 5: Duplicate notification prevention...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    // Get a test customer
    const customer = await models.Customer.findOne({
      where: { 
        tenant_id: tenant.id,
        customer_code: 'TEST001'
      }
    });

    if (!customer) {
      console.log('   ⚠️  No test customer found, skipping duplicate prevention test');
      return {
        success: true,
        testName: 'Duplicate prevention',
        passed: true,
        skipped: true,
      };
    }

    // Try to create duplicate notification schedules
    const scheduleData = {
      tenant_id: tenant.id,
      customer_id: customer.id,
      renewal_type: 'amc',
      renewal_record_id: customer.id,
      expiry_date: moment().add(30, 'days').format('YYYY-MM-DD'),
      notify_at: moment().add(1, 'day').format('YYYY-MM-DD'),
      days_before_expiry: 30,
      status: 'scheduled',
    };

    // Create first schedule
    const firstSchedule = await models.NotificationSchedule.create(scheduleData);
    console.log('   ✅ First schedule created successfully');

    // Try to create duplicate
    try {
      await models.NotificationSchedule.create(scheduleData);
      console.log('   ❌ Duplicate schedule was created (should have been prevented)');
      return {
        success: false,
        testName: 'Duplicate prevention',
        passed: false,
        error: 'Duplicate was not prevented',
      };
    } catch (duplicateError) {
      if (duplicateError.name === 'SequelizeUniqueConstraintError' ||
          duplicateError.message.includes('unique constraint') ||
          duplicateError.message.includes('duplicate key')) {
        console.log('   ✅ Duplicate schedule prevented by unique constraint');
        return {
          success: true,
          testName: 'Duplicate prevention',
          passed: true,
        };
      } else {
        console.log(`   ⚠️  Unexpected error (but duplicate was prevented): ${duplicateError.message}`);
        return {
          success: true,
          testName: 'Duplicate prevention',
          passed: true,
          warning: duplicateError.message,
        };
      }
    }

  } catch (error) {
    console.error('   ❌ Duplicate prevention test failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Duplicate prevention',
      passed: false,
    };
  }
}

/**
 * Test 6: Complete workflow validation
 */
async function testCompleteWorkflow() {
  try {
    console.log('\n🔄 Test 6: Complete workflow validation...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    // Step 1: Generate schedules
    const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
    const renewalService = new RenewalNotificationService();
    await renewalService.initialize();

    const scheduledCount = await renewalService.scheduleRenewalNotificationsForTenant(tenant.id);
    console.log(`   📅 Step 1: Generated ${scheduledCount} notification schedules`);

    // Step 2: Process pending notifications
    const processResult = await renewalService.processPendingNotifications();
    console.log(`   📧 Step 2: Processed ${processResult.processed} notifications (${processResult.successful} successful, ${processResult.failed} failed)`);

    // Step 3: Verify notification history
    const totalNotifications = await models.NotificationSchedule.count({
      where: { tenant_id: tenant.id }
    });
    console.log(`   📊 Step 3: Total notifications in history: ${totalNotifications}`);

    // Step 4: Check for proper status updates
    const statusCounts = await models.NotificationSchedule.findAll({
      where: { tenant_id: tenant.id },
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    console.log('   📈 Step 4: Status distribution:');
    statusCounts.forEach(status => {
      console.log(`     - ${status.status}: ${status.count}`);
    });

    const workflowPassed = scheduledCount >= 0 && processResult.processed >= 0 && totalNotifications > 0;

    return {
      success: true,
      scheduledCount,
      processResult,
      totalNotifications,
      statusCounts,
      testName: 'Complete workflow',
      passed: workflowPassed,
    };

  } catch (error) {
    console.error('   ❌ Complete workflow test failed:', error.message);
    return {
      success: false,
      error: error.message,
      testName: 'Complete workflow',
      passed: false,
    };
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting TallyCRM Comprehensive System Testing\n');

    const testResults = [];

    // Run all tests
    testResults.push(await testManualNotificationTrigger());
    testResults.push(await testScheduleGeneration());
    testResults.push(await testCustomerScenarios());
    testResults.push(await testTemplateSelection());
    testResults.push(await testDuplicatePrevention());
    testResults.push(await testCompleteWorkflow());

    // Calculate overall results
    const totalTests = testResults.length;
    const passedTests = testResults.filter(test => test.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(2);

    console.log('\n🎉 Comprehensive system testing completed!');
    console.log('\n📋 Test Summary:');
    console.log(`  📊 Total tests: ${totalTests}`);
    console.log(`  ✅ Passed: ${passedTests}`);
    console.log(`  ❌ Failed: ${failedTests}`);
    console.log(`  📈 Success rate: ${successRate}%`);

    console.log('\n📝 Detailed Results:');
    testResults.forEach((test, index) => {
      const statusIcon = test.passed ? '✅' : '❌';
      console.log(`  ${statusIcon} ${index + 1}. ${test.testName}`);
      if (!test.passed && test.error) {
        console.log(`     Error: ${test.error}`);
      }
    });

    if (passedTests === totalTests) {
      console.log('\n🎉 All tests passed! System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review and fix issues before production deployment.');
    }

    process.exit(passedTests === totalTests ? 0 : 1);

  } catch (error) {
    console.error('\n❌ Comprehensive system testing failed:', error);
    process.exit(1);
  }
}

// Run the tests
main();
