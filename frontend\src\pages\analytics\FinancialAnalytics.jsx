/**
 * Financial Analytics Page
 * Comprehensive financial analytics including revenue trends, payment analysis, and AMC tracking
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody, Spinner, Alert, Button } from '../../components/ui';
import { 
  <PERSON>Chart, 
  <PERSON>hart, 
  Pie<PERSON>hart, 
  Donut<PERSON>hart, 
  AreaChart,
  ComposedChart,
  TrendChart,
  MetricCard,
  RevenueCard,
  ChartContainer
} from '../../components/charts';
import { apiService } from '../../services/api';

const FinancialAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('30d');

  useEffect(() => {
    fetchFinancialAnalytics();
  }, [period]);

  const fetchFinancialAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch financial analytics data
      const response = await apiService.get(`/analytics/financial?period=${period}`);
      
      if (response.data.success) {
        setAnalyticsData(response.data.data);
      } else {
        throw new Error('Failed to fetch financial analytics data');
      }
    } catch (err) {
      console.error('Financial analytics fetch error:', err);
      setError(err.message || 'Failed to load financial analytics data');
      
      // Set mock data for development
      setAnalyticsData(getMockFinancialAnalytics());
    } finally {
      setLoading(false);
    }
  };

  // Mock data for development/fallback - Updated with realistic service revenue
  const getMockFinancialAnalytics = () => ({
    totalRevenue: 4850000,  // Increased to show service call revenue
    revenueInPeriod: 785000, // Increased period revenue
    monthlyRecurring: 185000, // Higher recurring from AMCs
    oneTimeRevenue: 600000,   // Higher one-time from service calls
    revenueTrend: [
      { date: '2023-07', value: 620000, recurring: 150000, oneTime: 470000 },
      { date: '2023-08', value: 685000, recurring: 165000, oneTime: 520000 },
      { date: '2023-09', value: 598000, recurring: 158000, oneTime: 440000 },
      { date: '2023-10', value: 742000, recurring: 172000, oneTime: 570000 },
      { date: '2023-11', value: 665000, recurring: 175000, oneTime: 490000 },
      { date: '2023-12', value: 785000, recurring: 185000, oneTime: 600000 }
    ],
    revenueByProduct: [
      { name: 'Tally Software', value: 1850000 },
      { name: 'Service Calls', value: 1250000 },  // Added service call revenue
      { name: 'AMC Services', value: 1150000 },
      { name: 'Training', value: 420000 },
      { name: 'Support & Maintenance', value: 180000 }
    ],
    amcStatusDistribution: [
      { name: 'Active', value: 145, status: 'active' },
      { name: 'Expired', value: 28, status: 'expired' },
      { name: 'Pending Renewal', value: 15, status: 'pending' },
      { name: 'Cancelled', value: 8, status: 'cancelled' }
    ],
    paymentStatusDistribution: [
      { name: 'Paid', value: 185, status: 'paid' },
      { name: 'Pending', value: 22, status: 'pending' },
      { name: 'Overdue', value: 8, status: 'overdue' },
      { name: 'Partial', value: 5, status: 'partial' }
    ],
    quarterlyComparison: [
      { quarter: 'Q1 2024', revenue: 1885000, growth: 8.5 },
      { quarter: 'Q2 2024', revenue: 2098000, growth: 12.2 },
      { quarter: 'Q3 2024', revenue: 2255000, growth: 15.8 },
      { quarter: 'Q4 2024', revenue: 2485000, growth: 18.3 }
    ],
    upcomingRenewals: [
      { month: 'Jan 2024', count: 25, value: 125000 },
      { month: 'Feb 2024', count: 18, value: 95000 },
      { month: 'Mar 2024', count: 32, value: 165000 },
      { month: 'Apr 2024', count: 22, value: 115000 },
      { month: 'May 2024', count: 28, value: 145000 },
      { month: 'Jun 2024', count: 20, value: 105000 }
    ]
  });

  const periodOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Alert variant="error" className="mb-6">
          <strong>Financial Analytics Error:</strong> {error}
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Financial Analytics</h1>
            <p className="text-gray-600">Revenue insights, payment tracking, and financial performance metrics</p>
          </div>
          
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Period:</label>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {periodOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Button
              onClick={fetchFinancialAnalytics}
              variant="outline"
              size="sm"
            >
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <RevenueCard
          title="Total Revenue"
          value={analyticsData.totalRevenue}
          previousValue={analyticsData.totalRevenue - analyticsData.revenueInPeriod}
          size="medium"
        />
        
        <RevenueCard
          title="Period Revenue"
          value={analyticsData.revenueInPeriod}
          color="success"
          size="medium"
        />
        
        <RevenueCard
          title="Monthly Recurring"
          value={analyticsData.monthlyRecurring}
          color="primary"
          size="medium"
        />
        
        <MetricCard
          title="Revenue Growth"
          value={((analyticsData.revenueInPeriod / (analyticsData.totalRevenue - analyticsData.revenueInPeriod)) * 100)}
          format="percentage"
          color="warning"
          size="medium"
          icon={
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          }
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Revenue Trend */}
        <ChartContainer
          title="Revenue Trend"
          subtitle={`Revenue performance over the last ${period}`}
        >
          <ComposedChart
            data={analyticsData.revenueTrend}
            elements={[
              { type: 'area', dataKey: 'value', name: 'Total Revenue', color: '#10b981' },
              { type: 'bar', dataKey: 'recurring', name: 'Recurring', color: '#1d5795' },
              { type: 'bar', dataKey: 'oneTime', name: 'One-time', color: '#f59e0b' }
            ]}
            height={300}
            formatters={{ 
              value: (val) => `₹${(val/1000).toFixed(0)}K`,
              recurring: (val) => `₹${(val/1000).toFixed(0)}K`,
              oneTime: (val) => `₹${(val/1000).toFixed(0)}K`
            }}
          />
        </ChartContainer>

        {/* Quarterly Growth */}
        <ChartContainer
          title="Quarterly Performance"
          subtitle="Revenue and growth rate by quarter"
        >
          <ComposedChart
            data={analyticsData.quarterlyComparison}
            elements={[
              { type: 'bar', dataKey: 'revenue', name: 'Revenue', color: '#1d5795' },
              { type: 'line', dataKey: 'growth', name: 'Growth %', color: '#10b981', yAxisId: 'right' }
            ]}
            height={300}
            formatters={{ 
              revenue: (val) => `₹${(val/1000).toFixed(0)}K`,
              growth: (val) => `${val}%`
            }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Revenue by Product */}
        <ChartContainer
          title="Revenue by Product"
          subtitle="Revenue distribution across products"
        >
          <DonutChart
            data={analyticsData.revenueByProduct}
            height={280}
            centerContent={
              <div className="text-center">
                <div className="text-xl font-bold text-gray-900">
                  ₹{(analyticsData.totalRevenue/100000).toFixed(1)}L
                </div>
                <div className="text-sm text-gray-500">Total</div>
              </div>
            }
          />
        </ChartContainer>

        {/* AMC Status Distribution */}
        <ChartContainer
          title="AMC Status"
          subtitle="Annual Maintenance Contract status"
        >
          <PieChart
            data={analyticsData.amcStatusDistribution}
            height={280}
            colorScheme="status"
            showLabels={true}
            showPercentages={true}
          />
        </ChartContainer>

        {/* Payment Status */}
        <ChartContainer
          title="Payment Status"
          subtitle="Current payment status distribution"
        >
          <BarChart
            data={analyticsData.paymentStatusDistribution}
            bars={[{ dataKey: 'value', name: 'Count' }]}
            height={280}
            orientation="vertical"
            colorScheme="status"
            formatters={{ value: (val) => `${val} payments` }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 3 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Renewals */}
        <ChartContainer
          title="Upcoming AMC Renewals"
          subtitle="Renewal schedule for next 6 months"
        >
          <ComposedChart
            data={analyticsData.upcomingRenewals}
            elements={[
              { type: 'bar', dataKey: 'count', name: 'Renewal Count', color: '#6b7280' },
              { type: 'line', dataKey: 'value', name: 'Renewal Value', color: '#10b981', yAxisId: 'right' }
            ]}
            height={300}
            formatters={{ 
              count: (val) => `${val} renewals`,
              value: (val) => `₹${(val/1000).toFixed(0)}K`
            }}
          />
        </ChartContainer>

        {/* Financial Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Financial Summary</h3>
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Revenue Breakdown</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Recurring Revenue</span>
                  <span className="font-medium text-green-600">
                    ₹{(analyticsData.monthlyRecurring/1000).toFixed(0)}K
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">One-time Revenue</span>
                  <span className="font-medium text-blue-600">
                    ₹{(analyticsData.oneTimeRevenue/1000).toFixed(0)}K
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Period Growth</span>
                  <span className="font-medium text-green-600">
                    {((analyticsData.revenueInPeriod / (analyticsData.totalRevenue - analyticsData.revenueInPeriod)) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Top Product</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {analyticsData.revenueByProduct && analyticsData.revenueByProduct.length > 0
                    ? analyticsData.revenueByProduct[0].name
                    : 'No data'
                  }
                </span>
                <span className="font-medium text-gray-900">
                  {analyticsData.revenueByProduct && analyticsData.revenueByProduct.length > 0
                    ? `₹${(analyticsData.revenueByProduct[0].value/100000).toFixed(1)}L`
                    : '₹0L'
                  }
                </span>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Active AMCs</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Active Contracts</span>
                <span className="font-medium text-green-600">
                  {analyticsData.amcStatusDistribution && analyticsData.amcStatusDistribution.length > 0
                    ? (analyticsData.amcStatusDistribution.find(item => item.status === 'active')?.value || 0)
                    : 0
                  }
                </span>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Payment Health</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Payment Rate</span>
                <span className="font-medium text-green-600">
                  {analyticsData.paymentStatusDistribution && analyticsData.paymentStatusDistribution.length > 0
                    ? (((analyticsData.paymentStatusDistribution.find(item => item.status === 'paid')?.value || 0) /
                        analyticsData.paymentStatusDistribution.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)
                    : '0'
                  }%
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FinancialAnalytics;
