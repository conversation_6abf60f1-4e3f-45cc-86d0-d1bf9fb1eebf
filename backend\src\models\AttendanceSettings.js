import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const AttendanceSettings = sequelize.define('AttendanceSettings', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    // General Settings
    enable_gps_tracking: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Enable GPS location tracking for attendance',
    },
    enable_face_recognition: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Enable face recognition for attendance',
    },
    enable_biometric: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Enable biometric device integration',
    },
    enable_qr_code: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Enable QR code scanning for attendance',
    },
    
    // Notification Settings
    send_daily_reminders: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Send daily attendance reminders',
    },
    reminder_time: {
      type: DataTypes.TIME,
      allowNull: true,
      defaultValue: '08:45:00',
      comment: 'Time to send daily reminders',
    },
    send_late_arrival_alerts: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Send alerts for late arrivals',
    },
    send_absence_alerts: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Send alerts for absences',
    },
    send_overtime_alerts: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Send alerts for overtime work',
    },
    
    // Approval Settings
    require_manager_approval: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Require manager approval for manual entries',
    },
    auto_approve_threshold_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 30,
      comment: 'Auto-approve manual entries within this threshold',
    },
    
    // Payroll Integration
    enable_payroll_integration: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable integration with payroll system',
    },
    payroll_calculation_method: {
      type: DataTypes.ENUM('daily', 'monthly', 'hourly'),
      allowNull: false,
      defaultValue: 'monthly',
      comment: 'Method for payroll calculation',
    },
    
    // Holiday Settings
    auto_mark_holidays: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Automatically mark holidays in attendance',
    },
    holiday_calendar_source: {
      type: DataTypes.ENUM('manual', 'google', 'outlook', 'api'),
      allowNull: false,
      defaultValue: 'manual',
      comment: 'Source for holiday calendar',
    },
    
    // Reporting Settings
    enable_real_time_reports: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable real-time attendance reports',
    },
    report_generation_time: {
      type: DataTypes.TIME,
      allowNull: true,
      defaultValue: '23:30:00',
      comment: 'Time to generate daily reports',
    },
    auto_email_reports: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Automatically email reports to managers',
    },
    
    // Mobile App Settings
    allow_mobile_check_in: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Allow check-in from mobile app',
    },
    mobile_location_accuracy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 50,
      comment: 'Required GPS accuracy in meters for mobile check-in',
    },
    offline_sync_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable offline sync for mobile app',
    },
    
    // Security Settings
    max_check_in_distance: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 100,
      comment: 'Maximum allowed distance from office for check-in (meters)',
    },
    enable_ip_restriction: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Restrict attendance to specific IP addresses',
    },
    allowed_ip_addresses: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Array of allowed IP addresses',
    },
    
    // Custom Settings
    custom_fields: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Custom tenant-specific settings',
    },
    
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'attendance_settings',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
        unique: true,
      },
    ],
  });

  // Instance methods
  AttendanceSettings.prototype.isLocationAllowed = function(latitude, longitude) {
    if (!this.enable_gps_tracking) return true;
    
    // This would check against office locations
    // Implementation depends on how office locations are stored
    return true;
  };

  AttendanceSettings.prototype.isIpAllowed = function(ipAddress) {
    if (!this.enable_ip_restriction || !this.allowed_ip_addresses) {
      return true;
    }
    
    return this.allowed_ip_addresses.includes(ipAddress);
  };

  // Class methods
  AttendanceSettings.getDefaultSettings = function() {
    return {
      enable_gps_tracking: false,
      enable_face_recognition: false,
      enable_biometric: false,
      enable_qr_code: false,
      send_daily_reminders: true,
      reminder_time: '08:45:00',
      send_late_arrival_alerts: true,
      send_absence_alerts: true,
      send_overtime_alerts: false,
      require_manager_approval: false,
      auto_approve_threshold_minutes: 30,
      enable_payroll_integration: true,
      payroll_calculation_method: 'monthly',
      auto_mark_holidays: true,
      holiday_calendar_source: 'manual',
      enable_real_time_reports: true,
      report_generation_time: '23:30:00',
      auto_email_reports: false,
      allow_mobile_check_in: true,
      mobile_location_accuracy: 50,
      offline_sync_enabled: true,
      max_check_in_distance: 100,
      enable_ip_restriction: false,
      custom_fields: {},
    };
  };

  // Associations
  AttendanceSettings.associate = function(models) {
    AttendanceSettings.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });
  };

  return AttendanceSettings;
}
