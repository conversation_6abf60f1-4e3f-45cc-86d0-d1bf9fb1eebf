import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaFilter,
  <PERSON>a<PERSON>ye,
  FaEyeSlash,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaTable,
  FaTh,
  FaList
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import { apiService } from '../../services/api';
import Spinner from '../ui/Feedback/Spinner';
import ConfirmDialog from '../ui/ConfirmDialog';
import CallStatusForm from './CallStatusForm';

const CallStatusesManagement = () => {
  const [callStatuses, setCallStatuses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [viewMode, setViewMode] = useState('table');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ show: false, item: null });

  // Fetch call statuses
  const fetchCallStatuses = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage,
        limit: 10,
        search: searchTerm,
        sortBy,
        sortOrder,
      });

      if (filterCategory !== 'all') {
        params.append('category', filterCategory);
      }

      if (filterStatus !== 'all') {
        params.append('isActive', filterStatus === 'active');
      }

      const response = await apiService.get(`/master-data/call-statuses?${params}`);

      if (response.data?.success) {
        setCallStatuses(response.data.data.callstatus || []);
        setTotalPages(response.data.data.pagination?.totalPages || 1);
      } else {
        throw new Error(response.data?.message || 'Failed to fetch call statuses');
      }
    } catch (error) {
      console.error('Error fetching call statuses:', error);
      toast.error('Failed to load call statuses');
      setCallStatuses([]);
    } finally {
      setLoading(false);
    }
  };

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    fetchCallStatuses();
  }, [currentPage, searchTerm, filterCategory, filterStatus, sortBy, sortOrder]);

  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
    setCurrentPage(1);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setShowForm(true);
  };

  const handleDelete = (item) => {
    setDeleteConfirm({ show: true, item });
  };

  const confirmDelete = async () => {
    try {
      const response = await apiService.delete(`/master-data/call-statuses/${deleteConfirm.item.id}`);

      if (response.data?.success) {
        toast.success('Call status deleted successfully');
        fetchCallStatuses();
      } else {
        throw new Error(response.data?.message || 'Failed to delete call status');
      }
    } catch (error) {
      console.error('Error deleting call status:', error);
      toast.error(error.message || 'Failed to delete call status');
    } finally {
      setDeleteConfirm({ show: false, item: null });
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingItem(null);
    fetchCallStatuses();
  };

  const getSortIcon = (field) => {
    if (sortBy !== field) return <FaSort className="w-3 h-3 text-gray-400" />;
    return sortOrder === 'asc'
      ? <FaSortUp className="w-3 h-3 form-label-primary" />
      : <FaSortDown className="w-3 h-3 form-label-primary" />;
  };

  const getCategoryBadge = (category) => {
    const colors = {
      open: 'bg-info-100 text-info-800',
      in_progress: 'bg-warning-100 text-warning-800',
      resolved: 'bg-success-100 text-success-800',
      closed: 'bg-secondary-100 text-secondary-800',
      cancelled: 'bg-danger-100 text-danger-800'
    };

    return (
      <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${colors[category] || 'bg-gray-100 text-gray-800'}`}>
        {category?.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const getStatusBadge = (isActive) => {
    return (
      <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${
        isActive ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
      }`}
      >
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  // Check if a status can be deleted
  const canDeleteStatus = (status) => {
    // Allow deletion of default/system statuses but prevent deletion of custom statuses
    // Default categories that CAN be deleted
    const defaultCategories = ['in_progress', 'closed', 'open', 'resolved', 'on_hold'];
    return defaultCategories.includes(status.category?.toLowerCase());
  };

  if (loading && callStatuses.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-medium text-gray-900">Call Statuses</h2>
          <span className="text-sm text-gray-500">
            ({callStatuses.length} items)
          </span>
        </div>

        <div className="flex items-center space-x-3">
          {/* View Mode Toggle - Hidden on mobile, only show card view */}
          <div className="hidden sm:flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'table'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              title="Table View"
            >
              <FaList className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('card')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'card'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              title="Card View"
            >
              <FaTh className="w-4 h-4" />
            </button>
          </div>

          {/* Mobile - Force card view only */}
          <div className="sm:hidden">
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <div className="p-2 rounded-md bg-white text-primary-600 shadow-sm cursor-not-allowed">
                <FaTh className="w-4 h-4" />
              </div>
            </div>
          </div>

          <button
            onClick={() => {
              setEditingItem(null);
              setShowForm(true);
            }}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FaPlus className="w-4 h-4 mr-2" />
            Add New
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search call statuses..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {/* Category Filter */}
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="all">All Categories</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
            <option value="cancelled">Cancelled</option>
          </select>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          {/* Clear Filters */}
          <button
            onClick={() => {
              setSearchTerm('');
              setFilterCategory('all');
              setFilterStatus('all');
              setCurrentPage(1);
            }}
            className="px-4 py-2 text-sm text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Content */}
      {/* Table View - Hidden on mobile */}
      {viewMode === 'table' && (
        <div className="hidden sm:block">
          <div className="bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="overflow-x-auto max-w-full">
              <table className="w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/3"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Name</span>
                        {getSortIcon('name')}
                      </div>
                    </th>
                    <th
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-1/6"
                      onClick={() => handleSort('code')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Code</span>
                        {getSortIcon('code')}
                      </div>
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                      Category
                    </th>
                    <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                      Color
                    </th>
                    <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Status
                    </th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {callStatuses.map((status) => (
                    <tr key={status.id} className="hover:bg-gray-50">
                      <td className="px-3 py-3">
                        <div className="text-sm font-medium text-gray-900 truncate">{status.name}</div>
                        {status.description && (
                          <div className="text-xs text-gray-500 truncate max-w-[200px]" title={status.description}>
                            {status.description}
                          </div>
                        )}
                      </td>
                      <td className="px-3 py-3">
                        <span className="text-xs text-gray-900 font-mono">{status.code}</span>
                      </td>
                      <td className="px-3 py-3">
                        {getCategoryBadge(status.category)}
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div
                          className="w-6 h-6 rounded-full border border-gray-300 mx-auto"
                          style={{ backgroundColor: status.color }}
                          title={status.color}
                        >
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        {getStatusBadge(status.is_active)}
                      </td>
                      <td className="px-3 py-3 text-right">
                        <div className="flex items-center justify-end space-x-1">
                          <button
                            onClick={() => handleEdit(status)}
                            className="text-primary-600 hover:text-primary-900 p-1 rounded"
                            title="Edit"
                          >
                            <FaEdit className="w-3 h-3" />
                          </button>
                          {canDeleteStatus(status) && (
                            <button
                              onClick={() => handleDelete(status)}
                              className="text-red-600 hover:text-red-900 p-1 rounded"
                              title="Delete"
                            >
                              <FaTrash className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Card View - Always visible on mobile, toggleable on desktop */}
      {viewMode === 'card' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {callStatuses.map((status) => (
            <div key={status.id} className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: status.color }}
                  >
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">{status.name}</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(status)}
                    className="text-primary-600 hover:text-primary-900 p-1 rounded"
                    title="Edit"
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                  {canDeleteStatus(status) && (
                    <button
                      onClick={() => handleDelete(status)}
                      className="text-red-600 hover:text-red-900 p-1 rounded"
                      title="Delete"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Code:</span>
                  <span className="ml-2 text-sm text-gray-900 font-mono">{status.code}</span>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-500">Category:</span>
                  <div className="mt-1">
                    {getCategoryBadge(status.category)}
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-500">Status:</span>
                  <div className="mt-1">
                    {getStatusBadge(status.is_active)}
                  </div>
                </div>

                {status.description && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Description:</span>
                    <p className="mt-1 text-sm text-gray-600">{status.description}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Mobile Card View - Force card view on mobile */}
      <div className="sm:hidden">
        <div className="grid grid-cols-1 gap-6">
          {callStatuses.map((status) => (
            <div key={status.id} className="bg-white rounded-lg shadow-sm border p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: status.color }}
                  >
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">{status.name}</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(status)}
                    className="text-primary-600 hover:text-primary-900 p-1 rounded"
                    title="Edit"
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                  {canDeleteStatus(status) && (
                    <button
                      onClick={() => handleDelete(status)}
                      className="text-red-600 hover:text-red-900 p-1 rounded"
                      title="Delete"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <span className="text-sm font-medium text-gray-500">Code:</span>
                  <span className="ml-2 text-sm text-gray-900 font-mono">{status.code}</span>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-500">Category:</span>
                  <div className="mt-1">
                    {getCategoryBadge(status.category)}
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium text-gray-500">Status:</span>
                  <div className="mt-1">
                    {getStatusBadge(status.is_active)}
                  </div>
                </div>

                {status.description && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Description:</span>
                    <p className="mt-1 text-sm text-gray-600">{status.description}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between bg-white px-4 py-3 border rounded-lg">
          <div className="flex items-center">
            <p className="text-sm text-gray-700">
              Page <span className="font-medium">{currentPage}</span> of{' '}
              <span className="font-medium">{totalPages}</span>
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {loading && callStatuses.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-modal">
          <Spinner size="xl" variant="white" />
        </div>
      )}

      {/* Form Modal */}
      {showForm && (
        <CallStatusForm
          item={editingItem}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowForm(false);
            setEditingItem(null);
          }}
        />
      )}

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={deleteConfirm.show}
        onClose={() => setDeleteConfirm({ show: false, item: null })}
        onConfirm={confirmDelete}
        title="Delete Call Status"
        message={`Are you sure you want to delete "${deleteConfirm.item?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700 focus:ring-red-500"
      />
    </div>
  );
};

export default CallStatusesManagement;
