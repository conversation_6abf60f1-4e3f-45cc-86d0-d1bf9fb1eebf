-- Fix service_calls table schema to make subject and description optional
-- This SQL script will update the database constraints

BEGIN;

-- Make subject column nullable
ALTER TABLE service_calls ALTER COLUMN subject DROP NOT NULL;

-- Make description column nullable  
ALTER TABLE service_calls ALTER COLUMN description DROP NOT NULL;

-- Verify the changes
SELECT 
    column_name, 
    is_nullable, 
    data_type,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'service_calls' 
AND column_name IN ('subject', 'description')
ORDER BY column_name;

COMMIT;

-- Display success message
SELECT 'Successfully made subject and description fields optional in service_calls table' as result;
