// Quick API validation script with authentication
// Run with: node validate-api.js

const http = require('http');

// Test credentials from seeder
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Admin@123'
};

function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      hostname: 'localhost',
      port: 8080,
      path: `/api/v1${path}`,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = http.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, error: 'Invalid JSON' });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function login() {
  console.log('🔐 Attempting login...');

  try {
    const result = await makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(TEST_CREDENTIALS)
    });

    if (result.status === 200 && result.data.status === 'success') {
      console.log('✅ Login successful');
      return result.data.token;
    } else {
      console.log('❌ Login failed:', result.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return null;
  }
}

async function validateAPI() {
  console.log('🔍 Validating Service Stats API...\n');

  try {
    // First, authenticate
    const token = await login();
    if (!token) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }

    console.log('\n📡 Testing service stats endpoint...');

    const result = await makeRequest('/service-calls/stats', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`Status: ${result.status}`);

    if (result.status === 200) {
      console.log('✅ API is responding');

      if (result.data && result.data.success) {
        console.log('✅ Response has success: true');

        const stats = result.data.data;
        console.log('\n📊 Stats Data:');
        console.log(`Total Calls: ${stats.totalCalls}`);
        console.log(`Recent Calls: ${stats.recentCalls}`);
        console.log(`Free Calls: ${stats.callsByType?.freeCalls}`);
        console.log(`AMC Calls: ${stats.callsByType?.amcCalls}`);
        console.log(`Per Calls: ${stats.callsByType?.paidCalls}`);
        console.log(`Status Count: ${stats.callsByStatus?.length} statuses`);

        if (stats.metadata) {
          console.log('\n🔧 Metadata:');
          console.log(`Has Error: ${stats.metadata.hasError}`);
          console.log(`Last Updated: ${stats.metadata.lastUpdated}`);
          if (stats.metadata.serverMetadata) {
            console.log(`Server Metadata Available: Yes`);
          }
        } else {
          console.log('\n⚠️ No metadata field in response');
        }

        // Show full response structure for debugging
        console.log('\n🔍 Full Response Structure:');
        console.log('Keys in data:', Object.keys(stats));
        console.log('Monthly Growth Keys:', Object.keys(stats.monthlyGrowth || {}));

        // Validate data structure
        console.log('\n🔍 Data Structure Validation:');
        const validations = [
          { field: 'totalCalls', type: 'number', value: stats.totalCalls },
          { field: 'recentCalls', type: 'number', value: stats.recentCalls },
          { field: 'callsByType.freeCalls', type: 'number', value: stats.callsByType?.freeCalls },
          { field: 'callsByType.amcCalls', type: 'number', value: stats.callsByType?.amcCalls },
          { field: 'callsByType.paidCalls', type: 'number', value: stats.callsByType?.paidCalls },
          { field: 'callsByStatus', type: 'array', value: stats.callsByStatus },
          { field: 'monthlyGrowth', type: 'object', value: stats.monthlyGrowth }
        ];

        let passed = 0;
        validations.forEach(validation => {
          const actualType = Array.isArray(validation.value) ? 'array' : typeof validation.value;
          const isValid = actualType === validation.type;
          console.log(`${isValid ? '✅' : '❌'} ${validation.field}: ${actualType} (expected: ${validation.type})`);
          if (isValid) passed++;
        });

        console.log(`\n📈 Validation Summary: ${passed}/${validations.length} passed`);

      } else {
        console.log('❌ Response missing success field or success: false');
        console.log('Response:', JSON.stringify(result.data, null, 2));
      }
    } else {
      console.log(`❌ API returned status ${result.status}`);
      console.log('Response:', result.data);
    }

  } catch (error) {
    console.log('❌ API validation failed:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Backend server may not be running on port 8080');
      console.log('   Try: cd backend && npm run dev');
    }
  }
}

validateAPI();
