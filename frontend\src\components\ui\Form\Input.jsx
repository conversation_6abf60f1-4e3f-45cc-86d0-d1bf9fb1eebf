import React from 'react';
import { cn } from '../../../utils/helpers';

const Input = React.forwardRef(({
  className,
  type = 'text',
  error,
  disabled,
  size = 'md',
  variant = 'default',
  ...props
}, ref) => {
  const baseClasses = 'w-full border rounded transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed';

  const variants = {
    default: 'border-gray-300 form-input-primary',
    error: 'border-red-500 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-500 focus:border-green-500 focus:ring-green-500',
  };

  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const currentVariant = error ? 'error' : variant;

  return (
    <input
      ref={ref}
      type={type}
      className={cn(
        baseClasses,
        variants[currentVariant],
        sizes[size],
        className
      )}
      disabled={disabled}
      {...props}
    />
  );
});

Input.displayName = 'Input';

export default Input;
