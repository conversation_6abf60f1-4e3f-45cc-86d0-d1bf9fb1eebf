import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';
import { Op } from 'sequelize';

/**
 * Test script to configure default renewal notification settings
 */
async function configureDefaultRenewalSettings() {
  try {
    console.log('🔧 Starting renewal notification configuration...');

    // Get the first active tenant for testing
    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    if (!tenant) {
      console.error('❌ No active tenant found. Please create a tenant first.');
      return;
    }

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);

    // Get the first user for created_by field
    const user = await models.User.findOne({
      where: { tenant_id: tenant.id },
      attributes: ['id', 'first_name', 'last_name']
    });

    if (!user) {
      console.error('❌ No user found for this tenant. Please create a user first.');
      return;
    }

    console.log(`✅ Using user: ${user.first_name} ${user.last_name} (${user.id})`);

    // Default renewal settings configuration
    const defaultSettings = [
      {
        field_name: 'amc_expiry_date',
        reminder_days: [30, 15, 7, 2],
        notification_channels: ['email'],
        is_active: true,
        description: 'AMC Expiry Date Notifications'
      },
      {
        field_name: 'amc_renewal_date',
        reminder_days: [30, 15, 7, 2],
        notification_channels: ['email'],
        is_active: true,
        description: 'AMC Renewal Date Notifications'
      },
      {
        field_name: 'tss_expiry_date',
        reminder_days: [30, 15, 7, 2],
        notification_channels: ['email'],
        is_active: true,
        description: 'TSS Expiry Date Notifications'
      },
      {
        field_name: 'tss_renewal_date',
        reminder_days: [30, 15, 7, 2],
        notification_channels: ['email'],
        is_active: true,
        description: 'TSS Renewal Date Notifications'
      }
    ];

    console.log('📋 Creating default renewal notification settings...');

    const createdSettings = [];

    for (const setting of defaultSettings) {
      try {
        // Check if setting already exists
        const existingSetting = await models.RenewalNotificationSettings.findOne({
          where: {
            tenant_id: tenant.id,
            field_name: setting.field_name
          }
        });

        if (existingSetting) {
          console.log(`⏭️  Setting for ${setting.field_name} already exists, updating...`);
          
          await existingSetting.update({
            reminder_days: setting.reminder_days,
            notification_channels: setting.notification_channels,
            is_active: setting.is_active
          });

          createdSettings.push(existingSetting);
          console.log(`✅ Updated: ${setting.field_name}`);
        } else {
          console.log(`➕ Creating new setting for ${setting.field_name}...`);
          
          const newSetting = await models.RenewalNotificationSettings.create({
            tenant_id: tenant.id,
            field_name: setting.field_name,
            reminder_days: setting.reminder_days,
            notification_channels: setting.notification_channels,
            is_active: setting.is_active,
            created_by: user.id
          });

          createdSettings.push(newSetting);
          console.log(`✅ Created: ${setting.field_name}`);
        }
      } catch (error) {
        console.error(`❌ Error processing ${setting.field_name}:`, error.message);
      }
    }

    console.log('\n📊 Configuration Summary:');
    console.log(`✅ Total settings configured: ${createdSettings.length}`);
    
    // Verify settings are retrievable
    console.log('\n🔍 Verifying settings are retrievable...');
    const allSettings = await models.RenewalNotificationSettings.findAll({
      where: { tenant_id: tenant.id },
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name'],
          required: false
        }
      ],
      order: [['field_name', 'ASC']]
    });

    console.log('\n📋 Current Renewal Notification Settings:');
    allSettings.forEach(setting => {
      console.log(`  📌 ${setting.field_name}:`);
      console.log(`     - Reminder Days: [${setting.reminder_days.join(', ')}]`);
      console.log(`     - Channels: [${setting.notification_channels.join(', ')}]`);
      console.log(`     - Active: ${setting.is_active}`);
      console.log(`     - Created by: ${setting.creator ? `${setting.creator.first_name} ${setting.creator.last_name}` : 'System'}`);
      console.log('');
    });

    // Test notification schedule generation
    console.log('🔄 Testing notification schedule generation...');
    
    try {
      const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
      const renewalService = new RenewalNotificationService();
      await renewalService.initialize();
      
      const scheduledCount = await renewalService.scheduleRenewalNotificationsForTenant(tenant.id);
      console.log(`✅ Schedule generation test completed: ${scheduledCount} notifications scheduled`);
    } catch (scheduleError) {
      console.error('⚠️  Schedule generation test failed:', scheduleError.message);
      console.log('   This is expected if no customer renewal data exists yet.');
    }

    console.log('\n🎉 Default renewal settings configuration completed successfully!');
    
    return {
      success: true,
      tenant: tenant,
      settingsCount: createdSettings.length,
      settings: allSettings
    };

  } catch (error) {
    console.error('❌ Error configuring default renewal settings:', error);
    throw error;
  }
}

/**
 * Test API endpoint simulation
 */
async function testAPIEndpoints() {
  try {
    console.log('\n🧪 Testing API endpoint functionality...');

    // Get tenant for testing
    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    if (!tenant) {
      console.error('❌ No tenant found for API testing');
      return;
    }

    // Simulate GET /api/v1/renewal-notifications/settings
    console.log('📡 Testing GET /api/v1/renewal-notifications/settings...');
    const settings = await models.RenewalNotificationSettings.findAll({
      where: { tenant_id: tenant.id },
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
      order: [['field_name', 'ASC'], ['created_at', 'DESC']],
    });

    console.log(`✅ GET settings successful: ${settings.length} settings found`);

    // Simulate GET /api/v1/renewal-notifications/stats
    console.log('📡 Testing GET /api/v1/renewal-notifications/stats...');
    const stats = await models.NotificationSchedule.findAll({
      where: { tenant_id: tenant.id },
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    const pendingNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'scheduled',
        notify_at: {
          [Op.lte]: new Date(),
        },
      },
    });

    console.log(`✅ GET stats successful: ${stats.length} status groups, ${pendingNotifications} pending`);

    console.log('\n📊 API Test Results:');
    console.log(`  - Settings endpoint: ✅ Working (${settings.length} records)`);
    console.log(`  - Stats endpoint: ✅ Working (${pendingNotifications} pending notifications)`);

    return {
      success: true,
      settingsCount: settings.length,
      pendingNotifications: pendingNotifications,
      stats: stats
    };

  } catch (error) {
    console.error('❌ API endpoint testing failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting TallyCRM Renewal Notification Configuration Test\n');

    // Phase 1: Configure default settings
    const configResult = await configureDefaultRenewalSettings();
    
    // Phase 2: Test API endpoints
    const apiResult = await testAPIEndpoints();

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`  ✅ Renewal settings configured: ${configResult.settingsCount}`);
    console.log(`  ✅ API endpoints tested: 2/2 working`);
    console.log(`  ✅ Tenant: ${configResult.tenant.name}`);
    console.log(`  ✅ Pending notifications: ${apiResult.pendingNotifications}`);

    process.exit(0);

  } catch (error) {
    console.error('\n❌ Configuration test failed:', error);
    process.exit(1);
  }
}

// Run the configuration
main();
