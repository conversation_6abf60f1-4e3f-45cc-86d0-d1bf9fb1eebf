import React from 'react';
import { Con<PERSON><PERSON>, <PERSON>, But<PERSON> } from '../ui';
import preminfoLogo from '../../assets/preminfologo.png';

const ErrorFallback = ({ error, resetErrorBoundary }) => {
  return (
    <Container className="mt-20">
      <div className="flex justify-center">
        <div className="w-full max-w-2xl lg:max-w-xl">
          <Card className="text-center">
            <div className="p-12">
              {/* Logo */}
              <div className="mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 p-0" style={{ backgroundColor: '#ffffff' }}>
                  <img
                    src={preminfoLogo}
                    alt="Preminfo Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>

              <div className="mb-6">
                <i className="bi bi-exclamation-triangle text-red-600 text-6xl"></i>
              </div>

              <h2 className="text-red-600 text-2xl font-semibold mb-3">Oops! Something went wrong</h2>

              <p className="text-gray-500 mb-6">
                We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
              </p>

              {process.env.NODE_ENV === 'development' && (
                <div className="mb-6">
                  <details className="text-left">
                    <summary className="inline-flex items-center px-3 py-1 text-sm border border-gray-300 rounded text-gray-700 hover:bg-gray-50 cursor-pointer mb-3">
                      Show Error Details
                    </summary>
                    <pre className="bg-gray-100 p-3 rounded text-red-600 text-sm text-left whitespace-pre-wrap">
                      {error.message}
                      {error.stack && (
                        <>
                          <br />
                          <br />
                          {error.stack}
                        </>
                      )}
                    </pre>
                  </details>
                </div>
              )}

              <div className="flex gap-2 justify-center">
                <Button
                  variant="primary"
                  onClick={resetErrorBoundary}
                  className="px-4"
                >
                  <i className="bi bi-arrow-clockwise mr-2"></i>
                  Try Again
                </Button>

                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/'}
                  className="px-4"
                >
                  <i className="bi bi-house mr-2"></i>
                  Go Home
                </Button>
              </div>

              <hr className="my-4 border-gray-200" />

              <p className="text-gray-500 text-sm mb-0">
                If this problem persists, please contact our support team at{' '}
                <a href="mailto:<EMAIL>" className="text-purple-600 hover:text-purple-500"><EMAIL></a>
              </p>
            </div>
          </Card>
        </div>
      </div>
    </Container>
  );
};

export default ErrorFallback;
