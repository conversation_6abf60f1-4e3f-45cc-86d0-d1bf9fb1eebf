# Customer Import Guide

## Overview
The Customer Import functionality allows you to bulk import customer data from Excel (.xlsx, .xls) or CSV files. This guide walks you through the complete import process.

## Prerequisites
- You must be logged in with appropriate permissions (customers.create)
- Your file must be in Excel (.xlsx, .xls) or CSV format
- File size must be less than 10MB

## Step-by-Step Import Process

### Step 1: Access Import Page
1. Navigate to **Customers** section
2. Click on **Import** button or go to `/customers/import`

### Step 2: Prepare Your Data File
Download the template file to see the required format and fields:

#### Required Fields (marked with *)
- **Company Name*** - Customer company name
- **Customer Code*** - Unique customer identifier
- **Tally Serial Number*** - Unique Tally serial number
- **Admin Email*** - Primary admin email
- **MD Contact Person*** - Managing Director contact name
- **MD Phone Number*** - MD phone number (with country code)
- **MD Email*** - MD email address
- **Office Contact Person*** - Office contact name
- **Office Mobile Number*** - Office mobile number
- **Office Email*** - Office email address
- **Auditor Name*** - Auditor name
- **Auditor Number*** - Auditor phone number
- **Auditor Email*** - Auditor email
- **Tax Consultant Name*** - Tax consultant name
- **Tax Consultant Number*** - Tax consultant phone
- **Tax Consultant Email*** - Tax consultant email
- **Area*** - Business area/location
- **Number of Tally Users*** - Number of users (numeric)
- **Executive Name*** - Assigned sales executive
- **Status*** - Customer status (ACTIVE, INACTIVE, etc.)

#### Optional Fields
- IT Name, IT Number, IT Email
- Address fields (Line 1, Line 2, City, State, Country, PIN Code)
- Business details (GST Number, PAN Number, Industry, etc.)
- Additional information (Website, Lead Source, Notes, etc.)

### Step 3: Upload File
1. Click **"Select File"** button
2. Choose your Excel or CSV file
3. File will be validated automatically
4. If valid, you'll see a **"Preview Data"** button

### Step 4: Preview Data
1. Click **"Preview Data"** to analyze your file
2. The system will show:
   - **Total rows** processed
   - **Valid rows** that can be imported
   - **Error rows** with validation issues
   - **Duplicate rows** (if any)
3. Review the preview table showing first 20 rows
4. Check validation errors and fix them if needed

### Step 5: Handle Duplicates (if any)
If duplicates are found:
1. Review the duplicate entries shown
2. Select which duplicates to force import (if desired)
3. Unselected duplicates will be skipped

### Step 6: Execute Import
1. Click **"Import Data"** to proceed
2. If duplicates exist, choose your handling preference
3. The system will import valid rows and skip invalid ones
4. View the import results summary

### Step 7: Review Results
The results page shows:
- **Successfully imported** customers
- **Skipped rows** (errors or duplicates)
- **Error details** for failed imports
- Option to **"Import Another File"** or **"View Customers"**

## File Format Guidelines

### CSV Format
- Use comma (,) as delimiter
- Enclose text fields in quotes if they contain commas
- Use UTF-8 encoding
- First row must contain column headers

### Excel Format
- Use .xlsx or .xls format
- First row must contain column headers
- Data should start from row 2

### Phone Number Format
- Include country code (e.g., +91-9876543210)
- Accepted formats: +91-9876543210, +91 9876543210, 9876543210

### Email Format
- Must be valid email addresses
- Example: <EMAIL>

## Common Issues and Solutions

### File Upload Issues
- **"Invalid file type"**: Ensure file is .xlsx, .xls, or .csv
- **"File too large"**: Reduce file size to under 10MB
- **"No file selected"**: Make sure to select a file first

### Validation Errors
- **Missing required fields**: Fill all mandatory fields marked with *
- **Invalid email format**: Check email addresses are properly formatted
- **Invalid phone numbers**: Include country code and proper format
- **Duplicate customer codes**: Use unique customer codes
- **Invalid number of users**: Must be a positive number

### Authentication Issues
- **"Please login first"**: Your session expired, login again
- **"Permission denied"**: Contact admin for customers.create permission

## Best Practices

1. **Start Small**: Test with a few rows first
2. **Use Template**: Download and use the provided template
3. **Validate Data**: Check data quality before import
4. **Backup**: Keep a backup of your original data
5. **Review Results**: Always check import results for errors

## Support
If you encounter issues:
1. Check this guide for common solutions
2. Review error messages carefully
3. Contact system administrator for permission issues
4. Keep error logs for troubleshooting

## Sample Data Format
```csv
Company Name*,Customer Code*,Tally Serial Number*,Admin Email*,MD Contact Person*,MD Phone Number*,MD Email*,Office Contact Person*,Office Mobile Number*,Office Email*,Auditor Name*,Auditor Number*,Auditor Email*,Tax Consultant Name*,Tax Consultant Number*,Tax Consultant Email*,IT Name,IT Number,IT Email,Area*,Number of Tally Users*,Executive Name*,Status*
ABC Technologies Pvt Ltd,ABC001,TSN001,<EMAIL>,John Doe,+91-9876543210,<EMAIL>,Jane Smith,+91-9876543211,<EMAIL>,CA Rajesh Kumar,+91-9876543212,<EMAIL>,Priya Sharma,+91-9876543213,<EMAIL>,Amit Patel,+91-9876543214,<EMAIL>,Mumbai Central,5,Sales Executive 1,ACTIVE
```
