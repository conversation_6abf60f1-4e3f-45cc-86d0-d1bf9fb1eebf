import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/helpers';
import LoadingScreen from './LoadingScreen';

const ResponsiveTable = ({
  columns,
  data,
  onRowClick,
  className,
  mobileCardRender,
  loading = false,
  emptyMessage = 'No data available',
  laptopOptimized = true, // New prop for laptop optimization
  compactMode = false, // New prop for compact display
  showTooltips = true // New prop for tooltip support
}) => {
  if (loading) {
    return (
      <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-theme-100">
        <LoadingScreen
          title="Loading Data..."
          subtitle="Fetching table content"
          variant="modal"
        />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-purple-100">
        <div className="p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            <i className="bi bi-inbox text-purple-600 text-2xl"></i>
          </div>
          <h6 className="text-gray-900 font-medium mb-1">No Data Found</h6>
          <p className="text-gray-500 text-sm">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  // Helper function to determine if column should be hidden on laptop
  const shouldHideOnLaptop = (column) => {
    return laptopOptimized && (
      column.hideOnLaptop ||
      column.priority === 'low' ||
      (column.priority === 'medium' && window.innerWidth <= 1366)
    );
  };

  // Helper function to get responsive column classes
  const getColumnClasses = (column) => {
    let classes = cn(
      'px-2 sm:px-3 lg:px-4 py-2 sm:py-3 text-left text-xs font-bold uppercase tracking-wider',
      compactMode && 'table-laptop-padding table-laptop-text',
      column.className
    );

    if (laptopOptimized) {
      if (column.hideOnSmallLaptop) classes += ' col-hide-small-laptop';
      if (column.hideOnLaptop) classes += ' col-hide-laptop';
      if (column.showDesktopOnly) classes += ' col-show-desktop-only';
    }

    return classes;
  };

  // Helper function to get cell classes with responsive behavior
  const getCellClasses = (column) => {
    let classes = cn(
      'px-2 sm:px-3 lg:px-4 py-2 sm:py-3',
      compactMode && 'table-laptop-padding',
      column.cellClassName
    );

    if (laptopOptimized) {
      if (column.hideOnSmallLaptop) classes += ' col-hide-small-laptop';
      if (column.hideOnLaptop) classes += ' col-hide-laptop';
      if (column.showDesktopOnly) classes += ' col-show-desktop-only';
      if (column.truncate !== false) classes += ' text-truncate-responsive';
    }

    return classes;
  };

  return (
    <>
      {/* Desktop/Laptop Table View - Optimized for laptop screens */}
      <div className={cn(
        'hidden sm:block bg-white shadow-xl rounded-2xl border border-theme-100 service-list-container',
        laptopOptimized ? 'table-container-laptop' : 'overflow-hidden'
      )}>
        <div className={cn(
          'table-wrapper',
          laptopOptimized ? 'overflow-x-hidden overflow-y-visible' : 'overflow-x-auto overflow-y-visible'
        )}>
          <table className={cn(
            'w-full divide-y divide-theme-200',
            laptopOptimized ? 'table-fixed' : 'min-w-full',
            compactMode && 'table-compact',
            className
          )}>
            <thead style={{ backgroundColor: 'var(--primary-color, #1d5795)' }}>
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    className={getColumnClasses(column)}
                    style={{
                      color: 'var(--primary-text, #ffffff)',
                      width: laptopOptimized && column.width ? column.width : 'auto'
                    }}
                    title={showTooltips ? column.header : undefined}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className={cn(
                    'hover:bg-gray-50 transition-all duration-200',
                    onRowClick && 'cursor-pointer',
                    rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                  )}
                  onClick={() => onRowClick && onRowClick(row)}
                >
                  {columns.map((column, colIndex) => {
                    const cellContent = column.render ? column.render(row, rowIndex) : row[column.key];
                    const shouldTruncate = laptopOptimized && column.truncate !== false;

                    return (
                      <td
                        key={colIndex}
                        className={cn(
                          getCellClasses(column),
                          column.key === 'actions' && 'table-dropdown'
                        )}
                        title={showTooltips && shouldTruncate && typeof cellContent === 'string' ? cellContent : undefined}
                        style={{
                          width: laptopOptimized && column.width ? column.width : 'auto',
                          maxWidth: laptopOptimized && column.maxWidth ? column.maxWidth : 'none',
                          overflow: column.key === 'actions' ? 'visible' : 'hidden'
                        }}
                      >
                        <div className={shouldTruncate ? 'truncate' : ''}>
                          {cellContent}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="sm:hidden space-y-4">
        {data.map((row, index) => (
          <div
            key={index}
            className={cn(
              'bg-white shadow-xl rounded-2xl p-5 border border-gray-100 hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1',
              onRowClick && 'cursor-pointer active:scale-95'
            )}
            onClick={() => onRowClick && onRowClick(row)}
          >
            {mobileCardRender ? mobileCardRender(row, index) : (
              <div className="space-y-4">
                {columns
                  .filter(column => !column.hideOnMobile)
                  .slice(0, 4)
                  .map((column, colIndex) => (
                    <div key={colIndex} className="flex justify-between items-start">
                      <span className="text-sm font-bold uppercase tracking-wide" style={{ color: 'var(--primary-color, #1d5795)' }}>
                        {column.header}
                      </span>
                      <span className="text-sm text-gray-900 text-right ml-3 font-medium">
                        {column.render ? column.render(row, index) : row[column.key]}
                      </span>
                    </div>
                  ))}
                {columns.filter(column => !column.hideOnMobile).length > 4 && (
                  <div className="pt-3 border-t border-gray-100">
                    <button className="text-sm font-bold hover:opacity-80 transition-colors" style={{ color: 'var(--primary-color, #1d5795)' }}>
                      View Details →
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </>
  );
};

export default ResponsiveTable;
