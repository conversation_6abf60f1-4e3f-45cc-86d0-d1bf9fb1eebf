/* TallyCRM Global Styles */

/* CSS Variables */
:root {
  /* Primary Colors - Will be dynamically updated by theme manager */
  --primary-color: #2f69b3;
  --primary-dark: #1e4a7a;
  --primary-darker: #0f2a4a;
  --primary-light: #4a8bc2;
  --primary-lighter: #6ba3d0;
  --primary-rgb: 47, 105, 179;

  /* Dynamic Text Colors - Will be dynamically updated by theme manager */
  --primary-text: #ffffff;
  --primary-text-rgb: 255, 255, 255;
  --is-light-theme: 0;

  /* Secondary Colors */
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;

  /* Neutral Colors */
  --white: #ffffff;
  --light: #f8f9fa;
  --dark: #212529;
  --muted: #6c757d;

  /* Background Colors */
  --bg-primary: #f8f9fa;
  --bg-secondary: #e9ecef;
  --bg-dark: #343a40;

  /* Border Colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;

  /* Theme Solid Colors - Will be dynamically updated */
  --sidebar-background: var(--primary-color);
  --button-background: var(--primary-color);
  --auth-background: var(--primary-color);

  /* Theme-consistent icon and background colors - Will be dynamically updated */
  --theme-icon-bg: rgba(var(--primary-rgb), 0.1);
  --theme-icon-bg-hover: rgba(var(--primary-rgb), 0.2);
  --theme-icon-color: var(--primary-color);
  --theme-badge-bg: rgba(var(--primary-rgb), 0.1);
  --theme-alert-bg: rgba(var(--primary-rgb), 0.1);

  /* Theme-consistent content backgrounds */
  --theme-content-bg: rgba(var(--primary-rgb), 0.08);
  --theme-modal-bg: rgba(var(--primary-rgb), 0.05);
  --theme-card-bg: rgba(var(--primary-rgb), 0.03);

  /* Tailwind-compatible color scale variables - Will be dynamically updated */
  --primary-50: 239, 246, 255;
  --primary-100: 219, 234, 254;
  --primary-200: 191, 219, 254;
  --primary-300: 147, 197, 253;
  --primary-400: 96, 165, 250;
  --primary-500: 59, 130, 246;
  --primary-600: 37, 99, 235;
  --primary-700: 29, 78, 216;
  --primary-800: 30, 64, 175;
  --primary-900: 30, 58, 138;
  --primary-950: 23, 37, 84;

  /* Theme color scale (same as primary but separate for clarity) */
  --theme-50: var(--primary-50);
  --theme-100: var(--primary-100);
  --theme-200: var(--primary-200);
  --theme-300: var(--primary-300);
  --theme-400: var(--primary-400);
  --theme-500: var(--primary-500);
  --theme-600: var(--primary-600);
  --theme-700: var(--primary-700);
  --theme-800: var(--primary-800);
  --theme-900: var(--primary-900);

  /* Shadow */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --shadow-primary: 0 0.5rem 1rem rgba(var(--primary-rgb), 0.15);

  /* Border Radius */
  --border-radius: 0.375rem;
  --border-radius-sm: 0.25rem;
  --border-radius-lg: 0.5rem;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;

  /* Typography */
  --font-family-sans-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.125rem;
  --line-height-base: 1.5;

  /* Transitions */
  --transition: all 0.15s ease-in-out;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: var(--line-height-base);
  color: var(--dark);
  background-color: var(--bg-primary);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons */
.btn {
  transition: var(--transition);
}

.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Cards */
.card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow);
}

/* Forms */
.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Tables */
.table {
  --bs-table-bg: transparent;
}

.table-hover tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* Utilities */
.text-muted {
  color: var(--muted) !important;
}

.bg-light {
  background-color: var(--bg-primary) !important;
}

.border-light {
  border-color: var(--border-light) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Loading Spinner */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Timeline Styles */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--border-color);
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 5px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--white);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.timeline-content {
  background: var(--bg-primary);
  padding: 15px;
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    border: 1px solid var(--border-color) !important;
    box-shadow: none !important;
  }

  .timeline::before {
    background: #000 !important;
  }

  .timeline-marker {
    border-color: #000 !important;
  }
}

/* Dropdown Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-in {
  animation-duration: 100ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  animation-fill-mode: forwards;
}

.fade-in-0 {
  animation-name: fadeIn;
}

.zoom-in-95 {
  animation-name: zoomIn;
}

.duration-100 {
  animation-duration: 100ms;
}

/* Dropdown Positioning and Z-Index */
.dropdown-menu {
  z-index: 1000;
  position: absolute;
  min-width: 12rem;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

/* Responsive Table Optimizations for Laptop Screens */
.table-container {
  position: relative;
  overflow-y: visible;
}

/* Laptop-optimized table container - no horizontal scroll */
.table-container-laptop {
  position: relative;
  overflow: hidden;
  width: 100%;
  margin: 0 auto;
  max-width: 100%;
}

/* Prevent table cells from clipping dropdowns */
.table td.dropdown-cell {
  position: relative;
  overflow: visible;
}

/* Ensure dropdown containers in tables have proper z-index and overflow */
.table-container-laptop [data-dropdown-container] {
  position: relative;
  z-index: 1000;
}

/* Ensure table rows don't clip dropdowns */
.table-container-laptop tbody tr {
  position: relative;
}

/* Fix for status filter dropdown z-index */
.z-dropdown {
  z-index: var(--z-dropdown, 1000) !important;
  position: relative;
}

/* Ensure lead page filter containers don't clip dropdowns */
.leads-filter-container {
  position: relative;
  z-index: 100;
}

.leads-filter-container select {
  position: relative;
  z-index: var(--z-dropdown, 1000);
}

/* Responsive column utilities */
.col-hide-laptop {
  @media (max-width: 1440px) {
    display: none !important;
  }
}

.col-hide-small-laptop {
  @media (max-width: 1366px) {
    display: none !important;
  }
}

.col-show-desktop-only {
  @media (max-width: 1600px) {
    display: none !important;
  }
}

/* Text truncation with tooltip support */
.text-truncate-responsive {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 0;
  min-width: 0;
}

/* Flexible column widths for laptop optimization */
.col-flex-1 { flex: 1; min-width: 0; }
.col-flex-2 { flex: 2; min-width: 0; }
.col-flex-3 { flex: 3; min-width: 0; }

/* Laptop-specific table cell padding */
@media (min-width: 1024px) and (max-width: 1440px) {
  .table-laptop-padding {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .table-laptop-text {
    font-size: 0.75rem !important;
  }
}

/* Compact table styling for laptops */
.table-compact {
  @media (min-width: 1024px) and (max-width: 1440px) {
    font-size: 0.75rem;
  }
}

.table-compact th,
.table-compact td {
  @media (min-width: 1024px) and (max-width: 1440px) {
    padding: 0.375rem 0.5rem;
  }
}

/* Ultra-compact mode for laptop screens */
@media (min-width: 1024px) and (max-width: 1440px) {
  .service-list-container .table-container-laptop {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  .service-list-container .table-compact th,
  .service-list-container .table-compact td {
    padding: 0.25rem 0.375rem;
    font-size: 0.75rem;
  }

  .service-list-container .table-compact th {
    font-size: 0.7rem;
    font-weight: 600;
  }
}

/* Enhanced Z-Index Hierarchy for Dropdowns */
:root {
  --z-dropdown: 1000;
  --z-dropdown-high: 2000;
  --z-dropdown-highest: 3000;
  --z-filter-dropdown: 4000;
  --z-date-picker: 5000;
  --z-status-modal: 55000;
  --z-timer-modal: 60000;
  --z-modal: 9999;
}

/* Table dropdown fixes */
.table-dropdown {
  z-index: var(--z-dropdown-high) !important;
  position: relative;
}

.table-dropdown .dropdown-menu,
.table-dropdown select {
  z-index: calc(var(--z-dropdown-high) + 100) !important;
}

/* Filter dropdown fixes */
.filter-dropdown {
  z-index: var(--z-dropdown-high) !important;
  position: relative;
}

.filter-dropdown select {
  z-index: calc(var(--z-dropdown-high) + 50) !important;
  position: relative;
}

/* Date picker dropdown fixes */
.date-picker-dropdown {
  z-index: calc(var(--z-dropdown-high) + 200) !important;
  position: relative;
}

/* Action button dropdown fixes */
.action-dropdown {
  z-index: calc(var(--z-dropdown-high) + 300) !important;
  position: relative;
}

.action-dropdown .dropdown-menu {
  z-index: calc(var(--z-dropdown-high) + 400) !important;
}

/* Service list specific dropdown fixes */
.service-list-container .table-container {
  overflow: visible !important;
  width: 100%;
  margin: 0 auto;
}

.service-list-container .table-wrapper {
  overflow-x: auto;
  overflow-y: visible !important;
  width: 100%;
}

/* Ensure service list container has proper alignment */
.service-list-container {
  width: 100%;
  max-width: 100%;
}

.service-list-container > div {
  width: 100%;
}

/* Ensure all dropdown containers have proper stacking */
.dropdown-container {
  position: relative;
  z-index: var(--z-dropdown);
}

.dropdown-container select,
.dropdown-container .dropdown-menu {
  z-index: calc(var(--z-dropdown) + 100);
}

/* Fix for specific dropdown issues in tables */
.table tbody tr {
  position: relative;
}

.table tbody tr td {
  overflow: visible;
}

/* Date range picker specific fixes */
.date-range-picker {
  z-index: var(--z-dropdown-highest) !important;
  position: relative;
}

.date-range-picker .dropdown-menu {
  z-index: calc(var(--z-dropdown-highest) + 100) !important;
}

/* Specific fix for leads page action dropdown menu */
.action-dropdown > div[role="menu"],
.action-dropdown > div > div[role="menu"] {
  z-index: 10000 !important;
  position: absolute !important;
  background: white !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Ensure leads table containers don't clip dropdowns */
.leads-table-container,
.leads-table-container .table-wrapper,
.leads-table-container .responsive-table-container,
.leads-table-container .table-container-laptop {
  overflow: visible !important;
}

/* Force visibility for action dropdown menus */
.action-dropdown,
.action-dropdown * {
  overflow: visible !important;
}

/* Specific targeting for the dropdown menu content */
.action-dropdown .py-1 {
  z-index: 10001 !important;
  position: relative !important;
}

/* Ensure main layout doesn't clip dropdowns */
main.theme-content-bg {
  overflow-y: visible !important;
}

/* Global dropdown visibility fix */
.dropdown-menu,
[role="menu"] {
  z-index: 10000 !important;
  position: absolute !important;
}

/* Prevent any parent from clipping dropdowns */
.action-dropdown,
.action-dropdown *,
.leads-table-container,
.leads-table-container *,
.service-list-container,
.service-list-container * {
  overflow: visible !important;
}

/* Modal z-index hierarchy - highest priority */
.z-modal-overlay {
  z-index: 50000 !important;
}

.z-modal-content {
  z-index: 50001 !important;
  position: relative;
}

/* Ensure modals are above all dropdowns and filters */
.fixed.z-modal-overlay {
  z-index: 50000 !important;
}

/* Timer History Modal specific fixes */
.timer-history-modal {
  z-index: 60000 !important;
}

.timer-history-modal .timer-history-content {
  max-height: calc(90vh - 120px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Ensure proper scrolling in timer history modal */
.timer-history-modal .flex-1 {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Timer text optimization for long durations */
.timer-history-modal .timer-summary-card {
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.timer-history-modal .timer-value {
  word-break: break-word;
  hyphens: auto;
  line-height: 1.2;
  max-width: 100%;
}

/* Responsive timer text sizing */
@media (max-width: 768px) {
  .timer-history-modal .timer-value {
    font-size: 0.875rem !important;
    line-height: 1.1;
  }
}

/* Fix for date range filter dropdown */
.date-range-picker .dropdown-menu {
  z-index: 5001 !important;
  position: absolute !important;
}

/* Filter dropdown hierarchy */
.filter-dropdown select {
  position: relative !important;
}

.filter-dropdown {
  position: relative !important;
}
