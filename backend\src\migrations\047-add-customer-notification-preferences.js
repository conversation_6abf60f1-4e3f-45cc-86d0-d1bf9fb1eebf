/**
 * Migration: Add customer notification preferences
 * - Add notification preference columns to customers table
 * - Add notification templates table
 * - Add renewal notification settings table
 */

import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    // Add notification preference columns to customers table
    await queryInterface.addColumn('customers', 'notification_sms', {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable SMS notifications for this customer',
    }, { transaction });

    await queryInterface.addColumn('customers', 'notification_email', {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable email notifications for this customer',
    }, { transaction });

    await queryInterface.addColumn('customers', 'notification_whatsapp', {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Enable WhatsApp notifications for this customer',
    }, { transaction });

    // Create notification templates table
    await queryInterface.createTable('notification_templates', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tenant_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: 'Template name for identification',
      },
      type: {
        type: DataTypes.ENUM('new_lead', 'new_customer', 'service_call_created', 'service_call_completed', 'renewal_reminder', 'custom'),
        allowNull: false,
        comment: 'Type of notification template',
      },
      channel: {
        type: DataTypes.ENUM('sms', 'email', 'whatsapp'),
        allowNull: false,
        comment: 'Notification channel',
      },
      subject: {
        type: DataTypes.STRING(200),
        allowNull: true,
        comment: 'Subject line for email notifications',
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: 'Template content with placeholders',
      },
      variables: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Available variables for this template',
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether this template is active',
      },
      is_default: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Whether this is the default template for this type',
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    }, { transaction });

    // Create renewal notification settings table
    await queryInterface.createTable('renewal_notification_settings', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tenant_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      field_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: 'Name of the expiry field (e.g., tally_renewal_date, support_expiry_date)',
      },
      reminder_days: {
        type: DataTypes.JSON,
        allowNull: false,
        comment: 'Array of days before expiry to send reminders (e.g., [30, 15, 7, 1])',
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether renewal notifications are active for this field',
      },
      notification_channels: {
        type: DataTypes.JSON,
        allowNull: false,
        defaultValue: '["email", "sms"]',
        comment: 'Channels to use for renewal notifications',
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    }, { transaction });

    // Add indexes
    await queryInterface.addIndex('notification_templates', ['tenant_id'], { transaction });
    await queryInterface.addIndex('notification_templates', ['type'], { transaction });
    await queryInterface.addIndex('notification_templates', ['channel'], { transaction });
    await queryInterface.addIndex('notification_templates', ['is_active'], { transaction });
    await queryInterface.addIndex('notification_templates', ['is_default'], { transaction });
    await queryInterface.addIndex('notification_templates', ['created_by'], { transaction });

    await queryInterface.addIndex('renewal_notification_settings', ['tenant_id'], { transaction });
    await queryInterface.addIndex('renewal_notification_settings', ['field_name'], { transaction });
    await queryInterface.addIndex('renewal_notification_settings', ['is_active'], { transaction });
    await queryInterface.addIndex('renewal_notification_settings', ['created_by'], { transaction });

    // Add unique constraint for template type + channel + tenant
    await queryInterface.addConstraint('notification_templates', {
      fields: ['tenant_id', 'type', 'channel', 'is_default'],
      type: 'unique',
      name: 'unique_default_template_per_type_channel',
      where: {
        is_default: true
      },
      transaction
    });

    // Add unique constraint for field name per tenant
    await queryInterface.addConstraint('renewal_notification_settings', {
      fields: ['tenant_id', 'field_name'],
      type: 'unique',
      name: 'unique_field_per_tenant',
      transaction
    });

    await transaction.commit();
    console.log('✅ Added customer notification preferences and templates tables');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error adding notification system:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    // Remove columns from customers table
    await queryInterface.removeColumn('customers', 'notification_sms', { transaction });
    await queryInterface.removeColumn('customers', 'notification_email', { transaction });
    await queryInterface.removeColumn('customers', 'notification_whatsapp', { transaction });

    // Drop tables
    await queryInterface.dropTable('renewal_notification_settings', { transaction });
    await queryInterface.dropTable('notification_templates', { transaction });

    await transaction.commit();
    console.log('✅ Removed notification system tables and columns');

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error removing notification system:', error);
    throw error;
  }
};
