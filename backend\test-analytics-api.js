/**
 * Test Analytics API Endpoints
 * This script tests the new analytics API endpoints
 */

import fetch from 'node-fetch';
import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

const API_BASE = 'http://localhost:3001/api/v1';

async function testAnalyticsAPI() {
  try {
    console.log('🧪 Testing Analytics API Endpoints...\n');

    // First, we need to create test data and get an auth token
    console.log('📋 Setting up test data...');
    
    // Find or create test tenant
    let testTenant = await models.Tenant.findOne({
      where: { name: 'Test Tenant' }
    });

    if (!testTenant) {
      testTenant = await models.Tenant.create({
        name: 'Test Tenant',
        slug: 'test-tenant',
        subdomain: 'test-tenant',
        status: 'active'
      });
    }

    // Find or create test user
    let testUser = await models.User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await models.User.create({
        tenant_id: testTenant.id,
        first_name: 'Test',
        last_name: 'User',
        email: '<EMAIL>',
        password: 'password123',
        is_active: true
      });
    }

    // Create some test customers
    const testCustomers = [];
    for (let i = 1; i <= 5; i++) {
      const customer = await models.Customer.findOrCreate({
        where: { customer_code: `TEST${i.toString().padStart(3, '0')}` },
        defaults: {
          tenant_id: testTenant.id,
          customer_code: `TEST${i.toString().padStart(3, '0')}`,
          company_name: `TEST COMPANY ${i}`,
          customer_type: i <= 3 ? 'customer' : 'inactive',
          is_active: i <= 3,
          email: `test${i}@example.com`,
          phone: `*********${i}`,
          tally_serial_number: `TSN${i.toString().padStart(3, '0')}`,
          created_at: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)) // Spread over last 5 days
        }
      });
      testCustomers.push(customer[0]);
    }

    console.log('⚠️  Skipping service call creation due to complex dependencies');

    console.log('✅ Test data created');

    // For testing purposes, we'll simulate authentication
    // In a real scenario, you'd need to authenticate and get a token
    console.log('⚠️  Note: This test requires the server to be running and proper authentication');
    console.log('🔗 Analytics API endpoints available at:');
    console.log(`   GET ${API_BASE}/analytics - Comprehensive analytics`);
    console.log(`   GET ${API_BASE}/analytics/customers - Customer analytics`);
    console.log(`   GET ${API_BASE}/analytics/services - Service analytics`);
    console.log(`   GET ${API_BASE}/analytics/financial - Financial analytics`);
    console.log(`   GET ${API_BASE}/analytics/executives - Executive analytics`);
    
    console.log('\n📊 Sample API calls:');
    console.log(`curl -H "Authorization: Bearer YOUR_TOKEN" "${API_BASE}/analytics?period=30d"`);
    console.log(`curl -H "Authorization: Bearer YOUR_TOKEN" "${API_BASE}/analytics/customers?period=7d"`);
    console.log(`curl -H "Authorization: Bearer YOUR_TOKEN" "${API_BASE}/analytics/services?period=90d"`);

    // Test the analytics functions directly (without HTTP)
    console.log('\n🧪 Testing analytics functions directly...');
    
    const { 
      getCustomerAnalytics,
      getServiceAnalytics,
      getFinancialAnalytics,
      getExecutiveAnalytics
    } = await import('./src/controllers/tenantAnalyticsController.js');

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    // Test customer analytics
    console.log('\n📊 Customer Analytics:');
    const customerAnalytics = await getCustomerAnalytics(testTenant.id, startDate, endDate);
    console.log('- Total customers:', customerAnalytics.totalCustomers);
    console.log('- New customers:', customerAnalytics.newCustomers);
    console.log('- Customer types:', customerAnalytics.customerTypeDistribution.length);
    console.log('- Acquisition trend points:', customerAnalytics.acquisitionTrend.length);

    // Test service analytics
    console.log('\n🔧 Service Analytics:');
    const serviceAnalytics = await getServiceAnalytics(testTenant.id, startDate, endDate);
    console.log('- Total service calls:', serviceAnalytics.totalServiceCalls);
    console.log('- Service calls in period:', serviceAnalytics.serviceCallsInPeriod);
    console.log('- Status distribution:', serviceAnalytics.serviceCallsByStatus.length);
    console.log('- Priority distribution:', serviceAnalytics.serviceCallsByPriority.length);

    // Test financial analytics
    console.log('\n💰 Financial Analytics:');
    const financialAnalytics = await getFinancialAnalytics(testTenant.id, startDate, endDate);
    console.log('- Total revenue:', financialAnalytics.totalRevenue);
    console.log('- Revenue in period:', financialAnalytics.revenueInPeriod);
    console.log('- Revenue trend points:', financialAnalytics.revenueTrend.length);

    // Test executive analytics
    console.log('\n👥 Executive Analytics:');
    const executiveAnalytics = await getExecutiveAnalytics(testTenant.id, startDate, endDate);
    console.log('- Service calls by executive:', executiveAnalytics.serviceCallsByExecutive.length);
    console.log('- Executive performance metrics:', executiveAnalytics.executivePerformance.length);

    console.log('\n✅ Analytics API functions tested successfully!');
    console.log('\n🚀 To test the HTTP endpoints:');
    console.log('1. Start the backend server: npm run dev');
    console.log('2. Authenticate and get a token');
    console.log('3. Make requests to the analytics endpoints');

  } catch (error) {
    console.error('❌ Test failed:', error);
    logger.error('Analytics API test error:', error);
  } finally {
    // Close database connection
    await models.sequelize.close();
  }
}

// Run the test
testAnalyticsAPI();
