import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  console.log('🔄 Updating service_calls to use products_issues instead of type_of_calls...');

  try {
    // Step 1: Remove the existing foreign key constraint
    console.log('1. Removing existing foreign key constraint...');
    try {
      await queryInterface.removeConstraint('service_calls', 'service_calls_type_of_call_id_fkey');
    } catch (error) {
      console.log('   Foreign key constraint not found or already removed');
    }

    // Step 2: Add new column for products_issues reference
    console.log('2. Adding products_issues_id column...');
    try {
      await queryInterface.addColumn('service_calls', 'products_issues_id', {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'products_issues',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'Reference to ProductsIssues for specific issue types',
      });
    } catch (error) {
      if (error.original?.code !== '42701') { // Column already exists
        throw error;
      }
      console.log('   Column already exists');
    }

    // Step 3: Add index for the new column
    console.log('3. Adding index for products_issues_id...');
    try {
      await queryInterface.addIndex('service_calls', ['products_issues_id'], {
        name: 'service_calls_products_issues_id_idx',
      });
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
      console.log('   Index already exists');
    }

    // Step 4: Migrate existing data (optional - set to null for now)
    console.log('4. Migrating existing data...');
    await queryInterface.sequelize.query(`
      UPDATE service_calls 
      SET products_issues_id = NULL 
      WHERE type_of_call_id IS NOT NULL;
    `);

    // Step 5: Keep type_of_call_id for backward compatibility but make it optional
    console.log('5. Making type_of_call_id optional...');
    // The column is already nullable, so no change needed

    console.log('✅ Migration completed successfully');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  console.log('🔄 Reverting service_calls products_issues changes...');

  try {
    // Remove index
    try {
      await queryInterface.removeIndex('service_calls', 'service_calls_products_issues_id_idx');
    } catch (error) {
      console.log('   Index not found or already removed');
    }

    // Remove column
    try {
      await queryInterface.removeColumn('service_calls', 'products_issues_id');
    } catch (error) {
      console.log('   Column not found or already removed');
    }

    // Restore foreign key constraint for type_of_call_id
    try {
      await queryInterface.addConstraint('service_calls', {
        fields: ['type_of_call_id'],
        type: 'foreign key',
        name: 'service_calls_type_of_call_id_fkey',
        references: {
          table: 'type_of_calls',
          field: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      });
    } catch (error) {
      console.log('   Foreign key constraint already exists');
    }

    console.log('✅ Rollback completed successfully');

  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
};
