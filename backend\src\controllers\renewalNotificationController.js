import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { validationResult } from 'express-validator';
import RenewalNotificationService from '../services/RenewalNotificationService.js';
import scheduledJobService from '../services/ScheduledJobService.js';

/**
 * Get all renewal notification settings for the current tenant
 */
export const getRenewalSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const settings = await models.RenewalNotificationSettings.findAll({
      where: { tenant_id: tenantId },
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
      order: [['field_name', 'ASC'], ['created_at', 'DESC']],
    });

    // If no settings exist, create default ones
    if (settings.length === 0) {
      const defaultSettings = models.RenewalNotificationSettings.getDefaultSettings();
      const createdSettings = [];

      for (const defaultSetting of defaultSettings) {
        const created = await models.RenewalNotificationSettings.create({
          ...defaultSetting,
          tenant_id: tenantId,
          created_by: req.user.id,
        });
        createdSettings.push(created);
      }

      return res.json({
        success: true,
        data: createdSettings,
        message: 'Default renewal settings created',
      });
    }

    res.json({
      success: true,
      data: settings,
    });
  } catch (error) {
    logger.error('Error fetching renewal settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch renewal settings',
      error: error.message,
    });
  }
};

/**
 * Create a new renewal notification setting
 */
export const createRenewalSetting = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const tenantId = req.user.tenant.id;
    const { field_name, reminder_days, notification_channels, is_active } = req.body;

    // Check if setting already exists for this field
    const existingSetting = await models.RenewalNotificationSettings.findOne({
      where: {
        tenant_id: tenantId,
        field_name: field_name,
      },
    });

    if (existingSetting) {
      return res.status(400).json({
        success: false,
        message: `Renewal setting for ${field_name} already exists`,
      });
    }

    const setting = await models.RenewalNotificationSettings.create({
      tenant_id: tenantId,
      field_name,
      reminder_days,
      notification_channels: notification_channels || ['email'],
      is_active: is_active !== undefined ? is_active : true,
      created_by: req.user.id,
    });

    // Trigger schedule regeneration for this tenant
    try {
      const renewalService = new RenewalNotificationService();
      await renewalService.scheduleRenewalNotificationsForTenant(tenantId);
      logger.info(`Renewal schedules regenerated for tenant: ${tenantId}`);
    } catch (scheduleError) {
      logger.error('Error regenerating schedules:', scheduleError);
    }

    logger.info(`Renewal setting created: ${field_name} for tenant: ${tenantId}`);

    res.status(201).json({
      success: true,
      data: setting,
      message: 'Renewal setting created successfully',
    });
  } catch (error) {
    logger.error('Error creating renewal setting:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create renewal setting',
      error: error.message,
    });
  }
};

/**
 * Update a renewal notification setting
 */
export const updateRenewalSetting = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const tenantId = req.user.tenant.id;
    const { id } = req.params;
    const { reminder_days, notification_channels, is_active } = req.body;

    const setting = await models.RenewalNotificationSettings.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
      },
    });

    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Renewal setting not found',
      });
    }

    await setting.update({
      reminder_days,
      notification_channels: notification_channels || setting.notification_channels,
      is_active: is_active !== undefined ? is_active : setting.is_active,
    });

    // Trigger schedule regeneration for this tenant
    try {
      const renewalService = new RenewalNotificationService();
      await renewalService.scheduleRenewalNotificationsForTenant(tenantId);
      logger.info(`Renewal schedules regenerated for tenant: ${tenantId}`);
    } catch (scheduleError) {
      logger.error('Error regenerating schedules:', scheduleError);
    }

    logger.info(`Renewal setting updated: ${setting.field_name} for tenant: ${tenantId}`);

    res.json({
      success: true,
      data: setting,
      message: 'Renewal setting updated successfully',
    });
  } catch (error) {
    logger.error('Error updating renewal setting:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update renewal setting',
      error: error.message,
    });
  }
};

/**
 * Delete a renewal notification setting
 */
export const deleteRenewalSetting = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;

    const setting = await models.RenewalNotificationSettings.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
      },
    });

    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Renewal setting not found',
      });
    }

    // Cancel any scheduled notifications for this setting
    await models.NotificationSchedule.update(
      { status: 'cancelled' },
      {
        where: {
          tenant_id: tenantId,
          renewal_type: setting.field_name.includes('amc') ? 'amc' : 'tss',
          status: 'scheduled',
        },
      }
    );

    await setting.destroy();

    logger.info(`Renewal setting deleted: ${setting.field_name} for tenant: ${tenantId}`);

    res.json({
      success: true,
      message: 'Renewal setting deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting renewal setting:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete renewal setting',
      error: error.message,
    });
  }
};

/**
 * Get renewal notification statistics
 */
export const getRenewalStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await models.NotificationSchedule.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    const pendingNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenantId,
        status: 'scheduled',
        notify_at: {
          [models.sequelize.Op.lte]: new Date(),
        },
      },
    });

    const upcomingRenewals = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenantId,
        status: 'scheduled',
        days_before_expiry: {
          [models.sequelize.Op.lte]: 7,
        },
      },
    });

    res.json({
      success: true,
      data: {
        notificationStats: stats,
        pendingNotifications,
        upcomingRenewals,
      },
    });
  } catch (error) {
    logger.error('Error fetching renewal stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch renewal statistics',
      error: error.message,
    });
  }
};

/**
 * Manually trigger renewal notification processing
 */
export const triggerNotifications = async (req, res) => {
  try {
    const { date } = req.body;
    
    const result = await scheduledJobService.triggerRenewalNotifications(date);

    res.json({
      success: true,
      data: result,
      message: 'Renewal notifications triggered successfully',
    });
  } catch (error) {
    logger.error('Error triggering notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger notifications',
      error: error.message,
    });
  }
};

/**
 * Manually trigger schedule generation
 */
export const triggerScheduleGeneration = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    
    const scheduled = await scheduledJobService.triggerScheduleGeneration(tenantId);

    res.json({
      success: true,
      data: { scheduled },
      message: `Schedule generation completed: ${scheduled} notifications scheduled`,
    });
  } catch (error) {
    logger.error('Error triggering schedule generation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger schedule generation',
      error: error.message,
    });
  }
};

/**
 * Get scheduled job status
 */
export const getJobStatus = async (req, res) => {
  try {
    const status = scheduledJobService.getJobStatus();

    res.json({
      success: true,
      data: status,
    });
  } catch (error) {
    logger.error('Error fetching job status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch job status',
      error: error.message,
    });
  }
};

/**
 * Get notification history
 */
export const getNotificationHistory = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { page = 1, limit = 20, status, renewal_type, customer_id } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { tenant_id: tenantId };

    if (status) whereClause.status = status;
    if (renewal_type) whereClause.renewal_type = renewal_type;
    if (customer_id) whereClause.customer_id = customer_id;

    const { count, rows: notifications } = await models.NotificationSchedule.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'email', 'phone'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    res.json({
      success: true,
      data: {
        notifications,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching notification history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification history',
      error: error.message,
    });
  }
};

/**
 * Get notification details by ID
 */
export const getNotificationDetails = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;

    const notification = await models.NotificationSchedule.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'email', 'phone'],
        },
      ],
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found',
      });
    }

    res.json({
      success: true,
      data: notification,
    });
  } catch (error) {
    logger.error('Error fetching notification details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification details',
      error: error.message,
    });
  }
};
