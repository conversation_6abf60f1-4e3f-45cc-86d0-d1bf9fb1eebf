// Test script to check the service analytics API
async function testServiceAnalyticsAPI() {
    console.log('🧪 Testing Service Analytics API...');

    try {
        // Test the service analytics API endpoint (updated to use port 8082)
        const response = await fetch('http://localhost:8082/api/v1/reports/service-analytics?dateRange=30&groupBy=day', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add any required auth headers here if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            }
        });

        if (!response.ok) {
            console.error(`❌ Service Analytics API failed with status: ${response.status}`);
            const errorText = await response.text();
            console.error('Error response:', errorText);

            // Try legacy stats API as fallback
            console.log('🔄 Trying legacy stats API...');
            const legacyResponse = await fetch('http://localhost:8082/api/v1/service-calls/stats', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!legacyResponse.ok) {
                console.error(`❌ Legacy Stats API also failed with status: ${legacyResponse.status}`);
                return;
            }

            const legacyData = await legacyResponse.json();
            console.log('✅ Legacy Stats API Response received');
            console.log('📊 Legacy Response structure:', {
                success: legacyData.success,
                hasData: !!legacyData.data,
                hasCallsByType: !!legacyData.data?.callsByType,
                callsByType: legacyData.data?.callsByType
            });
            return;
        }

        const data = await response.json();
        console.log('✅ Service Analytics API Response received');
        console.log('📊 Response structure:', {
            success: data.success,
            hasData: !!data.data,
            hasSummary: !!data.data?.summary,
            summaryKeys: data.data?.summary ? Object.keys(data.data.summary) : []
        });

        if (data.data?.summary) {
            console.log('📈 Summary data:', {
                totalCalls: data.data.summary.totalCalls,
                freeCalls: data.data.summary.freeCalls,
                amcCalls: data.data.summary.amcCalls,
                paidCalls: data.data.summary.paidCalls,
                completedCalls: data.data.summary.completedCalls,
                openCalls: data.data.summary.openCalls,
                inProgressCalls: data.data.summary.inProgressCalls
            });
        }

    } catch (error) {
        console.error('❌ Error testing API:', error.message);
    }
}

// Run the test
testServiceAnalyticsAPI();
