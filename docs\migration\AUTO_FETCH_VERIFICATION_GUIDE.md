# Auto-Fetch Functionality Verification Guide

## Overview

The TallyCRM service form includes comprehensive auto-fetch functionality for Tally Version and TSS Status when a customer is selected. This guide explains how the functionality works and how to verify it's working correctly.

## Auto-Fetch Features

### 1. Tally Version Auto-Fetch

**Data Sources (Priority Order):**
1. `customer.tally_version` (Direct field)
2. `customer.tssDetails[0].version` (TSS table)
3. `customer.custom_fields.tally_version` (Custom fields)
4. `customer.custom_fields.product_version` (Custom fields)
5. `customer.product_info.version` (Product info)
6. `customer.products[].version` (Products array)

**Visual Indicators:**
- ✅ Green checkmark: Tally version successfully auto-fetched
- ⚠️ Yellow warning: No Tally version found in customer data
- Field shows "Auto-fetched from customer data" placeholder

### 2. TSS Status Auto-Fetch

**Data Sources (Priority Order):**
1. `customer.custom_fields.tss_status` (Primary source)
2. `customer.tssDetails[0].status` (CustomerTSS table)

**Status Logic:**
- **Active**: TSS status is "YES"/"yes"/"active"/"Active"/true/1 AND not expired
- **Inactive**: TSS status is "NO"/"no"/"inactive" OR expired OR no TSS data

**Visual Indicators:**
- ✅ Green checkmark: TSS is active
- ❌ Red X: TSS is inactive
- Shows expiry date when TSS is active

### 3. Additional Auto-Fetched Data

**Customer Information:**
- Company name
- Contact number
- Email address
- Tally serial number
- Designation

**TSS Information:**
- TSS status (active/inactive)
- TSS expiry date (when available)

**Features (from custom_fields):**
- Auto Backup Module
- WhatsApp Group
- Tally Cloud
- Tally on Mobile
- Tally on WhatsApp
- Cloud Service Status

## How to Test Auto-Fetch Functionality

### Step 1: Access Service Form
1. Navigate to Services → Create New Service
2. Use the Enhanced Service Form

### Step 2: Test Customer Selection
1. Select a customer from the dropdown
2. Watch for loading toast: "Fetching customer details..."
3. Verify success toast: "Customer details auto-filled successfully"

### Step 3: Verify Auto-Populated Data
1. Check the "Customer Information (Auto-populated)" section
2. Look for status indicators in the header:
   - ✅ Tally Version (if found)
   - ✅ TSS Status (if found)
   - ⚠️ Partial Data (if some data missing)

### Step 4: Test Tally Serial Number Auto-Fetch
1. Clear customer selection
2. Enter a Tally serial number (minimum 3 characters)
3. System should auto-search and select matching customer
4. All customer data should auto-populate

### Step 5: Test Manual Refresh
1. Click the "🔄 Refresh" button in the auto-populated section
2. System should re-fetch customer data
3. Verify updated information

## Console Debugging

The system provides extensive console logging for debugging:

### TSS Status Debug Logs
```javascript
🔍 TSS Data Sources Check: {
  customerName: "Company Name",
  customerId: "uuid",
  customerTSSTable: "Found/Not Found",
  customFieldsTssStatus: "YES/NO/active/inactive",
  customFieldsTssExpiry: "date or null"
}
```

### Tally Version Debug Logs
```javascript
🔍 Tally Version Auto-fetch Debug: {
  customerId: "uuid",
  customerName: "Company Name",
  directTallyVersion: "version or null",
  tssDetailsVersion: "version or null",
  customFieldsTallyVersion: "version or null",
  finalTallyVersion: "final selected version",
  autoFetchStatus: "SUCCESS/NO_DATA_FOUND"
}
```

## Troubleshooting

### Issue: No Tally Version Auto-Fetched
**Possible Causes:**
1. Customer has no `tally_version` field data
2. No TSS records with version info
3. No version data in custom_fields
4. No product information linked

**Solution:**
1. Check customer data in database
2. Add Tally version to customer record
3. Create TSS record with version info
4. Update custom_fields with version data

### Issue: TSS Status Shows Inactive
**Possible Causes:**
1. `custom_fields.tss_status` is "NO" or "inactive"
2. TSS expiry date has passed
3. No TSS data in either source

**Solution:**
1. Update `custom_fields.tss_status` to "YES"
2. Update TSS expiry date to future date
3. Create CustomerTSS record with active status

### Issue: Auto-Fetch Not Triggering
**Possible Causes:**
1. API endpoint not responding
2. Customer ID not valid
3. Network connectivity issues

**Solution:**
1. Check browser console for errors
2. Verify API endpoint `/customers/:id` is working
3. Check network tab for failed requests

## API Endpoint Details

### Customer Data Fetch
```
GET /api/v1/customers/:id?includeRelations=true
```

**Includes:**
- Industry information
- Area information
- Assigned executive
- Customer contacts
- TSS details with license edition
- AMC contracts

## Database Schema Requirements

### Customer Table
- `tally_version` (STRING): Direct Tally version field
- `custom_fields` (JSONB): Contains TSS status and other data

### CustomerTSS Table
- `customer_id` (UUID): Links to customer
- `version` (STRING): Tally software version
- `status` (ENUM): active/expired/suspended/cancelled
- `expiry_date` (DATE): TSS expiry date

## Best Practices

1. **Always test with real customer data** that has various combinations of data sources
2. **Check console logs** for detailed debugging information
3. **Verify visual indicators** are showing correct status
4. **Test edge cases** like expired TSS, missing data, etc.
5. **Use the refresh button** to re-fetch data after making changes

## Expected Behavior

✅ **Working Correctly:**
- Customer selection triggers auto-fetch
- Loading and success toasts appear
- Data populates in form fields
- Visual indicators show correct status
- Console logs show detailed debug info

❌ **Not Working:**
- No loading toast on customer selection
- Fields remain empty after selection
- Error toasts appear
- Console shows API errors
- Visual indicators missing or incorrect
