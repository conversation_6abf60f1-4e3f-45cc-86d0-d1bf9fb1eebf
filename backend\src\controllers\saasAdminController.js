import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import UsageTrackingService from '../services/usageTrackingService.js';

/**
 * Get all tenants (SaaS admin only)
 */
export const getAllTenants = async (req, res) => {
  try {
    const { page = 1, limit = 20, status, plan } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};
    if (status) {
      whereClause.subscription_status = status;
    }
    if (plan) {
      whereClause.subscription_plan = plan;
    }

    const { count, rows: tenants } = await models.Tenant.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.Subscription,
          as: 'subscription',
          include: [
            {
              model: models.SubscriptionPlan,
              as: 'plan',
            },
          ],
        },
        {
          model: models.User,
          as: 'users',
          attributes: ['id', 'email', 'first_name', 'last_name', 'is_active'],
          limit: 5,
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    // Get usage summary for each tenant
    const tenantsWithUsage = await Promise.all(
      tenants.map(async (tenant) => {
        const usageSummary = await UsageTrackingService.getUsageSummary(tenant.id);
        return {
          ...tenant.toJSON(),
          usage: usageSummary?.currentUsage || {},
        };
      })
    );

    res.json({
      success: true,
      data: {
        tenants: tenantsWithUsage,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Get all tenants error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve tenants',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get tenant details (SaaS admin only)
 */
export const getTenantDetails = async (req, res) => {
  try {
    const { tenantId } = req.params;

    const tenant = await models.Tenant.findByPk(tenantId, {
      include: [
        {
          model: models.Subscription,
          as: 'subscription',
          include: [
            {
              model: models.SubscriptionPlan,
              as: 'plan',
            },
            {
              model: models.Invoice,
              as: 'invoices',
              limit: 10,
              order: [['created_at', 'DESC']],
            },
          ],
        },
        {
          model: models.User,
          as: 'users',
          attributes: ['id', 'email', 'first_name', 'last_name', 'is_active', 'created_at'],
        },
        {
          model: models.Customer,
          as: 'customers',
          attributes: ['id', 'customer_name', 'created_at'],
          limit: 10,
          order: [['created_at', 'DESC']],
        },
      ],
    });

    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found',
      });
    }

    // Get detailed usage summary
    const usageSummary = await UsageTrackingService.getUsageSummary(tenantId);

    res.json({
      success: true,
      data: {
        tenant,
        usage: usageSummary,
      },
    });
  } catch (error) {
    logger.error('Get tenant details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve tenant details',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update tenant status (SaaS admin only)
 */
export const updateTenantStatus = async (req, res) => {
  try {
    const { tenantId } = req.params;
    const { status, reason } = req.body;

    const tenant = await models.Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found',
      });
    }

    const oldStatus = tenant.subscription_status;
    await tenant.update({
      subscription_status: status,
      updated_at: new Date(),
    });

    // Log the status change
    logger.info('Tenant status updated by admin:', {
      tenantId,
      oldStatus,
      newStatus: status,
      reason,
      adminUserId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Tenant status updated successfully',
      data: { tenant },
    });
  } catch (error) {
    logger.error('Update tenant status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update tenant status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get SaaS metrics overview
 */
export const getSaasMetrics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Get tenant counts by status
    const tenantCounts = await models.Tenant.findAll({
      attributes: [
        'subscription_status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['subscription_status'],
    });

    // Get subscription plan distribution
    const planDistribution = await models.Tenant.findAll({
      attributes: [
        'subscription_plan',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['subscription_plan'],
    });

    // Get revenue metrics
    const revenueMetrics = await models.Payment.findOne({
      where: {
        status: 'succeeded',
        processed_at: {
          [models.Sequelize.Op.between]: [startDate, endDate],
        },
      },
      attributes: [
        [models.sequelize.fn('SUM', models.sequelize.col('amount')), 'total_revenue'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'total_payments'],
        [models.sequelize.fn('AVG', models.sequelize.col('amount')), 'avg_payment'],
      ],
    });

    // Get new signups
    const newSignups = await models.Tenant.count({
      where: {
        created_at: {
          [models.Sequelize.Op.between]: [startDate, endDate],
        },
      },
    });

    // Get churn rate (canceled subscriptions)
    const churnedTenants = await models.Tenant.count({
      where: {
        subscription_status: 'canceled',
        updated_at: {
          [models.Sequelize.Op.between]: [startDate, endDate],
        },
      },
    });

    const totalActiveTenants = await models.Tenant.count({
      where: {
        subscription_status: ['active', 'trial'],
      },
    });

    const churnRate = totalActiveTenants > 0 ? (churnedTenants / totalActiveTenants) * 100 : 0;

    res.json({
      success: true,
      data: {
        period,
        tenantCounts: tenantCounts.reduce((acc, item) => {
          acc[item.subscription_status] = parseInt(item.getDataValue('count'));
          return acc;
        }, {}),
        planDistribution: planDistribution.reduce((acc, item) => {
          acc[item.subscription_plan] = parseInt(item.getDataValue('count'));
          return acc;
        }, {}),
        revenue: {
          total: parseFloat(revenueMetrics?.getDataValue('total_revenue')) || 0,
          totalPayments: parseInt(revenueMetrics?.getDataValue('total_payments')) || 0,
          averagePayment: parseFloat(revenueMetrics?.getDataValue('avg_payment')) || 0,
        },
        growth: {
          newSignups,
          churnedTenants,
          churnRate: Math.round(churnRate * 100) / 100,
        },
        totalActiveTenants,
      },
    });
  } catch (error) {
    logger.error('Get SaaS metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve SaaS metrics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Force update usage for all tenants (SaaS admin only)
 */
export const updateAllUsage = async (req, res) => {
  try {
    const tenants = await models.Tenant.findAll({
      where: { is_active: true },
      attributes: ['id'],
    });

    const updatePromises = tenants.map(tenant => 
      UsageTrackingService.updateAllUsage(tenant.id)
    );

    await Promise.all(updatePromises);

    logger.info('Usage updated for all tenants by admin:', {
      tenantCount: tenants.length,
      adminUserId: req.user.id,
    });

    res.json({
      success: true,
      message: `Usage updated for ${tenants.length} tenants`,
    });
  } catch (error) {
    logger.error('Update all usage error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update usage for all tenants',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
