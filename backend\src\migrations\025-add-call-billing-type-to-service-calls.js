export const up = async (queryInterface, Sequelize) => {
  try {
    // Check if column already exists
    const tableDescription = await queryInterface.describeTable('service_calls');

    if (tableDescription.call_billing_type) {
      console.log('✅ call_billing_type column already exists, skipping...');
      return;
    }

    // Create the enum type with proper escaping - use CREATE TYPE IF NOT EXISTS for safety
    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_service_calls_call_billing_type') THEN
          CREATE TYPE "enum_service_calls_call_billing_type" AS ENUM (
            'free_call',
            'amc_call',
            'per_call'
          );
        END IF;
      END $$;
    `);

    // Add the column with the new enum type
    await queryInterface.addColumn('service_calls', 'call_billing_type', {
      type: 'enum_service_calls_call_billing_type',
      allowNull: true,
      comment: 'Call billing type for the service call (free_call, amc_call, per_call)',
    });

    console.log('✅ Added call_billing_type column to service_calls table');
  } catch (error) {
    console.error('❌ Error adding call_billing_type column:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  try {
    // Remove call_billing_type field from service_calls table
    await queryInterface.removeColumn('service_calls', 'call_billing_type');

    // Drop the enum type
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_service_calls_call_billing_type" CASCADE;'
    );

    console.log('✅ Removed call_billing_type column and enum type from service_calls table');
  } catch (error) {
    console.error('❌ Error removing call_billing_type column:', error);
    throw error;
  }
};
