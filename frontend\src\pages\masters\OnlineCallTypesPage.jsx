import React from 'react';
import { FaArrowLeft } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import OnlineCallTypesManagement from '../../components/masters/OnlineCallTypesManagement';

const OnlineCallTypesPage = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/masters?tab=call-statuses');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft className="mr-2" />
                <span>Back to Masters</span>
              </button>
              <div className="h-6 border-l border-gray-300"></div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Online Call Types Management
                </h1>
                <p className="text-sm text-gray-500">
                  Manage online call types and issues for service calls
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <OnlineCallTypesManagement />
      </div>
    </div>
  );
};

export default OnlineCallTypesPage;
