import React, { useState, useEffect } from 'react';
import {
  FaTimes as X,
  <PERSON>a<PERSON><PERSON> as User,
  FaBuilding as Building,
  FaPhone as Phone,
  FaEnvelope as Mail,
  FaMapMarkerAlt as MapPin,
  FaSave as Save,
  <PERSON>a<PERSON>pinner as Loader
} from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import { useExecutiveSearch } from '../../hooks/useExecutiveSearch';
import SearchableSelect from '../ui/SearchableSelect';
import MobileInput from '../ui/MobileInput';
import { hasPhoneDigits, validationRules } from '../../utils/validation';

const NewCustomerModal = ({ isOpen, onClose, onCustomerCreated }) => {
  const [loading, setLoading] = useState(false);
  const [areas, setAreas] = useState([]);
  const [industries, setIndustries] = useState([]);
  const [executives, setExecutives] = useState([]);
  const [products, setProducts] = useState([]);
  const [licenseEditions, setLicenseEditions] = useState([]);
  const [designations, setDesignations] = useState([]);

  // Loading states for each master data
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [loadingIndustries, setLoadingIndustries] = useState(false);
  const [loadingExecutives, setLoadingExecutives] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [loadingLicenseEditions, setLoadingLicenseEditions] = useState(false);
  const [loadingDesignations, setLoadingDesignations] = useState(false);

  // Server-side search hook for executives
  const { searchResults: executiveSearchResults, isSearching: isSearchingExecutives, searchExecutives } = useExecutiveSearch();

  const [formData, setFormData] = useState({
    // Basic Info (mandatory fields)
    customerName: '',
    tallySerialNo: '',
    adminEmail: '',
    mdContactPerson: '',
    mdPhoneNo: '',
    mdEmail: '',
    officeContactPerson: '',
    officeMobileNo: '',
    officeEmail: '',
    auditorName: '',
    auditorNo: '',
    auditorEmail: '',
    taxConsultantName: '',
    taxConsultantNo: '',
    taxConsultantEmail: '',
    area: '',
    noOfTallyUsers: '',
    executiveName: '',
    status: 'ACTIVE',

    // Task 4: Default call type
    defaultCallType: 'free_call',

    // Optional fields
    product: null,
    licenceEdition: null,
    location: null,
    industry: null,
    profileStatus: 'FOLLOW UP',
    followUpExecutive: null,
    mapLocation: '',
    latitude: '',
    longitude: '',
    gstNo: '',
    remarks: '',
    pinCode: '',
    stateCountry: '',
    itName: '',
    itNo: '',
    itEmail: '',
  });

  const [errors, setErrors] = useState({});

  // Fetch master data
  useEffect(() => {
    if (isOpen) {
      fetchMasterData();
    }
  }, [isOpen]);

  // Enhanced master data loading with individual loading states
  const loadAreas = async () => {
    try {
      setLoadingAreas(true);
      const response = await apiService.get('/master-data/areas');
      if (response.data?.success) {
        setAreas(response.data.data?.area || []);
      }
    } catch (error) {
      console.error('Error loading areas:', error);
      toast.error('Failed to load areas');
    } finally {
      setLoadingAreas(false);
    }
  };

  const loadIndustries = async () => {
    try {
      setLoadingIndustries(true);
      const response = await apiService.get('/master-data/industries');
      if (response.data?.success) {
        setIndustries(response.data.data?.industry || []);
      }
    } catch (error) {
      console.error('Error loading industries:', error);
      toast.error('Failed to load industries');
    } finally {
      setLoadingIndustries(false);
    }
  };

  const loadExecutives = async () => {
    try {
      setLoadingExecutives(true);
      const response = await apiService.get('/executives');
      if (response.data?.success) {
        setExecutives(response.data.data?.executives || []);
      }
    } catch (error) {
      console.error('Error loading executives:', error);
      toast.error('Failed to load executives');
    } finally {
      setLoadingExecutives(false);
    }
  };

  const loadProducts = async () => {
    try {
      setLoadingProducts(true);
      const response = await apiService.get('/master-data/tally-products');
      if (response.data?.success) {
        setProducts(response.data.data?.tallyproduct || []);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoadingProducts(false);
    }
  };

  const loadLicenseEditions = async () => {
    try {
      setLoadingLicenseEditions(true);
      const response = await apiService.get('/master-data/license-editions');
      if (response.data?.success) {
        setLicenseEditions(response.data.data?.licenseedition || []);
      }
    } catch (error) {
      console.error('Error loading license editions:', error);
      toast.error('Failed to load license editions');
    } finally {
      setLoadingLicenseEditions(false);
    }
  };

  const loadDesignations = async () => {
    try {
      setLoadingDesignations(true);
      const response = await apiService.get('/master-data/designations');
      if (response.data?.success) {
        setDesignations(response.data.data?.designation || []);
      }
    } catch (error) {
      console.error('Error loading designations:', error);
      toast.error('Failed to load designations');
    } finally {
      setLoadingDesignations(false);
    }
  };

  const fetchMasterData = async () => {
    await Promise.all([
      loadAreas(),
      loadIndustries(),
      loadExecutives(),
      loadProducts(),
      loadLicenseEditions(),
      loadDesignations()
    ]);
  };

  // Auto-fetch MD mobile number when MD contact person is entered
  const fetchMDMobileNumber = async (mdContactPersonName) => {
    if (!mdContactPersonName.trim()) return;

    try {
      const response = await apiService.get(`/customers/search-md-contact?name=${encodeURIComponent(mdContactPersonName)}`);

      if (response.data?.success && response.data?.data?.mdPhoneNo) {
        setFormData(prev => ({
          ...prev,
          mdPhoneNo: response.data.data.mdPhoneNo
        }));
        toast.success('MD Phone number auto-filled from existing records');
      }
    } catch (error) {
      // Silently fail - this is just a convenience feature
      console.log('Could not auto-fetch MD mobile number:', error.message);
    }
  };

  const handleInputChange = (field, value) => {
    let processedValue = value;

    // Convert Tally Serial No to uppercase and allow only alphanumeric characters
    if (field === 'tallySerialNo') {
      processedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    }

    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Auto-fetch MD mobile number when MD contact person name is entered
    if (field === 'mdContactPerson' && processedValue.trim().length > 2) {
      // Debounce the API call
      const timeoutId = setTimeout(() => {
        fetchMDMobileNumber(processedValue);
      }, 1000);

      // Store timeout ID to clear it if user continues typing
      if (window.mdContactPersonTimeout) {
        clearTimeout(window.mdContactPersonTimeout);
      }
      window.mdContactPersonTimeout = timeoutId;
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.tallySerialNo.trim()) {
      newErrors.tallySerialNo = 'Tally Serial Number is required';
    }

    // Email validations with required check
    if (!formData.adminEmail.trim()) {
      newErrors.adminEmail = 'Admin email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.adminEmail)) {
      newErrors.adminEmail = 'Please enter a valid admin email';
    }

    if (!formData.mdContactPerson.trim()) {
      newErrors.mdContactPerson = 'MD Contact Person is required';
    }

    if (!formData.mdPhoneNo.trim()) {
      newErrors.mdPhoneNo = 'MD Phone Number is required';
    } else {
      const phoneValidation = validationRules.phone(formData.mdPhoneNo, true);
      if (!phoneValidation.isValid) {
        newErrors.mdPhoneNo = phoneValidation.message;
      }
    }

    if (!formData.mdEmail.trim()) {
      newErrors.mdEmail = 'MD Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.mdEmail)) {
      newErrors.mdEmail = 'Please enter a valid MD email';
    }

    if (!formData.officeContactPerson.trim()) {
      newErrors.officeContactPerson = 'Office Contact Person is required';
    }

    if (!formData.officeMobileNo.trim()) {
      newErrors.officeMobileNo = 'Office Mobile Number is required';
    } else {
      const phoneValidation = validationRules.phone(formData.officeMobileNo, true);
      if (!phoneValidation.isValid) {
        newErrors.officeMobileNo = phoneValidation.message;
      }
    }

    // Professional Contacts section - now optional with conditional validation
    // Office Email - completely optional
    if (formData.officeEmail.trim() && !/\S+@\S+\.\S+/.test(formData.officeEmail)) {
      newErrors.officeEmail = 'Please enter a valid office email';
    }

    // Auditor fields - conditional validation (if any field is filled, all are required)
    const auditorFields = ['auditorName', 'auditorNo', 'auditorEmail'];
    const hasAnyAuditorField = auditorFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For auditor phone number, check actual phone digits
      if (field === 'auditorNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyAuditorField) {
      if (!formData.auditorName.trim()) {
        newErrors.auditorName = 'Auditor Name is required when any auditor field is filled';
      }
      if (!formData.auditorNo.trim()) {
        newErrors.auditorNo = 'Auditor Contact Number is required when any auditor field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.auditorNo, false);
        if (!phoneValidation.isValid) {
          newErrors.auditorNo = phoneValidation.message;
        }
      }
      if (!formData.auditorEmail.trim()) {
        newErrors.auditorEmail = 'Auditor Email is required when any auditor field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.auditorEmail)) {
        newErrors.auditorEmail = 'Please enter a valid auditor email';
      }
    } else if (formData.auditorEmail.trim() && !/\S+@\S+\.\S+/.test(formData.auditorEmail)) {
      newErrors.auditorEmail = 'Please enter a valid auditor email';
    } else if (formData.auditorNo.trim()) {
      const phoneValidation = validationRules.phone(formData.auditorNo, false);
      if (!phoneValidation.isValid) {
        newErrors.auditorNo = phoneValidation.message;
      }
    }

    // Tax Consultant fields - conditional validation (if any field is filled, all are required)
    const taxConsultantFields = ['taxConsultantName', 'taxConsultantNo', 'taxConsultantEmail'];
    const hasAnyTaxConsultantField = taxConsultantFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For tax consultant phone number, check actual phone digits
      if (field === 'taxConsultantNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyTaxConsultantField) {
      if (!formData.taxConsultantName.trim()) {
        newErrors.taxConsultantName = 'Tax Consultant Name is required when any tax consultant field is filled';
      }
      if (!formData.taxConsultantNo.trim()) {
        newErrors.taxConsultantNo = 'Tax Consultant Contact Number is required when any tax consultant field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.taxConsultantNo, false);
        if (!phoneValidation.isValid) {
          newErrors.taxConsultantNo = phoneValidation.message;
        }
      }
      if (!formData.taxConsultantEmail.trim()) {
        newErrors.taxConsultantEmail = 'Tax Consultant Email is required when any tax consultant field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.taxConsultantEmail)) {
        newErrors.taxConsultantEmail = 'Please enter a valid tax consultant email';
      }
    } else if (formData.taxConsultantEmail.trim() && !/\S+@\S+\.\S+/.test(formData.taxConsultantEmail)) {
      newErrors.taxConsultantEmail = 'Please enter a valid tax consultant email';
    } else if (formData.taxConsultantNo.trim()) {
      const phoneValidation = validationRules.phone(formData.taxConsultantNo, false);
      if (!phoneValidation.isValid) {
        newErrors.taxConsultantNo = phoneValidation.message;
      }
    }

    // Area, Executive Name, and No. of Tally Users are now optional - no validation required

    // Optional field validations
    if (formData.gstNo && formData.gstNo.length !== 15) {
      newErrors.gstNo = 'GST number must be exactly 15 characters';
    }

    if (formData.pinCode && !/^\d{6}$/.test(formData.pinCode)) {
      newErrors.pinCode = 'Pin code must be exactly 6 digits';
    }

    // IT fields - conditional validation (if any field is filled, all are required)
    const itFields = ['itName', 'itNo', 'itEmail'];
    const hasAnyItField = itFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For IT phone number, check actual phone digits
      if (field === 'itNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyItField) {
      if (!formData.itName.trim()) {
        newErrors.itName = 'IT Name is required when any IT field is filled';
      }
      if (!formData.itNo.trim()) {
        newErrors.itNo = 'IT Contact Number is required when any IT field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.itNo, false);
        if (!phoneValidation.isValid) {
          newErrors.itNo = phoneValidation.message;
        }
      }
      if (!formData.itEmail.trim()) {
        newErrors.itEmail = 'IT Email is required when any IT field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.itEmail)) {
        newErrors.itEmail = 'Please enter a valid IT email';
      }
    } else if (formData.itEmail.trim() && !/\S+@\S+\.\S+/.test(formData.itEmail)) {
      newErrors.itEmail = 'Please enter a valid IT email';
    } else if (formData.itNo.trim()) {
      const phoneValidation = validationRules.phone(formData.itNo, false);
      if (!phoneValidation.isValid) {
        newErrors.itNo = phoneValidation.message;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      setLoading(true);

      // Prepare data for API
      const customerData = {
        company_name: formData.customerName,
        customer_code: formData.tallySerialNo,
        display_name: formData.customerName,
        customer_type: 'customer',
        business_type: 'private_limited',
        email: formData.adminEmail,
        phone: formData.mdPhoneNo,
        address_line_1: formData.mapLocation || null,
        city: formData.area || null,
        state: formData.stateCountry || null,
        country: 'India',
        postal_code: formData.pinCode || null,
        gst_number: formData.gstNo || null,
        area_id: formData.location || null,
        industry_id: formData.industry || null,
        assigned_executive_id: formData.followUpExecutive || null,
        tally_serial_number: formData.tallySerialNo,
        latitude: formData.latitude ? parseFloat(formData.latitude) : null,
        longitude: formData.longitude ? parseFloat(formData.longitude) : null,
        notes: formData.remarks || null,
        custom_fields: {
          profile_status: formData.profileStatus,
          customer_status: formData.status,
          follow_up_executive_id: formData.followUpExecutive,
          product_id: formData.product,
          license_edition_id: formData.licenceEdition,
          admin_email: formData.adminEmail,
          md_contact_person: formData.mdContactPerson,
          md_phone_no: formData.mdPhoneNo,
          md_email: formData.mdEmail,
          office_contact_person: formData.officeContactPerson,
          office_mobile_no: formData.officeMobileNo,
          office_email: formData.officeEmail,
          auditor_name: formData.auditorName,
          auditor_no: formData.auditorNo,
          auditor_email: formData.auditorEmail,
          tax_consultant_name: formData.taxConsultantName,
          tax_consultant_no: formData.taxConsultantNo,
          tax_consultant_email: formData.taxConsultantEmail,
          it_name: formData.itName,
          it_no: formData.itNo,
          it_email: formData.itEmail,
          area: formData.area,
          pin_code: formData.pinCode,
          state_country: formData.stateCountry,
          no_of_tally_users: formData.noOfTallyUsers,
          executive_name: formData.executiveName,
          // Task 4: Default call type
          default_call_type: formData.defaultCallType,
          // Enhanced fields for better integration
          map_location: formData.mapLocation,
          latitude: formData.latitude,
          longitude: formData.longitude,
        }
      };

      const response = await apiService.post('/customers', customerData);

      if (response.data?.success) {
        toast.success('Customer created successfully');

        // Call the callback with the new customer data
        const newCustomer = response.data.data.customer;
        onCustomerCreated({
          id: newCustomer.id,
          company_name: newCustomer.company_name,
          name: newCustomer.display_name,
          phone: newCustomer.phone,
          customer_code: newCustomer.customer_code,
          tally_serial_number: newCustomer.tally_serial_number,
          email: newCustomer.email,
          // Additional fields for service form auto-population
          custom_fields: newCustomer.custom_fields || {},
        });

        // Reset form and close modal
        setFormData({
          customerName: '', tallySerialNo: '', adminEmail: '', mdContactPerson: '',
          mdPhoneNo: '', mdEmail: '', officeContactPerson: '', officeMobileNo: '',
          officeEmail: '', auditorName: '', auditorNo: '', auditorEmail: '',
          taxConsultantName: '', taxConsultantNo: '', taxConsultantEmail: '',
          area: '', noOfTallyUsers: '', executiveName: '', status: 'ACTIVE',
          defaultCallType: 'free_call', // Task 4: Reset to default
          product: null, licenceEdition: null, location: null, industry: null,
          profileStatus: 'FOLLOW UP', followUpExecutive: null, mapLocation: '',
          latitude: '', longitude: '', gstNo: '', remarks: '', pinCode: '',
          stateCountry: '', itName: '', itNo: '', itEmail: '',
        });
        setErrors({});
        onClose();
      } else {
        toast.error(response.data?.message || 'Failed to create customer');
      }
    } catch (error) {
      console.error('Error creating customer:', error);
      toast.error(error.response?.data?.message || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <User className="h-6 w-6 text-gray-900" />
            <h2 className="text-xl font-semibold text-gray-900">Add New Customer</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer Name *
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => handleInputChange('customerName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                placeholder="Enter customer name"
              />
              {errors.customerName && <p className="text-red-500 text-xs mt-1">{errors.customerName}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tally Serial Number *
              </label>
              <input
                type="text"
                value={formData.tallySerialNo}
                onChange={(e) => handleInputChange('tallySerialNo', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-all duration-200 ${
                  errors.tallySerialNo
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                    : 'border-gray-300 focus:ring-gray-500 focus:border-gray-500'
                }`}
                placeholder="Enter Tally serial number (e.g., GS001)"
                style={{ textTransform: 'uppercase' }}
              />
              {errors.tallySerialNo && <p className="text-red-500 text-xs mt-1">{errors.tallySerialNo}</p>}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4 bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-2 flex items-center">
              <Mail className="mr-2 h-5 w-5 text-gray-900" />
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Admin Email *
                </label>
                <input
                  type="email"
                  value={formData.adminEmail}
                  onChange={(e) => handleInputChange('adminEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.adminEmail && <p className="text-red-500 text-xs mt-1">{errors.adminEmail}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MD Contact Person *
                </label>
                <input
                  type="text"
                  value={formData.mdContactPerson}
                  onChange={(e) => handleInputChange('mdContactPerson', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="MD name"
                />
                {errors.mdContactPerson && <p className="text-red-500 text-xs mt-1">{errors.mdContactPerson}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MD Phone Number *
                </label>
                <MobileInput
                  value={formData.mdPhoneNo}
                  onChange={(e) => handleInputChange('mdPhoneNo', e.target.value)}
                  placeholder="Managing Director phone number"
                  error={!!errors.mdPhoneNo}
                  className="w-full"
                />
                {errors.mdPhoneNo && <p className="text-red-500 text-xs mt-1">{errors.mdPhoneNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MD Email *
                </label>
                <input
                  type="email"
                  value={formData.mdEmail}
                  onChange={(e) => handleInputChange('mdEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.mdEmail && <p className="text-red-500 text-xs mt-1">{errors.mdEmail}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Office Contact Person *
                </label>
                <input
                  type="text"
                  value={formData.officeContactPerson}
                  onChange={(e) => handleInputChange('officeContactPerson', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="Office contact name"
                />
                {errors.officeContactPerson && <p className="text-red-500 text-xs mt-1">{errors.officeContactPerson}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Office Mobile Number *
                </label>
                <MobileInput
                  value={formData.officeMobileNo}
                  onChange={(e) => handleInputChange('officeMobileNo', e.target.value)}
                  placeholder="Office mobile number"
                  error={!!errors.officeMobileNo}
                  className="w-full"
                />
                {errors.officeMobileNo && <p className="text-red-500 text-xs mt-1">{errors.officeMobileNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Office Email *
                </label>
                <input
                  type="email"
                  value={formData.officeEmail}
                  onChange={(e) => handleInputChange('officeEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.officeEmail && <p className="text-red-500 text-xs mt-1">{errors.officeEmail}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Area *
                  <span className="text-xs text-gray-500 ml-2">
                    (Type to search)
                  </span>
                </label>
                <SearchableSelect
                  options={areas}
                  value={formData.area}
                  onChange={(value) => handleInputChange('area', value)}
                  placeholder="Search areas..."
                  searchFields={['name', 'city', 'state', 'description']}
                  displayField="name"
                  valueField="id"
                  error={!!errors.area}
                  disabled={loadingAreas}
                  minSearchLength={1}
                  maxResults={10}
                  allowClear={true}
                  noResultsText="No areas found"
                  searchingText="Type to search areas..."
                  renderOption={(option, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                      isHighlighted
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-900 hover:bg-gray-50'
                    }`}
                    >
                      <div className="font-medium text-gray-900">{option.name}</div>
                      <div className="text-sm text-gray-600 mt-1">
                        {option.city}, {option.state}
                      </div>
                      {option.description && (
                        <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                      )}
                    </div>
                  )}
                />
                {loadingAreas && (
                  <p className="mt-2 text-sm text-gray-600">Loading areas...</p>
                )}
                {errors.area && <p className="text-red-500 text-xs mt-1">{errors.area}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Tally Users (Optional)
                </label>
                <input
                  type="number"
                  value={formData.noOfTallyUsers}
                  onChange={(e) => handleInputChange('noOfTallyUsers', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="1"
                  min="1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Executive Name *
                  <span className="text-xs text-gray-500 ml-2">
                    (Type 2+ letters to search)
                  </span>
                </label>
                <SearchableSelect
                  options={executives}
                  value={formData.followUpExecutive}
                  onChange={(value) => handleInputChange('followUpExecutive', value)}
                  placeholder="Search executives..."
                  searchFields={['first_name', 'last_name', 'email', 'employee_code']}
                  displayField="first_name"
                  valueField="id"
                  error={!!errors.executiveName}
                  disabled={loadingExecutives}
                  minSearchLength={2}
                  maxResults={10}
                  allowClear={true}
                  noResultsText="No executives found"
                  searchingText="Type 2+ letters to search executives..."
                  // Server-side search props
                  onSearch={searchExecutives}
                  isSearching={isSearchingExecutives}
                  searchResults={executiveSearchResults}
                  renderOption={(option, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                      isHighlighted
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-900 hover:bg-gray-50'
                    }`}
                    >
                      <div className="font-medium text-gray-900">
                        {option.first_name} {option.last_name}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {option.employee_code} • {option.email}
                      </div>
                      {option.department && (
                        <div className="text-xs text-gray-600 font-medium bg-gray-100 px-2 py-1 rounded-full inline-block mt-1">
                          {option.department}
                        </div>
                      )}
                    </div>
                  )}
                  renderSelected={(option) => (
                    <span className="block truncate">
                      {option.first_name} {option.last_name} ({option.employee_code})
                    </span>
                  )}
                />
                {loadingExecutives && (
                  <p className="mt-2 text-sm text-gray-600">Loading executives...</p>
                )}
                {errors.executiveName && <p className="text-red-500 text-xs mt-1">{errors.executiveName}</p>}
              </div>
            </div>
          </div>

          {/* Additional Contact Information */}
          <div className="space-y-4 bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-2 flex items-center">
              <Phone className="mr-2 h-5 w-5 text-gray-900" />
              Additional Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auditor Name *
                </label>
                <input
                  type="text"
                  value={formData.auditorName}
                  onChange={(e) => handleInputChange('auditorName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="Auditor name"
                />
                {errors.auditorName && <p className="text-red-500 text-xs mt-1">{errors.auditorName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auditor Contact Number *
                </label>
                <MobileInput
                  value={formData.auditorNo}
                  onChange={(e) => handleInputChange('auditorNo', e.target.value)}
                  placeholder="Auditor phone number"
                  error={!!errors.auditorNo}
                  className="w-full"
                />
                {errors.auditorNo && <p className="text-red-500 text-xs mt-1">{errors.auditorNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Auditor Email *
                </label>
                <input
                  type="email"
                  value={formData.auditorEmail}
                  onChange={(e) => handleInputChange('auditorEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.auditorEmail && <p className="text-red-500 text-xs mt-1">{errors.auditorEmail}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Consultant Name *
                </label>
                <input
                  type="text"
                  value={formData.taxConsultantName}
                  onChange={(e) => handleInputChange('taxConsultantName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="Tax consultant name"
                />
                {errors.taxConsultantName && <p className="text-red-500 text-xs mt-1">{errors.taxConsultantName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Consultant Contact Number *
                </label>
                <MobileInput
                  value={formData.taxConsultantNo}
                  onChange={(e) => handleInputChange('taxConsultantNo', e.target.value)}
                  placeholder="Tax consultant phone number"
                  error={!!errors.taxConsultantNo}
                  className="w-full"
                />
                {errors.taxConsultantNo && <p className="text-red-500 text-xs mt-1">{errors.taxConsultantNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tax Consultant Email *
                </label>
                <input
                  type="email"
                  value={formData.taxConsultantEmail}
                  onChange={(e) => handleInputChange('taxConsultantEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.taxConsultantEmail && <p className="text-red-500 text-xs mt-1">{errors.taxConsultantEmail}</p>}
              </div>
            </div>
          </div>

          {/* IT Contact Information */}
          <div className="space-y-4 bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-2 flex items-center">
              <User className="mr-2 h-5 w-5 text-gray-900" />
              IT Contact Information (Optional)
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IT Name
                </label>
                <input
                  type="text"
                  value={formData.itName}
                  onChange={(e) => handleInputChange('itName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="IT person name"
                />
                {errors.itName && <p className="text-red-500 text-xs mt-1">{errors.itName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IT Contact Number
                </label>
                <MobileInput
                  value={formData.itNo}
                  onChange={(e) => handleInputChange('itNo', e.target.value)}
                  placeholder="IT phone number"
                  error={!!errors.itNo}
                  className="w-full"
                />
                {errors.itNo && <p className="text-red-500 text-xs mt-1">{errors.itNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  IT Email
                </label>
                <input
                  type="email"
                  value={formData.itEmail}
                  onChange={(e) => handleInputChange('itEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="<EMAIL>"
                />
                {errors.itEmail && <p className="text-red-500 text-xs mt-1">{errors.itEmail}</p>}
              </div>
            </div>
          </div>

          {/* Optional Information */}
          <div className="space-y-4 bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-300 pb-2 flex items-center">
              <Building className="mr-2 h-5 w-5 text-gray-900" />
              Business Information (Optional)
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Product
                  <span className="text-xs text-gray-500 ml-2">
                    (Type 2+ letters to search)
                  </span>
                </label>
                <SearchableSelect
                  options={products}
                  value={formData.product}
                  onChange={(value) => handleInputChange('product', value)}
                  placeholder="Search products..."
                  searchFields={['name', 'description', 'category']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingProducts}
                  minSearchLength={2}
                  maxResults={10}
                  allowClear={true}
                  noResultsText="No products found"
                  searchingText="Type 2+ letters to search products..."
                  renderOption={(option, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                      isHighlighted
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-900 hover:bg-gray-50'
                    }`}
                    >
                      <div className="font-medium text-gray-900">{option.name}</div>
                      {option.description && (
                        <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        {option.category && (
                          <div className="text-xs text-gray-600 font-medium bg-gray-100 px-2 py-1 rounded-full">
                            {option.category}
                          </div>
                        )}
                        {option.price && (
                          <div className="text-sm font-medium text-gray-700">
                            ₹{parseFloat(option.price).toLocaleString('en-IN')}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                />
                {loadingProducts && (
                  <p className="mt-2 text-sm text-gray-600">Loading products...</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  License Edition
                  <span className="text-xs text-gray-500 ml-2">
                    (Type 2+ letters to search)
                  </span>
                </label>
                <SearchableSelect
                  options={licenseEditions}
                  value={formData.licenceEdition}
                  onChange={(value) => handleInputChange('licenceEdition', value)}
                  placeholder="Search license editions..."
                  searchFields={['name', 'description', 'version']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingLicenseEditions}
                  minSearchLength={2}
                  maxResults={10}
                  allowClear={true}
                  noResultsText="No license editions found"
                  searchingText="Type 2+ letters to search editions..."
                  renderOption={(option, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                      isHighlighted
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-900 hover:bg-gray-50'
                    }`}
                    >
                      <div className="font-medium text-gray-900">{option.name}</div>
                      {option.description && (
                        <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        {option.version && (
                          <div className="text-xs text-gray-600 font-medium bg-gray-100 px-2 py-1 rounded-full">
                            v{option.version}
                          </div>
                        )}
                        {option.price && (
                          <div className="text-sm font-medium text-gray-700">
                            ₹{parseFloat(option.price).toLocaleString('en-IN')}
                          </div>
                        )}
                      </div>
                      {option.max_users && (
                        <div className="text-xs text-gray-500 mt-1">
                          Max Users: {option.max_users === 999 ? 'Unlimited' : option.max_users}
                        </div>
                      )}
                    </div>
                  )}
                />
                {loadingLicenseEditions && (
                  <p className="mt-2 text-sm text-gray-600">Loading license editions...</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Industry
                  <span className="text-xs text-gray-500 ml-2">
                    (Type 2+ letters to search)
                  </span>
                </label>
                <SearchableSelect
                  options={industries}
                  value={formData.industry}
                  onChange={(value) => handleInputChange('industry', value)}
                  placeholder="Search industries..."
                  searchFields={['name', 'description', 'category']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingIndustries}
                  minSearchLength={2}
                  maxResults={10}
                  allowClear={true}
                  noResultsText="No industries found"
                  searchingText="Type 2+ letters to search industries..."
                  renderOption={(option, isHighlighted) => (
                    <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                      isHighlighted
                        ? 'bg-gray-100 text-gray-900'
                        : 'text-gray-900 hover:bg-gray-50'
                    }`}
                    >
                      <div className="font-medium text-gray-900">{option.name}</div>
                      {option.description && (
                        <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                      )}
                      <div className="flex items-center justify-between mt-1">
                        {option.category && (
                          <div className="text-xs text-gray-600 font-medium bg-gray-100 px-2 py-1 rounded-full">
                            {option.category}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                />
                {loadingIndustries && (
                  <p className="mt-2 text-sm text-gray-600">Loading industries...</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  GST Number
                </label>
                <input
                  type="text"
                  value={formData.gstNo}
                  onChange={(e) => handleInputChange('gstNo', e.target.value.toUpperCase())}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="22AAAAA0000A1Z5"
                  maxLength="15"
                />
                {errors.gstNo && <p className="text-red-500 text-xs mt-1">{errors.gstNo}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pin Code
                </label>
                <input
                  type="text"
                  value={formData.pinCode}
                  onChange={(e) => handleInputChange('pinCode', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="400001"
                  maxLength="6"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  State/Country
                </label>
                <input
                  type="text"
                  value={formData.stateCountry}
                  onChange={(e) => handleInputChange('stateCountry', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="Maharashtra, India"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Map Location
                </label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={formData.mapLocation}
                    onChange={(e) => handleInputChange('mapLocation', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                    placeholder="Enter location or click map button"
                  />
                  <button
                    type="button"
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200"
                    title="Select location on map"
                  >
                    <MapPin className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Latitude
                </label>
                <input
                  type="number"
                  step="any"
                  value={formData.latitude}
                  onChange={(e) => handleInputChange('latitude', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 bg-gray-50"
                  placeholder="Auto-filled from map"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Longitude
                </label>
                <input
                  type="number"
                  step="any"
                  value={formData.longitude}
                  onChange={(e) => handleInputChange('longitude', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 bg-gray-50"
                  placeholder="Auto-filled from map"
                  readOnly
                />
              </div>

              {/* Default Call Type (Task 4) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Default Call Type *
                </label>
                <select
                  value={formData.defaultCallType}
                  onChange={(e) => handleInputChange('defaultCallType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  <option value="free_call">🆓 Free Call</option>
                  <option value="per_call">💰 Per Call</option>
                  <option value="amc_call">🔧 AMC Call</option>
                </select>
                <p className="mt-1 text-xs text-gray-600">
                  First 3 services will be free calls, then automatically become per call services
                </p>
                {errors.defaultCallType && <p className="text-red-500 text-xs mt-1">{errors.defaultCallType}</p>}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Remarks
                </label>
                <textarea
                  value={formData.remarks}
                  onChange={(e) => handleInputChange('remarks', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500"
                  placeholder="Any additional remarks..."
                  rows="3"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-gray-900 border border-transparent rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <Loader className="h-4 w-4 animate-spin" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Create Customer</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewCustomerModal;
