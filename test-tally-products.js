// Test script to check the tally products API validation fix
async function testTallyProductsAPI() {
    console.log('🧪 Testing Tally Products API validation fix...');
    
    try {
        // Test data with empty hsn_code field
        const testData = {
            name: 'Test Tally Product',
            code: 'TEST_PRODUCT',
            description: 'Test product for validation',
            category: 'software',
            version: '1.0',
            price: 1000.00,
            cost_price: 800.00,
            hsn_code: '', // Empty string - should be converted to null
            gst_rate: 18.00,
            unit: 'Nos',
            is_service: false,
            is_active: true
        };
        
        console.log('📤 Sending test data:', testData);
        
        // Test creating a tally product with empty hsn_code
        const response = await fetch('http://localhost:8082/api/v1/master-data/tally-products', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Tally Product created successfully!');
            console.log('📊 Response:', {
                success: result.success,
                message: result.message,
                hsnCode: result.data?.tallyproduct?.hsn_code,
                version: result.data?.tallyproduct?.version,
                unit: result.data?.tallyproduct?.unit
            });
            
            // Clean up - delete the test record
            if (result.data?.tallyproduct?.id) {
                const deleteResponse = await fetch(`http://localhost:8082/api/v1/master-data/tally-products/${result.data.tallyproduct.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (deleteResponse.ok) {
                    console.log('🗑️ Test record cleaned up successfully');
                } else {
                    console.log('⚠️ Could not clean up test record');
                }
            }
        } else {
            console.error('❌ Tally Product creation failed:');
            console.error('Status:', response.status);
            console.error('Response:', result);
        }
        
        // Test updating with empty hsn_code
        console.log('\n🔄 Testing update with empty hsn_code...');
        
        const updateData = {
            name: 'Updated Test Product',
            hsn_code: '',    // Empty string - should be converted to null
            version: '',     // Empty string - should be converted to null
            unit: ''         // Empty string - should be converted to null
        };
        
        console.log('📤 Sending update data:', updateData);
        
        // Note: This test assumes there's an existing tally product
        // In a real test, you'd create a record first, then update it
        const updateResponse = await fetch('http://localhost:8082/api/v1/master-data/tally-products/9f4aef1d-1da9-4dd8-b941-bc6b64412acc', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });
        
        const updateResult = await updateResponse.json();
        
        if (updateResponse.ok) {
            console.log('✅ Tally Product updated successfully!');
            console.log('📊 Update Response:', {
                success: updateResult.success,
                message: updateResult.message,
                hsnCode: updateResult.data?.tallyproduct?.hsn_code,
                version: updateResult.data?.tallyproduct?.version,
                unit: updateResult.data?.tallyproduct?.unit
            });
        } else {
            console.log('ℹ️ Update test result (may fail if record doesn\'t exist):');
            console.log('Status:', updateResponse.status);
            console.log('Response:', updateResult);
        }
        
        // Test with various empty value formats
        console.log('\n🧪 Testing various empty value formats...');
        
        const emptyValueTests = [
            { hsn_code: '' },           // Empty string
            { hsn_code: '   ' },        // Whitespace only
            { hsn_code: null },         // Null
            { hsn_code: undefined },    // Undefined (will be omitted from JSON)
            { hsn_code: '123456' },     // Valid HSN code
        ];
        
        for (let i = 0; i < emptyValueTests.length; i++) {
            const testCase = emptyValueTests[i];
            console.log(`\n📋 Test case ${i + 1}:`, testCase);
            
            const testResponse = await fetch('http://localhost:8082/api/v1/master-data/tally-products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: `Test Product ${i + 1}`,
                    code: `TEST_${i + 1}`,
                    category: 'software',
                    ...testCase
                })
            });
            
            const testResult = await testResponse.json();
            
            if (testResponse.ok) {
                console.log(`✅ Test case ${i + 1} passed`);
                // Clean up
                if (testResult.data?.tallyproduct?.id) {
                    await fetch(`http://localhost:8082/api/v1/master-data/tally-products/${testResult.data.tallyproduct.id}`, {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
            } else {
                console.log(`❌ Test case ${i + 1} failed:`, testResult.message);
            }
        }
        
    } catch (error) {
        console.error('❌ Error testing API:', error.message);
    }
}

// Run the test
testTallyProductsAPI();
