import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'http';
import { Server } from 'socket.io';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from the correct path
const envPath = path.join(__dirname, '../.env');
console.log('🔧 Loading environment from:', envPath);
dotenv.config({ path: envPath });

// Debug environment variables
console.log('🔍 Environment Debug:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('APP_URL:', process.env.APP_URL);

// Import configurations
import appConfig from '../config/app.js';
import { logger } from './utils/logger.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { requestLogger } from './middleware/requestLogger.js';
import { serveStaticFiles } from './middleware/staticFiles.js';

// Import routes
import healthRoutes from './routes/health.js';
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import customerRoutes from './routes/customers.js';
import leadRoutes from './routes/leads.js';
import serviceCallRoutes from './routes/serviceCalls.js';
import masterDataRoutes from './routes/masterData.js';
import onlineCallTypeRoutes from './routes/onlineCallTypes.js';
import executiveRoutes from './routes/executives.js';
import dashboardRoutes from './routes/dashboard.js';
import salesRoutes from './routes/sales.js';
import reportsRoutes from './routes/reports.js';
import settingsRoutes from './routes/settings.js';
import profileRoutes from './routes/profile.js';
import notificationRoutes from './routes/notifications.js';
import renewalNotificationRoutes from './routes/renewalNotifications.js';
import batchRoutes from './routes/batchRoutes.js';
import analyticsRoutes from './routes/analytics.js';
// Attendance Management routes
import attendanceRoutes from './routes/attendanceRoutes.js';
import leaveRoutes from './routes/leaveRoutes.js';
import payrollRoutes from './routes/payrollRoutes.js';
// SaaS routes
import subscriptionRoutes from './routes/subscription.js';
import billingRoutes from './routes/billing.js';
import webhookRoutes from './routes/webhooks.js';

// Environment variables already loaded above

// Create Express application
const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Debug middleware to log header sizes and prevent 431 errors
app.use((req, res, next) => {
  // Calculate total header size
  const headerString = Object.entries(req.headers)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\r\n');
  const headerSize = Buffer.byteLength(headerString, 'utf8');

  // Log large headers for debugging
  if (headerSize > 8192) { // 8KB threshold
    console.log(`⚠️ Large headers detected (${headerSize} bytes):`, {
      url: req.url,
      method: req.method,
      headerSize,
      authHeaderLength: req.headers.authorization ? req.headers.authorization.length : 0,
      userAgent: req.headers['user-agent'] ? req.headers['user-agent'].substring(0, 100) : 'none'
    });
  }

  // Log OPTIONS requests to monitor CORS preflight optimization
  if (req.method === 'OPTIONS') {
    console.log(`🔄 CORS Preflight: ${req.originalUrl} - Origin: ${req.headers.origin || 'none'}`);
  }

  // Set max header size to 32KB to handle large tokens temporarily
  if (req.connection) {
    req.connection.maxHeadersCount = 1000;
  }
  next();
});

// Security middleware - HTTP only configuration (minimal for debugging)
app.use(helmet({
  contentSecurityPolicy: false, // Temporarily disable CSP
  crossOriginEmbedderPolicy: false,
  hsts: false, // Disable HTTPS Strict Transport Security
  crossOriginOpenerPolicy: false, // Disable Cross-Origin-Opener-Policy
  crossOriginResourcePolicy: false, // Disable CORP
}));

// CORS configuration (conditionally applied)
if (appConfig.frontend.enableCors) {
  app.use(cors({
    origin: appConfig.frontend.corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    maxAge: 86400, // Cache preflight responses for 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204
  }));
  logger.info('🌐 CORS enabled for origins:', appConfig.frontend.corsOrigins);
} else {
  // For same-origin deployments, allow all origins
  app.use(cors({
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    maxAge: 86400, // Cache preflight responses for 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204
  }));
  logger.info('🌐 CORS disabled - allowing all origins (same-origin deployment)');
}

// Rate limiting (conditionally applied)
if (appConfig.security.enableRateLimiting) {
  const limiter = rateLimit({
    windowMs: appConfig.security.rateLimitWindowMs,
    max: appConfig.security.rateLimitMaxRequests,
    message: {
      error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use(limiter);
  logger.info('🛡️ Rate limiting enabled');
} else {
  logger.info('⚠️ Rate limiting disabled');
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression middleware
app.use(compression());

// Debug middleware to log all requests
app.use((req, res, next) => {
  console.log(`🔍 DEBUG: ${req.method} ${req.path} ${req.url}`);
  next();
});

// Request logging middleware
app.use(requestLogger);

// API routes
app.use('/health', healthRoutes);
app.use(`${appConfig.api.prefix}/auth`, authRoutes);
app.use(`${appConfig.api.prefix}/users`, userRoutes);
app.use(`${appConfig.api.prefix}/customers`, customerRoutes);
app.use(`${appConfig.api.prefix}/leads`, leadRoutes);
// Mount batch routes BEFORE service-calls routes to avoid conflicts
app.use(`${appConfig.api.prefix}`, batchRoutes); // Batch API routes
app.use(`${appConfig.api.prefix}/service-calls`, serviceCallRoutes);
app.use(`${appConfig.api.prefix}/master-data`, masterDataRoutes);

// Add online call types routes here with existing routes
console.log('🔧 Loading online call types routes (with existing routes)...');
console.log('🔍 onlineCallTypeRoutes type:', typeof onlineCallTypeRoutes);
console.log('🔍 onlineCallTypeRoutes is function:', typeof onlineCallTypeRoutes === 'function');
console.log('🔍 onlineCallTypeRoutes stack:', onlineCallTypeRoutes.stack ? onlineCallTypeRoutes.stack.length : 'no stack');
app.use(`${appConfig.api.prefix}/online-call-types`, onlineCallTypeRoutes);
console.log('✅ Online call types routes loaded with existing routes');
console.log('🔧 API Prefix:', appConfig.api.prefix);

// Add a direct test route to the app
app.get(`${appConfig.api.prefix}/direct-test`, (req, res) => {
  res.json({
    message: 'Direct test route is working!',
    timestamp: new Date().toISOString(),
    path: req.path
  });
});
console.log('✅ Direct test route added at:', `${appConfig.api.prefix}/direct-test`);



// Online call types routes moved to be with existing routes above
app.use(`${appConfig.api.prefix}/executives`, executiveRoutes);
app.use(`${appConfig.api.prefix}/dashboard`, dashboardRoutes);
app.use(`${appConfig.api.prefix}/sales`, salesRoutes);
app.use(`${appConfig.api.prefix}/reports`, reportsRoutes);
app.use(`${appConfig.api.prefix}/settings`, settingsRoutes);
app.use(`${appConfig.api.prefix}/profile`, profileRoutes);
app.use(`${appConfig.api.prefix}/notifications`, notificationRoutes);
app.use(`${appConfig.api.prefix}/renewal-notifications`, renewalNotificationRoutes);
app.use(`${appConfig.api.prefix}/analytics`, analyticsRoutes);
// Attendance Management routes
app.use(`${appConfig.api.prefix}/attendance`, attendanceRoutes);
app.use(`${appConfig.api.prefix}/leaves`, leaveRoutes);
app.use(`${appConfig.api.prefix}/payroll`, payrollRoutes);
// SaaS routes
app.use(`${appConfig.api.prefix}/subscription`, subscriptionRoutes);
app.use(`${appConfig.api.prefix}/billing`, billingRoutes);
app.use('/webhooks', webhookRoutes); // Webhooks don't need API prefix

// Serve static files (uploads, etc.)
app.use('/uploads', express.static('uploads'));

// API documentation (Swagger)
import { conditionalSwagger } from './utils/swagger.js';
try {
  const swaggerEnabled = conditionalSwagger(app);
  if (swaggerEnabled) {
    logger.info('📚 Swagger documentation enabled and configured');
  }
} catch (error) {
  logger.error('❌ Failed to setup Swagger documentation:', error);
}

// Serve frontend static files in production
serveStaticFiles(app);

// 404 handler (must be after static file serving)
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  // Stop scheduled jobs
  try {
    const { default: scheduledJobService } = await import('./services/ScheduledJobService.js');
    scheduledJobService.stopAllJobs();
    logger.info('⏰ Scheduled jobs stopped');
  } catch (error) {
    logger.error('❌ Error stopping scheduled jobs:', error);
  }

  server.close(() => {
    logger.info('HTTP server closed.');

    // Close database connections, cleanup resources, etc.
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Create HTTP server
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: appConfig.frontend.corsOrigins,
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`🔌 Socket connected: ${socket.id}`);

  // Handle joining service call rooms
  socket.on('join_service_room', ({ serviceCallId }) => {
    const roomName = `service_${serviceCallId}`;
    socket.join(roomName);
    logger.info(`📡 Socket ${socket.id} joined room: ${roomName}`);
  });

  // Handle leaving service call rooms
  socket.on('leave_service_room', ({ serviceCallId }) => {
    const roomName = `service_${serviceCallId}`;
    socket.leave(roomName);
    logger.info(`📡 Socket ${socket.id} left room: ${roomName}`);
  });

  // Handle timer status requests
  socket.on('get_timer_status', async ({ serviceCallId }) => {
    try {
      // Import here to avoid circular dependencies
      const { default: models } = await import('./models/index.js');

      const serviceCall = await models.ServiceCall.findByPk(serviceCallId, {
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'category', 'color']
          }
        ]
      });

      if (serviceCall) {
        const timerData = {
          serviceCallId,
          timer: {
            is_running: serviceCall.started_at && !serviceCall.completed_at,
            is_paused: !serviceCall.started_at || serviceCall.completed_at,
            current_accumulated_seconds: serviceCall.total_time_seconds || 0,
            current_accumulated_formatted: formatTime(serviceCall.total_time_seconds || 0)
          },
          status: serviceCall.status,
          timestamp: new Date().toISOString()
        };

        socket.emit('timer_update', timerData);
      }
    } catch (error) {
      logger.error('Error fetching timer status:', error);
      socket.emit('timer_error', { serviceCallId, error: error.message });
    }
  });

  socket.on('disconnect', (reason) => {
    logger.info(`🔌 Socket disconnected: ${socket.id}, reason: ${reason}`);
  });
});

// Helper function to format time
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Make io available globally for timer updates
global.io = io;

// Start server
const PORT = appConfig.app.port;
server.listen(PORT, async () => {
  logger.info(`🚀 TallyCRM Backend Server started successfully!`);
  logger.info(`📍 Environment: ${appConfig.app.env}`);
  logger.info(`🌐 Server running on: ${appConfig.app.url}`);
  logger.info(`📡 API Base URL: ${appConfig.app.url}${appConfig.api.prefix}`);
  logger.info(`🔌 Socket.IO server initialized`);

  if (appConfig.development.enableSwagger && appConfig.app.env === 'development') {
    logger.info(`📚 API Docs: ${appConfig.app.url}${appConfig.development.swaggerPath}`);
  }

  // Initialize scheduled jobs
  try {
    const { default: scheduledJobService } = await import('./services/ScheduledJobService.js');
    await scheduledJobService.initialize();
    logger.info('⏰ Scheduled job service initialized successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize scheduled job service:', error);
  }
});

// Configure server to handle large headers (for JWT tokens)
server.maxHeadersCount = 0; // No limit on header count
server.headersTimeout = 60000; // 60 seconds
server.requestTimeout = 60000; // 60 seconds

export default app;
