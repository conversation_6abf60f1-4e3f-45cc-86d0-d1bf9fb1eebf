import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LeadContactHistory = sequelize.define('LeadContactHistory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    lead_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'leads',
        key: 'id',
      },
      comment: 'Reference to the lead',
    },
    executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Executive who made the contact',
    },
    contact_type: {
      type: DataTypes.ENUM('phone', 'email', 'meeting', 'whatsapp', 'other'),
      allowNull: false,
      defaultValue: 'phone',
      comment: 'Type of contact made',
    },
    contact_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'When the contact was made',
    },
    duration_minutes: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Duration of contact in minutes (for calls/meetings)',
    },
    outcome: {
      type: DataTypes.ENUM('interested', 'not_interested', 'callback_requested', 'meeting_scheduled', 'converted', 'no_response', 'other'),
      allowNull: true,
      comment: 'Outcome of the contact',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Notes about the contact',
    },
    next_follow_up: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When to follow up next',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'lead_contact_history',
    timestamps: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['lead_id'],
      },
      {
        fields: ['executive_id'],
      },
      {
        fields: ['contact_date'],
      },
      {
        fields: ['created_by'],
      },
      {
        fields: ['next_follow_up'],
      },
    ],
  });

  // Associations
  LeadContactHistory.associate = function(models) {
    LeadContactHistory.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    LeadContactHistory.belongsTo(models.Lead, {
      foreignKey: 'lead_id',
      as: 'lead',
    });

    LeadContactHistory.belongsTo(models.Executive, {
      foreignKey: 'executive_id',
      as: 'executive',
    });

    LeadContactHistory.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });
  };

  return LeadContactHistory;
}
