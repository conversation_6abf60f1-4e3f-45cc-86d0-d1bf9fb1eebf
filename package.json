{"name": "tallycrm-workspace", "version": "1.0.0", "description": "TallyCRM SaaS - Complete workspace for frontend and backend", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && npm run lint:fix", "format": "npm run format:frontend && npm run format:backend", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && npm run format", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf node_modules dist", "clean:backend": "cd backend && rm -rf node_modules build", "reset": "npm run clean && npm run install:all", "db:setup": "cd backend && npm run db:create && npm run migrate && npm run seed", "db:reset": "cd backend && npm run db:drop && npm run db:create && npm run migrate && npm run seed", "prepare": "husky install"}, "keywords": ["crm", "tally", "saas", "react", "nodejs", "express", "postgresql", "workspace"], "author": "Cloudstier Solutions", "license": "PROPRIETARY", "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/cloudstier/tallycrm.git"}, "bugs": {"url": "https://github.com/cloudstier/tallycrm/issues"}, "homepage": "https://github.com/cloudstier/tallycrm#readme", "dependencies": {"axios": "1.10.0", "node-fetch": "3.3.2"}}