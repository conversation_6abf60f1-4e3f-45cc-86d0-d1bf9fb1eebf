# Service Call Timer System Documentation

## 📋 Overview

The Service Call Timer System is an automated time tracking mechanism that monitors the time spent on service calls based on their status transitions. The timer automatically starts, pauses, resumes, and stops based on specific status changes, providing accurate time tracking for billing and performance analysis.

## 🎯 Timer Behavior Summary

| Timer Action | Status Codes | Description |
|--------------|-------------|-------------|
| **START** | `ON_PROCESS`, `PROGRESS`, `IN_PROGRESS` | Timer starts when service work begins |
| **PAUSE** | `ON_HOLD`, `HOLD` | Timer pauses, calculates session time |
| **STOP** | `COMPLETED`, `CANCELLED`, `NO_ISSUE` | Timer stops, calculates total time |
| **RESET** | `PENDING`, `OPEN` | Timer resets to 00:00:00 |
| **NO ACTION** | All other statuses | Status change logged without timer action |

## 🔄 Complete Call Status List

### Timer-Active Statuses (START TIMER)

| Status Name | Status Code | Category | Timer Action | Billable | Description |
|-------------|-------------|----------|--------------|----------|-------------|
| **On Process** | `ON_PROCESS` | `in_progress` | ⏱️ **START** | ✅ Yes | Call is currently being processed |
| **Progress** | `PROGRESS` | `in_progress` | ⏱️ **START** | ✅ Yes | Work is in progress |
| **In Progress** | `IN_PROGRESS` | `in_progress` | ⏱️ **START** | ✅ Yes | Service work is actively ongoing |

### Timer-Pause Statuses (PAUSE TIMER)

| Status Name | Status Code | Category | Timer Action | Billable | Description |
|-------------|-------------|----------|--------------|----------|-------------|
| **Hold** | `HOLD` | `in_progress` | ⏸️ **PAUSE** | ✅ Yes | Work temporarily paused |
| **On Hold** | `ON_HOLD` | `in_progress` | ⏸️ **PAUSE** | ✅ Yes | Service temporarily on hold |

### Timer-Stop Statuses (STOP TIMER)

| Status Name | Status Code | Category | Timer Action | Billable | Description |
|-------------|-------------|----------|--------------|----------|-------------|
| **Completed** | `COMPLETED` | `closed` | ⏹️ **STOP** | ✅ Yes | Service successfully completed |
| **Cancelled** | `CANCELLED` | `cancelled` | ⏹️ **STOP** | ❌ No | Service cancelled |
| **No Issue** | `NO_ISSUE` | `resolved` | ⏹️ **STOP** | ❌ No | No issue found, false alarm |

### Timer-Reset Statuses (RESET TIMER)

| Status Name | Status Code | Category | Timer Action | Billable | Description |
|-------------|-------------|----------|--------------|----------|-------------|
| **Pending** | `PENDING` | `open` | 🔄 **RESET** | ❌ No | Call pending, waiting to be processed |
| **Open** | `OPEN` | `open` | 🔄 **RESET** | ❌ No | Call is open and ready to serve |

### Non-Timer Statuses (NO TIMER ACTION)

| Status Name | Status Code | Category | Timer Action | Billable | Description |
|-------------|-------------|----------|--------------|----------|-------------|
| **Onsite** | `ONSITE` | `in_progress` | 📝 **LOG ONLY** | ✅ Yes | Onsite visit required/in progress |
| **Customization** | `CUSTOMIZATION` | `in_progress` | 📝 **LOG ONLY** | ✅ Yes | Customization work required |
| **Call Not Atten** | `CALL_NOT_ATTEN` | `open` | 📝 **LOG ONLY** | ❌ No | Call not attended by customer |
| **Whats Up** | `WHATS_UP` | `in_progress` | 📝 **LOG ONLY** | ❌ No | WhatsApp communication in progress |
| **Urgent** | `URGENT` | `open` | 📝 **LOG ONLY** | ✅ Yes | Urgent priority call |
| **Follow Up Programmer** | `FOLLOW_UP_PROGRAMMER` | `in_progress` | 📝 **LOG ONLY** | ✅ Yes | Follow up required with programmer |
| **Follow Up Customer** | `FOLLOW_UP_CUSTOMER` | `in_progress` | 📝 **LOG ONLY** | ❌ No | Follow up required with customer |

## 🔄 Timer State Transitions

### Starting the Timer
```
PENDING/OPEN → ON_PROCESS/PROGRESS/IN_PROGRESS
```
- Timer starts automatically
- `started_at` timestamp is set
- Time tracking history records timer start
- Frontend shows running timer with live updates

### Pausing the Timer
```
ON_PROCESS/PROGRESS → HOLD/ON_HOLD
```
- Timer pauses automatically
- Session duration is calculated and saved
- Time tracking history records pause with session time
- Timer can be resumed later

### Resuming the Timer
```
HOLD/ON_HOLD → ON_PROCESS/PROGRESS/IN_PROGRESS
```
- Timer resumes automatically
- New session starts
- Previous session time is preserved
- Total time accumulates across all sessions

### Stopping the Timer
```
ON_PROCESS/PROGRESS → COMPLETED/CANCELLED/NO_ISSUE
```
- Timer stops automatically
- Final session duration is calculated
- Total time across all sessions is calculated
- `completed_at` timestamp is set
- Final time tracking summary is saved

### Resetting the Timer
```
Any Status → PENDING/OPEN
```
- Timer resets to 00:00:00
- All time tracking data is cleared
- `started_at` is reset to null
- Fresh start for the service call

## 📊 Time Tracking Data Structure

### Database Fields
```sql
-- ServiceCall table fields
started_at: TIMESTAMP          -- When timer first started
completed_at: TIMESTAMP        -- When timer was stopped
total_time_minutes: INTEGER    -- Total time in minutes
total_time_seconds: INTEGER    -- Total time in seconds (more precise)
actual_hours: DECIMAL(5,2)     -- Calculated hours for billing
time_tracking_history: JSONB   -- Detailed session history
```

### Time Tracking History Format
```json
{
  "timestamp": "2025-06-10T12:06:18.275Z",
  "user_id": "user-uuid",
  "status_from": "PENDING",
  "status_to": "ON_PROCESS",
  "action": "timer_start",
  "start_time": "2025-06-10T12:06:18.275Z",
  "session_duration_seconds": 152,
  "session_duration_minutes": 3,
  "total_duration_seconds": 152,
  "total_duration_minutes": 3
}
```

## 🎮 Frontend Timer Display

### Live Timer (In Progress)
```
⏱️ 01:23:45 running
```
- Shows hours:minutes:seconds
- Updates every second
- Animated play icon
- Blue color indicating active

### Total Time Display
```
🕐 Total: 2h 35m
```
- Shows accumulated time across all sessions
- Displayed even when timer is paused
- Gray color for completed time

### Timer States
- **Running**: Blue with animated icon
- **Paused**: Orange with pause icon
- **Stopped**: Green with total time
- **Reset**: Gray showing 00:00:00

## 🔧 Technical Implementation

### Backend Service
- **File**: `backend/src/services/TimeTrackingService.js`
- **Method**: `handleStatusChange(serviceCall, oldStatus, newStatus, userId)`
- **Precision**: Seconds-level accuracy
- **Storage**: JSONB format for session history

### Status Transition Logic
```javascript
switch (newStatusCode) {
  case 'ON_PROCESS':
  case 'PROGRESS':
  case 'IN_PROGRESS':
    await this.startTimer(serviceCall, timeEntry, timeHistory);
    break;
    
  case 'COMPLETED':
  case 'CANCELLED':
  case 'NO_ISSUE':
    await this.stopTimer(serviceCall, timeEntry, timeHistory);
    break;
    
  case 'ON_HOLD':
  case 'HOLD':
    await this.pauseTimer(serviceCall, timeEntry, timeHistory);
    break;
    
  case 'PENDING':
  case 'OPEN':
    // Reset timer to 00:00:00
    timeEntry.action = 'timer_reset';
    break;
    
  default:
    // Just log status change
    timeEntry.action = 'status_change';
    break;
}
```

### Frontend Integration
- **File**: `frontend/src/pages/services/ServiceList.jsx`
- **Component**: `TimerDisplay`
- **Updates**: Real-time using `setInterval`
- **Format**: HH:MM:SS display

## 📈 Business Rules

### Billable Time
- Only time spent in **timer-active statuses** is billable
- Paused time is included in total billable time
- Non-timer statuses don't contribute to billable hours

### Auto-Close Rules
- Some statuses have `auto_close_after_days` settings
- `CALL_NOT_ATTEN`: Auto-close after 3 days
- `FOLLOW_UP_PROGRAMMER`: Auto-close after 2 days
- `FOLLOW_UP_CUSTOMER`: Auto-close after 3 days

### Status Transitions
- Not all status transitions are allowed
- Validation prevents invalid transitions
- Final statuses (`COMPLETED`, `CANCELLED`, `NO_ISSUE`) cannot be changed

## 🎯 Key Features

### Automatic Timer Management
- ✅ No manual intervention required
- ✅ Starts/stops based on status changes
- ✅ Handles multiple pause/resume cycles
- ✅ Maintains session history

### Precision Tracking
- ✅ Seconds-level accuracy
- ✅ Multiple session support
- ✅ Cumulative time calculation
- ✅ Detailed audit trail

### Business Intelligence
- ✅ Accurate billing data
- ✅ Performance metrics
- ✅ Time allocation analysis
- ✅ Productivity insights

### User Experience
- ✅ Real-time timer display
- ✅ Visual status indicators
- ✅ Automatic updates
- ✅ No manual timer management

## 🔍 Monitoring & Analytics

### Available Metrics
- Total time spent per service call
- Average time per service type
- Time distribution across statuses
- Productivity by technician
- Billing accuracy reports

### API Endpoints
- `GET /service-calls/:id/time-tracking` - Get time summary
- `GET /service-calls/time-tracking-stats` - Get statistics
- Timer data included in service call responses

## 🚀 Future Enhancements

### Planned Features
- Time tracking reports and dashboards
- Automated billing integration
- Performance benchmarking
- Custom timer rules per service type
- Mobile app timer synchronization

## 🔄 Complete Status Flow Examples

### Example 1: Normal Service Flow
```
PENDING → ON_PROCESS → COMPLETED
  🔄        ⏱️ START     ⏹️ STOP
  Reset     Timer On     Timer Off
  00:00     Running      Total: 45m
```

### Example 2: Service with Hold
```
PENDING → ON_PROCESS → HOLD → ON_PROCESS → COMPLETED
  🔄        ⏱️ START    ⏸️ PAUSE  ⏱️ RESUME   ⏹️ STOP
  Reset     Session 1   Save S1   Session 2   Total Time
  00:00     20m         20m       25m         45m total
```

### Example 3: Service with Multiple Pauses
```
PENDING → ON_PROCESS → HOLD → PROGRESS → HOLD → PROGRESS → COMPLETED
  🔄        ⏱️ START    ⏸️ PAUSE ⏱️ RESUME ⏸️ PAUSE ⏱️ RESUME  ⏹️ STOP
  Reset     Session 1   Save S1  Session 2  Save S2  Session 3  Total
  00:00     15m         15m      20m        35m      10m        45m total
```

### Example 4: Cancelled Service
```
PENDING → ON_PROCESS → CANCELLED
  🔄        ⏱️ START     ⏹️ STOP
  Reset     Timer On     Timer Off (Not Billable)
  00:00     Running      Total: 30m (Logged but not billed)
```

### Example 5: Reset and Restart
```
PENDING → ON_PROCESS → PENDING → ON_PROCESS → COMPLETED
  🔄        ⏱️ START     🔄 RESET   ⏱️ START     ⏹️ STOP
  Reset     Timer On     Reset      Fresh Start  Timer Off
  00:00     Running      00:00      Running      Total: 25m
```

## 🎯 Timer Behavior Matrix

| From Status | To Status | Timer Action | Time Calculation | Billable |
|-------------|-----------|--------------|------------------|----------|
| PENDING | ON_PROCESS | START | Timer begins | Yes |
| PENDING | PROGRESS | START | Timer begins | Yes |
| ON_PROCESS | HOLD | PAUSE | Session saved | Yes |
| HOLD | ON_PROCESS | RESUME | New session | Yes |
| ON_PROCESS | COMPLETED | STOP | Total calculated | Yes |
| ON_PROCESS | CANCELLED | STOP | Total calculated | No |
| ON_PROCESS | NO_ISSUE | STOP | Total calculated | No |
| HOLD | COMPLETED | STOP | Total calculated | Yes |
| Any Status | PENDING | RESET | All time cleared | No |
| ONSITE | CUSTOMIZATION | LOG ONLY | No timer change | Yes |
| WHATS_UP | FOLLOW_UP_CUSTOMER | LOG ONLY | No timer change | No |

## 🛠️ Troubleshooting Guide

### Common Issues

#### Timer Not Starting
**Problem**: Status changed to ON_PROCESS but timer didn't start
**Solution**:
- Check if TimeTrackingService is properly called in status update
- Verify status code matches exactly (`ON_PROCESS`, `PROGRESS`, `IN_PROGRESS`)
- Check database logs for any errors

#### Timer Not Stopping
**Problem**: Status changed to COMPLETED but timer still running
**Solution**:
- Verify status transition was successful
- Check if `completed_at` timestamp was set
- Refresh the page to see updated timer state

#### Incorrect Time Calculation
**Problem**: Total time doesn't match expected duration
**Solution**:
- Check `time_tracking_history` for all sessions
- Verify each session has proper start/end times
- Look for missing pause/resume entries

#### Timer Reset Unexpectedly
**Problem**: Timer reset when it shouldn't have
**Solution**:
- Check if status was changed to PENDING or OPEN
- Verify no unauthorized status changes occurred
- Review time tracking history for reset entries

### Debug Information

#### Check Timer Status
```javascript
// Get time tracking summary
const summary = TimeTrackingService.getTimeTrackingSummary(serviceCall);
console.log('Timer running:', summary.is_timer_running);
console.log('Total time:', summary.total_time_formatted);
console.log('Sessions:', summary.sessions.length);
```

#### Verify Status Transitions
```javascript
// Check valid transitions
const canTransition = currentStatus.canTransitionTo(newStatus);
console.log('Can transition:', canTransition);
```

#### Review Time History
```sql
-- Check time tracking history in database
SELECT
  call_number,
  time_tracking_history,
  started_at,
  completed_at,
  total_time_seconds
FROM service_calls
WHERE id = 'service-call-id';
```

## 📋 Configuration Options

### Timer Settings
```javascript
// TimeTrackingService configuration
const TIMER_CONFIG = {
  precision: 'seconds',           // seconds | minutes
  autoSave: true,                // Auto-save sessions
  maxSessionGap: 24 * 60 * 60,   // Max gap between sessions (24 hours)
  roundingMode: 'up',            // up | down | nearest
  includeWeekends: true,         // Include weekend time
  businessHoursOnly: false       // Track only business hours
};
```

### Status Configuration
```javascript
// Custom timer behavior per status
const STATUS_TIMER_CONFIG = {
  'ON_PROCESS': { action: 'start', billable: true },
  'PROGRESS': { action: 'start', billable: true },
  'HOLD': { action: 'pause', billable: true },
  'COMPLETED': { action: 'stop', billable: true },
  'CANCELLED': { action: 'stop', billable: false }
};
```

## 📊 Reporting & Analytics

### Available Reports
1. **Time Tracking Summary Report**
   - Total time per service call
   - Average time per status
   - Billable vs non-billable time

2. **Productivity Analysis**
   - Time spent per technician
   - Average resolution time
   - Status transition patterns

3. **Billing Report**
   - Billable hours by customer
   - Revenue by time spent
   - Efficiency metrics

### Sample Queries
```sql
-- Get total billable time by customer
SELECT
  c.company_name,
  SUM(sc.total_time_seconds) / 3600.0 as total_hours,
  COUNT(sc.id) as service_count,
  AVG(sc.total_time_seconds) / 3600.0 as avg_hours_per_service
FROM service_calls sc
JOIN customers c ON sc.customer_id = c.id
WHERE sc.total_time_seconds > 0
GROUP BY c.id, c.company_name
ORDER BY total_hours DESC;

-- Get time distribution by status
SELECT
  cs.name as status_name,
  COUNT(*) as call_count,
  AVG(sc.total_time_seconds) / 3600.0 as avg_hours,
  SUM(sc.total_time_seconds) / 3600.0 as total_hours
FROM service_calls sc
JOIN call_statuses cs ON sc.status_id = cs.id
WHERE sc.total_time_seconds > 0
GROUP BY cs.id, cs.name
ORDER BY total_hours DESC;
```

## 🔐 Security & Permissions

### Timer Access Control
- Only authorized users can change service call status
- Timer modifications are logged with user ID
- Audit trail maintained for all timer actions
- Role-based access to timer reports

### Data Integrity
- Timer data is immutable once recorded
- Session history cannot be manually edited
- Automatic validation of time calculations
- Backup and recovery procedures in place

---

## 📚 Quick Reference

### Timer Actions by Status Code
```
START TIMER:  ON_PROCESS, PROGRESS, IN_PROGRESS
PAUSE TIMER:  HOLD, ON_HOLD
STOP TIMER:   COMPLETED, CANCELLED, NO_ISSUE
RESET TIMER:  PENDING, OPEN
NO ACTION:    ONSITE, CUSTOMIZATION, CALL_NOT_ATTEN,
              WHATS_UP, URGENT, FOLLOW_UP_PROGRAMMER,
              FOLLOW_UP_CUSTOMER
```

### Key Database Fields
```
started_at           - Timer start timestamp
completed_at         - Timer stop timestamp
total_time_seconds   - Total time in seconds
total_time_minutes   - Total time in minutes
actual_hours         - Billable hours (decimal)
time_tracking_history - Session details (JSONB)
```

### API Endpoints
```
GET  /service-calls/:id/time-tracking     - Get timer summary
GET  /service-calls/time-tracking-stats   - Get statistics
PUT  /service-calls/:id/status           - Update status (triggers timer)
```

---

*This comprehensive documentation covers the complete Service Call Timer System implementation. For the latest updates and technical details, refer to the source code in `backend/src/services/TimeTrackingService.js`.*

**Last Updated**: June 2025
**Version**: 1.0
**Author**: TallyCRM Development Team
