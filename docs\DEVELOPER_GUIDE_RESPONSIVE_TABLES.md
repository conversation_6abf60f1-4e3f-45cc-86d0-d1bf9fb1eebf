# Developer Guide: Responsive Tables in TallyCRM

## Quick Start

### Using ResponsiveTable Component

```jsx
import ResponsiveTable from '../../components/ui/ResponsiveTable';

const MyTable = ({ data }) => {
  const columns = [
    {
      header: 'Name',
      key: 'name',
      width: '25%',
      priority: 'high',
      render: (item) => (
        <div className="text-sm font-medium text-gray-900 truncate" title={item.name}>
          {item.name}
        </div>
      )
    },
    {
      header: 'Description',
      key: 'description',
      width: '30%',
      priority: 'medium',
      hideOnSmallLaptop: true,
      render: (item) => (
        <div className="text-sm text-gray-900 truncate" title={item.description}>
          {item.description}
        </div>
      )
    },
    {
      header: 'Actions',
      key: 'actions',
      width: '15%',
      priority: 'high',
      truncate: false,
      render: (item) => (
        <div className="flex space-x-2">
          <button onClick={() => handleEdit(item)}>Edit</button>
          <button onClick={() => handleDelete(item.id)}>Delete</button>
        </div>
      )
    }
  ];

  return (
    <ResponsiveTable
      columns={columns}
      data={data}
      laptopOptimized={true}
      compactMode={true}
      showTooltips={true}
      emptyMessage="No data found"
    />
  );
};
```

## Column Configuration

### Required Properties
```jsx
{
  header: 'Column Title',    // Display name
  key: 'dataKey',           // Data property key
  render: (item) => <div/>  // Render function
}
```

### Responsive Properties
```jsx
{
  width: '25%',              // Fixed width percentage
  priority: 'high',          // 'high', 'medium', 'low'
  hideOnMobile: true,        // Hide on mobile screens
  hideOnTablet: true,        // Hide on tablet screens  
  hideOnLaptop: true,        // Hide on laptop screens (≤1440px)
  hideOnSmallLaptop: true,   // Hide on small laptops (≤1366px)
  showDesktopOnly: true,     // Show only on large desktops (≥1600px)
  truncate: false,           // Disable text truncation (default: true)
  maxWidth: '200px'          // Maximum column width
}
```

### Priority System
- **high**: Always visible (essential data)
- **medium**: Hidden on small laptops (≤1366px)
- **low**: Hidden on laptops (≤1440px)

## CSS Classes

### Responsive Column Hiding
```css
.col-hide-laptop          /* Hide on laptops (≤1440px) */
.col-hide-small-laptop    /* Hide on small laptops (≤1366px) */
.col-show-desktop-only    /* Show only on large desktops (≥1600px) */
```

### Text Truncation
```css
.text-truncate-responsive /* Responsive text truncation */
.truncate                 /* Standard Tailwind truncation */
```

### Compact Tables
```css
.table-compact           /* Compact table for laptops */
.table-laptop-padding    /* Reduced padding for laptops */
.table-laptop-text       /* Smaller text for laptops */
```

## Utility Functions

### Import Table Utils
```jsx
import {
  getResponsiveColumns,
  calculateColumnWidths,
  getOptimalTableConfig,
  truncateText,
  isLaptopScreen
} from '../../utils/tableUtils';
```

### Get Responsive Columns
```jsx
const visibleColumns = getResponsiveColumns(columns, window.innerWidth);
```

### Calculate Optimal Config
```jsx
const config = getOptimalTableConfig(columns, {
  enableLaptopOptimization: true,
  enableCompactMode: true,
  enableTooltips: true
});
```

## Best Practices

### 1. Column Design
- **Essential First**: Place most important columns first
- **Progressive Enhancement**: Use priority system for optional columns
- **Meaningful Tooltips**: Always provide tooltips for truncated content

### 2. Width Management
```jsx
// Good: Percentage-based widths
{ width: '25%', priority: 'high' }

// Good: Flexible columns for remaining space
{ priority: 'medium' } // Auto-calculated width

// Avoid: Fixed pixel widths
{ width: '200px' } // Not responsive
```

### 3. Content Rendering
```jsx
// Good: Truncation with tooltip
render: (item) => (
  <div className="text-sm text-gray-900 truncate" title={item.longText}>
    {item.longText}
  </div>
)

// Good: Multi-line content
render: (item) => (
  <div className="min-w-0">
    <div className="text-sm font-medium text-gray-900 truncate" title={item.name}>
      {item.name}
    </div>
    <div className="text-xs text-gray-500 truncate" title={item.subtitle}>
      {item.subtitle}
    </div>
  </div>
)
```

### 4. Action Buttons
```jsx
// Good: Compact action buttons
render: (item) => (
  <div className="flex space-x-1">
    <button className="p-1 rounded hover:bg-gray-50" title="View">
      <FaEye className="h-3 w-3" />
    </button>
    <button className="p-1 rounded hover:bg-gray-50" title="Edit">
      <FaEdit className="h-3 w-3" />
    </button>
  </div>
)
```

## Common Patterns

### 1. Name + Subtitle Pattern
```jsx
{
  header: 'Customer',
  key: 'customer',
  width: '25%',
  priority: 'high',
  render: (item) => (
    <div className="min-w-0">
      <div className="text-sm font-bold text-gray-900 truncate" title={item.name}>
        {item.name}
      </div>
      <div className="text-xs text-gray-500 truncate" title={item.email}>
        {item.email}
      </div>
    </div>
  )
}
```

### 2. Status Badge Pattern
```jsx
{
  header: 'Status',
  key: 'status',
  width: '12%',
  priority: 'high',
  render: (item) => (
    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
      item.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
    }`}>
      {item.isActive ? 'Active' : 'Inactive'}
    </span>
  )
}
```

### 3. Currency Pattern
```jsx
{
  header: 'Amount',
  key: 'amount',
  width: '12%',
  priority: 'low',
  hideOnLaptop: true,
  render: (item) => (
    <div className="text-sm font-medium text-gray-900">
      ₹{item.amount?.toLocaleString() || '0'}
    </div>
  )
}
```

## Troubleshooting

### Common Issues

#### 1. Horizontal Scrolling Still Appears
```jsx
// Check total width percentages
const totalWidth = columns.reduce((sum, col) => 
  sum + (col.width ? parseFloat(col.width) : 0), 0
);
// Should be ≤ 100%

// Solution: Reduce column widths or add responsive hiding
```

#### 2. Content Not Truncating
```jsx
// Ensure parent has min-width: 0
<div className="min-w-0">
  <div className="truncate" title={fullText}>
    {fullText}
  </div>
</div>
```

#### 3. Actions Not Visible
```jsx
// Ensure actions column has sufficient width and high priority
{
  header: 'Actions',
  width: '15%',        // Sufficient width
  priority: 'high',    // Always visible
  truncate: false      // Don't truncate buttons
}
```

## Migration Checklist

When converting existing tables:

- [ ] Replace `<table>` with `<ResponsiveTable>`
- [ ] Define column configuration with responsive properties
- [ ] Add laptop optimization props
- [ ] Test on target screen sizes (1366x768, 1920x1080)
- [ ] Verify all functionality works
- [ ] Add tooltips for truncated content
- [ ] Update any custom CSS if needed

## Performance Tips

1. **Memoize Column Definitions**: Use `useMemo` for column arrays
2. **Optimize Render Functions**: Avoid inline object creation
3. **Lazy Load Data**: Implement pagination for large datasets
4. **Virtual Scrolling**: Consider for tables with 1000+ rows

## Testing

Always test your responsive tables on:
- Chrome DevTools device simulation
- Actual laptop devices when possible
- Different data volumes (empty, few, many records)
- All CRUD operations and interactions
