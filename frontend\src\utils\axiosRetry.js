/**
 * Axios retry utility for production environments
 * Handles network failures, timeouts, and server errors with exponential backoff
 */

import axios from 'axios';

/**
 * Determines if an error should trigger a retry
 * @param {Object} error - Axios error object
 * @returns {boolean} - Whether to retry the request
 */
export const isRetryableError = (error) => {
  // Network errors (no response received)
  if (!error.response) {
    return true;
  }

  // Server errors (5xx)
  if (error.response.status >= 500) {
    return true;
  }

  // Rate limiting (429)
  if (error.response.status === 429) {
    return true;
  }

  // Timeout errors
  if (error.code === 'ECONNABORTED') {
    return true;
  }

  return false;
};

/**
 * Calculate delay for exponential backoff
 * @param {number} retryCount - Current retry attempt
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {number} - Delay in milliseconds
 */
export const calculateRetryDelay = (retryCount, baseDelay = 1000) => {
  const delay = baseDelay * Math.pow(2, retryCount);
  const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
  return Math.min(delay + jitter, 30000); // Cap at 30 seconds
};

/**
 * Axios retry interceptor
 * @param {Object} axiosInstance - Axios instance to add retry logic to
 * @param {Object} options - Retry configuration options
 */
export const addRetryInterceptor = (axiosInstance, options = {}) => {
  const {
    retries = 3,
    retryDelay = 1000,
    retryCondition = isRetryableError,
    onRetry = null
  } = options;

  axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const config = error.config;

      // Initialize retry count if not present
      if (!config.__retryCount) {
        config.__retryCount = 0;
      }

      // Check if we should retry
      const shouldRetry = config.__retryCount < retries && retryCondition(error);

      if (!shouldRetry) {
        return Promise.reject(error);
      }

      // Increment retry count
      config.__retryCount += 1;

      // Calculate delay
      const delay = calculateRetryDelay(config.__retryCount, retryDelay);

      // Call onRetry callback if provided
      if (onRetry) {
        onRetry(config.__retryCount, error, delay);
      }

      // Log retry attempt in production
      if (process.env.NODE_ENV === 'production') {
        console.warn(`Retrying request (${config.__retryCount}/${retries}):`, {
          url: config.url,
          method: config.method,
          error: error.message,
          delay: `${delay}ms`
        });
      }

      // Wait for the calculated delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Retry the request
      return axiosInstance(config);
    }
  );
};

/**
 * Production-ready axios configuration
 * @param {Object} baseConfig - Base axios configuration
 * @returns {Object} - Configured axios instance
 */
export const createProductionAxios = (baseConfig = {}) => {
  const instance = axios.create({
    timeout: 60000, // 60 seconds timeout for production
    ...baseConfig,
    headers: {
      'Content-Type': 'application/json',
      ...baseConfig.headers
    }
  });

  // Add retry logic
  addRetryInterceptor(instance, {
    retries: 3,
    retryDelay: 1000,
    onRetry: (retryCount, error, delay) => {
      // Custom retry logging or metrics can be added here
      if (process.env.NODE_ENV === 'production') {
        console.warn(`API Retry ${retryCount}: ${error.message} (waiting ${delay}ms)`);
      }
    }
  });

  return instance;
};

/**
 * Health check utility for API endpoints
 * @param {string} baseURL - Base URL to check
 * @returns {Promise<boolean>} - Whether the API is healthy
 */
export const checkApiHealth = async (baseURL) => {
  try {
    const response = await axios.get(`${baseURL}/health`, {
      timeout: 5000,
      validateStatus: (status) => status === 200
    });
    return response.status === 200;
  } catch (error) {
    console.error('API health check failed:', error.message);
    return false;
  }
};

/**
 * Network connectivity check
 * @returns {Promise<boolean>} - Whether network is available
 */
export const checkNetworkConnectivity = async () => {
  try {
    // Try to fetch a small resource
    await fetch('/favicon.ico', {
      method: 'HEAD',
      cache: 'no-cache',
      signal: AbortSignal.timeout(3000)
    });
    return true;
  } catch (error) {
    console.error('Network connectivity check failed:', error.message);
    return false;
  }
};

export default {
  isRetryableError,
  calculateRetryDelay,
  addRetryInterceptor,
  createProductionAxios,
  checkApiHealth,
  checkNetworkConnectivity
};
