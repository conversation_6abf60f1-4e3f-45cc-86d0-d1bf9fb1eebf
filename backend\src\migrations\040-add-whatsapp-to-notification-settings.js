import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    console.log('🔄 Adding WhatsApp support to notification_settings...');

    // Check if the column already exists
    const tableDescription = await queryInterface.describeTable('notification_settings');
    
    if (!tableDescription.whatsapp_enabled) {
      // Add whatsapp_enabled column
      await queryInterface.addColumn('notification_settings', 'whatsapp_enabled', {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: 'Enable WhatsApp notifications',
      }, { transaction });

      console.log('✅ Added whatsapp_enabled column to notification_settings');

      // Update existing records to enable WhatsApp by default
      await queryInterface.sequelize.query(
        'UPDATE notification_settings SET whatsapp_enabled = true WHERE whatsapp_enabled IS NULL',
        { transaction }
      );

      console.log('✅ Updated existing notification settings to enable WhatsApp by default');
    } else {
      console.log('✅ whatsapp_enabled column already exists in notification_settings');
    }

    await transaction.commit();
    console.log('✅ Successfully added WhatsApp support to notification settings');
  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error adding WhatsApp support to notification settings:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    console.log('🔄 Removing WhatsApp support from notification_settings...');

    // Check if the column exists before trying to remove it
    const tableDescription = await queryInterface.describeTable('notification_settings');
    
    if (tableDescription.whatsapp_enabled) {
      await queryInterface.removeColumn('notification_settings', 'whatsapp_enabled', { transaction });
      console.log('✅ Removed whatsapp_enabled column from notification_settings');
    } else {
      console.log('✅ whatsapp_enabled column does not exist in notification_settings');
    }

    await transaction.commit();
    console.log('✅ Successfully removed WhatsApp support from notification settings');
  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error removing WhatsApp support from notification settings:', error);
    throw error;
  }
};
