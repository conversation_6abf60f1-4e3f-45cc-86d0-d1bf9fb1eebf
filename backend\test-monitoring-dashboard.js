import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';
import { Op } from 'sequelize';

/**
 * Test script to implement and test system monitoring dashboard
 */
async function testMonitoringDashboard() {
  try {
    console.log('📊 Starting system monitoring dashboard testing...');

    // Get the first active tenant for testing
    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    if (!tenant) {
      console.error('❌ No active tenant found. Please create a tenant first.');
      return;
    }

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);

    // Test 1: Renewal notification statistics
    console.log('\n📈 Testing renewal notification statistics...');
    
    const stats = await models.NotificationSchedule.findAll({
      where: { tenant_id: tenant.id },
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    console.log('   📊 Notification status breakdown:');
    stats.forEach(stat => {
      console.log(`     - ${stat.status}: ${stat.count} notifications`);
    });

    // Test 2: Pending notifications
    const pendingNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'scheduled',
        notify_at: {
          [Op.lte]: new Date(),
        },
      },
    });

    console.log(`   📅 Pending notifications (due today): ${pendingNotifications}`);

    // Test 3: Upcoming renewals (next 7 days)
    const upcomingRenewals = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'scheduled',
        days_before_expiry: {
          [Op.lte]: 7,
        },
      },
    });

    console.log(`   ⏰ Upcoming renewals (next 7 days): ${upcomingRenewals}`);

    // Test 4: Failed notifications requiring attention
    const failedNotifications = await models.NotificationSchedule.findAll({
      where: {
        tenant_id: tenant.id,
        status: 'failed',
        retry_count: {
          [Op.gte]: 2, // Failed multiple times
        },
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'email'],
        },
      ],
      order: [['updated_at', 'DESC']],
      limit: 10,
    });

    console.log(`   ❌ Failed notifications requiring attention: ${failedNotifications.length}`);
    
    if (failedNotifications.length > 0) {
      console.log('   📋 Recent failed notifications:');
      failedNotifications.forEach((notification, index) => {
        console.log(`     ${index + 1}. ${notification.customer?.company_name || 'Unknown'} - ${notification.renewal_type} (${notification.retry_count} retries)`);
      });
    }

    // Test 5: Success rate calculation
    const totalNotifications = await models.NotificationSchedule.count({
      where: { tenant_id: tenant.id },
    });

    const successfulNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'sent',
      },
    });

    const successRate = totalNotifications > 0 ? ((successfulNotifications / totalNotifications) * 100).toFixed(2) : 0;
    console.log(`   📈 Overall success rate: ${successRate}% (${successfulNotifications}/${totalNotifications})`);

    return {
      success: true,
      tenant: tenant,
      stats: {
        statusBreakdown: stats,
        pendingNotifications,
        upcomingRenewals,
        failedNotifications: failedNotifications.length,
        successRate: parseFloat(successRate),
        totalNotifications,
        successfulNotifications,
      },
    };

  } catch (error) {
    console.error('❌ Error testing monitoring dashboard:', error);
    throw error;
  }
}

/**
 * Test scheduled job status monitoring
 */
async function testJobStatusMonitoring() {
  try {
    console.log('\n⚙️ Testing scheduled job status monitoring...');

    // Import and test scheduled job service
    const scheduledJobService = (await import('./src/services/ScheduledJobService.js')).default;
    
    // Test job status retrieval
    const jobStatus = scheduledJobService.getJobStatus();
    
    console.log('   📋 Scheduled job status:');
    Object.entries(jobStatus).forEach(([jobName, status]) => {
      const statusIcon = status.running ? '🟢' : '🔴';
      const scheduledIcon = status.scheduled ? '✅' : '❌';
      console.log(`     ${statusIcon} ${jobName}:`);
      console.log(`       - Running: ${status.running}`);
      console.log(`       - Scheduled: ${status.scheduled}`);
      console.log(`       - Destroyed: ${status.destroyed}`);
    });

    // Test job health check
    const healthyJobs = Object.values(jobStatus).filter(status => status.running && status.scheduled && !status.destroyed);
    const totalJobs = Object.keys(jobStatus).length;
    const jobHealthPercentage = totalJobs > 0 ? ((healthyJobs.length / totalJobs) * 100).toFixed(2) : 0;

    console.log(`   📊 Job health: ${jobHealthPercentage}% (${healthyJobs.length}/${totalJobs} healthy)`);

    return {
      success: true,
      jobStatus,
      healthyJobs: healthyJobs.length,
      totalJobs,
      jobHealthPercentage: parseFloat(jobHealthPercentage),
    };

  } catch (error) {
    console.error('❌ Error testing job status monitoring:', error);
    throw error;
  }
}

/**
 * Test performance metrics tracking
 */
async function testPerformanceMetrics() {
  try {
    console.log('\n📊 Testing performance metrics tracking...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    // Test 1: Notification processing time analysis
    const recentNotifications = await models.NotificationSchedule.findAll({
      where: {
        tenant_id: tenant.id,
        status: 'sent',
        sent_at: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
        },
      },
      attributes: ['id', 'created_at', 'sent_at', 'retry_count'],
      order: [['sent_at', 'DESC']],
      limit: 100,
    });

    console.log(`   📈 Recent notifications analyzed: ${recentNotifications.length}`);

    if (recentNotifications.length > 0) {
      // Calculate average processing time
      const processingTimes = recentNotifications.map(notification => {
        const created = new Date(notification.created_at);
        const sent = new Date(notification.sent_at);
        return (sent - created) / (1000 * 60 * 60); // Hours
      });

      const avgProcessingTime = (processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length).toFixed(2);
      const maxProcessingTime = Math.max(...processingTimes).toFixed(2);
      const minProcessingTime = Math.min(...processingTimes).toFixed(2);

      console.log(`   ⏱️  Average processing time: ${avgProcessingTime} hours`);
      console.log(`   ⏱️  Max processing time: ${maxProcessingTime} hours`);
      console.log(`   ⏱️  Min processing time: ${minProcessingTime} hours`);

      // Retry analysis
      const retriedNotifications = recentNotifications.filter(n => n.retry_count > 0);
      const retryRate = ((retriedNotifications.length / recentNotifications.length) * 100).toFixed(2);
      
      console.log(`   🔄 Retry rate: ${retryRate}% (${retriedNotifications.length}/${recentNotifications.length})`);
    }

    // Test 2: Database performance check
    const dbStartTime = Date.now();
    await models.NotificationSchedule.count({ where: { tenant_id: tenant.id } });
    const dbQueryTime = Date.now() - dbStartTime;

    console.log(`   🗄️  Database query performance: ${dbQueryTime}ms`);

    // Test 3: System resource usage (basic check)
    const memoryUsage = process.memoryUsage();
    console.log(`   💾 Memory usage:`);
    console.log(`     - RSS: ${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     - Heap Used: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log(`     - Heap Total: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`);

    return {
      success: true,
      metrics: {
        recentNotifications: recentNotifications.length,
        avgProcessingTime: recentNotifications.length > 0 ? parseFloat(avgProcessingTime) : 0,
        retryRate: recentNotifications.length > 0 ? parseFloat(retryRate) : 0,
        dbQueryTime,
        memoryUsage: {
          rss: parseFloat((memoryUsage.rss / 1024 / 1024).toFixed(2)),
          heapUsed: parseFloat((memoryUsage.heapUsed / 1024 / 1024).toFixed(2)),
          heapTotal: parseFloat((memoryUsage.heapTotal / 1024 / 1024).toFixed(2)),
        },
      },
    };

  } catch (error) {
    console.error('❌ Error testing performance metrics:', error);
    throw error;
  }
}

/**
 * Test alert system for failed notifications
 */
async function testAlertSystem() {
  try {
    console.log('\n🚨 Testing alert system for failed notifications...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    // Check for critical alerts
    const criticalAlerts = [];

    // Alert 1: High failure rate
    const totalNotifications = await models.NotificationSchedule.count({
      where: { tenant_id: tenant.id },
    });

    const failedNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'failed',
      },
    });

    const failureRate = totalNotifications > 0 ? (failedNotifications / totalNotifications) * 100 : 0;
    
    if (failureRate > 10) { // Alert if failure rate > 10%
      criticalAlerts.push({
        type: 'HIGH_FAILURE_RATE',
        severity: 'HIGH',
        message: `High notification failure rate: ${failureRate.toFixed(2)}%`,
        value: failureRate,
        threshold: 10,
      });
    }

    // Alert 2: Notifications stuck in retry
    const stuckNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'failed',
        retry_count: {
          [Op.gte]: 3,
        },
      },
    });

    if (stuckNotifications > 0) {
      criticalAlerts.push({
        type: 'STUCK_NOTIFICATIONS',
        severity: 'MEDIUM',
        message: `${stuckNotifications} notifications exceeded retry limit`,
        value: stuckNotifications,
        threshold: 0,
      });
    }

    // Alert 3: Old pending notifications
    const oldPendingNotifications = await models.NotificationSchedule.count({
      where: {
        tenant_id: tenant.id,
        status: 'scheduled',
        notify_at: {
          [Op.lt]: new Date(Date.now() - 24 * 60 * 60 * 1000), // Older than 1 day
        },
      },
    });

    if (oldPendingNotifications > 0) {
      criticalAlerts.push({
        type: 'OLD_PENDING_NOTIFICATIONS',
        severity: 'MEDIUM',
        message: `${oldPendingNotifications} notifications pending for more than 24 hours`,
        value: oldPendingNotifications,
        threshold: 0,
      });
    }

    console.log(`   🚨 Critical alerts found: ${criticalAlerts.length}`);
    
    if (criticalAlerts.length > 0) {
      console.log('   📋 Alert details:');
      criticalAlerts.forEach((alert, index) => {
        const severityIcon = alert.severity === 'HIGH' ? '🔴' : '🟡';
        console.log(`     ${severityIcon} ${index + 1}. ${alert.type}: ${alert.message}`);
      });
    } else {
      console.log('   ✅ No critical alerts - system is healthy');
    }

    return {
      success: true,
      alerts: criticalAlerts,
      alertCount: criticalAlerts.length,
      systemHealth: criticalAlerts.length === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION',
    };

  } catch (error) {
    console.error('❌ Error testing alert system:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting TallyCRM System Monitoring Dashboard Testing\n');

    // Test 1: Monitoring dashboard
    const dashboardResult = await testMonitoringDashboard();
    
    // Test 2: Job status monitoring
    const jobResult = await testJobStatusMonitoring();
    
    // Test 3: Performance metrics
    const performanceResult = await testPerformanceMetrics();
    
    // Test 4: Alert system
    const alertResult = await testAlertSystem();

    console.log('\n🎉 All monitoring tests completed successfully!');
    console.log('\n📋 Monitoring Dashboard Summary:');
    console.log(`  ✅ Tenant: ${dashboardResult.tenant.name}`);
    console.log(`  📊 Total notifications: ${dashboardResult.stats.totalNotifications}`);
    console.log(`  📈 Success rate: ${dashboardResult.stats.successRate}%`);
    console.log(`  📅 Pending notifications: ${dashboardResult.stats.pendingNotifications}`);
    console.log(`  ⏰ Upcoming renewals: ${dashboardResult.stats.upcomingRenewals}`);
    console.log(`  ❌ Failed notifications: ${dashboardResult.stats.failedNotifications}`);
    console.log(`  ⚙️  Job health: ${jobResult.jobHealthPercentage}%`);
    console.log(`  🚨 System alerts: ${alertResult.alertCount}`);
    console.log(`  🏥 System health: ${alertResult.systemHealth}`);

    process.exit(0);

  } catch (error) {
    console.error('\n❌ Monitoring dashboard testing failed:', error);
    process.exit(1);
  }
}

// Run the tests
main();
