/**
 * Pie Chart Component
 * Responsive pie chart using Recharts for categorical data distribution
 */

import React from 'react';
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { CHART_COLORS, CHART_THEMES } from './chartThemes';
import { formatNumber } from './chartUtils';

const PieChart = ({
  data = [],
  dataKey = 'value',
  nameKey = 'name',
  height = 300,
  showLegend = true,
  showTooltip = true,
  showLabels = false,
  showPercentages = true,
  colorScheme = 'primary', // 'primary', 'status', 'priority'
  innerRadius = 0, // Set > 0 for donut chart
  outerRadius = null, // Auto-calculated if null
  theme = 'default',
  className = '',
  ...props
}) => {
  // Theme configuration
  const currentTheme = CHART_THEMES[theme] || CHART_THEMES.default;

  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + (Number(item[dataKey]) || 0), 0);

  // Get colors based on scheme
  const getSliceColor = (index, item) => {
    if (colorScheme === 'status' && item.status) {
      return CHART_COLORS.status[item.status.toLowerCase()] || CHART_COLORS.primary[index];
    }
    if (colorScheme === 'priority' && item.priority) {
      return CHART_COLORS.priority[item.priority.toLowerCase()] || CHART_COLORS.primary[index];
    }
    return CHART_COLORS.primary[index % CHART_COLORS.primary.length];
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload }) => {
    if (!active || !payload || payload.length === 0) return null;

    const data = payload[0].payload;
    const value = payload[0].value;
    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <div className="flex items-center space-x-2 mb-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: payload[0].color }}
          />
          <span className="font-medium text-gray-900">{data[nameKey]}</span>
        </div>
        <div className="space-y-1">
          <div className="flex justify-between space-x-4">
            <span className="text-sm text-gray-600">Value:</span>
            <span className="text-sm font-medium text-gray-900">
              {formatNumber(value)}
            </span>
          </div>
          <div className="flex justify-between space-x-4">
            <span className="text-sm text-gray-600">Percentage:</span>
            <span className="text-sm font-medium text-gray-900">
              {percentage}%
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Custom label component
  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }) => {
    if (!showLabels) return null;
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent < 0.05) return null; // Don't show labels for slices < 5%

    return (
      <text 
        x={x} 
        y={y} 
        fill={currentTheme.textColor}
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={currentTheme.fontSize}
        className="font-medium"
      >
        {showPercentages ? `${(percent * 100).toFixed(0)}%` : name}
      </text>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 text-sm">No data available for chart</p>
      </div>
    );
  }

  return (
    <div className={className} {...props}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={showLabels ? CustomLabel : false}
            outerRadius={outerRadius || Math.min(height * 0.35, 120)}
            innerRadius={innerRadius}
            fill="#8884d8"
            dataKey={dataKey}
            nameKey={nameKey}
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={getSliceColor(index, entry)}
              />
            ))}
          </Pie>
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {showLegend && (
            <Legend 
              wrapperStyle={{ 
                fontSize: currentTheme.fontSize,
                color: currentTheme.textColor 
              }}
              formatter={(value, entry) => (
                <span style={{ color: currentTheme.textColor }}>
                  {value}
                </span>
              )}
            />
          )}
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PieChart;
