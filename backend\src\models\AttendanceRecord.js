import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const AttendanceRecord = sequelize.define('AttendanceRecord', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Reference to executive/employee',
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Attendance date',
    },
    check_in_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Check-in timestamp with timezone',
    },
    check_out_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Check-out timestamp with timezone',
    },
    status: {
      type: DataTypes.ENUM('present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'),
      allowNull: false,
      defaultValue: 'present',
      comment: 'Attendance status for the day',
    },
    location_check_in: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'GPS coordinates and address for check-in',
    },
    location_check_out: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'GPS coordinates and address for check-out',
    },
    total_hours: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Total working hours for the day',
    },
    break_time_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Total break time in minutes',
    },
    overtime_hours: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Overtime hours worked',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes or remarks',
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who approved manual entry or changes',
    },
    is_manual_entry: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is a manual entry by admin/manager',
    },
    is_holiday: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this date is a holiday',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'attendance_records',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['employee_id'],
      },
      {
        fields: ['date'],
      },
      {
        fields: ['tenant_id', 'employee_id', 'date'],
        unique: true,
        name: 'unique_employee_date_attendance',
      },
      {
        fields: ['status'],
      },
      {
        fields: ['is_manual_entry'],
      },
      {
        fields: ['created_at'],
      },
    ],
  });

  // Instance methods
  AttendanceRecord.prototype.calculateTotalHours = function() {
    if (this.check_in_time && this.check_out_time) {
      const checkIn = new Date(this.check_in_time);
      const checkOut = new Date(this.check_out_time);
      const diffMs = checkOut - checkIn;
      const diffHours = diffMs / (1000 * 60 * 60);
      const breakHours = this.break_time_minutes / 60;
      return Math.max(0, diffHours - breakHours);
    }
    return 0;
  };

  AttendanceRecord.prototype.isLateArrival = function(shiftStartTime = '09:00:00') {
    if (!this.check_in_time) return false;
    
    const checkInTime = new Date(this.check_in_time);
    const shiftStart = new Date(this.date + 'T' + shiftStartTime);
    
    return checkInTime > shiftStart;
  };

  AttendanceRecord.prototype.isEarlyDeparture = function(shiftEndTime = '18:00:00') {
    if (!this.check_out_time) return false;
    
    const checkOutTime = new Date(this.check_out_time);
    const shiftEnd = new Date(this.date + 'T' + shiftEndTime);
    
    return checkOutTime < shiftEnd;
  };

  // Class methods
  AttendanceRecord.getStatusOptions = function() {
    return [
      { value: 'present', label: 'Present' },
      { value: 'absent', label: 'Absent' },
      { value: 'late', label: 'Late' },
      { value: 'half_day', label: 'Half Day' },
      { value: 'work_from_home', label: 'Work From Home' },
      { value: 'on_leave', label: 'On Leave' },
    ];
  };

  // Associations
  AttendanceRecord.associate = function(models) {
    AttendanceRecord.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    AttendanceRecord.belongsTo(models.Executive, {
      foreignKey: 'employee_id',
      as: 'employee',
    });

    AttendanceRecord.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver',
    });
  };

  return AttendanceRecord;
}
