import React, { useState, useEffect } from 'react';
import { FaTimes, FaPhone, FaEnvelope, FaUsers, FaWhatsapp, FaEllipsisH, FaPlus, FaCalendar, FaClock } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { leadAPI, executiveAPI } from '../../services/api';

const ContactHistoryModal = ({ isOpen, onClose, leadId, onContactAdded }) => {
  const [contactHistory, setContactHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    contact_type: 'phone',
    contact_date: new Date().toISOString().slice(0, 16), // datetime-local format
    duration_minutes: '',
    outcome: '',
    notes: '',
    next_follow_up: '',
    executive_id: '',
  });
  const [executives, setExecutives] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [submitError, setSubmitError] = useState('');

  useEffect(() => {
    if (isOpen && leadId) {
      fetchContactHistory();
      fetchExecutives();
    }
  }, [isOpen, leadId]);

  const fetchContactHistory = async () => {
    try {
      setLoading(true);
      const response = await leadAPI.getContactHistory(leadId);
      if (response.data?.success) {
        setContactHistory(response.data.data.contactHistory);
      }
    } catch (error) {
      console.error('Error fetching contact history:', error);
      toast.error('Failed to load contact history');
    } finally {
      setLoading(false);
    }
  };

  const fetchExecutives = async () => {
    try {
      const response = await executiveAPI.getActiveExecutives();
      if (response.data?.success) {
        setExecutives(response.data.data.executives || []);
      }
    } catch (error) {
      console.error('Error fetching executives:', error);
      // Don't show error toast for executives fetch failure as it's not critical
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear field-specific error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear general submit error
    if (submitError) {
      setSubmitError('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setFormErrors({});
    setSubmitError('');

    try {
      setLoading(true);

      // Clean up form data - remove empty fields
      const cleanedFormData = { ...formData };

      // Remove empty optional fields
      if (!cleanedFormData.executive_id || cleanedFormData.executive_id === '') {
        delete cleanedFormData.executive_id;
      }
      if (!cleanedFormData.duration_minutes || cleanedFormData.duration_minutes === '') {
        delete cleanedFormData.duration_minutes;
      }
      if (!cleanedFormData.outcome || cleanedFormData.outcome === '') {
        delete cleanedFormData.outcome;
      }
      if (!cleanedFormData.notes || cleanedFormData.notes === '') {
        delete cleanedFormData.notes;
      }
      if (!cleanedFormData.next_follow_up || cleanedFormData.next_follow_up === '') {
        delete cleanedFormData.next_follow_up;
      }

      const response = await leadAPI.addContactHistory(leadId, cleanedFormData);

      if (response.data?.success) {
        toast.success('Contact history added successfully');
        setFormData({
          contact_type: 'phone',
          contact_date: new Date().toISOString().slice(0, 16),
          duration_minutes: '',
          outcome: '',
          notes: '',
          next_follow_up: '',
          executive_id: '',
        });
        setShowAddForm(false);
        fetchContactHistory();
        if (onContactAdded) onContactAdded();
      } else {
        // Handle API response with success: false
        const errorMessage = response.data?.message || 'Failed to add contact history';
        const errors = response.data?.errors || {};

        if (Object.keys(errors).length > 0) {
          setFormErrors(errors);
          toast.error('Please fix the validation errors below');
        } else {
          setSubmitError(errorMessage);
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('Error adding contact history:', error);

      // Handle different types of errors
      if (error.response) {
        // Server responded with error status
        const status = error.response.status;
        const errorData = error.response.data;

        if (status === 400 && errorData?.errors) {
          // Validation errors
          setFormErrors(errorData.errors);
          toast.error('Please fix the validation errors below');
        } else if (status === 404) {
          setSubmitError('Lead not found. Please refresh the page and try again.');
          toast.error('Lead not found. Please refresh the page and try again.');
        } else if (status === 403) {
          setSubmitError('You do not have permission to add contact history.');
          toast.error('You do not have permission to add contact history.');
        } else if (status === 401) {
          setSubmitError('Your session has expired. Please log in again.');
          toast.error('Your session has expired. Please log in again.');
        } else {
          // Other server errors
          const message = errorData?.message || `Server error (${status}). Please try again.`;
          setSubmitError(message);
          toast.error(message);
        }
      } else if (error.request) {
        // Network error
        setSubmitError('Network error. Please check your connection and try again.');
        toast.error('Network error. Please check your connection and try again.');
      } else {
        // Other errors
        setSubmitError('An unexpected error occurred. Please try again.');
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const getContactTypeIcon = (type) => {
    switch (type) {
      case 'phone': return <FaPhone className="h-4 w-4" />;
      case 'email': return <FaEnvelope className="h-4 w-4" />;
      case 'meeting': return <FaUsers className="h-4 w-4" />;
      case 'whatsapp': return <FaWhatsapp className="h-4 w-4" />;
      default: return <FaEllipsisH className="h-4 w-4" />;
    }
  };

  const renderFieldError = (fieldName) => {
    if (formErrors[fieldName]) {
      return (
        <p className="mt-1 text-sm text-red-600">
          {formErrors[fieldName]}
        </p>
      );
    }
    return null;
  };

  const getOutcomeBadge = (outcome) => {
    const colors = {
      interested: 'bg-green-100 text-green-800',
      not_interested: 'bg-red-100 text-red-800',
      callback_requested: 'bg-yellow-100 text-yellow-800',
      meeting_scheduled: 'bg-blue-100 text-blue-800',
      converted: 'bg-purple-100 text-purple-800',
      no_response: 'bg-gray-100 text-gray-800',
      other: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[outcome] || colors.other}`}>
        {outcome?.replace('_', ' ').toUpperCase() || 'N/A'}
      </span>
    );
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-modal bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Contact History</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAddForm(!showAddForm)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700"
            >
              <FaPlus className="mr-2 h-4 w-4" />
              Add Contact
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
            >
              <FaTimes className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Add Contact Form */}
          {showAddForm && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Contact</h3>

              {/* General Error Message */}
              {submitError && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{submitError}</p>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Type *
                    </label>
                    <select
                      name="contact_type"
                      value={formData.contact_type}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.contact_type ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="phone">Phone Call</option>
                      <option value="email">Email</option>
                      <option value="meeting">Meeting</option>
                      <option value="whatsapp">WhatsApp</option>
                      <option value="other">Other</option>
                    </select>
                    {renderFieldError('contact_type')}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Date & Time *
                    </label>
                    <input
                      type="datetime-local"
                      name="contact_date"
                      value={formData.contact_date}
                      onChange={handleInputChange}
                      required
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.contact_date ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {renderFieldError('contact_date')}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (minutes)
                    </label>
                    <input
                      type="number"
                      name="duration_minutes"
                      value={formData.duration_minutes}
                      onChange={handleInputChange}
                      min="0"
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.duration_minutes ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {renderFieldError('duration_minutes')}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Outcome
                    </label>
                    <select
                      name="outcome"
                      value={formData.outcome}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.outcome ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select outcome...</option>
                      <option value="interested">Interested</option>
                      <option value="not_interested">Not Interested</option>
                      <option value="callback_requested">Callback Requested</option>
                      <option value="meeting_scheduled">Meeting Scheduled</option>
                      <option value="converted">Converted</option>
                      <option value="no_response">No Response</option>
                      <option value="other">Other</option>
                    </select>
                    {renderFieldError('outcome')}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Executive (Optional)
                    </label>
                    <select
                      name="executive_id"
                      value={formData.executive_id}
                      onChange={handleInputChange}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.executive_id ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select executive (optional)...</option>
                      {executives.map((executive) => (
                        <option key={executive.id} value={executive.id}>
                          {executive.first_name} {executive.last_name}
                          {executive.employee_code && ` (${executive.employee_code})`}
                        </option>
                      ))}
                    </select>
                    {renderFieldError('executive_id')}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Next Follow-up Date
                    </label>
                    <input
                      type="date"
                      name="next_follow_up"
                      value={formData.next_follow_up}
                      onChange={handleInputChange}
                      min={new Date().toISOString().split('T')[0]}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        formErrors.next_follow_up ? 'border-red-300 bg-red-50' : 'border-gray-300'
                      }`}
                    />
                    {renderFieldError('next_follow_up')}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      formErrors.notes ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="Add any notes about this contact..."
                  />
                  {renderFieldError('notes')}
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Adding...' : 'Add Contact'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Contact History List */}
          {loading && !showAddForm ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : contactHistory.length === 0 ? (
            <div className="text-center py-8">
              <FaPhone className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No contact history</h3>
              <p className="text-gray-500">Start by adding your first contact with this lead.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {contactHistory.map((contact) => (
                <div key={contact.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600">
                          {getContactTypeIcon(contact.contact_type)}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="text-sm font-medium text-gray-900 capitalize">
                            {contact.contact_type.replace('_', ' ')}
                          </h4>
                          {contact.outcome && getOutcomeBadge(contact.outcome)}
                        </div>
                        <div className="flex items-center text-sm text-gray-500 space-x-4 mb-2">
                          <span className="flex items-center">
                            <FaCalendar className="mr-1 h-3 w-3" />
                            {formatDateTime(contact.contact_date)}
                          </span>
                          {contact.duration_minutes && (
                            <span className="flex items-center">
                              <FaClock className="mr-1 h-3 w-3" />
                              {contact.duration_minutes} min
                            </span>
                          )}
                        </div>
                        {contact.notes && (
                          <p className="text-sm text-gray-700 mb-2">{contact.notes}</p>
                        )}
                        {contact.next_follow_up && (
                          <p className="text-xs text-blue-600">
                            Next follow-up: {new Date(contact.next_follow_up).toLocaleDateString('en-IN')}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {contact.executive ? 
                        `${contact.executive.first_name} ${contact.executive.last_name}` : 
                        (contact.creator ? `${contact.creator.first_name} ${contact.creator.last_name}` : 'Unknown')
                      }
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactHistoryModal;
