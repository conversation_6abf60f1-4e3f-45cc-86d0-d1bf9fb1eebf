import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('attendance_records', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Reference to executive/employee',
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Attendance date',
    },
    check_in_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Check-in timestamp with timezone',
    },
    check_out_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Check-out timestamp with timezone',
    },
    status: {
      type: DataTypes.ENUM('present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'),
      allowNull: false,
      defaultValue: 'present',
      comment: 'Attendance status for the day',
    },
    location_check_in: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'GPS coordinates and address for check-in',
    },
    location_check_out: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'GPS coordinates and address for check-out',
    },
    total_hours: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Total working hours for the day',
    },
    break_time_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Total break time in minutes',
    },
    overtime_hours: {
      type: DataTypes.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Overtime hours worked',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional notes or remarks',
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who approved manual entry or changes',
    },
    is_manual_entry: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is a manual entry by admin/manager',
    },
    is_holiday: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this date is a holiday',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('attendance_records', ['tenant_id']);
  await queryInterface.addIndex('attendance_records', ['employee_id']);
  await queryInterface.addIndex('attendance_records', ['date']);
  await queryInterface.addIndex('attendance_records', ['tenant_id', 'employee_id', 'date'], {
    unique: true,
    name: 'unique_employee_date_attendance',
  });
  await queryInterface.addIndex('attendance_records', ['status']);
  await queryInterface.addIndex('attendance_records', ['is_manual_entry']);
  await queryInterface.addIndex('attendance_records', ['created_at']);

  console.log('✅ Created attendance_records table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('attendance_records');
  console.log('✅ Dropped attendance_records table');
};
