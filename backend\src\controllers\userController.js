import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { emailService } from '../services/emailService.js';

/**
 * Get all users
 */
export const getUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, search } = req.query;
    const tenantId = req.user.tenant.id;

    const whereClause = {
      tenant_id: tenantId,
    };

    if (search) {
      whereClause[models.Sequelize.Op.or] = [
        { first_name: { [models.Sequelize.Op.iLike]: `%${search}%` } },
        { last_name: { [models.Sequelize.Op.iLike]: `%${search}%` } },
        { email: { [models.Sequelize.Op.iLike]: `%${search}%` } },
      ];
    }

    const users = await models.User.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'is_active',
        'is_verified',
        'last_login_at',
        'created_at',
      ],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      order: [['created_at', 'DESC']],
    });

    res.json({
      success: true,
      data: {
        users: users.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: users.count,
          pages: Math.ceil(users.count / parseInt(limit)),
        },
      },
    });
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get user by ID
 */
export const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const user = await models.User.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug', 'description'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'is_active',
        'is_verified',
        'last_login_at',
        'created_at',
        'updated_at',
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.json({
      success: true,
      data: { user },
    });
  } catch (error) {
    logger.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new user
 */
export const createUser = async (req, res) => {
  try {
    const { first_name, last_name, email, phone, password, role_ids } = req.body;
    const tenantId = req.user.tenant.id;

    // Check if user already exists
    const existingUser = await models.User.findOne({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists',
      });
    }

    // Create user
    const user = await models.User.create({
      tenant_id: tenantId,
      first_name,
      last_name,
      email: email.toLowerCase(),
      phone,
      password, // Will be hashed by the model hook
      is_active: true,
      is_verified: false,
    });

    // Assign roles if provided
    if (role_ids && role_ids.length > 0) {
      const roleAssignments = role_ids.map(roleId => ({
        user_id: user.id,
        role_id: roleId,
        assigned_at: new Date(),
        is_active: true,
      }));

      await models.UserRole.bulkCreate(roleAssignments);
    }

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail({
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        phone: user.phone,
      });
    } catch (emailError) {
      logger.error('Failed to send welcome email:', emailError);
      // Don't fail user creation if welcome email fails
    }

    // Fetch created user with roles
    const createdUser = await models.User.findByPk(user.id, {
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'is_active',
        'is_verified',
        'created_at',
      ],
    });

    logger.info('User created successfully:', {
      userId: user.id,
      email: user.email,
      tenantId,
    });

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: { user: createdUser },
    });
  } catch (error) {
    logger.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update user
 */
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { first_name, last_name, email, phone, is_active, role_ids } = req.body;
    const tenantId = req.user.tenant.id;

    const user = await models.User.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Check if email is being changed and if it already exists
    if (email && email.toLowerCase() !== user.email) {
      const existingUser = await models.User.findOne({
        where: {
          email: email.toLowerCase(),
          id: { [models.Sequelize.Op.ne]: id },
        },
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'User with this email already exists',
        });
      }
    }

    // Update user
    await user.update({
      first_name: first_name || user.first_name,
      last_name: last_name || user.last_name,
      email: email ? email.toLowerCase() : user.email,
      phone: phone || user.phone,
      is_active: is_active !== undefined ? is_active : user.is_active,
    });

    // Update roles if provided
    if (role_ids) {
      // Remove existing roles
      await models.UserRole.destroy({
        where: { user_id: user.id },
      });

      // Add new roles
      if (role_ids.length > 0) {
        const roleAssignments = role_ids.map(roleId => ({
          user_id: user.id,
          role_id: roleId,
          assigned_at: new Date(),
          is_active: true,
        }));

        await models.UserRole.bulkCreate(roleAssignments);
      }
    }

    // Fetch updated user with roles
    const updatedUser = await models.User.findByPk(user.id, {
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'is_active',
        'is_verified',
        'updated_at',
      ],
    });

    res.json({
      success: true,
      message: 'User updated successfully',
      data: { user: updatedUser },
    });
  } catch (error) {
    logger.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete user (soft delete with unsubscribe email)
 */
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const user = await models.User.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Store user data for unsubscribe email before deletion
    const userData = {
      id: user.id,
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      phone: user.phone,
    };

    // Soft delete - deactivate user instead of hard delete
    await user.update({
      is_active: false,
      email: `deleted_${Date.now()}_${user.email}`, // Prevent email conflicts
    });

    // Send unsubscribe email
    try {
      await emailService.sendUnsubscribeEmail(userData);
    } catch (emailError) {
      logger.error('Failed to send unsubscribe email:', emailError);
      // Don't fail deletion if unsubscribe email fails
    }

    logger.info('User deleted successfully:', {
      userId: user.id,
      email: userData.email,
      tenantId,
    });

    res.json({
      success: true,
      message: 'User deleted successfully',
    });
  } catch (error) {
    logger.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
