/**
 * Migration to make subject and description fields optional in service_calls table
 * This aligns the database schema with the updated validation requirements
 */
import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  try {
    console.log('Making subject and description fields optional in service_calls table...');

    // Make subject column nullable
    await queryInterface.changeColumn('service_calls', 'subject', {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200], // Allow empty strings
      },
    });

    // Make description column nullable
    await queryInterface.changeColumn('service_calls', 'description', {
      type: DataTypes.TEXT,
      allowNull: true,
    });

    console.log('✅ Successfully made subject and description fields optional');
  } catch (error) {
    console.error('❌ Error making subject and description optional:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  try {
    console.log('Reverting subject and description fields to required in service_calls table...');

    // Make subject column required again
    await queryInterface.changeColumn('service_calls', 'subject', {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [5, 200],
      },
    });

    // Make description column required again
    await queryInterface.changeColumn('service_calls', 'description', {
      type: DataTypes.TEXT,
      allowNull: false,
    });

    console.log('✅ Successfully reverted subject and description fields to required');
  } catch (error) {
    console.error('❌ Error reverting subject and description fields:', error);
    throw error;
  }
};
