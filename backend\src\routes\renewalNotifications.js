import express from 'express';
import { body } from 'express-validator';
import { authenticateToken } from '../middleware/auth.js';
import {
  getRenewalSettings,
  createRenewalSetting,
  updateRenewalSetting,
  deleteRenewalSetting,
  getRenewalStats,
  triggerNotifications,
  triggerScheduleGeneration,
  getJobStatus,
  getNotificationHistory,
  getNotificationDetails,
} from '../controllers/renewalNotificationController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/renewal-notifications/settings:
 *   get:
 *     summary: Get all renewal notification settings
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Renewal settings retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/settings', getRenewalSettings);

/**
 * @swagger
 * /api/renewal-notifications/settings:
 *   post:
 *     summary: Create a new renewal notification setting
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - field_name
 *               - reminder_days
 *             properties:
 *               field_name:
 *                 type: string
 *                 enum: [amc_expiry_date, amc_renewal_date, tss_expiry_date, tss_renewal_date]
 *               reminder_days:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [30, 15, 7, 2]
 *               notification_channels:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [email, sms, whatsapp]
 *                 default: [email]
 *               is_active:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: Renewal setting created successfully
 *       400:
 *         description: Validation error or setting already exists
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/settings', [
  body('field_name')
    .isIn(['amc_expiry_date', 'amc_renewal_date', 'tss_expiry_date', 'tss_renewal_date'])
    .withMessage('Invalid field name'),
  body('reminder_days')
    .isArray({ min: 1 })
    .withMessage('Reminder days must be a non-empty array')
    .custom((value) => {
      if (!value.every(day => Number.isInteger(day) && day > 0)) {
        throw new Error('All reminder days must be positive integers');
      }
      return true;
    }),
  body('notification_channels')
    .optional()
    .isArray()
    .withMessage('Notification channels must be an array'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
], createRenewalSetting);

/**
 * @swagger
 * /api/renewal-notifications/settings/{id}:
 *   put:
 *     summary: Update a renewal notification setting
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reminder_days:
 *                 type: array
 *                 items:
 *                   type: integer
 *               notification_channels:
 *                 type: array
 *                 items:
 *                   type: string
 *               is_active:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Renewal setting updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Setting not found
 *       500:
 *         description: Server error
 */
router.put('/settings/:id', [
  body('reminder_days')
    .optional()
    .isArray({ min: 1 })
    .withMessage('Reminder days must be a non-empty array')
    .custom((value) => {
      if (value && !value.every(day => Number.isInteger(day) && day > 0)) {
        throw new Error('All reminder days must be positive integers');
      }
      return true;
    }),
  body('notification_channels')
    .optional()
    .isArray()
    .withMessage('Notification channels must be an array'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
], updateRenewalSetting);

/**
 * @swagger
 * /api/renewal-notifications/settings/{id}:
 *   delete:
 *     summary: Delete a renewal notification setting
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting ID
 *     responses:
 *       200:
 *         description: Renewal setting deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Setting not found
 *       500:
 *         description: Server error
 */
router.delete('/settings/:id', deleteRenewalSetting);

/**
 * @swagger
 * /api/renewal-notifications/stats:
 *   get:
 *     summary: Get renewal notification statistics
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/stats', getRenewalStats);

/**
 * @swagger
 * /api/renewal-notifications/trigger:
 *   post:
 *     summary: Manually trigger renewal notification processing
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date to process notifications for (defaults to today)
 *     responses:
 *       200:
 *         description: Notifications triggered successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/trigger', triggerNotifications);

/**
 * @swagger
 * /api/renewal-notifications/schedule:
 *   post:
 *     summary: Manually trigger schedule generation
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Schedule generation triggered successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/schedule', triggerScheduleGeneration);

/**
 * @swagger
 * /api/renewal-notifications/jobs/status:
 *   get:
 *     summary: Get scheduled job status
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Job status retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/jobs/status', getJobStatus);

/**
 * @swagger
 * /api/renewal-notifications/history:
 *   get:
 *     summary: Get notification history
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [scheduled, sent, failed, cancelled]
 *       - in: query
 *         name: renewal_type
 *         schema:
 *           type: string
 *           enum: [amc, tss, license, maintenance, support]
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification history retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/history', getNotificationHistory);

/**
 * @swagger
 * /api/renewal-notifications/history/{id}:
 *   get:
 *     summary: Get notification details by ID
 *     tags: [Renewal Notifications]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Notification ID
 *     responses:
 *       200:
 *         description: Notification details retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Notification not found
 *       500:
 *         description: Server error
 */
router.get('/history/:id', getNotificationDetails);

export default router;
