import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  try {
    // Check if the column already exists with correct enum values
    const tableDescription = await queryInterface.describeTable('service_calls');

    if (tableDescription.call_billing_type) {
      // Check if the enum already has the correct values
      try {
        const [enumValues] = await queryInterface.sequelize.query(`
          SELECT unnest(enum_range(NULL::enum_service_calls_call_billing_type)) as enum_value;
        `);

        const currentValues = enumValues.map(row => row.enum_value);
        const expectedValues = ['free_call', 'amc_call', 'per_call'];

        if (expectedValues.every(val => currentValues.includes(val))) {
          console.log('✅ call_billing_type column already exists with correct enum values, skipping...');
          return;
        }

        console.log('🔄 Dropping existing call_billing_type column with incorrect enum...');
        await queryInterface.removeColumn('service_calls', 'call_billing_type');
      } catch (error) {
        // If we can't check enum values, assume column needs to be recreated
        console.log('🔄 Cannot verify enum values, recreating column...');
        await queryInterface.removeColumn('service_calls', 'call_billing_type');
      }
    } else {
      // Column doesn't exist, this migration was probably already handled by migration 025
      console.log('✅ call_billing_type column does not exist, assuming migration 025 handled it correctly');
      return;
    }

    // Drop the enum type if it exists
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_service_calls_call_billing_type" CASCADE;'
    );

    // Create the enum type with proper escaping
    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_service_calls_call_billing_type" AS ENUM (
        'free_call',
        'amc_call',
        'per_call'
      );
    `);

    // Add the column with the new enum type
    await queryInterface.addColumn('service_calls', 'call_billing_type', {
      type: 'enum_service_calls_call_billing_type',
      allowNull: true,
      comment: 'Call billing type for the service call (free_call, amc_call, per_call)',
    });

    console.log('✅ Successfully added call_billing_type column with proper ENUM type');
  } catch (error) {
    console.error('❌ Error in migration 026:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  try {
    // Remove the column
    await queryInterface.removeColumn('service_calls', 'call_billing_type');
    
    // Drop the enum type
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_service_calls_call_billing_type" CASCADE;'
    );
    
    console.log('✅ Removed call_billing_type column and ENUM type');
  } catch (error) {
    console.error('❌ Error in rollback migration 026:', error);
    throw error;
  }
};
