import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import {
  FaUser,
  FaEdit,
  FaSave,
  FaTimes,
  FaCamera,
  FaShieldAlt,
  FaBell,
  FaKey,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';

const ProfilePage = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    designation: '',
    department: '',
    employeeId: '',
    joinDate: '',
    reportingManager: '',
    location: '',
    bio: '',
    profilePicture: '/assets/default-avatar.png'
  });

  // Fetch profile data from API
  useEffect(() => {
    fetchProfileData();
  }, []);

  const fetchProfileData = async () => {
    try {
      setLoading(true);

      // Fetch user profile data
      const response = await apiService.get('/auth/me');

      if (response.data?.success) {
        const userData = response.data.data.user;
        setProfileData({
          firstName: userData.first_name || '',
          lastName: userData.last_name || '',
          email: userData.email || '',
          phone: userData.phone || '',
          designation: userData.designation || 'Employee',
          department: userData.department || 'General',
          employeeId: userData.employee_id || `EMP${userData.id}`,
          joinDate: userData.createdAt ? userData.createdAt.split('T')[0] : '',
          reportingManager: userData.reporting_manager || 'N/A',
          location: userData.location || '',
          bio: userData.bio || 'No bio available',
          profilePicture: userData.avatar_url || '/assets/default-avatar.png'
        });
      } else {
        toast.error('Failed to load profile data');
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,
    monthlyReports: true,
    serviceUpdates: true,
    salesAlerts: true,
    systemAlerts: false
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    loginAlerts: true,
    sessionTimeout: 30,
    passwordExpiry: 90
  });

  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationChange = (field, value) => {
    setNotificationSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSecurityChange = (field, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveProfile = async () => {
    try {
      const response = await apiService.put('/profile', {
        first_name: profileData.firstName,
        last_name: profileData.lastName,
        phone: profileData.phone,
        location: profileData.location,
        bio: profileData.bio,
        avatar_url: profileData.profilePicture
      });

      if (response.data?.success) {
        setIsEditing(false);
        toast.success('Profile updated successfully');
      } else {
        toast.error(response.data?.message || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    }
  };

  const handleChangePassword = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }
    if (passwordData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    try {
      const response = await apiService.post('/profile/change-password', {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword
      });

      if (response.data?.success) {
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
        toast.success('Password changed successfully');
      } else {
        toast.error(response.data?.message || 'Failed to change password');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error('Failed to change password');
    }
  };

  const handleSaveNotifications = async () => {
    try {
      const response = await apiService.put('/settings/notifications', {
        email_notifications: notificationSettings.emailNotifications,
        sms_notifications: notificationSettings.smsNotifications,
        push_notifications: notificationSettings.pushNotifications,
        weekly_reports: notificationSettings.weeklyReports,
        monthly_reports: notificationSettings.monthlyReports,
        service_updates: notificationSettings.serviceUpdates,
        sales_alerts: notificationSettings.salesAlerts,
        system_alerts: notificationSettings.systemAlerts
      });

      if (response.data?.success) {
        toast.success('Notification preferences updated');
      } else {
        toast.error(response.data?.message || 'Failed to update notification preferences');
      }
    } catch (error) {
      console.error('Error updating notifications:', error);
      toast.error('Failed to update notification preferences');
    }
  };

  const handleSaveSecurity = async () => {
    try {
      const response = await apiService.put('/settings/security', {
        two_factor_auth: securitySettings.twoFactorAuth,
        login_alerts: securitySettings.loginAlerts,
        session_timeout: parseInt(securitySettings.sessionTimeout),
        password_expiry: parseInt(securitySettings.passwordExpiry)
      });

      if (response.data?.success) {
        toast.success('Security settings updated');
      } else {
        toast.error(response.data?.message || 'Failed to update security settings');
      }
    } catch (error) {
      console.error('Error updating security settings:', error);
      toast.error('Failed to update security settings');
    }
  };

  const tabs = [
    { id: 'profile', name: 'Profile Information', icon: <FaUser /> },
    { id: 'password', name: 'Change Password', icon: <FaKey /> },
    { id: 'notifications', name: 'Notifications', icon: <FaBell /> },
    { id: 'security', name: 'Security', icon: <FaShieldAlt /> }
  ];

  const renderProfileTab = () => (
    <div className="grid grid-cols-12 gap-4">
      <div className="lg:col-span-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 text-center">
          <div className="p-4">
            <div className="relative inline-block mb-3">
              <img
                src={profileData.profilePicture}
                alt="Profile"
                className="rounded-circle"
                style={{ width: '120px', height: '120px', objectFit: 'cover' }}
              />
              <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm absolute bottom-0 end-0 rounded-circle">
                <FaCamera />
              </button>
            </div>
            <h5>{profileData.firstName} {profileData.lastName}</h5>
            <p className="text-gray-600">{profileData.designation}</p>
            <p className="text-gray-600">{profileData.department}</p>
          </div>
        </div>
      </div>

      <div className="lg:col-span-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
            <h5 className="text-lg font-semibold text-gray-900 mb-0">Personal Information</h5>
            {isEditing ? (
              <div className="inline-flex rounded-md shadow-sm">
                <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-green-600 text-white border-green-600 hover:bg-green-700 focus:ring-green-500 px-3 py-1 text-sm" onClick={handleSaveProfile}>
                  <FaSave className="mr-1" />
                  Save
                </button>
                <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-gray-600 text-white border-gray-600 hover:bg-gray-700 focus:ring-gray-500 px-3 py-1 text-sm" onClick={() => setIsEditing(false)}>
                  <FaTimes className="mr-1" />
                  Cancel
                </button>
              </div>
            ) : (
              <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm" onClick={() => setIsEditing(true)}>
                <FaEdit className="mr-1" />
                Edit
              </button>
            )}
          </div>
          <div className="p-4">
            <div className="grid grid-cols-12 gap-4">
              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    value={profileData.firstName}
                    onChange={(e) => handleProfileChange('firstName', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.firstName}</p>
                )}
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    value={profileData.lastName}
                    onChange={(e) => handleProfileChange('lastName', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.lastName}</p>
                )}
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                {isEditing ? (
                  <input
                    type="email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    value={profileData.email}
                    onChange={(e) => handleProfileChange('email', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.email}</p>
                )}
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                {isEditing ? (
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    value={profileData.phone}
                    onChange={(e) => handleProfileChange('phone', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.phone}</p>
                )}
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                <p className="form-control-plaintext">{profileData.designation}</p>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <p className="form-control-plaintext">{profileData.department}</p>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Employee ID</label>
                <p className="form-control-plaintext">{profileData.employeeId}</p>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Join Date</label>
                <p className="form-control-plaintext">{new Date(profileData.joinDate).toLocaleDateString()}</p>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Reporting Manager</label>
                <p className="form-control-plaintext">{profileData.reportingManager}</p>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    value={profileData.location}
                    onChange={(e) => handleProfileChange('location', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.location}</p>
                )}
              </div>

              <div className="col-span-12 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                {isEditing ? (
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    rows="3"
                    value={profileData.bio}
                    onChange={(e) => handleProfileChange('bio', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.bio}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPasswordTab = () => (
    <div className="grid grid-cols-12 gap-4 justify-center">
      <div className="lg:col-span-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
            <h5 className="text-lg font-semibold text-gray-900 mb-0">Change Password</h5>
          </div>
          <div className="p-4">
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
              <div className="input-group">
                <input
                  type={showCurrentPassword ? 'text' : 'password'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={passwordData.currentPassword}
                  onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                />
                <button
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
              <div className="input-group">
                <input
                  type={showNewPassword ? 'text' : 'password'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={passwordData.newPassword}
                  onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                />
                <button
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              <small className="text-gray-600">Password must be at least 8 characters long</small>
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
              <div className="input-group">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={passwordData.confirmPassword}
                  onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                />
                <button
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500" onClick={handleChangePassword}>
              <FaKey className="mr-2" />
              Change Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="grid grid-cols-12 gap-4 justify-center">
      <div className="lg:col-span-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
            <h5 className="text-lg font-semibold text-gray-900 mb-0">Notification Preferences</h5>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm" onClick={handleSaveNotifications}>
              <FaSave className="mr-1" />
              Save
            </button>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-12 gap-4">
              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.emailNotifications}
                    onChange={(e) => handleNotificationChange('emailNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">Email Notifications</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.smsNotifications}
                    onChange={(e) => handleNotificationChange('smsNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">SMS Notifications</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.pushNotifications}
                    onChange={(e) => handleNotificationChange('pushNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">Push Notifications</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.weeklyReports}
                    onChange={(e) => handleNotificationChange('weeklyReports', e.target.checked)}
                  />
                  <label className="form-check-label">Weekly Reports</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.monthlyReports}
                    onChange={(e) => handleNotificationChange('monthlyReports', e.target.checked)}
                  />
                  <label className="form-check-label">Monthly Reports</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.serviceUpdates}
                    onChange={(e) => handleNotificationChange('serviceUpdates', e.target.checked)}
                  />
                  <label className="form-check-label">Service Updates</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.salesAlerts}
                    onChange={(e) => handleNotificationChange('salesAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">Sales Alerts</label>
                </div>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.systemAlerts}
                    onChange={(e) => handleNotificationChange('systemAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">System Alerts</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="grid grid-cols-12 gap-4 justify-center">
      <div className="lg:col-span-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
            <h5 className="text-lg font-semibold text-gray-900 mb-0">Security Settings</h5>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary-600 text-white border-primary-600 hover:bg-primary-700 focus:ring-primary-500 px-3 py-1 text-sm" onClick={handleSaveSecurity}>
              <FaSave className="mr-1" />
              Save
            </button>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-12 gap-4">
              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={securitySettings.twoFactorAuth}
                    onChange={(e) => handleSecurityChange('twoFactorAuth', e.target.checked)}
                  />
                  <label className="form-check-label">Two-Factor Authentication</label>
                </div>
                <small className="text-gray-600">Add an extra layer of security to your account</small>
              </div>

              <div className="md:col-span-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={securitySettings.loginAlerts}
                    onChange={(e) => handleSecurityChange('loginAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">Login Alerts</label>
                </div>
                <small className="text-gray-600">Get notified of new login attempts</small>
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={securitySettings.sessionTimeout}
                  onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}
                  min="5"
                  max="120"
                />
              </div>

              <div className="md:col-span-6 mb-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Password Expiry (days)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={securitySettings.passwordExpiry}
                  onChange={(e) => handleSecurityChange('passwordExpiry', parseInt(e.target.value))}
                  min="30"
                  max="365"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileTab();
      case 'password':
        return renderPasswordTab();
      case 'notifications':
        return renderNotificationsTab();
      case 'security':
        return renderSecurityTab();
      default:
        return renderProfileTab();
    }
  };

  if (loading) {
    return (
      <div className="w-full px-4">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12">
            <div className="flex justify-between items-center mb-6">
              <h1 className="h3 mb-0">My Profile</h1>
            </div>
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-4 text-center py-5">
                <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
                  <span className="sr-only">Loading...</span>
                </div>
                <p className="mt-3 text-gray-600">Loading profile...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="grid grid-cols-12 gap-4 mb-6">
        <div className="col-span-12">
          <h2 className="mb-0">My Profile</h2>
          <p className="text-gray-600">Manage your profile information and account settings</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12">
          <ul className="flex space-x-4 flex border-b border-gray-200 mb-6">
            {tabs.map(tab => (
              <li key={tab.id} className="nav-item">
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.icon}
                  <span className="ml-2">{tab.name}</span>
                </button>
              </li>
            ))}
          </ul>

          {/* Tab Content */}
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
