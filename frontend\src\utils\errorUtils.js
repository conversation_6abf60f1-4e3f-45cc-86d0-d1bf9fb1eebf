import { toast } from 'react-hot-toast';
import { getUserFriendlyMessage, userFriendlyMessages, errorMessages } from '../config/validationConfig.js';

/**
 * Enhanced error handling utility for API responses
 */
export class ErrorHandler {
  /**
   * Parse API error response and extract user-friendly message
   * @param {Object|Error} error - The error object from API call
   * @param {string} fallbackMessage - Custom fallback message
   * @returns {string} User-friendly error message
   */
  static parseApiError(error, fallbackMessage = null) {
    // Handle different error structures
    let errorMessage = '';
    let errorDetails = null;

    if (error?.response?.data) {
      const data = error.response.data;

      // Try to get message from various possible fields, prioritizing user-friendly messages
      errorMessage = data.details?.userFriendlyMessage || data.message || data.error || data.details?.message || '';
      errorDetails = data.details || null;
      
      // Handle validation errors - both array and object formats
      if (data.errors) {
        if (Array.isArray(data.errors)) {
          // Array format: [{ field: 'email', message: 'Email is required' }]
          const validationMessages = data.errors.map(err =>
            typeof err === 'string' ? err : err.message || err.msg || ''
          ).filter(msg => msg.length > 0);

          if (validationMessages.length > 0) {
            errorMessage = validationMessages.join('. ');
          }
        } else if (typeof data.errors === 'object') {
          // Object format: { customer_code: "This customer code is already in use" }
          const fieldErrors = Object.values(data.errors).filter(msg => msg && typeof msg === 'string');

          if (fieldErrors.length > 0) {
            // For object format, we prefer to show the main message and let handleFormErrors deal with field-specific display
            // But if there's no main message, we can show the field errors
            if (!errorMessage || errorMessage.length === 0) {
              errorMessage = fieldErrors.join('. ');
            }
          }
        }
      }
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    // Convert to user-friendly message
    const friendlyMessage = getUserFriendlyMessage(errorMessage, fallbackMessage);
    
    return {
      message: friendlyMessage,
      originalMessage: errorMessage,
      details: errorDetails,
      statusCode: error?.response?.status || null
    };
  }

  /**
   * Display error message to user via toast
   * @param {Object|Error} error - The error object
   * @param {string} fallbackMessage - Custom fallback message
   * @param {Object} options - Toast options
   */
  static showError(error, fallbackMessage = null, options = {}) {
    const { message } = this.parseApiError(error, fallbackMessage);
    
    toast.error(message, {
      duration: 5000,
      ...options
    });
    
    // Log technical details for debugging
    if (process.env.NODE_ENV === 'development') {
      console.error('Error details:', error);
    }
  }

  /**
   * Handle form validation errors
   * @param {Object} error - The error object
   * @param {Function} setErrors - Function to set form errors
   * @param {Function} setShowErrorPopup - Function to show error popup
   */
  static handleFormErrors(error, setErrors, setShowErrorPopup = null) {
    const { message, details } = this.parseApiError(error);

    // Handle backend validation errors - support both array and object formats
    const apiErrors = error?.response?.data?.errors;

    if (apiErrors) {
      const formErrors = {};

      if (Array.isArray(apiErrors)) {
        // Handle array format: [{ field: 'email', message: 'Email is required' }]
        apiErrors.forEach(err => {
          if (err.field && err.message) {
            // Map backend field names to frontend field names
            const frontendFieldName = this.mapBackendFieldToFrontend(err.field);
            formErrors[frontendFieldName] = this.getFieldSpecificMessage(frontendFieldName, err.message);
          }
        });
      } else if (typeof apiErrors === 'object') {
        // Handle object format: { customer_code: "This customer code is already in use" }
        Object.keys(apiErrors).forEach(fieldName => {
          const fieldError = apiErrors[fieldName];
          if (fieldError) {
            // Map backend field names to frontend field names
            const frontendFieldName = this.mapBackendFieldToFrontend(fieldName);
            // Use the exact error message from the API for field-specific errors
            formErrors[frontendFieldName] = this.getFieldSpecificMessage(frontendFieldName, fieldError);
          }
        });
      }

      if (Object.keys(formErrors).length > 0) {
        setErrors(formErrors);
        if (setShowErrorPopup) setShowErrorPopup(true);
        return true; // Indicate that field-specific errors were handled
      }
    }

    // Only show general error message if no field-specific errors were found
    this.showError(error, userFriendlyMessages.validationFailed);
    return false; // Indicate that only general error was shown
  }

  /**
   * Map backend field names to frontend field names
   * @param {string} backendFieldName - The backend field name
   * @returns {string} Frontend field name
   */
  static mapBackendFieldToFrontend(backendFieldName) {
    const fieldMapping = {
      // Customer form mappings
      'customer_code': 'tallySerialNo',
      'company_name': 'customerName',
      'admin_email': 'adminEmail',
      'md_contact_person': 'mdContactPerson',
      'md_phone_no': 'mdPhoneNo',
      'md_email': 'mdEmail',
      'office_contact_person': 'officeContactPerson',
      'office_phone_no': 'officePhoneNo',
      'office_email': 'officeEmail',
      'accounts_contact_person': 'accountsContactPerson',
      'accounts_phone_no': 'accountsPhoneNo',
      'accounts_email': 'accountsEmail',
      'auditor_contact_person': 'auditorContactPerson',
      'auditor_phone_no': 'auditorPhoneNo',
      'auditor_email': 'auditorEmail',
      'it_contact_person': 'itContactPerson',
      'it_phone_no': 'itPhoneNo',
      'it_email': 'itEmail',
      'tax_consultant_contact_person': 'taxConsultantContactPerson',
      'tax_consultant_phone_no': 'taxConsultantPhoneNo',
      'tax_consultant_email': 'taxConsultantEmail',
      'gst_no': 'gstNo',
      'profile_status': 'profileStatus',
      'customer_status': 'customerStatus',
      'follow_up_executive': 'followUpExecutive',
      'area_id': 'location',
      'industry_id': 'industry',

      // Service call form mappings
      'customer_id': 'customer',
      'call_type_id': 'callType',
      'status_id': 'status',
      'assigned_to': 'assignedTo',
      'call_description': 'description',
      'call_date': 'callDate',
      'scheduled_date': 'scheduledDate',

      // Common mappings
      'created_at': 'createdAt',
      'updated_at': 'updatedAt'
    };

    return fieldMapping[backendFieldName] || backendFieldName;
  }

  /**
   * Get field-specific error message
   * @param {string} fieldName - The field name
   * @param {string} originalMessage - The original error message
   * @returns {string} Enhanced error message
   */
  static getFieldSpecificMessage(fieldName, originalMessage) {
    if (!originalMessage) {
      return errorMessages.invalidFormat(fieldName);
    }

    const lowerMessage = originalMessage.toLowerCase();
    const lowerField = fieldName.toLowerCase();

    // Check if the message is already user-friendly (doesn't contain technical terms)
    const technicalTerms = ['sequelize', 'validation', 'constraint', 'foreign key', 'sql', 'database'];
    const hasTechnicalTerms = technicalTerms.some(term => lowerMessage.includes(term));

    // If the message is already user-friendly and specific, use it as-is
    if (!hasTechnicalTerms && originalMessage.length > 10 && originalMessage.length < 200) {
      // Check if it's a generic message that we should enhance
      const genericMessages = ['required', 'invalid', 'missing', 'empty'];
      const isGeneric = genericMessages.some(term =>
        lowerMessage === term || lowerMessage === `${term}.` || lowerMessage === `${fieldName} is ${term}`
      );

      if (!isGeneric) {
        // Use the original message as it's already specific and user-friendly
        return originalMessage;
      }
    }

    // Enhance generic or technical messages with field-specific ones
    if (lowerMessage.includes('required') || lowerMessage.includes('missing') || lowerMessage.includes('empty')) {
      if (lowerField.includes('company') || lowerField.includes('name')) {
        return errorMessages.companyNameRequired;
      }
      if (lowerField.includes('contact') || lowerField.includes('person')) {
        return errorMessages.contactPersonRequired;
      }
      if (lowerField.includes('phone')) {
        return errorMessages.phoneRequired;
      }
      if (lowerField.includes('email')) {
        return errorMessages.emailRequired;
      }
      if (lowerField.includes('customer')) {
        return errorMessages.customerRequired;
      }
      if (lowerField.includes('type') || lowerField.includes('call')) {
        return errorMessages.callTypeRequired;
      }
      if (lowerField.includes('description')) {
        return errorMessages.descriptionRequired;
      }
      if (lowerField.includes('status')) {
        return errorMessages.statusRequired;
      }
    }

    // Field-specific format messages for generic "invalid" errors
    if (lowerMessage.includes('invalid') && lowerMessage.length < 20) {
      if (lowerField.includes('email')) {
        return errorMessages.invalidEmail;
      }
      if (lowerField.includes('phone')) {
        return errorMessages.invalidPhone;
      }
      if (lowerField.includes('gst')) {
        return errorMessages.invalidGST;
      }
      if (lowerField.includes('pan')) {
        return errorMessages.invalidPAN;
      }
    }

    // Return the original message if it's already good, or enhance generic ones
    return originalMessage;
  }

  /**
   * Handle authentication errors
   * @param {Object} error - The error object
   * @returns {boolean} True if it was an auth error
   */
  static handleAuthError(error) {
    const statusCode = error?.response?.status;
    const errorMessage = error?.response?.data?.message || error?.message || '';
    
    if (statusCode === 401 || 
        errorMessage.toLowerCase().includes('unauthorized') ||
        errorMessage.toLowerCase().includes('token expired') ||
        errorMessage.toLowerCase().includes('session expired')) {
      
      this.showError(error, userFriendlyMessages.sessionExpired);
      
      // Clear auth data and redirect
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      setTimeout(() => {
        if (!window.location.pathname.includes('/auth/login')) {
          window.location.href = '/auth/login';
        }
      }, 1000);
      
      return true;
    }
    
    return false;
  }

  /**
   * Show success message
   * @param {string} message - Success message
   * @param {Object} options - Toast options
   */
  static showSuccess(message, options = {}) {
    toast.success(message, {
      duration: 3000,
      ...options
    });
  }

  /**
   * Show info message
   * @param {string} message - Info message
   * @param {Object} options - Toast options
   */
  static showInfo(message, options = {}) {
    toast(message, {
      duration: 4000,
      icon: 'ℹ️',
      ...options
    });
  }

  /**
   * Show warning message
   * @param {string} message - Warning message
   * @param {Object} options - Toast options
   */
  static showWarning(message, options = {}) {
    toast(message, {
      duration: 4000,
      icon: '⚠️',
      style: {
        background: '#f59e0b',
        color: '#fff',
      },
      ...options
    });
  }
}

/**
 * Hook for using error handler in React components
 */
export const useErrorHandler = () => {
  return {
    showError: ErrorHandler.showError,
    showSuccess: ErrorHandler.showSuccess,
    showInfo: ErrorHandler.showInfo,
    showWarning: ErrorHandler.showWarning,
    parseApiError: ErrorHandler.parseApiError,
    handleFormErrors: ErrorHandler.handleFormErrors,
    handleAuthError: ErrorHandler.handleAuthError
  };
};

/**
 * Higher-order function to wrap API calls with error handling
 * @param {Function} apiCall - The API call function
 * @param {string} errorMessage - Custom error message
 * @param {boolean} showToast - Whether to show toast notification
 * @returns {Function} Wrapped function
 */
export const withErrorHandling = (apiCall, errorMessage = null, showToast = true) => {
  return async (...args) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      if (showToast) {
        ErrorHandler.showError(error, errorMessage);
      }
      throw error;
    }
  };
};

export default ErrorHandler;
