import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const RenewalNotificationSettings = sequelize.define('RenewalNotificationSettings', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    field_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Name of the expiry field (e.g., tally_renewal_date, support_expiry_date)',
    },
    reminder_days: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: 'Array of days before expiry to send reminders (e.g., [30, 15, 7, 1])',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether renewal notifications are active for this field',
    },
    notification_channels: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: '["email", "sms"]',
      comment: 'Channels to use for renewal notifications',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'renewal_notification_settings',
    timestamps: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['field_name'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['created_by'],
      },
      {
        unique: true,
        fields: ['tenant_id', 'field_name'],
        name: 'unique_field_per_tenant',
      },
    ],
  });

  // Associations
  RenewalNotificationSettings.associate = function(models) {
    RenewalNotificationSettings.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    RenewalNotificationSettings.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });
  };

  // Instance methods
  RenewalNotificationSettings.prototype.shouldSendReminder = function(daysUntilExpiry) {
    if (!this.is_active) return false;
    
    const reminderDays = Array.isArray(this.reminder_days) ? this.reminder_days : [];
    return reminderDays.includes(daysUntilExpiry);
  };

  RenewalNotificationSettings.prototype.getActiveChannels = function() {
    return Array.isArray(this.notification_channels) ? this.notification_channels : ['email'];
  };

  // Static methods
  RenewalNotificationSettings.getActiveSettings = async function(tenantId) {
    return await this.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
      },
      order: [['field_name', 'ASC']],
    });
  };

  RenewalNotificationSettings.getSettingForField = async function(tenantId, fieldName) {
    return await this.findOne({
      where: {
        tenant_id: tenantId,
        field_name: fieldName,
        is_active: true,
      },
    });
  };

  RenewalNotificationSettings.getDefaultSettings = function() {
    return [
      {
        field_name: 'tally_renewal_date',
        reminder_days: [30, 15, 7, 1],
        notification_channels: ['email', 'sms'],
        is_active: true,
      },
      {
        field_name: 'support_expiry_date',
        reminder_days: [30, 15, 7, 1],
        notification_channels: ['email', 'sms'],
        is_active: true,
      },
      {
        field_name: 'license_expiry_date',
        reminder_days: [30, 15, 7, 1],
        notification_channels: ['email', 'sms'],
        is_active: true,
      },
      {
        field_name: 'maintenance_expiry_date',
        reminder_days: [30, 15, 7, 1],
        notification_channels: ['email', 'sms'],
        is_active: true,
      },
    ];
  };

  return RenewalNotificationSettings;
}
