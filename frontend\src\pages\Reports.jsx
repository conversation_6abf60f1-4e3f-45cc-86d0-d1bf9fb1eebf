import React, { useState, useEffect } from 'react';
import { Container, Card, CardBody, CardHeader, Button } from '../components/ui';
import { FaChartBar, FaChartLine, FaChartPie, FaDownload, FaCalendar, FaFilter, FaUsers, FaRupeeSign, FaTrophy, FaClock, FaFileExcel, FaFilePdf } from 'react-icons/fa';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';
import LoadingScreen from '../components/ui/LoadingScreen';

const Reports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('last30days');
  const [selectedReport, setSelectedReport] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState({
    customers: { data: [], summary: {} },
    sales: { data: [], summary: {} },
    serviceCalls: { data: [], summary: {} },
    amcs: { data: [], summary: {} }
  });

  const reportTypes = [
    { id: 'overview', label: 'Overview', icon: FaChartBar },
    { id: 'sales', label: 'Sales Performance', icon: FaChartLine },
    { id: 'customers', label: 'Customer Analytics', icon: FaUsers },
    { id: 'revenue', label: 'Revenue Analysis', icon: FaRupeeSign },
  ];

  const periods = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'last30days', label: 'Last 30 Days' },
    { value: 'last90days', label: 'Last 90 Days' },
    { value: 'thisyear', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' },
  ];

  // Fetch reports data from API
  useEffect(() => {
    fetchReportsData();
  }, [selectedPeriod]);

  const fetchReportsData = async () => {
    try {
      setLoading(true);

      // Build date filters based on selected period
      const dateFilters = getDateFilters(selectedPeriod);

      // Fetch data from all report endpoints
      const [customersRes, salesRes, serviceCallsRes, amcsRes] = await Promise.all([
        apiService.get('/reports/customers', { params: dateFilters }).catch(() => ({ data: { data: { customers: [], summary: {} } } })),
        apiService.get('/reports/sales', { params: dateFilters }).catch(() => ({ data: { data: { sales: [], summary: {} } } })),
        apiService.get('/reports/service-calls', { params: dateFilters }).catch(() => ({ data: { data: { serviceCalls: [], summary: {} } } })),
        apiService.get('/reports/amc', { params: dateFilters }).catch(() => ({ data: { data: { amcs: [], summary: {} } } }))
      ]);

      setReportData({
        customers: customersRes.data?.data || { data: [], summary: {} },
        sales: salesRes.data?.data || { data: [], summary: {} },
        serviceCalls: serviceCallsRes.data?.data || { data: [], summary: {} },
        amcs: amcsRes.data?.data || { data: [], summary: {} }
      });
    } catch (error) {
      console.error('Error fetching reports data:', error);
      toast.error('Failed to fetch reports data');
    } finally {
      setLoading(false);
    }
  };

  const getDateFilters = (period) => {
    const now = new Date();
    const filters = {};

    switch (period) {
      case 'today':
        filters.dateFrom = now.toISOString().split('T')[0];
        filters.dateTo = now.toISOString().split('T')[0];
        break;
      case 'yesterday':
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        filters.dateFrom = yesterday.toISOString().split('T')[0];
        filters.dateTo = yesterday.toISOString().split('T')[0];
        break;
      case 'last7days':
        const last7Days = new Date(now);
        last7Days.setDate(last7Days.getDate() - 7);
        filters.dateFrom = last7Days.toISOString().split('T')[0];
        filters.dateTo = now.toISOString().split('T')[0];
        break;
      case 'last30days':
        const last30Days = new Date(now);
        last30Days.setDate(last30Days.getDate() - 30);
        filters.dateFrom = last30Days.toISOString().split('T')[0];
        filters.dateTo = now.toISOString().split('T')[0];
        break;
      case 'last90days':
        const last90Days = new Date(now);
        last90Days.setDate(last90Days.getDate() - 90);
        filters.dateFrom = last90Days.toISOString().split('T')[0];
        filters.dateTo = now.toISOString().split('T')[0];
        break;
      case 'thisyear':
        filters.dateFrom = `${now.getFullYear()}-01-01`;
        filters.dateTo = now.toISOString().split('T')[0];
        break;
      default:
        // last30days as default
        const defaultLast30 = new Date(now);
        defaultLast30.setDate(defaultLast30.getDate() - 30);
        filters.dateFrom = defaultLast30.toISOString().split('T')[0];
        filters.dateTo = now.toISOString().split('T')[0];
    }

    return filters;
  };

  const handleExport = async (format) => {
    try {
      setLoading(true);
      const dateFilters = getDateFilters(selectedPeriod);

      const exportData = {
        reportType: selectedReport === 'overview' ? 'customers' : selectedReport === 'sales' ? 'sales' : selectedReport === 'customers' ? 'customers' : 'service-calls',
        filters: dateFilters,
        format: format === 'pdf' ? 'pdf' : 'xlsx'
      };

      const response = await apiService.post('/reports/export', exportData);

      if (response.data.success) {
        toast.success(`${format.toUpperCase()} export initiated. Download will start shortly.`);

        // In a real implementation, you would handle the download URL
        if (response.data.data?.downloadUrl) {
          // Create a temporary link to download the file
          const link = document.createElement('a');
          link.href = response.data.data.downloadUrl;
          link.download = `report-${selectedReport}-${Date.now()}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      } else {
        toast.error('Export failed. Please try again.');
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Export failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderOverviewReport = () => {
    const totalRevenue = reportData.sales.summary?.totalRevenue || 0;
    const totalCustomers = reportData.customers.summary?.totalCustomers || reportData.customers.data?.length || 0;
    const totalSales = reportData.sales.summary?.totalSales || reportData.sales.data?.length || 0;
    const totalServiceCalls = reportData.serviceCalls.summary?.totalServiceCalls || reportData.serviceCalls.data?.length || 0;

    return (
      <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 truncate">
                    ₹{totalRevenue.toLocaleString('en-IN')}
                  </h4>
                  <p className="mb-0 text-sm sm:text-base" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Revenue</p>
                </div>
                <div className="rounded-lg p-2 sm:p-3 flex-shrink-0" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                  <FaRupeeSign className="h-5 w-5 sm:h-6 sm:w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +18.5%
                </div>
                <span className="text-xs ml-2" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' }}>vs last period</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900 truncate">{totalCustomers}</h4>
                  <p className="text-gray-600 mb-0 text-sm sm:text-base">Total Customers</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-2 sm:p-3 flex-shrink-0">
                  <FaUsers className="h-5 w-5 sm:h-6 sm:w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +12.3%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last period</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900 truncate">{totalSales}</h4>
                  <p className="text-gray-600 mb-0 text-sm sm:text-base">Total Sales</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-2 sm:p-3 flex-shrink-0">
                  <FaTrophy className="h-5 w-5 sm:h-6 sm:w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +25.7%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last period</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900 truncate">{totalServiceCalls}</h4>
                  <p className="text-gray-600 mb-0 text-sm sm:text-base">Service Calls</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-2 sm:p-3 flex-shrink-0">
                  <FaClock className="h-5 w-5 sm:h-6 sm:w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +8.2%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last period</span>
              </div>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
              <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Revenue Trend</h5>
            </CardHeader>
            <CardBody className="p-4 sm:p-6">
              <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FaChartLine className="h-8 w-8 sm:h-12 sm:w-12 theme-icon-text mx-auto mb-3 sm:mb-4" />
                  <p className="text-gray-500 text-sm sm:text-base">Revenue chart will be displayed here</p>
                  <p className="text-xs sm:text-sm text-gray-400">Integration with chart library needed</p>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="bg-white border-0 shadow-lg">
            <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
              <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Sales by Category</h5>
            </CardHeader>
            <CardBody className="p-4 sm:p-6">
              <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FaChartPie className="h-8 w-8 sm:h-12 sm:w-12 theme-icon-text mx-auto mb-3 sm:mb-4" />
                  <p className="text-gray-500 text-sm sm:text-base">Pie chart will be displayed here</p>
                  <p className="text-xs sm:text-sm text-gray-400">Integration with chart library needed</p>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Performance Table */}
        <Card className="bg-white border-0 shadow-lg">
          <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
            <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Recent Sales</h5>
          </CardHeader>
          <CardBody className="p-0">
            <div className="overflow-x-auto">
              {loading ? (
                <LoadingScreen
                  title="Loading Sales Data..."
                  subtitle="Fetching recent sales information"
                  variant="modal"
                />
              ) : reportData.sales.data && reportData.sales.data.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                      <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">Date</th>
                      <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {reportData.sales.data.slice(0, 5).map((sale, index) => (
                      <tr key={sale.id || index} className="hover:bg-gray-50">
                        <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 dashboard-icon-bg rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                              <span className="dashboard-icon-text font-medium text-xs sm:text-sm">
                                {sale.customerName ? sale.customerName.charAt(0).toUpperCase() : 'C'}
                              </span>
                            </div>
                            <span className="font-medium text-gray-900 text-sm sm:text-base truncate">
                              {sale.customerName || 'Unknown Customer'}
                            </span>
                          </div>
                        </td>
                        <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden sm:table-cell">
                          {sale.saleDate ? new Date(sale.saleDate).toLocaleDateString() : 'N/A'}
                        </td>
                        <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap dashboard-icon-text font-medium text-sm sm:text-base">
                          ₹{(sale.totalAmount || sale.amount || 0).toLocaleString('en-IN')}
                        </td>
                        <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap hidden md:table-cell">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            sale.status === 'confirmed' ? 'bg-green-100 text-green-600' :
                              sale.status === 'delivered' ? 'bg-blue-100 text-blue-600' :
                                sale.status === 'cancelled' ? 'bg-red-100 text-red-600' :
                                  'bg-yellow-100 text-yellow-600'
                          }`}
                          >
                            {sale.status || 'Draft'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-8">
                  <FaChartBar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No sales data available</p>
                </div>
              )}
            </div>
          </CardBody>
        </Card>
      </div>
    );
  };

  // Add render functions for other report types
  const renderSalesReport = () => (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
          <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Sales Performance</h5>
        </CardHeader>
        <CardBody className="p-0">
          <div className="overflow-x-auto">
            {loading ? (
              <LoadingScreen
                title="Loading Sales Performance..."
                subtitle="Analyzing sales data and metrics"
                variant="modal"
              />
            ) : reportData.sales.data && reportData.sales.data.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">Date</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Type</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.sales.data.map((sale, index) => (
                    <tr key={sale.id || index} className="hover:bg-gray-50">
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 dashboard-icon-bg rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                            <span className="dashboard-icon-text font-medium text-xs sm:text-sm">
                              {sale.customerName ? sale.customerName.charAt(0).toUpperCase() : 'C'}
                            </span>
                          </div>
                          <span className="font-medium text-gray-900 text-sm sm:text-base truncate">
                            {sale.customerName || 'Unknown Customer'}
                          </span>
                        </div>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden sm:table-cell">
                        {sale.saleDate ? new Date(sale.saleDate).toLocaleDateString() : 'N/A'}
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap dashboard-icon-text font-medium text-sm sm:text-base">
                        ₹{(sale.totalAmount || sale.amount || 0).toLocaleString('en-IN')}
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap hidden md:table-cell">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          sale.status === 'confirmed' ? 'bg-green-100 text-green-600' :
                            sale.status === 'delivered' ? 'bg-blue-100 text-blue-600' :
                              sale.status === 'cancelled' ? 'bg-red-100 text-red-600' :
                                'bg-yellow-100 text-yellow-600'
                        }`}
                        >
                          {sale.status || 'Draft'}
                        </span>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden lg:table-cell">
                        {sale.saleType || 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-8">
                <FaChartLine className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No sales data available</p>
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderCustomersReport = () => (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
          <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Customer Analytics</h5>
        </CardHeader>
        <CardBody className="p-0">
          <div className="overflow-x-auto">
            {loading ? (
              <LoadingScreen
                title="Loading Customer Analytics..."
                subtitle="Processing customer data and insights"
                variant="modal"
              />
            ) : reportData.customers.data && reportData.customers.data.length > 0 ? (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">Location</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Industry</th>
                    <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Executive</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {reportData.customers.data.map((customer, index) => (
                    <tr key={customer.id || index} className="hover:bg-gray-50">
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 dashboard-icon-bg rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0">
                            <span className="dashboard-icon-text font-medium text-xs sm:text-sm">
                              {customer.customerName ? customer.customerName.charAt(0).toUpperCase() : 'C'}
                            </span>
                          </div>
                          <span className="font-medium text-gray-900 text-sm sm:text-base truncate">
                            {customer.customerName || 'Unknown Customer'}
                          </span>
                        </div>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden sm:table-cell">
                        {customer.location || 'N/A'}
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          customer.profileStatus === 'active' ? 'bg-green-100 text-green-600' :
                            customer.profileStatus === 'inactive' ? 'bg-red-100 text-red-600' :
                              'bg-yellow-100 text-yellow-600'
                        }`}
                        >
                          {customer.profileStatus === 'active' ? 'Active' :
                            customer.profileStatus === 'inactive' ? 'Inactive' : 'Prospect'}
                        </span>
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden md:table-cell">
                        {customer.industry || 'N/A'}
                      </td>
                      <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-gray-600 text-sm sm:text-base hidden lg:table-cell">
                        {customer.followUpExecutive || 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-8">
                <FaUsers className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No customer data available</p>
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderRevenueReport = () => (
    <div className="space-y-4 sm:space-y-6 w-full max-w-full overflow-x-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-6">
        <div className="bg-green-600 text-white rounded-lg shadow-lg">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <h4 className="text-xl sm:text-2xl font-bold mb-0 truncate">
                  ₹{(reportData.sales.summary?.totalRevenue || 0).toLocaleString('en-IN')}
                </h4>
                <p className="text-green-100 mb-0 text-sm sm:text-base">Total Revenue</p>
              </div>
              <div className="bg-white/20 rounded-lg p-2 sm:p-3 flex-shrink-0">
                <FaRupeeSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-blue-600 text-white rounded-lg shadow-lg">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <h4 className="text-xl sm:text-2xl font-bold mb-0 truncate">
                  ₹{(reportData.sales.summary?.totalPaid || 0).toLocaleString('en-IN')}
                </h4>
                <p className="text-blue-100 mb-0 text-sm sm:text-base">Paid Amount</p>
              </div>
              <div className="bg-white/20 rounded-lg p-2 sm:p-3 flex-shrink-0">
                <FaRupeeSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-orange-600 text-white rounded-lg shadow-lg">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div className="min-w-0 flex-1">
                <h4 className="text-xl sm:text-2xl font-bold mb-0 truncate">
                  ₹{(reportData.sales.summary?.totalOutstanding || 0).toLocaleString('en-IN')}
                </h4>
                <p className="text-orange-100 mb-0 text-sm sm:text-base">Outstanding</p>
              </div>
              <div className="bg-white/20 rounded-lg p-2 sm:p-3 flex-shrink-0">
                <FaRupeeSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Card className="bg-white border-0 shadow-lg">
        <CardHeader className="border-b border-gray-200 px-4 sm:px-6 py-3 sm:py-4">
          <h5 className="text-base sm:text-lg font-semibold text-gray-900 mb-0">Revenue Analysis</h5>
        </CardHeader>
        <CardBody className="p-4 sm:p-6">
          <div className="h-48 sm:h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <FaChartPie className="h-8 w-8 sm:h-12 sm:w-12 theme-icon-text mx-auto mb-3 sm:mb-4" />
              <p className="text-gray-500 text-sm sm:text-base">Revenue analysis chart will be displayed here</p>
              <p className="text-xs sm:text-sm text-gray-400">Integration with chart library needed</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  return (
    <div className="w-full">
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Reports & Analytics</h1>
        <p className="text-gray-600 text-sm sm:text-base lg:text-lg">Comprehensive insights into your business performance</p>
      </div>

      {/* Top Navigation for Report Types */}
      <div className="mb-4 sm:mb-6">
        <Card className="bg-white border-0 shadow-lg">
          <CardBody className="p-0">
            {/* Desktop: Single row with horizontal scroll, Mobile: Grid layout */}
            <div className="hidden sm:flex overflow-x-auto scrollbar-hide">
              {reportTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.id}
                    onClick={() => setSelectedReport(type.id)}
                    className={`flex-shrink-0 flex items-center px-4 py-3 text-sm font-medium transition-all duration-200 whitespace-nowrap border-b-2 ${
                      selectedReport === type.id
                        ? 'settings-nav-active border-primary-color'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 border-transparent'
                    }`}
                    style={selectedReport === type.id ? { borderBottomColor: 'var(--primary-color)' } : {}}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {type.label}
                  </button>
                );
              })}
            </div>

            {/* Mobile: Grid layout with 2 columns */}
            <div className="grid grid-cols-2 gap-0 sm:hidden">
              {reportTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.id}
                    onClick={() => setSelectedReport(type.id)}
                    className={`flex items-center justify-center px-3 py-3 text-xs font-medium transition-all duration-200 border-b-2 ${
                      selectedReport === type.id
                        ? 'settings-nav-active border-primary-color'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 border-transparent'
                    }`}
                    style={selectedReport === type.id ? { borderBottomColor: 'var(--primary-color)' } : {}}
                  >
                    <Icon className="mr-1 h-3 w-3" />
                    <span className="truncate">{type.label}</span>
                  </button>
                );
              })}
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Controls - Full Width */}
      <div className="w-full mb-4 sm:mb-6">
        <Card className="bg-white border-0 shadow-lg">
          <CardBody className="p-3 sm:p-4">
            <div className="flex flex-col gap-3 sm:gap-4">
              {/* Top row: Period selector and filters */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
                <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3 w-full sm:w-auto">
                  <div className="flex items-center w-full xs:w-auto">
                    <FaCalendar className="mr-2 dashboard-icon-text flex-shrink-0" />
                    <select
                      value={selectedPeriod}
                      onChange={(e) => setSelectedPeriod(e.target.value)}
                      className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:border-transparent w-full xs:w-auto min-w-0"
                      style={{ '--tw-ring-color': 'var(--primary-color)' }}
                    >
                      {periods.map((period) => (
                        <option key={period.value} value={period.value}>
                          {period.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 w-full xs:w-auto"
                  >
                    <FaFilter className="mr-2" />
                    More Filters
                  </Button>
                </div>

                {/* Export buttons */}
                <div className="flex items-center gap-2 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('pdf')}
                    className="flex-1 sm:flex-none"
                    disabled={loading}
                  >
                    <FaFilePdf className="mr-1 sm:mr-2" />
                    <span className="hidden xs:inline">PDF</span>
                    <span className="xs:hidden">PDF</span>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExport('excel')}
                    className="flex-1 sm:flex-none"
                    disabled={loading}
                  >
                    <FaFileExcel className="mr-1 sm:mr-2" />
                    <span className="hidden xs:inline">Excel</span>
                    <span className="xs:hidden">XLS</span>
                  </Button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Report Content - Full Width */}
      <div className="w-full max-w-full overflow-x-hidden">
        {selectedReport === 'overview' && renderOverviewReport()}
        {selectedReport === 'sales' && renderSalesReport()}
        {selectedReport === 'customers' && renderCustomersReport()}
        {selectedReport === 'revenue' && renderRevenueReport()}
      </div>
    </div>
  );
};

export default Reports;
