import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import api from '../../services/api';
import {
  FaChartLine,
  FaChartBar,
  FaChartPie,
  FaClock,
  FaUsers,
  FaTools,
  FaCalendar,
  FaDownload,
  FaFilter,
  FaArrowUp,
  FaArrowDown,
  FaEquals,
  FaExclamationTriangle,
  FaCheckCircle,
  FaSpinner,
  FaPhone,
  FaMoneyBillWave,
  FaUserCog,
  FaTachometerAlt,
  FaFileExcel,
  FaFilePdf,
  FaEye,
  FaChevronDown,
  FaInfoCircle
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import Tooltip from '../../components/ui/Tooltip';
import {
  <PERSON><PERSON>hart,
  Bar<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON>ut<PERSON>hart,
  AreaChart,
  Composed<PERSON>hart,
  ChartContainer
} from '../../components/charts';

const ServiceAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30'); // days
  const [selectedMetric, setSelectedMetric] = useState('all');
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [collapsedSections, setCollapsedSections] = useState({
    callSummary: false,
    performance: false,
    technicians: false
  });
  const [analyticsData, setAnalyticsData] = useState({
    summary: {},
    trends: {},
    performance: {},
    technicians: {},
    callTypes: {},
    responseTime: {},
    resolution: {}
  });

  // Comprehensive filters state
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    statusId: '',
    priority: '',
    assignedTo: '',
    customerId: '',
    callBillingType: '',
    isOverdue: '',
    statusCategory: '',
    scheduledDateFrom: '',
    scheduledDateTo: ''
  });

  // Filter options state
  const [filterOptions, setFilterOptions] = useState({
    statuses: [],
    executives: [],
    customers: [],
    priorities: [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ],
    callBillingTypes: [
      { value: 'free_call', label: 'Free Call' },
      { value: 'amc_call', label: 'AMC Call' },
      { value: 'per_call', label: 'Per Call' }
    ],
    statusCategories: [
      { value: 'open', label: 'Open' },
      { value: 'in_progress', label: 'In Progress' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' },
      { value: 'cancelled', label: 'Cancelled' }
    ]
  });

  useEffect(() => {
    fetchAnalyticsData();
    fetchFilterOptions();
  }, [dateRange, selectedMetric, filters]);

  // Close export dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportOptions && !event.target.closest('.export-dropdown')) {
        setShowExportOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showExportOptions]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('dateRange', dateRange);
      params.append('groupBy', 'day');

      // Add filter parameters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      // Fetch real service analytics data
      const response = await api.get(`/reports/service-analytics?${params}`);
      setAnalyticsData(response.data.data);

    } catch (error) {
      console.error('Error fetching analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Fetch filter options
  const fetchFilterOptions = async () => {
    try {
      // Fetch statuses from service calls filters endpoint
      const statusResponse = await api.get('/service-calls/filters');
      const statusData = statusResponse.data.data;

      // Fetch customers with multiple pages if needed
      let allCustomers = [];
      let currentPage = 1;
      let hasMoreCustomers = true;

      while (hasMoreCustomers && currentPage <= 5) { // Limit to 5 pages (500 customers max)
        try {
          const customerResponse = await api.get(`/customers?limit=100&page=${currentPage}`);
          const customerData = customerResponse.data.data;

          if (customerData.customers && customerData.customers.length > 0) {
            allCustomers = [...allCustomers, ...customerData.customers];

            // Check if there are more pages
            const pagination = customerResponse.data.pagination;
            hasMoreCustomers = pagination && currentPage < pagination.totalPages;
            currentPage++;
          } else {
            hasMoreCustomers = false;
          }
        } catch (customerError) {
          console.error(`Error fetching customers page ${currentPage}:`, customerError);
          hasMoreCustomers = false;
        }
      }

      // Fetch executives with multiple pages if needed
      let allExecutives = [];
      currentPage = 1;
      let hasMoreExecutives = true;

      while (hasMoreExecutives && currentPage <= 5) { // Limit to 5 pages (500 executives max)
        try {
          const executiveResponse = await api.get(`/executives?limit=100&page=${currentPage}`);
          const executiveData = executiveResponse.data.data;

          if (executiveData.executives && executiveData.executives.length > 0) {
            allExecutives = [...allExecutives, ...executiveData.executives];

            // Check if there are more pages
            const pagination = executiveResponse.data.pagination;
            hasMoreExecutives = pagination && currentPage < pagination.totalPages;
            currentPage++;
          } else {
            hasMoreExecutives = false;
          }
        } catch (executiveError) {
          console.error(`Error fetching executives page ${currentPage}:`, executiveError);
          hasMoreExecutives = false;
        }
      }

      setFilterOptions(prev => ({
        ...prev,
        statuses: statusData.statuses || [],
        executives: allExecutives.map(exec => ({
          value: exec.id,
          label: `${exec.first_name || ''} ${exec.last_name || ''}`.trim() || exec.name || 'Unknown'
        })),
        customers: allCustomers.map(customer => ({
          value: customer.id,
          label: customer.company_name || customer.name || 'Unknown Customer'
        }))
      }));
    } catch (error) {
      console.error('Error fetching filter options:', error);
      // Set empty arrays as fallback
      setFilterOptions(prev => ({
        ...prev,
        statuses: [],
        executives: [],
        customers: []
      }));
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApplyFilters = () => {
    fetchAnalyticsData();
    setShowFilters(false);
  };

  const clearAllFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      statusId: '',
      priority: '',
      assignedTo: '',
      customerId: '',
      callBillingType: '',
      isOverdue: '',
      statusCategory: '',
      scheduledDateFrom: '',
      scheduledDateTo: ''
    });
  };



  const getMetricIcon = (value, threshold = 0, isHigherBetter = true) => {
    if (value > threshold) {
      return isHigherBetter ?
        <FaArrowUp className="text-green-500" /> :
        <FaArrowDown className="text-red-500" />;
    } else if (value < threshold) {
      return isHigherBetter ?
        <FaArrowDown className="text-red-500" /> :
        <FaArrowUp className="text-green-500" />;
    }
    return <FaEquals className="text-gray-500" />;
  };

  const toggleSection = (section) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const exportToPDF = async () => {
    toast.loading('Generating PDF report...');
    // PDF export logic would go here
    setTimeout(() => {
      toast.success('PDF report generated successfully');
    }, 2000);
  };

  const exportToExcel = async () => {
    toast.loading('Generating Excel report...');
    // Excel export logic would go here
    setTimeout(() => {
      toast.success('Excel report generated successfully');
    }, 2000);
  };

  const exportAnalytics = async () => {
    try {
      toast.loading('Exporting analytics data...');

      // Ensure we have the latest data
      const summary = analyticsData.summary || {};
      const trends = analyticsData.trends || {};
      const callTypes = analyticsData.callTypes || {};
      const technicianPerformance = analyticsData.technicianPerformance || {};

      const exportData = [
        ['Metric', 'Value'],
        ['Total Service Calls', summary.totalCalls || 0],
        ['Open Calls', summary.openCalls || 0],
        ['Completed Calls', summary.completedCalls || 0],
        ['In Progress Calls', summary.inProgressCalls || 0],
        ['Free Calls', summary.freeCalls || 0],
        ['AMC Calls', summary.amcCalls || 0],
        ['Per Calls', summary.paidCalls || 0],
        ['Completion Rate (%)', summary.completionRate || 0],
        ['Average Response Time (hours)', summary.avgResponseTime || 0],
        ['Average Resolution Time (hours)', summary.avgResolutionTime || 0],
        [''],
        ['Daily Trends'],
        ...Object.entries(trends).map(([date, count]) => [date, count]),
        [''],
        ['Call Types'],
        ...Object.entries(callTypes).map(([type, count]) => [type, count]),
        [''],
        ['Technician Performance'],
        ['Name', 'Total Calls', 'Completed', 'Completion Rate (%)', 'Avg Response Time'],
        ...Object.values(technicianPerformance).map(tech => [
          tech.name || 'Unknown',
          tech.totalCalls || 0,
          tech.completedCalls || 0,
          tech.completionRate || 0,
          tech.avgResponseTime || 0
        ])
      ];

      const csvContent = exportData.map(row => 
        row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(',')
      ).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `service_analytics_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Analytics exported successfully');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export analytics');
    }
  };

  // Chart data functions
  const getServiceCallsTrendData = () => {
    const trends = analyticsData.trends || {};
    return Object.entries(trends).map(([date, count]) => ({
      date,
      total: count,
      completed: Math.floor(count * 0.7), // Mock completed calls
      pending: Math.floor(count * 0.3)    // Mock pending calls
    }));
  };

  const getServiceStatusData = () => {
    const summary = analyticsData.summary || {};
    return [
      { name: 'Completed', value: summary.completedCalls || 0, status: 'completed' },
      { name: 'In Progress', value: summary.inProgressCalls || 0, status: 'in-progress' },
      { name: 'Open', value: summary.openCalls || 0, status: 'open' },
      { name: 'Cancelled', value: summary.cancelledCalls || 0, status: 'cancelled' }
    ];
  };

  const getCallTypeData = () => {
    const callTypes = analyticsData.callTypes || {};
    return Object.entries(callTypes).map(([type, count]) => ({
      name: type,
      value: count
    }));
  };

  const getExecutivePerformanceData = () => {
    const technicians = analyticsData.technicianPerformance || {};
    return Object.values(technicians).map(tech => ({
      name: tech.name || 'Unknown',
      calls: tech.totalCalls || 0
    }));
  };

  const getResponseTimeData = () => {
    return [
      { name: 'High', hours: 2.5, priority: 'high' },
      { name: 'Medium', hours: 8.0, priority: 'medium' },
      { name: 'Low', hours: 24.0, priority: 'low' }
    ];
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Service Analytics..."
        subtitle="Analyzing service data and generating insights"
        variant="page"
      />
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="mb-0 text-2xl font-bold text-gray-900">📊 Service Analytics</h2>
          <p className="text-gray-600 mt-1">Comprehensive service performance insights and trends</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
          >
            <FaFilter className="mr-2" />
            Filters
          </button>

          <div className="flex items-center gap-2 relative z-50">
            <FaCalendar className="text-gray-500" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 relative z-50"
              style={{ position: 'relative', zIndex: 50 }}
            >
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
          </div>

          <div className="relative export-dropdown">
            <button
              onClick={() => setShowExportOptions(!showExportOptions)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
            >
              <FaDownload className="mr-2" />
              Export
              <FaChevronDown className="ml-2" />
            </button>

            {showExportOptions && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      exportAnalytics();
                      setShowExportOptions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FaFileExcel className="mr-2 text-green-600" />
                    Export to CSV
                  </button>
                  <button
                    onClick={() => {
                      exportToExcel();
                      setShowExportOptions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FaFileExcel className="mr-2 text-green-600" />
                    Export to Excel
                  </button>
                  <button
                    onClick={() => {
                      exportToPDF();
                      setShowExportOptions(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <FaFilePdf className="mr-2 text-red-600" />
                    Export to PDF
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Comprehensive Filters Section */}
      {showFilters && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Analytics Filters</h3>

          {/* Date Filters Section */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-800 mb-3">Date Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Call Date From</label>
                <input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Call Date To</label>
                <input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Scheduled Date From</label>
                <input
                  type="date"
                  value={filters.scheduledDateFrom}
                  onChange={(e) => handleFilterChange('scheduledDateFrom', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Scheduled Date To</label>
                <input
                  type="date"
                  value={filters.scheduledDateTo}
                  onChange={(e) => handleFilterChange('scheduledDateTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Status and Priority Filters */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-800 mb-3">Status & Priority Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={filters.statusId}
                  onChange={(e) => handleFilterChange('statusId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Statuses</option>
                  {filterOptions.statuses.map(status => (
                    <option key={status.id} value={status.id}>{status.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status Category</label>
                <select
                  value={filters.statusCategory}
                  onChange={(e) => handleFilterChange('statusCategory', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Categories</option>
                  {filterOptions.statusCategories.map(category => (
                    <option key={category.value} value={category.value}>{category.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select
                  value={filters.priority}
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Priorities</option>
                  {filterOptions.priorities.map(priority => (
                    <option key={priority.value} value={priority.value}>{priority.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Overdue Status</label>
                <select
                  value={filters.isOverdue}
                  onChange={(e) => handleFilterChange('isOverdue', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Services</option>
                  <option value="true">Overdue Only</option>
                  <option value="false">Not Overdue</option>
                </select>
              </div>
            </div>
          </div>

          {/* Service Type and Assignment Filters */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-800 mb-3">Service Type & Assignment Filters</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Service Type</label>
                <select
                  value={filters.callBillingType}
                  onChange={(e) => handleFilterChange('callBillingType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Service Types</option>
                  {filterOptions.callBillingTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Customer</label>
                <select
                  value={filters.customerId}
                  onChange={(e) => handleFilterChange('customerId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Customers</option>
                  {filterOptions.customers.map(customer => (
                    <option key={customer.value} value={customer.value}>{customer.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Assigned Executive</label>
                <select
                  value={filters.assignedTo}
                  onChange={(e) => handleFilterChange('assignedTo', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Executives</option>
                  {filterOptions.executives.map(executive => (
                    <option key={executive.value} value={executive.value}>{executive.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <button
              onClick={clearAllFilters}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Clear All Filters
            </button>
            <div className="flex gap-2">
              <button
                onClick={() => setShowFilters(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleApplyFilters}
                className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
                style={{ backgroundColor: '#15579e' }}
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 📞 Call Summary Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FaPhone className="text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Call Summary</h3>
          </div>
          <button
            onClick={() => toggleSection('callSummary')}
            className="sm:hidden p-2 text-gray-500 hover:text-gray-700"
          >
            <FaChevronDown className={`transform transition-transform ${collapsedSections.callSummary ? 'rotate-180' : ''}`} />
          </button>
        </div>

        <div className={`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 ${collapsedSections.callSummary ? 'hidden sm:grid' : ''}`}>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-xs font-medium">Total Calls</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.summary?.totalCalls || 0}</p>
              </div>
              <div className="bg-blue-100 rounded-lg p-2">
                <FaPhone className="text-blue-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Percentage of calls successfully completed">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Completion Rate</p>
                </Tooltip>
                <p className="text-2xl font-bold text-green-600">{analyticsData.summary?.completionRate || 0}%</p>
              </div>
              <div className="bg-green-100 rounded-lg p-2">
                <FaCheckCircle className="text-green-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Average time to first response">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Avg Response</p>
                </Tooltip>
                <p className="text-2xl font-bold text-orange-600">{analyticsData.summary?.avgResponseTime || 0}h</p>
              </div>
              <div className="bg-orange-100 rounded-lg p-2">
                <FaClock className="text-orange-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Calls currently open and pending">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Open Calls</p>
                </Tooltip>
                <p className="text-2xl font-bold text-red-600">{analyticsData.summary?.openCalls || 0}</p>
              </div>
              <div className="bg-red-100 rounded-lg p-2">
                <FaExclamationTriangle className="text-red-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Free service calls provided">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Free Calls</p>
                </Tooltip>
                <p className="text-2xl font-bold text-green-600">{analyticsData.summary?.freeCalls || 0}</p>
              </div>
              <div className="bg-green-100 rounded-lg p-2">
                <FaCheckCircle className="text-green-600" size={16} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 📊 Performance Metrics Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FaTachometerAlt className="text-green-600" />
            <h3 className="text-lg font-semibold text-gray-900">Performance Metrics</h3>
          </div>
          <button
            onClick={() => toggleSection('performance')}
            className="sm:hidden p-2 text-gray-500 hover:text-gray-700"
          >
            <FaChevronDown className={`transform transition-transform ${collapsedSections.performance ? 'rotate-180' : ''}`} />
          </button>
        </div>

        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${collapsedSections.performance ? 'hidden sm:grid' : ''}`}>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Service calls provided at no charge">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Free Calls</p>
                </Tooltip>
                <p className="text-2xl font-bold text-green-600">{analyticsData.summary?.freeCalls || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {analyticsData.summary?.totalCalls > 0 ?
                    `${((analyticsData.summary?.freeCalls || 0) / analyticsData.summary.totalCalls * 100).toFixed(1)}% of total` :
                    'No data available'
                  }
                </p>
              </div>
              <div className="bg-green-100 rounded-lg p-2">
                <FaCheckCircle className="text-green-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Annual Maintenance Contract service calls">
                  <p className="text-gray-600 text-xs font-medium cursor-help">AMC Calls</p>
                </Tooltip>
                <p className="text-2xl font-bold text-blue-600">{analyticsData.summary?.amcCalls || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {analyticsData.summary?.totalCalls > 0 ?
                    `${((analyticsData.summary?.amcCalls || 0) / analyticsData.summary.totalCalls * 100).toFixed(1)}% of total` :
                    'No data available'
                  }
                </p>
              </div>
              <div className="bg-blue-100 rounded-lg p-2">
                <FaTools className="text-blue-600" size={16} />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <Tooltip content="Billable service calls with charges">
                  <p className="text-gray-600 text-xs font-medium cursor-help">Per Calls</p>
                </Tooltip>
                <p className="text-2xl font-bold text-purple-600">{analyticsData.summary?.paidCalls || 0}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {analyticsData.summary?.totalCalls > 0 ?
                    `${((analyticsData.summary?.paidCalls || 0) / analyticsData.summary.totalCalls * 100).toFixed(1)}% of total` :
                    'No data available'
                  }
                </p>
              </div>
              <div className="bg-purple-100 rounded-lg p-2">
                <FaMoneyBillWave className="text-purple-600" size={16} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Service Calls Trend Chart */}
        <ChartContainer
          title="Service Calls Trend"
          subtitle={`Service call activity over the last ${dateRange} days`}
        >
          <LineChart
            data={getServiceCallsTrendData()}
            lines={[
              { dataKey: 'total', name: 'Total Calls', color: '#1d5795' },
              { dataKey: 'completed', name: 'Completed', color: '#10b981' },
              { dataKey: 'pending', name: 'Pending', color: '#f59e0b' }
            ]}
            height={300}
            formatters={{
              total: (val) => `${val} calls`,
              completed: (val) => `${val} completed`,
              pending: (val) => `${val} pending`
            }}
          />
        </ChartContainer>

        {/* Service Call Status Distribution */}
        <ChartContainer
          title="Service Call Status"
          subtitle="Distribution by current status"
        >
          <DonutChart
            data={getServiceStatusData()}
            height={300}
            colorScheme="status"
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {analyticsData.summary?.totalCalls || 0}
                </div>
                <div className="text-sm text-gray-500">Total Calls</div>
              </div>
            }
          />
        </ChartContainer>
      </div>

      {/* Additional Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Call Type Distribution */}
        <ChartContainer
          title="Call Types"
          subtitle="Distribution by service type"
          size="small"
        >
          <PieChart
            data={getCallTypeData()}
            height={250}
            showLabels={true}
            showPercentages={true}
          />
        </ChartContainer>

        {/* Executive Performance */}
        <ChartContainer
          title="Executive Performance"
          subtitle="Calls handled by each executive"
          size="small"
        >
          <BarChart
            data={getExecutivePerformanceData()}
            bars={[{ dataKey: 'calls', name: 'Calls Handled' }]}
            height={250}
            orientation="vertical"
            formatters={{ calls: (val) => `${val} calls` }}
          />
        </ChartContainer>

        {/* Response Time Analysis */}
        <ChartContainer
          title="Response Time"
          subtitle="Average response time by priority"
          size="small"
        >
          <BarChart
            data={getResponseTimeData()}
            bars={[{ dataKey: 'hours', name: 'Avg Hours' }]}
            height={250}
            orientation="horizontal"
            colorScheme="priority"
            formatters={{ hours: (val) => `${val}h` }}
          />
        </ChartContainer>
      </div>

      {/* Legacy Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-8">
        {/* Call Volume Trends */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-gray-900">Call Volume Trends</h3>
            <Tooltip content="Daily call volume over the selected period">
              <FaChartLine className="text-blue-600 cursor-help" />
            </Tooltip>
          </div>
          <div className="space-y-2">
            {Object.entries(analyticsData.trends || {}).slice(-7).map(([date, count]) => {
              const maxCount = Math.max(...Object.values(analyticsData.trends || {}), 1);
              const percentage = (count / maxCount) * 100;

              return (
                <div key={date} className="flex items-center justify-between group">
                  <span className="text-xs text-gray-600">{new Date(date).toLocaleDateString()}</span>
                  <div className="flex items-center">
                    <Tooltip content={`${count} calls on ${new Date(date).toLocaleDateString()}`}>
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-2 cursor-help">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300 group-hover:bg-blue-700"
                          style={{ width: `${Math.max(percentage, 5)}%` }}
                        ></div>
                      </div>
                    </Tooltip>
                    <span className="text-xs font-medium text-gray-900 w-6">{count}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Call Types Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-gray-900">Call Types</h3>
            <Tooltip content="Distribution of different call types">
              <FaChartPie className="text-green-600 cursor-help" />
            </Tooltip>
          </div>
          <div className="space-y-2">
            {Object.entries(analyticsData.callTypes || {}).map(([type, count]) => {
              const percentage = ((count / (analyticsData.summary?.totalCalls || 1)) * 100).toFixed(1);
              const isLongText = type.length > 12;

              return (
                <div key={type} className="flex items-center justify-between group">
                  <div className="flex-1 min-w-0 mr-2">
                    {isLongText ? (
                      <Tooltip content={type} position="top">
                        <span className="text-xs text-gray-600 truncate block cursor-help">
                          {type}
                        </span>
                      </Tooltip>
                    ) : (
                      <span className="text-xs text-gray-600 block">
                        {type}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center flex-shrink-0">
                    <Tooltip content={`${count} calls (${percentage}%)`}>
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2 cursor-help">
                        <div
                          className="bg-green-600 h-2 rounded-full transition-all duration-300 group-hover:bg-green-700"
                          style={{ width: `${Math.max(parseFloat(percentage), 5)}%` }}
                        ></div>
                      </div>
                    </Tooltip>
                    <span className="text-xs font-medium text-gray-900 w-8">{count}</span>
                    <span className="text-xs text-gray-500 w-10">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Billing Types Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-gray-900">Billing Types</h3>
            <Tooltip content="Revenue distribution by billing type">
              <FaMoneyBillWave className="text-purple-600 cursor-help" />
            </Tooltip>
          </div>
          <div className="space-y-2">
            {Object.entries(analyticsData.billingTypes || {}).map(([type, count]) => {
              const percentage = ((count / (analyticsData.summary?.totalCalls || 1)) * 100).toFixed(1);
              const colors = {
                'Free Calls': 'bg-green-600',
                'AMC Calls': 'bg-blue-600',
                'Per Calls': 'bg-purple-600'
              };
              const hoverColors = {
                'Free Calls': 'group-hover:bg-green-700',
                'AMC Calls': 'group-hover:bg-blue-700',
                'Per Calls': 'group-hover:bg-purple-700'
              };
              const isLongText = type.length > 12;

              return (
                <div key={type} className="flex items-center justify-between group">
                  <div className="flex-1 min-w-0 mr-2">
                    {isLongText ? (
                      <Tooltip content={type} position="top">
                        <span className="text-xs text-gray-600 truncate block cursor-help">
                          {type}
                        </span>
                      </Tooltip>
                    ) : (
                      <span className="text-xs text-gray-600 block">
                        {type}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center flex-shrink-0">
                    <Tooltip content={`${count} calls (${percentage}%)`}>
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2 cursor-help">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${colors[type] || 'bg-gray-600'} ${hoverColors[type] || ''}`}
                          style={{ width: `${Math.max(parseFloat(percentage), 5)}%` }}
                        ></div>
                      </div>
                    </Tooltip>
                    <span className="text-xs font-medium text-gray-900 w-8">{count}</span>
                    <span className="text-xs text-gray-500 w-10">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* 🧑‍🔧 Technician Performance Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <FaUserCog className="text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-900">Technician Performance</h3>
          </div>
          <button
            onClick={() => toggleSection('technicians')}
            className="sm:hidden p-2 text-gray-500 hover:text-gray-700"
          >
            <FaChevronDown className={`transform transition-transform ${collapsedSections.technicians ? 'rotate-180' : ''}`} />
          </button>
        </div>

        <div className={`${collapsedSections.technicians ? 'hidden sm:block' : ''}`}>
          {/* Desktop Table View */}
          <div className="hidden md:block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Technician
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Calls
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completed
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion Rate
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Response
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {Object.values(analyticsData.technicianPerformance || {})
                    .filter(tech => tech && tech.totalCalls > 0)
                    .sort((a, b) => b.totalCalls - a.totalCalls)
                    .map((tech, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{tech.name}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{tech.totalCalls}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{tech.completedCalls}</div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className={`text-sm font-medium ${
                            tech.completionRate >= 80 ? 'text-green-600' :
                            tech.completionRate >= 60 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {tech.completionRate >= 80 ? '✔️' : tech.completionRate >= 60 ? '⚠️' : '❗'} {tech.completionRate}%
                          </span>
                          <div className="ml-2 w-12 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                tech.completionRate >= 80 ? 'bg-green-600' :
                                tech.completionRate >= 60 ? 'bg-yellow-600' : 'bg-red-600'
                              }`}
                              style={{ width: `${tech.completionRate}%` }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{tech.avgResponseTime}h</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden grid grid-cols-1 gap-3">
            {Object.values(analyticsData.technicianPerformance || {})
              .filter(tech => tech && tech.totalCalls > 0)
              .sort((a, b) => b.totalCalls - a.totalCalls)
              .map((tech, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900">{tech.name}</h4>
                  <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                    tech.completionRate >= 80 ? 'bg-green-100 text-green-800' :
                    tech.completionRate >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {tech.completionRate >= 80 ? '✔️' : tech.completionRate >= 60 ? '⚠️' : '❗'} {tech.completionRate}%
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Total</p>
                    <p className="font-medium">{tech.totalCalls}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Completed</p>
                    <p className="font-medium">{tech.completedCalls}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Avg Response</p>
                    <p className="font-medium">{tech.avgResponseTime}h</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Response Time & Resolution Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Response Time Analysis */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-gray-900">Response Time Analysis</h3>
            <Tooltip content="Time taken to first respond to service calls">
              <FaClock className="text-orange-600 cursor-help" />
            </Tooltip>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Average Response Time</span>
              <span className="text-lg font-semibold text-orange-600">
                🕐 {analyticsData.summary?.avgResponseTime || 0} hours
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Responses Tracked</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.responseTime?.data?.length || 0}</span>
            </div>
            {(analyticsData.responseTime?.data?.length || 0) > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>⚡ Fast (&lt;2h)</span>
                  <span>⏱️ Average (2-8h)</span>
                  <span>🐌 Slow (&gt;8h)</span>
                </div>
                <Tooltip content="Response time distribution breakdown">
                  <div className="w-full bg-gray-200 rounded-full h-3 cursor-help">
                    {(() => {
                      const data = analyticsData.responseTime?.data || [];
                      const fast = data.filter(t => t < 2).length;
                      const average = data.filter(t => t >= 2 && t <= 8).length;
                      const slow = data.filter(t => t > 8).length;
                      const total = data.length;

                      return (
                        <div className="flex h-3 rounded-full overflow-hidden">
                          <div
                            className="bg-green-500 hover:bg-green-600 transition-colors"
                            style={{ width: `${(fast / total) * 100}%` }}
                          ></div>
                          <div
                            className="bg-yellow-500 hover:bg-yellow-600 transition-colors"
                            style={{ width: `${(average / total) * 100}%` }}
                          ></div>
                          <div
                            className="bg-red-500 hover:bg-red-600 transition-colors"
                            style={{ width: `${(slow / total) * 100}%` }}
                          ></div>
                        </div>
                      );
                    })()}
                  </div>
                </Tooltip>
                <div className="flex justify-between text-xs text-gray-600">
                  <span>{analyticsData.responseTime?.data ? analyticsData.responseTime.data.filter(t => t < 2).length : 0} fast</span>
                  <span>{analyticsData.responseTime?.data ? analyticsData.responseTime.data.filter(t => t >= 2 && t <= 8).length : 0} average</span>
                  <span>{analyticsData.responseTime?.data ? analyticsData.responseTime.data.filter(t => t > 8).length : 0} slow</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Resolution Time Analysis */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-semibold text-gray-900">Resolution Time Analysis</h3>
            <Tooltip content="Time taken to completely resolve service calls">
              <FaCheckCircle className="text-green-600 cursor-help" />
            </Tooltip>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Average Resolution Time</span>
              <span className="text-lg font-semibold text-green-600">
                ✅ {analyticsData.summary?.avgResolutionTime || 0} hours
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Resolutions Tracked</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.resolution?.data?.length || 0}</span>
            </div>
            {(analyticsData.resolution?.data?.length || 0) > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>🚀 Quick (&lt;4h)</span>
                  <span>📋 Standard (4-24h)</span>
                  <span>⏳ Extended (&gt;24h)</span>
                </div>
                <Tooltip content="Resolution time distribution breakdown">
                  <div className="w-full bg-gray-200 rounded-full h-3 cursor-help">
                    {(() => {
                      const data = analyticsData.resolution?.data || [];
                      const quick = data.filter(t => t < 4).length;
                      const standard = data.filter(t => t >= 4 && t <= 24).length;
                      const extended = data.filter(t => t > 24).length;
                      const total = data.length;

                      return (
                        <div className="flex h-3 rounded-full overflow-hidden">
                          <div
                            className="bg-green-500 hover:bg-green-600 transition-colors"
                            style={{ width: `${(quick / total) * 100}%` }}
                          ></div>
                          <div
                            className="bg-blue-500 hover:bg-blue-600 transition-colors"
                            style={{ width: `${(standard / total) * 100}%` }}
                          ></div>
                          <div
                            className="bg-orange-500 hover:bg-orange-600 transition-colors"
                            style={{ width: `${(extended / total) * 100}%` }}
                          ></div>
                        </div>
                      );
                    })()}
                  </div>
                </Tooltip>
                <div className="flex justify-between text-xs text-gray-600">
                  <span>{analyticsData.resolution?.data ? analyticsData.resolution.data.filter(t => t < 4).length : 0} quick</span>
                  <span>{analyticsData.resolution?.data ? analyticsData.resolution.data.filter(t => t >= 4 && t <= 24).length : 0} standard</span>
                  <span>{analyticsData.resolution?.data ? analyticsData.resolution.data.filter(t => t > 24).length : 0} extended</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceAnalytics;
