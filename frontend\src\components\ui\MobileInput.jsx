import React, { useState, useEffect, useRef } from 'react';
import { FaChevronDown, FaSearch } from 'react-icons/fa';
import { countryCodes, defaultCountryCode, findCountryByCode } from '../../data/countryCodes';

/**
 * MobileInput Component
 * 
 * A reusable component for international mobile number input with country code selection.
 * Features:
 * - Searchable country code dropdown
 * - Smart validation (only triggers when actual phone digits are entered)
 * - Seamless integration with existing forms
 * - Preserves existing validation patterns
 * - International phone number support
 */
const MobileInput = ({
  value = '',
  onChange,
  placeholder = 'Enter mobile number',
  error = false,
  disabled = false,
  readOnly = false,
  required = false,
  className = '',
  name,
  id,
  defaultCountryCode: propDefaultCountryCode = defaultCountryCode,
  showErrorMessage = true, // New prop to control error message display
  ...props
}) => {
  const [selectedCountryCode, setSelectedCountryCode] = useState(propDefaultCountryCode);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Parse existing phone number on component mount or value change
  useEffect(() => {
    if (value) {
      const parsed = parsePhoneNumber(value);
      setSelectedCountryCode(parsed.countryCode);
      setPhoneNumber(parsed.phoneNumber);
    } else {
      setSelectedCountryCode(propDefaultCountryCode);
      setPhoneNumber('');
    }
  }, [value, propDefaultCountryCode]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isDropdownOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isDropdownOpen]);

  /**
   * Parse phone number to extract country code and phone number
   */
  const parsePhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return { countryCode: propDefaultCountryCode, phoneNumber: '' };
    
    const trimmed = phoneNumber.trim();
    
    // Find matching country code (longest match first)
    const sortedCodes = [...countryCodes].sort((a, b) => b.code.length - a.code.length);
    const matchingCode = sortedCodes.find(code => trimmed.startsWith(code.code));
    
    if (matchingCode) {
      return {
        countryCode: matchingCode.code,
        phoneNumber: trimmed.substring(matchingCode.code.length).trim()
      };
    }
    
    // If no country code found, assume it's just the phone number
    return { countryCode: propDefaultCountryCode, phoneNumber: trimmed };
  };

  /**
   * Format phone number with country code
   */
  const formatPhoneWithCountryCode = (countryCode, phoneNumber) => {
    if (!phoneNumber || phoneNumber.trim() === '') return '';
    return `${countryCode} ${phoneNumber.trim()}`;
  };

  /**
   * Handle country code selection
   */
  const handleCountryCodeChange = (countryCode) => {
    setSelectedCountryCode(countryCode);
    setIsDropdownOpen(false);
    setSearchTerm('');
    
    // Only trigger onChange if there's an actual phone number
    if (phoneNumber.trim()) {
      const newValue = formatPhoneWithCountryCode(countryCode, phoneNumber);
      if (onChange) {
        onChange({ target: { name, value: newValue } });
      }
    }
  };

  /**
   * Handle phone number input change
   */
  const handlePhoneNumberChange = (e) => {
    const newPhoneNumber = e.target.value;
    setPhoneNumber(newPhoneNumber);
    
    // Only send value if there's actual phone number
    const newValue = newPhoneNumber.trim() ? 
      formatPhoneWithCountryCode(selectedCountryCode, newPhoneNumber) : '';
    
    if (onChange) {
      onChange({ target: { name, value: newValue } });
    }
  };

  /**
   * Filter countries based on search term
   */
  const filteredCountries = countryCodes.filter(country =>
    country.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.code.includes(searchTerm)
  );

  /**
   * Get selected country info
   */
  const selectedCountry = findCountryByCode(selectedCountryCode);

  return (
    <div className={`relative ${className}`}>
      <div className={`flex border-2 rounded-xl overflow-hidden ${
        error
          ? 'border-red-300 bg-red-50'
          : 'border-gray-200 bg-white hover:border-gray-300 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}>
        
        {/* Country Code Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => !disabled && !readOnly && setIsDropdownOpen(!isDropdownOpen)}
            disabled={disabled || readOnly}
            className={`flex items-center px-3 py-3 bg-gray-50 border-r border-gray-200 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 transition-colors ${
              disabled || readOnly ? 'cursor-not-allowed' : 'cursor-pointer'
            }`}
            aria-label="Select country code"
          >
            <span className="text-lg mr-1">{selectedCountry.flag}</span>
            <span className="text-sm font-medium text-gray-700 mr-1">{selectedCountryCode}</span>
            {!disabled && !readOnly && (
              <FaChevronDown className={`text-xs text-gray-500 transition-transform ${
                isDropdownOpen ? 'rotate-180' : ''
              }`} />
            )}
          </button>

          {/* Dropdown Menu */}
          {isDropdownOpen && !disabled && !readOnly && (
            <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-hidden">
              {/* Search Input */}
              <div className="p-2 border-b border-gray-100">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search countries..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-8 pr-3 py-2 text-sm border border-gray-200 rounded focus:outline-none focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Country List */}
              <div className="max-h-40 overflow-y-auto">
                {filteredCountries.length > 0 ? (
                  filteredCountries.map((country, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleCountryCodeChange(country.code)}
                      className={`w-full flex items-center px-3 py-2 text-left hover:bg-blue-50 focus:outline-none focus:bg-blue-50 transition-colors ${
                        selectedCountryCode === country.code ? 'bg-blue-100' : ''
                      }`}
                    >
                      <span className="text-lg mr-3">{country.flag}</span>
                      <span className="flex-1 text-sm text-gray-700">{country.country}</span>
                      <span className="text-sm font-medium text-gray-600">{country.code}</span>
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-500 text-center">
                    No countries found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <input
          type="tel"
          value={phoneNumber}
          onChange={handlePhoneNumberChange}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          required={required}
          name={name}
          id={id}
          className={`flex-1 px-4 py-3 focus:outline-none bg-transparent ${
            disabled ? 'cursor-not-allowed' : ''
          } ${readOnly ? 'cursor-default' : ''}`}
          {...props}
        />
      </div>

      {/* Error Message - Only show if error is a string and showErrorMessage is true */}
      {error && typeof error === 'string' && showErrorMessage && (
        <p className="mt-2 text-sm text-red-600 font-medium">{error}</p>
      )}
    </div>
  );
};

export default MobileInput;
