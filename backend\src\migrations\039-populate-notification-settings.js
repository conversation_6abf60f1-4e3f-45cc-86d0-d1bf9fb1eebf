import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    console.log('🔄 Populating default notification settings for existing tenants...');

    // Check if notification_settings table exists
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('notification_settings')) {
      console.log('⚠️  notification_settings table does not exist, skipping population');
      await transaction.commit();
      return;
    }

    // Check if tenants table exists
    if (!tables.includes('tenants')) {
      console.log('⚠️  tenants table does not exist, skipping population');
      await transaction.commit();
      return;
    }

    // Get all existing tenants
    const tenants = await queryInterface.sequelize.query(
      'SELECT id FROM tenants WHERE is_active = true',
      { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
    );

    console.log(`📋 Found ${tenants.length} active tenants`);

    if (tenants.length === 0) {
      console.log('✅ No active tenants found, nothing to populate');
      await transaction.commit();
      return;
    }

    // Check which tenants already have notification settings
    const existingSettings = await queryInterface.sequelize.query(
      'SELECT tenant_id FROM notification_settings',
      { type: queryInterface.sequelize.QueryTypes.SELECT, transaction }
    );

    const existingTenantIds = new Set(existingSettings.map(setting => setting.tenant_id));
    const tenantsNeedingSettings = tenants.filter(tenant => !existingTenantIds.has(tenant.id));

    console.log(`📋 ${tenantsNeedingSettings.length} tenants need default notification settings`);

    // Create default notification settings for tenants that don't have them
    for (const tenant of tenantsNeedingSettings) {
      await queryInterface.bulkInsert('notification_settings', [{
        tenant_id: tenant.id,
        service_created: true,
        service_started: true,
        service_completed: true,
        service_cancelled: false,
        service_on_hold: false,
        service_pending: false,
        service_follow_up: false,
        service_onsite: true,
        service_on_process: true,
        email_enabled: true,
        sms_enabled: false,
        push_enabled: false,
        email_templates: '{}',
        notification_delay_minutes: 0,
        business_hours_only: false,
        business_hours_start: '09:00:00',
        business_hours_end: '18:00:00',
        created_at: new Date(),
        updated_at: new Date(),
      }], { transaction });
    }

    await transaction.commit();
    console.log(`✅ Successfully created default notification settings for ${tenantsNeedingSettings.length} tenants`);
  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error populating notification settings:', error);
    
    // Don't throw the error - this is a data population migration
    // If it fails, the application should still work with default settings
    console.log('⚠️  Migration failed but continuing - notification service will use default settings');
  }
};

export const down = async (queryInterface, Sequelize) => {
  const transaction = await queryInterface.sequelize.transaction();

  try {
    console.log('🔄 Removing populated notification settings...');

    // This migration only populates data, so down migration removes the populated data
    // We'll only remove settings that match the default values we inserted
    await queryInterface.sequelize.query(`
      DELETE FROM notification_settings 
      WHERE 
        service_created = true AND
        service_started = true AND
        service_completed = true AND
        service_cancelled = false AND
        service_on_hold = false AND
        service_pending = false AND
        service_follow_up = false AND
        service_onsite = true AND
        service_on_process = true AND
        email_enabled = true AND
        sms_enabled = false AND
        push_enabled = false AND
        email_templates = '{}' AND
        notification_delay_minutes = 0 AND
        business_hours_only = false AND
        business_hours_start = '09:00:00' AND
        business_hours_end = '18:00:00'
    `, { transaction });

    await transaction.commit();
    console.log('✅ Successfully removed populated notification settings');
  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error removing populated notification settings:', error);
    throw error;
  }
};
