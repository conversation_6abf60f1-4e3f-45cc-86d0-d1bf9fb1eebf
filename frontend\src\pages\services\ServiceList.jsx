import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import themeManager from '../../utils/themeManager';
import TimerHistoryModal from '../../components/TimerHistoryModal';
import useServiceListTimers from '../../hooks/useServiceListTimers';
import useServiceListData from '../../hooks/useServiceListData';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaDownload,
  FaUser,
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools,
  FaTh,
  FaList,
  FaEllipsisV,
  FaPlay,
  FaPause,
  FaMapMarkerAlt,
  FaLaptop,
  FaChartLine,
  FaHistory,
  FaExchangeAlt,
  FaFilter
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import DateRangeFilter from '../../components/DateRangeFilter';
import { useViewPreference, PAGE_NAMES } from '../../utils/viewPreferences';

const ServiceList = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [servicesPerPage] = useState(10);
  const [currentThemeColor, setCurrentThemeColor] = useState('#2f69b3');
  const [viewMode, setViewMode] = useViewPreference(PAGE_NAMES.SERVICES, 'table');
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [showTimerHistory, setShowTimerHistory] = useState(false);
  const [selectedServiceForHistory, setSelectedServiceForHistory] = useState(null);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState({});
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [selectedServiceForStatus, setSelectedServiceForStatus] = useState(null);
  const [showTypeModal, setShowTypeModal] = useState(false);
  const [selectedServiceForType, setSelectedServiceForType] = useState(null);
  const [showAssignedModal, setShowAssignedModal] = useState(false);
  const [selectedServiceForAssigned, setSelectedServiceForAssigned] = useState(null);
  const [typeUpdateLoading, setTypeUpdateLoading] = useState({});
  const [assignedUpdateLoading, setAssignedUpdateLoading] = useState({});
  const [executives, setExecutives] = useState([]);
  const [executiveSearchTerm, setExecutiveSearchTerm] = useState('');
  const [executiveSearchResults, setExecutiveSearchResults] = useState([]);
  const [isSearchingExecutives, setIsSearchingExecutives] = useState(false);
  const [dateRange, setDateRange] = useState(null);

  // Use consolidated data hook to reduce duplicate API calls
  const {
    services,
    serviceStats,
    filterOptions,
    callStatuses,
    totalPages,
    totalServices,
    loading,
    statsLoading,
    filtersLoading,
    fetchAllData,
    fetchInitialData,
    updateService
  } = useServiceListData();

  // Use the service list timers hook for real-time timer updates
  const {
    getCurrentAccumulatedTime,
    isTimerRunning: isServiceTimerRunning,
    isTimerPaused: isServiceTimerPaused
  } = useServiceListTimers(services);

  // Get current theme color
  useEffect(() => {
    const savedTheme = localStorage.getItem('primaryColor') || '#2f69b3';
    setCurrentThemeColor(savedTheme);
  }, []);

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Update current time every second for real-time timer display
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // These functions are now handled by the consolidated useServiceListData hook

  // Debug: Log serviceStats whenever it changes
  useEffect(() => {
    console.log('🔍 ServiceList: serviceStats updated:', {
      totalCalls: serviceStats.totalCalls,
      callsByType: serviceStats.callsByType,
      hasError: serviceStats.metadata?.hasError,
      dataSource: serviceStats.metadata?.dataSource
    });
  }, [serviceStats]);

  // Fetch services data using consolidated hook - debounced for search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      console.log('🔄 ServiceList: Triggering fetchAllData...');
      fetchAllData({
        page: currentPage,
        limit: servicesPerPage,
        searchTerm,
        filterStatus,
        filterType,
        // Add date range parameters
        ...(dateRange && {
          startDate: dateRange.startDate.toISOString().split('T')[0],
          endDate: dateRange.endDate.toISOString().split('T')[0]
        }),
      });
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, filterType, currentPage, dateRange, fetchAllData]);

  // Fetch initial data on component mount
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  // Data fetching is now handled by the consolidated useServiceListData hook

  // Toggle dropdown for actions
  const toggleDropdown = (serviceId) => {
    setDropdownOpen(prev => ({
      ...prev,
      [serviceId]: !prev[serviceId]
    }));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen({});
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const handleDelete = async (serviceId) => {
    if (window.confirm('Are you sure you want to delete this service request?')) {
      try {
        const response = await apiService.delete(`/service-calls/${serviceId}`);
        if (response.data?.success) {
          // Refresh data instead of manual state update
          fetchAllData({
            page: currentPage,
            limit: servicesPerPage,
            searchTerm,
            filterStatus,
            filterType,
            forceRefresh: true
          });
          toast.success('Service request deleted successfully');
        } else {
          toast.error(response.data?.message || 'Failed to delete service request');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        toast.error('Failed to delete service request');
      }
    }
  };

  const handleShowTimerHistory = (service) => {
    setSelectedServiceForHistory(service);
    setShowTimerHistory(true);
  };

  // Handle status change - ENHANCED to work exactly like edit form
  const handleStatusChange = async (serviceId, newStatusId) => {
    try {
      setStatusUpdateLoading(prev => ({ ...prev, [serviceId]: true }));

      // CRITICAL FIX: Validate that newStatusId is a valid UUID string
      if (!newStatusId || typeof newStatusId !== 'string') {
        console.error('❌ Invalid status ID provided:', { newStatusId, type: typeof newStatusId });
        toast.error('Invalid status ID provided');
        return;
      }

      // Find the new status for better user feedback
      const newStatus = callStatuses.find(status => status.id === newStatusId);

      if (!newStatus) {
        console.error('❌ Status not found in callStatuses:', {
          newStatusId,
          availableStatuses: callStatuses.map(s => ({ id: s.id, name: s.name }))
        });
        toast.error('Selected status not found');
        return;
      }

      console.log('🔄 Action button status change:', {
        serviceId,
        newStatusId,
        newStatusIdType: typeof newStatusId,
        newStatusName: newStatus?.name,
        newStatusCode: newStatus?.code,
        isValidUUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(newStatusId)
      });

      // CRITICAL FIX: Use the exact same API call structure as edit form
      const updateData = {
        status_id: newStatusId // Use snake_case for backend compatibility
      };

      console.log('🚀 Making API call to update status (action button):', updateData);
      const response = await apiService.put(`/service-calls/${serviceId}`, updateData);

      if (response.data?.success) {
        console.log('✅ Status update successful (action button):', response.data);

        // Use optimistic update with the consolidated hook
        updateService(serviceId, {
          status: newStatus?.name || 'Updated',
          statusObject: newStatus
        });

        toast.success(`Status updated to ${newStatus?.name || 'new status'}`);

        // Refresh data after a small delay to ensure backend processing is complete
        setTimeout(() => {
          console.log('🔄 Refreshing services data after status change...');
          fetchAllData({
            page: currentPage,
            limit: servicesPerPage,
            searchTerm,
            filterStatus,
            filterType,
            forceRefresh: true
          });
        }, 500); // 500ms delay to ensure backend timer processing is complete

      } else {
        console.error('❌ API response indicates failure (action button):', response.data);
        const errorMessage = response.data?.details?.userFriendlyMessage ||
                            response.data?.message ||
                            'Failed to update status';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Error updating status (action button):', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to update service status');
    } finally {
      setStatusUpdateLoading(prev => ({ ...prev, [serviceId]: false }));
    }
  };

  // Fetch executives for assignment dropdown
  const fetchExecutives = async () => {
    try {
      console.log('🔄 Fetching executives for assignment...');
      const response = await apiService.get('/executives', {
        params: {
          isActive: true,
          limit: 100,
          sortBy: 'first_name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data) {
        const executivesList = response.data.data.map(exec => ({
          id: exec.id,
          name: `${exec.first_name || ''} ${exec.last_name || ''}`.trim() || exec.name || 'Unknown',
          employee_code: exec.employee_code,
          phone: exec.phone,
          email: exec.email
        }));
        setExecutives(executivesList);
        console.log('✅ Executives loaded:', executivesList.length);
      } else {
        console.warn('⚠️ No executives data received');
        setExecutives([]);
      }
    } catch (error) {
      console.error('❌ Error fetching executives:', error);
      toast.error('Failed to load executives');
      setExecutives([]);
    }
  };

  // Update service type
  const updateServiceType = async (serviceId, newType) => {
    try {
      setTypeUpdateLoading(prev => ({ ...prev, [serviceId]: true }));

      const updateData = {
        call_billing_type: newType
      };

      console.log('🚀 Making API call to update type:', updateData);
      const response = await apiService.put(`/service-calls/${serviceId}`, updateData);

      if (response.data?.success) {
        console.log('✅ Type update successful:', response.data);

        // Use optimistic update with the consolidated hook
        updateService(serviceId, {
          type: newType
        });

        toast.success(`Type updated to ${newType}`);
        setShowTypeModal(false);
        setSelectedServiceForType(null);

      } else {
        console.error('❌ API response indicates failure:', response.data);
        const errorMessage = response.data?.details?.userFriendlyMessage ||
                            response.data?.message ||
                            'Failed to update type';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Error updating type:', error);
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to update service type');
    } finally {
      setTypeUpdateLoading(prev => ({ ...prev, [serviceId]: false }));
    }
  };

  // Update service assigned executive
  const updateServiceAssigned = async (serviceId, executiveId, executiveName) => {
    try {
      setAssignedUpdateLoading(prev => ({ ...prev, [serviceId]: true }));

      const updateData = {
        assigned_to: executiveId
      };

      console.log('🚀 Making API call to update assigned executive:', updateData);
      const response = await apiService.put(`/service-calls/${serviceId}`, updateData);

      if (response.data?.success) {
        console.log('✅ Assigned executive update successful:', response.data);

        // Use optimistic update with the consolidated hook
        updateService(serviceId, {
          assignedTo: executiveName || 'Assigned',
          assignedToId: executiveId
        });

        toast.success(`Assigned to ${executiveName || 'executive'}`);
        setShowAssignedModal(false);
        setSelectedServiceForAssigned(null);
        setExecutiveSearchTerm('');
        setExecutiveSearchResults([]);

      } else {
        console.error('❌ API response indicates failure:', response.data);
        const errorMessage = response.data?.details?.userFriendlyMessage ||
                            response.data?.message ||
                            'Failed to update assigned executive';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Error updating assigned executive:', error);
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to update assigned executive');
    } finally {
      setAssignedUpdateLoading(prev => ({ ...prev, [serviceId]: false }));
    }
  };

  // Search executives
  const searchExecutives = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setExecutiveSearchResults([]);
      return;
    }

    try {
      setIsSearchingExecutives(true);
      console.log('🔍 Searching executives with term:', searchTerm);

      const response = await apiService.get('/executives', {
        params: {
          search: searchTerm,
          isActive: true,
          limit: 20,
          sortBy: 'first_name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data?.executives) {
        const searchResults = response.data.data.executives.map(exec => ({
          id: exec.id,
          name: `${exec.first_name || ''} ${exec.last_name || ''}`.trim() || exec.name || 'Unknown',
          employee_code: exec.employee_code,
          phone: exec.phone,
          email: exec.email
        }));
        setExecutiveSearchResults(searchResults);
        console.log('✅ Executive search results:', searchResults.length);
      } else {
        console.warn('⚠️ No executive search results');
        setExecutiveSearchResults([]);
      }
    } catch (error) {
      console.error('❌ Error searching executives:', error);
      setExecutiveSearchResults([]);
    } finally {
      setIsSearchingExecutives(false);
    }
  };

  // Handle executive search input change with debouncing
  const handleExecutiveSearchChange = (value) => {
    setExecutiveSearchTerm(value);

    // Debounce search
    if (window.executiveSearchTimeout) {
      clearTimeout(window.executiveSearchTimeout);
    }

    window.executiveSearchTimeout = setTimeout(() => {
      searchExecutives(value);
    }, 300);
  };

  // Open status change modal
  const openStatusModal = (service, event) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    console.log('🔄 Opening status modal for service:', service.id);
    console.log('📋 Available call statuses:', Array.isArray(callStatuses) ? callStatuses.length : 0, callStatuses);

    // Close all dropdowns when opening status modal
    setDropdownOpen({});

    // Close any open filter dropdowns by triggering a click outside event
    const closeEvent = new Event('click', { bubbles: true });
    document.dispatchEvent(closeEvent);

    setSelectedServiceForStatus(service);
    setShowStatusModal(true);
  };

  // Open type change modal
  const openTypeModal = (service, event) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    console.log('🔄 Opening type modal for service:', service.id);

    // Close all dropdowns when opening type modal
    setDropdownOpen({});

    // Close any open filter dropdowns by triggering a click outside event
    const closeEvent = new Event('click', { bubbles: true });
    document.dispatchEvent(closeEvent);

    setSelectedServiceForType(service);
    setShowTypeModal(true);
  };

  // Open assigned change modal
  const openAssignedModal = (service, event) => {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    console.log('🔄 Opening assigned modal for service:', service.id);

    // Close all dropdowns when opening assigned modal
    setDropdownOpen({});

    // Close any open filter dropdowns by triggering a click outside event
    const closeEvent = new Event('click', { bubbles: true });
    document.dispatchEvent(closeEvent);

    setSelectedServiceForAssigned(service);
    setShowAssignedModal(true);

    // Load executives if not already loaded
    if (executives.length === 0) {
      fetchExecutives();
    }
  };

  // Close dropdowns when clicking outside
  const handleCloseDropdowns = () => {
    setDropdownOpen({});
  };

  const getStatusBadge = (status) => {
    // Handle both string and object status
    const statusValue = typeof status === 'object' && status !== null ? status.name || status.status || 'unknown' : status || 'unknown';
    const statusString = String(statusValue).toLowerCase();

    const badgeConfig = {
      'completed': { bg: 'bg-success-100', text: 'text-success-800', icon: '✅' },
      'in-progress': { bg: 'bg-theme-100', text: 'text-theme-800', icon: '🔄' },
      'scheduled': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '📅' },
      'pending': { bg: 'bg-secondary-100', text: 'text-secondary-800', icon: '⏳' },
      'cancelled': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '❌' },
      'open': { bg: 'badge-primary', text: '', icon: '🔓' }
    };

    const config = badgeConfig[statusString] || badgeConfig.pending;
    const displayText = statusString.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getPriorityBadge = (priority) => {
    const priorityValue = typeof priority === 'object' && priority !== null ? priority.name || priority.priority || 'medium' : priority || 'medium';
    const priorityString = String(priorityValue).toLowerCase();

    const badgeConfig = {
      'high': { bg: 'bg-danger-100', text: 'text-danger-800', icon: '🔥' },
      'critical': { bg: 'bg-danger-200', text: 'text-danger-900', icon: '🚨' },
      'medium': { bg: 'bg-warning-100', text: 'text-warning-800', icon: '⚡' },
      'low': { bg: 'bg-success-100', text: 'text-success-800', icon: '🌱' }
    };

    const config = badgeConfig[priorityString] || badgeConfig.medium;
    const displayText = priorityString.charAt(0).toUpperCase() + priorityString.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  const getCallTypeIcon = (callType) => {
    const typeConfig = {
      'free call': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🆓' },
      'amc call': { icon: FaTools, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🔧' },
      'per call': { icon: FaRupeeSign, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '💰' },
    };

    const config = typeConfig[callType] || typeConfig['free call'];
    const IconComponent = config.icon;

    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-lg ${config.bg}`}>
        <span className="mr-1 text-sm">{config.emoji}</span>
        <IconComponent className={`${config.color} h-4 w-4`} />
      </div>
    );
  };

  const formatCallTypeDisplay = (callType) => {
    const displayMapping = {
      'free_call': 'Free Call',
      'amc_call': 'AMC Call',
      'per_call': 'Per Call',
      // Legacy support for space format
      'free call': 'Free Call',
      'amc call': 'AMC Call',
      'per call': 'Per Call'
    };
    return displayMapping[callType] || 'Free Call';
  };

  const getServiceTypeIcon = (type) => {
    const typeConfig = {
      // Call billing types (underscore format)
      'free_call': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🆓' },
      'amc_call': { icon: FaTools, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🔧' },
      'per_call': { icon: FaRupeeSign, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '💰' },
      // Legacy support (space format)
      'free call': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🆓' },
      'amc call': { icon: FaTools, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🔧' },
      'per call': { icon: FaRupeeSign, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '💰' },
      // Legacy service types (fallback)
      'Installation': { icon: FaTools, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🔧' },
      'Support': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🛠️' },
      'Training': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🎓' },
      'Maintenance': { icon: FaTools, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '⚙️' },
      'Consultation': { icon: FaUser, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '💡' },
      // Service location types (fallback)
      'onsite': { icon: FaMapMarkerAlt, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '🏢' },
      'online': { icon: FaLaptop, color: 'theme-icon-text', bg: 'theme-icon-bg', emoji: '💻' }
    };

    const config = typeConfig[type] || typeConfig.Support;
    const IconComponent = config.icon;

    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-lg ${config.bg}`}>
        <span className="mr-1 text-sm">{config.emoji}</span>
        <IconComponent className={`${config.color} h-4 w-4`} />
      </div>
    );
  };

  // Helper function to format time duration with proper display
  const formatTimeDuration = (totalSeconds) => {
    if (!totalSeconds || totalSeconds === 0) return '0 seconds';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;

  };

  // Helper function to format time for display in HH:MM:SS format
  const formatTimeDisplay = (totalSeconds) => {
    if (!totalSeconds || totalSeconds === 0) return '00:00:00';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  // Timer Display Component
  const TimerDisplay = ({ service }) => {
    // Check if service is in progress - check both status name and code
    const statusName = service.status?.toLowerCase() || '';
    const statusCode = service.statusObject?.code || service.status?.code || '';

    const isInProgress = statusName === 'in progress' ||
                        statusName === 'in-progress' ||
                        statusName === 'on process' ||
                        statusCode === 'ON_PROCESS' ||
                        statusCode === 'IN_PROGRESS' ||
                        statusCode === 'PROGRESS';

    const isOnHold = statusName === 'on hold' ||
                     statusName === 'hold' ||
                     statusCode === 'ON_HOLD' ||
                     statusCode === 'HOLD';

    const isPending = statusName === 'pending' ||
                      statusCode === 'PENDING';

    const isOpen = statusName === 'open' ||
                   statusCode === 'OPEN';

    // Get time tracking data from service and real-time timer hook
    const timeTrackingSummary = service.time_tracking_summary || {};
    const totalSeconds = service.totalTimeSeconds || timeTrackingSummary.total_time_seconds || 0;

    // Use real-time timer data from hook when available, fallback to service data
    const realtimeAccumulatedTime = getCurrentAccumulatedTime(service.id);
    const realtimeTimerRunning = isServiceTimerRunning(service.id);
    const realtimeTimerPaused = isServiceTimerPaused(service.id);

    // Use real-time data if available, otherwise fallback to service data
    const currentAccumulatedTime = realtimeAccumulatedTime || timeTrackingSummary.current_accumulated_time || 0;
    const backendTimerRunning = realtimeTimerRunning || timeTrackingSummary.is_timer_running || false;
    const backendTimerPaused = realtimeTimerPaused || timeTrackingSummary.is_timer_paused || false;


    // Calculate elapsed time based on backend timer state and status
    const getElapsedTime = () => {
      // Use backend timer state as primary source of truth
      if (backendTimerRunning && isInProgress) {
        // Timer is actively running - calculate real-time elapsed time
        if (service.startedAt) {
          const startTime = new Date(service.startedAt);
          const totalElapsedSinceStart = Math.floor((currentTime - startTime) / 1000); // seconds

          // Backend adjusts started_at to account for accumulated time, so this gives total time
          return Math.max(0, totalElapsedSinceStart);
        }

        // Fallback to backend accumulated time if no startedAt
        return currentAccumulatedTime || 0;
      }

      if (backendTimerPaused && (isOnHold || isOpen || isPending)) {
        // Timer is paused - show stored accumulated time (static)
        return totalSeconds || currentAccumulatedTime || 0;
      }

      // For completed or other statuses, show final total time
      return totalSeconds || 0;
    };

    const displayTime = getElapsedTime();

    // If service is completed, show total time only
    if (!isInProgress && !isOnHold && !isOpen && !isPending && totalSeconds > 0) {
      return (
        <div className="text-xs text-gray-600 flex items-center w-full overflow-hidden">
          <FaClock className="mr-1 h-2 w-2 flex-shrink-0" />
          <span className="font-mono text-xs truncate">{formatTimeDisplay(totalSeconds)}</span>
        </div>
      );
    }

    // If service is on hold, open, or pending (all are paused states), show paused time
    if (isOnHold || isOpen || isPending) {
      return (
        <div className="text-xs text-warning-600 flex items-center w-full overflow-hidden">
          <FaPause className="mr-1 h-2 w-2 flex-shrink-0" />
          <span className="font-mono text-xs truncate">{formatTimeDisplay(displayTime)}</span>
        </div>
      );
    }

    // If service is in progress, show running timer (even if time is 0)
    if (isInProgress) {
      return (
        <div className="w-full overflow-hidden">
          <div className="flex items-center text-xs text-theme-600 font-medium">
            <FaPlay className="mr-1 h-2 w-2 animate-pulse flex-shrink-0" />
            <span className="font-mono text-xs truncate">{formatTimeDisplay(displayTime)}</span>
          </div>
        </div>
      );
    }

    // Default case - no time tracked
    return (
      <div className="text-xs text-gray-500 flex items-center w-full overflow-hidden">
        <FaClock className="mr-1 h-2 w-2 flex-shrink-0" />
        <span className="font-mono text-xs">00:00:00</span>
      </div>
    );
  };

  // Helper function to safely get service count with fallback
  const getServiceCount = (apiValue, fallbackFilter) => {
    // Check if we have an error state
    if (serviceStats.metadata?.hasError) {
      console.warn('Service stats has error, using fallback calculation');
    }

    // If we have valid API data, use it
    if (typeof apiValue === 'number' && apiValue >= 0) {
      return apiValue;
    }

    // If API data is unavailable or invalid, calculate from services array
    if (Array.isArray(services) && services.length > 0) {
      return services.filter(fallbackFilter).length;
    }

    // If no data available at all, return 0
    return 0;
  };

  // Helper function to display error state for cards
  const renderCardContent = (value, label, icon, isError = false) => {
    if (statsLoading) {
      return (
        <>
          <div className="w-12 h-5 bg-gray-200 animate-pulse rounded mb-0"></div>
          <p className="text-gray-600 mb-0 text-xs truncate">{label}</p>
        </>
      );
    }

    if (isError && serviceStats.metadata?.hasError) {
      return (
        <>
          <div className="flex items-center">
            <h4 className="text-base sm:text-lg lg:text-xl font-bold mb-0 text-gray-900 truncate">
              {value}
            </h4>
            <span
              className="ml-2 text-orange-500 cursor-help"
              title={serviceStats.metadata.errorMessage || 'Data temporarily unavailable'}
            >
              ⚠️
            </span>
          </div>
          <p className="text-gray-600 mb-0 text-xs truncate">{label}</p>
        </>
      );
    }

    return (
      <>
        <h4 className="text-base sm:text-lg lg:text-xl font-bold mb-0 text-gray-900 truncate">
          {value}
        </h4>
        <p className="text-gray-600 mb-0 text-xs truncate">{label}</p>
      </>
    );
  };

  // Helper function to get status count with case-insensitive matching
  const getStatusCount = (statusName) => {
    const apiCount = serviceStats.callsByStatus?.find(s =>
      s.status?.toLowerCase() === statusName.toLowerCase() ||
      s.status?.toLowerCase().replace(/\s+/g, '-') === statusName.toLowerCase().replace(/\s+/g, '-')
    )?.count;

    return getServiceCount(apiCount, (service) => {
      const serviceStatus = service.status?.toLowerCase() || '';
      const normalizedStatus = serviceStatus.replace(/\s+/g, '-');
      const targetStatus = statusName.toLowerCase().replace(/\s+/g, '-');

      return serviceStatus === statusName.toLowerCase() ||
             normalizedStatus === targetStatus ||
             serviceStatus.includes(statusName.toLowerCase());
    });
  };

  // Helper function to get call type count with multiple field checking
  const getCallTypeCount = (callType, apiField) => {
    // First try to get the count from the API data
    const apiCount = serviceStats.callsByType?.[apiField];

    // If we have valid API data, use it directly
    if (typeof apiCount === 'number' && apiCount >= 0) {
      return apiCount;
    }

    // Fallback to manual calculation from services data
    return getServiceCount(apiCount, (service) => {
      // The transformed service data stores call_billing_type in the 'type' field
      const type = service.type?.toLowerCase() || '';

      switch (callType.toLowerCase()) {
        case 'free':
          return type === 'free_call' || type === 'free call' || type === 'free';
        case 'amc':
          return type === 'amc_call' || type === 'amc call' || type === 'amc';
        case 'paid':
          return type === 'per_call' || type === 'per call' || type === 'paid' || type === 'paid call';
        default:
          return false;
      }
    });
  };

  const formatGrowthPercentage = (percentage, baseColorClass = 'blue') => {
    if (statsLoading) {
      return (
        <div className="bg-gray-200 animate-pulse px-2 py-1 rounded-full text-xs font-medium flex items-center">
          <div className="w-8 h-3 bg-gray-300 rounded"></div>
        </div>
      );
    }

    // Check if we have an error state
    if (serviceStats.metadata?.hasError) {
      return (
        <div className={'bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs font-medium flex items-center'}>
          <i className="bi bi-exclamation-triangle mr-1"></i>
          Error
        </div>
      );
    }

    // Distinguish between null (no data) and 0 (zero growth)
    if (percentage === null || percentage === undefined) {
      return (
        <div className={'bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium flex items-center'}>
          <i className="bi bi-dash mr-1"></i>
          N/A
        </div>
      );
    }

    // Handle NaN values
    if (isNaN(percentage)) {
      return (
        <div className={'bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium flex items-center'}>
          <i className="bi bi-dash mr-1"></i>
          N/A
        </div>
      );
    }

    // Valid percentage value (including 0)
    const numericPercentage = Number(percentage);
    const isPositive = numericPercentage >= 0;
    const colorClass = isPositive
      ? 'bg-theme-100 text-theme-600'
      : 'bg-danger-100 text-danger-600';

    // Special case for zero
    if (numericPercentage === 0) {
      return (
        <div className={'bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium flex items-center'}>
          <i className="bi bi-dash mr-1"></i>
          0%
        </div>
      );
    }

    return (
      <div className={`${colorClass} px-2 py-1 rounded-full text-xs font-medium flex items-center`}>
        <i className={`bi ${isPositive ? 'bi-arrow-up' : 'bi-arrow-down'} mr-1`}></i>
        {isPositive ? '+' : ''}{numericPercentage}%
      </div>
    );
  };

  const handleExport = () => {
    try {
      if (!services || services.length === 0) {
        toast.error('No services to export');
        return;
      }

      // Prepare comprehensive data for export
      const exportData = services.map(service => ({
        'Service Number': service.serviceNumber || 'N/A',
        'Customer': service.customer || 'N/A',
        'Customer ID': service.customerId || 'N/A',
        'Contact Person': service.contactPerson || 'N/A',
        'Service Type': service.type || 'N/A',
        'Call Billing Type': formatCallTypeDisplay(service.type) || 'N/A',
        'Priority': service.priority || 'N/A',
        'Status': service.status || 'N/A',
        'Assigned To': service.assignedTo || 'Unassigned',
        'Created Date': service.createdDate ? new Date(service.createdDate).toLocaleDateString() : 'N/A',
        'Scheduled Date': service.scheduledDate ? new Date(service.scheduledDate).toLocaleDateString() : 'N/A',
        'Completed Date': service.completedDate ? new Date(service.completedDate).toLocaleDateString() : 'N/A',
        'Amount': service.amount ? `₹${service.amount.toLocaleString()}` : '₹0',
        'Estimated Hours': service.estimatedHours || '0',
        'Actual Hours': service.actualHours || '0',
        'Total Time (HH:MM:SS)': formatTimeDisplay(service.totalTimeSeconds || 0),
        'Total Time (Minutes)': service.totalTimeMinutes || '0',
        'Location': service.location || 'N/A',
        'Is Overdue': service.isOverdue ? 'Yes' : 'No',
        'Days Overdue': service.daysOverdue || '0',
        'Timer Status': service.time_tracking_summary?.is_timer_running ? 'Running' :
                       service.time_tracking_summary?.is_timer_paused ? 'Paused' : 'Stopped'
      }));

      // Convert to CSV with proper escaping
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = row[header] || '';
            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            const stringValue = value.toString();
            if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
              return `"${stringValue.replace(/"/g, '""')}"`;
            }
            return stringValue;
          }).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `services_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(`Exported ${exportData.length} services successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export services');
    }
  };

  // Card View Component
  const ServiceCard = ({ service }) => (
    <Card className="hover:shadow-lg transition-all duration-200 border border-gray-200">
      <CardBody className="p-4">
        {service.isOverdue && (
          <div className="mb-3 px-2 py-1 bg-red-600 text-white text-xs rounded font-bold text-center">
            ⚠️ {service.daysOverdue} day{service.daysOverdue > 1 ? 's' : ''} overdue
          </div>
        )}
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-start">
            <div className="w-10 h-10 theme-icon-bg rounded-full flex items-center justify-center mr-3 mt-1">
              <span className="theme-icon-text font-bold text-sm">#</span>
            </div>
            <div className="flex-1">
              <h6 className="font-bold text-gray-900 mb-1">{service.serviceNumber}</h6>
              {service.description && (
                <p className="text-xs text-gray-600 mb-2 leading-relaxed">{service.description}</p>
              )}
              <p className="text-sm text-gray-600 mb-0">{service.customer}</p>
            </div>
          </div>
          <div className="relative action-dropdown">
            <button
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                toggleDropdown(service.id);
              }}
            >
              <FaEllipsisV className="h-4 w-4" />
            </button>
            {dropdownOpen[service.id] && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 dropdown-menu">
                <div className="py-1">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate(`/services/${service.id}`);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEye className="mr-3 h-4 w-4 theme-icon-text" />
                    View Details
                  </button>



                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate(`/services/${service.id}/edit`);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEdit className="mr-3 h-4 w-4 theme-icon-text" />
                    Edit
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-danger-600 hover:bg-danger-50"
                    onClick={() => {
                      handleDelete(service.id);
                      setDropdownOpen({});
                    }}
                  >
                    <FaTrash className="mr-3 h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Call Type:</span>
            <div className="flex items-center">
              {getCallTypeIcon(service.type)}
              <span className="ml-2 text-sm font-medium">{formatCallTypeDisplay(service.type)}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            <button
              className="cursor-pointer hover:opacity-80 transition-opacity relative"
              onClick={(e) => {
                e.stopPropagation();
                openStatusModal(service, e);
              }}
              disabled={statusUpdateLoading[service.id] || service.statusObject?.code === 'COMPLETED'}
              title="Click to change status"
            >
              {statusUpdateLoading[service.id] ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-500 border-t-transparent mr-1"></div>
                  <span className="text-blue-600 text-xs">Updating...</span>
                </div>
              ) : (
                getStatusBadge(service.status)
              )}
            </button>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Amount:</span>
            <div className="flex items-center">
              <span className="font-bold text-black">₹{service.amount?.toLocaleString() || '0'}</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Time:</span>
            <TimerDisplay service={service} />
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Scheduled:</span>
            <div className="flex items-center px-2 py-1 rounded-lg bg-gray-50">
              <FaCalendar className={`mr-1 h-3 w-3 ${
                service.isOverdue ? 'text-red-600' : 'text-gray-600'
              }`}
              />
              <span className={`text-xs font-medium ${
                service.isOverdue ? 'text-red-600 font-bold' : 'text-gray-700'
              }`}
              >
                {new Date(service.scheduledDate).toLocaleDateString()}
              </span>
              {service.isOverdue && (
                <span className="ml-2 px-1 py-0.5 bg-red-600 text-white text-xs rounded font-bold">
                  {service.daysOverdue}d overdue
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Created:</span>
            <div className="flex items-center px-2 py-1 rounded-lg bg-gray-50">
              <FaCalendar className="mr-1 h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-700">
                {new Date(service.createdDate).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Services..."
        subtitle="Fetching service requests and tracking progress"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 service-list-container">
      <div className="w-full max-w-full mx-auto">
        {/* Colorful Header */}
        <div className="mb-6 sm:mb-8">
          <div className="header-gradient shadow-xl p-3 sm:p-4 lg:p-6 text-white rounded-lg">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4">
              <div className="flex-1 min-w-0">
                <h2 className="text-base sm:text-lg lg:text-xl xl:text-2xl font-bold mb-1 sm:mb-2 flex items-center">
                  <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center mr-2 sm:mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaTools className="text-xs sm:text-sm lg:text-base" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  <span className="truncate">Service Management</span>
                </h2>
                <p className="text-xs sm:text-sm hidden sm:block" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Manage service requests and track progress efficiently</p>
              </div>
              <div className="flex flex-row gap-1 sm:gap-2 lg:gap-3 w-full lg:w-auto">
                <button
                  className="inline-flex items-center justify-center px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 lg:py-2.5 border-2 text-xs font-medium rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed flex-1 sm:flex-none min-w-0"
                  style={{
                    borderColor: themeManager.getContrastingTextColor(currentThemeColor),
                    color: themeManager.getContrastingTextColor(currentThemeColor),
                    backgroundColor: `${themeManager.getContrastingTextColor(currentThemeColor)}20`,
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = `${themeManager.getContrastingTextColor(currentThemeColor)}30`;
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = `${themeManager.getContrastingTextColor(currentThemeColor)}20`;
                  }}
                  onClick={handleExport}
                  disabled={services.length === 0}
                >
                  <FaDownload className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate text-xs">Export</span>
                </button>
                <Link
                  to="/customers/add"
                  className="inline-flex items-center justify-center px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 lg:py-2.5 border border-transparent text-xs font-medium rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 shadow-lg flex-1 sm:flex-none min-w-0"
                  style={{
                    backgroundColor: '#ffffff',
                    color: 'black'
                  }}
                >
                  <FaPlus className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate text-xs">Add Customer</span>
                </Link>
                <Link
                  to="/services/add"
                  className="inline-flex items-center justify-center px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 lg:py-2.5 border border-transparent text-xs font-medium rounded-lg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 shadow-lg flex-1 sm:flex-none min-w-0"
                  style={{
                    backgroundColor: '#ffffff',
                    color: 'black'
                  }}
                >
                  <FaPlus className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate text-xs">New Service</span>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Error Banner for Stats API Failure */}
        {serviceStats.metadata?.hasError && (
          <div className="mb-6 bg-orange-50 border-l-4 border-orange-400 p-4 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <i className="bi bi-exclamation-triangle text-orange-400 text-lg"></i>
              </div>
              <div className="ml-3">
                <p className="text-sm text-orange-700">
                  <strong>Data Warning:</strong> Some statistics may be temporarily unavailable.
                  Displaying calculated values from current data.
                </p>
                {serviceStats.metadata.errorMessage && (
                  <p className="text-xs text-orange-600 mt-1">
                    {serviceStats.metadata.errorMessage}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Quick Stats - 8 Cards in 2 Rows */}
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
          {/* Row 1 - Status Cards */}
          <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {statsLoading ? (
                    <div className="w-12 h-5 bg-white/20 animate-pulse rounded mb-0"></div>
                  ) : (
                    <h4 className="text-base sm:text-lg lg:text-xl font-bold mb-0 truncate">
                      {getServiceCount(serviceStats.totalCalls, () => true)}
                      {serviceStats.metadata?.hasError && (
                        <span
                          className="ml-2 text-yellow-300 cursor-help"
                          title={serviceStats.metadata.errorMessage || 'Data temporarily unavailable'}
                        >
                          ⚠️
                        </span>
                      )}
                    </h4>
                  )}
                  <p className="mb-0 text-xs truncate" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Services</p>
                </div>
                <div className="rounded-lg p-1.5 sm:p-2 ml-2" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                  <FaTools className="h-4 w-4 sm:h-5 sm:w-5" style={{ color: 'var(--primary-text, #ffffff)' }} />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                <div className={`px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full text-xs font-medium flex items-center ${
                  (serviceStats.monthlyGrowth?.total || 0) >= 0
                    ? 'bg-success-500/20 text-success-300'
                    : 'bg-danger-500/20 text-danger-300'
                }`}
                >
                  <i className={`bi ${(serviceStats.monthlyGrowth?.total || 0) >= 0 ? 'bi-arrow-up' : 'bi-arrow-down'} mr-1`}></i>
                  {(serviceStats.monthlyGrowth?.total || 0) >= 0 ? '+' : ''}{serviceStats.monthlyGrowth?.total || 0}%
                </div>
                <span className="text-xs ml-1 sm:ml-2 truncate" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.7)' }}>vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getStatusCount('in-progress'),
                    'In Progress',
                    'clock',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaClock className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.inProgress, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getStatusCount('scheduled'),
                    'Scheduled',
                    'calendar',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaCalendar className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.scheduled, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getStatusCount('completed'),
                    'Completed',
                    'check',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaUser className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.completed, 'green')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>

          {/* Row 2 - Call Type Cards */}
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getCallTypeCount('free', 'freeCalls'),
                    'Free Calls',
                    'user',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaUser className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.freeCalls, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getCallTypeCount('amc', 'amcCalls'),
                    'AMC Calls',
                    'tools',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaTools className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.amcCalls, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getCallTypeCount('paid', 'paidCalls'),
                    'Per Calls',
                    'rupee',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaRupeeSign className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.paidCalls, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  {renderCardContent(
                    getServiceCount(serviceStats.recentCalls, () => true),
                    'Recent Calls',
                    'chart-line',
                    true
                  )}
                </div>
                <div className="white-stats-icon-bg rounded-lg p-1.5 sm:p-2 ml-2">
                  <FaChartLine className="h-4 w-4 sm:h-5 sm:w-5 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                {formatGrowthPercentage(serviceStats.monthlyGrowth?.recentCalls, 'theme')}
                <span className="text-gray-500 text-xs ml-1 sm:ml-2 truncate">last 30 days</span>
              </div>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <div className="bg-white shadow-xl mb-6 border-2 rounded-lg" style={{ borderColor: currentThemeColor }}>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 p-3 sm:p-4 laptop:p-3">
            {/* Search */}
            <div>
              <label className="block text-xs sm:text-sm laptop:text-sm font-bold mb-2 laptop:mb-1.5" style={{ color: currentThemeColor }}>🔍 Search</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-3 w-3 sm:h-4 sm:w-4" style={{ color: currentThemeColor }} />
                </div>
                <input
                  type="text"
                  className="block w-full pl-8 sm:pl-10 pr-3 py-2 sm:py-2.5 laptop:py-2 border-2 rounded-lg text-xs sm:text-sm laptop:text-sm bg-white transition-all duration-200 focus:outline-none focus:ring-2"
                  style={{
                    borderColor: currentThemeColor,
                    '--tw-ring-color': currentThemeColor
                  }}
                  placeholder="Search services..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="relative filter-dropdown" style={{ zIndex: 4000 }}>
              <label className="block text-xs sm:text-sm laptop:text-sm font-bold mb-2 laptop:mb-1.5" style={{ color: currentThemeColor }}>📊 Status</label>
              <select
                className="block w-full px-3 py-2 sm:py-2.5 laptop:py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white transition-all duration-200 text-xs sm:text-sm laptop:text-sm z-dropdown"
                style={{
                  borderColor: currentThemeColor,
                  '--tw-ring-color': currentThemeColor,
                  zIndex: 4001
                }}
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                disabled={filtersLoading || loading}
              >
                <option value="all">All Status</option>
                {filtersLoading ? (
                  <option disabled>Loading...</option>
                ) : (
                  filterOptions.statuses.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))
                )}
              </select>
              {filtersLoading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-2 border-t-transparent" style={{ borderColor: currentThemeColor }}></div>
                </div>
              )}
            </div>

            {/* Service Type Filter */}
            <div className="relative filter-dropdown" style={{ zIndex: 3000 }}>
              <label className="block text-xs sm:text-sm laptop:text-sm font-bold mb-2 laptop:mb-1.5" style={{ color: currentThemeColor }}>🛠️ Type</label>
              <select
                className="block w-full px-3 py-2 sm:py-2.5 laptop:py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 bg-white transition-all duration-200 text-xs sm:text-sm laptop:text-sm z-dropdown"
                style={{
                  borderColor: currentThemeColor,
                  '--tw-ring-color': currentThemeColor,
                  zIndex: 3001
                }}
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                disabled={filtersLoading || loading}
              >
                <option value="all">All Types</option>
                {filtersLoading ? (
                  <option disabled>Loading...</option>
                ) : (
                  filterOptions.callBillingTypes?.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  )) || []
                )}
              </select>
              {filtersLoading && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-2 border-t-transparent" style={{ borderColor: currentThemeColor }}></div>
                </div>
              )}
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-xs sm:text-sm laptop:text-sm font-bold mb-2 laptop:mb-1.5" style={{ color: currentThemeColor }}>📅 Date Range</label>
              <DateRangeFilter
                onDateRangeChange={handleDateRangeChange}
                label="Date"
              />
            </div>
          </div>

          {/* Filter Summary */}
          {(searchTerm || filterStatus !== 'all' || filterType !== 'all' || dateRange) && (
            <div className="mt-3 pt-3 border-t border-gray-200 px-3 sm:px-4 laptop:px-3 pb-3 sm:pb-4 laptop:pb-3">
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-gray-600">Active filters:</span>
                {searchTerm && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-theme-100 text-theme-800">
                    Search: "{searchTerm}"
                    <button
                      onClick={() => setSearchTerm('')}
                      className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-theme-400 hover:bg-theme-200 hover:text-theme-600"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filterStatus !== 'all' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-theme-100 text-theme-800">
                    Status: {filterOptions.statuses.find(s => s.value === filterStatus)?.label || filterStatus}
                    <button
                      onClick={() => setFilterStatus('all')}
                      className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-theme-400 hover:bg-theme-200 hover:text-theme-600"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filterType !== 'all' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-theme-100 text-theme-800">
                    Type: {filterOptions.callBillingTypes?.find(t => t.value === filterType)?.label || filterType}
                    <button
                      onClick={() => setFilterType('all')}
                      className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-theme-400 hover:bg-theme-200 hover:text-theme-600"
                    >
                      ×
                    </button>
                  </span>
                )}
                {dateRange && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-theme-100 text-theme-800">
                    Date: {dateRange.startDate.toLocaleDateString()} - {dateRange.endDate.toLocaleDateString()}
                    <button
                      onClick={() => setDateRange(null)}
                      className="ml-1.5 inline-flex items-center justify-center w-4 h-4 rounded-full text-theme-400 hover:bg-theme-200 hover:text-theme-600"
                    >
                      ×
                    </button>
                  </span>
                )}
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterStatus('all');
                    setFilterType('all');
                    setDateRange(null);
                  }}
                  className="text-xs text-gray-500 hover:text-gray-700 underline"
                >
                  Clear all
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Unified Control Area - Filter & View Controls */}
        <div className="bg-white shadow-xl mb-6 border-2 rounded-lg" style={{ borderColor: currentThemeColor }}>
          <div className="flex items-center justify-between p-3 sm:p-4 laptop:p-3">
            {/* Left side - Filter Controls */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <span className="text-xs sm:text-sm laptop:text-sm font-bold" style={{ color: currentThemeColor }}>
                  🎛️ Controls:
                </span>
              </div>

              {/* Advanced Filter Button - Could be expanded in future */}
              <button
                className="inline-flex items-center px-3 py-2 sm:py-2.5 laptop:py-2 border-2 rounded-lg text-xs sm:text-sm laptop:text-sm font-medium transition-all duration-200 hover:opacity-80 focus:outline-none focus:ring-2"
                style={{
                  borderColor: currentThemeColor,
                  backgroundColor: 'transparent',
                  color: currentThemeColor,
                  '--tw-ring-color': currentThemeColor
                }}
                title="Advanced filtering options"
                disabled
              >
                <FaFilter className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                Advanced Filters
              </button>
            </div>

            {/* Right side - View Mode Toggle (Hidden on mobile) */}
            <div className="hidden sm:flex items-center gap-3">
              <span className="text-xs sm:text-sm laptop:text-sm font-bold" style={{ color: currentThemeColor }}>
                👁️ View:
              </span>
              <div className="flex rounded-lg border-2 bg-white overflow-hidden" style={{ borderColor: currentThemeColor }}>
                <button
                  className={`inline-flex items-center justify-center px-3 py-2 sm:py-2.5 laptop:py-2 text-xs sm:text-sm laptop:text-sm font-medium transition-all duration-200 ${
                    viewMode === 'table' ? 'text-white' : 'hover:opacity-80'
                  }`}
                  style={{
                    backgroundColor: viewMode === 'table' ? currentThemeColor : 'transparent',
                    color: viewMode === 'table' ? '#ffffff' : currentThemeColor
                  }}
                  onClick={() => setViewMode('table')}
                  title="Table View"
                >
                  <FaList className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  Table
                </button>
                <button
                  className={`inline-flex items-center justify-center px-3 py-2 sm:py-2.5 laptop:py-2 text-xs sm:text-sm laptop:text-sm font-medium transition-all duration-200 ${
                    viewMode === 'card' ? 'text-white' : 'hover:opacity-80'
                  }`}
                  style={{
                    backgroundColor: viewMode === 'card' ? currentThemeColor : 'transparent',
                    color: viewMode === 'card' ? '#ffffff' : currentThemeColor
                  }}
                  onClick={() => setViewMode('card')}
                  title="Card View"
                >
                  <FaTh className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  Cards
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Conditional View Rendering */}
        {/* Table View - Hidden on mobile */}
        {viewMode === 'table' && (
          <div className="hidden sm:block w-full">
            <ResponsiveTable
              columns={[
                {
                  header: '🔢 Service #',
                  key: 'serviceNumber',
                  width: '15%',
                  priority: 'high',
                  render: (service) => (
                    <div className="min-w-0">
                      <div className="text-xs font-bold text-gray-900">{service.serviceNumber}</div>
                    </div>
                  )
                },
                {
                  header: '👤 Customer',
                  key: 'customer',
                  width: '17%',
                  priority: 'high',
                  render: (service) => (
                    <div className="min-w-0">
                      <div className="text-xs font-bold text-gray-900 truncate" title={service.customer}>{service.customer}</div>
                      <div className="text-xs text-gray-600 truncate" title={service.contactPerson}>{service.contactPerson}</div>
                    </div>
                  )
                },
                {
                  header: '🔢 Tally Serial',
                  key: 'tallySerial',
                  width: '12%',
                  priority: 'medium',
                  render: (service) => (
                    <div className="min-w-0">
                      <div className="text-xs font-medium text-gray-900 truncate" title={service.tallySerialNumber || 'Not specified'}>
                        {service.tallySerialNumber || 'N/A'}
                      </div>
                    </div>
                  )
                },
                {
                  header: '💳 Type',
                  key: 'type',
                  width: '10%',
                  priority: 'medium',
                  render: (service) => (
                    <button
                      className="text-xs font-medium text-gray-900 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded transition-colors cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        openTypeModal(service, e);
                      }}
                      title="Click to change type"
                    >
                      {formatCallTypeDisplay(service.type)}
                    </button>
                  )
                },
                {
                  header: '👨‍💼 Assigned',
                  key: 'assignedTo',
                  width: '15%',
                  priority: 'high',
                  render: (service) => (
                    <button
                      className="text-xs font-medium text-gray-900 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded transition-colors cursor-pointer truncate w-full text-left"
                      onClick={(e) => {
                        e.stopPropagation();
                        openAssignedModal(service, e);
                      }}
                      title="Click to change assigned executive"
                    >
                      {service.assignedTo || 'Unassigned'}
                    </button>
                  )
                },
                {
                  header: '📊 Status',
                  key: 'status',
                  width: '13%',
                  priority: 'high',
                  render: (service) => (
                    <button
                      className="cursor-pointer hover:opacity-80 transition-opacity relative"
                      onClick={(e) => {
                        e.stopPropagation();
                        openStatusModal(service, e);
                      }}
                      disabled={statusUpdateLoading[service.id] || service.statusObject?.code === 'COMPLETED'}
                      title="Click to change status"
                    >
                      {statusUpdateLoading[service.id] ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent mr-2"></div>
                          <span className="text-blue-600 text-sm">Updating...</span>
                        </div>
                      ) : (
                        getStatusBadge(service.status)
                      )}
                    </button>
                  )
                },
                {
                  header: '⏱️ Time',
                  key: 'time',
                  width: '11%',
                  priority: 'medium',
                  render: (service) => (
                    <div className="w-full overflow-hidden">
                      <TimerDisplay service={service} />
                    </div>
                  )
                },
                {
                  header: '📅 Date',
                  key: 'scheduledDate',
                  width: '13%',
                  priority: 'medium',
                  render: (service) => (
                    <div className="min-w-0">
                      <div className="flex items-center justify-start">
                        <FaCalendar className={`mr-1 h-2 w-2 ${
                          service.isOverdue ? 'text-red-600' : 'theme-icon-text'
                        }`} />
                        <span className={`text-xs ${
                          service.isOverdue ? 'text-red-600 font-bold' : 'theme-icon-text'
                        }`}>
                          {new Date(service.scheduledDate).toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: '2-digit'
                          })}
                        </span>
                        {service.isOverdue && (
                          <span className="ml-1 px-1 py-0.5 bg-red-600 text-white text-xs rounded font-bold">
                            {service.daysOverdue}d
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Created: {new Date(service.createdDate).toLocaleDateString('en-GB', {
                          day: '2-digit',
                          month: '2-digit'
                        })}
                      </div>
                    </div>
                  )
                },
                {
                  header: '⚙️ Actions',
                  key: 'actions',
                  width: '15%',
                  priority: 'high',
                  truncate: false,
                  render: (service) => (
                    <div className="flex space-x-1 justify-center items-center">
                      <button
                        className="p-2 theme-icon-text hover:text-theme-800 theme-icon-bg hover:bg-theme-200 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        onClick={() => navigate(`/services/${service.id}`)}
                        title="View Details"
                      >
                        <FaEye className="h-3.5 w-3.5" />
                      </button>
                      <button
                        className="p-2 theme-icon-text hover:text-theme-800 theme-icon-bg hover:bg-theme-200 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        onClick={() => handleShowTimerHistory(service)}
                        title="Timer History"
                      >
                        <FaHistory className="h-3.5 w-3.5" />
                      </button>
                      <button
                        className="p-2 theme-icon-text hover:text-theme-800 theme-icon-bg hover:bg-theme-200 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        onClick={() => navigate(`/services/${service.id}/edit`)}
                        title="Edit"
                      >
                        <FaEdit className="h-3.5 w-3.5" />
                      </button>
                      <button
                        className="p-2 text-danger-600 hover:text-danger-800 bg-danger-50 hover:bg-danger-100 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                        onClick={() => handleDelete(service.id)}
                        title="Delete"
                      >
                        <FaTrash className="h-3.5 w-3.5" />
                      </button>
                    </div>
                  )
                }
              ]}
              data={services}
              loading={loading}
              laptopOptimized={true}
              compactMode={true}
              showTooltips={true}
              emptyMessage={
                searchTerm || filterStatus !== 'all' || filterType !== 'all' || dateRange
                  ? 'No services match your current filters. Try adjusting your search criteria.'
                  : 'Get started by creating your first service request.'
              }
            />
          </div>
        )}

        {/* Mobile Card View - Always show on mobile */}
        <div className="sm:hidden">
          {services.length === 0 ? (
            <div className="bg-white shadow-xl p-12 text-center border border-gray-100 rounded-lg">
              <div className="w-24 h-24 mx-auto mb-6 theme-icon-bg rounded-full flex items-center justify-center">
                <FaTools className="h-12 w-12 theme-icon-text" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">No Services Found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterStatus !== 'all' || filterType !== 'all' || dateRange
                  ? 'No services match your current filters. Try adjusting your search criteria.'
                  : 'Get started by creating your first service request.'}
              </p>
              <Link
                to="/services/add"
                className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-theme-600 hover:bg-theme-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-500 transition-all duration-200"
              >
                <FaPlus className="mr-2 h-4 w-4" />
                Create Service Request
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {services.map((service) => (
                <ServiceCard key={service.id} service={service} />
              ))}
            </div>
          )}
        </div>

        {/* Desktop Card View - Conditional based on viewMode */}
        {viewMode === 'card' && (
          <div className="hidden sm:block">
            {services.length === 0 ? (
              <div className="bg-white shadow-xl p-12 text-center border border-gray-100 rounded-lg">
                <div className="w-24 h-24 mx-auto mb-6 theme-icon-bg rounded-full flex items-center justify-center">
                  <FaTools className="h-12 w-12 theme-icon-text" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">No Services Found</h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || filterStatus !== 'all' || filterType !== 'all' || dateRange
                    ? 'No services match your current filters. Try adjusting your search criteria.'
                    : 'Get started by creating your first service request.'}
                </p>
                <Link
                  to="/services/add"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-theme-600 hover:bg-theme-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-500 transition-all duration-200"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Create Service Request
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 3xl:grid-cols-6 gap-6">
                {services.map((service) => (
                  <ServiceCard key={service.id} service={service} />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Enhanced Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 mt-6 border rounded-lg" style={{ backgroundColor: '#f8fafc', borderColor: '#e2e8f0' }}>
            <div className="flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  aria-label="Go to previous page"
                  className="relative inline-flex items-center px-4 py-2 border-2 text-sm font-medium rounded-lg bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    borderColor: '#d1d5db',
                    color: '#15579e'
                  }}
                  onMouseEnter={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = '#f9fafb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = 'white';
                    }
                  }}
                  onFocus={(e) => {
                    e.target.style.boxShadow = '0 0 0 2px rgba(21, 87, 158, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  aria-label="Go to next page"
                  className="ml-3 relative inline-flex items-center px-4 py-2 border-2 text-sm font-medium rounded-lg bg-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    borderColor: '#d1d5db',
                    color: '#15579e'
                  }}
                  onMouseEnter={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = '#f9fafb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!e.target.disabled) {
                      e.target.style.backgroundColor = 'white';
                    }
                  }}
                  onFocus={(e) => {
                    e.target.style.boxShadow = '0 0 0 2px rgba(21, 87, 158, 0.2)';
                  }}
                  onBlur={(e) => {
                    e.target.style.boxShadow = 'none';
                  }}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm font-medium" style={{ color: '#374151' }}>
                    📄 Showing page <span className="font-bold" style={{ color: '#15579e' }}>{currentPage}</span> of{' '}
                    <span className="font-bold" style={{ color: '#15579e' }}>{totalPages}</span>
                    {' '}({totalServices} total services)
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-lg shadow-lg -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      aria-label="Go to previous page"
                      className="relative inline-flex items-center px-3 py-2 rounded-l-lg border-2 bg-white text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        borderColor: '#d1d5db',
                        color: '#15579e'
                      }}
                      onMouseEnter={(e) => {
                        if (!e.target.disabled) {
                          e.target.style.backgroundColor = '#f9fafb';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!e.target.disabled) {
                          e.target.style.backgroundColor = 'white';
                        }
                      }}
                      onFocus={(e) => {
                        e.target.style.boxShadow = '0 0 0 2px rgba(21, 87, 158, 0.2)';
                      }}
                      onBlur={(e) => {
                        e.target.style.boxShadow = 'none';
                      }}
                    >
                      Previous
                    </button>
                    {/* Show limited page numbers for better UX */}
                    {(() => {
                      const maxVisiblePages = 5;
                      const startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
                      const pages = [];

                      for (let i = startPage; i <= endPage; i++) {
                        pages.push(i);
                      }

                      return pages.map((pageNum) => (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          aria-current={currentPage === pageNum ? 'page' : undefined}
                          aria-label={`${currentPage === pageNum ? 'Current page, ' : ''}Page ${pageNum}`}
                          className={`relative inline-flex items-center px-4 py-2 border-2 text-sm font-bold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                            currentPage === pageNum
                              ? 'z-10 text-white shadow-lg ring-2 ring-offset-2'
                              : 'bg-white text-black hover:bg-gray-50 focus:ring-gray-300'
                          }`}
                          style={currentPage === pageNum ? {
                            backgroundColor: '#15579e',
                            borderColor: '#15579e',
                            ringColor: '#15579e'
                          } : {
                            borderColor: '#d1d5db'
                          }}
                        >
                          {pageNum}
                        </button>
                      ));
                    })()}
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      aria-label="Go to next page"
                      className="relative inline-flex items-center px-3 py-2 rounded-r-lg border-2 bg-white text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        borderColor: '#d1d5db',
                        color: '#15579e'
                      }}
                      onMouseEnter={(e) => {
                        if (!e.target.disabled) {
                          e.target.style.backgroundColor = '#f9fafb';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!e.target.disabled) {
                          e.target.style.backgroundColor = 'white';
                        }
                      }}
                      onFocus={(e) => {
                        e.target.style.boxShadow = '0 0 0 2px rgba(21, 87, 158, 0.2)';
                      }}
                      onBlur={(e) => {
                        e.target.style.boxShadow = 'none';
                      }}
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Timer History Modal */}
        {selectedServiceForHistory && (
          <TimerHistoryModal
            isOpen={showTimerHistory}
            onClose={() => setShowTimerHistory(false)}
            serviceCallId={selectedServiceForHistory.id}
            serviceCallNumber={selectedServiceForHistory.serviceNumber}
          />
        )}

        {/* Status Change Modal */}
        {showStatusModal && selectedServiceForStatus && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 55000 }}>
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4" style={{ zIndex: 55001 }}>
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Change Status
                  </h3>
                  <button
                    onClick={() => setShowStatusModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Service: {selectedServiceForStatus.serviceNumber} - {selectedServiceForStatus.customer}
                </p>
              </div>

              <div className="px-6 py-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Status
                    </label>
                    <div className="flex items-center px-3 py-2 bg-gray-50 rounded-lg border">
                      <div
                        className="w-3 h-3 rounded-full mr-2 border border-gray-300"
                        style={{ backgroundColor: selectedServiceForStatus.statusObject?.color || '#6c757d' }}
                      ></div>
                      <span className="text-sm font-medium text-gray-900">
                        {selectedServiceForStatus.statusObject?.name || selectedServiceForStatus.status}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select New Status
                    </label>
                    {!Array.isArray(callStatuses) || callStatuses.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        No statuses available
                      </div>
                    ) : (
                      <div className="relative">
                        <select
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white"
                          defaultValue={selectedServiceForStatus.statusObject?.id || ''}
                          onChange={(e) => {
                            if (e.target.value && e.target.value !== selectedServiceForStatus.statusObject?.id?.toString()) {
                              console.log('🔄 Status modal change:', {
                                selectedValue: e.target.value,
                                valueType: typeof e.target.value,
                                currentStatusId: selectedServiceForStatus.statusObject?.id,
                                serviceId: selectedServiceForStatus.id
                              });
                              // CRITICAL FIX: Don't use parseInt() - keep UUID as string
                              handleStatusChange(selectedServiceForStatus.id, e.target.value);
                              setShowStatusModal(false);
                            }
                          }}
                        >
                          <option value="">Choose a status...</option>
                          {(Array.isArray(callStatuses) ? callStatuses : []).map((status) => (
                            <option
                              key={status.id}
                              value={status.id}
                              disabled={selectedServiceForStatus.statusObject?.id === status.id}
                            >
                              {status.name} {selectedServiceForStatus.statusObject?.id === status.id ? '(Current)' : ''}
                            </option>
                          ))}
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    )}
                  </div>


                </div>
              </div>

              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                <button
                  onClick={() => setShowStatusModal(false)}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Type Change Modal */}
        {showTypeModal && selectedServiceForType && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 55000 }}>
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4" style={{ zIndex: 55001 }}>
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Change Service Type
                  </h3>
                  <button
                    onClick={() => setShowTypeModal(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Service: {selectedServiceForType.serviceNumber} - {selectedServiceForType.customer}
                </p>
              </div>

              <div className="px-6 py-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Type
                    </label>
                    <div className="flex items-center px-3 py-2 bg-gray-50 rounded-lg border">
                      <span className="text-sm font-medium text-gray-900">
                        {formatCallTypeDisplay(selectedServiceForType.type)}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select New Type
                    </label>
                    <div className="space-y-2">
                      {['free_call', 'per_call', 'amc_call'].map((type) => (
                        <button
                          key={type}
                          onClick={() => {
                            if (type !== selectedServiceForType.type) {
                              updateServiceType(selectedServiceForType.id, type);
                            }
                          }}
                          disabled={typeUpdateLoading[selectedServiceForType.id] || type === selectedServiceForType.type}
                          className={`w-full px-4 py-3 text-left rounded-lg border transition-colors ${
                            type === selectedServiceForType.type
                              ? 'bg-blue-50 border-blue-200 text-blue-700 cursor-not-allowed'
                              : 'bg-white border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">
                              {formatCallTypeDisplay(type)}
                            </span>
                            {type === selectedServiceForType.type && (
                              <span className="text-xs text-blue-600">(Current)</span>
                            )}
                            {typeUpdateLoading[selectedServiceForType.id] && (
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                <button
                  onClick={() => setShowTypeModal(false)}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Assigned Executive Change Modal */}
        {showAssignedModal && selectedServiceForAssigned && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center" style={{ zIndex: 55000 }}>
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4" style={{ zIndex: 55001 }}>
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Assign Executive
                  </h3>
                  <button
                    onClick={() => {
                      setShowAssignedModal(false);
                      setExecutiveSearchTerm('');
                      setExecutiveSearchResults([]);
                    }}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  Service: {selectedServiceForAssigned.serviceNumber} - {selectedServiceForAssigned.customer}
                </p>
              </div>

              <div className="px-6 py-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Currently Assigned
                    </label>
                    <div className="flex items-center px-3 py-2 bg-gray-50 rounded-lg border">
                      <span className="text-sm font-medium text-gray-900">
                        {selectedServiceForAssigned.assignedTo || 'Unassigned'}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Search Executive
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={executiveSearchTerm}
                        onChange={(e) => handleExecutiveSearchChange(e.target.value)}
                        placeholder="Search by name, mobile, or employee code..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      {isSearchingExecutives && (
                        <div className="absolute right-3 top-2.5">
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Executive Search Results */}
                  {executiveSearchTerm.length >= 2 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Search Results
                      </label>
                      <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-lg">
                        {executiveSearchResults.length === 0 && !isSearchingExecutives ? (
                          <div className="px-4 py-3 text-sm text-gray-500 text-center">
                            No executives found
                          </div>
                        ) : (
                          executiveSearchResults.map((executive) => (
                            <button
                              key={executive.id}
                              onClick={() => {
                                updateServiceAssigned(
                                  selectedServiceForAssigned.id,
                                  executive.id,
                                  executive.name
                                );
                              }}
                              disabled={assignedUpdateLoading[selectedServiceForAssigned.id]}
                              className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-200 last:border-b-0 transition-colors"
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="font-medium text-gray-900">{executive.name}</div>
                                  <div className="text-sm text-gray-500">
                                    {executive.employee_code && `Code: ${executive.employee_code}`}
                                    {executive.phone && ` • Phone: ${executive.phone}`}
                                  </div>
                                </div>
                                {assignedUpdateLoading[selectedServiceForAssigned.id] && (
                                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                                )}
                              </div>
                            </button>
                          ))
                        )}
                      </div>
                    </div>
                  )}

                  {/* Default Executives List (when no search) */}
                  {executiveSearchTerm.length < 2 && executives.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Available Executives
                      </label>
                      <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-lg">
                        {executives.slice(0, 10).map((executive) => (
                          <button
                            key={executive.id}
                            onClick={() => {
                              updateServiceAssigned(
                                selectedServiceForAssigned.id,
                                executive.id,
                                executive.name
                              );
                            }}
                            disabled={assignedUpdateLoading[selectedServiceForAssigned.id]}
                            className="w-full px-4 py-3 text-left hover:bg-gray-50 border-b border-gray-200 last:border-b-0 transition-colors"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-gray-900">{executive.name}</div>
                                <div className="text-sm text-gray-500">
                                  {executive.employee_code && `Code: ${executive.employee_code}`}
                                  {executive.phone && ` • Phone: ${executive.phone}`}
                                </div>
                              </div>
                              {assignedUpdateLoading[selectedServiceForAssigned.id] && (
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                <button
                  onClick={() => {
                    setShowAssignedModal(false);
                    setExecutiveSearchTerm('');
                    setExecutiveSearchResults([]);
                  }}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ServiceList;
