// Test script to verify service analytics API
const API_BASE_URL = 'http://localhost:8080/api/v1';

async function testServiceAnalyticsAPI() {
    console.log('🧪 Testing Service Analytics API...');
    
    try {
        const response = await fetch(`${API_BASE_URL}/reports/service-analytics?dateRange=30&groupBy=day`);
        
        if (!response.ok) {
            console.error(`❌ API failed with status: ${response.status}`);
            const errorText = await response.text();
            console.error('Error response:', errorText);
            return;
        }
        
        const data = await response.json();
        console.log('✅ API Response received');
        console.log('📊 Response structure:', {
            success: data.success,
            hasData: !!data.data,
            hasSummary: !!data.data?.summary
        });
        
        if (data.success && data.data && data.data.summary) {
            console.log('📈 Summary data:', {
                totalCalls: data.data.summary.totalCalls,
                freeCalls: data.data.summary.freeCalls,
                amcCalls: data.data.summary.amcCalls,
                paidCalls: data.data.summary.paidCalls
            });
        } else {
            console.log('⚠️ Unexpected data structure:', data);
        }
        
    } catch (error) {
        console.error('❌ API test failed:', error.message);
    }
}

// Run the test
testServiceAnalyticsAPI();
