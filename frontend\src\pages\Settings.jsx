import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button } from '../components/ui';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>ser, FaBell, FaLock, FaDatabase, FaPalette, FaSave, FaEye, FaEyeSlash, FaUpload, FaTrash } from 'react-icons/fa';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';
import useAppStore from '../store/appStore';
import themeManager from '../utils/themeManager';
import { useUserPreferences } from '../hooks/useUserPreferences';
import EmailTemplates from '../components/EmailTemplates';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [profileImagePreview, setProfileImagePreview] = useState(null);

  // Use user preferences hook for theme management
  const { preferences: userPrefs, updateThemeColor } = useUserPreferences(true);
  const { theme, setTheme } = useAppStore();

  const [settings, setSettings] = useState({
    // General Settings
    companyName: '',
    companyEmail: '',
    companyPhone: '',
    companyAddress: '',
    timezone: 'Asia/Kolkata',
    dateFormat: 'DD/MM/YYYY',
    currency: 'INR',

    // Profile Settings
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    profileImage: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',

    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: false,
    weeklyReports: true,
    monthlyReports: true,
    serviceUpdates: true,
    salesAlerts: true,
    systemAlerts: true,

    // Security Settings
    twoFactorAuth: false,
    loginAlerts: true,
    sessionTimeout: '30',
    passwordExpiry: '90',

    // Database Settings
    backupFrequency: 'daily',
    retentionPeriod: '365',
    autoBackup: true,

    // Theme Settings
    primaryColor: '',
  });

  // Fetch settings data from API
  useEffect(() => {
    fetchSettingsData();
  }, []);

  // Sync settings state with user preferences
  useEffect(() => {
    if (userPrefs.loaded) {
      setSettings(prev => ({
        ...prev,
        primaryColor: userPrefs.primaryColor || ''
      }));
    }
  }, [userPrefs.primaryColor, userPrefs.loaded]);

  const fetchSettingsData = async () => {
    try {
      setLoading(true);

      // Fetch tenant settings, user preferences, and profile data
      const [tenantRes, userRes, profileRes] = await Promise.all([
        apiService.get('/settings/tenant').catch(() => ({ data: { data: { settings: {} } } })),
        apiService.get('/settings/user').catch(() => ({ data: { data: { preferences: {} } } })),
        apiService.get('/profile').catch(() => ({ data: { data: { user: {} } } }))
      ]);

      const tenantSettings = tenantRes.data?.data?.tenant || {};
      const userPreferences = userRes.data?.data?.user || {};
      const profileData = profileRes.data?.data?.user || {};

      console.log('Tenant settings loaded:', tenantSettings);
      console.log('User preferences loaded:', userPreferences);
      console.log('Profile data loaded:', profileData);
      console.log('Primary color from DB:', userPreferences.primary_color);

      // Debug: Log the full API responses
      console.log('Full tenant response:', tenantRes.data);
      console.log('Full user response:', userRes.data);
      console.log('Full profile response:', profileRes.data);

      setSettings(prev => ({
        ...prev,
        // General/Tenant Settings
        companyName: tenantSettings.name || '',
        companyEmail: tenantSettings.email || '',
        companyPhone: tenantSettings.phone || '',
        companyAddress: tenantSettings.address || '',
        timezone: tenantSettings.timezone || 'Asia/Kolkata',
        dateFormat: tenantSettings.date_format || 'DD/MM/YYYY',
        currency: tenantSettings.currency || 'INR',

        // Profile Settings
        firstName: profileData.first_name || '',
        lastName: profileData.last_name || '',
        email: profileData.email || '',
        phone: profileData.phone || '',
        profileImage: profileData.profile_image || '',

        // User Preferences
        emailNotifications: userPreferences.email_notifications !== false,
        pushNotifications: userPreferences.notifications_enabled !== false,
        primaryColor: userPreferences.primary_color || userPrefs.primaryColor || '',

        // Additional notification settings from preferences
        smsNotifications: userPreferences.preferences?.sms_notifications !== false,
        weeklyReports: userPreferences.preferences?.weekly_reports !== false,
        monthlyReports: userPreferences.preferences?.monthly_reports !== false,
        serviceUpdates: userPreferences.preferences?.service_updates !== false,
        salesAlerts: userPreferences.preferences?.sales_alerts !== false,
        systemAlerts: userPreferences.preferences?.system_alerts !== false,

        // Security settings from preferences
        twoFactorAuth: userPreferences.preferences?.two_factor_auth !== false,
        loginAlerts: userPreferences.preferences?.login_alerts !== false,
        sessionTimeout: userPreferences.preferences?.session_timeout || '30',
        passwordExpiry: userPreferences.preferences?.password_expiry || '90',
      }));

      if (profileData.profile_image) {
        setProfileImagePreview(profileData.profile_image);
      }

      // Theme color is now handled by useUserPreferences hook
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleProfileImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setProfileImage(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Use theme manager for color operations
  const adjustColorBrightness = (hex, percent) => {
    return themeManager.adjustBrightness(hex, percent);
  };

  const handleSave = async (section) => {
    try {
      setLoading(true);

      switch (section) {
        case 'general':
          await apiService.put('/settings/tenant', {
            name: settings.companyName,
            email: settings.companyEmail,
            phone: settings.companyPhone,
            address: settings.companyAddress,
            timezone: settings.timezone,
            date_format: settings.dateFormat,
            currency: settings.currency,
          });
          toast.success('General settings updated successfully');
          break;

        case 'profile':
          // Update profile data only (no password change)
          const profileData = {
            first_name: settings.firstName,
            last_name: settings.lastName,
            email: settings.email,
            phone: settings.phone,
          };

          // Handle profile image upload
          if (profileImage) {
            const formData = new FormData();
            Object.keys(profileData).forEach(key => {
              formData.append(key, profileData[key]);
            });
            formData.append('profile_image', profileImage);

            await apiService.put('/profile', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            });
          } else {
            await apiService.put('/profile', profileData);
          }

          toast.success('Profile updated successfully');
          setProfileImage(null);
          break;

        case 'password':
          // Handle password change separately
          if (!settings.currentPassword) {
            toast.error('Current password is required');
            return;
          }

          if (!settings.newPassword) {
            toast.error('New password is required');
            return;
          }

          if (settings.newPassword !== settings.confirmPassword) {
            toast.error('New passwords do not match');
            return;
          }

          if (settings.newPassword.length < 8) {
            toast.error('New password must be at least 8 characters long');
            return;
          }

          await apiService.post('/profile/change-password', {
            current_password: settings.currentPassword,
            new_password: settings.newPassword,
          });

          toast.success('Password changed successfully');
          // Clear password fields
          setSettings(prev => ({
            ...prev,
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          }));
          break;

        case 'notifications':
          await apiService.put('/settings/notifications', {
            email_notifications: settings.emailNotifications,
            sms_notifications: settings.smsNotifications,
            push_notifications: settings.pushNotifications,
            weekly_reports: settings.weeklyReports,
            monthly_reports: settings.monthlyReports,
            service_updates: settings.serviceUpdates,
            sales_alerts: settings.salesAlerts,
            system_alerts: settings.systemAlerts,
          });
          toast.success('Notification settings updated successfully');
          break;

        case 'theme':
          console.log('Saving theme color:', settings.primaryColor);
          const result = await updateThemeColor(settings.primaryColor);
          if (result.success) {
            toast.success('Theme settings updated successfully');
          } else {
            toast.error('Failed to update theme settings');
          }
          break;

        case 'security':
          await apiService.put('/settings/security', {
            two_factor_auth: settings.twoFactorAuth,
            login_alerts: settings.loginAlerts,
            session_timeout: parseInt(settings.sessionTimeout),
            password_expiry: parseInt(settings.passwordExpiry),
          });
          toast.success('Security settings updated successfully');
          break;

        case 'database':
          await apiService.put('/settings/backup', {
            backup_frequency: settings.backupFrequency,
            retention_period: parseInt(settings.retentionPeriod),
            auto_backup: settings.autoBackup,
          });
          toast.success('Database settings updated successfully');
          break;

        default:
          toast.success(`${section} settings updated successfully`);
      }
    } catch (error) {
      console.error(`Error saving ${section} settings:`, error);
      toast.error(`Failed to update ${section} settings`);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: FaCog },
    { id: 'profile', label: 'Profile', icon: FaUser },
    { id: 'theme', label: 'Theme', icon: FaPalette },
    { id: 'notifications', label: 'Notifications', icon: FaBell },
    { id: 'security', label: 'Security', icon: FaLock },
    { id: 'database', label: 'Database', icon: FaDatabase },
  ];

  const renderGeneralSettings = () => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="border-b border-gray-200 px-6 py-4">
        <h5 className="text-lg font-semibold text-gray-900 mb-0">General Settings</h5>
      </CardHeader>
      <CardBody className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg form-input-theme transition-all duration-200"
              value={settings.companyName}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Company Email</label>
            <input
              type="email"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg form-input-theme transition-all duration-200"
              value={settings.companyEmail}
              onChange={(e) => handleInputChange('companyEmail', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Company Phone</label>
            <input
              type="tel"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg form-input-theme transition-all duration-200"
              value={settings.companyPhone}
              onChange={(e) => handleInputChange('companyPhone', e.target.value)}
              disabled={loading}
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Company Address</label>
            <textarea
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              rows="3"
              value={settings.companyAddress}
              onChange={(e) => handleInputChange('companyAddress', e.target.value)}
              disabled={loading}
              placeholder="Enter company address"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
            <select
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.timezone}
              onChange={(e) => handleInputChange('timezone', e.target.value)}
            >
              <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
              <option value="America/New_York">America/New_York (EST)</option>
              <option value="Europe/London">Europe/London (GMT)</option>
              <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
            <select
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.dateFormat}
              onChange={(e) => handleInputChange('dateFormat', e.target.value)}
            >
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
            <select
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.currency}
              onChange={(e) => handleInputChange('currency', e.target.value)}
            >
              <option value="INR">INR (₹)</option>
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
            </select>
          </div>
        </div>
        <div className="mt-6 flex justify-end">
          <Button
            className="btn-theme-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
            onClick={() => handleSave('general')}
          >
            <FaSave className="mr-2" />
            Save Changes
          </Button>
        </div>
      </CardBody>
    </Card>
  );

  const renderProfileSettings = () => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="border-b border-gray-200 px-6 py-4">
        <h5 className="text-lg font-semibold text-gray-900 mb-0">Profile Settings</h5>
      </CardHeader>
      <CardBody className="p-6">
        {/* Profile Image Section */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Profile Image</label>
          <div className="flex items-center space-x-4">
            <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
              {profileImagePreview ? (
                <img
                  src={profileImagePreview}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <FaUser className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <div className="flex space-x-2">
              <label className="cursor-pointer">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleProfileImageChange}
                  className="hidden"
                  disabled={loading}
                />
                <span className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
                  <FaUpload className="mr-2" />
                  Upload
                </span>
              </label>
              {profileImagePreview && (
                <button
                  type="button"
                  onClick={() => {
                    setProfileImagePreview(null);
                    setProfileImage(null);
                  }}
                  className="inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-all duration-200"
                  disabled={loading}
                >
                  <FaTrash className="mr-2" />
                  Remove
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            <input
              type="tel"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                value={settings.currentPassword}
                onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                placeholder="Enter current password"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
                style={{ '--hover-color': 'var(--primary-color)' }}
                onMouseEnter={(e) => e.target.style.color = 'var(--primary-color)'}
                onMouseLeave={(e) => e.target.style.color = ''}
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FaEyeSlash /> : <FaEye />}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
            <input
              type="password"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.newPassword}
              onChange={(e) => handleInputChange('newPassword', e.target.value)}
              placeholder="Enter new password"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
            <input
              type="password"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              placeholder="Confirm new password"
            />
          </div>
        </div>

        {/* Profile Update Section */}
        <div className="mt-6 flex justify-between">
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
            onClick={() => handleSave('profile')}
            disabled={loading}
          >
            <FaSave className="mr-2" />
            Update Profile
          </Button>

          {/* Password Change Section */}
          {(settings.currentPassword || settings.newPassword || settings.confirmPassword) && (
            <Button
              className="btn-theme-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
              onClick={() => handleSave('password')}
              disabled={loading}
            >
              <FaLock className="mr-2" />
              Change Password
            </Button>
          )}
        </div>
      </CardBody>
    </Card>
  );

  const renderThemeSettings = () => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="border-b border-gray-200 px-6 py-4">
        <h5 className="text-lg font-semibold text-gray-900 mb-0">Theme Settings</h5>
      </CardHeader>
      <CardBody className="p-6">
        <div className="space-y-6">
          {/* Primary Color Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Primary Color</label>
            <div className="flex items-center space-x-4">
              <input
                type="color"
                className="w-16 h-16 border border-gray-300 rounded-lg focus-primary transition-all duration-200 cursor-pointer"
                value={settings.primaryColor}
                onChange={(e) => {
                  handleInputChange('primaryColor', e.target.value);
                  themeManager.applyTheme(e.target.value);
                }}
                disabled={loading}
              />
              <div className="flex-1">
                <input
                  type="text"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus-primary transition-all duration-200"
                  value={settings.primaryColor}
                  onChange={(e) => {
                    handleInputChange('primaryColor', e.target.value);
                    themeManager.applyTheme(e.target.value);
                  }}
                  disabled={loading}
                  placeholder="#2f69b3"
                />
                <p className="text-sm text-gray-500 mt-1">
                  This color will be applied to sidebar, buttons, links, and other primary elements. Text color will automatically adjust for optimal contrast.
                </p>
              </div>
            </div>
          </div>

          {/* Theme Preview Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Theme Preview</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Primary Button Preview */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <h6 className="text-sm font-medium text-gray-700 mb-3">Primary Button</h6>
                <button className="btn-primary px-4 py-2 rounded-lg font-medium transition-all duration-200">
                  Sample Button
                </button>
              </div>

              {/* Navigation Preview */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <h6 className="text-sm font-medium text-gray-700 mb-3">Navigation Style</h6>
                <div className="bg-primary-dynamic px-4 py-2 rounded-lg">
                  <span className="text-primary-dynamic">Navigation Item</span>
                </div>
              </div>

              {/* Stats Card Preview */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <h6 className="text-sm font-medium text-gray-700 mb-3">Stats Card</h6>
                <div className="stats-card-primary px-4 py-3 rounded-lg">
                  <div className="text-primary-dynamic font-semibold">Sample Stats</div>
                  <div className="text-primary-dynamic opacity-80 text-sm">Dynamic text color</div>
                </div>
              </div>

              {/* Header Preview */}
              <div className="p-4 border border-gray-200 rounded-lg">
                <h6 className="text-sm font-medium text-gray-700 mb-3">Header Style</h6>
                <div className="header-gradient px-4 py-2 rounded-lg">
                  <span className="text-primary-dynamic font-medium">Header Text</span>
                </div>
              </div>
            </div>
          </div>


          {/* Theme Preview */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Theme Preview</label>
            <div className="border border-gray-200 rounded-lg p-4 space-y-3">
              <div className="flex items-center space-x-3">
                <button
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    !settings.primaryColor ? 'bg-gray-200 text-gray-500' : ''
                  }`}
                  style={settings.primaryColor ? {
                    backgroundColor: settings.primaryColor,
                    color: themeManager.getContrastingTextColor(settings.primaryColor)
                  } : {}}
                >
                  {settings.primaryColor ? 'Primary Button' : 'Select a color first'}
                </button>
                <button
                  className={`px-4 py-2 border rounded-lg font-medium transition-all duration-200 ${
                    !settings.primaryColor ? 'border-gray-300 text-gray-500' : ''
                  }`}
                  style={settings.primaryColor ? {
                    borderColor: settings.primaryColor,
                    color: settings.primaryColor
                  } : {}}
                >
                  Outline Button
                </button>
              </div>
              <div className="flex items-center space-x-3">
                <a
                  href="#"
                  className={`font-medium hover:underline ${
                    !settings.primaryColor ? 'text-gray-500' : ''
                  }`}
                  style={settings.primaryColor ? { color: settings.primaryColor } : {}}
                >
                  Sample Link
                </a>
                <span
                  className={`px-2 py-1 rounded text-sm ${
                    !settings.primaryColor ? 'bg-gray-200 text-gray-500' : ''
                  }`}
                  style={settings.primaryColor ? {
                    backgroundColor: settings.primaryColor,
                    color: themeManager.getContrastingTextColor(settings.primaryColor)
                  } : {}}
                >
                  Badge
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Button
            className={`border-0 shadow-md hover:shadow-lg transition-all duration-200 ${
              !settings.primaryColor ? 'bg-gray-400 text-white cursor-not-allowed' : ''
            }`}
            style={settings.primaryColor ? {
              backgroundColor: settings.primaryColor,
              color: themeManager.getContrastingTextColor(settings.primaryColor)
            } : {}}
            onClick={() => handleSave('theme')}
            disabled={loading || !settings.primaryColor}
          >
            <FaSave className="mr-2" />
            {settings.primaryColor ? 'Save Theme' : 'Select a color to save'}
          </Button>
        </div>
      </CardBody>
    </Card>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader className="border-b border-gray-200 px-6 py-4">
          <h5 className="text-lg font-semibold text-gray-900 mb-0">Notification Settings</h5>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h6 className="text-sm font-medium text-gray-900">Email Notifications</h6>
                <p className="text-sm text-gray-500">Receive notifications via email</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.emailNotifications}
                  onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                  disabled={loading}
                />
                <div
                  className={`w-11 h-6 rounded-full peer transition-all duration-200 ${
                    settings.emailNotifications ? 'toggle-switch checked' : 'toggle-switch'
                  } peer-focus:outline-none peer-focus:ring-4 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}
                  style={{
                    backgroundColor: settings.emailNotifications ? 'var(--theme-icon-color)' : '#e5e7eb',
                    '--tw-ring-color': 'rgba(var(--primary-rgb), 0.3)'
                  }}
                >
                </div>
              </label>
            </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Push Notifications</h6>
              <p className="text-sm text-gray-500">Receive push notifications in browser</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.pushNotifications}
                onChange={(e) => handleInputChange('pushNotifications', e.target.checked)}
                disabled={loading}
              />
              <div
                className={'w-11 h-6 rounded-full peer transition-all duration-200 toggle-bg peer-focus:outline-none peer-focus:ring-4 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[\'\'] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all'}
                style={{
                  backgroundColor: settings.pushNotifications ? 'var(--theme-icon-color)' : '#e5e7eb'
                }}
              >
              </div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">SMS Notifications</h6>
              <p className="text-sm text-gray-500">Receive notifications via SMS</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.smsNotifications}
                onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Weekly Reports</h6>
              <p className="text-sm text-gray-500">Receive weekly summary reports</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.weeklyReports}
                onChange={(e) => handleInputChange('weeklyReports', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Monthly Reports</h6>
              <p className="text-sm text-gray-500">Receive monthly summary reports</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.monthlyReports}
                onChange={(e) => handleInputChange('monthlyReports', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Service Updates</h6>
              <p className="text-sm text-gray-500">Receive notifications about service updates</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.serviceUpdates}
                onChange={(e) => handleInputChange('serviceUpdates', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Sales Alerts</h6>
              <p className="text-sm text-gray-500">Receive notifications about sales activities</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.salesAlerts}
                onChange={(e) => handleInputChange('salesAlerts', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">System Alerts</h6>
              <p className="text-sm text-gray-500">Receive notifications about system events</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.systemAlerts}
                onChange={(e) => handleInputChange('systemAlerts', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
          <div className="mt-6 flex justify-end">
            <Button
              className="btn-theme-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
              onClick={() => handleSave('notifications')}
              disabled={loading}
            >
              <FaSave className="mr-2" />
              Save Notifications
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Email Templates Sub-section */}
      <Card className="bg-white border-0 shadow-lg">
        <CardHeader className="border-b border-gray-200 px-6 py-4">
          <h5 className="text-lg font-semibold text-gray-900 mb-0">Email Templates</h5>
        </CardHeader>
        <CardBody className="p-6">
          <EmailTemplates />
        </CardBody>
      </Card>
    </div>
  );

  const renderSecuritySettings = () => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="border-b border-gray-200 px-6 py-4">
        <h5 className="text-lg font-semibold text-gray-900 mb-0">Security Settings</h5>
      </CardHeader>
      <CardBody className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
            <input
              type="number"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.sessionTimeout}
              onChange={(e) => handleInputChange('sessionTimeout', e.target.value)}
              disabled={loading}
              min="5"
              max="480"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days)</label>
            <input
              type="number"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.passwordExpiry}
              onChange={(e) => handleInputChange('passwordExpiry', e.target.value)}
              disabled={loading}
              min="30"
              max="365"
            />
          </div>
        </div>
        <div className="mt-6 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h6>
              <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.twoFactorAuth}
                onChange={(e) => handleInputChange('twoFactorAuth', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Login Alerts</h6>
              <p className="text-sm text-gray-500">Get notified when someone logs into your account</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.loginAlerts}
                onChange={(e) => handleInputChange('loginAlerts', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
        <div className="mt-6 flex justify-end">
          <Button
            className="btn-theme-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
            onClick={() => handleSave('security')}
            disabled={loading}
          >
            <FaSave className="mr-2" />
            Save Security
          </Button>
        </div>
      </CardBody>
    </Card>
  );

  const renderDatabaseSettings = () => (
    <Card className="bg-white border-0 shadow-lg">
      <CardHeader className="border-b border-gray-200 px-6 py-4">
        <h5 className="text-lg font-semibold text-gray-900 mb-0">Database & Security</h5>
      </CardHeader>
      <CardBody className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
            <select
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.backupFrequency}
              onChange={(e) => handleInputChange('backupFrequency', e.target.value)}
              disabled={loading}
            >
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Retention Period (days)</label>
            <input
              type="number"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
              value={settings.retentionPeriod}
              onChange={(e) => handleInputChange('retentionPeriod', e.target.value)}
              disabled={loading}
              min="7"
              max="3650"
            />
          </div>
        </div>
        <div className="mt-6">
          <div className="flex items-center justify-between">
            <div>
              <h6 className="text-sm font-medium text-gray-900">Auto Backup</h6>
              <p className="text-sm text-gray-500">Automatically backup database at scheduled intervals</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.autoBackup}
                onChange={(e) => handleInputChange('autoBackup', e.target.checked)}
                disabled={loading}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
        <div className="mt-6 flex justify-end">
          <Button
            className="btn-theme-primary border-0 shadow-md hover:shadow-lg transition-all duration-200"
            onClick={() => handleSave('database')}
            disabled={loading}
          >
            <FaSave className="mr-2" />
            Save Database Settings
          </Button>
        </div>
      </CardBody>
    </Card>
  );

  return (
    <div className="w-full">
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600 text-sm sm:text-base">Manage your application preferences and configurations</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 sm:mb-6">
        <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0">6</h4>
                <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Settings Sections</p>
              </div>
              <div className="rounded-lg p-2 sm:p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                <FaCog className="h-5 w-5 sm:h-6 sm:w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">Active</h4>
                <p className="text-gray-600 mb-0 text-sm">System Status</p>
              </div>
              <div className="theme-icon-bg rounded-lg p-2 sm:p-3">
                <FaLock className="h-5 w-5 sm:h-6 sm:w-6 theme-icon-text" />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">{settings.backupFrequency}</h4>
                <p className="text-gray-600 mb-0 text-sm">Backup Schedule</p>
              </div>
              <div className="theme-icon-bg rounded-lg p-2 sm:p-3">
                <FaDatabase className="h-5 w-5 sm:h-6 sm:w-6 theme-icon-text" />
              </div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
          <div className="p-4 sm:p-6">
            <div className="flex justify-between items-center">
              <div>
                <div className="flex items-center space-x-2">
                  <div
                    className={`w-6 h-6 rounded-full border border-gray-300 ${
                      !settings.primaryColor ? 'bg-gray-100' : ''
                    }`}
                    style={settings.primaryColor ? { backgroundColor: settings.primaryColor } : {}}
                  >
                  </div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">
                    {settings.primaryColor || 'Not set'}
                  </h4>
                </div>
                <p className="text-gray-600 mb-0 text-sm">Primary Color</p>
              </div>
              <div className="theme-icon-bg rounded-lg p-2 sm:p-3">
                <FaPalette className="h-5 w-5 sm:h-6 sm:w-6 theme-icon-text" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card className="bg-white border-0 shadow-lg">
            <CardBody className="p-0">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 text-left text-sm font-medium transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'settings-nav-active'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon className="mr-3 h-5 w-5" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </CardBody>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {loading && (
            <div className="loading-center">
              <div className="loading-text">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-t-transparent" style={{ borderColor: 'var(--primary-color, #1d5795)' }}></div>
                <span className="ml-2 text-gray-600">Loading settings...</span>
              </div>
            </div>
          )}
          {!loading && activeTab === 'general' && renderGeneralSettings()}
          {!loading && activeTab === 'profile' && renderProfileSettings()}
          {!loading && activeTab === 'theme' && renderThemeSettings()}
          {!loading && activeTab === 'notifications' && renderNotificationSettings()}
          {!loading && activeTab === 'security' && renderSecuritySettings()}
          {!loading && activeTab === 'database' && renderDatabaseSettings()}
        </div>
      </div>
    </div>
  );
};

export default Settings;
