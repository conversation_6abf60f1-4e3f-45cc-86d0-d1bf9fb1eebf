import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { customerAPI, apiService } from '../../services/api';
import { useExecutiveSearch } from '../../hooks/useExecutiveSearch';
import { useAreaSearch } from '../../hooks/useAreaSearch';
import { useProductSearch } from '../../hooks/useProductSearch';
import { SearchableSelect } from '../../components/ui';
import MapLocationPicker from '../ui/MapLocationPicker';
import MobileInput from '../ui/MobileInput';
import { hasPhoneDigits, validationRules } from '../../utils/validation';
import {
  FaSave,
  FaTimes,
  FaUser,
  FaBuilding,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaPlus,
  FaMinus,
  FaMap,
  FaCog,
  FaCalendar,
  FaShieldAlt,
  FaTools,
  FaClipboardList,
  FaExclamationTriangle,
  FaCheckCircle
} from 'react-icons/fa';

const CustomerFormValidated = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Basic Info (matching todo.md requirements)
    customerName: '',
    tallySerialNo: '',
    product: null,
    licenceEdition: null,
    location: null,
    industry: null, // Task 1: Added industry field
    profileStatus: 'FOLLOW UP',
    customerStatus: 'ACTIVE', // Default to Active
    defaultCallType: 'free_call', // Task 4: Default call type
    followUpExecutive: null,
    mapLocation: '',
    latitude: '',
    longitude: '',
    gstNo: '',
    remarks: '',

    // Task 1: New required fields
    adminEmail: '', // Email-ID of account admin (mandatory)
    mdContactPerson: '', // Contact Person - MD (mandatory)
    mdPhoneNo: '', // MD Phone No (mandatory)
    mdEmail: '', // MD Email ID (mandatory)
    officeContactPerson: '', // Contact Person Office (mandatory)
    officeMobileNo: '', // Office Mobile No (mandatory)
    officeEmail: '', // Office Email ID (mandatory)
    auditorName: '', // Auditor Name (mandatory)
    auditorNo: '', // Auditor No (mandatory)
    auditorEmail: '', // Auditor Email ID (mandatory)
    taxConsultantName: '', // Tax consultant Name (mandatory)
    taxConsultantNo: '', // Tax Consultant No (mandatory)
    taxConsultantEmail: '', // Tax Consultant Email ID (mandatory)
    itName: '', // IT Name (optional)
    itNo: '', // IT No (optional)
    itEmail: '', // IT Email ID (optional)
    area: '', // Area (mandatory)
    pinCode: '', // PIN Code (optional)
    stateCountry: '', // State Country (optional)
    noOfTallyUsers: '', // No. of Tally Users (mandatory)
    // logDetails removed - Task 3: User logs should be automatic, not manual entry
    executiveName: '', // Executive Name (mandatory)
    status: '', // Status (mandatory)

    // Customer Address Book (Multiple Entries)
    addressBook: [
      {
        type: '',
        contactPerson: '',
        mobileNumbers: [''],
        phone: '',
        email: '',
        isMandatory: false
      }
    ],

    // Tally Software Service (TSS)
    tssStatus: 'NO',
    tssExpiryDate: '',

    // AMC (Annual Maintenance Contract)
    amcStatus: 'NO',
    amcFromDate: '',
    amcToDate: '',
    renewalDate: '',
    noOfVisits: '',
    currentAmcAmount: '',
    lastYearAmcAmount: '',

    // Additional Features (New section) - with expiry dates like TSS
    tdlAddons: false,
    tdlAddonsExpiryDate: '',
    tdlAddonsFile: null, // File upload for TDL & Addons
    whatsappTelegramGroup: false,
    whatsappTelegramGroupExpiryDate: '',
    autoBackup: false,
    autoBackupExpiryDate: '',
    cloudUser: false,
    cloudUserExpiryDate: '',
    mobileApp: false,
    mobileAppExpiryDate: ''
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [showErrorPopup, setShowErrorPopup] = useState(false);

  // Master data states
  const [tallyProducts, setTallyProducts] = useState([]);
  const [licenseEditions, setLicenseEditions] = useState([]);
  const [areas, setAreas] = useState([]);
  const [executives, setExecutives] = useState([]);
  const [industries, setIndustries] = useState([]); // Task 1: Added industries state

  // Loading states
  const [loadingTallyProducts, setLoadingTallyProducts] = useState(false);
  const [loadingLicenseEditions, setLoadingLicenseEditions] = useState(false);
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [loadingExecutives, setLoadingExecutives] = useState(false);
  const [loadingIndustries, setLoadingIndustries] = useState(false); // Task 1: Added industries loading state

  // Server-side search hooks
  const { searchResults: executiveSearchResults, isSearching: isSearchingExecutives, searchExecutives } = useExecutiveSearch();
  const { searchResults: areaSearchResults, isSearching: isSearchingAreas, searchAreas } = useAreaSearch();
  const { searchResults: productSearchResults, isSearching: isSearchingProducts, searchProducts } = useProductSearch();

  // Map picker state
  const [showMapPicker, setShowMapPicker] = useState(false);

  // Required fields for validation (Task 5 & 6: Removed conditional fields)
  const requiredFields = [
    'customerName',
    'tallySerialNo',
    // Contact Information fields removed - now handled by conditional validation
    // 'adminEmail', // Optional - only required if no other contact is provided
    // 'mdContactPerson', // Conditional - only required if any MD field is filled
    // 'mdPhoneNo', // Conditional - only required if any MD field is filled
    // 'mdEmail', // Conditional - only required if any MD field is filled
    // 'officeContactPerson', // Conditional - only required if any Office field is filled
    // 'officeMobileNo', // Conditional - only required if any Office field is filled
    // 'officeEmail', // Conditional - only required if any Office field is filled
    // Professional Contacts fields removed - now handled by conditional validation
    // 'auditorName', // Conditional - only required if any Auditor field is filled
    // 'auditorNo', // Conditional - only required if any Auditor field is filled
    // 'auditorEmail', // Conditional - only required if any Auditor field is filled
    // 'taxConsultantName', // Conditional - only required if any Tax Consultant field is filled
    // 'taxConsultantNo', // Conditional - only required if any Tax Consultant field is filled
    // 'taxConsultantEmail', // Conditional - only required if any Tax Consultant field is filled
    // 'area', // Now optional - Business Information
    // 'noOfTallyUsers', // Now optional - Business Information
    // 'logDetails', // Task 3: Removed manual log entry
    // 'executiveName', // Now optional - Business Information
    // 'status' // Now optional - Business Information
  ];

  const designationOptions = [
    { value: 'owner', label: '👑 Owner', isMandatory: true },
    { value: 'manager', label: '👨‍💼 Manager', isMandatory: false },
    { value: 'accountant', label: '📊 Accountant', isMandatory: false },
    { value: 'auditor-CA', label: '🎓 Auditor-CA', isMandatory: false },
    { value: 'auditor-STP', label: '📋 Auditor-STP', isMandatory: false }
  ];

  // State for dropdown data
  const [designations, setDesignations] = useState([]);
  const [loadingDesignations, setLoadingDesignations] = useState(false);

  // Error Popup Component
  const ErrorPopup = ({ errors, onClose }) => {
    const errorEntries = Object.entries(errors).filter(([key, value]) => value);

    if (errorEntries.length === 0) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-96 overflow-hidden">
          <div className="bg-red-500 text-white px-6 py-4 flex items-center justify-between">
            <div className="flex items-center">
              <FaExclamationTriangle className="mr-2" />
              <h3 className="text-lg font-semibold">Form Validation Errors</h3>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <FaTimes />
            </button>
          </div>

          <div className="p-6 overflow-y-auto max-h-80">
            <p className="text-gray-600 mb-4">
              Please fix the following errors before submitting:
            </p>

            <div className="space-y-3">
              {errorEntries.map(([field, error], index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-800">
                      {getFieldLabel(field)}
                    </div>
                    <div className="text-red-600 text-sm mt-1">{error}</div>
                    {field === 'email' && error.includes('check all email fields') && (
                      <div className="text-gray-600 text-xs mt-1 italic">
                        Please check: Account Admin Email, MD Email, Office Email, Auditor Email, Tax Consultant Email, IT Email, and Address Book emails.
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 px-6 py-4 flex justify-end">
            <button
              onClick={onClose}
              className="btn-primary px-4 py-2 rounded-lg transition-colors flex items-center"
            >
              <FaCheckCircle className="mr-2" />
              Got it
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to get user-friendly field labels
  const getFieldLabel = (fieldName) => {
    const fieldLabels = {
      customerName: 'Customer Name',
      tallySerialNo: 'Tally Serial No',
      adminEmail: 'Email ID of Account Admin',
      mdContactPerson: 'Contact Person - MD',
      mdPhoneNo: 'MD Phone No',
      mdEmail: 'MD Email ID',
      officeContactPerson: 'Contact Person Office',
      officeMobileNo: 'Office Mobile No',
      officeEmail: 'Office Email ID',
      auditorName: 'Auditor Name',
      auditorNo: 'Auditor No',
      auditorEmail: 'Auditor Email ID',
      taxConsultantName: 'Tax Consultant Name',
      taxConsultantNo: 'Tax Consultant No',
      taxConsultantEmail: 'Tax Consultant Email ID',
      itName: 'IT Name',
      itNo: 'IT No',
      itEmail: 'IT Email ID',
      area: 'Area',
      noOfTallyUsers: 'No. of Tally Users',
      // logDetails: 'Log Details', // Task 3: Removed manual log entry
      executiveName: 'Executive Name',
      status: 'Status',
      product: 'Product',
      licenceEdition: 'License Edition',
      location: 'Location',
      followUpExecutive: 'Follow-up Executive',
      gstNo: 'GST No',
      mapLocation: 'Map Location',
      pinCode: 'PIN Code',
      stateCountry: 'State/Country',
      tssExpiryDate: 'TSS Expiry Date',
      amcFromDate: 'AMC From Date',
      amcToDate: 'AMC To Date',
      renewalDate: 'Renewal Date',
      noOfVisits: 'Number of Visits',
      currentAmcAmount: 'Current AMC Amount',
      tdlAddonsExpiryDate: 'TDL & Addons Expiry Date',
      whatsappTelegramGroupExpiryDate: 'WhatsApp/Telegram Group Expiry Date',
      autoBackupExpiryDate: 'Auto Backup Expiry Date',
      cloudUserExpiryDate: 'Cloud User Expiry Date',
      mobileAppExpiryDate: 'Mobile App Expiry Date',
      addressBook: 'Address Book',
      // Generic email field (fallback)
      email: 'Email'
    };

    // Handle address book specific errors
    if (fieldName.startsWith('addressBook_')) {
      const parts = fieldName.split('_');
      const index = parts[1];
      const field = parts[2];
      const fieldMap = {
        contactPerson: 'Contact Person',
        mobile: 'Mobile Number',
        email: 'Email'
      };
      return `Address Book Entry ${parseInt(index) + 1} - ${fieldMap[field] || field}`;
    }

    return fieldLabels[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  // Function to process backend errors and map them to specific fields
  const processBackendErrors = (backendErrors) => {
    const processedErrors = {};

    Object.entries(backendErrors).forEach(([field, message]) => {
      // Handle email duplicate errors by checking which email field has the duplicate value
      if (field === 'email' && message.includes('already registered')) {
        // Extract the email address from the error message if possible
        const emailMatch = message.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        const duplicateEmail = emailMatch ? emailMatch[1] : null;

        // Check which email field contains the duplicate value
        const emailFields = [
          { field: 'adminEmail', label: 'Email ID of Account Admin' },
          { field: 'mdEmail', label: 'MD Email ID' },
          { field: 'officeEmail', label: 'Office Email ID' },
          { field: 'auditorEmail', label: 'Auditor Email ID' },
          { field: 'taxConsultantEmail', label: 'Tax Consultant Email ID' },
          { field: 'itEmail', label: 'IT Email ID' }
        ];

        let foundDuplicate = false;

        // If we have the duplicate email, find the exact field
        if (duplicateEmail) {
          emailFields.forEach(({ field: emailField, label }) => {
            if (formData[emailField] === duplicateEmail) {
              processedErrors[emailField] = `This email address (${duplicateEmail}) is already registered`;
              foundDuplicate = true;
            }
          });
        }

        // If we couldn't match the exact email, check for any non-empty email fields
        if (!foundDuplicate) {
          emailFields.forEach(({ field: emailField, label }) => {
            if (formData[emailField] && formData[emailField].trim() !== '' && !foundDuplicate) {
              processedErrors[emailField] = `This email address (${formData[emailField]}) is already registered`;
              foundDuplicate = true;
            }
          });
        }

        // Also check address book emails
        if (!foundDuplicate) {
          formData.addressBook.forEach((entry, index) => {
            if (entry.email && entry.email.trim() !== '' && !foundDuplicate) {
              if (!duplicateEmail || entry.email === duplicateEmail) {
                processedErrors[`addressBook_${index}_email`] = `This email address (${entry.email}) is already registered`;
                foundDuplicate = true;
              }
            }
          });
        }

        // If we still couldn't identify the specific field, use a generic error
        if (!foundDuplicate) {
          processedErrors.email = 'Email address is already registered. Please check all email fields.';
        }
      } else {
        // For other errors, use the field name as-is
        processedErrors[field] = message;
      }
    });

    return processedErrors;
  };

  useEffect(() => {
    fetchDropdownData();
    if (isEdit) {
      fetchCustomer();
    }
  }, [isEdit, id]);

  // Fetch dropdown data for form
  const fetchDropdownData = async () => {
    try {
      // Fetch tally products
      try {
        setLoadingTallyProducts(true);
        const tallyProductsResponse = await apiService.get('/master-data/tally-products');
        if (tallyProductsResponse.data?.success && Array.isArray(tallyProductsResponse.data.data?.tallyproduct)) {
          setTallyProducts(tallyProductsResponse.data.data.tallyproduct);
        } else {
          console.warn('Tally Products API returned invalid data:', tallyProductsResponse.data);
          setTallyProducts([]);
        }
      } catch (error) {
        console.error('Error fetching tally products:', error);
        setTallyProducts([]);
      } finally {
        setLoadingTallyProducts(false);
      }

      // Fetch license editions
      try {
        setLoadingLicenseEditions(true);
        const licenseEditionsResponse = await apiService.get('/master-data/license-editions');
        if (licenseEditionsResponse.data?.success && Array.isArray(licenseEditionsResponse.data.data?.licenseedition)) {
          setLicenseEditions(licenseEditionsResponse.data.data.licenseedition);
        } else {
          console.warn('License Editions API returned invalid data:', licenseEditionsResponse.data);
          setLicenseEditions([]);
        }
      } catch (error) {
        console.error('Error fetching license editions:', error);
        setLicenseEditions([]);
      } finally {
        setLoadingLicenseEditions(false);
      }

      // Fetch areas
      try {
        setLoadingAreas(true);
        const areasResponse = await apiService.get('/master-data/areas');
        if (areasResponse.data?.success && Array.isArray(areasResponse.data.data?.area)) {
          setAreas(areasResponse.data.data.area);
        } else {
          console.warn('Areas API returned invalid data:', areasResponse.data);
          setAreas([]);
        }
      } catch (areasError) {
        console.error('Error fetching areas:', areasError);
        setAreas([]);
      } finally {
        setLoadingAreas(false);
      }

      // Fetch executives
      try {
        setLoadingExecutives(true);
        const executivesResponse = await apiService.get('/executives');
        if (executivesResponse.data?.success && Array.isArray(executivesResponse.data.data?.executives)) {
          setExecutives(executivesResponse.data.data.executives);
        } else {
          console.warn('Executives API returned invalid data:', executivesResponse.data);
          setExecutives([]);
        }
      } catch (executivesError) {
        console.error('Error fetching executives:', executivesError);
        setExecutives([]);
      } finally {
        setLoadingExecutives(false);
      }

      // Task 1: Fetch industries
      try {
        setLoadingIndustries(true);
        const industriesResponse = await apiService.get('/master-data/industries');
        if (industriesResponse.data?.success && Array.isArray(industriesResponse.data.data?.industry)) {
          setIndustries(industriesResponse.data.data.industry);
        } else {
          console.warn('Industries API returned invalid data:', industriesResponse.data);
          setIndustries([]);
        }
      } catch (industriesError) {
        console.error('Error fetching industries:', industriesError);
        setIndustries([]);
      } finally {
        setLoadingIndustries(false);
      }
      // Fetch designations
      try {
        setLoadingDesignations(true);
        const designationsResponse = await apiService.get('/master-data/designations');
        if (designationsResponse.data?.success && Array.isArray(designationsResponse.data.data?.designation)) {
          setDesignations(designationsResponse.data.data.designation);
        } else {
          console.warn('Designations API returned invalid data:', designationsResponse.data);
          setDesignations([]);
        }
      } catch (error) {
        console.error('Error fetching designations:', error);
        setDesignations([]);
      } finally {
        setLoadingDesignations(false);
      }

    } catch (error) {
      console.error('Error fetching dropdown data:', error);
      // Set empty arrays as fallback
      setTallyProducts([]);
      setLicenseEditions([]);
      setAreas([]);
      setExecutives([]);
      setIndustries([]); // Task 1: Added industries fallback
      setDesignations([]);
    }
  };

  const fetchCustomer = async () => {
    try {
      setLoading(true);
      const response = await customerAPI.getById(id, { includeRelations: true });

      if (response.data?.success && response.data?.data?.customer) {
        const { customer } = response.data.data;
        const customFields = customer.custom_fields || {};

        setFormData({
          customerName: customer.company_name || '',
          tallySerialNo: customer.customer_code || customer.tally_serial_number || '',
          product: customFields.product_id || null,
          licenceEdition: customFields.license_edition_id || null,
          location: customer.area_id || null,
          industry: customer.industry_id || customFields.industry_id || null, // Task 1: Added industry field
          profileStatus: customFields.profile_status || 'FOLLOW UP',
          customerStatus: customFields.customer_status || 'ACTIVE',
          followUpExecutive: customer.assigned_executive_id || customFields.follow_up_executive_id || null,
          mapLocation: customer.address_line_1 || customFields.map_location || '',
          latitude: customer.latitude || '',
          longitude: customer.longitude || '',
          gstNo: customer.gst_number || '',
          remarks: customer.notes || '',

          // Task 1: New required fields
          adminEmail: customFields.admin_email || '',
          mdContactPerson: customFields.md_contact_person || '',
          mdPhoneNo: customFields.md_phone_no || '',
          mdEmail: customFields.md_email || '',
          officeContactPerson: customFields.office_contact_person || '',
          officeMobileNo: customFields.office_mobile_no || '',
          officeEmail: customFields.office_email || '',
          auditorName: customFields.auditor_name || '',
          auditorNo: customFields.auditor_no || '',
          auditorEmail: customFields.auditor_email || '',
          taxConsultantName: customFields.tax_consultant_name || '',
          taxConsultantNo: customFields.tax_consultant_no || '',
          taxConsultantEmail: customFields.tax_consultant_email || '',
          itName: customFields.it_name || '',
          itNo: customFields.it_no || '',
          itEmail: customFields.it_email || '',
          area: customFields.area || '',
          pinCode: customFields.pin_code || '',
          stateCountry: customFields.state_country || '',
          noOfTallyUsers: customFields.no_of_tally_users || '',
          // logDetails: customFields.log_details || '', // Task 3: Removed manual log entry
          executiveName: customFields.executive_name || '',
          status: customFields.status || '',

          addressBook: customFields.address_book && customFields.address_book.length > 0
            ? customFields.address_book.map(entry => ({
              type: entry.type || '',
              contactPerson: entry.contact_person || '',
              mobileNumbers: entry.mobile_numbers || [''],
              phone: entry.phone || '',
              email: entry.email || '',
              isMandatory: entry.is_mandatory || false
            }))
            : [{
              type: '',
              contactPerson: customer.contact_person || '',
              mobileNumbers: [customer.phone || ''],
              phone: customer.phone || '',
              email: customer.email || '',
              isMandatory: false
            }],
          tssStatus: customFields.tss_status || 'NO',
          tssExpiryDate: customFields.tss_expiry_date || '',
          amcStatus: customFields.amc_status || 'NO',
          amcFromDate: customFields.amc_from_date || '',
          amcToDate: customFields.amc_to_date || '',
          renewalDate: customFields.renewal_date || '',
          noOfVisits: customFields.no_of_visits || '',
          currentAmcAmount: customFields.current_amc_amount || '',
          lastYearAmcAmount: customFields.last_year_amc_amount || '',
          additionalServices: customFields.additional_services || [],
          tdlAddons: customFields.tdl_addons || false,
          tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || '',
          tdlAddonsFile: null, // File will be handled separately for existing customers
          whatsappTelegramGroup: customFields.whatsapp_telegram_group || false,
          whatsappTelegramGroupExpiryDate: customFields.whatsapp_telegram_group_expiry_date || '',
          autoBackup: customFields.auto_backup || false,
          autoBackupExpiryDate: customFields.auto_backup_expiry_date || '',
          cloudUser: customFields.cloud_user || false,
          cloudUserExpiryDate: customFields.cloud_user_expiry_date || '',
          mobileApp: customFields.mobile_app || false,
          mobileAppExpiryDate: customFields.mobile_app_expiry_date || ''
        });
      }
    } catch (error) {
      console.error('Error fetching customer:', error);
      toast.error('Failed to load customer data');
      navigate('/customers');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    let processedValue = value;

    // Handle checkbox inputs
    if (type === 'checkbox') {
      processedValue = checked;
    }

    // Convert Tally Serial No to uppercase and allow only alphanumeric characters
    if (name === 'tallySerialNo') {
      processedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    }

    // New Task 1: Convert GST No to uppercase while typing
    if (name === 'gstNo') {
      processedValue = value.toUpperCase();
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Real-time validation
    if (touched[name] || processedValue !== '') {
      validateField(name, processedValue);
    }
  };

  // Handle SearchableSelect changes
  const handleSearchableSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Real-time validation
    if (touched[name] || value !== null) {
      validateField(name, value);
    }
  };

  // Helper functions for address book management
  const addAddressBookEntry = () => {
    setFormData(prev => ({
      ...prev,
      addressBook: [
        ...prev.addressBook,
        {
          type: '',
          contactPerson: '',
          mobileNumbers: [''],
          phone: '',
          email: '',
          isMandatory: false
        }
      ]
    }));
  };

  const removeAddressBookEntry = (index) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.filter((_, i) => i !== index)
    }));
  };

  const handleAddressBookChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, i) =>
        (i === index ? { ...entry, [field]: value } : entry)
      )
    }));
  };

  const addMobileNumber = (addressIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        (index === addressIndex
          ? { ...entry, mobileNumbers: [...entry.mobileNumbers, ''] }
          : entry)
      )
    }));
  };

  const removeMobileNumber = (addressIndex, mobileIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        (index === addressIndex
          ? { ...entry, mobileNumbers: entry.mobileNumbers.filter((_, i) => i !== mobileIndex) }
          : entry)
      )
    }));
  };

  // File upload handler for TDL & Addons
  const handleFileUpload = (e) => {
    const { name, files } = e.target;
    const file = files[0];

    if (file) {
      // Validate file type (allow common document formats)
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'image/jpg'
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a valid file (PDF, DOC, DOCX, JPG, PNG)');
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setFormData(prev => ({
        ...prev,
        [name]: file
      }));

      toast.success(`File "${file.name}" uploaded successfully`);
    }
  };

  const handleMobileNumberChange = (addressIndex, mobileIndex, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        (index === addressIndex
          ? {
            ...entry,
            mobileNumbers: entry.mobileNumbers.map((num, i) =>
              (i === mobileIndex ? value : num)
            )
          }
          : entry)
      )
    }));
  };

  // Map location handler
  const handleLocationSelect = (locationData) => {
    setFormData(prev => ({
      ...prev,
      mapLocation: locationData.address,
      latitude: locationData.latitude,
      longitude: locationData.longitude
    }));
  };


  const validateField = (fieldName, value) => {
    // Basic validation for new fields
    if (requiredFields.includes(fieldName) && (!value || value.toString().trim() === '')) {
      return { isValid: false, message: `${fieldName} is required` };
    }

    // Email validation
    if (fieldName.includes('email') && value && !/\S+@\S+\.\S+/.test(value)) {
      return { isValid: false, message: 'Please enter a valid email' };
    }

    return { isValid: true, message: '' };
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].toString().trim() === '') {
        const fieldLabel = field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        newErrors[field] = `${fieldLabel} is required`;
      }
    });

    // New Task 3: Contact Information section - unified conditional validation
    // Check if ANY field in the entire Contact Information section is filled
    const contactInfoFields = [
      'adminEmail', 'mdContactPerson', 'mdPhoneNo', 'mdEmail',
      'officeContactPerson', 'officeMobileNo', 'officeEmail'
    ];

    const hasAnyContactInfoField = contactInfoFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For mobile fields, check actual phone digits
      if (field === 'mdPhoneNo' || field === 'officeMobileNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    // If ANY field in Contact Information is filled, ALL fields become required
    if (hasAnyContactInfoField) {
      if (!formData.adminEmail || formData.adminEmail.trim() === '') {
        newErrors.adminEmail = 'Email-ID of Account Admin is required when any Contact Information field is filled';
      }
      if (!formData.mdContactPerson || formData.mdContactPerson.trim() === '') {
        newErrors.mdContactPerson = 'Contact Person - MD is required when any Contact Information field is filled';
      }
      if (!formData.mdPhoneNo || formData.mdPhoneNo.trim() === '') {
        newErrors.mdPhoneNo = 'MD Phone No is required when any Contact Information field is filled';
      }
      if (!formData.mdEmail || formData.mdEmail.trim() === '') {
        newErrors.mdEmail = 'MD Email ID is required when any Contact Information field is filled';
      }
      if (!formData.officeContactPerson || formData.officeContactPerson.trim() === '') {
        newErrors.officeContactPerson = 'Contact Person Office is required when any Contact Information field is filled';
      }
      if (!formData.officeMobileNo || formData.officeMobileNo.trim() === '') {
        newErrors.officeMobileNo = 'Office Mobile No is required when any Contact Information field is filled';
      }
      if (!formData.officeEmail || formData.officeEmail.trim() === '') {
        newErrors.officeEmail = 'Office Email ID is required when any Contact Information field is filled';
      }
    }

    // Email validation for all email fields
    const emailFields = ['adminEmail', 'mdEmail', 'officeEmail', 'auditorEmail', 'taxConsultantEmail', 'itEmail'];
    emailFields.forEach(field => {
      if (formData[field] && !/\S+@\S+\.\S+/.test(formData[field])) {
        newErrors[field] = 'Please enter a valid email';
      }
    });

    // Phone number validation (supports international formats)
    const phoneFields = ['mdPhoneNo', 'officeMobileNo', 'auditorNo', 'taxConsultantNo', 'itNo'];
    phoneFields.forEach(field => {
      if (formData[field]) {
        const phoneValidation = validationRules.phone(formData[field], false);
        if (!phoneValidation.isValid) {
          newErrors[field] = phoneValidation.message;
        }
      }
    });

    // Professional Contacts section - conditional validation
    // Office Email - completely optional, only validate format if provided
    if (formData.officeEmail && formData.officeEmail.trim() && !/\S+@\S+\.\S+/.test(formData.officeEmail)) {
      newErrors.officeEmail = 'Please enter a valid office email';
    }

    // If any field in a professional contact group is filled, all fields for that contact must be filled
    const professionalContacts = [
      { name: 'Auditor', fields: ['auditorName', 'auditorNo', 'auditorEmail'] },
      { name: 'Tax Consultant', fields: ['taxConsultantName', 'taxConsultantNo', 'taxConsultantEmail'] },
      { name: 'IT', fields: ['itName', 'itNo', 'itEmail'] }
    ];

    professionalContacts.forEach(contact => {
      const hasAnyField = contact.fields.some(field => {
        if (!formData[field] || formData[field].trim() === '') return false;

        // For mobile fields, check actual phone digits
        if (field.includes('No') || field.includes('no')) {
          return hasPhoneDigits(formData[field]);
        }

        return true;
      });

      if (hasAnyField) {
        contact.fields.forEach(field => {
          if (!formData[field] || formData[field].trim() === '') {
            newErrors[field] = `${field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} is required when any ${contact.name} field is filled`;
          }
        });
      }
    });

    // Validate mandatory address book entries
    const mandatoryDesignations = designationOptions.filter(d => d.isMandatory).map(d => d.value);
    const addressBookTypes = formData.addressBook.map(entry => entry.type);

    mandatoryDesignations.forEach(designation => {
      if (!addressBookTypes.includes(designation)) {
        newErrors.addressBook = `${designation} contact is mandatory`;
      }
    });

    // Validate address book entries
    formData.addressBook.forEach((entry, index) => {
      if (entry.type && !entry.contactPerson.trim()) {
        newErrors[`addressBook_${index}_contactPerson`] = 'Contact person is required';
      }
      if (entry.type && entry.mobileNumbers.every(num => !num.trim())) {
        newErrors[`addressBook_${index}_mobile`] = 'At least one mobile number is required';
      }
      if (entry.type && !entry.email.trim()) {
        newErrors[`addressBook_${index}_email`] = 'Email is required';
      } else if (entry.email && !/\S+@\S+\.\S+/.test(entry.email)) {
        newErrors[`addressBook_${index}_email`] = 'Please enter a valid email';
      }
    });

    // GST validation
    if (formData.gstNo && formData.gstNo.length !== 15) {
      newErrors.gstNo = 'GST number must be exactly 15 characters';
    }

    // TSS validation
    if (formData.tssStatus === 'YES') {
      if (!formData.tssExpiryDate) {
        newErrors.tssExpiryDate = 'TSS Expiry Date is required when TSS is active';
      }
    }

    // AMC validation
    if (formData.amcStatus === 'YES') {
      if (!formData.amcFromDate) newErrors.amcFromDate = 'AMC From Date is required';
      if (!formData.amcToDate) newErrors.amcToDate = 'AMC To Date is required';
      if (!formData.renewalDate) newErrors.renewalDate = 'Renewal Date is required';
      if (!formData.noOfVisits) newErrors.noOfVisits = 'Number of visits is required';
      if (!formData.currentAmcAmount) newErrors.currentAmcAmount = 'Current AMC amount is required';
    }

    // Additional Features validation - like TSS
    if (formData.tdlAddons && !formData.tdlAddonsExpiryDate) {
      newErrors.tdlAddonsExpiryDate = 'Expiry Date is required when TDL & Addons is selected';
    }
    if (formData.whatsappTelegramGroup && !formData.whatsappTelegramGroupExpiryDate) {
      newErrors.whatsappTelegramGroupExpiryDate = 'Expiry Date is required when WhatsApp/Telegram Group is selected';
    }
    if (formData.autoBackup && !formData.autoBackupExpiryDate) {
      newErrors.autoBackupExpiryDate = 'Expiry Date is required when Auto Backup is selected';
    }
    if (formData.cloudUser && !formData.cloudUserExpiryDate) {
      newErrors.cloudUserExpiryDate = 'Expiry Date is required when Cloud User is selected';
    }
    if (formData.mobileApp && !formData.mobileAppExpiryDate) {
      newErrors.mobileAppExpiryDate = 'Expiry Date is required when Mobile App is selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log('=== CUSTOMER FORM SUBMISSION STARTED ===');
    console.log('Form data:', formData);
    console.log('Is edit mode:', isEdit);
    console.log('Required fields:', requiredFields);

    // Mark all fields as touched
    const allTouched = {};
    Object.keys(formData).forEach(key => {
      allTouched[key] = true;
    });
    setTouched(allTouched);

    if (!validateForm()) {
      console.log('Form validation failed');
      setShowErrorPopup(true);
      return;
    }

    console.log('Form validation passed, making API call');
    setLoading(true);

    try {
      // Prepare data for API according to backend Customer model structure
      const customerData = {
        // Required fields
        company_name: formData.customerName && formData.customerName.trim() ? formData.customerName : null,
        customer_code: formData.tallySerialNo, // Using tally serial as customer code

        // Optional basic fields
        display_name: formData.customerName,
        customer_type: 'customer', // Default type
        business_type: 'private_limited', // Default business type

        // Contact information from address book
        email: formData.addressBook.find(entry => entry.email)?.email || null,
        phone: formData.addressBook.find(entry => entry.mobileNumbers?.length > 0)?.mobileNumbers[0] || null,

        // Address fields
        address_line_1: formData.mapLocation || null,
        city: formData.location || null,
        state: null,
        country: 'India',
        postal_code: null,

        // Business details
        gst_number: formData.gstNo || null,
        pan_number: null,
        area_id: formData.location || null,
        industry_id: formData.industry || null, // Task 1: Added industry field
        assigned_executive_id: formData.followUpExecutive || null,

        // Tally specific fields - store IDs in custom_fields for now since backend expects names
        tally_version: null, // Will be stored in custom_fields
        tally_serial_number: formData.tallySerialNo,
        license_type: null, // Will be stored in custom_fields

        // Location
        latitude: formData.latitude ? parseFloat(formData.latitude) : null,
        longitude: formData.longitude ? parseFloat(formData.longitude) : null,

        // Additional fields
        notes: formData.remarks || null,

        // Custom fields for additional data that doesn't map to standard fields
        custom_fields: {
          profile_status: formData.profileStatus,
          customer_status: formData.customerStatus,
          follow_up_executive_id: formData.followUpExecutive,
          product_id: formData.product,
          license_edition_id: formData.licenceEdition,

          // Task 1: New required fields
          admin_email: formData.adminEmail,
          md_contact_person: formData.mdContactPerson,
          md_phone_no: formData.mdPhoneNo,
          md_email: formData.mdEmail,
          office_contact_person: formData.officeContactPerson,
          office_mobile_no: formData.officeMobileNo,
          office_email: formData.officeEmail,
          auditor_name: formData.auditorName,
          auditor_no: formData.auditorNo,
          auditor_email: formData.auditorEmail,
          tax_consultant_name: formData.taxConsultantName,
          tax_consultant_no: formData.taxConsultantNo,
          tax_consultant_email: formData.taxConsultantEmail,
          it_name: formData.itName,
          it_no: formData.itNo,
          it_email: formData.itEmail,
          area: formData.area,
          pin_code: formData.pinCode,
          state_country: formData.stateCountry,
          no_of_tally_users: formData.noOfTallyUsers ? parseInt(formData.noOfTallyUsers) : null,
          // log_details: formData.logDetails, // Task 3: Removed manual log entry
          executive_name: formData.executiveName,
          status: formData.status,

          address_book: formData.addressBook.map(entry => ({
            type: entry.type,
            contact_person: entry.contactPerson,
            mobile_numbers: entry.mobileNumbers.filter(num => num.trim() !== ''),
            phone: entry.phone,
            email: entry.email,
            is_mandatory: entry.isMandatory
          })),
          tss_status: formData.tssStatus,
          tss_expiry_date: formData.tssExpiryDate || null,
          amc_status: formData.amcStatus,
          amc_from_date: formData.amcFromDate || null,
          amc_to_date: formData.amcToDate || null,
          renewal_date: formData.renewalDate || null,
          no_of_visits: formData.noOfVisits ? parseInt(formData.noOfVisits) : null,
          current_amc_amount: formData.currentAmcAmount ? parseFloat(formData.currentAmcAmount) : null,
          last_year_amc_amount: formData.lastYearAmcAmount ? parseFloat(formData.lastYearAmcAmount) : null,
          additional_services: formData.additionalServices,
          tdl_addons: formData.tdlAddons,
          tdl_addons_expiry_date: formData.tdlAddonsExpiryDate || null,
          whatsapp_telegram_group: formData.whatsappTelegramGroup,
          whatsapp_telegram_group_expiry_date: formData.whatsappTelegramGroupExpiryDate || null,
          auto_backup: formData.autoBackup,
          auto_backup_expiry_date: formData.autoBackupExpiryDate || null,
          cloud_user: formData.cloudUser,
          cloud_user_expiry_date: formData.cloudUserExpiryDate || null,
          mobile_app: formData.mobileApp,
          mobile_app_expiry_date: formData.mobileAppExpiryDate || null
        },

        // Status
        is_active: true
      };

      let response;

      // Check if we have file uploads
      const hasFileUploads = formData.tdlAddonsFile;

      if (hasFileUploads) {
        // Use FormData for file uploads
        const formDataToSend = new FormData();

        // Add customer data as JSON string
        formDataToSend.append('customerData', JSON.stringify(customerData));

        // Add files
        if (formData.tdlAddonsFile) {
          formDataToSend.append('tdlAddonsFile', formData.tdlAddonsFile);
        }

        if (isEdit) {
          console.log('Updating customer with ID and files:', id);
          response = await apiService.put(`/customers/${id}`, formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
        } else {
          console.log('Creating new customer with files');
          response = await apiService.post('/customers', formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
        }
      } else {
        // Use regular JSON for non-file uploads
        if (isEdit) {
          console.log('Updating customer with ID:', id);
          response = await customerAPI.update(id, customerData);
        } else {
          console.log('Creating new customer');
          response = await customerAPI.create(customerData);
        }
      }

      console.log('API response:', response);

      if (response.data?.success) {
        toast.success(isEdit ? 'Customer updated successfully' : 'Customer created successfully');
        navigate('/customers');
      } else {
        toast.error(response.data?.message || 'Failed to save customer');

        // Handle backend validation errors
        if (response.data?.errors) {
          const processedErrors = processBackendErrors(response.data.errors);
          setErrors(processedErrors);
          setShowErrorPopup(true);
        }
      }
    } catch (error) {
      console.error('Error saving customer:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');

      // Handle form validation errors
      ErrorHandler.handleFormErrors(error, setErrors, setShowErrorPopup);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/customers');
  };


  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold mb-2 flex items-center text-primary-dynamic">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaBuilding className="text-xl" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  {isEdit ? 'Edit Customer' : 'New Customer'}
                </h2>
                <p className="text-lg" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>
                  {isEdit ? 'Update customer information and details' : 'Add a new customer to your CRM system'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  type="button"
                  className="inline-flex items-center px-6 py-3 border-2 border-white border-opacity-30 text-sm font-medium rounded-xl text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                  onClick={handleCancel}
                >
                  <FaTimes className="mr-2 h-4 w-4" />
                  Cancel
                </button>
                <button
                  type="submit"
                  form="customerForm"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  style={{ color: 'var(--primary-color)' }}
                  disabled={loading}
                  onClick={() => console.log('Submit button clicked!')}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 mr-2" style={{ borderColor: 'var(--primary-color)' }}></div>
                      {isEdit ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2 h-4 w-4" />
                      {isEdit ? 'Update Customer' : 'Create Customer'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form id="customerForm" onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border-primary-dynamic" style={{ borderColor: 'rgba(var(--primary-rgb), 0.2)' }}>
            <div className="px-6 py-4 bg-primary-dynamic" style={{ backgroundColor: 'var(--primary-color)' }}>
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic" style={{ color: 'var(--primary-text)' }}>
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaBuilding className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Basic Information
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Customer Name */}
                <div>
                  <label className="block text-sm font-bold mb-2 text-gray-700">Customer Name *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'bg-white hover:opacity-80'
                    }`}
                    style={!errors.customerName ? {
                      borderColor: 'rgba(var(--primary-rgb), 0.3)',
                      '--tw-ring-color': 'var(--primary-color)'
                    } : {}}
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    placeholder="Enter customer name"
                  />
                  {errors.customerName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerName}</p>}
                </div>

                {/* Tally Serial No */}
                <div>
                  <label className="block text-sm font-bold mb-2 text-gray-700">
                    Tally Serial No *
                    <span className="text-xs ml-2 text-gray-500">(e.g., GS001)</span>
                  </label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.tallySerialNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'bg-white hover:opacity-80'
                    }`}
                    style={!errors.tallySerialNo ? {
                      borderColor: 'rgba(var(--primary-rgb), 0.3)',
                      '--tw-ring-color': 'var(--primary-color)'
                    } : {}}
                    name="tallySerialNo"
                    value={formData.tallySerialNo}
                    onChange={handleInputChange}
                    placeholder="GS001"
                  />
                  {errors.tallySerialNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tallySerialNo}</p>}
                </div>

                {/* Product */}
                <div>
                  <label className="block text-sm font-bold mb-2 text-gray-700">
                    Product
                    <span className="text-xs ml-2 text-gray-500">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={tallyProducts}
                    value={formData.product}
                    onChange={(productId) => setFormData(prev => ({ ...prev, product: productId }))}
                    placeholder="Search products..."
                    searchFields={['name', 'description', 'category']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingTallyProducts}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No products found"
                    searchingText="Type 2+ letters to search products..."
                    // Server-side search props
                    onSearch={searchProducts}
                    isSearching={isSearchingProducts}
                    searchResults={productSearchResults}
                  />
                  {loadingTallyProducts && (
                    <p className="mt-2 text-sm text-gray-600">Loading products...</p>
                  )}
                </div>

                {/* Licence Edition */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    Licence Edition
                    <span className="text-xs text-gray-500 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={licenseEditions}
                    value={formData.licenceEdition}
                    onChange={(editionId) => setFormData(prev => ({ ...prev, licenceEdition: editionId }))}
                    placeholder="Search license editions..."
                    searchFields={['name', 'description', 'version']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingLicenseEditions}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No license editions found"
                    searchingText="Type to search editions..."
                  />
                  {loadingLicenseEditions && (
                    <p className="mt-2 text-sm text-gray-600">Loading license editions...</p>
                  )}
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    Location
                    <span className="text-xs text-gray-500 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={areas}
                    value={formData.location}
                    onChange={(areaId) => setFormData(prev => ({ ...prev, location: areaId }))}
                    placeholder="Search locations..."
                    searchFields={['name', 'city', 'state', 'description']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingAreas}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No locations found"
                    searchingText="Type 2+ letters to search locations..."
                    // Server-side search props
                    onSearch={searchAreas}
                    isSearching={isSearchingAreas}
                    searchResults={areaSearchResults}
                  />
                  {loadingAreas && (
                    <p className="mt-2 text-sm text-gray-600">Loading locations...</p>
                  )}
                </div>

                {/* Task 1: Industry */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    Industry
                    <span className="text-xs text-gray-500 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={industries}
                    value={formData.industry}
                    onChange={(industryId) => setFormData(prev => ({ ...prev, industry: industryId }))}
                    placeholder="Search industries..."
                    searchFields={['name', 'description', 'category']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingIndustries}
                    minSearchLength={1}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No industries found"
                    searchingText="Type to search industries..."
                  />
                  {loadingIndustries && (
                    <p className="mt-2 text-sm text-gray-600">Loading industries...</p>
                  )}
                </div>

                {/* Profile Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Profile Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="profileStatus"
                    value={formData.profileStatus}
                    onChange={handleInputChange}
                  >
                    <option value="FOLLOW UP">📞 FOLLOW UP</option>
                    <option value="OTHERS">📋 OTHERS</option>
                  </select>
                </div>

                {/* Customer Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Customer Status *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerStatus
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="customerStatus"
                    value={formData.customerStatus}
                    onChange={handleInputChange}
                  >
                    <option value="ACTIVE">✅ Active</option>
                    <option value="INACTIVE">❌ Inactive</option>
                  </select>
                  {errors.customerStatus && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerStatus}</p>}
                </div>

                {/* Follow-up Executive */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    Follow-up Executive
                    <span className="text-xs text-gray-500 ml-2">
                      (Type to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={executives}
                    value={formData.followUpExecutive}
                    onChange={(executiveId) => setFormData(prev => ({ ...prev, followUpExecutive: executiveId }))}
                    placeholder="Search executives..."
                    searchFields={['first_name', 'last_name', 'email', 'employee_code']}
                    displayField="first_name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingExecutives}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No executives found"
                    searchingText="Type 2+ letters to search executives..."
                    // Server-side search props
                    onSearch={searchExecutives}
                    isSearching={isSearchingExecutives}
                    searchResults={executiveSearchResults}
                  />
                  {loadingExecutives && (
                    <p className="mt-2 text-sm text-gray-600">Loading executives...</p>
                  )}
                </div>

                {/* GST No */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">GST No</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.gstNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="gstNo"
                    value={formData.gstNo}
                    onChange={handleInputChange}
                    placeholder="27AABCU9603R1ZX"
                    maxLength="15"
                  />
                  {errors.gstNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.gstNo}</p>}
                </div>


              </div>

              {/* Map Location Section */}
              <div className="mt-6 p-4 rounded-xl border" style={{
                backgroundColor: 'rgba(var(--primary-rgb), 0.05)',
                borderColor: 'rgba(var(--primary-rgb), 0.2)'
              }}
              >
                <h4 className="text-lg font-bold mb-4 flex items-center" style={{ color: 'var(--primary-color)' }}>
                  <FaMap className="mr-2" />
                  Map Location
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <label className="block text-sm font-bold text-gray-700 mb-2">Map Location</label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="flex-1 px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                        name="mapLocation"
                        value={formData.mapLocation}
                        onChange={handleInputChange}
                        placeholder="Enter location or click map button"
                      />
                      <button
                        type="button"
                        onClick={() => setShowMapPicker(true)}
                        className="px-4 py-3 btn-primary rounded-xl focus:outline-none transition-all duration-200"
                        title="Select location on map"
                      >
                        <FaMap className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-gray-700 mb-2">Latitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                      name="latitude"
                      value={formData.latitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-gray-700 mb-2">Longitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                      name="longitude"
                      value={formData.longitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* Remarks */}
              <div className="mt-6">
                <label className="block text-sm font-bold text-gray-700 mb-2">Remarks</label>
                <textarea
                  className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200"
                  name="remarks"
                  value={formData.remarks}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Enter any remarks"
                >
                </textarea>
              </div>
            </div>
          </div>

          {/* Task 1: Contact Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden border-primary-dynamic" style={{ borderColor: 'rgba(var(--primary-rgb), 0.2)' }}>
            <div className="px-6 py-4 bg-primary-dynamic" style={{ backgroundColor: 'var(--primary-color)' }}>
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic" style={{ color: 'var(--primary-text)' }}>
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaUser className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Contact Information
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Admin Email */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Email-ID of Account Admin *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.adminEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="adminEmail"
                    value={formData.adminEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.adminEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.adminEmail}</p>}
                </div>

                {/* MD Contact Person */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Contact Person - MD *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.mdContactPerson
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="mdContactPerson"
                    value={formData.mdContactPerson}
                    onChange={handleInputChange}
                    placeholder="Managing Director Name"
                  />
                  {errors.mdContactPerson && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdContactPerson}</p>}
                </div>

                {/* MD Phone No */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">MD Phone No *</label>
                  <MobileInput
                    value={formData.mdPhoneNo}
                    onChange={handleInputChange}
                    name="mdPhoneNo"
                    placeholder="Managing Director phone number"
                    error={!!errors.mdPhoneNo}
                    className="w-full"
                  />
                  {errors.mdPhoneNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdPhoneNo}</p>}
                </div>

                {/* MD Email */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">MD Email ID *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.mdEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="mdEmail"
                    value={formData.mdEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.mdEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdEmail}</p>}
                </div>

                {/* Office Contact Person */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Contact Person Office *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.officeContactPerson
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="officeContactPerson"
                    value={formData.officeContactPerson}
                    onChange={handleInputChange}
                    placeholder="Office Contact Person"
                  />
                  {errors.officeContactPerson && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeContactPerson}</p>}
                </div>

                {/* Office Mobile No */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Office Mobile No *</label>
                  <MobileInput
                    value={formData.officeMobileNo}
                    onChange={handleInputChange}
                    name="officeMobileNo"
                    placeholder="Office mobile number"
                    error={!!errors.officeMobileNo}
                    className="w-full"
                  />
                  {errors.officeMobileNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeMobileNo}</p>}
                </div>

                {/* Office Email */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Office Email ID *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.officeEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="officeEmail"
                    value={formData.officeEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.officeEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeEmail}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Task 1: Professional Contacts */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaClipboardList className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Professional Contacts
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Auditor Name */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Auditor Name *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.auditorName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="auditorName"
                    value={formData.auditorName}
                    onChange={handleInputChange}
                    placeholder="Auditor Name"
                  />
                  {errors.auditorName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorName}</p>}
                </div>

                {/* Auditor No */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Auditor No *</label>
                  <MobileInput
                    value={formData.auditorNo}
                    onChange={handleInputChange}
                    name="auditorNo"
                    placeholder="Auditor phone number"
                    error={!!errors.auditorNo}
                    className="w-full"
                  />
                  {errors.auditorNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorNo}</p>}
                </div>

                {/* Auditor Email */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Auditor Email ID *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.auditorEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="auditorEmail"
                    value={formData.auditorEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.auditorEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorEmail}</p>}
                </div>

                {/* Tax Consultant Name */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Tax Consultant Name *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.taxConsultantName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="taxConsultantName"
                    value={formData.taxConsultantName}
                    onChange={handleInputChange}
                    placeholder="Tax Consultant Name"
                  />
                  {errors.taxConsultantName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantName}</p>}
                </div>

                {/* Tax Consultant No */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Tax Consultant No *</label>
                  <MobileInput
                    value={formData.taxConsultantNo}
                    onChange={handleInputChange}
                    name="taxConsultantNo"
                    placeholder="Tax consultant phone number"
                    error={!!errors.taxConsultantNo}
                    className="w-full"
                  />
                  {errors.taxConsultantNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantNo}</p>}
                </div>

                {/* Tax Consultant Email */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Tax Consultant Email ID *</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.taxConsultantEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="taxConsultantEmail"
                    value={formData.taxConsultantEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.taxConsultantEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantEmail}</p>}
                </div>

                {/* IT Name (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">IT Name</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="itName"
                    value={formData.itName}
                    onChange={handleInputChange}
                    placeholder="IT Person Name (Optional)"
                  />
                </div>

                {/* IT No (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">IT No</label>
                  <MobileInput
                    value={formData.itNo}
                    onChange={handleInputChange}
                    name="itNo"
                    placeholder="IT phone number (Optional)"
                    error={!!errors.itNo}
                    className="w-full"
                  />
                  {errors.itNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.itNo}</p>}
                </div>

                {/* IT Email (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">IT Email ID</label>
                  <input
                    type="email"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="itEmail"
                    value={formData.itEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL> (Optional)"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Task 1: Business Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaBuilding className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Business Information
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Area */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Area (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.area
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300'
                    }`}
                    name="area"
                    value={formData.area}
                    onChange={handleInputChange}
                    placeholder="Business Area"
                  />
                  {errors.area && <p className="mt-2 text-sm text-red-600 font-medium">{errors.area}</p>}
                </div>

                {/* PIN Code */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">PIN Code</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="pinCode"
                    value={formData.pinCode}
                    onChange={handleInputChange}
                    placeholder="600001 (Optional)"
                    maxLength="6"
                  />
                </div>

                {/* State Country */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">State Country</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="stateCountry"
                    value={formData.stateCountry}
                    onChange={handleInputChange}
                    placeholder="Tamil Nadu, India (Optional)"
                  />
                </div>

                {/* No. of Tally Users */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">No. of Tally Users (Optional)</label>
                  <input
                    type="number"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="noOfTallyUsers"
                    value={formData.noOfTallyUsers}
                    onChange={handleInputChange}
                    placeholder="Number of Tally Users"
                    min="1"
                  />
                </div>

                {/* Executive Name */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Executive Name (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.executiveName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                    }`}
                    name="executiveName"
                    value={formData.executiveName}
                    onChange={handleInputChange}
                    placeholder="Executive Name"
                  />
                  {errors.executiveName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.executiveName}</p>}
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Status (Optional)</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Status</option>
                    <option value="ACTIVE">✅ Active</option>
                    <option value="INACTIVE">❌ Inactive</option>
                    <option value="PENDING">⏳ Pending</option>
                    <option value="FOLLOW_UP">📞 Follow Up</option>
                  </select>
                </div>
              </div>

              {/* Task 3: Log Details removed - logs should be automatic, not manual entry */}
            </div>
          </div>

          {/* Customer Address Book */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaUser className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Customer Address Book
                <span className="ml-2 text-sm px-2 py-1 rounded-full" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  Owner contact is mandatory
                </span>
              </h3>
            </div>
            <div className="p-6 form-section-background">
              {formData.addressBook.map((entry, index) => (
                <div key={index} className="mb-6 p-6 bg-white rounded-xl border-2 border-gray-200 shadow-sm">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-lg font-bold text-gray-700">Contact #{index + 1}</h4>
                    {formData.addressBook.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeAddressBookEntry(index)}
                        className="text-gray-600 hover:text-gray-800 transition-colors duration-200"
                      >
                        <FaMinus className="h-5 w-5" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {/* Type */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">
                        Type *
                        {designationOptions.find(d => d.value === entry.type)?.isMandatory && (
                          <span className="text-red-600 ml-1">(Mandatory)</span>
                        )}
                      </label>
                      <select
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_type`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        value={entry.type}
                        onChange={(e) => handleAddressBookChange(index, 'type', e.target.value)}
                      >
                        <option value="">Select type</option>
                        {designationOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      {errors[`addressBook_${index}_type`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_type`]}</p>
                      )}
                    </div>

                    {/* Contact Person */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Contact Person *</label>
                      <input
                        type="text"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_contactPerson`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        value={entry.contactPerson}
                        onChange={(e) => handleAddressBookChange(index, 'contactPerson', e.target.value)}
                        placeholder="Enter contact person name"
                      />
                      {errors[`addressBook_${index}_contactPerson`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_contactPerson`]}</p>
                      )}
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Phone</label>
                      <MobileInput
                        value={entry.phone}
                        onChange={(e) => handleAddressBookChange(index, 'phone', e.target.value)}
                        placeholder="Enter phone number"
                        className="w-full"
                      />
                    </div>

                    {/* Email */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-bold text-gray-700 mb-2">Email *</label>
                      <input
                        type="email"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_email`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        value={entry.email}
                        onChange={(e) => handleAddressBookChange(index, 'email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                      {errors[`addressBook_${index}_email`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_email`]}</p>
                      )}
                    </div>
                  </div>

                  {/* Mobile Numbers */}
                  <div className="mt-4">
                    <label className="block text-sm font-bold text-gray-700 mb-2">Mobile Numbers *</label>
                    {entry.mobileNumbers.map((mobile, mobileIndex) => (
                      <div key={mobileIndex} className="flex gap-2 mb-2">
                        <MobileInput
                          value={mobile}
                          onChange={(e) => handleMobileNumberChange(index, mobileIndex, e.target.value)}
                          placeholder="Enter mobile number"
                          error={!!errors[`addressBook_${index}_mobile`]}
                          className="flex-1"
                        />
                        {entry.mobileNumbers.length > 1 && (
                          <button
                            type="button"
                            onClick={() => removeMobileNumber(index, mobileIndex)}
                            className="px-3 py-2 bg-gray-500 text-white rounded-xl hover:bg-gray-600 transition-colors duration-200"
                          >
                            <FaMinus className="h-4 w-4" />
                          </button>
                        )}
                        {mobileIndex === entry.mobileNumbers.length - 1 && (
                          <button
                            type="button"
                            onClick={() => addMobileNumber(index)}
                            className="px-3 py-2 btn-primary rounded-xl transition-colors duration-200"
                          >
                            <FaPlus className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    ))}
                    {errors[`addressBook_${index}_mobile`] && (
                      <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_mobile`]}</p>
                    )}
                  </div>
                </div>
              ))}

              {/* Add More Address Book Entry */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={addAddressBookEntry}
                  className="inline-flex items-center px-6 py-3 bg-primary-dynamic font-bold rounded-xl hover:opacity-90 transition-all duration-200 shadow-lg"
                  style={{ backgroundColor: 'var(--primary-color)', color: 'var(--primary-text)' }}
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Add More Contact
                </button>
              </div>

              {errors.addressBook && (
                <p className="mt-4 text-sm text-red-600 font-medium text-center">{errors.addressBook}</p>
              )}
            </div>
          </div>

          {/* TSS Status Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaShieldAlt className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Tally Software Service (TSS)
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* TSS Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">TSS Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="tssStatus"
                    value={formData.tssStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional fields based on TSS Status */}
                {formData.tssStatus === 'YES' ? (
                  <>
                    {/* TSS Expiry Date */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">TSS Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.tssExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="tssExpiryDate"
                        value={formData.tssExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.tssExpiryDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tssExpiryDate}</p>}
                    </div>

                    {/* Admin Email */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Admin Email *</label>
                      <input
                        type="email"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.adminEmail
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="adminEmail"
                        value={formData.adminEmail}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                      />
                      {errors.adminEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.adminEmail}</p>}
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </div>

          {/* AMC Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaCalendar className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Annual Maintenance Contract (AMC)
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* AMC Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">AMC Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="amcStatus"
                    value={formData.amcStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional fields based on AMC Status */}
                {formData.amcStatus === 'YES' && (
                  <>
                    {/* AMC From Date */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">AMC From Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcFromDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="amcFromDate"
                        value={formData.amcFromDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcFromDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcFromDate}</p>}
                    </div>

                    {/* AMC To Date */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">AMC To Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcToDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="amcToDate"
                        value={formData.amcToDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcToDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcToDate}</p>}
                    </div>

                    {/* Renewal Date */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Renewal Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.renewalDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="renewalDate"
                        value={formData.renewalDate}
                        onChange={handleInputChange}
                      />
                      {errors.renewalDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.renewalDate}</p>}
                    </div>

                    {/* No of Visits */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">No of Visits *</label>
                      <input
                        type="number"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.noOfVisits
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="noOfVisits"
                        value={formData.noOfVisits}
                        onChange={handleInputChange}
                        placeholder="Enter number of visits"
                        min="1"
                      />
                      {errors.noOfVisits && <p className="mt-2 text-sm text-red-600 font-medium">{errors.noOfVisits}</p>}
                    </div>

                    {/* Current AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Current AMC Amount (₹) *</label>
                      <input
                        type="number"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.currentAmcAmount
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="currentAmcAmount"
                        value={formData.currentAmcAmount}
                        onChange={handleInputChange}
                        placeholder="Enter current AMC amount"
                        min="0"
                        step="0.01"
                      />
                      {errors.currentAmcAmount && <p className="mt-2 text-sm text-red-600 font-medium">{errors.currentAmcAmount}</p>}
                    </div>

                    {/* Last Year AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-gray-700 mb-2">Last Year AMC Amount (₹)</label>
                      <input
                        type="number"
                        className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus-primary bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                        name="lastYearAmcAmount"
                        value={formData.lastYearAmcAmount}
                        onChange={handleInputChange}
                        placeholder="Enter last year AMC amount"
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>


          {/* Additional Features */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaClipboardList className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Additional Features
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
                {/* TDL & Addons */}
                <div className="p-4 bg-white rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="tdlAddons"
                      name="tdlAddons"
                      checked={formData.tdlAddons}
                      onChange={handleInputChange}
                      className="w-5 h-5 focus-primary bg-gray-100 border-gray-300 rounded"
                      style={{
                        accentColor: 'var(--primary-color, #2f69b3)'
                      }}
                    />
                    <label htmlFor="tdlAddons" className="text-sm font-bold text-gray-700 cursor-pointer">
                      🔧 TDL & Addons
                    </label>
                  </div>
                  {formData.tdlAddons && (
                    <div className="space-y-3">
                      {/* Expiry Date */}
                      <div>
                        <label className="block text-xs font-bold text-gray-600 mb-1">Expiry Date *</label>
                        <input
                          type="date"
                          className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                            errors.tdlAddonsExpiryDate
                              ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                              : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                          }`}
                          name="tdlAddonsExpiryDate"
                          value={formData.tdlAddonsExpiryDate}
                          onChange={handleInputChange}
                        />
                        {errors.tdlAddonsExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.tdlAddonsExpiryDate}</p>}
                      </div>

                      {/* File Upload */}
                      <div>
                        <label className="block text-xs font-bold text-gray-600 mb-1">Upload File (Optional)</label>
                        <input
                          type="file"
                          className="block w-full px-3 py-2 border-2 border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus-primary bg-white hover:border-gray-300 transition-all duration-200 text-xs file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-xs file:font-medium file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100"
                          name="tdlAddonsFile"
                          onChange={handleFileUpload}
                          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        />
                        {formData.tdlAddonsFile && (
                          <p className="mt-1 text-xs text-gray-600">
                            📎 {formData.tdlAddonsFile.name} ({(formData.tdlAddonsFile.size / 1024 / 1024).toFixed(2)} MB)
                          </p>
                        )}
                        <p className="mt-1 text-xs text-gray-500">Supported: PDF, DOC, DOCX, JPG, PNG (Max 5MB)</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* WhatsApp/Telegram Group */}
                <div className="p-4 bg-white rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="whatsappTelegramGroup"
                      name="whatsappTelegramGroup"
                      checked={formData.whatsappTelegramGroup}
                      onChange={handleInputChange}
                      className="w-5 h-5 focus-primary bg-gray-100 border-gray-300 rounded"
                      style={{
                        accentColor: 'var(--primary-color, #2f69b3)'
                      }}
                    />
                    <label htmlFor="whatsappTelegramGroup" className="text-sm font-bold text-gray-700 cursor-pointer">
                      📱 WhatsApp/Telegram Group
                    </label>
                  </div>
                  {formData.whatsappTelegramGroup && (
                    <div>
                      <label className="block text-xs font-bold text-gray-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.whatsappTelegramGroupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="whatsappTelegramGroupExpiryDate"
                        value={formData.whatsappTelegramGroupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.whatsappTelegramGroupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.whatsappTelegramGroupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Auto Backup */}
                <div className="p-4 bg-white rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="autoBackup"
                      name="autoBackup"
                      checked={formData.autoBackup}
                      onChange={handleInputChange}
                      className="w-5 h-5 focus-primary bg-gray-100 border-gray-300 rounded"
                      style={{
                        accentColor: 'var(--primary-color, #2f69b3)'
                      }}
                    />
                    <label htmlFor="autoBackup" className="text-sm font-bold text-gray-700 cursor-pointer">
                      💾 Auto Backup
                    </label>
                  </div>
                  {formData.autoBackup && (
                    <div>
                      <label className="block text-xs font-bold text-gray-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.autoBackupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="autoBackupExpiryDate"
                        value={formData.autoBackupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.autoBackupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.autoBackupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Cloud User */}
                <div className="p-4 bg-white rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="cloudUser"
                      name="cloudUser"
                      checked={formData.cloudUser}
                      onChange={handleInputChange}
                      className="w-5 h-5 focus-primary bg-gray-100 border-gray-300 rounded"
                      style={{
                        accentColor: 'var(--primary-color, #2f69b3)'
                      }}
                    />
                    <label htmlFor="cloudUser" className="text-sm font-bold text-gray-700 cursor-pointer">
                      ☁️ Cloud User
                    </label>
                  </div>
                  {formData.cloudUser && (
                    <div>
                      <label className="block text-xs font-bold text-gray-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.cloudUserExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="cloudUserExpiryDate"
                        value={formData.cloudUserExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.cloudUserExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.cloudUserExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Mobile App */}
                <div className="p-4 bg-white rounded-xl border border-gray-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="mobileApp"
                      name="mobileApp"
                      checked={formData.mobileApp}
                      onChange={handleInputChange}
                      className="w-5 h-5 focus-primary bg-gray-100 border-gray-300 rounded"
                      style={{
                        accentColor: 'var(--primary-color, #2f69b3)'
                      }}
                    />
                    <label htmlFor="mobileApp" className="text-sm font-bold text-gray-700 cursor-pointer">
                      📱 Mobile App
                    </label>
                  </div>
                  {formData.mobileApp && (
                    <div>
                      <label className="block text-xs font-bold text-gray-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.mobileAppExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-gray-200 focus-primary bg-white hover:border-gray-300'
                        }`}
                        name="mobileAppExpiryDate"
                        value={formData.mobileAppExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.mobileAppExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.mobileAppExpiryDate}</p>}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>

        {/* Map Location Picker Modal */}
        <MapLocationPicker
          isOpen={showMapPicker}
          onClose={() => setShowMapPicker(false)}
          onLocationSelect={handleLocationSelect}
          initialLocation={formData.mapLocation}
          initialLat={formData.latitude}
          initialLng={formData.longitude}
        />

        {/* Error Popup */}
        {showErrorPopup && (
          <ErrorPopup
            errors={errors}
            onClose={() => setShowErrorPopup(false)}
          />
        )}
      </div>
    </div>
  );
};

export default CustomerFormValidated;
