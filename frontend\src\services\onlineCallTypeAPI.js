import { apiService } from './api';

/**
 * Online Call Type API Service
 * Handles all API calls related to online call types/issues management
 */
class OnlineCallTypeAPI {
  constructor() {
    this.baseURL = '/master-data/online-call-types';
  }

  /**
   * Get all online call types with pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response with online call types and pagination
   */
  async getAll(params = {}) {
    try {
      const queryParams = new URLSearchParams();

      // Add pagination parameters
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);

      // Add filter parameters
      if (params.search) queryParams.append('search', params.search);
      if (params.category) queryParams.append('category', params.category);
      if (params.isActive !== undefined) queryParams.append('isActive', params.isActive);

      // Add sorting parameters
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

      const url = `${this.baseURL}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiService.get(url);

      return response.data;
    } catch (error) {
      console.error('Error fetching online call types:', error);
      throw error;
    }
  }

  /**
   * Get online call type by ID
   * @param {string} id - Online call type ID
   * @returns {Promise<Object>} API response with online call type data
   */
  async getById(id) {
    try {
      const response = await apiService.get(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching online call type:', error);
      throw error;
    }
  }

  /**
   * Create new online call type
   * @param {Object} data - Online call type data
   * @returns {Promise<Object>} API response with created online call type
   */
  async create(data) {
    try {
      const response = await apiService.post(this.baseURL, data);
      return response.data;
    } catch (error) {
      console.error('Error creating online call type:', error);
      throw error;
    }
  }

  /**
   * Update online call type
   * @param {string} id - Online call type ID
   * @param {Object} data - Updated online call type data
   * @returns {Promise<Object>} API response with updated online call type
   */
  async update(id, data) {
    try {
      const response = await apiService.put(`${this.baseURL}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating online call type:', error);
      throw error;
    }
  }

  /**
   * Delete online call type (soft delete)
   * @param {string} id - Online call type ID
   * @returns {Promise<Object>} API response
   */
  async delete(id) {
    try {
      const response = await apiService.delete(`${this.baseURL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting online call type:', error);
      throw error;
    }
  }

  /**
   * Search online call types for dropdown
   * @param {Object} params - Search parameters
   * @returns {Promise<Array>} Array of online call types
   */
  async search(params = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (params.q) queryParams.append('q', params.q);
      if (params.category) queryParams.append('category', params.category);
      if (params.limit) queryParams.append('limit', params.limit);

      const url = `${this.baseURL}/search${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiService.get(url);

      return response.data.data || [];
    } catch (error) {
      console.error('Error searching online call types:', error);
      throw error;
    }
  }

  /**
   * Get all categories with counts
   * @returns {Promise<Array>} Array of categories
   */
  async getCategories() {
    try {
      const response = await apiService.get(`${this.baseURL}/categories`);
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Bulk update status of online call types
   * @param {Array} ids - Array of online call type IDs
   * @param {boolean} isActive - New status
   * @returns {Promise<Object>} API response
   */
  async bulkUpdateStatus(ids, isActive) {
    try {
      const response = await apiService.put(`${this.baseURL}/bulk/status`, {
        ids,
        is_active: isActive,
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk updating status:', error);
      throw error;
    }
  }

  /**
   * Get active online call types for dropdown (cached)
   * @param {Object} params - Optional parameters
   * @returns {Promise<Array>} Array of active online call types
   */
  async getActiveForDropdown(params = {}) {
    try {
      const searchParams = {
        limit: 100,
        ...params,
      };

      const callTypes = await this.search(searchParams);

      // Format for dropdown usage
      return callTypes.map(callType => ({
        id: callType.id,
        name: callType.name,
        category: callType.category,
        displayName: callType.category ? `${callType.category} - ${callType.name}` : callType.name,
        description: callType.description,
      }));
    } catch (error) {
      console.error('Error fetching active online call types:', error);
      return [];
    }
  }

  /**
   * Get online call types grouped by category
   * @returns {Promise<Object>} Object with categories as keys and call types as values
   */
  async getGroupedByCategory() {
    try {
      const callTypes = await this.search({ limit: 500 });

      const grouped = {};
      callTypes.forEach(callType => {
        const category = callType.category || 'Uncategorized';
        if (!grouped[category]) {
          grouped[category] = [];
        }
        grouped[category].push({
          id: callType.id,
          name: callType.name,
          description: callType.description,
        });
      });

      // Sort categories and call types within each category
      const sortedGrouped = {};
      Object.keys(grouped)
        .sort()
        .forEach(category => {
          sortedGrouped[category] = grouped[category].sort((a, b) => a.name.localeCompare(b.name));
        });

      return sortedGrouped;
    } catch (error) {
      console.error('Error fetching grouped online call types:', error);
      return {};
    }
  }

  /**
   * Validate online call type data
   * @param {Object} data - Online call type data to validate
   * @returns {Object} Validation result with errors
   */
  validateData(data) {
    const errors = {};

    // Name validation
    if (!data.name || data.name.trim().length === 0) {
      errors.name = 'Name is required';
    } else if (data.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    } else if (data.name.trim().length > 100) {
      errors.name = 'Name must be less than 100 characters';
    }

    // Category validation
    if (data.category && data.category.trim().length > 50) {
      errors.category = 'Category must be less than 50 characters';
    }

    // Description validation
    if (data.description && data.description.trim().length > 500) {
      errors.description = 'Description must be less than 500 characters';
    }

    // Sort order validation
    if (data.sort_order !== undefined && data.sort_order !== null) {
      if (!Number.isInteger(data.sort_order) || data.sort_order < 0) {
        errors.sort_order = 'Sort order must be a non-negative integer';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Format online call type for display
   * @param {Object} callType - Online call type object
   * @returns {Object} Formatted call type
   */
  formatForDisplay(callType) {
    return {
      ...callType,
      displayName: callType.category ? `${callType.category} - ${callType.name}` : callType.name,
      statusText: callType.is_active ? 'Active' : 'Inactive',
      statusColor: callType.is_active ? 'green' : 'red',
      createdDate: callType.created_at ? new Date(callType.created_at).toLocaleDateString() : '',
      updatedDate: callType.updated_at ? new Date(callType.updated_at).toLocaleDateString() : '',
    };
  }
}

// Export singleton instance
const onlineCallTypeAPI = new OnlineCallTypeAPI();
export default onlineCallTypeAPI;
