#!/usr/bin/env node

/**
 * Test script for Service Stats API
 * Tests the /api/v1/service-calls/stats endpoint
 */

import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:8080/api/v1';
const TEST_TOKEN = 'your-test-token-here'; // Replace with actual token

async function testServiceStatsAPI() {
  console.log('🧪 Testing Service Stats API...\n');

  try {
    // Test the service stats endpoint
    console.log('📡 Testing GET /service-calls/stats');
    
    const response = await fetch(`${API_BASE_URL}/service-calls/stats`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });

    console.log(`Status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response received');

    // Validate response structure
    console.log('\n📊 Validating Response Structure:');
    
    const validations = [
      { field: 'success', expected: 'boolean', actual: typeof data.success },
      { field: 'data', expected: 'object', actual: typeof data.data },
      { field: 'data.totalCalls', expected: 'number', actual: typeof data.data?.totalCalls },
      { field: 'data.recentCalls', expected: 'number', actual: typeof data.data?.recentCalls },
      { field: 'data.callsByStatus', expected: 'array', actual: Array.isArray(data.data?.callsByStatus) ? 'array' : typeof data.data?.callsByStatus },
      { field: 'data.callsByType', expected: 'object', actual: typeof data.data?.callsByType },
      { field: 'data.callsByType.freeCalls', expected: 'number', actual: typeof data.data?.callsByType?.freeCalls },
      { field: 'data.callsByType.amcCalls', expected: 'number', actual: typeof data.data?.callsByType?.amcCalls },
      { field: 'data.callsByType.paidCalls', expected: 'number', actual: typeof data.data?.callsByType?.paidCalls },
      { field: 'data.monthlyGrowth', expected: 'object', actual: typeof data.data?.monthlyGrowth },
    ];

    let validationsPassed = 0;
    validations.forEach(validation => {
      const passed = validation.expected === validation.actual;
      console.log(`${passed ? '✅' : '❌'} ${validation.field}: ${validation.actual} (expected: ${validation.expected})`);
      if (passed) validationsPassed++;
    });

    console.log(`\n📈 Validation Summary: ${validationsPassed}/${validations.length} passed`);

    // Display actual data
    console.log('\n📋 Actual Data:');
    console.log('Total Calls:', data.data?.totalCalls);
    console.log('Recent Calls:', data.data?.recentCalls);
    console.log('Calls by Status:', data.data?.callsByStatus?.length, 'statuses');
    console.log('Call Types:', {
      free: data.data?.callsByType?.freeCalls,
      amc: data.data?.callsByType?.amcCalls,
      paid: data.data?.callsByType?.paidCalls
    });
    console.log('Monthly Growth Available:', Object.keys(data.data?.monthlyGrowth || {}).length, 'metrics');

    // Test error handling
    console.log('\n🔍 Testing Error Handling:');
    const invalidResponse = await fetch(`${API_BASE_URL}/service-calls/stats-invalid`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });

    console.log(`Invalid endpoint status: ${invalidResponse.status} (expected: 404)`);

    console.log('\n✅ Service Stats API test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the backend server is running on port 8080');
      console.log('   Run: npm run dev (in backend directory)');
    }
  }
}

// Run the test
testServiceStatsAPI();
