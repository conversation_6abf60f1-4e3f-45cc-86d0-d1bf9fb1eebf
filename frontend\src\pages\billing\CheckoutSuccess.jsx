import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link, useSearchParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { subscriptionService } from '../../services/subscriptionService';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CheckoutSuccess = () => {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState(null);
  const [error, setError] = useState(null);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    if (sessionId) {
      verifySession();
    } else {
      setError('No session ID provided');
      setLoading(false);
    }
  }, [sessionId]);

  const verifySession = async () => {
    try {
      setLoading(true);
      // Wait a moment for webhook processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Fetch updated subscription
      const response = await subscriptionService.getCurrentSubscription();
      setSubscription(response.data.subscription);

      toast.success('Subscription activated successfully!');
    } catch (error) {
      console.error('Error verifying session:', error);
      setError('Failed to verify subscription. Please contact support if payment was processed.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Processing your subscription..."
        subtitle="Please wait while we confirm your payment and activate your subscription"
        variant="dashboard"
      />
    );
  }

  if (error) {
    return (
      <>
        <Helmet>
          <title>Checkout Error - TallyCRM</title>
        </Helmet>

        <div className="min-h-screen flex items-center justify-center">
          <Card className="max-w-md w-full">
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="bi bi-exclamation-triangle text-red-600 text-2xl"></i>
              </div>

              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Subscription Error
              </h2>

              <p className="text-gray-600 mb-6">
                {error}
              </p>

              <div className="space-y-3">
                <Link to="/billing/plans" className="block">
                  <Button className="w-full">
                    Try Again
                  </Button>
                </Link>

                <Link to="/support" className="block">
                  <Button variant="outline" className="w-full">
                    Contact Support
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        </div>
      </>
    );
  }

  return (
    <>
      <Helmet>
        <title>Subscription Activated - TallyCRM</title>
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="max-w-lg w-full">
          <div className="p-8 text-center">
            {/* Success Icon */}
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <i className="bi bi-check-circle-fill text-green-600 text-3xl"></i>
            </div>

            {/* Success Message */}
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to TallyCRM!
            </h1>

            <p className="text-gray-600 mb-6">
              Your subscription has been activated successfully. You now have access to all the features of your plan.
            </p>

            {/* Subscription Details */}
            {subscription && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h3 className="font-semibold text-gray-900 mb-3">Subscription Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Plan:</span>
                    <span className="font-medium text-gray-900">{subscription.plan?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="font-medium text-green-600 capitalize">{subscription.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Billing:</span>
                    <span className="font-medium text-gray-900">
                      ₹{subscription.amount}/{subscription.interval}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Next Billing:</span>
                    <span className="font-medium text-gray-900">
                      {new Date(subscription.current_period_end).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Link to="/dashboard" className="block">
                <Button className="w-full btn-theme-primary">
                  <i className="bi bi-house-door mr-2"></i>
                  Go to Dashboard
                </Button>
              </Link>

              <Link to="/billing/dashboard" className="block">
                <Button variant="outline" className="w-full">
                  <i className="bi bi-credit-card mr-2"></i>
                  View Billing Details
                </Button>
              </Link>
            </div>

            {/* Next Steps */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-3">What's Next?</h3>
              <div className="text-left space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <i className="bi bi-check-circle text-green-500 mr-2"></i>
                  <span>Set up your team and invite users</span>
                </div>
                <div className="flex items-center">
                  <i className="bi bi-check-circle text-green-500 mr-2"></i>
                  <span>Import your customer data</span>
                </div>
                <div className="flex items-center">
                  <i className="bi bi-check-circle text-green-500 mr-2"></i>
                  <span>Configure your service call workflows</span>
                </div>
                <div className="flex items-center">
                  <i className="bi bi-check-circle text-green-500 mr-2"></i>
                  <span>Explore reporting and analytics</span>
                </div>
              </div>
            </div>

            {/* Support */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                Need help getting started?{' '}
                <Link to="/support" className="text-purple-600 hover:text-purple-700">
                  Contact our support team
                </Link>
              </p>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

export default CheckoutSuccess;
