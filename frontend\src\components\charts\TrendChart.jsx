/**
 * Trend Chart Component
 * Specialized line chart for displaying trends with comparison periods
 */

import React from 'react';
import Line<PERSON>hart from './LineChart';
import { processTrendData, calculateMovingAverage } from './chartUtils';

const TrendChart = ({
  data = [],
  compareData = null, // Optional comparison data (e.g., previous period)
  dataKey = 'value',
  dateKey = 'date',
  showMovingAverage = false,
  movingAverageWindow = 7,
  showComparison = true,
  height = 300,
  className = '',
  ...props
}) => {
  // Process main data
  const processedData = processTrendData(data, dateKey, dataKey);
  
  // Add moving average if requested
  const dataWithMA = showMovingAverage 
    ? calculateMovingAverage(processedData, movingAverageWindow, dataKey)
    : processedData;

  // Process comparison data if provided
  const processedCompareData = compareData 
    ? processTrendData(compareData, dateKey, dataKey)
    : null;

  // Combine data for chart
  const chartData = dataWithMA.map(item => {
    const result = { ...item };
    
    // Add comparison data if available
    if (processedCompareData) {
      const compareItem = processedCompareData.find(
        comp => comp[dateKey].getTime() === item[dateKey].getTime()
      );
      if (compareItem) {
        result[`${dataKey}_compare`] = compareItem[dataKey];
      }
    }
    
    return result;
  });

  // Configure lines
  const lines = [
    {
      dataKey: dataKey,
      name: 'Current Period',
      color: '#1d5795',
      strokeWidth: 2
    }
  ];

  // Add comparison line if data exists
  if (showComparison && processedCompareData) {
    lines.push({
      dataKey: `${dataKey}_compare`,
      name: 'Previous Period',
      color: '#94a3b8',
      strokeWidth: 2
    });
  }

  // Add moving average line if requested
  if (showMovingAverage) {
    lines.push({
      dataKey: 'movingAverage',
      name: `${movingAverageWindow}-day Average`,
      color: '#f59e0b',
      strokeWidth: 1
    });
  }

  return (
    <LineChart
      data={chartData}
      lines={lines}
      xAxisKey={dateKey}
      height={height}
      className={className}
      {...props}
    />
  );
};

export default TrendChart;
