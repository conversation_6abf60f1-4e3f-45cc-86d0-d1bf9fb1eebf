// Simple test script to verify overdue services API
// Run this with: node test-overdue-api.js

const API_BASE_URL = 'http://localhost:8080/api/v1';

// Test function to check if overdue filter works
async function testOverdueFilter() {
  try {
    console.log('🧪 Testing Overdue Services API...\n');

    // Test 1: Get all service calls
    console.log('1. Testing all service calls...');
    const allResponse = await fetch(`${API_BASE_URL}/service-calls?limit=10`);
    
    if (allResponse.status === 401) {
      console.log('❌ Authentication required. Please test through the frontend application.');
      return;
    }
    
    const allData = await allResponse.json();
    console.log(`   Status: ${allResponse.status}`);
    console.log(`   Total calls: ${allData.data?.pagination?.totalItems || 'N/A'}`);

    // Test 2: Get overdue service calls
    console.log('\n2. Testing overdue service calls filter...');
    const overdueResponse = await fetch(`${API_BASE_URL}/service-calls?isOverdue=true&limit=10`);
    const overdueData = await overdueResponse.json();
    console.log(`   Status: ${overdueResponse.status}`);
    console.log(`   Overdue calls: ${overdueData.data?.pagination?.totalItems || 'N/A'}`);

    // Test 3: Get service call filters
    console.log('\n3. Testing service call filters endpoint...');
    const filtersResponse = await fetch(`${API_BASE_URL}/service-calls/filters`);
    const filtersData = await filtersResponse.json();
    console.log(`   Status: ${filtersResponse.status}`);
    console.log(`   Available statuses: ${filtersData.data?.statuses?.length || 'N/A'}`);

    // Test 4: Test service analytics with filters
    console.log('\n4. Testing service analytics with overdue filter...');
    const analyticsResponse = await fetch(`${API_BASE_URL}/reports/service-analytics?isOverdue=true&dateRange=30`);
    const analyticsData = await analyticsResponse.json();
    console.log(`   Status: ${analyticsResponse.status}`);
    console.log(`   Analytics data available: ${analyticsData.success ? 'Yes' : 'No'}`);

    console.log('\n✅ API tests completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Login to the frontend application');
    console.log('   2. Navigate to Service Reports or Service Analytics');
    console.log('   3. Click "Filters" button');
    console.log('   4. Set "Overdue Status" to "Overdue Only"');
    console.log('   5. Click "Apply Filters"');
    console.log('   6. Verify that overdue services (SC-OVERDUE-001, etc.) appear');

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    console.log('\n📝 Note: This test requires the backend server to be running on port 8080');
  }
}

// Run the test
testOverdueFilter();
