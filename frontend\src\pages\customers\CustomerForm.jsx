import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService, masterDataAPI } from '../../services/api';
import { useSubmitDeduplication } from '../../hooks/useRequestDeduplication';
import SearchableSelect from '../../components/ui/SearchableSelect';
import MapLocationPicker from '../../components/ui/MapLocationPicker';
import MobileInput from '../../components/ui/MobileInput';
import { hasPhoneDigits, validationRules } from '../../utils/validation';
import {
  FaSave,
  FaTimes,
  FaUser,
  FaBuilding,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaPlus,
  FaMinus,
  FaMap,
  FaCog,
  FaCalendar,
  FaShieldAlt,
  FaTools,
  FaClipboardList
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Basic Info
    customerName: '',
    tallySerialNo: '',
    product: null, // Product ID for searchable select
    licenceEdition: null, // License Edition ID for searchable select
    location: null, // Area ID for searchable select
    industry: null, // Task 1: Industry ID for searchable select
    profileStatus: 'FOLLOW UP',
    followUpExecutive: null, // Executive ID for searchable select
    mapLocation: '',
    latitude: '',
    longitude: '',
    gstNo: '',
    remarks: '',

    // Task 1: New required fields
    adminEmail: '', // Email-ID of account admin (mandatory)
    mdContactPerson: '', // Contact Person - MD (mandatory)
    mdPhoneNo: '', // MD Phone No (mandatory)
    mdEmail: '', // MD Email ID (mandatory)
    officeContactPerson: '', // Contact Person Office (mandatory)
    officeMobileNo: '', // Office Mobile No (mandatory)
    officeEmail: '', // Office Email ID (mandatory)
    auditorName: '', // Auditor Name (mandatory)
    auditorNo: '', // Auditor No (mandatory)
    auditorEmail: '', // Auditor Email ID (mandatory)
    taxConsultantName: '', // Tax consultant Name (mandatory)
    taxConsultantNo: '', // Tax Consultant No (mandatory)
    taxConsultantEmail: '', // Tax Consultant Email ID (mandatory)
    itName: '', // IT Name (optional)
    itNo: '', // IT No (optional)
    itEmail: '', // IT Email ID (optional)
    area: '', // Area (mandatory)
    pinCode: '', // PIN Code (optional)
    stateCountry: '', // State Country (optional)
    noOfTallyUsers: '', // No. of Tally Users (mandatory)
    // logDetails removed - Task 3: User logs should be automatic, not manual entry
    executiveName: '', // Executive Name (mandatory)
    status: '', // Status (mandatory)

    // Task 9: Customer active/inactive status
    customerStatus: 'ACTIVE',

    // Task 4: Default call type
    defaultCallType: 'free_call',

    // Notification Preferences
    notificationSms: true,
    notificationEmail: true,
    notificationWhatsapp: true,

    // Customer Address Book (Multiple Entries)
    addressBook: [
      {
        type: '',
        contactPerson: '',
        mobileNumbers: [''],
        phone: '',
        email: '',
        isMandatory: false
      }
    ],

    // Consolidated Services & Features Section
    // Tally Software Service (TSS)
    tssStatus: 'NO',
    tssExpiryDate: '',

    // AMC (Annual Maintenance Contract)
    amcStatus: 'NO',
    amcFromDate: '',
    amcToDate: '',
    renewalDate: '',
    noOfVisits: '',
    currentAmcAmount: '',
    lastYearAmcAmount: '',

    // TDL & Addons (Moved to additional features like TSS)
    tdlAddons: false,
    tdlAddonsExpiryDate: '',
    tdlAddonsFile: null, // File upload for TDL & Addons

    // Additional Features with expiry dates (Task 2)
    whatsappTelegramGroup: false,
    whatsappTelegramGroupExpiryDate: '',
    autoBackup: false,
    autoBackupExpiryDate: '',
    cloudUser: false,
    cloudUserExpiryDate: '',
    mobileApp: false,
    mobileAppExpiryDate: '',


  });

  const [errors, setErrors] = useState({});

  // Map picker state (Task 5)
  const [showMapPicker, setShowMapPicker] = useState(false);

  // Master data states
  const [tallyProducts, setTallyProducts] = useState([]);
  const [licenseEditions, setLicenseEditions] = useState([]);
  const [areas, setAreas] = useState([]);
  const [executives, setExecutives] = useState([]);
  const [industries, setIndustries] = useState([]); // Task 1: Added industries state

  // Loading states
  const [loadingTallyProducts, setLoadingTallyProducts] = useState(false);
  const [loadingLicenseEditions, setLoadingLicenseEditions] = useState(false);
  const [loadingAreas, setLoadingAreas] = useState(false);
  const [loadingExecutives, setLoadingExecutives] = useState(false);
  const [loadingIndustries, setLoadingIndustries] = useState(false); // Task 1: Added industries loading state
  const [loadingDesignations, setLoadingDesignations] = useState(false);

  const designationOptions = [
    { value: 'owner', label: '👑 Owner', isMandatory: true },
    { value: 'manager', label: '👨‍💼 Manager', isMandatory: false },
    { value: 'accountant', label: '📊 Accountant', isMandatory: false },
    { value: 'auditor-CA', label: '🎓 Auditor-CA', isMandatory: false },
    { value: 'auditor-STP', label: '📋 Auditor-STP', isMandatory: false }
  ];

  // State for dropdown data
  const [designations, setDesignations] = useState([]);



  // Helper functions for address book management
  const addAddressBookEntry = () => {
    setFormData(prev => ({
      ...prev,
      addressBook: [
        ...prev.addressBook,
        {
          type: '',
          contactPerson: '',
          mobileNumbers: [''],
          phone: '',
          email: '',
          isMandatory: false
        }
      ]
    }));
  };

  const removeAddressBookEntry = (index) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.filter((_, i) => i !== index)
    }));
  };

  const addMobileNumber = (addressIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? { ...entry, mobileNumbers: [...entry.mobileNumbers, ''] }
          : entry
      )
    }));
  };

  const removeMobileNumber = (addressIndex, mobileIndex) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? { ...entry, mobileNumbers: entry.mobileNumbers.filter((_, i) => i !== mobileIndex) }
          : entry
      )
    }));
  };

  // Auto-fetch MD mobile number when MD contact person is entered
  const fetchMDMobileNumber = async (mdContactPersonName) => {
    if (!mdContactPersonName.trim()) return;

    try {
      // This would be an API call to fetch MD mobile number from existing records
      // For now, we'll implement a simple lookup logic
      // In a real application, this would query the database for existing MD contacts

      // Example implementation - you can replace this with actual API call
      const response = await apiService.get(`/customers/search-md-contact?name=${encodeURIComponent(mdContactPersonName)}`);

      if (response.data?.success && response.data?.data?.mdPhoneNo) {
        setFormData(prev => ({
          ...prev,
          mdPhoneNo: response.data.data.mdPhoneNo
        }));
        toast.success('MD Phone number auto-filled from existing records');
      }
    } catch (error) {
      // Silently fail - this is just a convenience feature
      console.log('Could not auto-fetch MD mobile number:', error.message);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    let processedValue = value;

    // Handle checkbox inputs
    if (type === 'checkbox') {
      processedValue = checked;
    }

    // Convert Tally Serial No to uppercase and allow only alphanumeric characters
    if (name === 'tallySerialNo') {
      processedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Auto-fetch MD mobile number when MD contact person name is entered
    if (name === 'mdContactPerson' && processedValue.trim().length > 2) {
      // Debounce the API call
      const timeoutId = setTimeout(() => {
        fetchMDMobileNumber(processedValue);
      }, 1000);

      // Store timeout ID to clear it if user continues typing
      if (window.mdContactPersonTimeout) {
        clearTimeout(window.mdContactPersonTimeout);
      }
      window.mdContactPersonTimeout = timeoutId;
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleAddressBookChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, i) =>
        i === index ? { ...entry, [field]: value } : entry
      )
    }));
  };

  const handleMobileNumberChange = (addressIndex, mobileIndex, value) => {
    setFormData(prev => ({
      ...prev,
      addressBook: prev.addressBook.map((entry, index) =>
        index === addressIndex
          ? {
              ...entry,
              mobileNumbers: entry.mobileNumbers.map((num, i) =>
                i === mobileIndex ? value : num
              )
            }
          : entry
      )
    }));
  };



  // Map location handler (Task 5)
  const handleLocationSelect = (locationData) => {
    setFormData(prev => ({
      ...prev,
      mapLocation: locationData.address,
      latitude: locationData.latitude,
      longitude: locationData.longitude
    }));
  };

  // File upload handler for TDL & Addons
  const handleFileUpload = (e) => {
    const { name, files } = e.target;
    const file = files[0];

    if (file) {
      // Validate file type (allow common document formats)
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'image/jpg'
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a valid file (PDF, DOC, DOCX, JPG, PNG)');
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast.error('File size must be less than 5MB');
        return;
      }

      setFormData(prev => ({
        ...prev,
        [name]: file
      }));

      toast.success(`File "${file.name}" uploaded successfully`);
    }
  };

  // Load all master data from API

  const loadTallyProducts = async () => {
    try {
      setLoadingTallyProducts(true);
      const response = await masterDataAPI.getTallyProducts({
        isActive: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.tallyproduct) {
        setTallyProducts(response.data.data.tallyproduct);
      }
    } catch (error) {
      console.error('Error loading tally products:', error);
      toast.error('Failed to load tally products');
    } finally {
      setLoadingTallyProducts(false);
    }
  };

  const loadLicenseEditions = async () => {
    try {
      setLoadingLicenseEditions(true);
      const response = await masterDataAPI.getLicenseEditions({
        isActive: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.licenseedition) {
        setLicenseEditions(response.data.data.licenseedition);
      }
    } catch (error) {
      console.error('Error loading license editions:', error);
      toast.error('Failed to load license editions');
    } finally {
      setLoadingLicenseEditions(false);
    }
  };

  const loadAreas = async () => {
    try {
      setLoadingAreas(true);
      const response = await masterDataAPI.getAreas({
        isActive: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.area) {
        setAreas(response.data.data.area);
      }
    } catch (error) {
      console.error('Error loading areas:', error);
      toast.error('Failed to load areas');
    } finally {
      setLoadingAreas(false);
    }
  };

  const loadExecutives = async () => {
    try {
      setLoadingExecutives(true);
      const response = await masterDataAPI.getExecutives({
        isActive: true,
        limit: 100,
        sortBy: 'first_name',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.executives) {
        setExecutives(response.data.data.executives);
      }
    } catch (error) {
      console.error('Error loading executives:', error);
      toast.error('Failed to load executives');
    } finally {
      setLoadingExecutives(false);
    }
  };

  const loadDesignations = async () => {
    try {
      setLoadingDesignations(true);
      const response = await masterDataAPI.getDesignations({
        isActive: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.designation) {
        setDesignations(response.data.data.designation);
      }
    } catch (error) {
      console.error('Error loading designations:', error);
      toast.error('Failed to load designations');
    } finally {
      setLoadingDesignations(false);
    }
  };

  // Task 1: Load industries function
  const loadIndustries = async () => {
    try {
      setLoadingIndustries(true);
      const response = await masterDataAPI.getIndustries({
        isActive: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data?.industry) {
        setIndustries(response.data.data.industry);
      }
    } catch (error) {
      console.error('Error loading industries:', error);
      toast.error('Failed to load industries');
    } finally {
      setLoadingIndustries(false);
    }
  };





  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.tallySerialNo.trim()) {
      newErrors.tallySerialNo = 'Tally Serial No is required';
    }

    // Task 1: New mandatory fields validation
    if (!formData.adminEmail.trim()) {
      newErrors.adminEmail = 'Email-ID of account admin is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.adminEmail)) {
      newErrors.adminEmail = 'Please enter a valid admin email';
    }

    if (!formData.mdContactPerson.trim()) {
      newErrors.mdContactPerson = 'Contact Person - MD is required';
    }

    if (!formData.mdPhoneNo.trim()) {
      newErrors.mdPhoneNo = 'MD Phone No is required';
    } else {
      const phoneValidation = validationRules.phone(formData.mdPhoneNo, true);
      if (!phoneValidation.isValid) {
        newErrors.mdPhoneNo = phoneValidation.message;
      }
    }

    if (!formData.mdEmail.trim()) {
      newErrors.mdEmail = 'MD Email ID is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.mdEmail)) {
      newErrors.mdEmail = 'Please enter a valid MD email';
    }

    if (!formData.officeContactPerson.trim()) {
      newErrors.officeContactPerson = 'Contact Person Office is required';
    }

    if (!formData.officeMobileNo.trim()) {
      newErrors.officeMobileNo = 'Office Mobile No is required';
    } else {
      const phoneValidation = validationRules.phone(formData.officeMobileNo, true);
      if (!phoneValidation.isValid) {
        newErrors.officeMobileNo = phoneValidation.message;
      }
    }

    // Professional Contacts section - now optional with conditional validation
    // Office Email - completely optional
    if (formData.officeEmail.trim() && !/\S+@\S+\.\S+/.test(formData.officeEmail)) {
      newErrors.officeEmail = 'Please enter a valid office email';
    }

    // Auditor fields - conditional validation (if any field is filled, all are required)
    const auditorFields = ['auditorName', 'auditorNo', 'auditorEmail'];
    const hasAnyAuditorField = auditorFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For auditor phone number, check actual phone digits
      if (field === 'auditorNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyAuditorField) {
      if (!formData.auditorName.trim()) {
        newErrors.auditorName = 'Auditor Name is required when any auditor field is filled';
      }
      if (!formData.auditorNo.trim()) {
        newErrors.auditorNo = 'Auditor No is required when any auditor field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.auditorNo, false);
        if (!phoneValidation.isValid) {
          newErrors.auditorNo = phoneValidation.message;
        }
      }
      if (!formData.auditorEmail.trim()) {
        newErrors.auditorEmail = 'Auditor Email ID is required when any auditor field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.auditorEmail)) {
        newErrors.auditorEmail = 'Please enter a valid auditor email';
      }
    } else if (formData.auditorEmail.trim() && !/\S+@\S+\.\S+/.test(formData.auditorEmail)) {
      newErrors.auditorEmail = 'Please enter a valid auditor email';
    } else if (formData.auditorNo.trim()) {
      const phoneValidation = validationRules.phone(formData.auditorNo, false);
      if (!phoneValidation.isValid) {
        newErrors.auditorNo = phoneValidation.message;
      }
    }

    // Tax Consultant fields - conditional validation (if any field is filled, all are required)
    const taxConsultantFields = ['taxConsultantName', 'taxConsultantNo', 'taxConsultantEmail'];
    const hasAnyTaxConsultantField = taxConsultantFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For tax consultant phone number, check actual phone digits
      if (field === 'taxConsultantNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyTaxConsultantField) {
      if (!formData.taxConsultantName.trim()) {
        newErrors.taxConsultantName = 'Tax consultant Name is required when any tax consultant field is filled';
      }
      if (!formData.taxConsultantNo.trim()) {
        newErrors.taxConsultantNo = 'Tax Consultant No is required when any tax consultant field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.taxConsultantNo, false);
        if (!phoneValidation.isValid) {
          newErrors.taxConsultantNo = phoneValidation.message;
        }
      }
      if (!formData.taxConsultantEmail.trim()) {
        newErrors.taxConsultantEmail = 'Tax Consultant Email ID is required when any tax consultant field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.taxConsultantEmail)) {
        newErrors.taxConsultantEmail = 'Please enter a valid tax consultant email';
      }
    } else if (formData.taxConsultantEmail.trim() && !/\S+@\S+\.\S+/.test(formData.taxConsultantEmail)) {
      newErrors.taxConsultantEmail = 'Please enter a valid tax consultant email';
    } else if (formData.taxConsultantNo.trim()) {
      const phoneValidation = validationRules.phone(formData.taxConsultantNo, false);
      if (!phoneValidation.isValid) {
        newErrors.taxConsultantNo = phoneValidation.message;
      }
    }

    // Area, Executive Name, No. of Tally Users, and Status are now optional - no validation required

    // IT fields - conditional validation (if any field is filled, all are required)
    const itFields = ['itName', 'itNo', 'itEmail'];
    const hasAnyItField = itFields.some(field => {
      if (!formData[field] || formData[field].trim() === '') return false;

      // For IT phone number, check actual phone digits
      if (field === 'itNo') {
        return hasPhoneDigits(formData[field]);
      }

      return true;
    });

    if (hasAnyItField) {
      if (!formData.itName.trim()) {
        newErrors.itName = 'IT Name is required when any IT field is filled';
      }
      if (!formData.itNo.trim()) {
        newErrors.itNo = 'IT No is required when any IT field is filled';
      } else {
        const phoneValidation = validationRules.phone(formData.itNo, false);
        if (!phoneValidation.isValid) {
          newErrors.itNo = phoneValidation.message;
        }
      }
      if (!formData.itEmail.trim()) {
        newErrors.itEmail = 'IT Email ID is required when any IT field is filled';
      } else if (!/\S+@\S+\.\S+/.test(formData.itEmail)) {
        newErrors.itEmail = 'Please enter a valid IT email';
      }
    } else if (formData.itEmail.trim() && !/\S+@\S+\.\S+/.test(formData.itEmail)) {
      newErrors.itEmail = 'Please enter a valid IT email';
    } else if (formData.itNo.trim()) {
      const phoneValidation = validationRules.phone(formData.itNo, false);
      if (!phoneValidation.isValid) {
        newErrors.itNo = phoneValidation.message;
      }
    }

    // Validate mandatory address book entries
    const mandatoryDesignations = designationOptions.filter(d => d.isMandatory).map(d => d.value);
    const addressBookTypes = formData.addressBook.map(entry => entry.type);

    mandatoryDesignations.forEach(designation => {
      if (!addressBookTypes.includes(designation)) {
        newErrors.addressBook = `${designation} contact is mandatory`;
      }
    });

    // Task 4: Validate that at least one "owner" type exists in address book
    const hasOwnerType = formData.addressBook.some(entry =>
      entry.type && entry.type.toLowerCase().includes('owner')
    );
    if (!hasOwnerType) {
      newErrors.addressBook = 'Customer address book must add the owner type';
    }

    // Validate address book entries
    formData.addressBook.forEach((entry, index) => {
      if (entry.type && !entry.contactPerson.trim()) {
        newErrors[`addressBook_${index}_contactPerson`] = 'Contact person is required';
      }
      if (entry.type && entry.mobileNumbers.every(num => !hasPhoneDigits(num))) {
        newErrors[`addressBook_${index}_mobile`] = 'At least one mobile number is required';
      }
      if (entry.type && !entry.email.trim()) {
        newErrors[`addressBook_${index}_email`] = 'Email is required';
      } else if (entry.email && !/\S+@\S+\.\S+/.test(entry.email)) {
        newErrors[`addressBook_${index}_email`] = 'Please enter a valid email';
      }

      // Validate mobile numbers format
      entry.mobileNumbers.forEach((mobile, mobileIndex) => {
        if (mobile.trim()) {
          const phoneValidation = validationRules.phone(mobile, false);
          if (!phoneValidation.isValid) {
            newErrors[`addressBook_${index}_mobile_${mobileIndex}`] = phoneValidation.message;
          }
        }
      });

      // Validate phone number format
      if (entry.phone && entry.phone.trim()) {
        const phoneValidation = validationRules.phone(entry.phone, false);
        if (!phoneValidation.isValid) {
          newErrors[`addressBook_${index}_phone`] = phoneValidation.message;
        }
      }
    });

    // GST validation
    if (formData.gstNo && formData.gstNo.length !== 15) {
      newErrors.gstNo = 'GST number must be exactly 15 characters';
    }

    // TSS validation
    if (formData.tssStatus === 'YES') {
      if (!formData.tssExpiryDate) {
        newErrors.tssExpiryDate = 'TSS Expiry Date is required when TSS is active';
      }
    }

    // AMC validation
    if (formData.amcStatus === 'YES') {
      if (!formData.amcFromDate) newErrors.amcFromDate = 'AMC From Date is required';
      if (!formData.amcToDate) newErrors.amcToDate = 'AMC To Date is required';
      if (!formData.renewalDate) newErrors.renewalDate = 'Renewal Date is required';
      if (!formData.noOfVisits) newErrors.noOfVisits = 'Number of visits is required';
      if (!formData.currentAmcAmount) newErrors.currentAmcAmount = 'Current AMC amount is required';
    }

    // TDL & Addons validation (moved to additional features)
    if (formData.tdlAddons && !formData.tdlAddonsExpiryDate) {
      newErrors.tdlAddonsExpiryDate = 'Expiry Date is required when TDL & Addons is selected';
    }

    // Additional Features validation (Task 2)
    if (formData.whatsappTelegramGroup && !formData.whatsappTelegramGroupExpiryDate) {
      newErrors.whatsappTelegramGroupExpiryDate = 'Expiry Date is required when WhatsApp/Telegram Group is selected';
    }
    if (formData.autoBackup && !formData.autoBackupExpiryDate) {
      newErrors.autoBackupExpiryDate = 'Expiry Date is required when Auto Backup is selected';
    }
    if (formData.cloudUser && !formData.cloudUserExpiryDate) {
      newErrors.cloudUserExpiryDate = 'Expiry Date is required when Cloud User is selected';
    }
    if (formData.mobileApp && !formData.mobileAppExpiryDate) {
      newErrors.mobileAppExpiryDate = 'Expiry Date is required when Mobile App is selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission logic
  const handleSubmitInternal = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API according to backend Customer model structure
      const customerData = {
        // Required fields
        company_name: formData.customerName && formData.customerName.trim() ? formData.customerName : null,
        customer_code: formData.tallySerialNo, // Using tally serial as customer code

        // Optional basic fields
        display_name: formData.customerName,
        customer_type: formData.customerStatus === 'ACTIVE' ? 'customer' : 'inactive', // Map from customerStatus
        business_type: 'private_limited', // Default business type

        // Contact information from address book
        email: formData.addressBook.find(entry => entry.email)?.email || null,
        phone: formData.addressBook.find(entry => entry.mobileNumbers?.length > 0)?.mobileNumbers[0] || null,

        // Address fields
        address_line_1: formData.mapLocation || null,
        city: formData.location || null,
        state: null,
        country: 'India',
        postal_code: null,

        // Business details
        gst_number: formData.gstNo || null,
        pan_number: null,
        area_id: formData.location || null,
        industry_id: formData.industry || null, // Task 1: Added industry field
        assigned_executive_id: formData.followUpExecutive || null,

        // Tally specific fields - store IDs in custom_fields for now since backend expects names
        tally_version: null, // Will be stored in custom_fields
        tally_serial_number: formData.tallySerialNo,
        license_type: null, // Will be stored in custom_fields

        // Location
        latitude: formData.latitude ? parseFloat(formData.latitude) : null,
        longitude: formData.longitude ? parseFloat(formData.longitude) : null,

        // Additional fields
        notes: formData.remarks || null,

        // Custom fields for additional data that doesn't map to standard fields
        custom_fields: {
          profile_status: formData.profileStatus,
          customer_status: formData.customerStatus, // Task 9
          default_call_type: formData.defaultCallType, // Task 4: Default call type
          follow_up_executive_id: formData.followUpExecutive,
          product_id: formData.product,
          license_edition_id: formData.licenceEdition,
          address_book: formData.addressBook.map(entry => ({
            type: entry.type,
            contact_person: entry.contactPerson,
            mobile_numbers: entry.mobileNumbers.filter(num => num.trim() !== ''),
            phone: entry.phone,
            email: entry.email,
            is_mandatory: entry.isMandatory
          })),
          // Task 1: New fields
          admin_email: formData.adminEmail,
          md_contact_person: formData.mdContactPerson,
          md_phone_no: formData.mdPhoneNo,
          md_email: formData.mdEmail,
          office_contact_person: formData.officeContactPerson,
          office_mobile_no: formData.officeMobileNo,
          office_email: formData.officeEmail,
          auditor_name: formData.auditorName,
          auditor_no: formData.auditorNo,
          auditor_email: formData.auditorEmail,
          tax_consultant_name: formData.taxConsultantName,
          tax_consultant_no: formData.taxConsultantNo,
          tax_consultant_email: formData.taxConsultantEmail,
          it_name: formData.itName || null,
          it_no: formData.itNo || null,
          it_email: formData.itEmail || null,
          area: formData.area,
          pin_code: formData.pinCode || null,
          state_country: formData.stateCountry || null,
          no_of_tally_users: formData.noOfTallyUsers ? parseInt(formData.noOfTallyUsers) : null,
          // log_details: formData.logDetails, // Task 3: Removed manual log entry
          executive_name: formData.executiveName,
          status: formData.status,
          tss_status: formData.tssStatus,
          tss_expiry_date: formData.tssExpiryDate || null,
          amc_status: formData.amcStatus,
          amc_from_date: formData.amcFromDate || null,
          amc_to_date: formData.amcToDate || null,
          renewal_date: formData.renewalDate || null,
          no_of_visits: formData.noOfVisits ? parseInt(formData.noOfVisits) : null,
          current_amc_amount: formData.currentAmcAmount ? parseFloat(formData.currentAmcAmount) : null,
          last_year_amc_amount: formData.lastYearAmcAmount ? parseFloat(formData.lastYearAmcAmount) : null,
          // TDL & Addons (moved to additional features)
          tdl_addons: formData.tdlAddons,
          tdl_addons_expiry_date: formData.tdlAddonsExpiryDate || null,
          // Additional Features with expiry dates (Task 2)
          whatsapp_telegram_group: formData.whatsappTelegramGroup,
          whatsapp_telegram_group_expiry_date: formData.whatsappTelegramGroupExpiryDate || null,
          auto_backup: formData.autoBackup,
          auto_backup_expiry_date: formData.autoBackupExpiryDate || null,
          cloud_user: formData.cloudUser,
          cloud_user_expiry_date: formData.cloudUserExpiryDate || null,
          mobile_app: formData.mobileApp,
          mobile_app_expiry_date: formData.mobileAppExpiryDate || null
        },

        // Status - map from form customerStatus to is_active boolean
        is_active: formData.customerStatus === 'ACTIVE',

        // Notification preferences
        notification_sms: formData.notificationSms,
        notification_email: formData.notificationEmail,
        notification_whatsapp: formData.notificationWhatsapp
      };



      let response;

      // Check if we have file uploads
      const hasFileUploads = formData.tdlAddonsFile;

      if (hasFileUploads) {
        // Use FormData for file uploads
        const formDataToSend = new FormData();

        // Add customer data as JSON string
        formDataToSend.append('customerData', JSON.stringify(customerData));

        // Add files
        if (formData.tdlAddonsFile) {
          formDataToSend.append('tdlAddonsFile', formData.tdlAddonsFile);
        }

        if (isEdit) {
          response = await apiService.put(`/customers/${id}`, formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
        } else {
          response = await apiService.post('/customers', formDataToSend, {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
        }
      } else {
        // Use regular JSON for non-file uploads
        if (isEdit) {
          response = await apiService.put(`/customers/${id}`, customerData);
        } else {
          response = await apiService.post('/customers', customerData);
        }
      }

      if (response.data?.success) {
        toast.success(isEdit ? 'Customer updated successfully' : 'Customer created successfully');
        navigate('/customers');
      } else {
        toast.error(response.data?.message || 'Failed to save customer');
      }
    } catch (error) {
      console.error('Error saving customer:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to save customer information');
    } finally {
      setLoading(false);
    }
  };

  // Use the deduplication hook
  const { handleSubmit } = useSubmitDeduplication(handleSubmitInternal);

  // Load all master data on component mount
  useEffect(() => {
    const loadAllMasterData = async () => {
      await Promise.all([
        loadTallyProducts(),
        loadLicenseEditions(),
        loadAreas(),
        loadExecutives(),
        loadIndustries(), // Task 1: Added industries loading
        loadDesignations()
      ]);
    };

    loadAllMasterData();
  }, []);

  // Load customer data for editing
  useEffect(() => {
    if (isEdit && id) {
      const loadCustomer = async () => {
        try {
          setLoading(true);
          const response = await apiService.get(`/customers/${id}`);
          if (response.data?.success && response.data?.data?.customer) {
            const customer = response.data.data.customer;
            const customFields = customer.custom_fields || {};

            setFormData({
              customerName: customer.company_name || '',
              tallySerialNo: customer.customer_code || customer.tally_serial_number || '',
              product: customFields.product_id || null,
              licenceEdition: customFields.license_edition_id || null,
              location: customer.area_id || null,
              industry: customer.industry_id || customFields.industry_id || null, // Task 1: Added industry field
              profileStatus: customFields.profile_status || 'FOLLOW UP',
              customerStatus: customer.is_active ? 'ACTIVE' : 'INACTIVE', // Task 9 - map from is_active

              // Task 4: Default call type
              defaultCallType: customFields.default_call_type || 'free_call',

              // Notification preferences
              notificationSms: customer.notification_sms !== false,
              notificationEmail: customer.notification_email !== false,
              notificationWhatsapp: customer.notification_whatsapp !== false,

              followUpExecutive: customer.assigned_executive_id || customFields.follow_up_executive_id || null,
              mapLocation: customer.address_line_1 || customFields.map_location || '',
              latitude: customer.latitude || '',
              longitude: customer.longitude || '',
              gstNo: customer.gst_number || '',
              remarks: customer.notes || '',
              // Task 1: New fields
              adminEmail: customFields.admin_email || '',
              mdContactPerson: customFields.md_contact_person || '',
              mdPhoneNo: customFields.md_phone_no || '',
              mdEmail: customFields.md_email || '',
              officeContactPerson: customFields.office_contact_person || '',
              officeMobileNo: customFields.office_mobile_no || '',
              officeEmail: customFields.office_email || '',
              auditorName: customFields.auditor_name || '',
              auditorNo: customFields.auditor_no || '',
              auditorEmail: customFields.auditor_email || '',
              taxConsultantName: customFields.tax_consultant_name || '',
              taxConsultantNo: customFields.tax_consultant_no || '',
              taxConsultantEmail: customFields.tax_consultant_email || '',
              itName: customFields.it_name || '',
              itNo: customFields.it_no || '',
              itEmail: customFields.it_email || '',
              area: customFields.area || '',
              pinCode: customFields.pin_code || '',
              stateCountry: customFields.state_country || '',
              noOfTallyUsers: customFields.no_of_tally_users || '',
              // logDetails: customFields.log_details || '', // Task 3: Removed manual log entry
              executiveName: customFields.executive_name || '',
              status: customFields.status || '',
              addressBook: customFields.address_book && customFields.address_book.length > 0
                ? customFields.address_book.map(entry => ({
                    type: entry.type || '',
                    contactPerson: entry.contact_person || '',
                    mobileNumbers: entry.mobile_numbers && entry.mobile_numbers.length > 0 ? entry.mobile_numbers : [''],
                    phone: entry.phone || '',
                    email: entry.email || '',
                    isMandatory: entry.is_mandatory || false
                  }))
                : [{
                    type: '',
                    contactPerson: '',
                    mobileNumbers: [customer.phone || ''],
                    phone: customer.phone || '',
                    email: customer.email || '',
                    isMandatory: false
                  }],
              tssStatus: customFields.tss_status || 'NO',
              tssExpiryDate: customFields.tss_expiry_date || '',
              amcStatus: customFields.amc_status || 'NO',
              amcFromDate: customFields.amc_from_date || '',
              amcToDate: customFields.amc_to_date || '',
              renewalDate: customFields.renewal_date || '',
              noOfVisits: customFields.no_of_visits || '',
              currentAmcAmount: customFields.current_amc_amount || '',
              lastYearAmcAmount: customFields.last_year_amc_amount || '',
              // TDL & Addons (moved to additional features)
              tdlAddons: customFields.tdl_addons || false,
              tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || '',
              tdlAddonsFile: null, // File will be handled separately for existing customers
              // Additional Features with expiry dates (Task 2)
              whatsappTelegramGroup: customFields.whatsapp_telegram_group || false,
              whatsappTelegramGroupExpiryDate: customFields.whatsapp_telegram_group_expiry_date || '',
              autoBackup: customFields.auto_backup || false,
              autoBackupExpiryDate: customFields.auto_backup_expiry_date || '',
              cloudUser: customFields.cloud_user || false,
              cloudUserExpiryDate: customFields.cloud_user_expiry_date || '',
              mobileApp: customFields.mobile_app || false,
              mobileAppExpiryDate: customFields.mobile_app_expiry_date || '',
              additionalServices: customFields.additional_services || []
            });
          }
        } catch (error) {
          console.error('Error loading customer:', error);
          toast.error('Failed to load customer data');
        } finally {
          setLoading(false);
        }
      };

      loadCustomer();
    }
  }, [isEdit, id]);

  return (
    <div className="min-h-screen bg-gray-50 overflow-x-hidden">
      <div className="w-full max-w-full min-w-0">
        {/* Colorful Header */}
        <div className="mb-6 sm:mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
              <div className="min-w-0">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center text-primary-dynamic">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaBuilding className="text-sm sm:text-lg" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  <span className="truncate">{isEdit ? 'Edit Customer' : 'Add New Customer'}</span>
                </h2>
                <p className="text-sm sm:text-base lg:text-lg" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>
                  {isEdit ? 'Update customer information in your database' : 'Enter customer details to add to your database'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Top Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4 mb-6">
          <button
            type="button"
            onClick={() => navigate('/customers')}
            className="inline-flex items-center justify-center px-6 py-3 border-2 border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 min-h-[44px] touch-manipulation"
          >
            <FaTimes className="mr-2" />
            Cancel
          </button>
          <button
            type="submit"
            form="customer-form"
            disabled={loading}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl bg-primary-dynamic hover:opacity-90 focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg min-h-[44px] touch-manipulation"
            style={{
              backgroundColor: 'var(--primary-color)',
              color: 'var(--primary-text)',
              '--tw-ring-color': 'var(--primary-color)'
            }}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 mr-2" style={{ borderColor: 'var(--primary-text)' }}></div>
                {isEdit ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <FaSave className="mr-2" />
                {isEdit ? 'Update Customer' : 'Create Customer'}
              </>
            )}
          </button>
        </div>

        {/* Form */}
        <form id="customer-form" className="space-y-6 sm:space-y-8 max-w-full overflow-x-hidden" onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-4 sm:px-6 py-3 sm:py-4">
              <h3 className="text-lg sm:text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center mr-2 sm:mr-3 flex-shrink-0" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaBuilding className="h-3 w-3 sm:h-4 sm:w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                <span className="truncate">Basic Information</span>
              </h3>
            </div>
            <div className="p-4 sm:p-6 form-section-background">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {/* Customer Name */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Customer Name *</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    placeholder="Enter customer name"
                  />
                  {errors.customerName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerName}</p>}
                </div>

                {/* Tally Serial No */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">
                    Tally Serial No *
                    <span className="text-xs text-blue-600 ml-2">(e.g., GS001)</span>
                  </label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.tallySerialNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="tallySerialNo"
                    value={formData.tallySerialNo}
                    onChange={handleInputChange}
                    placeholder="GS001"
                  />
                  {errors.tallySerialNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tallySerialNo}</p>}
                </div>

                {/* Product */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">
                    Product
                    <span className="text-xs text-green-600 ml-2">
                      (Type 2+ letters to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={tallyProducts}
                    value={formData.product}
                    onChange={(productId) => setFormData(prev => ({ ...prev, product: productId }))}
                    placeholder="Search products..."
                    searchFields={['name', 'description', 'category']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingTallyProducts}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No products found"
                    searchingText="Type 2+ letters to search products..."
                    renderOption={(option, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                        isHighlighted
                          ? 'bg-green-50 text-green-900'
                          : 'text-gray-900 hover:bg-gray-50'
                      }`}>
                        <div className="font-medium text-green-800">{option.name}</div>
                        {option.description && (
                          <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          {option.category && (
                            <div className="text-xs text-green-600 font-medium bg-green-100 px-2 py-1 rounded-full">
                              {option.category}
                            </div>
                          )}
                          {option.price && (
                            <div className="text-sm font-medium text-gray-700">
                              ₹{parseFloat(option.price).toLocaleString('en-IN')}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  />
                  {loadingTallyProducts && (
                    <p className="mt-2 text-sm text-green-600">Loading products...</p>
                  )}
                </div>

                {/* Licence Edition */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">
                    Licence Edition
                    <span className="text-xs text-orange-600 ml-2">
                      (Type 2+ letters to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={licenseEditions}
                    value={formData.licenceEdition}
                    onChange={(editionId) => setFormData(prev => ({ ...prev, licenceEdition: editionId }))}
                    placeholder="Search license editions..."
                    searchFields={['name', 'description', 'version']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingLicenseEditions}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No license editions found"
                    searchingText="Type 2+ letters to search editions..."
                    renderOption={(option, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                        isHighlighted
                          ? 'bg-orange-50 text-orange-900'
                          : 'text-gray-900 hover:bg-gray-50'
                      }`}>
                        <div className="font-medium text-orange-800">{option.name}</div>
                        {option.description && (
                          <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          {option.version && (
                            <div className="text-xs text-orange-600 font-medium bg-orange-100 px-2 py-1 rounded-full">
                              v{option.version}
                            </div>
                          )}
                          {option.price && (
                            <div className="text-sm font-medium text-gray-700">
                              ₹{parseFloat(option.price).toLocaleString('en-IN')}
                            </div>
                          )}
                        </div>
                        {option.max_users && (
                          <div className="text-xs text-gray-500 mt-1">
                            Max Users: {option.max_users === 999 ? 'Unlimited' : option.max_users}
                          </div>
                        )}
                      </div>
                    )}
                  />
                  {loadingLicenseEditions && (
                    <p className="mt-2 text-sm text-orange-600">Loading license editions...</p>
                  )}
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">
                    Location
                    <span className="text-xs text-teal-600 ml-2">
                      (Type 2+ letters to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={areas}
                    value={formData.location}
                    onChange={(areaId) => setFormData(prev => ({ ...prev, location: areaId }))}
                    placeholder="Search locations..."
                    searchFields={['name', 'city', 'state', 'description']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingAreas}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No locations found"
                    searchingText="Type 2+ letters to search locations..."
                    renderOption={(option, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                        isHighlighted
                          ? 'bg-teal-50 text-teal-900'
                          : 'text-gray-900 hover:bg-gray-50'
                      }`}>
                        <div className="font-medium text-teal-800">{option.name}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          {option.city}, {option.state}
                        </div>
                        {option.description && (
                          <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                        )}
                      </div>
                    )}
                  />
                  {loadingAreas && (
                    <p className="mt-2 text-sm text-teal-600">Loading locations...</p>
                  )}
                </div>

                {/* Task 1: Industry */}
                <div>
                  <label className="block text-sm font-bold text-amber-700 mb-2">
                    Industry
                    <span className="text-xs text-amber-600 ml-2">
                      (Type 2+ letters to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={industries}
                    value={formData.industry}
                    onChange={(industryId) => setFormData(prev => ({ ...prev, industry: industryId }))}
                    placeholder="Search industries..."
                    searchFields={['name', 'description', 'category']}
                    displayField="name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingIndustries}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No industries found"
                    searchingText="Type 2+ letters to search industries..."
                    renderOption={(option, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                        isHighlighted
                          ? 'bg-amber-50 text-amber-900'
                          : 'text-gray-900 hover:bg-gray-50'
                      }`}>
                        <div className="font-medium text-amber-800">{option.name}</div>
                        {option.description && (
                          <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          {option.category && (
                            <div className="text-xs text-amber-600 font-medium bg-amber-100 px-2 py-1 rounded-full">
                              {option.category}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  />
                  {loadingIndustries && (
                    <p className="mt-2 text-sm text-amber-600">Loading industries...</p>
                  )}
                </div>

                {/* Profile Status */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Profile Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300 transition-all duration-200 sm:text-sm"
                    name="profileStatus"
                    value={formData.profileStatus}
                    onChange={handleInputChange}
                  >
                    <option value="FOLLOW UP">📞 FOLLOW UP</option>
                    <option value="OTHERS">📋 OTHERS</option>
                  </select>
                </div>

                {/* Customer Status (Task 9) */}
                <div>
                  <label className="block text-sm font-bold text-emerald-700 mb-2">Customer Status *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerStatus
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-emerald-200 focus:ring-emerald-500 focus:border-emerald-500 bg-white hover:border-emerald-300'
                    }`}
                    name="customerStatus"
                    value={formData.customerStatus}
                    onChange={handleInputChange}
                  >
                    <option value="ACTIVE">✅ Active</option>
                    <option value="INACTIVE">❌ Inactive</option>
                  </select>
                  {errors.customerStatus && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerStatus}</p>}
                </div>

                {/* Default Call Type (Task 4) */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">Default Call Type *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.defaultCallType
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="defaultCallType"
                    value={formData.defaultCallType}
                    onChange={handleInputChange}
                  >
                    <option value="free_call">🆓 Free Call</option>
                    <option value="per_call">💰 Per Call</option>
                    <option value="amc_call">🔧 AMC Call</option>
                  </select>
                  {errors.defaultCallType && <p className="mt-2 text-sm text-red-600 font-medium">{errors.defaultCallType}</p>}
                  <p className="mt-1 text-xs text-gray-600">
                    First 3 services will be free calls, then automatically become per call services
                  </p>
                </div>

                {/* Notification Preferences */}
                <div className="col-span-2">
                  <label className="block text-sm font-bold text-blue-700 mb-3">Notification Preferences</label>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notificationSms"
                        name="notificationSms"
                        checked={formData.notificationSms}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notificationSms" className="ml-2 block text-sm text-gray-900">
                        📱 SMS
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notificationEmail"
                        name="notificationEmail"
                        checked={formData.notificationEmail}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notificationEmail" className="ml-2 block text-sm text-gray-900">
                        📧 Email
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="notificationWhatsapp"
                        name="notificationWhatsapp"
                        checked={formData.notificationWhatsapp}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notificationWhatsapp" className="ml-2 block text-sm text-gray-900">
                        💬 WhatsApp
                      </label>
                    </div>
                  </div>
                </div>

                {/* Follow-up Executive */}
                <div>
                  <label className="block text-sm font-bold text-pink-700 mb-2">
                    Follow-up Executive
                    <span className="text-xs text-pink-600 ml-2">
                      (Type 2+ letters to search)
                    </span>
                  </label>
                  <SearchableSelect
                    options={executives}
                    value={formData.followUpExecutive}
                    onChange={(executiveId) => setFormData(prev => ({ ...prev, followUpExecutive: executiveId }))}
                    placeholder="Search executives..."
                    searchFields={['first_name', 'last_name', 'email', 'employee_code']}
                    displayField="first_name"
                    valueField="id"
                    className="w-full"
                    disabled={loadingExecutives}
                    minSearchLength={2}
                    maxResults={10}
                    allowClear={true}
                    noResultsText="No executives found"
                    searchingText="Type 2+ letters to search executives..."
                    renderOption={(option, isHighlighted) => (
                      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
                        isHighlighted
                          ? 'bg-pink-50 text-pink-900'
                          : 'text-gray-900 hover:bg-gray-50'
                      }`}>
                        <div className="font-medium text-pink-800">
                          {option.first_name} {option.last_name}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {option.employee_code} • {option.email}
                        </div>
                        {option.department && (
                          <div className="text-xs text-pink-600 font-medium bg-pink-100 px-2 py-1 rounded-full inline-block mt-1">
                            {option.department}
                          </div>
                        )}
                      </div>
                    )}
                    renderSelected={(option) => (
                      <span className="block truncate">
                        {option.first_name} {option.last_name} ({option.employee_code})
                      </span>
                    )}
                  />
                  {loadingExecutives && (
                    <p className="mt-2 text-sm text-pink-600">Loading executives...</p>
                  )}
                </div>

                {/* GST No */}
                <div>
                  <label className="block text-sm font-bold text-red-700 mb-2">GST No</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.gstNo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-red-200 focus:ring-red-500 focus:border-red-500 bg-white hover:border-red-300'
                    }`}
                    name="gstNo"
                    value={formData.gstNo}
                    onChange={handleInputChange}
                    placeholder="27AABCU9603R1ZX"
                    maxLength="15"
                  />
                  {errors.gstNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.gstNo}</p>}
                </div>
              </div>

              {/* Task 1: New Contact Information Section */}
              <div className="mt-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                <h4 className="text-xl font-bold text-purple-700 mb-6 flex items-center">
                  <FaUser className="mr-2" />
                  Contact Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Admin Email */}
                  <div>
                    <label className="block text-sm font-bold text-purple-700 mb-2">Email-ID of Account Admin *</label>
                    <input
                      type="email"
                      className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                        errors.adminEmail
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                          : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                      }`}
                      name="adminEmail"
                      value={formData.adminEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                    {errors.adminEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.adminEmail}</p>}
                  </div>

                  {/* MD Contact Person */}
                  <div>
                    <label className="block text-sm font-bold text-blue-700 mb-2">Contact Person - MD *</label>
                    <input
                      type="text"
                      className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                        errors.mdContactPerson
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                          : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                      }`}
                      name="mdContactPerson"
                      value={formData.mdContactPerson}
                      onChange={handleInputChange}
                      placeholder="Managing Director Name"
                    />
                    {errors.mdContactPerson && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdContactPerson}</p>}
                  </div>

                  {/* MD Phone No */}
                  <div>
                    <label className="block text-sm font-bold text-green-700 mb-2">MD Phone No *</label>
                    <MobileInput
                      value={formData.mdPhoneNo}
                      onChange={handleInputChange}
                      name="mdPhoneNo"
                      placeholder="Managing Director phone number"
                      error={errors.mdPhoneNo || !!errors.mdPhoneNo}
                      showErrorMessage={false}
                      className="w-full"
                    />
                    {errors.mdPhoneNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdPhoneNo}</p>}
                  </div>

                  {/* MD Email */}
                  <div>
                    <label className="block text-sm font-bold text-orange-700 mb-2">MD Email ID *</label>
                    <input
                      type="email"
                      className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                        errors.mdEmail
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                          : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                      }`}
                      name="mdEmail"
                      value={formData.mdEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                    {errors.mdEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.mdEmail}</p>}
                  </div>

                  {/* Office Contact Person */}
                  <div>
                    <label className="block text-sm font-bold text-teal-700 mb-2">Contact Person Office *</label>
                    <input
                      type="text"
                      className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                        errors.officeContactPerson
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                          : 'border-teal-200 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300'
                      }`}
                      name="officeContactPerson"
                      value={formData.officeContactPerson}
                      onChange={handleInputChange}
                      placeholder="Office Contact Person"
                    />
                    {errors.officeContactPerson && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeContactPerson}</p>}
                  </div>

                  {/* Office Mobile No */}
                  <div>
                    <label className="block text-sm font-bold text-indigo-700 mb-2">Office Mobile No *</label>
                    <MobileInput
                      value={formData.officeMobileNo}
                      onChange={handleInputChange}
                      name="officeMobileNo"
                      placeholder="Office mobile number"
                      error={errors.officeMobileNo || !!errors.officeMobileNo}
                      showErrorMessage={false}
                      className="w-full"
                    />
                    {errors.officeMobileNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeMobileNo}</p>}
                  </div>
                </div>
              </div>
            </div>
          </div>

              {/* Map Location Section (Task 5) */}
              <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <h4 className="text-lg font-bold text-blue-700 mb-4 flex items-center">
                  <FaMap className="mr-2" />
                  Map Location
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-1">
                    <label className="block text-sm font-bold text-blue-700 mb-2">Map Location</label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="flex-1 px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                        name="mapLocation"
                        value={formData.mapLocation}
                        onChange={handleInputChange}
                        placeholder="Enter location or click map button"
                      />
                      <button
                        type="button"
                        onClick={() => setShowMapPicker(true)}
                        className="px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                        title="Select location on map"
                      >
                        <FaMap className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-green-700 mb-2">Latitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50 transition-all duration-200 sm:text-sm"
                      name="latitude"
                      value={formData.latitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled from map"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-orange-700 mb-2">Longitude</label>
                    <input
                      type="number"
                      step="any"
                      className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-gray-50 transition-all duration-200 sm:text-sm"
                      name="longitude"
                      value={formData.longitude}
                      onChange={handleInputChange}
                      placeholder="Auto-filled from map"
                      readOnly
                    />
                  </div>
                </div>
              </div>

              {/* Remarks */}
              <div className="mt-6">
                <label className="block text-sm font-bold text-gray-700 mb-2">Remarks</label>
                <textarea
                  className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200"
                  name="remarks"
                  value={formData.remarks}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Enter any remarks"
                ></textarea>
              </div>
            </div>
          </div>

          {/* Additional Contact Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaEnvelope className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Professional Contacts
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Office Email */}
                <div>
                  <label className="block text-sm font-bold text-cyan-700 mb-2">Office Email ID (Optional)</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.officeEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-cyan-200 focus:ring-cyan-500 focus:border-cyan-500 bg-white hover:border-cyan-300'
                    }`}
                    name="officeEmail"
                    value={formData.officeEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.officeEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.officeEmail}</p>}
                </div>

                {/* Auditor Name */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Auditor Name (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.auditorName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="auditorName"
                    value={formData.auditorName}
                    onChange={handleInputChange}
                    placeholder="Auditor Name"
                  />
                  {errors.auditorName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorName}</p>}
                </div>

                {/* Auditor No */}
                <div>
                  <label className="block text-sm font-bold text-pink-700 mb-2">Auditor No (Optional)</label>
                  <MobileInput
                    value={formData.auditorNo}
                    onChange={handleInputChange}
                    name="auditorNo"
                    placeholder="Auditor phone number"
                    error={errors.auditorNo || !!errors.auditorNo}
                    showErrorMessage={false}
                    className="w-full"
                  />
                  {errors.auditorNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorNo}</p>}
                </div>

                {/* Auditor Email */}
                <div>
                  <label className="block text-sm font-bold text-red-700 mb-2">Auditor Email ID (Optional)</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.auditorEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-red-200 focus:ring-red-500 focus:border-red-500 bg-white hover:border-red-300'
                    }`}
                    name="auditorEmail"
                    value={formData.auditorEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.auditorEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.auditorEmail}</p>}
                </div>

                {/* Tax Consultant Name */}
                <div>
                  <label className="block text-sm font-bold text-yellow-700 mb-2">Tax Consultant Name (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.taxConsultantName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-yellow-200 focus:ring-yellow-500 focus:border-yellow-500 bg-white hover:border-yellow-300'
                    }`}
                    name="taxConsultantName"
                    value={formData.taxConsultantName}
                    onChange={handleInputChange}
                    placeholder="Tax Consultant Name"
                  />
                  {errors.taxConsultantName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantName}</p>}
                </div>

                {/* Tax Consultant No */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Tax Consultant No (Optional)</label>
                  <MobileInput
                    value={formData.taxConsultantNo}
                    onChange={handleInputChange}
                    name="taxConsultantNo"
                    placeholder="Tax consultant phone number"
                    error={errors.taxConsultantNo || !!errors.taxConsultantNo}
                    showErrorMessage={false}
                    className="w-full"
                  />
                  {errors.taxConsultantNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantNo}</p>}
                </div>

                {/* Tax Consultant Email */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">Tax Consultant Email ID (Optional)</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.taxConsultantEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                    }`}
                    name="taxConsultantEmail"
                    value={formData.taxConsultantEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.taxConsultantEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.taxConsultantEmail}</p>}
                </div>

                {/* IT Name (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">IT Name (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.itName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="itName"
                    value={formData.itName}
                    onChange={handleInputChange}
                    placeholder="IT Person Name"
                  />
                  {errors.itName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.itName}</p>}
                </div>

                {/* IT No (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">IT No (Optional)</label>
                  <MobileInput
                    value={formData.itNo}
                    onChange={handleInputChange}
                    name="itNo"
                    placeholder="IT phone number"
                    error={errors.itNo || !!errors.itNo}
                    showErrorMessage={false}
                    className="w-full"
                  />
                  {errors.itNo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.itNo}</p>}
                </div>

                {/* IT Email (Optional) */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">IT Email ID (Optional)</label>
                  <input
                    type="email"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.itEmail
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-teal-200 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300'
                    }`}
                    name="itEmail"
                    value={formData.itEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                  {errors.itEmail && <p className="mt-2 text-sm text-red-600 font-medium">{errors.itEmail}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Customer Address Book */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaUser className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Customer Address Book
              </h3>
            </div>
            <div className="p-6 form-section-background">
              {formData.addressBook.map((entry, index) => (
                <div key={index} className="mb-6 p-4 bg-gray-50 rounded-xl border border-blue-200">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-lg font-bold text-blue-700">Contact Entry #{index + 1}</h4>
                    <div className="flex gap-2">
                      {formData.addressBook.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeAddressBookEntry(index)}
                          className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-600 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 transition-all duration-200"
                        >
                          <FaMinus className="mr-1" />
                          Remove
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Type (Designation) */}
                    <div>
                      <label className="block text-sm font-bold text-purple-700 mb-2">
                        Type (Designation) *
                        {designationOptions.find(d => d.value === entry.type)?.isMandatory && (
                          <span className="ml-1 text-red-500">Mandatory</span>
                        )}
                      </label>
                      <select
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_type`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                        }`}
                        value={entry.type}
                        onChange={(e) => handleAddressBookChange(index, 'type', e.target.value)}
                      >
                        <option value="">Select designation</option>
                        {designationOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                      {errors[`addressBook_${index}_type`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_type`]}</p>
                      )}
                    </div>

                    {/* Contact Person */}
                    <div>
                      <label className="block text-sm font-bold text-green-700 mb-2">Contact Person *</label>
                      <input
                        type="text"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_contactPerson`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        value={entry.contactPerson}
                        onChange={(e) => handleAddressBookChange(index, 'contactPerson', e.target.value)}
                        placeholder="Enter contact person name"
                      />
                      {errors[`addressBook_${index}_contactPerson`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_contactPerson`]}</p>
                      )}
                    </div>

                    {/* Mobile Numbers */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-bold text-orange-700 mb-2">Mobile Numbers *</label>
                      <div className="space-y-2">
                        {entry.mobileNumbers.map((mobile, mobileIndex) => (
                          <div key={mobileIndex} className="flex gap-2">
                            <MobileInput
                              value={mobile}
                              onChange={(e) => handleMobileNumberChange(index, mobileIndex, e.target.value)}
                              placeholder="Enter mobile number"
                              error={!!errors[`addressBook_${index}_mobile`] || !!errors[`addressBook_${index}_mobile_${mobileIndex}`]}
                              showErrorMessage={false}
                              className="flex-1"
                            />
                            {entry.mobileNumbers.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeMobileNumber(index, mobileIndex)}
                                className="px-3 py-2 text-red-600 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 transition-all duration-200"
                              >
                                <FaMinus />
                              </button>
                            )}
                            {mobileIndex === entry.mobileNumbers.length - 1 && (
                              <button
                                type="button"
                                onClick={() => addMobileNumber(index)}
                                className="px-3 py-2 text-green-600 bg-green-100 border border-green-300 rounded-lg hover:bg-green-200 transition-all duration-200"
                              >
                                <FaPlus />
                              </button>
                            )}
                          </div>
                        ))}
                        {errors[`addressBook_${index}_mobile`] && (
                          <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_mobile`]}</p>
                        )}
                      </div>
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-bold text-teal-700 mb-2">Phone</label>
                      <MobileInput
                        value={entry.phone}
                        onChange={(e) => handleAddressBookChange(index, 'phone', e.target.value)}
                        placeholder="Enter phone number"
                        error={errors[`addressBook_${index}_phone`] || !!errors[`addressBook_${index}_phone`]}
                        showErrorMessage={false}
                        className="w-full"
                      />
                      {errors[`addressBook_${index}_phone`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_phone`]}</p>
                      )}
                    </div>

                    {/* Email */}
                    <div>
                      <label className="block text-sm font-bold text-indigo-700 mb-2">Email *</label>
                      <input
                        type="email"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors[`addressBook_${index}_email`]
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300'
                        }`}
                        value={entry.email}
                        onChange={(e) => handleAddressBookChange(index, 'email', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                      {errors[`addressBook_${index}_email`] && (
                        <p className="mt-2 text-sm text-red-600 font-medium">{errors[`addressBook_${index}_email`]}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Add New Address Book Entry */}
              <div className="text-center">
                <button
                  type="button"
                  onClick={addAddressBookEntry}
                  className="inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl border transition-all duration-200"
                  style={{
                    color: 'var(--primary-color)',
                    backgroundColor: 'rgba(var(--primary-rgb), 0.1)',
                    borderColor: 'rgba(var(--primary-rgb), 0.3)'
                  }}
                >
                  <FaPlus className="mr-2" />
                  Add Contact Entry
                </button>
              </div>

              {/* Address Book Validation Error */}
              {errors.addressBook && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-sm text-red-600 font-medium">{errors.addressBook}</p>
                </div>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaClipboardList className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Additional Information
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Area */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Area (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.area
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300'
                    }`}
                    name="area"
                    value={formData.area}
                    onChange={handleInputChange}
                    placeholder="Enter area"
                  />
                  {errors.area && <p className="mt-2 text-sm text-red-600 font-medium">{errors.area}</p>}
                </div>

                {/* PIN Code */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">PIN Code (Optional)</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200 sm:text-sm"
                    name="pinCode"
                    value={formData.pinCode}
                    onChange={handleInputChange}
                    placeholder="400001"
                    maxLength="6"
                  />
                </div>

                {/* State Country */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">State Country (Optional)</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300 transition-all duration-200 sm:text-sm"
                    name="stateCountry"
                    value={formData.stateCountry}
                    onChange={handleInputChange}
                    placeholder="Maharashtra, India"
                  />
                </div>

                {/* No. of Tally Users */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">No. of Tally Users (Optional)</label>
                  <input
                    type="number"
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="noOfTallyUsers"
                    value={formData.noOfTallyUsers}
                    onChange={handleInputChange}
                    placeholder="5"
                    min="1"
                  />
                </div>

                {/* Executive Name */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Executive Name (Optional)</label>
                  <input
                    type="text"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.executiveName
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                    }`}
                    name="executiveName"
                    value={formData.executiveName}
                    onChange={handleInputChange}
                    placeholder="Executive Name"
                  />
                  {errors.executiveName && <p className="mt-2 text-sm text-red-600 font-medium">{errors.executiveName}</p>}
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-bold text-gray-700 mb-2">Status (Optional)</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200 sm:text-sm"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Status</option>
                    <option value="ACTIVE">✅ Active</option>
                    <option value="INACTIVE">❌ Inactive</option>
                    <option value="PENDING">⏳ Pending</option>
                    <option value="FOLLOW_UP">📞 Follow Up</option>
                  </select>
                </div>

                {/* Task 3: Log Details removed - logs should be automatic, not manual entry */}
              </div>
            </div>
          </div>

          {/* TSS (Tally Software Service) */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaShieldAlt className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Tally Software Service (TSS)
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* TSS Status */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">TSS Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300 transition-all duration-200 sm:text-sm"
                    name="tssStatus"
                    value={formData.tssStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional fields based on TSS Status */}
                {formData.tssStatus === 'YES' ? (
                  <>
                    {/* TSS Expiry Date */}
                    <div>
                      <label className="block text-sm font-bold text-blue-700 mb-2">TSS Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.tssExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                        }`}
                        name="tssExpiryDate"
                        value={formData.tssExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.tssExpiryDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.tssExpiryDate}</p>}
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </div>

          {/* AMC (Annual Maintenance Contract) */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaTools className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                AMC (Annual Maintenance Contract)
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* AMC Status */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">AMC Status</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                    name="amcStatus"
                    value={formData.amcStatus}
                    onChange={handleInputChange}
                  >
                    <option value="YES">✅ YES</option>
                    <option value="NO">❌ NO</option>
                  </select>
                </div>

                {/* Conditional AMC fields */}
                {formData.amcStatus === 'YES' && (
                  <>
                    {/* From Date */}
                    <div>
                      <label className="block text-sm font-bold text-green-700 mb-2">From Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcFromDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        name="amcFromDate"
                        value={formData.amcFromDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcFromDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcFromDate}</p>}
                    </div>

                    {/* To Date */}
                    <div>
                      <label className="block text-sm font-bold text-blue-700 mb-2">To Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.amcToDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                        }`}
                        name="amcToDate"
                        value={formData.amcToDate}
                        onChange={handleInputChange}
                      />
                      {errors.amcToDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amcToDate}</p>}
                    </div>

                    {/* Renewal Date */}
                    <div>
                      <label className="block text-sm font-bold text-purple-700 mb-2">Renewal Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.renewalDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                        }`}
                        name="renewalDate"
                        value={formData.renewalDate}
                        onChange={handleInputChange}
                      />
                      {errors.renewalDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.renewalDate}</p>}
                    </div>

                    {/* Number of Visits */}
                    <div>
                      <label className="block text-sm font-bold text-teal-700 mb-2">No. of Visits *</label>
                      <input
                        type="number"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.noOfVisits
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-teal-200 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300'
                        }`}
                        name="noOfVisits"
                        value={formData.noOfVisits}
                        onChange={handleInputChange}
                        placeholder="12"
                        min="1"
                      />
                      {errors.noOfVisits && <p className="mt-2 text-sm text-red-600 font-medium">{errors.noOfVisits}</p>}
                    </div>

                    {/* Current AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-indigo-700 mb-2">Current AMC Amount *</label>
                      <input
                        type="number"
                        step="0.01"
                        className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                          errors.currentAmcAmount
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300'
                        }`}
                        name="currentAmcAmount"
                        value={formData.currentAmcAmount}
                        onChange={handleInputChange}
                        placeholder="50000"
                      />
                      {errors.currentAmcAmount && <p className="mt-2 text-sm text-red-600 font-medium">{errors.currentAmcAmount}</p>}
                    </div>

                    {/* Last Year AMC Amount */}
                    <div>
                      <label className="block text-sm font-bold text-pink-700 mb-2">Last Year AMC Amount</label>
                      <input
                        type="number"
                        step="0.01"
                        className="block w-full px-4 py-3 border-2 border-pink-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 bg-white hover:border-pink-300 transition-all duration-200 sm:text-sm"
                        name="lastYearAmcAmount"
                        value={formData.lastYearAmcAmount}
                        onChange={handleInputChange}
                        placeholder="45000"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>



          {/* Consolidated Services & Features Section */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaClipboardList className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Services & Features
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6">
                {/* TDL & Addons */}
                <div className="p-4 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl border border-yellow-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="tdlAddons"
                      name="tdlAddons"
                      checked={formData.tdlAddons}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-yellow-600 bg-gray-100 border-gray-300 rounded focus:ring-yellow-500 focus:ring-2"
                    />
                    <label htmlFor="tdlAddons" className="text-sm font-bold text-yellow-700 cursor-pointer">
                      🔧 TDL & Addons
                    </label>
                  </div>
                  {formData.tdlAddons && (
                    <div className="space-y-3">
                      {/* Expiry Date */}
                      <div>
                        <label className="block text-xs font-bold text-yellow-600 mb-1">Expiry Date *</label>
                        <input
                          type="date"
                          className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                            errors.tdlAddonsExpiryDate
                              ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                              : 'border-yellow-200 focus:ring-yellow-500 focus:border-yellow-500 bg-white hover:border-yellow-300'
                          }`}
                          name="tdlAddonsExpiryDate"
                          value={formData.tdlAddonsExpiryDate}
                          onChange={handleInputChange}
                        />
                        {errors.tdlAddonsExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.tdlAddonsExpiryDate}</p>}
                      </div>

                      {/* File Upload */}
                      <div>
                        <label className="block text-xs font-bold text-yellow-600 mb-1">Upload File (Optional)</label>
                        <input
                          type="file"
                          className="block w-full px-3 py-2 border-2 border-yellow-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white hover:border-yellow-300 transition-all duration-200 text-xs file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-xs file:font-medium file:bg-yellow-50 file:text-yellow-700 hover:file:bg-yellow-100"
                          name="tdlAddonsFile"
                          onChange={handleFileUpload}
                          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        />
                        {formData.tdlAddonsFile && (
                          <p className="mt-1 text-xs text-yellow-600">
                            📎 {formData.tdlAddonsFile.name} ({(formData.tdlAddonsFile.size / 1024 / 1024).toFixed(2)} MB)
                          </p>
                        )}
                        <p className="mt-1 text-xs text-yellow-500">Supported: PDF, DOC, DOCX, JPG, PNG (Max 5MB)</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* WhatsApp/Telegram Group */}
                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="whatsappTelegramGroup"
                      name="whatsappTelegramGroup"
                      checked={formData.whatsappTelegramGroup}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                    />
                    <label htmlFor="whatsappTelegramGroup" className="text-sm font-bold text-green-700 cursor-pointer">
                      📱 WhatsApp/Telegram Group
                    </label>
                  </div>
                  {formData.whatsappTelegramGroup && (
                    <div>
                      <label className="block text-xs font-bold text-green-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.whatsappTelegramGroupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                        }`}
                        name="whatsappTelegramGroupExpiryDate"
                        value={formData.whatsappTelegramGroupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.whatsappTelegramGroupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.whatsappTelegramGroupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Auto Backup */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="autoBackup"
                      name="autoBackup"
                      checked={formData.autoBackup}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <label htmlFor="autoBackup" className="text-sm font-bold text-blue-700 cursor-pointer">
                      💾 Auto Backup
                    </label>
                  </div>
                  {formData.autoBackup && (
                    <div>
                      <label className="block text-xs font-bold text-blue-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.autoBackupExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                        }`}
                        name="autoBackupExpiryDate"
                        value={formData.autoBackupExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.autoBackupExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.autoBackupExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Cloud User */}
                <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="cloudUser"
                      name="cloudUser"
                      checked={formData.cloudUser}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <label htmlFor="cloudUser" className="text-sm font-bold text-purple-700 cursor-pointer">
                      ☁️ Cloud User
                    </label>
                  </div>
                  {formData.cloudUser && (
                    <div>
                      <label className="block text-xs font-bold text-purple-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.cloudUserExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                        }`}
                        name="cloudUserExpiryDate"
                        value={formData.cloudUserExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.cloudUserExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.cloudUserExpiryDate}</p>}
                    </div>
                  )}
                </div>

                {/* Mobile App */}
                <div className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <input
                      type="checkbox"
                      id="mobileApp"
                      name="mobileApp"
                      checked={formData.mobileApp}
                      onChange={handleInputChange}
                      className="w-5 h-5 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <label htmlFor="mobileApp" className="text-sm font-bold text-orange-700 cursor-pointer">
                      📱 Mobile App
                    </label>
                  </div>
                  {formData.mobileApp && (
                    <div>
                      <label className="block text-xs font-bold text-orange-600 mb-1">Expiry Date *</label>
                      <input
                        type="date"
                        className={`block w-full px-3 py-2 border-2 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 text-xs ${
                          errors.mobileAppExpiryDate
                            ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                            : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                        }`}
                        name="mobileAppExpiryDate"
                        value={formData.mobileAppExpiryDate}
                        onChange={handleInputChange}
                      />
                      {errors.mobileAppExpiryDate && <p className="mt-1 text-xs text-red-600 font-medium">{errors.mobileAppExpiryDate}</p>}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4">
            <button
              type="button"
              onClick={() => navigate('/customers')}
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200 min-h-[48px] touch-manipulation"
            >
              <FaTimes className="mr-2" />
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-xl bg-primary-dynamic hover:opacity-90 focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg min-h-[48px] touch-manipulation"
              style={{
                backgroundColor: 'var(--primary-color)',
                color: 'var(--primary-text)',
                '--tw-ring-color': 'var(--primary-color)'
              }}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 mr-2" style={{ borderColor: 'var(--primary-text)' }}></div>
                  {isEdit ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <FaSave className="mr-2" />
                  {isEdit ? 'Update Customer' : 'Create Customer'}
                </>
              )}
            </button>
          </div>
        </form>

        {/* Map Location Picker Modal (Task 5) */}
        <MapLocationPicker
          isOpen={showMapPicker}
          onClose={() => setShowMapPicker(false)}
          onLocationSelect={handleLocationSelect}
          initialLocation={formData.mapLocation}
          initialLat={formData.latitude}
          initialLng={formData.longitude}
        />
      </div>
    </div>
  );
};

export default CustomerForm;
