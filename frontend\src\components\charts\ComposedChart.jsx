/**
 * Composed Chart Component
 * Combines multiple chart types (bars, lines, areas) in a single chart
 */

import React from 'react';
import {
  ComposedChart as RechartsComposedChart,
  Bar,
  Line,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { CHART_COLORS, CHART_THEMES } from './chartThemes';
import { formatNumber, formatDate } from './chartUtils';

const ComposedChart = ({
  data = [],
  elements = [], // Array of chart elements: [{ type: 'bar'|'line'|'area', dataKey, name, color, yAxisId }]
  xAxisKey = 'date',
  height = 300,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  formatters = {},
  theme = 'default',
  className = '',
  ...props
}) => {
  // Theme configuration
  const currentTheme = CHART_THEMES[theme] || CHART_THEMES.default;

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 mb-2">
          {formatDate(label, 'short')}
        </p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between space-x-4 mb-1">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-sm" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600">{entry.name}:</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatters[entry.dataKey] 
                ? formatters[entry.dataKey](entry.value)
                : formatNumber(entry.value)
              }
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 text-sm">No data available for chart</p>
      </div>
    );
  }

  // Check if we need dual Y-axes
  const hasSecondaryAxis = elements.some(el => el.yAxisId === 'right');

  return (
    <div className={className} {...props}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsComposedChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <defs>
            {elements.filter(el => el.type === 'area').map((area, index) => {
              const color = area.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length];
              return (
                <linearGradient 
                  key={`gradient-${area.dataKey}`}
                  id={`gradient-${area.dataKey}`} 
                  x1="0" 
                  y1="0" 
                  x2="0" 
                  y2="1"
                >
                  <stop 
                    offset="5%" 
                    stopColor={color} 
                    stopOpacity={0.3} 
                  />
                  <stop 
                    offset="95%" 
                    stopColor={color} 
                    stopOpacity={0.1} 
                  />
                </linearGradient>
              );
            })}
          </defs>
          
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={currentTheme.gridColor}
              opacity={0.5}
            />
          )}
          
          <XAxis 
            dataKey={xAxisKey}
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatDate(value, 'short')}
          />
          
          <YAxis 
            yAxisId="left"
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={(value) => formatNumber(value, 'compact')}
          />
          
          {hasSecondaryAxis && (
            <YAxis 
              yAxisId="right"
              orientation="right"
              tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
              tickLine={{ stroke: currentTheme.gridColor }}
              axisLine={{ stroke: currentTheme.gridColor }}
              tickFormatter={(value) => formatNumber(value, 'compact')}
            />
          )}
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {showLegend && (
            <Legend 
              wrapperStyle={{ 
                fontSize: currentTheme.fontSize,
                color: currentTheme.textColor 
              }}
            />
          )}
          
          {/* Render chart elements */}
          {elements.map((element, index) => {
            const color = element.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length];
            const yAxisId = element.yAxisId || 'left';
            
            switch (element.type) {
              case 'bar':
                return (
                  <Bar
                    key={element.dataKey}
                    yAxisId={yAxisId}
                    dataKey={element.dataKey}
                    name={element.name}
                    fill={color}
                    radius={[2, 2, 0, 0]}
                  />
                );
                
              case 'line':
                return (
                  <Line
                    key={element.dataKey}
                    yAxisId={yAxisId}
                    type="monotone"
                    dataKey={element.dataKey}
                    name={element.name}
                    stroke={color}
                    strokeWidth={element.strokeWidth || 2}
                    dot={{ fill: color, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: color, strokeWidth: 2, fill: '#fff' }}
                  />
                );
                
              case 'area':
                return (
                  <Area
                    key={element.dataKey}
                    yAxisId={yAxisId}
                    type="monotone"
                    dataKey={element.dataKey}
                    name={element.name}
                    stroke={color}
                    strokeWidth={element.strokeWidth || 2}
                    fill={`url(#gradient-${element.dataKey})`}
                  />
                );
                
              default:
                return null;
            }
          })}
        </RechartsComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ComposedChart;
