export const up = async (queryInterface, Sequelize) => {
  // Create salary_structures table
  await queryInterface.createTable('salary_structures', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    basic_salary: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    hra: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    transport_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    medical_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    special_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    other_allowances: {
      type: Sequelize.JSONB,
      allowNull: true,
    },
    pf_employee_percentage: {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 12.0,
    },
    pf_employer_percentage: {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 12.0,
    },
    esi_employee_percentage: {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0.75,
    },
    esi_employer_percentage: {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 3.25,
    },
    professional_tax: {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    tds_percentage: {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    other_deductions: {
      type: Sequelize.JSONB,
      allowNull: true,
    },
    effective_from: {
      type: Sequelize.DATEONLY,
      allowNull: false,
    },
    effective_to: {
      type: Sequelize.DATEONLY,
      allowNull: true,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Create payroll_records table
  await queryInterface.createTable('payroll_records', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    salary_structure_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'salary_structures',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    payroll_number: {
      type: Sequelize.STRING(50),
      allowNull: false,
      unique: true,
    },
    month: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    year: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    total_working_days: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    present_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    absent_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    leave_days: {
      type: Sequelize.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
    },
    overtime_hours: {
      type: Sequelize.DECIMAL(6, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    basic_salary: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    hra: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    transport_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    medical_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    special_allowance: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    overtime_amount: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    other_earnings: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    gross_salary: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    pf_employee: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    pf_employer: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    esi_employee: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    esi_employer: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    professional_tax: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    income_tax: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    late_deduction: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    absent_deduction: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    other_deductions: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    total_deductions: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    net_salary: {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    status: {
      type: Sequelize.ENUM('draft', 'calculated', 'approved', 'paid', 'cancelled'),
      allowNull: false,
      defaultValue: 'draft',
    },
    processed_by: {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    processed_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    approved_by: {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    approved_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    paid_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    payment_method: {
      type: Sequelize.STRING(50),
      allowNull: true,
    },
    payment_reference: {
      type: Sequelize.STRING(100),
      allowNull: true,
    },
    notes: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('salary_structures', ['tenant_id', 'employee_id']);
  await queryInterface.addIndex('salary_structures', ['effective_from', 'effective_to']);
  await queryInterface.addIndex('salary_structures', ['is_active']);
  
  await queryInterface.addIndex('payroll_records', ['tenant_id', 'employee_id', 'month', 'year'], {
    unique: true,
    name: 'payroll_records_unique'
  });
  
  await queryInterface.addIndex('payroll_records', ['payroll_number'], { unique: true });
  await queryInterface.addIndex('payroll_records', ['status']);
  await queryInterface.addIndex('payroll_records', ['month', 'year']);
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('payroll_records');
  await queryInterface.dropTable('salary_structures');
};
