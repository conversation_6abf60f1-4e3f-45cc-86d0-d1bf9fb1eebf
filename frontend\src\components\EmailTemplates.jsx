import React, { useState, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { FaEdit, FaSave, FaTimes, FaEye, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';

const EmailTemplates = () => {
  const [templates, setTemplates] = useState({});
  const [loading, setLoading] = useState(true);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [tempContent, setTempContent] = useState('');
  const [tempSubject, setTempSubject] = useState('');

  // Default templates
  const defaultTemplates = {
    welcome_email: {
      name: 'Welcome Email',
      subject: 'Welcome to PremInfoTech - {{customer_name}}',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #2f69b3 0%, #1e4a73 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to PremInfoTech!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your Trusted Tally Support Partner</p>
          </div>
          <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
            <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
              Welcome to PremInfoTech! We're excited to have you as our valued customer.
            </p>
            <p style="color: #666; line-height: 1.6; font-size: 16px;">
              Our team is committed to providing you with the best Tally support and services. 
              If you have any questions or need assistance, please don't hesitate to contact us.
            </p>
            <div style="text-align: center; margin: 30px 0;">
              <p style="color: #2f69b3; font-weight: bold; font-size: 18px;">Thank you for choosing PremInfoTech!</p>
            </div>
          </div>
        </div>
      `,
      enabled: true
    },
    service_created: {
      name: 'Service Created',
      subject: 'Service Request Created - {{service_number}}',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #2f69b3; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">Service Request Created</h1>
          </div>
          <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
            <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
              Your service request has been successfully created and assigned to our team.
            </p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">Service Details:</h3>
              <p><strong>Service Number:</strong> {{service_number}}</p>
              <p><strong>Type of Call:</strong> {{type_of_call}}</p>
              <p><strong>Created Date:</strong> {{created_date}}</p>
              <p><strong>Assigned Executive:</strong> {{executive_name}}</p>
            </div>
            <p style="color: #666; line-height: 1.6;">
              We will keep you updated on the progress of your service request.
            </p>
          </div>
        </div>
      `,
      enabled: true
    },
    service_completed: {
      name: 'Service Completed',
      subject: 'Service Completed - {{service_number}}',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #28a745; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">✅ Service Completed</h1>
          </div>
          <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
            <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
              Great news! Your service request has been successfully completed.
            </p>
            <div style="background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;">
              <h3 style="color: #155724; margin-top: 0;">Service Summary:</h3>
              <p><strong>Service Number:</strong> {{service_number}}</p>
              <p><strong>Completed Date:</strong> {{completed_date}}</p>
              <p><strong>Executive:</strong> {{executive_name}}</p>
            </div>
            <p style="color: #666; line-height: 1.6;">
              Thank you for choosing PremInfoTech. We hope our service met your expectations.
            </p>
          </div>
        </div>
      `,
      enabled: true
    },
    expiry_notification: {
      name: 'Expiry Notification',
      subject: 'Service Expiry Reminder - {{service_type}}',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #ffc107; color: #212529; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">⚠️ Service Expiry Reminder</h1>
          </div>
          <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
            <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
            <p style="color: #666; line-height: 1.6;">
              This is a reminder that your {{service_type}} service is expiring soon.
            </p>
            <div style="background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #ffeaa7;">
              <h3 style="color: #856404; margin-top: 0;">Expiry Details:</h3>
              <p><strong>Service Type:</strong> {{service_type}}</p>
              <p><strong>Expiry Date:</strong> {{expiry_date}}</p>
              <p><strong>Days Remaining:</strong> {{days_remaining}}</p>
            </div>
            <p style="color: #666; line-height: 1.6;">
              Please contact us to renew your service and avoid any interruption.
            </p>
          </div>
        </div>
      `,
      enabled: true
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, []);

  // Function to safely render template content for preview
  const getSafeTemplateContent = (content) => {
    if (!content) return '';
    // Replace template variables with safe placeholder text for preview
    return content
      .replace(/\{\{customer_name\}\}/g, 'John Doe')
      .replace(/\{\{service_number\}\}/g, 'SE-001')
      .replace(/\{\{type_of_call\}\}/g, 'Technical Support')
      .replace(/\{\{created_date\}\}/g, new Date().toLocaleDateString())
      .replace(/\{\{completed_date\}\}/g, new Date().toLocaleDateString())
      .replace(/\{\{executive_name\}\}/g, 'Support Executive')
      .replace(/\{\{service_type\}\}/g, 'Annual Maintenance')
      .replace(/\{\{expiry_date\}\}/g, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString())
      .replace(/\{\{days_remaining\}\}/g, '30');
  };

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/settings/email-templates');
      if (response.data?.success) {
        setTemplates(response.data.data || defaultTemplates);
      } else {
        setTemplates(defaultTemplates);
      }
    } catch (error) {
      console.error('Error fetching email templates:', error);
      setTemplates(defaultTemplates);
    } finally {
      setLoading(false);
    }
  };

  const saveTemplate = async (templateKey) => {
    try {
      const templateData = {
        ...(templates[templateKey] || {}),
        subject: tempSubject,
        content: tempContent
      };

      const response = await apiService.put(`/settings/email-templates/${templateKey}`, templateData);
      
      if (response.data?.success) {
        setTemplates(prev => ({
          ...prev,
          [templateKey]: templateData
        }));
        setEditingTemplate(null);
        setTempContent('');
        setTempSubject('');
        toast.success('Template updated successfully');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    }
  };

  const toggleTemplate = async (templateKey) => {
    try {
      const currentTemplate = templates[templateKey] || {};
      const updatedTemplate = {
        ...currentTemplate,
        enabled: !currentTemplate.enabled
      };

      const response = await apiService.put(`/settings/email-templates/${templateKey}`, updatedTemplate);
      
      if (response.data?.success) {
        setTemplates(prev => ({
          ...prev,
          [templateKey]: updatedTemplate
        }));
        toast.success(`Template ${updatedTemplate.enabled ? 'enabled' : 'disabled'} successfully`);
      }
    } catch (error) {
      console.error('Error toggling template:', error);
      toast.error('Failed to update template');
    }
  };

  const startEditing = (templateKey) => {
    setEditingTemplate(templateKey);
    setTempSubject(templates[templateKey]?.subject || '');
    setTempContent(templates[templateKey]?.content || '');
  };

  const cancelEditing = () => {
    setEditingTemplate(null);
    setTempContent('');
    setTempSubject('');
  };

  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ],
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-semibold text-gray-900">Email Templates</h3>
        <p className="text-sm text-gray-600 mt-1">
          Customize email templates for different notifications. Use variables like &#123;&#123;customer_name&#125;&#125;, &#123;&#123;service_number&#125;&#125;, etc.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(templates || {}).map(([key, template]) => {
          if (!template) return null;

          return (
            <div key={key} className="bg-white border border-gray-200 rounded-lg shadow-sm">
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h4 className="text-md font-medium text-gray-900">{template?.name || 'Untitled Template'}</h4>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => toggleTemplate(key)}
                      className={`p-1 rounded ${template?.enabled ? 'text-green-600' : 'text-gray-400'}`}
                      title={template?.enabled ? 'Disable template' : 'Enable template'}
                    >
                      {template?.enabled ? <FaToggleOn size={20} /> : <FaToggleOff size={20} />}
                    </button>
                    <button
                      onClick={() => setPreviewTemplate(key)}
                      className="p-1 text-blue-600 hover:text-blue-800"
                      title="Preview template"
                    >
                      <FaEye size={16} />
                    </button>
                    <button
                      onClick={() => startEditing(key)}
                      className="p-1 text-gray-600 hover:text-gray-800"
                      title="Edit template"
                    >
                      <FaEdit size={16} />
                    </button>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Status: {template?.enabled ? 'Enabled' : 'Disabled'}
                </p>
              </div>

              {editingTemplate === key ? (
                <div className="p-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <input
                      type="text"
                      value={tempSubject}
                      onChange={(e) => setTempSubject(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Email subject"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Content</label>
                    <ReactQuill
                      value={tempContent}
                      onChange={setTempContent}
                      modules={quillModules}
                      theme="snow"
                      style={{ height: '200px' }}
                    />
                  </div>
                  <div className="flex justify-end space-x-2 pt-12">
                    <button
                      onClick={cancelEditing}
                      className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                    >
                      <FaTimes className="inline mr-1" /> Cancel
                    </button>
                    <button
                      onClick={() => saveTemplate(key)}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      <FaSave className="inline mr-1" /> Save
                    </button>
                  </div>
                </div>
              ) : (
                <div className="p-4">
                  <div className="text-sm text-gray-600 mb-2">
                    <strong>Subject:</strong> {template?.subject || 'No subject'}
                  </div>
                  <div className="text-sm text-gray-500 max-h-20 overflow-hidden">
                    <div dangerouslySetInnerHTML={{ __html: getSafeTemplateContent(template?.content).substring(0, 150) + '...' }} />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold">Preview: {templates[previewTemplate].name}</h3>
              <button
                onClick={() => setPreviewTemplate(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes size={20} />
              </button>
            </div>
            <div className="p-6">
              <div className="mb-4">
                <strong>Subject:</strong> {getSafeTemplateContent(templates[previewTemplate]?.subject)}
              </div>
              <div className="border border-gray-200 rounded p-4">
                <div dangerouslySetInnerHTML={{ __html: getSafeTemplateContent(templates[previewTemplate]?.content) }} />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailTemplates;
