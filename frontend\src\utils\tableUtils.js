/**
 * Responsive Table Utilities
 * Provides helper functions and configurations for laptop-optimized tables
 */

// Standard breakpoints for responsive behavior
export const BREAKPOINTS = {
  MOBILE: 640,      // sm
  TABLET: 768,      // md  
  LAPTOP: 1024,     // lg
  SMALL_LAPTOP: 1366, // Custom breakpoint for small laptops
  DESKTOP: 1440,    // xl
  LARGE_DESKTOP: 1600 // 2xl
};

// Column priority levels for responsive hiding
export const COLUMN_PRIORITIES = {
  HIGH: 'high',     // Always visible
  MEDIUM: 'medium', // Hidden on small laptops
  LOW: 'low'        // Hidden on laptops
};

/**
 * Get responsive column configuration based on screen size
 * @param {Array} columns - Array of column definitions
 * @param {number} screenWidth - Current screen width
 * @returns {Array} Filtered columns for current screen size
 */
export const getResponsiveColumns = (columns, screenWidth = window.innerWidth) => {
  return columns.filter(column => {
    if (column.hideOnMobile && screenWidth < BREAKPOINTS.MOBILE) return false;
    if (column.hideOnTablet && screenWidth < BREAKPOINTS.TABLET) return false;
    if (column.hideOnLaptop && screenWidth < BREAKPOINTS.LAPTOP) return false;
    if (column.hideOnSmallLaptop && screenWidth < BREAKPOINTS.SMALL_LAPTOP) return false;
    if (column.showDesktopOnly && screenWidth < BREAKPOINTS.DESKTOP) return false;
    
    // Priority-based hiding
    if (column.priority === COLUMN_PRIORITIES.LOW && screenWidth < BREAKPOINTS.LAPTOP) return false;
    if (column.priority === COLUMN_PRIORITIES.MEDIUM && screenWidth < BREAKPOINTS.SMALL_LAPTOP) return false;
    
    return true;
  });
};

/**
 * Calculate optimal column widths for laptop screens
 * @param {Array} columns - Array of column definitions
 * @param {number} screenWidth - Current screen width
 * @returns {Object} Column width configuration
 */
export const calculateColumnWidths = (columns, screenWidth = window.innerWidth) => {
  const visibleColumns = getResponsiveColumns(columns, screenWidth);
  const totalFixedWidth = visibleColumns.reduce((total, col) => {
    if (col.width && col.width.includes('%')) {
      return total + parseFloat(col.width);
    }
    return total;
  }, 0);
  
  const flexColumns = visibleColumns.filter(col => !col.width || !col.width.includes('%'));
  const remainingWidth = Math.max(0, 100 - totalFixedWidth);
  const flexWidth = flexColumns.length > 0 ? remainingWidth / flexColumns.length : 0;
  
  return visibleColumns.reduce((widths, col, index) => {
    if (col.width && col.width.includes('%')) {
      widths[col.key] = col.width;
    } else {
      widths[col.key] = `${flexWidth}%`;
    }
    return widths;
  }, {});
};

/**
 * Generate responsive CSS classes for table cells
 * @param {Object} column - Column definition
 * @param {boolean} isHeader - Whether this is a header cell
 * @returns {string} CSS class string
 */
export const getResponsiveCellClasses = (column, isHeader = false) => {
  let classes = [];
  
  // Base classes
  if (isHeader) {
    classes.push('px-2 sm:px-3 lg:px-4 py-2 sm:py-3 text-left text-xs font-bold uppercase tracking-wider');
  } else {
    classes.push('px-2 sm:px-3 lg:px-4 py-2 sm:py-3');
  }
  
  // Responsive hiding classes
  if (column.hideOnMobile) classes.push('hidden sm:table-cell');
  if (column.hideOnTablet) classes.push('hidden md:table-cell');
  if (column.hideOnLaptop) classes.push('hidden lg:table-cell');
  if (column.hideOnSmallLaptop) classes.push('col-hide-small-laptop');
  if (column.showDesktopOnly) classes.push('col-show-desktop-only');
  
  // Priority-based classes
  if (column.priority === COLUMN_PRIORITIES.LOW) classes.push('col-hide-laptop');
  if (column.priority === COLUMN_PRIORITIES.MEDIUM) classes.push('col-hide-small-laptop');
  
  // Text truncation
  if (column.truncate !== false) classes.push('text-truncate-responsive');
  
  // Custom classes
  if (column.className) classes.push(column.className);
  if (column.cellClassName && !isHeader) classes.push(column.cellClassName);
  
  return classes.join(' ');
};

/**
 * Truncate text with ellipsis and provide tooltip
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length before truncation
 * @returns {Object} Object with truncated text and full text for tooltip
 */
export const truncateText = (text, maxLength = 30) => {
  if (!text || typeof text !== 'string') return { display: '', tooltip: '' };
  
  if (text.length <= maxLength) {
    return { display: text, tooltip: text };
  }
  
  return {
    display: text.substring(0, maxLength) + '...',
    tooltip: text
  };
};

/**
 * Default column configurations for common table types
 */
export const DEFAULT_COLUMN_CONFIGS = {
  // Standard action column
  actions: {
    header: 'Actions',
    key: 'actions',
    width: '8%',
    priority: COLUMN_PRIORITIES.HIGH,
    truncate: false,
    className: 'text-center',
    cellClassName: 'text-center'
  },
  
  // Standard status column
  status: {
    header: 'Status',
    key: 'status',
    width: '12%',
    priority: COLUMN_PRIORITIES.HIGH,
    className: 'text-center',
    cellClassName: 'text-center'
  },
  
  // Standard date column
  date: {
    header: 'Date',
    key: 'date',
    width: '10%',
    priority: COLUMN_PRIORITIES.MEDIUM,
    hideOnSmallLaptop: true,
    className: 'text-center',
    cellClassName: 'text-center'
  },
  
  // Standard amount column
  amount: {
    header: 'Amount',
    key: 'amount',
    width: '10%',
    priority: COLUMN_PRIORITIES.LOW,
    hideOnLaptop: true,
    className: 'text-right',
    cellClassName: 'text-right'
  }
};

/**
 * Check if current screen size is laptop
 * @param {number} screenWidth - Current screen width
 * @returns {boolean} True if laptop size
 */
export const isLaptopScreen = (screenWidth = window.innerWidth) => {
  return screenWidth >= BREAKPOINTS.LAPTOP && screenWidth < BREAKPOINTS.DESKTOP;
};

/**
 * Check if current screen size is small laptop
 * @param {number} screenWidth - Current screen width
 * @returns {boolean} True if small laptop size
 */
export const isSmallLaptopScreen = (screenWidth = window.innerWidth) => {
  return screenWidth >= BREAKPOINTS.LAPTOP && screenWidth < BREAKPOINTS.SMALL_LAPTOP;
};

/**
 * Get optimal table configuration for current screen
 * @param {Array} columns - Column definitions
 * @param {Object} options - Configuration options
 * @returns {Object} Optimized table configuration
 */
export const getOptimalTableConfig = (columns, options = {}) => {
  const screenWidth = window.innerWidth;
  const {
    enableLaptopOptimization = true,
    enableCompactMode = isLaptopScreen(screenWidth),
    enableTooltips = true
  } = options;
  
  return {
    columns: getResponsiveColumns(columns, screenWidth),
    columnWidths: calculateColumnWidths(columns, screenWidth),
    laptopOptimized: enableLaptopOptimization && isLaptopScreen(screenWidth),
    compactMode: enableCompactMode,
    showTooltips: enableTooltips,
    screenWidth
  };
};
