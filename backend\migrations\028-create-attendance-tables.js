export const up = async (queryInterface, Sequelize) => {
  // Create attendance_policies table
  await queryInterface.createTable('attendance_policies', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: Sequelize.STRING(100),
      allowNull: false,
    },
    description: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    shift_start_time: {
      type: Sequelize.TIME,
      allowNull: false,
      defaultValue: '09:00:00',
    },
    shift_end_time: {
      type: Sequelize.TIME,
      allowNull: false,
      defaultValue: '18:00:00',
    },
    grace_period_minutes: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 15,
    },
    minimum_hours_full_day: {
      type: Sequelize.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 8.0,
    },
    overtime_threshold_hours: {
      type: Sequelize.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 8.0,
    },
    overtime_rate_multiplier: {
      type: Sequelize.DECIMAL(3, 2),
      allowNull: false,
      defaultValue: 1.5,
    },
    late_deduction_per_minute: {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    absent_day_deduction: {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    is_default: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Create attendance_settings table
  await queryInterface.createTable('attendance_settings', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    enable_gps_tracking: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    enable_face_recognition: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    enable_biometric: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    office_latitude: {
      type: Sequelize.DECIMAL(10, 8),
      allowNull: true,
    },
    office_longitude: {
      type: Sequelize.DECIMAL(11, 8),
      allowNull: true,
    },
    office_radius_meters: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 100,
    },
    send_daily_reminders: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    reminder_time: {
      type: Sequelize.TIME,
      allowNull: false,
      defaultValue: '08:45:00',
    },
    send_late_arrival_alerts: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    send_absence_alerts: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    require_manager_approval: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    auto_approve_threshold_minutes: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 30,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Create attendance_records table
  await queryInterface.createTable('attendance_records', {
    id: {
      type: Sequelize.UUID,
      defaultValue: Sequelize.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    date: {
      type: Sequelize.DATEONLY,
      allowNull: false,
    },
    check_in_time: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    check_out_time: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    check_in_location: {
      type: Sequelize.JSONB,
      allowNull: true,
    },
    check_out_location: {
      type: Sequelize.JSONB,
      allowNull: true,
    },
    total_hours: {
      type: Sequelize.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    overtime_hours: {
      type: Sequelize.DECIMAL(4, 2),
      allowNull: false,
      defaultValue: 0.0,
    },
    late_minutes: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    early_departure_minutes: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    status: {
      type: Sequelize.ENUM('present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'),
      allowNull: false,
      defaultValue: 'present',
    },
    notes: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    is_manual_entry: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    manual_entry_reason: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    approved_by: {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    approved_at: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
    updated_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('attendance_records', ['tenant_id', 'employee_id', 'date'], {
    unique: true,
    name: 'attendance_records_tenant_employee_date_unique'
  });
  
  await queryInterface.addIndex('attendance_records', ['tenant_id', 'date']);
  await queryInterface.addIndex('attendance_records', ['employee_id', 'date']);
  await queryInterface.addIndex('attendance_records', ['status']);
  await queryInterface.addIndex('attendance_policies', ['tenant_id', 'is_default']);
  await queryInterface.addIndex('attendance_settings', ['tenant_id'], { unique: true });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('attendance_records');
  await queryInterface.dropTable('attendance_settings');
  await queryInterface.dropTable('attendance_policies');
};
