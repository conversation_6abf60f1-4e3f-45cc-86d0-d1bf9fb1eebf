import React from 'react';
import { cn } from '../../../utils/helpers';

const Spinner = React.forwardRef(({
  className,
  size = 'md',
  variant = 'primary',
  center = false,
  centerType = 'default',
  ...props
}, ref) => {
  const baseClasses = 'inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent';

  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const variants = {
    primary: 'spinner-primary-text',
    secondary: 'text-gray-500',
    success: 'text-green-600',
    danger: 'text-red-600',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
    light: 'text-gray-300',
    dark: 'text-gray-800',
    white: 'text-white',
  };

  const centerClasses = {
    default: 'loading-center',
    full: 'loading-center-full',
    modal: 'loading-center-modal',
    inline: 'loading-center-inline'
  };

  const spinnerElement = (
    <div
      ref={ref}
      className={cn(
        baseClasses,
        sizes[size],
        variants[variant],
        className
      )}
      role="status"
      {...props}
    >
      <span className="sr-only">Loading...</span>
    </div>
  );

  if (center) {
    return (
      <div className={centerClasses[centerType]}>
        {spinnerElement}
      </div>
    );
  }

  return spinnerElement;
});

Spinner.displayName = 'Spinner';

export default Spinner;
