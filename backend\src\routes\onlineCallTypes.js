import express from 'express';
import { body, param, query } from 'express-validator';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';
import {
  getOnlineCallTypes,
  getOnlineCallTypeById,
  createOnlineCallType,
  updateOnlineCallType,
  deleteOnlineCallType,
  searchOnlineCallTypes,
  getCategories,
  bulkUpdateStatus,
} from '../controllers/onlineCallTypeController.js';

const router = express.Router();

// Test route to verify the router is working (no auth required)
router.get('/test', (req, res) => {
  res.json({
    message: 'Online call types router is working!',
    timestamp: new Date().toISOString(),
    path: req.path
  });
});

// Apply authentication to all other routes (after test route)
router.use(authenticateToken);

/**
 * @route   GET /api/v1/online-call-types
 * @desc    Get all online call types with pagination and filtering
 * @access  Private (requires master_data.read permission)
 */
router.get('/', [
  requirePermission('master_data.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Search term must be less than 100 characters'),
  query('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  query('isActive')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('isActive must be true or false'),
  query('sortBy')
    .optional()
    .isIn(['name', 'category', 'sort_order', 'created_at', 'updated_at'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
  validateRequest,
], getOnlineCallTypes);

/**
 * @route   GET /api/v1/online-call-types/search
 * @desc    Search online call types for dropdown
 * @access  Private (requires master_data.read permission)
 */
router.get('/search', [
  requirePermission('master_data.read'),
  query('q')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Search query must be less than 100 characters'),
  query('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Category must be less than 50 characters'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  validateRequest,
], searchOnlineCallTypes);

/**
 * @route   GET /api/v1/online-call-types/categories
 * @desc    Get all categories with counts
 * @access  Private (requires master_data.read permission)
 */
router.get('/categories', [
  requirePermission('master_data.read'),
], getCategories);

/**
 * @route   GET /api/v1/online-call-types/:id
 * @desc    Get online call type by ID
 * @access  Private (requires master_data.read permission)
 */
router.get('/:id', [
  requirePermission('master_data.read'),
  param('id')
    .isUUID()
    .withMessage('Online call type ID must be a valid UUID'),
  validateRequest,
], getOnlineCallTypeById);

/**
 * @route   POST /api/v1/online-call-types
 * @desc    Create new online call type
 * @access  Private (requires master_data.create permission)
 */
router.post('/', [
  requirePermission('master_data.create'),
  body('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .trim(),
  body('category')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters')
    .trim(),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  validateRequest,
], createOnlineCallType);

/**
 * @route   PUT /api/v1/online-call-types/:id
 * @desc    Update online call type
 * @access  Private (requires master_data.update permission)
 */
router.put('/:id', [
  requirePermission('master_data.update'),
  param('id')
    .isUUID()
    .withMessage('Online call type ID must be a valid UUID'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .trim(),
  body('category')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Category must be between 2 and 50 characters')
    .trim(),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be less than 500 characters')
    .trim(),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  body('sort_order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Sort order must be a non-negative integer'),
  validateRequest,
], updateOnlineCallType);

/**
 * @route   DELETE /api/v1/online-call-types/:id
 * @desc    Delete online call type (soft delete)
 * @access  Private (requires master_data.delete permission)
 */
router.delete('/:id', [
  requirePermission('master_data.delete'),
  param('id')
    .isUUID()
    .withMessage('Online call type ID must be a valid UUID'),
  validateRequest,
], deleteOnlineCallType);

/**
 * @route   PUT /api/v1/online-call-types/bulk/status
 * @desc    Bulk update status of online call types
 * @access  Private (requires master_data.update permission)
 */
router.put('/bulk/status', [
  requirePermission('master_data.update'),
  body('ids')
    .isArray({ min: 1 })
    .withMessage('IDs must be a non-empty array'),
  body('ids.*')
    .isUUID()
    .withMessage('Each ID must be a valid UUID'),
  body('is_active')
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  validateRequest,
], bulkUpdateStatus);

export default router;
