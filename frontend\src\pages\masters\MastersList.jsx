import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import * as XLSX from 'xlsx';
import useAppStore from '../../store/appStore';
import {
  FaUsers,
  FaTools,
  FaMapMarkerAlt,
  FaIndustry,
  FaUserTie,
  FaCog,
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaDownload,
  FaUpload,
  FaTh,
  FaList,
  FaEllipsisV
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import { useViewPreference, PAGE_NAMES } from '../../utils/viewPreferences';

const MastersList = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const activeTab = searchParams.get('tab') || 'license-editions';
  const [loading, setLoading] = useState(true);
  const { updateMasterCounts, masterCounts } = useAppStore();
  const [showAddModal, setShowAddModal] = useState(false);
  const [addFormData, setAddFormData] = useState({});
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState({});
  const [editingItem, setEditingItem] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewingItem, setViewingItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [designations, setDesignations] = useState([]);
  const [viewMode, setViewMode] = useViewPreference(PAGE_NAMES.MASTERS, 'table');
  const [dropdownOpen, setDropdownOpen] = useState({});

  // Pagination state for each master type
  const [pagination, setPagination] = useState({
    'license-editions': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'designations': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'tally-products': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'staff-roles': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'executives': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'industries': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'areas': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'nature-of-issues': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'additional-services': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 },
    'call-statuses': { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 10 }
  });
  const [masterData, setMasterData] = useState({
    'license-editions': [],
    'designations': [],
    'tally-products': [],
    'staff-roles': [],
    'executives': [],
    'industries': [],
    'areas': [],
    'nature-of-issues': [],
    'additional-services': [],
    'call-statuses': []
  });

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch master data from API
  useEffect(() => {
    fetchMasterData();
    fetchDesignations();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Reset to page 1 when searching
      if (searchTerm) {
        setPagination(prev => ({
          ...prev,
          [activeTab]: {
            ...prev[activeTab],
            currentPage: 1
          }
        }));
      }
      fetchMasterData();
    }, searchTerm ? 500 : 0); // Debounce search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, activeTab]); // Trigger on search term or tab change

  // Remove the problematic useEffect that causes infinite loop
  // Pagination changes are now handled directly in handlePageChange function

  // Also refresh counts when the active tab changes to ensure sidebar shows correct counts
  useEffect(() => {
    // Small delay to ensure data is loaded
    const timer = setTimeout(() => {
      if (masterData[activeTab] && masterData[activeTab].length > 0) {
        const currentCount = masterData[activeTab].length;
        updateMasterCounts({ [activeTab]: currentCount });
        console.log(`🔄 Updated ${activeTab} count:`, currentCount);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [activeTab, masterData, updateMasterCounts]);

  // Handle page change for pagination
  const handlePageChange = (masterType, newPage) => {
    setPagination(prev => ({
      ...prev,
      [masterType]: {
        ...prev[masterType],
        currentPage: newPage
      }
    }));

    // Trigger data fetch after pagination state update
    setTimeout(() => {
      fetchMasterData();
    }, 0);
  };

  // Handle navigation to dedicated pages
  useEffect(() => {
    if (activeTab === 'online-call-types') {
      navigate('/masters/online-call-types');
      return;
    }
    if (activeTab === 'call-statuses') {
      navigate('/masters/call-statuses');

    }
  }, [activeTab, navigate]);

  // Reset form data when activeTab changes
  useEffect(() => {
    setAddFormData({});
    setEditFormData({});
    setShowAddModal(false);
    setShowEditModal(false);
  }, [activeTab]);

  // Toggle dropdown for actions
  const toggleDropdown = (itemId) => {
    setDropdownOpen(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen({});
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const fetchMasterData = async () => {
    try {
      setLoading(true);

      // Build search params for API calls
      const buildParams = (masterType) => {
        const params = {
          limit: 10,
          page: pagination[masterType].currentPage
        };

        // Add search parameter if search term exists
        if (searchTerm && searchTerm.trim()) {
          params.search = searchTerm.trim();
        }

        return params;
      };

      // Fetch data from all master endpoints with search support
      const [
        licenseEditionsRes,
        designationsRes,
        tallyProductsRes,
        staffRolesRes,
        executivesRes,
        industriesRes,
        areasRes,
        natureOfIssuesRes,
        additionalServicesRes,
        callStatusesRes
      ] = await Promise.all([
        apiService.get('/master-data/license-editions', { params: buildParams('license-editions') }).catch(() => ({ data: { data: { licenseedition: [], pagination: {} } } })),
        apiService.get('/master-data/designations', { params: buildParams('designations') }).catch(() => ({ data: { data: { designation: [], pagination: {} } } })),
        apiService.get('/master-data/tally-products', { params: buildParams('tally-products') }).catch(() => ({ data: { data: { tallyproduct: [], pagination: {} } } })),
        apiService.get('/master-data/staff-roles', { params: buildParams('staff-roles') }).catch(() => ({ data: { data: { staffrole: [], pagination: {} } } })),
        apiService.get('/executives', { params: buildParams('executives') }).catch(() => ({ data: { data: { executives: [], pagination: {} } } })),
        apiService.get('/master-data/industries', { params: buildParams('industries') }).catch(() => ({ data: { data: { industry: [], pagination: {} } } })),
        apiService.get('/master-data/areas', { params: buildParams('areas') }).catch(() => ({ data: { data: { area: [], pagination: {} } } })),
        apiService.get('/master-data/nature-of-issues', { params: buildParams('nature-of-issues') }).catch(() => ({ data: { data: { natureofissue: [], pagination: {} } } })),
        apiService.get('/master-data/additional-services', { params: buildParams('additional-services') }).catch(() => ({ data: { data: { additionalservice: [], pagination: {} } } })),
        apiService.get('/master-data/call-statuses', { params: buildParams('call-statuses') }).catch(() => ({ data: { data: { callstatus: [], pagination: {} } } }))
      ]);

      // Debug: Log API responses (remove in production)
      if (process.env.NODE_ENV === 'development') {
        console.log('API Responses:', {
          licenseEditions: licenseEditionsRes.data?.data,
          designations: designationsRes.data?.data,
          tallyProducts: tallyProductsRes.data?.data,
          staffRoles: staffRolesRes.data?.data,
          executives: executivesRes.data?.data,
          industries: industriesRes.data?.data,
          areas: areasRes.data?.data,
          natureOfIssues: natureOfIssuesRes.data?.data,
          additionalServices: additionalServicesRes.data?.data,
          callStatuses: callStatusesRes.data?.data
        });
      }

      // Transform API data
      setMasterData({
        'license-editions': (licenseEditionsRes.data?.data?.licenseedition || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'designations': (designationsRes.data?.data?.designation || []).map(item => ({
          ...item, // Keep all original fields
          isMandatory: item.is_mandatory || false, // Add computed field for display
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'tally-products': (tallyProductsRes.data?.data?.tallyproduct || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'staff-roles': (staffRolesRes.data?.data?.staffrole || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'executives': (executivesRes.data?.data?.executives || []).map(item => ({
          ...item, // Keep all original fields
          name: `${item.first_name || ''} ${item.last_name || ''}`.trim() || 'Unknown Executive', // Add computed field for display
          role: item.designation?.name || 'Employee', // Add computed field for display - extract name from designation object
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'industries': (industriesRes.data?.data?.industry || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'areas': (areasRes.data?.data?.area || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'nature-of-issues': (natureOfIssuesRes.data?.data?.natureofissue || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'additional-services': (additionalServicesRes.data?.data?.additionalservice || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        })),
        'call-statuses': (callStatusesRes.data?.data?.callstatus || []).map(item => ({
          ...item, // Keep all original fields
          status: (item.is_active !== undefined && item.is_active !== null) ? (item.is_active ? 'active' : 'inactive') : 'active'
        }))
      });

      // Update master counts in the store
      const newMasterData = {
        'license-editions': (licenseEditionsRes.data?.data?.licenseedition || []),
        'designations': (designationsRes.data?.data?.designation || []),
        'tally-products': (tallyProductsRes.data?.data?.tallyproduct || []),
        'staff-roles': (staffRolesRes.data?.data?.staffrole || []),
        'executives': (executivesRes.data?.data?.executives || []),
        'industries': (industriesRes.data?.data?.industry || []),
        'areas': (areasRes.data?.data?.area || []),
        'nature-of-issues': (natureOfIssuesRes.data?.data?.natureofissue || []),
        'additional-services': (additionalServicesRes.data?.data?.additionalservice || []),
        'call-statuses': (callStatusesRes.data?.data?.callstatus || [])
      };

      // Calculate counts from actual data arrays and log for debugging
      const counts = {
        'license-editions': licenseEditionsRes.data?.data?.pagination?.totalItems || newMasterData['license-editions'].length,
        'designations': designationsRes.data?.data?.pagination?.totalItems || newMasterData.designations.length,
        'tally-products': tallyProductsRes.data?.data?.pagination?.totalItems || newMasterData['tally-products'].length,
        'staff-roles': staffRolesRes.data?.data?.pagination?.totalItems || newMasterData['staff-roles'].length,
        'executives': executivesRes.data?.data?.pagination?.totalItems || newMasterData.executives.length,
        'industries': industriesRes.data?.data?.pagination?.totalItems || newMasterData.industries.length,
        'areas': areasRes.data?.data?.pagination?.totalItems || newMasterData.areas.length,
        'nature-of-issues': natureOfIssuesRes.data?.data?.pagination?.totalItems || newMasterData['nature-of-issues'].length,
        'additional-services': additionalServicesRes.data?.data?.pagination?.totalItems || newMasterData['additional-services'].length,
        'call-statuses': callStatusesRes.data?.data?.pagination?.totalItems || newMasterData['call-statuses'].length,
      };

      console.log('🔍 Master Data Counts:', counts);
      console.log('🔍 Sample API Response Structure:', {
        licenseEditions: {
          hasData: !!licenseEditionsRes.data?.data,
          hasPagination: !!licenseEditionsRes.data?.data?.pagination,
          arrayLength: newMasterData['license-editions'].length,
          totalItems: licenseEditionsRes.data?.data?.pagination?.totalItems
        }
      });

      // Update pagination state with server response (preserve current page from state)
      setPagination(prev => ({
        ...prev,
        'license-editions': {
          ...prev['license-editions'],
          totalPages: licenseEditionsRes.data?.data?.pagination?.totalPages || 1,
          totalItems: licenseEditionsRes.data?.data?.pagination?.totalItems || 0,
          // Keep the current page from state, don't override with server response
          currentPage: prev['license-editions'].currentPage
        },
        'designations': {
          ...prev.designations,
          totalPages: designationsRes.data?.data?.pagination?.totalPages || 1,
          totalItems: designationsRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev.designations.currentPage
        },
        'tally-products': {
          ...prev['tally-products'],
          totalPages: tallyProductsRes.data?.data?.pagination?.totalPages || 1,
          totalItems: tallyProductsRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev['tally-products'].currentPage
        },
        'staff-roles': {
          ...prev['staff-roles'],
          totalPages: staffRolesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: staffRolesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev['staff-roles'].currentPage
        },
        'executives': {
          ...prev.executives,
          totalPages: executivesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: executivesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev.executives.currentPage
        },
        'industries': {
          ...prev.industries,
          totalPages: industriesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: industriesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev.industries.currentPage
        },
        'areas': {
          ...prev.areas,
          totalPages: areasRes.data?.data?.pagination?.totalPages || 1,
          totalItems: areasRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev.areas.currentPage
        },
        'nature-of-issues': {
          ...prev['nature-of-issues'],
          totalPages: natureOfIssuesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: natureOfIssuesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev['nature-of-issues'].currentPage
        },
        'additional-services': {
          ...prev['additional-services'],
          totalPages: additionalServicesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: additionalServicesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev['additional-services'].currentPage
        },
        'call-statuses': {
          ...prev['call-statuses'],
          totalPages: callStatusesRes.data?.data?.pagination?.totalPages || 1,
          totalItems: callStatusesRes.data?.data?.pagination?.totalItems || 0,
          currentPage: prev['call-statuses'].currentPage
        }
      }));

      updateMasterCounts(counts);

    } catch (error) {
      console.error('Error fetching master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setLoading(false);
    }
  };

  const fetchDesignations = async () => {
    try {
      const response = await apiService.get('/master-data/designations');
      if (response.data?.data?.designation) {
        setDesignations(response.data.data.designation);
      }
    } catch (error) {
      console.error('Error fetching designations:', error);
    }
  };

  const masterCategories = [
    {
      id: 'license-editions',
      title: 'License Editions',
      icon: <FaCog className="mr-2" />,
      description: 'Manage Tally license types: Silver, Gold, Platinum',
      count: masterData['license-editions'].length,
      color: 'primary'
    },
    {
      id: 'designations',
      title: 'Designations',
      icon: <FaUserTie className="mr-2" />,
      description: 'Define Customer Contact Roles: Owner, Auditor, Manager',
      count: masterData.designations.length,
      color: 'success'
    },
    {
      id: 'tally-products',
      title: 'Tally Products',
      icon: <FaTools className="mr-2" />,
      description: 'Manage product variants: Prime, Normal Edition',
      count: masterData['tally-products'].length,
      color: 'info'
    },
    {
      id: 'staff-roles',
      title: 'Staff Roles',
      icon: <FaUsers className="mr-2" />,
      description: 'Define user roles: Support, Sales, Admin',
      count: masterData['staff-roles'].length,
      color: 'warning'
    },
    {
      id: 'executives',
      title: 'Executives',
      icon: <FaUsers className="mr-2" />,
      description: 'Manage employee profiles and assignments',
      count: masterData.executives.length,
      color: 'secondary'
    },
    {
      id: 'industries',
      title: 'Industries',
      icon: <FaIndustry className="mr-2" />,
      description: 'Define Customer Industry Types: Garments, SuperMarket',
      count: masterData.industries.length,
      color: 'dark'
    },
    {
      id: 'areas',
      title: 'Areas',
      icon: <FaMapMarkerAlt className="mr-2" />,
      description: 'Manage customer locations: Tiruppur, Vellore',
      count: masterData.areas.length,
      color: 'primary'
    },
    {
      id: 'nature-of-issues',
      title: 'Nature of Issues',
      icon: <FaCog className="mr-2" />,
      description: 'Categorize service issue types: Installation, Bug Fix',
      count: masterData['nature-of-issues'].length,
      color: 'success'
    },
    {
      id: 'additional-services',
      title: 'Additional Services',
      icon: <FaTools className="mr-2" />,
      description: 'Manage extra services: Customization, Data Migration',
      count: masterData['additional-services'].length,
      color: 'info'
    },
    {
      id: 'call-statuses',
      title: 'Call Statuses',
      icon: <FaCog className="mr-2" />,
      description: 'Define service call statuses: Pending, Completed, Follow-up',
      count: masterData['call-statuses'].length,
      color: 'warning'
    },
    {
      id: 'online-call-types',
      title: 'Online Call Types',
      icon: <FaTools className="mr-2" />,
      description: 'Manage online call types and issues: GST, Installation, Technical Support',
      count: 0, // Will be updated when we load the data
      color: 'info'
    }
  ];


  const getStatusBadge = (status) => {
    // Handle undefined, null, or empty status
    const safeStatus = status || 'inactive';

    const statusConfig = {
      'active': { bg: 'bg-green-100', text: 'text-green-800', icon: '✅' },
      'inactive': { bg: 'bg-gray-100', text: 'text-gray-800', icon: '❌' }
    };

    const config = statusConfig[safeStatus] || statusConfig.inactive;
    const displayText = safeStatus.charAt(0).toUpperCase() + safeStatus.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };


  const handleAddNew = (type) => {
    // Handle special case for online call types
    if (type === 'online-call-types') {
      navigate('/masters/online-call-types');
      return;
    }

    // First reset any existing form data
    setAddFormData({});

    // Initialize form data based on type
    let initialData = {};
    switch (type) {
      case 'license-editions':
        initialData = {
          name: '',
          code: '',
          description: '',
          version: '',
          price: '',
          annual_maintenance_charge: '',
          max_companies: '',
          max_users: '',
          status: 'active'
        };
        break;
      case 'designations':
        initialData = {
          name: '',
          code: '',
          description: '',
          level: '1',
          department: '',
          is_mandatory: false,
          status: 'active'
        };
        break;
      case 'tally-products':
        initialData = {
          name: '',
          code: '',
          description: '',
          category: 'software',
          version: '',
          price: '',
          cost_price: '',
          hsn_code: '',
          gst_rate: '18',
          unit: 'Nos',
          is_service: false,
          status: 'active'
        };
        break;
      case 'staff-roles':
        initialData = {
          name: '',
          code: '',
          description: '',
          department: 'support',
          level: '1',
          status: 'active'
        };
        break;
      case 'executives':
        initialData = {
          employee_code: '',
          first_name: '',
          last_name: '',
          email: '',
          phone: '',
          alternate_phone: '',
          designation_id: '',
          department: 'support',
          date_of_birth: '',
          date_of_joining: '',
          address: '',
          city: '',
          state: '',
          postal_code: '',
          salary: '',
          commission_rate: '',
          target_amount: '',
          skills: [],
          areas_covered: [],
          status: 'active'
        };
        break;
      case 'industries':
        initialData = {
          name: '',
          code: '',
          description: '',
          category: '',
          status: 'active'
        };
        break;
      case 'areas':
        initialData = {
          name: '',
          code: '',
          description: '',
          city: '',
          state: '',
          country: 'India',
          status: 'active'
        };
        break;
      case 'nature-of-issues':
        initialData = {
          name: '',
          code: '',
          description: '',
          category: 'technical',
          priority: 'medium',
          estimated_resolution_time: '',
          status: 'active'
        };
        break;
      case 'additional-services':
        initialData = {
          name: '',
          code: '',
          description: '',
          category: 'customization',
          price: '',
          duration_hours: '',
          status: 'active'
        };
        break;
      case 'call-statuses':
        initialData = {
          name: '',
          code: '',
          description: '',
          category: 'open',
          color: '#3B82F6',
          is_final: false,
          requires_approval: false,
          auto_close_after_days: '',
          is_billable: false,
          status: 'active'
        };
        break;
      default:
        initialData = {};
    }
    setAddFormData(initialData);
    setShowAddModal(true);
  };

  const validateFormData = (data, type) => {
    // Validate executives
    if (type === 'executives') {
      if (!data.first_name || data.first_name.trim() === '') {
        throw new Error('First name is required');
      }
      if (!data.last_name || data.last_name.trim() === '') {
        throw new Error('Last name is required');
      }
      if (!data.email || data.email.trim() === '') {
        throw new Error('Email is required');
      }
      if (!data.phone || data.phone.trim() === '') {
        throw new Error('Phone number is required');
      }
      if (!data.designation_id || data.designation_id.trim() === '') {
        throw new Error('Designation is required');
      }
      if (!data.department || data.department.trim() === '') {
        throw new Error('Department is required');
      }
      if (!data.address || data.address.trim() === '') {
        throw new Error('Address is required');
      }
      if (!data.city || data.city.trim() === '') {
        throw new Error('City is required');
      }
      if (!data.state || data.state.trim() === '') {
        throw new Error('State is required');
      }
      if (!data.postal_code || data.postal_code.trim() === '') {
        throw new Error('Postal code is required');
      }
      if (!data.salary || data.salary.trim() === '') {
        throw new Error('Salary is required');
      }
      if (!data.commission_rate || data.commission_rate.trim() === '') {
        throw new Error('Commission rate is required');
      }
      if (!data.target_amount || data.target_amount.trim() === '') {
        throw new Error('Target amount is required');
      }

      // Validate phone number format
      const phoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
      if (!phoneRegex.test(data.phone)) {
        throw new Error('Please enter a valid phone number');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Validate alternate phone if provided (only if not empty)
      if (data.alternate_phone && data.alternate_phone.trim() !== '') {
        if (!phoneRegex.test(data.alternate_phone)) {
          throw new Error('Please enter a valid alternate phone number');
        }
      }

      // Validate commission rate
      const rate = parseFloat(data.commission_rate);
      if (isNaN(rate) || rate < 0 || rate > 100) {
        throw new Error('Commission rate must be between 0 and 100');
      }

      // Validate salary
      const salary = parseFloat(data.salary);
      if (isNaN(salary) || salary < 0) {
        throw new Error('Salary must be a positive number');
      }

      // Validate target amount
      const target = parseFloat(data.target_amount);
      if (isNaN(target) || target < 0) {
        throw new Error('Target amount must be a positive number');
      }

      return true;
    }

    // Check required fields for other types
    if (!data.name || data.name.trim() === '') {
      throw new Error('Name is required');
    }

    // Validate code field for industries and locations
    if (type === 'industries' || type === 'locations') {
      if (!data.code || data.code.trim() === '') {
        throw new Error('Code is required');
      }
      const codeRegex = /^[A-Z0-9_]+$/;
      if (!codeRegex.test(data.code)) {
        throw new Error('Code must contain only uppercase letters, numbers, and underscores');
      }
    }

    // Validate specific fields for locations
    if (type === 'locations') {
      if (!data.city || data.city.trim() === '') {
        throw new Error('City is required');
      }
      if (!data.state || data.state.trim() === '') {
        throw new Error('State is required');
      }
    }

    return true;
  };

  const handleAddSubmit = async () => {
    try {
      let endpoint = '';
      const data = addFormData;

      // Validate form data
      validateFormData(data, activeTab);

      switch (activeTab) {
        case 'license-editions':
          endpoint = '/master-data/license-editions';
          break;
        case 'designations':
          endpoint = '/master-data/designations';
          break;
        case 'tally-products':
          endpoint = '/master-data/tally-products';
          break;
        case 'staff-roles':
          endpoint = '/master-data/staff-roles';
          break;
        case 'executives':
          endpoint = '/executives';
          break;
        case 'industries':
          endpoint = '/master-data/industries';
          break;
        case 'areas':
          endpoint = '/master-data/areas';
          break;
        case 'nature-of-issues':
          endpoint = '/master-data/nature-of-issues';
          break;
        case 'additional-services':
          endpoint = '/master-data/additional-services';
          break;
        case 'call-statuses':
          endpoint = '/master-data/call-statuses';
          break;
        default:
          toast.error('Invalid item type');
          return;
      }

      const response = await apiService.post(endpoint, data);

      if (process.env.NODE_ENV === 'development') {
        console.log('Create response:', response.data);
      }

      if (response.data?.success) {
        // Add item to local state - extract the actual item from the response
        let newItem = response.data.data;

        if (process.env.NODE_ENV === 'development') {
          console.log('New item data:', newItem);
        }

        // The API returns data in format: { [modelName]: actualItem }
        // Extract the actual item based on the endpoint
        if (activeTab === 'industries' && newItem.industry) {
          newItem = newItem.industry;
        } else if (activeTab === 'locations' && newItem.area) {
          newItem = newItem.area;
        } else if (activeTab === 'products' && newItem.tallyproduct) {
          newItem = newItem.tallyproduct;
        } else if (activeTab === 'customers' && newItem.designation) {
          newItem = newItem.designation;
        } else if (activeTab === 'system' && newItem.staffrole) {
          newItem = newItem.staffrole;
        } else if (activeTab === 'employees' && newItem.executive) {
          newItem = newItem.executive;
        }

        // Transform the new item to match our frontend format
        let transformedItem = {};
        switch (activeTab) {
          case 'industries':
            transformedItem = {
              id: newItem.id,
              name: newItem.name || 'Unknown Industry',
              description: newItem.description || 'No description',
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          case 'locations':
            transformedItem = {
              id: newItem.id,
              state: newItem.state || 'Unknown State',
              city: newItem.city || newItem.name || 'Unknown City',
              pincode: newItem.pincode || 'N/A',
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          case 'products':
            transformedItem = {
              id: newItem.id,
              name: newItem.name || 'Unknown Product',
              category: newItem.category || 'Software',
              price: newItem.price || 0,
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          case 'customers':
            transformedItem = {
              id: newItem.id,
              type: newItem.name || 'Unknown Type',
              description: newItem.description || 'No description',
              minRevenue: 0,
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          case 'system':
            transformedItem = {
              id: newItem.id,
              setting: newItem.name || 'Unknown Setting',
              value: newItem.department || 'N/A',
              category: 'Staff Role',
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          case 'employees':
            transformedItem = {
              id: newItem.id,
              name: `${newItem.first_name || ''} ${newItem.last_name || ''}`.trim() || 'Unknown Employee',
              role: newItem.designation?.name || 'Employee', // Extract name from designation object
              department: newItem.department || 'General',
              status: (newItem.is_active !== undefined && newItem.is_active !== null) ? (newItem.is_active ? 'active' : 'inactive') : 'active'
            };
            break;
          default:
            transformedItem = newItem;
        }

        setMasterData(prev => ({
          ...prev,
          [activeTab]: [...prev[activeTab], transformedItem]
        }));
        toast.success('Item added successfully');
        setShowAddModal(false);
        setAddFormData({});

        // Refresh data to ensure consistency
        fetchMasterData();
      } else {
        toast.error(response.data?.message || 'Failed to add item');
      }
    } catch (error) {
      console.error('Error adding item:', error);
      if (error.message && error.message.includes('Code must contain') || error.message.includes('is required')) {
        toast.error(error.message);
      } else {
        toast.error(error.response?.data?.message || 'Failed to add item');
      }
    }
  };

  const handleView = (item) => {
    setViewingItem(item);
    setShowViewModal(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);

    // Since we now preserve all original fields, we can directly use the item data
    // Just need to handle the status field conversion
    const formData = {
      ...item,
      status: item.is_active !== undefined ? (item.is_active ? 'active' : 'inactive') : 'active'
    };

    console.log('Edit form data:', formData); // Debug log
    setEditFormData(formData);
    setShowEditModal(true);
  };

  const handleEditSubmit = async () => {
    try {
      let endpoint = '';
      const data = { ...editFormData };

      // Convert status back to is_active
      data.is_active = data.status === 'active';
      delete data.status;

      switch (activeTab) {
        case 'license-editions':
          endpoint = `/master-data/license-editions/${editingItem.id}`;
          break;
        case 'designations':
          endpoint = `/master-data/designations/${editingItem.id}`;
          break;
        case 'tally-products':
          endpoint = `/master-data/tally-products/${editingItem.id}`;
          break;
        case 'staff-roles':
          endpoint = `/master-data/staff-roles/${editingItem.id}`;
          break;
        case 'executives':
          endpoint = `/executives/${editingItem.id}`;
          break;
        case 'industries':
          endpoint = `/master-data/industries/${editingItem.id}`;
          break;
        case 'areas':
          endpoint = `/master-data/areas/${editingItem.id}`;
          break;
        case 'nature-of-issues':
          endpoint = `/master-data/nature-of-issues/${editingItem.id}`;
          break;
        case 'additional-services':
          endpoint = `/master-data/additional-services/${editingItem.id}`;
          break;
        case 'call-statuses':
          endpoint = `/master-data/call-statuses/${editingItem.id}`;
          break;
        default:
          toast.error('Invalid item type');
          return;
      }

      const response = await apiService.put(endpoint, data);

      if (response.data?.success) {
        // Update item in local state
        setMasterData(prev => ({
          ...prev,
          [activeTab]: prev[activeTab].map(item =>
            (item.id === editingItem.id
              ? { ...item, ...data, status: data.is_active ? 'active' : 'inactive' }
              : item)
          )
        }));

        toast.success('Item updated successfully');
        setShowEditModal(false);
        setEditFormData({});
        setEditingItem(null);

        // Refresh data to ensure consistency
        fetchMasterData();
      } else {
        toast.error(response.data?.message || 'Failed to update item');
      }
    } catch (error) {
      console.error('Error updating item:', error);
      toast.error(error.response?.data?.message || 'Failed to update item');
    }
  };

  const handleDelete = async (type, itemId) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        let endpoint = '';
        switch (type) {
          case 'license-editions':
            endpoint = `/master-data/license-editions/${itemId}`;
            break;
          case 'designations':
            endpoint = `/master-data/designations/${itemId}`;
            break;
          case 'tally-products':
            endpoint = `/master-data/tally-products/${itemId}`;
            break;
          case 'staff-roles':
            endpoint = `/master-data/staff-roles/${itemId}`;
            break;
          case 'executives':
            endpoint = `/executives/${itemId}`;
            break;
          case 'industries':
            endpoint = `/master-data/industries/${itemId}`;
            break;
          case 'areas':
            endpoint = `/master-data/areas/${itemId}`;
            break;
          case 'nature-of-issues':
            endpoint = `/master-data/nature-of-issues/${itemId}`;
            break;
          case 'additional-services':
            endpoint = `/master-data/additional-services/${itemId}`;
            break;
          case 'call-statuses':
            endpoint = `/master-data/call-statuses/${itemId}`;
            break;
          default:
            toast.error('Invalid item type');
            return;
        }

        const response = await apiService.delete(endpoint);
        if (response.data?.success) {
          // Remove item from local state
          setMasterData(prev => ({
            ...prev,
            [type]: prev[type].filter(item => item.id !== itemId)
          }));

          // Update master counts in store
          const newCounts = { ...masterCounts };
          newCounts[type] = (newCounts[type] || 1) - 1;
          updateMasterCounts(newCounts);

          toast.success('Item deleted successfully');

          // Refresh data to ensure consistency
          fetchMasterData();
        } else {
          toast.error(response.data?.message || 'Failed to delete item');
        }
      } catch (error) {
        console.error('Error deleting item:', error);
        if (error.response?.status === 409) {
          toast.error(error.response?.data?.message || 'Cannot delete item - it may be in use');
        } else if (error.response?.status === 404) {
          toast.error('Item not found');
        } else {
          toast.error('Failed to delete item');
        }
      }
    }
  };

  // Get data for display (server-side search is now handled in API calls)
  const getFilteredData = (type) => {
    let data = masterData[type] || [];

    // Apply status filter (keep client-side for now as it's not implemented server-side)
    if (filterStatus !== 'all') {
      data = data.filter(item => item.status === filterStatus);
    }

    return data;
  };

  // Export to Excel functionality
  const handleExport = async (type) => {
    try {
      const data = getFilteredData(type);
      const categoryTitle = masterCategories.find(cat => cat.id === type)?.title || 'Master Data';

      if (data.length === 0) {
        toast.error('No data available to export');
        return;
      }

      // Define field order and labels for better export
      const fieldMappings = getExportFieldMappings(type);

      // Create CSV content with proper headers
      const headers = fieldMappings.map(field => field.label);
      let csvContent = `${headers.join(',')}\n`;

      // Add data rows
      data.forEach(item => {
        const row = fieldMappings.map(field => {
          let value = item[field.key];

          // Handle special formatting
          if (field.key === 'is_active') {
            value = value ? 'Active' : 'Inactive';
          } else if (field.key === 'status') {
            value = value === 'active' ? 'Active' : 'Inactive';
          } else if (field.type === 'currency' && value) {
            value = `₹${parseFloat(value).toFixed(2)}`;
          } else if (field.type === 'boolean') {
            value = value ? 'Yes' : 'No';
          } else if (field.type === 'date' && value) {
            value = new Date(value).toLocaleDateString();
          }

          // Handle null/undefined values
          if (value === null || value === undefined) {
            value = '';
          }

          // Escape values that contain commas, quotes, or newlines
          value = String(value);
          if (value.includes(',') || value.includes('"') || value.includes('\n')) {
            value = `"${value.replace(/"/g, '""')}"`;
          }

          return value;
        });
        csvContent += `${row.join(',')}\n`;
      });

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      const fileName = `${categoryTitle.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;

      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      toast.success(`${categoryTitle} data exported successfully (${data.length} records)`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data');
    }
  };

  // Get export field mappings for each master type
  const getExportFieldMappings = (masterType) => {
    const commonFields = [
      { key: 'name', label: 'Name', type: 'text' },
      { key: 'code', label: 'Code', type: 'text' },
      { key: 'description', label: 'Description', type: 'text' },
    ];

    const statusField = { key: 'is_active', label: 'Status', type: 'boolean' };

    const fieldMappings = {
      'license-editions': [
        ...commonFields,
        { key: 'version', label: 'Version', type: 'text' },
        { key: 'price', label: 'Price', type: 'currency' },
        { key: 'annual_maintenance_charge', label: 'AMC', type: 'currency' },
        { key: 'max_companies', label: 'Max Companies', type: 'number' },
        { key: 'max_users', label: 'Max Users', type: 'number' },
        statusField
      ],
      'designations': [
        ...commonFields,
        { key: 'level', label: 'Level', type: 'number' },
        { key: 'department', label: 'Department', type: 'text' },
        { key: 'is_mandatory', label: 'Mandatory', type: 'boolean' },
        statusField
      ],
      'tally-products': [
        ...commonFields,
        { key: 'category', label: 'Category', type: 'text' },
        { key: 'version', label: 'Version', type: 'text' },
        { key: 'price', label: 'Price', type: 'currency' },
        { key: 'cost_price', label: 'Cost Price', type: 'currency' },
        { key: 'hsn_code', label: 'HSN Code', type: 'text' },
        { key: 'gst_rate', label: 'GST Rate (%)', type: 'number' },
        { key: 'unit', label: 'Unit', type: 'text' },
        { key: 'is_service', label: 'Is Service', type: 'boolean' },
        statusField
      ],
      'staff-roles': [
        ...commonFields,
        { key: 'department', label: 'Department', type: 'text' },
        { key: 'level', label: 'Level', type: 'number' },
        statusField
      ],
      'executives': [
        { key: 'employee_code', label: 'Employee Code', type: 'text' },
        { key: 'first_name', label: 'First Name', type: 'text' },
        { key: 'last_name', label: 'Last Name', type: 'text' },
        { key: 'email', label: 'Email', type: 'text' },
        { key: 'phone', label: 'Phone', type: 'text' },
        { key: 'department', label: 'Department', type: 'text' },
        { key: 'date_of_birth', label: 'Date of Birth', type: 'date' },
        { key: 'date_of_joining', label: 'Date of Joining', type: 'date' },
        { key: 'address', label: 'Address', type: 'text' },
        statusField
      ],
      'industries': [
        ...commonFields,
        { key: 'category', label: 'Category', type: 'text' },
        statusField
      ],
      'areas': [
        ...commonFields,
        { key: 'city', label: 'City', type: 'text' },
        { key: 'state', label: 'State', type: 'text' },
        { key: 'country', label: 'Country', type: 'text' },
        statusField
      ],
      'nature-of-issues': [
        ...commonFields,
        { key: 'category', label: 'Category', type: 'text' },
        { key: 'severity', label: 'Severity', type: 'text' },
        { key: 'estimated_resolution_time', label: 'Est. Resolution Time (hrs)', type: 'number' },
        { key: 'requires_onsite', label: 'Requires Onsite', type: 'boolean' },
        statusField
      ],
      'additional-services': [
        ...commonFields,
        { key: 'category', label: 'Category', type: 'text' },
        { key: 'service_type', label: 'Service Type', type: 'text' },
        { key: 'price', label: 'Price', type: 'currency' },
        { key: 'cost_price', label: 'Cost Price', type: 'currency' },
        { key: 'duration_hours', label: 'Duration (hrs)', type: 'number' },
        { key: 'gst_rate', label: 'GST Rate (%)', type: 'number' },
        { key: 'requires_approval', label: 'Requires Approval', type: 'boolean' },
        { key: 'is_billable', label: 'Is Billable', type: 'boolean' },
        statusField
      ],
      'call-statuses': [
        ...commonFields,
        { key: 'category', label: 'Category', type: 'text' },
        { key: 'color', label: 'Color', type: 'text' },
        { key: 'is_final', label: 'Is Final', type: 'boolean' },
        { key: 'requires_approval', label: 'Requires Approval', type: 'boolean' },
        { key: 'auto_close_after_days', label: 'Auto Close After (days)', type: 'number' },
        { key: 'is_billable', label: 'Is Billable', type: 'boolean' },
        statusField
      ]
    };

    return fieldMappings[masterType] || [...commonFields, statusField];
  };

  // Import from Excel functionality
  const handleImport = (masterType) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.xlsx,.xls';
    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const loadingToast = toast.loading('Processing import file...');

      try {
        let importData = [];

        // Handle different file types
        if (file.name.endsWith('.csv')) {
          // Handle CSV files
          const text = await file.text();
          const lines = text.split('\n');
          const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

          for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
              const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
              const item = {};
              headers.forEach((header, index) => {
                item[header] = values[index] || '';
              });
              importData.push(item);
            }
          }
        } else {
          // Handle Excel files (.xlsx, .xls)
          const arrayBuffer = await file.arrayBuffer();
          const workbook = XLSX.read(arrayBuffer, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          importData = jsonData;
        }

        if (importData.length === 0) {
          toast.dismiss(loadingToast);
          toast.error('No valid data found in file');
          return;
        }

        // Validate and transform data based on master type
        const validatedData = [];
        const errors = [];

        for (let i = 0; i < importData.length; i++) {
          const item = importData[i];
          const rowNumber = i + 2; // +2 because Excel rows start at 1 and we skip header

          try {
            const validatedItem = validateAndTransformImportData(masterType, item, rowNumber);
            if (validatedItem) {
              validatedData.push(validatedItem);
            }
          } catch (error) {
            errors.push(`Row ${rowNumber}: ${error.message}`);
          }
        }

        if (errors.length > 0) {
          toast.dismiss(loadingToast);
          toast.error(`Import failed with ${errors.length} errors. Check console for details.`);
          console.error('Import validation errors:', errors);
          return;
        }

        if (validatedData.length === 0) {
          toast.dismiss(loadingToast);
          toast.error('No valid records found after validation');
          return;
        }

        // Send data to backend
        await importDataToBackend(masterType, validatedData, loadingToast);

      } catch (error) {
        toast.dismiss(loadingToast);
        console.error('Import error:', error);
        toast.error('Failed to process import file');
      }
    };
    input.click();
  };

  // Validate and transform import data based on master type
  const validateAndTransformImportData = (masterType, item, rowNumber) => {
    const requiredFields = getRequiredFieldsForMasterType(masterType);
    const transformedItem = {};

    // Check required fields
    for (const field of requiredFields) {
      if (!item[field] || item[field].toString().trim() === '') {
        throw new Error(`Required field '${field}' is missing or empty`);
      }
      transformedItem[field] = item[field].toString().trim();
    }

    // Add optional fields
    const optionalFields = getOptionalFieldsForMasterType(masterType);
    for (const field of optionalFields) {
      if (item[field] !== undefined && item[field] !== null) {
        transformedItem[field] = item[field];
      }
    }

    // Set default values
    transformedItem.is_active = item.is_active !== undefined ?
      (item.is_active === true || item.is_active === 'true' || item.is_active === 'active' || item.is_active === 1) :
      true;

    return transformedItem;
  };

  // Get required fields for each master type
  const getRequiredFieldsForMasterType = (masterType) => {
    const fieldMap = {
      'license-editions': ['name', 'code'],
      'designations': ['name', 'code'],
      'tally-products': ['name', 'code'],
      'staff-roles': ['name', 'code'],
      'executives': ['employee_code', 'first_name', 'last_name', 'phone'],
      'industries': ['name', 'code'],
      'areas': ['name', 'code'],
      'nature-of-issues': ['name', 'code'],
      'additional-services': ['name', 'code'],
      'call-statuses': ['name', 'code']
    };
    return fieldMap[masterType] || ['name', 'code'];
  };

  // Get optional fields for each master type
  const getOptionalFieldsForMasterType = (masterType) => {
    const fieldMap = {
      'license-editions': ['description', 'version', 'price', 'annual_maintenance_charge', 'max_companies', 'max_users'],
      'designations': ['description', 'level', 'department', 'is_mandatory'],
      'tally-products': ['description', 'category', 'version', 'price', 'cost_price', 'hsn_code', 'gst_rate', 'unit', 'is_service'],
      'staff-roles': ['description', 'department', 'level'],
      'executives': ['email', 'department', 'date_of_birth', 'date_of_joining', 'address', 'city', 'state', 'country', 'pincode'],
      'industries': ['description', 'category'],
      'areas': ['description', 'city', 'state', 'country'],
      'nature-of-issues': ['description', 'category', 'severity', 'estimated_resolution_time', 'requires_onsite'],
      'additional-services': ['description', 'category', 'service_type', 'price', 'cost_price', 'duration_hours', 'gst_rate', 'requires_approval', 'is_billable'],
      'call-statuses': ['description', 'category', 'color', 'is_final', 'requires_approval', 'auto_close_after_days', 'is_billable']
    };
    return fieldMap[masterType] || ['description'];
  };

  // Import data to backend using individual API calls
  const importDataToBackend = async (masterType, validatedData, loadingToast) => {
    try {
      let endpoint = '';
      switch (masterType) {
        case 'license-editions':
          endpoint = '/master-data/license-editions';
          break;
        case 'designations':
          endpoint = '/master-data/designations';
          break;
        case 'tally-products':
          endpoint = '/master-data/tally-products';
          break;
        case 'staff-roles':
          endpoint = '/master-data/staff-roles';
          break;
        case 'executives':
          endpoint = '/executives';
          break;
        case 'industries':
          endpoint = '/master-data/industries';
          break;
        case 'areas':
          endpoint = '/master-data/areas';
          break;
        case 'nature-of-issues':
          endpoint = '/master-data/nature-of-issues';
          break;
        case 'additional-services':
          endpoint = '/master-data/additional-services';
          break;
        case 'call-statuses':
          endpoint = '/master-data/call-statuses';
          break;
        default:
          throw new Error('Invalid master type');
      }

      const successfulImports = [];
      const failedImports = [];
      let currentIndex = 0;

      // Process records one by one
      for (const record of validatedData) {
        currentIndex++;

        // Update loading toast with progress
        toast.loading(`Importing record ${currentIndex} of ${validatedData.length}...`, {
          id: loadingToast
        });

        try {
          const response = await apiService.post(endpoint, record);

          if (response.data?.success) {
            successfulImports.push({
              record,
              response: response.data
            });
          } else {
            failedImports.push({
              record,
              error: response.data?.message || 'Unknown error'
            });
          }
        } catch (error) {
          failedImports.push({
            record,
            error: error.response?.data?.message || error.message || 'Network error'
          });
        }

        // Small delay to prevent overwhelming the server
        if (currentIndex < validatedData.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      toast.dismiss(loadingToast);

      // Show results
      if (successfulImports.length > 0 && failedImports.length === 0) {
        toast.success(`Successfully imported all ${successfulImports.length} records!`);
      } else if (successfulImports.length > 0 && failedImports.length > 0) {
        toast.success(`Imported ${successfulImports.length} records successfully. ${failedImports.length} failed.`);
        console.warn('Failed imports:', failedImports);
      } else {
        toast.error(`Import failed. ${failedImports.length} records could not be imported.`);
        console.error('All imports failed:', failedImports);
      }

      // Refresh the data if any imports were successful
      if (successfulImports.length > 0) {
        fetchMasterData();
      }

    } catch (error) {
      toast.dismiss(loadingToast);
      console.error('Import process error:', error);
      toast.error('Failed to process import');
    }
  };

  // Render form fields based on master type
  const renderFormFields = (type, formData, setFormData, isEdit = false) => {
    const handleInputChange = (field, value) => {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    };

    const commonFields = (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter name"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Code <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              value={formData.code || ''}
              onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
              placeholder="Enter code"
              required
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            rows="3"
            value={formData.description || ''}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter description"
          />
        </div>
      </>
    );

    switch (type) {
      case 'license-editions':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Version</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.version || ''}
                  onChange={(e) => handleInputChange('version', e.target.value)}
                  placeholder="e.g., 6.6.3"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price (₹)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.price || ''}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Annual Maintenance Charge (₹)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.annual_maintenance_charge || ''}
                  onChange={(e) => handleInputChange('annual_maintenance_charge', e.target.value)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Companies</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.max_companies || ''}
                  onChange={(e) => handleInputChange('max_companies', e.target.value)}
                  placeholder="1"
                  min="1"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Max Users</label>
              <input
                type="number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={formData.max_users || ''}
                onChange={(e) => handleInputChange('max_users', e.target.value)}
                placeholder="1"
                min="1"
              />
            </div>
          </>
        );

      case 'designations':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.level || '1'}
                  onChange={(e) => handleInputChange('level', e.target.value)}
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.department || ''}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                  placeholder="e.g., Management, Sales"
                />
              </div>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  checked={formData.is_mandatory || false}
                  onChange={(e) => handleInputChange('is_mandatory', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-700">
                  Mandatory (At least one contact with this designation must be entered in Customer Address Book)
                </span>
              </label>
            </div>
          </>
        );

      case 'tally-products':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.category || 'software'}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <option value="software">Software</option>
                  <option value="hardware">Hardware</option>
                  <option value="service">Service</option>
                  <option value="addon">Add-on</option>
                  <option value="training">Training</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Version</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.version || ''}
                  onChange={(e) => handleInputChange('version', e.target.value)}
                  placeholder="e.g., 4.0"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price (₹)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.price || ''}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Cost Price (₹)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.cost_price || ''}
                  onChange={(e) => handleInputChange('cost_price', e.target.value)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">HSN Code</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.hsn_code || ''}
                  onChange={(e) => handleInputChange('hsn_code', e.target.value)}
                  placeholder="998361"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">GST Rate (%)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.gst_rate || '18'}
                  onChange={(e) => handleInputChange('gst_rate', e.target.value)}
                  placeholder="18"
                  min="0"
                  max="100"
                  step="0.01"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Unit</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.unit || 'Nos'}
                  onChange={(e) => handleInputChange('unit', e.target.value)}
                  placeholder="Nos"
                />
              </div>
            </div>
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  checked={formData.is_service || false}
                  onChange={(e) => handleInputChange('is_service', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-700">Is Service</span>
              </label>
            </div>
          </>
        );

      case 'staff-roles':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.department || 'support'}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                >
                  <option value="sales">Sales</option>
                  <option value="technical">Technical</option>
                  <option value="support">Support</option>
                  <option value="management">Management</option>
                  <option value="accounts">Accounts</option>
                  <option value="hr">HR</option>
                  <option value="marketing">Marketing</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.level || '1'}
                  onChange={(e) => handleInputChange('level', e.target.value)}
                >
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>
            </div>
          </>
        );

      case 'executives':
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Employee Code <span className="text-gray-400 text-xs">(Auto-generated if empty)</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.employee_code || ''}
                  onChange={(e) => handleInputChange('employee_code', e.target.value.toUpperCase())}
                  placeholder="Leave empty for auto-generation"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.department || 'support'}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                >
                  <option value="sales">Sales</option>
                  <option value="technical">Technical</option>
                  <option value="support">Support</option>
                  <option value="management">Management</option>
                  <option value="accounts">Accounts</option>
                  <option value="hr">HR</option>
                  <option value="marketing">Marketing</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.first_name || ''}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.last_name || ''}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  placeholder="Doe"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone <span className="text-red-500">*</span>
                </label>
                <input
                  type="tel"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+91 9876543210"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Alternate Phone</label>
                <input
                  type="tel"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.alternate_phone || ''}
                  onChange={(e) => handleInputChange('alternate_phone', e.target.value)}
                  placeholder="+91 9876543211"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Designation <span className="text-red-500">*</span>
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.designation_id || ''}
                  onChange={(e) => handleInputChange('designation_id', e.target.value)}
                  required
                >
                  <option value="">Select Designation</option>
                  {designations.map(designation => (
                    <option key={designation.id} value={designation.id}>
                      {designation.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.date_of_birth || ''}
                  onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date of Joining</label>
                <input
                  type="date"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.date_of_joining || ''}
                  onChange={(e) => handleInputChange('date_of_joining', e.target.value)}
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address <span className="text-red-500">*</span>
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                rows="3"
                value={formData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Complete address"
                required
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.city || ''}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Mumbai"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  State <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.state || ''}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  placeholder="Maharashtra"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Postal Code <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.postal_code || ''}
                  onChange={(e) => handleInputChange('postal_code', e.target.value)}
                  placeholder="400001"
                  required
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Salary (₹) <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.salary || ''}
                  onChange={(e) => handleInputChange('salary', e.target.value)}
                  placeholder="50000"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Commission Rate (%) <span className="text-red-500">*</span> <span className="text-gray-400 text-xs">(0-100)</span>
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.commission_rate || ''}
                  onChange={(e) => handleInputChange('commission_rate', e.target.value)}
                  placeholder="5.00"
                  min="0"
                  max="100"
                  step="0.01"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Amount (₹) <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.target_amount || ''}
                  onChange={(e) => handleInputChange('target_amount', e.target.value)}
                  placeholder="100000"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </>
        );

      case 'areas':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.city || ''}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Mumbai"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  State <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.state || ''}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  placeholder="Maharashtra"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={formData.country || 'India'}
                onChange={(e) => handleInputChange('country', e.target.value)}
                placeholder="India"
              />
            </div>
          </>
        );

      case 'nature-of-issues':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.category || 'technical'}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <option value="technical">Technical</option>
                  <option value="functional">Functional</option>
                  <option value="installation">Installation</option>
                  <option value="configuration">Configuration</option>
                  <option value="data">Data Related</option>
                  <option value="performance">Performance</option>
                  <option value="training">Training</option>
                  <option value="customization">Customization</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.priority || 'medium'}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Estimated Resolution Time (Hours)</label>
              <input
                type="number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={formData.estimated_resolution_time || ''}
                onChange={(e) => handleInputChange('estimated_resolution_time', e.target.value)}
                placeholder="2"
                min="0"
                step="0.5"
              />
            </div>
          </>
        );

      case 'additional-services':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.category || 'customization'}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <option value="customization">Customization</option>
                  <option value="training">Training</option>
                  <option value="data-migration">Data Migration</option>
                  <option value="integration">Integration</option>
                  <option value="consulting">Consulting</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="support">Support</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price (₹)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.price || ''}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="5000.00"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Duration (Hours)</label>
              <input
                type="number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={formData.duration_hours || ''}
                onChange={(e) => handleInputChange('duration_hours', e.target.value)}
                placeholder="8"
                min="0"
                step="0.5"
              />
            </div>
          </>
        );

      case 'call-statuses':
        return (
          <>
            {commonFields}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.category || 'open'}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  <option value="open">Open</option>
                  <option value="in_progress">In Progress</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Color</label>
                <input
                  type="color"
                  className="w-full h-10 px-1 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.color || '#3B82F6'}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    checked={formData.is_final || false}
                    onChange={(e) => handleInputChange('is_final', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-700">Is Final Status</span>
                </label>
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    checked={formData.requires_approval || false}
                    onChange={(e) => handleInputChange('requires_approval', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-700">Requires Approval</span>
                </label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Auto Close After (Days)</label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  value={formData.auto_close_after_days || ''}
                  onChange={(e) => handleInputChange('auto_close_after_days', e.target.value)}
                  placeholder="7"
                  min="0"
                />
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                    checked={formData.is_billable || false}
                    onChange={(e) => handleInputChange('is_billable', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-700">Is Billable</span>
                </label>
              </div>
            </div>
          </>
        );

      case 'industries':
        return (
          <>
            {commonFields}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={formData.category || ''}
                onChange={(e) => handleInputChange('category', e.target.value)}
                placeholder="e.g., Manufacturing, Service, Trading"
              />
            </div>
          </>
        );

      default:
        return commonFields;
    }
  };

  // Card View Component for Master Data
  const MasterCard = ({ item, type }) => (
    <Card className="hover:shadow-lg transition-all duration-200 border border-gray-200">
      <CardBody className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-blue-600 font-bold text-sm">
                {type === 'executives' ? '👤' :
                  type === 'industries' ? '🏭' :
                    type === 'areas' ? '📍' :
                      type === 'designations' ? '💼' :
                        type === 'tally-products' ? '📦' :
                          type === 'license-editions' ? '🔑' :
                            type === 'staff-roles' ? '👥' :
                              type === 'locations' ? '🌍' :
                                type === 'nature-of-issues' ? '🔧' :
                                  type === 'additional-services' ? '⚙️' :
                                    type === 'call-statuses' ? '📞' :
                                      type === 'employees' ? '👨‍💼' :
                                        type === 'customers' ? '🏢' :
                                          type === 'system' ? '⚙️' : '📄'}
              </span>
            </div>
            <div>
              <h6 className="font-bold text-gray-900 mb-1">
                {item.name || item.employee_code || item.setting || 'N/A'}
              </h6>
              <p className="text-sm text-gray-600 mb-0">
                {item.code || (item.first_name && item.last_name ? `${item.first_name} ${item.last_name}` : '') || item.category || 'N/A'}
              </p>
            </div>
          </div>
          <div className="relative">
            <button
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                toggleDropdown(item.id);
              }}
            >
              <FaEllipsisV className="h-4 w-4" />
            </button>
            {dropdownOpen[item.id] && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      handleView(item);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEye className="mr-3 h-4 w-4 text-blue-600" />
                    View Details
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      handleEdit(item);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEdit className="mr-3 h-4 w-4 text-blue-600" />
                    Edit
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    onClick={() => {
                      handleDelete(item.id, type);
                      setDropdownOpen({});
                    }}
                  >
                    <FaTrash className="mr-3 h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-3">
          {type === 'executives' && (
            <>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Phone:</span>
                <span className="text-sm font-medium">{item.phone || 'N/A'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Department:</span>
                <span className="text-sm font-medium">{item.department || 'N/A'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Designation:</span>
                <span className="text-sm font-medium">
                  {designations.find(d => d.id === item.designation_id)?.name || 'N/A'}
                </span>
              </div>
            </>
          )}

          {(type === 'industries' || type === 'areas' || type === 'designations' ||
            type === 'tally-products' || type === 'license-editions' || type === 'staff-roles' ||
            type === 'nature-of-issues' || type === 'additional-services' || type === 'call-statuses') && (
            <>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Description:</span>
                <span className="text-sm font-medium">{item.description || 'N/A'}</span>
              </div>
              {item.category && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Category:</span>
                  <span className="text-sm font-medium">{item.category}</span>
                </div>
              )}
            </>
          )}

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              (item.status === 'active' || item.is_active)
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
            >
              {(item.status === 'active' || item.is_active) ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </CardBody>
    </Card>
  );

  const renderMasterTable = (type) => {
    const data = getFilteredData(type);

    if (type === 'license-editions') {
      const columns = [
        {
          header: 'Name',
          key: 'name',
          width: '25%',
          priority: 'high',
          render: (item) => (
            <div className="min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate" title={item.name}>{item.name}</div>
              <div className="text-sm text-gray-500 truncate" title={item.code}>{item.code}</div>
            </div>
          )
        },
        {
          header: 'Description',
          key: 'description',
          width: '20%',
          priority: 'medium',
          hideOnSmallLaptop: true,
          render: (item) => (
            <div className="text-sm text-gray-900 truncate" title={item.description || '-'}>
              {item.description || '-'}
            </div>
          )
        },
        {
          header: 'Version',
          key: 'version',
          width: '10%',
          priority: 'low',
          hideOnLaptop: true,
          render: (item) => (
            <div className="text-sm text-gray-900">{item.version || '-'}</div>
          )
        },
        {
          header: 'Price',
          key: 'price',
          width: '15%',
          priority: 'high',
          render: (item) => (
            <div className="text-sm font-medium text-gray-900">₹{item.price?.toLocaleString() || '0'}</div>
          )
        },
        {
          header: 'Status',
          key: 'status',
          width: '12%',
          priority: 'high',
          render: (item) => getStatusBadge(item.status)
        },
        {
          header: 'Actions',
          key: 'actions',
          width: '18%',
          priority: 'high',
          truncate: false,
          render: (item) => (
            <div className="inline-flex rounded-md shadow-sm" role="group">
              <button
                className="inline-flex items-center px-2 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                title="View"
                onClick={() => handleView(item)}
              >
                <FaEye />
              </button>
              <button
                className="inline-flex items-center px-2 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                title="Edit"
                onClick={() => handleEdit(item)}
              >
                <FaEdit />
              </button>
              <button
                className="inline-flex items-center px-2 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                title="Delete"
                onClick={() => handleDelete(activeTab, item.id)}
              >
                <FaTrash />
              </button>
            </div>
          )
        }
      ];

      return (
        <ResponsiveTable
          columns={columns}
          data={data}
          laptopOptimized={true}
          compactMode={true}
          showTooltips={true}
          emptyMessage="No license editions found"
        />
      );
    }

    if (type === 'designations') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mandatory</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.description}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {item.department}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {item.isMandatory ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Required
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Optional
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'tally-products') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">
                    {item.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                  ₹{item.price ? parseFloat(item.price).toLocaleString() : '0'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'locations') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th>State</th>
              <th>City</th>
              <th>Pincode</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.map(item => (
              <tr key={item.id}>
                <td>{item.state}</td>
                <td>{item.city}</td>
                <td>{item.pincode}</td>
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500" title="View">
                      <FaEye />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500" title="Edit" onClick={() => handleEdit(item)}>
                      <FaEdit />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500" title="Delete" onClick={() => handleDelete(activeTab, item.id)}><FaTrash /></button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'staff-roles') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {item.department}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.level}</td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'executives') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.employee_code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.phone}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    {item.department}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'areas') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.city}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {item.state}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'industries') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.description}</td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'nature-of-issues') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    {item.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    item.priority === 'high' ? 'bg-red-100 text-red-800' :
                      item.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                  }`}
                  >
                    {item.priority}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'additional-services') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                    {item.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {item.price ? `₹${parseFloat(item.price).toLocaleString()}` : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'call-statuses') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map(item => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.name}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                    {item.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex items-center">
                    <div
                      className="w-4 h-4 rounded-full mr-2 border border-gray-300"
                      style={{ backgroundColor: item.color || '#6c757d' }}
                    >
                    </div>
                    <span className="text-xs">{item.color || '#6c757d'}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">{getStatusBadge(item.status)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500 rounded-l-md"
                      title="View"
                      onClick={() => handleView(item)}
                    >
                      <FaEye />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border-t border-b border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500"
                      title="Edit"
                      onClick={() => handleEdit(item)}
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="inline-flex items-center px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500 rounded-r-md"
                      title="Delete"
                      onClick={() => handleDelete(activeTab, item.id)}
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'employees') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th>Employee Name</th>
              <th>Role</th>
              <th>Department</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.map(item => (
              <tr key={item.id}>
                <td>{item.name}</td>
                <td>{item.role}</td>
                <td><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-600">{item.department}</span></td>
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500" title="View">
                      <FaEye />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500" title="Edit" onClick={() => handleEdit(item)}>
                      <FaEdit />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500" title="Delete" onClick={() => handleDelete(activeTab, item.id)}><FaTrash /></button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'customers') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th>Customer Type</th>
              <th>Description</th>
              <th>Min Revenue</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.map(item => (
              <tr key={item.id}>
                <td>{item.type}</td>
                <td>{item.description}</td>
                <td>₹{item.minRevenue.toLocaleString()}</td>
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500" title="View">
                      <FaEye />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500" title="Edit" onClick={() => handleEdit(item)}>
                      <FaEdit />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500" title="Delete" onClick={() => handleDelete(activeTab, item.id)}><FaTrash /></button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    if (type === 'system') {
      return (
        <table className="min-w-full divide-y divide-gray-200 hover:bg-gray-50">
          <thead className="bg-gray-50">
            <tr>
              <th>Setting</th>
              <th>Value</th>
              <th>Category</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.map(item => (
              <tr key={item.id}>
                <td>{item.setting}</td>
                <td><code>{item.value}</code></td>
                <td><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-600">{item.category}</span></td>
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <div className="inline-flex rounded-md shadow-sm" role="group">
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500" title="View">
                      <FaEye />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-gray-300 text-gray-400 cursor-not-allowed" title="Edit (Coming Soon)" disabled>
                      <FaEdit />
                    </button>
                    <button className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 px-3 py-1 text-sm border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500" title="Delete" onClick={() => handleDelete(activeTab, item.id)}><FaTrash /></button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      );
    }

    return <div>No data available</div>;
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Master Data..."
        subtitle="Please wait while we fetch system configurations"
        variant="page"
      />
    );
  }

  return (
    <div className="min-h-screen bg-orange-50">
      <div className="w-full">
        {/* Dynamic Theme Header */}
        <div className="mb-8">
          <div
            className="rounded-2xl shadow-xl p-6"
            style={{
              background: 'var(--sidebar-background, #1d5795)',
              color: 'var(--primary-text, #ffffff)'
            }}
          >
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div
                    className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center mr-3 sm:mr-4"
                    style={{
                      backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'
                    }}
                  >
                    <FaCog
                      className="text-lg sm:text-xl"
                      style={{ color: 'var(--primary-text, #ffffff)' }}
                    />
                  </div>
                  <span className="hidden sm:inline">Master Data Management</span>
                  <span className="sm:hidden">Master Data</span>
                </h2>
                <p
                  className="text-sm sm:text-base"
                  style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}
                >
                  <span className="hidden sm:inline">Manage system master data and configurations</span>
                  <span className="sm:hidden">Manage master data</span>
                </p>
              </div>
              <div className="flex flex-wrap gap-2 w-full sm:w-auto">
                <button
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border-2 border-opacity-30 text-xs sm:text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm flex-1 sm:flex-none justify-center"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                  onClick={() => handleExport(activeTab)}
                >
                  <FaDownload className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Export Data</span>
                  <span className="sm:hidden">Export</span>
                </button>
                <button
                  className="inline-flex items-center px-3 sm:px-6 py-2 sm:py-3 border-2 border-opacity-30 text-xs sm:text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm flex-1 sm:flex-none justify-center"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                  onClick={() => handleAddNew(activeTab)}
                >
                  <FaPlus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Add New</span>
                  <span className="sm:hidden">Add</span>
                </button>
              </div>
            </div>
          </div>
        </div>


        {/* Active Master Data */}
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
                  <h5 className="text-lg font-semibold text-gray-900 mb-0 flex items-center">
                    {masterCategories.find(cat => cat.id === activeTab)?.icon}
                    {masterCategories.find(cat => cat.id === activeTab)?.title}
                  </h5>
                  <div className="flex flex-wrap gap-2 w-full sm:w-auto">
                    <button
                      className="btn-theme-primary inline-flex items-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
                      style={{
                        backgroundColor: 'var(--primary-color)',
                        color: 'var(--primary-text)',
                        borderColor: 'var(--primary-color)'
                      }}
                      onClick={() => handleExport(activeTab)}
                    >
                      <FaDownload className="mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Export Excel</span>
                      <span className="sm:hidden">Export</span>
                    </button>
                    <button
                      className="btn-theme-primary inline-flex items-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
                      style={{
                        backgroundColor: 'var(--primary-color)',
                        color: 'var(--primary-text)',
                        borderColor: 'var(--primary-color)'
                      }}
                      onClick={() => handleImport(activeTab)}
                    >
                      <FaUpload className="mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Import Excel</span>
                      <span className="sm:hidden">Import</span>
                    </button>
                    <button
                      className="btn-theme-primary inline-flex items-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
                      style={{
                        backgroundColor: 'var(--primary-color)',
                        color: 'var(--primary-text)',
                        borderColor: 'var(--primary-color)'
                      }}
                      onClick={() => handleAddNew(activeTab)}
                    >
                      <FaPlus className="mr-1 sm:mr-2" />
                      <span className="hidden sm:inline">Add New</span>
                      <span className="sm:hidden">Add</span>
                    </button>
                  </div>
                </div>

                {/* Search and Filter Controls */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder={`Search ${masterCategories.find(cat => cat.id === activeTab)?.title}...`}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <div className="relative">
                      <select
                        className="appearance-none px-3 py-2 pr-8 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white cursor-pointer"
                        value={filterStatus}
                        onChange={(e) => setFilterStatus(e.target.value)}
                      >
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        </svg>
                      </div>
                    </div>

                    {/* View Mode Toggle - Hidden on mobile, only show card view */}
                    <div className="hidden sm:flex gap-1">
                      <button
                        className={`inline-flex items-center justify-center px-3 py-2 text-sm font-medium border rounded-md focus:outline-none focus:ring-2 transition-colors ${
                          viewMode === 'table'
                            ? 'btn-theme-primary'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        }`}
                        style={viewMode === 'table' ? {
                          backgroundColor: 'var(--primary-color)',
                          color: 'var(--primary-text)',
                          borderColor: 'var(--primary-color)'
                        } : {}}
                        onClick={() => setViewMode('table')}
                      >
                        <FaList className="h-4 w-4" />
                      </button>
                      <button
                        className={`inline-flex items-center justify-center px-3 py-2 text-sm font-medium border rounded-md focus:outline-none focus:ring-2 transition-colors ${
                          viewMode === 'card'
                            ? 'btn-theme-primary'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        }`}
                        style={viewMode === 'card' ? {
                          backgroundColor: 'var(--primary-color)',
                          color: 'var(--primary-text)',
                          borderColor: 'var(--primary-color)'
                        } : {}}
                        onClick={() => setViewMode('card')}
                      >
                        <FaTh className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Mobile - Force card view only */}
                    <div className="sm:hidden">
                      <div className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium bg-gray-100 text-gray-500 border border-gray-300 rounded-md cursor-not-allowed">
                        <FaTh className="h-4 w-4" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-4">
                {/* Conditional View Rendering */}
                {/* Table View - Hidden on mobile */}
                {viewMode === 'table' && (
                  <div className="hidden sm:block">
                    <div className="overflow-x-auto">
                      {renderMasterTable(activeTab)}
                    </div>
                  </div>
                )}

                {/* Card View - Always visible on mobile, toggleable on desktop */}
                {viewMode === 'card' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 3xl:grid-cols-6 gap-4">
                    {getFilteredData(activeTab).map(item => (
                      <MasterCard key={item.id} item={item} type={activeTab} />
                    ))}
                  </div>
                )}

                {/* Mobile Card View - Force card view on mobile */}
                <div className="sm:hidden">
                  <div className="grid grid-cols-1 gap-4">
                    {getFilteredData(activeTab).map(item => (
                      <MasterCard key={item.id} item={item} type={activeTab} />
                    ))}
                  </div>
                </div>

                {/* Empty State */}
                {getFilteredData(activeTab).length === 0 && (
                  <div className="text-center py-12">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                      <FaCog className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No data found</h3>
                    <p className="text-gray-600 mb-4">No {masterCategories.find(cat => cat.id === activeTab)?.title.toLowerCase()} found matching your criteria.</p>
                    <button
                      onClick={() => handleAddNew(activeTab)}
                      className="btn-theme-primary inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md"
                      style={{
                        backgroundColor: 'var(--primary-color)',
                        color: 'var(--primary-text)',
                        borderColor: 'var(--primary-color)'
                      }}
                    >
                      <FaPlus className="mr-2 h-4 w-4" />
                      Add New {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)}
                    </button>
                  </div>
                )}

                {/* Debug Pagination Info - Remove in production */}
                {pagination[activeTab] && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4 text-sm">
                    <strong>Debug Pagination Info for {activeTab}:</strong> Total Items: {pagination[activeTab].totalItems}, Total Pages: {pagination[activeTab].totalPages}, Current Page: {pagination[activeTab].currentPage}, Items Per Page: {pagination[activeTab].itemsPerPage}
                  </div>
                )}

                {/* Pagination Controls */}
                {pagination[activeTab] && pagination[activeTab].totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6 px-4 py-3 bg-white border-t border-gray-200">
                    <div className="flex items-center text-sm text-gray-700">
                      <span>
                        Showing {((pagination[activeTab].currentPage - 1) * pagination[activeTab].itemsPerPage) + 1} to{' '}
                        {Math.min(pagination[activeTab].currentPage * pagination[activeTab].itemsPerPage, pagination[activeTab].totalItems)} of{' '}
                        {pagination[activeTab].totalItems} results
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(activeTab, pagination[activeTab].currentPage - 1)}
                        disabled={pagination[activeTab].currentPage === 1}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>

                      {/* Page Numbers */}
                      {[...Array(Math.min(5, pagination[activeTab].totalPages))].map((_, index) => {
                        const pageNumber = Math.max(1, pagination[activeTab].currentPage - 2) + index;
                        if (pageNumber <= pagination[activeTab].totalPages) {
                          return (
                            <button
                              key={pageNumber}
                              onClick={() => handlePageChange(activeTab, pageNumber)}
                              className={`px-3 py-2 text-sm font-medium rounded-lg ${
                                pagination[activeTab].currentPage === pageNumber
                                  ? 'text-white'
                                  : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                              }`}
                              style={pagination[activeTab].currentPage === pageNumber ? {
                                backgroundColor: 'var(--primary-color)',
                                borderColor: 'var(--primary-color)'
                              } : {}}
                            >
                              {pageNumber}
                            </button>
                          );
                        }
                        return null;
                      })}

                      <button
                        onClick={() => handlePageChange(activeTab, pagination[activeTab].currentPage + 1)}
                        disabled={pagination[activeTab].currentPage === pagination[activeTab].totalPages}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Add New Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-modal">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mx-auto">
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Add New {masterCategories.find(cat => cat.id === activeTab)?.title}
                </h3>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddFormData({});
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="p-6">
                <form className="space-y-6">
                  {renderFormFields(activeTab, addFormData, setAddFormData)}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={addFormData.status || 'active'}
                      onChange={(e) => setAddFormData({ ...addFormData, status: e.target.value })}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </form>
              </div>

              <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
                <button
                  type="button"
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={() => {
                    setShowAddModal(false);
                    setAddFormData({});
                  }}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-theme-primary px-4 py-2 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary-color)',
                    color: 'var(--primary-text)',
                    borderColor: 'var(--primary-color)'
                  }}
                  onClick={handleAddSubmit}
                >
                  Add {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Modal */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-modal">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto mx-auto">
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Edit {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)}
                </h3>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="p-6">
                <form className="space-y-6">
                  {renderFormFields(activeTab, editFormData, setEditFormData, true)}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      value={editFormData.status || 'active'}
                      onChange={(e) => setEditFormData({ ...editFormData, status: e.target.value })}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>
                </form>
              </div>

              <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
                <button
                  type="button"
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={() => setShowEditModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-theme-primary px-4 py-2 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary-color)',
                    color: 'var(--primary-text)',
                    borderColor: 'var(--primary-color)'
                  }}
                  onClick={handleEditSubmit}
                >
                  Update {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* View Modal */}
        {showViewModal && viewingItem && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4"
            onClick={() => setShowViewModal(false)}
          >
            <div
              className="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-1/2 max-h-[90vh] overflow-y-auto mx-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  View {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)} Details
                </h3>
                <button
                  type="button"
                  className="text-gray-400 hover:text-gray-600"
                  onClick={() => setShowViewModal(false)}
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="p-6 space-y-4">
                {activeTab === 'executives' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Employee Code</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.employee_code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Full Name</label>
                        <p className="mt-1 text-sm text-gray-900">{`${viewingItem.first_name || ''} ${viewingItem.last_name || ''}`.trim() || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Email</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.email || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Phone</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.phone || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Alternate Phone</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.alternate_phone || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Department</label>
                        <p className="mt-1 text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {viewingItem.department || 'N/A'}
                          </span>
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Designation</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {designations.find(d => d.id === viewingItem.designation_id)?.name || 'N/A'}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.date_of_birth ? new Date(viewingItem.date_of_birth).toLocaleDateString() : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Date of Joining</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.date_of_joining ? new Date(viewingItem.date_of_joining).toLocaleDateString() : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Address</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.address || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">City</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.city || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">State</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.state || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Postal Code</label>
                        <p className="mt-1 text-sm text-gray-900">{viewingItem.postal_code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Salary</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.salary ? `₹${parseFloat(viewingItem.salary).toLocaleString()}` : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Commission Rate</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.commission_rate ? `${viewingItem.commission_rate}%` : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Target Amount</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.target_amount ? `₹${parseFloat(viewingItem.target_amount).toLocaleString()}` : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Status</label>
                        <p className="mt-1 text-sm text-gray-900">
                          {viewingItem.is_active ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Active
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Inactive
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* License Editions View */}
                {activeTab === 'license-editions' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Price:</label>
                        <p className="text-gray-900">{viewingItem.price ? `₹${viewingItem.price}` : 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Features:</label>
                        <p className="text-gray-900">{viewingItem.features || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Designations View */}
                {activeTab === 'designations' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Is Mandatory:</label>
                        <p className="text-gray-900">{viewingItem.is_mandatory ? 'Yes' : 'No'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Department:</label>
                        <p className="text-gray-900">{viewingItem.department || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Tally Products View */}
                {activeTab === 'tally-products' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Version:</label>
                        <p className="text-gray-900">{viewingItem.version || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Category:</label>
                        <p className="text-gray-900">{viewingItem.category || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Staff Roles View */}
                {activeTab === 'staff-roles' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Department:</label>
                        <p className="text-gray-900">{viewingItem.department || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Level:</label>
                        <p className="text-gray-900">{viewingItem.level || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Industries View */}
                {activeTab === 'industries' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Category:</label>
                        <p className="text-gray-900">{viewingItem.category || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Sector:</label>
                        <p className="text-gray-900">{viewingItem.sector || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Areas View */}
                {activeTab === 'areas' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">City:</label>
                        <p className="text-gray-900">{viewingItem.city || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">State:</label>
                        <p className="text-gray-900">{viewingItem.state || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Nature of Issues View */}
                {activeTab === 'nature-of-issues' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Category:</label>
                        <p className="text-gray-900">{viewingItem.category || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Priority:</label>
                        <p className="text-gray-900">{viewingItem.priority || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Additional Services View */}
                {activeTab === 'additional-services' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Price:</label>
                        <p className="text-gray-900">{viewingItem.price ? `₹${viewingItem.price}` : 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Duration:</label>
                        <p className="text-gray-900">{viewingItem.duration || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Call Statuses View */}
                {activeTab === 'call-statuses' && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Name:</label>
                        <p className="text-gray-900">{viewingItem.name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Code:</label>
                        <p className="text-gray-900">{viewingItem.code || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Description:</label>
                        <p className="text-gray-900">{viewingItem.description || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Status:</label>
                        <p className="text-gray-900">{viewingItem.status || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Color:</label>
                        <p className="text-gray-900">{viewingItem.color || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Order:</label>
                        <p className="text-gray-900">{viewingItem.order || 'N/A'}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
                <button
                  type="button"
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={() => setShowViewModal(false)}
                >
                  Close
                </button>
                <button
                  type="button"
                  className="btn-theme-primary px-4 py-2 text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
                  style={{
                    backgroundColor: 'var(--primary-color)',
                    color: 'var(--primary-text)',
                    borderColor: 'var(--primary-color)'
                  }}
                  onClick={() => {
                    setShowViewModal(false);
                    handleEdit(viewingItem);
                  }}
                >
                  Edit {masterCategories.find(cat => cat.id === activeTab)?.title.slice(0, -1)}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MastersList;
