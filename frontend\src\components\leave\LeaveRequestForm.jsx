import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Spin<PERSON> } from '../ui';
import { Calendar, AlertCircle, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';

const LeaveRequestForm = ({ leaveTypes, leaveBalance, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    leave_type_id: '',
    start_date: '',
    end_date: '',
    is_half_day: false,
    half_day_period: 'first_half',
    reason: '',
    emergency_contact: '',
    work_handover_to: '',
    handover_notes: '',
    priority: 'normal'
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [totalDays, setTotalDays] = useState(0);
  const [availableBalance, setAvailableBalance] = useState(0);

  useEffect(() => {
    calculateTotalDays();
  }, [formData.start_date, formData.end_date, formData.is_half_day]);

  useEffect(() => {
    updateAvailableBalance();
  }, [formData.leave_type_id, leaveBalance]);

  const calculateTotalDays = () => {
    if (!formData.start_date || !formData.end_date) {
      setTotalDays(0);
      return;
    }

    const startDate = new Date(formData.start_date);
    const endDate = new Date(formData.end_date);
    
    if (endDate < startDate) {
      setTotalDays(0);
      return;
    }

    let days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    if (formData.is_half_day) {
      days = 0.5;
    }

    setTotalDays(days);
  };

  const updateAvailableBalance = () => {
    if (!formData.leave_type_id) {
      setAvailableBalance(0);
      return;
    }

    const balance = leaveBalance.find(b => b.leave_type_id === formData.leave_type_id);
    setAvailableBalance(balance ? balance.available_days : 0);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.leave_type_id) {
      newErrors.leave_type_id = 'Please select a leave type';
    }

    if (!formData.start_date) {
      newErrors.start_date = 'Please select start date';
    }

    if (!formData.end_date) {
      newErrors.end_date = 'Please select end date';
    }

    if (formData.start_date && formData.end_date) {
      const startDate = new Date(formData.start_date);
      const endDate = new Date(formData.end_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (startDate < today) {
        newErrors.start_date = 'Start date cannot be in the past';
      }

      if (endDate < startDate) {
        newErrors.end_date = 'End date cannot be before start date';
      }
    }

    if (!formData.reason || formData.reason.trim().length < 10) {
      newErrors.reason = 'Please provide a reason (minimum 10 characters)';
    }

    if (totalDays > availableBalance) {
      newErrors.balance = `Insufficient leave balance. Available: ${availableBalance} days, Requested: ${totalDays} days`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit({
        ...formData,
        total_days: totalDays
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const selectedLeaveType = leaveTypes.find(type => type.id === formData.leave_type_id);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Leave Type Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Leave Type *
        </label>
        <select
          value={formData.leave_type_id}
          onChange={(e) => handleInputChange('leave_type_id', e.target.value)}
          className={`w-full border rounded-md px-3 py-2 ${
            errors.leave_type_id ? 'border-red-300' : 'border-gray-300'
          }`}
        >
          <option value="">Select leave type</option>
          {leaveTypes.map((type) => (
            <option key={type.id} value={type.id}>
              {type.name} ({type.code})
            </option>
          ))}
        </select>
        {errors.leave_type_id && (
          <p className="mt-1 text-sm text-red-600">{errors.leave_type_id}</p>
        )}
      </div>

      {/* Available Balance */}
      {formData.leave_type_id && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-blue-600 mr-2" />
            <span className="text-sm text-blue-800">
              Available Balance: <strong>{availableBalance} days</strong>
            </span>
          </div>
        </div>
      )}

      {/* Half Day Option */}
      {selectedLeaveType?.allow_half_day && (
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_half_day}
              onChange={(e) => handleInputChange('is_half_day', e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">Half Day Leave</span>
          </label>
          
          {formData.is_half_day && (
            <div className="mt-2">
              <select
                value={formData.half_day_period}
                onChange={(e) => handleInputChange('half_day_period', e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="first_half">First Half</option>
                <option value="second_half">Second Half</option>
              </select>
            </div>
          )}
        </div>
      )}

      {/* Date Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Date *
          </label>
          <input
            type="date"
            value={formData.start_date}
            onChange={(e) => handleInputChange('start_date', e.target.value)}
            min={new Date().toISOString().split('T')[0]}
            disabled={formData.is_half_day}
            className={`w-full border rounded-md px-3 py-2 ${
              errors.start_date ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.start_date && (
            <p className="mt-1 text-sm text-red-600">{errors.start_date}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            End Date *
          </label>
          <input
            type="date"
            value={formData.is_half_day ? formData.start_date : formData.end_date}
            onChange={(e) => handleInputChange('end_date', e.target.value)}
            min={formData.start_date || new Date().toISOString().split('T')[0]}
            disabled={formData.is_half_day}
            className={`w-full border rounded-md px-3 py-2 ${
              errors.end_date ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.end_date && (
            <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>
          )}
        </div>
      </div>

      {/* Total Days Display */}
      {totalDays > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Total Leave Days:</span>
            <span className="text-lg font-semibold text-blue-600">{totalDays} days</span>
          </div>
        </div>
      )}

      {/* Balance Error */}
      {errors.balance && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-sm text-red-800">{errors.balance}</span>
          </div>
        </div>
      )}

      {/* Reason */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Reason *
        </label>
        <textarea
          value={formData.reason}
          onChange={(e) => handleInputChange('reason', e.target.value)}
          rows={3}
          placeholder="Please provide a detailed reason for your leave request..."
          className={`w-full border rounded-md px-3 py-2 ${
            errors.reason ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.reason && (
          <p className="mt-1 text-sm text-red-600">{errors.reason}</p>
        )}
        <p className="mt-1 text-sm text-gray-500">
          {formData.reason.length}/1000 characters
        </p>
      </div>

      {/* Emergency Contact */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Emergency Contact
        </label>
        <input
          type="text"
          value={formData.emergency_contact}
          onChange={(e) => handleInputChange('emergency_contact', e.target.value)}
          placeholder="Emergency contact number during leave"
          className="w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      {/* Priority */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Priority
        </label>
        <select
          value={formData.priority}
          onChange={(e) => handleInputChange('priority', e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2"
        >
          <option value="low">Low</option>
          <option value="normal">Normal</option>
          <option value="high">High</option>
          <option value="urgent">Urgent</option>
        </select>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading || totalDays === 0}
          className="flex items-center"
        >
          {loading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Submitting...
            </>
          ) : (
            <>
              <FileText className="mr-2 h-4 w-4" />
              Submit Request
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

export default LeaveRequestForm;
