import { notificationService } from '../services/NotificationService.js';
import { logger } from '../utils/logger.js';
import models from '../models/index.js';
import { Op } from 'sequelize';

/**
 * Get notification settings for the current tenant
 */
export const getNotificationSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const settings = await notificationService.getNotificationSettings(tenantId);

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    logger.error('Error getting notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification settings',
      error: error.message
    });
  }
};

/**
 * Update notification settings for the current tenant
 */
export const updateNotificationSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const settingsData = req.body;

    // Validate settings data
    const allowedFields = [
      'service_created',
      'service_started', 
      'service_completed',
      'service_cancelled',
      'service_on_hold',
      'service_pending',
      'service_follow_up',
      'service_onsite',
      'service_on_process',
      'email_enabled',
      'sms_enabled',
      'push_enabled',
      'notification_delay_minutes',
      'business_hours_only',
      'business_hours_start',
      'business_hours_end',
      'email_templates'
    ];

    // Filter out invalid fields
    const validSettings = {};
    Object.keys(settingsData).forEach(key => {
      if (allowedFields.includes(key)) {
        validSettings[key] = settingsData[key];
      }
    });

    const settings = await notificationService.updateNotificationSettings(tenantId, validSettings);

    res.json({
      success: true,
      data: settings,
      message: 'Notification settings updated successfully'
    });
  } catch (error) {
    logger.error('Error updating notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update notification settings',
      error: error.message
    });
  }
};

/**
 * Test notification system by sending a test notification
 */
export const testNotification = async (req, res) => {
  try {
    const { eventType = 'service_created', customerEmail } = req.body;
    
    if (!customerEmail) {
      return res.status(400).json({
        success: false,
        message: 'Customer email is required for testing'
      });
    }

    // Mock service and customer data for testing
    const mockServiceData = {
      id: 'TEST-001',
      serviceNumber: 'TEST-001',
      service_number: 'TEST-001',
      service_type: 'onsite',
      status: { name: 'Test Status' },
      scheduled_date: new Date().toISOString().split('T')[0],
      description: 'Test notification service',
      tenant_id: req.user.tenant.id
    };

    const mockCustomerData = {
      id: 'test-customer',
      name: 'Test Customer',
      company_name: 'Test Company',
      email: customerEmail
    };

    // Send test notification
    const result = await notificationService.sendServiceNotification(
      mockServiceData,
      mockCustomerData,
      eventType
    );

    res.json({
      success: true,
      data: result,
      message: 'Test notification sent successfully'
    });
  } catch (error) {
    logger.error('Error sending test notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test notification',
      error: error.message
    });
  }
};

/**
 * Get available notification event types
 */
export const getNotificationEventTypes = async (req, res) => {
  try {
    const eventTypes = [
      {
        key: 'service_created',
        label: 'Service Created',
        description: 'When a new service request is created'
      },
      {
        key: 'service_started',
        label: 'Service Started',
        description: 'When service work begins (In Progress status)'
      },
      {
        key: 'service_completed',
        label: 'Service Completed',
        description: 'When service is marked as completed'
      },
      {
        key: 'service_cancelled',
        label: 'Service Cancelled',
        description: 'When service is cancelled'
      },
      {
        key: 'service_on_hold',
        label: 'Service On Hold',
        description: 'When service is put on hold'
      },
      {
        key: 'service_pending',
        label: 'Service Pending',
        description: 'When service is pending'
      },
      {
        key: 'service_follow_up',
        label: 'Service Follow Up',
        description: 'When service requires follow up'
      },
      {
        key: 'service_onsite',
        label: 'Service Onsite',
        description: 'When technician is onsite'
      },
      {
        key: 'service_on_process',
        label: 'Service On Process',
        description: 'When service is being processed'
      }
    ];

    res.json({
      success: true,
      data: eventTypes
    });
  } catch (error) {
    logger.error('Error getting notification event types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification event types',
      error: error.message
    });
  }
};

/**
 * Get all notification templates
 */
export const getNotificationTemplates = async (req, res) => {
  try {
    const { page = 1, limit = 10, type, channel, is_active } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {
      tenant_id: req.user.tenant.id,
    };

    if (type) whereClause.type = type;
    if (channel) whereClause.channel = channel;
    if (is_active !== undefined) whereClause.is_active = is_active === 'true';

    const { count, rows: templates } = await models.NotificationTemplate.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
      order: [['type', 'ASC'], ['channel', 'ASC'], ['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    res.json({
      success: true,
      data: {
        templates,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get notification templates error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification templates',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get notification template by ID
 */
export const getNotificationTemplateById = async (req, res) => {
  try {
    const { id } = req.params;

    const template = await models.NotificationTemplate.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
    });

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Notification template not found',
      });
    }

    res.json({
      success: true,
      data: { template },
    });

  } catch (error) {
    logger.error('Get notification template by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification template',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create notification template
 */
export const createNotificationTemplate = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const templateData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // If this is being set as default, unset other defaults for same type/channel
    if (templateData.is_default) {
      await models.NotificationTemplate.update(
        { is_default: false },
        {
          where: {
            tenant_id: req.user.tenant.id,
            type: templateData.type,
            channel: templateData.channel,
            is_default: true,
          },
          transaction,
        }
      );
    }

    const template = await models.NotificationTemplate.create(templateData, { transaction });

    await transaction.commit();

    // Fetch the created template with associations
    const createdTemplate = await models.NotificationTemplate.findByPk(template.id, {
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
    });

    logger.info('Notification template created successfully:', {
      id: template.id,
      type: templateData.type,
      channel: templateData.channel,
      createdBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: 'Notification template created successfully',
      data: { template: createdTemplate },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Create notification template error:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const errors = {};
      error.errors.forEach(err => {
        errors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create notification template',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get available template variables for a specific type
 */
export const getTemplateVariables = async (req, res) => {
  try {
    const { type } = req.params;

    const variables = models.NotificationTemplate.getAvailableVariables(type);

    res.json({
      success: true,
      data: { variables },
    });

  } catch (error) {
    logger.error('Get template variables error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch template variables',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
