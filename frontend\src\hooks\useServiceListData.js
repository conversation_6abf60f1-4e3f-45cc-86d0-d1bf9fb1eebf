import { useState, useEffect, useRef, useCallback } from 'react';
import { apiService, masterDataAPI } from '../services/api';

/**
 * Consolidated hook for ServiceList data management
 * Reduces duplicate API calls by batching requests and implementing smart caching
 */
export const useServiceListData = () => {
  const [services, setServices] = useState([]);
  const [serviceStats, setServiceStats] = useState({
    totalCalls: 0,
    recentCalls: 0,
    callsByStatus: [],
    callsByType: {
      freeCalls: 0,
      amcCalls: 0,
      paidCalls: 0
    },
    monthlyGrowth: {
      total: null, // null indicates no data available
      inProgress: null,
      scheduled: null,
      completed: null,
      freeCalls: null,
      amcCalls: null,
      paidCalls: null,
      recentCalls: null
    },
    metadata: {
      lastUpdated: null,
      hasError: false,
      errorMessage: null
    }
  });
  const [filterOptions, setFilterOptions] = useState({
    statuses: [],
    typeOfCalls: []
  });
  const [callStatuses, setCallStatuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [totalServices, setTotalServices] = useState(0);

  // Request deduplication and caching
  const requestCache = useRef(new Map());
  const lastFetchTime = useRef(0);
  const dataCache = useRef(new Map());

  // Helper function to safely process stats data
  const processStatsData = (statsData) => {
    if (!statsData || typeof statsData !== 'object') {
      console.warn('Invalid stats data received:', statsData);
      return {
        totalCalls: 0,
        recentCalls: 0,
        callsByStatus: [],
        callsByType: {
          freeCalls: 0,
          amcCalls: 0,
          paidCalls: 0
        },
        monthlyGrowth: {
          total: null,
          inProgress: null,
          scheduled: null,
          completed: null,
          freeCalls: null,
          amcCalls: null,
          paidCalls: null,
          recentCalls: null
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          hasError: true,
          errorMessage: 'Invalid data format received from server'
        }
      };
    }

    // Check if this is data from service analytics endpoint (has summary property)
    const isServiceAnalyticsData = statsData.summary && typeof statsData.summary === 'object';

    let totalCalls, recentCalls, callsByType;

    if (isServiceAnalyticsData) {
      // Process service analytics data structure
      totalCalls = Number(statsData.summary.totalCalls) || 0;
      recentCalls = Number(statsData.summary.totalCalls) || 0; // Use total calls as recent calls for now

      // Map service analytics data to expected format
      callsByType = {
        freeCalls: Number(statsData.summary.freeCalls) || 0,
        amcCalls: Number(statsData.summary.amcCalls) || 0,
        paidCalls: Number(statsData.summary.paidCalls) || 0
      };

      console.log('📊 Processing service analytics data:', {
        totalCalls,
        freeCalls: callsByType.freeCalls,
        amcCalls: callsByType.amcCalls,
        paidCalls: callsByType.paidCalls,
        rawSummary: statsData.summary
      });

      console.log('🔍 CRITICAL DEBUG - Service Analytics Processing:');
      console.log('- Input statsData exists:', !!statsData);
      console.log('- Input statsData.summary exists:', !!statsData.summary);
      console.log('- Extracted freeCalls:', statsData.summary?.freeCalls);
      console.log('- Extracted amcCalls:', statsData.summary?.amcCalls);
      console.log('- Extracted paidCalls:', statsData.summary?.paidCalls);
      console.log('- Final callsByType:', callsByType);


    } else {
      // Process legacy service-calls/stats data structure
      totalCalls = Number(statsData.totalCalls) || 0;
      recentCalls = Number(statsData.recentCalls) || 0;

      callsByType = {
        freeCalls: Number(statsData.callsByType?.freeCalls) || 0,
        amcCalls: Number(statsData.callsByType?.amcCalls) || 0,
        paidCalls: Number(statsData.callsByType?.paidCalls) || 0
      };
    }

    // Process callsByStatus with validation (fallback to empty array for service analytics)
    const callsByStatus = Array.isArray(statsData.callsByStatus)
      ? statsData.callsByStatus.map(item => ({
          status: String(item.status || 'Unknown'),
          category: String(item.category || 'unknown'),
          color: String(item.color || '#6b7280'),
          count: Number(item.count) || 0
        }))
      : [];

    // Process monthlyGrowth with null handling for missing data
    const monthlyGrowth = {
      total: statsData.monthlyGrowth?.total !== undefined ? Number(statsData.monthlyGrowth.total) : null,
      inProgress: statsData.monthlyGrowth?.inProgress !== undefined ? Number(statsData.monthlyGrowth.inProgress) : null,
      scheduled: statsData.monthlyGrowth?.scheduled !== undefined ? Number(statsData.monthlyGrowth.scheduled) : null,
      completed: statsData.monthlyGrowth?.completed !== undefined ? Number(statsData.monthlyGrowth.completed) : null,
      freeCalls: statsData.monthlyGrowth?.freeCalls !== undefined ? Number(statsData.monthlyGrowth.freeCalls) : null,
      amcCalls: statsData.monthlyGrowth?.amcCalls !== undefined ? Number(statsData.monthlyGrowth.amcCalls) : null,
      paidCalls: statsData.monthlyGrowth?.paidCalls !== undefined ? Number(statsData.monthlyGrowth.paidCalls) : null,
      recentCalls: statsData.monthlyGrowth?.recentCalls !== undefined ? Number(statsData.monthlyGrowth.recentCalls) : null
    };

    return {
      totalCalls,
      recentCalls,
      callsByStatus,
      callsByType,
      monthlyGrowth,
      metadata: {
        lastUpdated: new Date().toISOString(),
        hasError: false,
        errorMessage: null,
        serverMetadata: statsData.metadata || null,
        dataSource: isServiceAnalyticsData ? 'service-analytics' : 'service-calls-stats'
      }
    };
  };

  // Consolidated data fetching function with search optimization
  const fetchAllData = useCallback(async (params = {}) => {
    const {
      page = 1,
      limit = 10,
      searchTerm = '',
      filterStatus = 'all',
      filterType = 'all',
      forceRefresh = false
    } = params;

    // Search performance optimization: validate search term
    const trimmedSearchTerm = searchTerm.trim();
    const isValidSearch = trimmedSearchTerm.length === 0 || trimmedSearchTerm.length >= 2;

    if (!isValidSearch) {
      console.log('🔍 Search term too short, skipping search');
      return;
    }

    // Create cache key for request deduplication
    const cacheKey = JSON.stringify({ page, limit, searchTerm: trimmedSearchTerm, filterStatus, filterType });

    // Check cache first (unless force refresh)
    if (!forceRefresh && dataCache.current.has(cacheKey)) {
      const cachedData = dataCache.current.get(cacheKey);
      const cacheAge = Date.now() - cachedData.timestamp;

      // Use cache if less than 30 seconds old (shorter for search results)
      const cacheTimeout = trimmedSearchTerm ? 15000 : 30000;
      if (cacheAge < cacheTimeout) {
        console.log('📦 Using cached data for ServiceList');
        setServices(cachedData.services);
        setTotalPages(cachedData.totalPages);
        setTotalServices(cachedData.totalServices);
        return;
      }
    }

    // Prevent duplicate requests
    if (requestCache.current.has(cacheKey)) {
      console.log('🔄 Request already in progress, waiting...');
      return requestCache.current.get(cacheKey);
    }

    // Throttle requests - more aggressive throttling for search
    const now = Date.now();
    const throttleTime = trimmedSearchTerm ? 500 : 1000; // Faster for search
    if (now - lastFetchTime.current < throttleTime) {
      console.log('⏱️ Throttling API requests');
      return;
    }
    lastFetchTime.current = now;

    try {
      setLoading(true);

      // Create consolidated request promise
      const requestPromise = (async () => {
        // Try to use batch API endpoint if available
        try {
          console.log('🔄 Attempting Batch API call...');
          const response = await apiService.get('/service-calls/batch-data', {
            params: {
              page,
              limit,
              ...(trimmedSearchTerm && { search: trimmedSearchTerm }),
              ...(filterStatus !== 'all' && { status: filterStatus }),
              ...(filterType !== 'all' && { serviceType: filterType }),
              includeStats: true,
              includeFilters: true
            }
          });

          if (response.data?.success) {
            console.log('✅ Batch API successful, using batch data');
            console.log('🔍 Batch API stats:', response.data.data.stats);
            console.log('🔍 Batch API callsByType:', response.data.data.stats?.callsByType);
            return {
              services: response.data.data.serviceCalls || [],
              pagination: response.data.data.pagination || {},
              stats: response.data.data.stats || {},
              filters: response.data.data.filters || {}
            };
          }
        } catch (batchError) {
          console.log('📡 Batch API failed:', batchError.message);
          console.log('📡 Falling back to individual requests including service analytics API');
        }

        // Fallback to individual requests with enhanced error handling
        const [servicesResponse, statsResponse, filtersResponse] = await Promise.allSettled([
          apiService.get('/service-calls', {
            params: {
              page,
              limit,
              ...(trimmedSearchTerm && { search: trimmedSearchTerm }),
              ...(filterStatus !== 'all' && { status: filterStatus }),
              ...(filterType !== 'all' && { callBillingType: filterType }),
            }
          }),
          // Try service analytics API first, fallback to legacy stats API
          (async () => {
            try {
              console.log('🔄 Attempting Service Analytics API call...');
              console.log('🔍 Auth token present:', !!localStorage.getItem('tallycrm_token'));

              const analyticsResponse = await apiService.get('/reports/service-analytics', {
                params: {
                  dateRange: 30,
                  groupBy: 'day'
                }
              });
              console.log('✅ Service Analytics API Response:', analyticsResponse.data);
              console.log('✅ Service Analytics API successful:', {
                hasData: !!analyticsResponse.data,
                hasSummary: !!analyticsResponse.data?.data?.summary,
                summaryKeys: analyticsResponse.data?.data?.summary ? Object.keys(analyticsResponse.data.data.summary) : []
              });

              // Transform service analytics response to match expected structure
              // Service analytics API returns data directly under 'data', but code expects 'data.data'
              const transformedResponse = {
                ...analyticsResponse,
                data: {
                  data: analyticsResponse.data // Move data to data.data to match expected structure
                }
              };

              return transformedResponse;
            } catch (analyticsError) {
              console.error('❌ Service Analytics API failed:', {
                message: analyticsError.message,
                status: analyticsError.response?.status,
                statusText: analyticsError.response?.statusText,
                url: analyticsError.config?.url,
                method: analyticsError.config?.method,
                headers: analyticsError.config?.headers
              });
              console.warn('⚠️ Falling back to legacy stats API...');
              try {
                console.log('🔄 Attempting Legacy Stats API call...');
                const legacyResponse = await apiService.get('/service-calls/stats');
                console.log('✅ Legacy Stats API successful:', {
                  hasData: !!legacyResponse.data,
                  hasCallsByType: !!legacyResponse.data?.data?.callsByType,
                  callsByType: legacyResponse.data?.data?.callsByType
                });
                console.log('🔍 Legacy API Full Response:', legacyResponse.data);
                return legacyResponse;
              } catch (legacyError) {
                console.error('❌ Both Analytics and Legacy Stats APIs failed:', {
                  analyticsError: analyticsError.message,
                  legacyError: legacyError.message
                });
                throw legacyError;
              }
            }
          })(),
          apiService.get('/service-calls/filter-options')
        ]);

        return {
          services: servicesResponse.status === 'fulfilled' ?
            (servicesResponse.value.data?.data?.serviceCalls || []) : [],
          pagination: servicesResponse.status === 'fulfilled' ?
            (servicesResponse.value.data?.data?.pagination || {}) : {},
          stats: statsResponse.status === 'fulfilled' ?
            (statsResponse.value.data?.data || {}) : {},
          filters: filtersResponse.status === 'fulfilled' ?
            (filtersResponse.value.data?.data || {}) : {}
        };
      })();

      // Cache the request
      requestCache.current.set(cacheKey, requestPromise);

      const result = await requestPromise;

      // Debug: Log the stats response structure (remove in production)
      console.log('🔍 Stats Response Debug:', {
        hasStats: !!result.stats,
        statsKeys: result.stats ? Object.keys(result.stats) : [],
        hasSummary: !!result.stats?.summary,
        summaryData: result.stats?.summary,
        fullStats: result.stats
      });



      // Transform services data
      const transformedServices = result.services.map(service => {
        // Calculate if service is overdue
        const today = new Date();
        today.setHours(23, 59, 59, 999);

        let scheduledDate = null;
        if (service.scheduled_date) {
          scheduledDate = new Date(service.scheduled_date);
          if (!isNaN(scheduledDate.getTime())) {
            scheduledDate.setHours(23, 59, 59, 999);
          } else {
            scheduledDate = null;
          }
        }

        const isOverdue = scheduledDate &&
          scheduledDate < today &&
          (service.status?.category === 'open' ||
           service.status?.name?.toLowerCase().includes('open') ||
           service.status?.name?.toLowerCase().includes('pending') ||
           service.status?.name?.toLowerCase().includes('new'));

        // Debug logging for overdue services
        if (service.scheduled_date) {
          console.log(`🔍 Service ${service.call_number || service.id}:`, {
            scheduled_date: service.scheduled_date,
            scheduledDate: scheduledDate?.toISOString(),
            today: today.toISOString(),
            status: service.status,
            isOverdue,
            dateCheck: scheduledDate && scheduledDate < today,
            statusCheck: service.status?.category === 'open' ||
                        service.status?.name?.toLowerCase().includes('open') ||
                        service.status?.name?.toLowerCase().includes('pending') ||
                        service.status?.name?.toLowerCase().includes('new')
          });
        }

        return {
          id: service.id,
          serviceNumber: service.call_number || `SRV-${service.id}`,
          customer: service.customer?.company_name || service.customer?.display_name || 'Unknown Customer',
          customerId: service.customer?.id,
          contactPerson: service.contactPerson?.first_name && service.contactPerson?.last_name
            ? `${service.contactPerson.first_name} ${service.contactPerson.last_name}`
            : service.customer?.contact_person || 'N/A',
          type: service.call_billing_type || 'free_call', // Default to free_call if not specified
          priority: service.priority || 'medium',
          status: service.status?.name || service.status || 'pending',
          statusObject: service.status,
          assignedTo: service.assignedExecutive?.first_name && service.assignedExecutive?.last_name
            ? `${service.assignedExecutive.first_name} ${service.assignedExecutive.last_name}`
            : 'Unassigned',
          subject: service.subject || '',
          description: service.description || '',
          createdDate: service.created_at,
          scheduledDate: service.scheduled_date || service.created_at,
          completedDate: service.completed_at,
          startedAt: service.started_at,
          estimatedHours: service.estimated_hours || 0,
          actualHours: service.actual_hours || 0,
          totalTimeMinutes: service.total_time_minutes || 0,
          totalTimeSeconds: service.total_time_seconds || (service.total_time_minutes ? service.total_time_minutes * 60 : 0),
          timeTrackingHistory: service.time_tracking_history || [],
          isOverdue,
          daysOverdue: isOverdue ? Math.ceil((new Date() - new Date(service.scheduled_date)) / (1000 * 60 * 60 * 24)) : 0,
          time_tracking_summary: service.time_tracking_summary || {},
          amount: service.service_charge || 0,
          location: service.customer?.city && service.customer?.state ?
            `${service.customer.city}, ${service.customer.state}` : 'N/A',
          tallySerialNumber: service.tally_serial_number ||
            service.customer?.tally_serial_number ||
            service.customer?.customer_code ||
            service.customer?.custom_fields?.tally_serial_number ||
            'N/A'
        };
      });

      // Debug: Count overdue services
      const overdueCount = transformedServices.filter(s => s.isOverdue).length;
      console.log(`📊 Service Summary:`, {
        total: transformedServices.length,
        overdue: overdueCount,
        overdueServices: transformedServices.filter(s => s.isOverdue).map(s => ({
          id: s.id,
          serviceNumber: s.serviceNumber,
          scheduledDate: s.scheduledDate,
          daysOverdue: s.daysOverdue,
          status: s.status
        }))
      });

      // Update state
      setServices(transformedServices);
      setTotalPages(result.pagination.totalPages || Math.ceil((result.pagination.total || transformedServices.length) / limit));
      setTotalServices(result.pagination.total || transformedServices.length);

      // Process stats data safely
      const processedStats = processStatsData(result.stats);
      setServiceStats(processedStats);

      console.log('📊 Processed service stats:', {
        totalCalls: processedStats.totalCalls,
        callsByType: processedStats.callsByType,
        hasError: processedStats.metadata.hasError,
        lastUpdated: processedStats.metadata.lastUpdated,
        rawStatsData: result.stats
      });



      // Update filter options if available
      if (result.filters.statuses) {
        setFilterOptions(prev => ({
          ...prev,
          statuses: result.filters.statuses
        }));
      }
      if (result.filters.typeOfCalls) {
        setFilterOptions(prev => ({
          ...prev,
          typeOfCalls: result.filters.typeOfCalls
        }));
      }

      // Cache the result
      dataCache.current.set(cacheKey, {
        services: transformedServices,
        totalPages: result.pagination.totalPages || Math.ceil((result.pagination.total || transformedServices.length) / limit),
        totalServices: result.pagination.total || transformedServices.length,
        timestamp: Date.now()
      });

      // Clear request cache
      requestCache.current.delete(cacheKey);

    } catch (error) {
      console.error('Error fetching service list data:', error);
      setServices([]);
      setTotalPages(0);
      setTotalServices(0);

      // Set error state for stats with fallback data
      const errorStats = processStatsData(null);
      errorStats.metadata.hasError = true;
      errorStats.metadata.errorMessage = error.message || 'Failed to fetch service data';
      setServiceStats(errorStats);

      // Clear caches on error
      requestCache.current.delete(cacheKey);
      dataCache.current.clear();

      throw error;
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array since we don't want this to change

  // Fetch initial filter options and call statuses (cached)
  const fetchInitialData = useCallback(async () => {
    const cacheKey = 'initial_data';
    
    // Check cache first
    if (dataCache.current.has(cacheKey)) {
      const cachedData = dataCache.current.get(cacheKey);
      const cacheAge = Date.now() - cachedData.timestamp;
      
      // Use cache if less than 5 minutes old
      if (cacheAge < 300000) {
        console.log('📦 Using cached initial data');
        setFilterOptions(cachedData.filterOptions);
        setCallStatuses(cachedData.callStatuses);
        return;
      }
    }

    try {
      setFiltersLoading(true);
      setStatsLoading(true);

      const [filtersResponse] = await Promise.allSettled([
        apiService.get('/service-calls/filter-options')
      ]);

      const filterData = filtersResponse.status === 'fulfilled' ? filtersResponse.value.data?.data : {};

      // Fetch call statuses from API instead of using hardcoded values
      let callStatusesData = [];
      try {
        const callStatusesResponse = await masterDataAPI.getCallStatuses({
          limit: 100, // Get all statuses
          page: 1,
          isActive: true,
          sortBy: 'sort_order',
          sortOrder: 'ASC'
        });

        if (callStatusesResponse.data?.success) {
          callStatusesData = callStatusesResponse.data.data.callstatus || [];
          console.log('📋 Fetched call statuses for service list:', callStatusesData.length, 'statuses');
        } else {
          console.warn('⚠️ Call statuses API response indicates failure, using fallback');
        }
      } catch (statusError) {
        console.error('❌ Error fetching call statuses, using fallback:', statusError);
      }

      // Fallback to hardcoded statuses if API fails
      if (callStatusesData.length === 0) {
        callStatusesData = [
          { id: 'follow_up', name: 'Follow Up', code: 'FOLLOW_UP' },
          { id: 'call_not_atten', name: 'Call Not Atten', code: 'CALL_NOT_ATTEN' },
          { id: 'pending', name: 'Pending', code: 'PENDING' },
          { id: 'onsite', name: 'Onsite', code: 'ONSITE' },
          { id: 'completed', name: 'Completed', code: 'COMPLETED' },
          { id: 'cancelled', name: 'Cancelled', code: 'CANCELLED' },
          { id: 'no_issue', name: 'No Issue', code: 'NO_ISSUE' },
          { id: 'customization', name: 'Customization', code: 'CUSTOMIZATION' },
          { id: 'whats_up', name: 'Whats Up', code: 'WHATS_UP' },
          { id: 'urgent', name: 'Urgent', code: 'URGENT' },
          { id: 'on_process', name: 'On Process', code: 'ON_PROCESS' },
          { id: 'follow_up_programmer', name: 'Follow Up Programmer', code: 'FOLLOW_UP_PROGRAMMER' },
          { id: 'follow_up_customer', name: 'Follow Up Customer', code: 'FOLLOW_UP_CUSTOMER' }
        ];
        console.log('📋 Using fallback call statuses for service list');
      }

      setFilterOptions({
        statuses: filterData.statuses || [],
        typeOfCalls: filterData.typeOfCalls || [],
        callBillingTypes: filterData.callBillingTypes || [
          { value: 'free_call', label: 'Free Call' },
          { value: 'amc_call', label: 'AMC Call' },
          { value: 'per_call', label: 'Per Call' }
        ]
      });
      setCallStatuses(callStatusesData);

      // Cache the result
      dataCache.current.set(cacheKey, {
        filterOptions: {
          statuses: filterData.statuses || [],
          typeOfCalls: filterData.typeOfCalls || [],
          callBillingTypes: filterData.callBillingTypes || [
            { value: 'free_call', label: 'Free Call' },
            { value: 'amc_call', label: 'AMC Call' },
            { value: 'per_call', label: 'Per Call' }
          ]
        },
        callStatuses: callStatusesData,
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('Error fetching initial data:', error);
    } finally {
      setFiltersLoading(false);
      setStatsLoading(false);
    }
  }, []); // Empty dependency array since we don't want this to change

  // Clear cache function
  const clearCache = () => {
    dataCache.current.clear();
    requestCache.current.clear();
  };

  // Update single service in list (for optimistic updates)
  const updateService = useCallback((serviceId, updates) => {
    setServices(prevServices =>
      prevServices.map(service =>
        service.id === serviceId ? { ...service, ...updates } : service
      )
    );
  }, []);

  return {
    // Data
    services,
    serviceStats,
    filterOptions,
    callStatuses,
    totalPages,
    totalServices,

    // Loading states
    loading,
    statsLoading,
    filtersLoading,

    // Actions
    fetchAllData,
    fetchInitialData,
    updateService,
    clearCache,

    // State setters for external updates
    setServices,
    setServiceStats
  };
};

export default useServiceListData;
