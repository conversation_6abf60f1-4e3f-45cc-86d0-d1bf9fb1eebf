import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import MastersList from './MastersList';
import OnlineCallTypesPage from './OnlineCallTypesPage';
import CallStatusesPage from './CallStatusesPage';

const MastersRoutes = () => {
  return (
    <Routes>
      <Route index element={<MastersList />} />
      <Route path="online-call-types" element={<OnlineCallTypesPage />} />
      <Route path="call-statuses" element={<CallStatusesPage />} />
      <Route path="*" element={<Navigate to="/masters" replace />} />
    </Routes>
  );
};

export default MastersRoutes;
