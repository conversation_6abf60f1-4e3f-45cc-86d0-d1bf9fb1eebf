import React, { useState } from 'react';
import SearchableSelect from './SearchableSelect';

const SearchableSelectTest = () => {
  const [selectedValue, setSelectedValue] = useState(null);

  // Mock industry data for testing
  const mockIndustries = [
    {
      id: '1',
      name: 'Manufacturing',
      description: 'Manufacturing and production companies',
      category: 'Production'
    },
    {
      id: '2',
      name: 'Trading',
      description: 'Trading and distribution companies',
      category: 'Commerce'
    },
    {
      id: '3',
      name: 'Services',
      description: 'Service-based companies',
      category: 'Services'
    },
    {
      id: '4',
      name: 'Healthcare',
      description: 'Healthcare and medical services',
      category: 'Services'
    },
    {
      id: '5',
      name: 'Technology',
      description: 'Information technology and software',
      category: 'Technology'
    },
    {
      id: '6',
      name: 'Retail',
      description: 'Retail and consumer goods',
      category: 'Commerce'
    },
    {
      id: '7',
      name: 'Financial Services',
      description: 'Banking and financial services',
      category: 'Finance'
    },
    {
      id: '8',
      name: 'Education',
      description: 'Educational institutions and services',
      category: 'Education'
    }
  ];

  return (
    <div className="p-8 max-w-md mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">SearchableSelect Test</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Industry
          </label>
          <SearchableSelect
            options={mockIndustries}
            value={selectedValue}
            onChange={setSelectedValue}
            placeholder="Search industries..."
            searchFields={['name', 'description', 'category']}
            displayField="name"
            valueField="id"
            minSearchLength={2}
            maxResults={6}
            noResultsText="No industries found"
            searchingText="Type 2+ letters to search industries..."
          />
        </div>

        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-2">Selected Value:</h3>
          <pre className="text-sm text-gray-600">
            {selectedValue ? JSON.stringify(selectedValue, null, 2) : 'None'}
          </pre>
        </div>

        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">Selected Industry:</h3>
          <div className="text-sm text-blue-600">
            {selectedValue ? (
              <div>
                <div><strong>Name:</strong> {mockIndustries.find(i => i.id === selectedValue)?.name}</div>
                <div><strong>Description:</strong> {mockIndustries.find(i => i.id === selectedValue)?.description}</div>
                <div><strong>Category:</strong> {mockIndustries.find(i => i.id === selectedValue)?.category}</div>
              </div>
            ) : (
              'No industry selected'
            )}
          </div>
        </div>

        <button
          onClick={() => setSelectedValue(null)}
          className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
        >
          Clear Selection
        </button>
      </div>
    </div>
  );
};

export default SearchableSelectTest;
