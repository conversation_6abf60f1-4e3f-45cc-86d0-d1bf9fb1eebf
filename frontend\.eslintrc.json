{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:react-hooks/recommended"], "ignorePatterns": ["dist", "node_modules", ".eslintrc.json", "vite.config.js", "tailwind.config.js", "postcss.config.js"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "18.2"}}, "plugins": ["react-refresh", "react", "react-hooks"], "rules": {"react/jsx-no-target-blank": "off", "react/prop-types": "off", "react/no-unescaped-entities": "off", "react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-unused-vars": ["error", {"varsIgnorePattern": "^_", "argsIgnorePattern": "^_"}], "no-console": "off", "no-debugger": "warn", "no-undef": "error", "no-unreachable": "error", "no-duplicate-imports": "error", "react/jsx-closing-bracket-location": ["error", "line-aligned"], "react/jsx-closing-tag-location": "error", "react/jsx-curly-spacing": ["error", "never"], "react/jsx-equals-spacing": ["error", "never"], "react/jsx-indent": ["error", 2], "react/jsx-indent-props": ["error", 2], "react/jsx-no-duplicate-props": "error", "react/jsx-no-undef": "error", "react/jsx-uses-vars": "error", "indent": ["error", 2, {"SwitchCase": 1}], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "comma-dangle": ["error", "only-multiline"], "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "space-before-blocks": "error", "keyword-spacing": "error", "space-infix-ops": "error", "eol-last": "error", "no-trailing-spaces": "error", "no-multiple-empty-lines": ["error", {"max": 2, "maxEOF": 1}], "eqeqeq": "error", "no-var": "error", "prefer-const": "error", "no-eval": "error", "no-implied-eval": "error", "curly": ["error", "multi-line"], "dot-notation": "error", "no-else-return": "error", "no-empty-function": "warn", "no-lone-blocks": "error", "no-multi-spaces": "error", "no-new-wrappers": "error", "no-return-assign": "error", "no-self-compare": "error", "no-sequences": "error", "no-throw-literal": "error", "no-unused-expressions": "error", "no-useless-call": "error", "no-useless-concat": "error", "no-useless-return": "error", "prefer-promise-reject-errors": "error", "radix": "error", "yoda": "error", "arrow-spacing": "error", "no-confusing-arrow": "error", "no-useless-computed-key": "error", "no-useless-constructor": "error", "no-useless-rename": "error", "object-shorthand": "error", "prefer-arrow-callback": "error", "prefer-destructuring": ["error", {"array": true, "object": true}, {"enforceForRenamedProperties": false}], "prefer-rest-params": "error", "prefer-spread": "error", "prefer-template": "error", "rest-spread-spacing": "error", "template-curly-spacing": "error"}, "overrides": [{"files": ["**/*.test.js", "**/*.test.jsx", "**/*.spec.js", "**/*.spec.jsx"], "env": {"jest": true}, "rules": {"no-unused-expressions": "off"}}]}