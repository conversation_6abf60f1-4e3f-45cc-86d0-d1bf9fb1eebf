import React from 'react';
import { cn } from '../../../utils/helpers';

const Select = React.forwardRef(({
  children,
  className,
  error,
  disabled,
  size = 'md',
  variant = 'default',
  placeholder,
  ...props
}, ref) => {
  const baseClasses = 'w-full border rounded transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:bg-gray-100 disabled:text-gray-500 disabled:cursor-not-allowed bg-white';

  const variants = {
    default: 'border-gray-300 focus:border-primary-500 focus:ring-primary-500',
    error: 'border-danger-500 focus:border-danger-500 focus:ring-danger-500',
    success: 'border-success-500 focus:border-success-500 focus:ring-success-500',
  };

  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const currentVariant = error ? 'error' : variant;

  return (
    <select
      ref={ref}
      className={cn(
        baseClasses,
        variants[currentVariant],
        sizes[size],
        className
      )}
      disabled={disabled}
      {...props}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {children}
    </select>
  );
});

Select.displayName = 'Select';

export default Select;
