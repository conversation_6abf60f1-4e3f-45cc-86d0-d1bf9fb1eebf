<!DOCTYPE html>
<html>
<head>
    <title>Debug Overdue Services</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .service { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .overdue { background-color: #ffebee; border-color: #f44336; }
        .normal { background-color: #e8f5e8; border-color: #4caf50; }
        .completed { background-color: #f5f5f5; border-color: #9e9e9e; }
        .debug { background-color: #fff3e0; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Debug Overdue Services</h1>
    
    <div>
        <button onclick="testOverdueLogic()">Test Overdue Logic</button>
        <button onclick="fetchFromAPI()">Fetch from API (if logged in)</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testOverdueLogic() {
            const results = document.getElementById('results');
            
            // Test data based on what we created in the database
            const testServices = [
                {
                    id: 1,
                    call_number: 'SC-OVERDUE-001',
                    scheduled_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Pending', category: 'open' },
                    customer: { company_name: 'Test Customer' }
                },
                {
                    id: 2,
                    call_number: 'SC-OVERDUE-002',
                    scheduled_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Pending', category: 'open' },
                    customer: { company_name: 'Test Customer' }
                },
                {
                    id: 3,
                    call_number: 'SC-OVERDUE-003',
                    scheduled_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Pending', category: 'open' },
                    customer: { company_name: 'Test Customer' }
                },
                {
                    id: 4,
                    call_number: 'SC-OVERDUE-004',
                    scheduled_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Pending', category: 'open' },
                    customer: { company_name: 'Test Customer' }
                },
                {
                    id: 5,
                    call_number: 'SC-NORMAL-001',
                    scheduled_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Pending', category: 'open' },
                    customer: { company_name: 'Test Customer' }
                },
                {
                    id: 6,
                    call_number: 'SC-COMPLETED-001',
                    scheduled_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    status: { name: 'Completed', category: 'closed' },
                    customer: { company_name: 'Test Customer' }
                }
            ];

            results.innerHTML = '<h2>Test Results (Simulated Data):</h2>';

            testServices.forEach(service => {
                const result = processService(service);
                const cssClass = result.isOverdue ? 'overdue' : (result.status.category === 'closed' ? 'completed' : 'normal');
                
                results.innerHTML += `
                    <div class="service ${cssClass}">
                        <h3>${service.call_number}</h3>
                        <p><strong>Customer:</strong> ${service.customer.company_name}</p>
                        <p><strong>Scheduled Date:</strong> ${service.scheduled_date}</p>
                        <p><strong>Status:</strong> ${service.status.name} (${service.status.category})</p>
                        <p><strong>Is Overdue:</strong> ${result.isOverdue ? `YES (${result.daysOverdue} days)` : 'NO'}</p>
                        <div class="debug">
                            <strong>Debug Info:</strong><br>
                            Today: ${result.today}<br>
                            Scheduled Date Parsed: ${result.scheduledDateParsed}<br>
                            Status Category Check: ${result.statusCheck}<br>
                            Date Check: ${result.dateCheck}
                        </div>
                    </div>
                `;
            });
        }

        function processService(service) {
            // Exact logic from frontend
            const today = new Date();
            today.setHours(23, 59, 59, 999);

            let scheduledDate = null;
            if (service.scheduled_date) {
                scheduledDate = new Date(service.scheduled_date);
                if (!isNaN(scheduledDate.getTime())) {
                    scheduledDate.setHours(23, 59, 59, 999);
                } else {
                    scheduledDate = null;
                }
            }

            const statusCheck = service.status?.category === 'open' ||
                               service.status?.name?.toLowerCase().includes('open') ||
                               service.status?.name?.toLowerCase().includes('pending') ||
                               service.status?.name?.toLowerCase().includes('new');

            const dateCheck = scheduledDate && scheduledDate < today;

            const isOverdue = scheduledDate && scheduledDate < today && statusCheck;

            const daysOverdue = isOverdue ? Math.ceil((new Date() - new Date(service.scheduled_date)) / (1000 * 60 * 60 * 24)) : 0;

            return {
                isOverdue,
                daysOverdue,
                today: today.toISOString(),
                scheduledDateParsed: scheduledDate ? scheduledDate.toISOString() : 'null',
                statusCheck,
                dateCheck,
                status: service.status
            };
        }

        async function fetchFromAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>Fetching from API...</h2>';

            try {
                // Try to fetch from the API (this will only work if user is logged in)
                const response = await fetch('/api/v1/service-calls?limit=20');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data) {
                    results.innerHTML = '<h2>API Results:</h2>';
                    
                    data.data.forEach(service => {
                        const result = processService(service);
                        const cssClass = result.isOverdue ? 'overdue' : (result.status.category === 'closed' ? 'completed' : 'normal');
                        
                        results.innerHTML += `
                            <div class="service ${cssClass}">
                                <h3>${service.call_number || 'No Number'}</h3>
                                <p><strong>Customer:</strong> ${service.customer?.company_name || 'Unknown'}</p>
                                <p><strong>Scheduled Date:</strong> ${service.scheduled_date || 'Not scheduled'}</p>
                                <p><strong>Status:</strong> ${service.status?.name || 'No status'} (${service.status?.category || 'No category'})</p>
                                <p><strong>Is Overdue:</strong> ${result.isOverdue ? `YES (${result.daysOverdue} days)` : 'NO'}</p>
                            </div>
                        `;
                    });
                } else {
                    results.innerHTML = '<h2>API Error:</h2><p>No data received or API returned error</p>';
                }
            } catch (error) {
                results.innerHTML = `<h2>API Error:</h2><p>${error.message}</p><p>Make sure you're logged in to the application first.</p>`;
            }
        }
    </script>
</body>
</html>
