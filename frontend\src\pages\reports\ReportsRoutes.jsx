import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ReportsList from './ReportsList';
import ServiceAnalytics from './ServiceAnalytics';
import ServiceReports from './ServiceReports';
import ServiceCallReports from './ServiceCallReports';
import CustomerReports from './CustomerReports';
import SalesReports from './SalesReports';
import AmcReports from './AmcReports';
// Analytics Pages
import CustomerAnalytics from '../analytics/CustomerAnalytics';
import FinancialAnalytics from '../analytics/FinancialAnalytics';

const ReportsRoutes = () => {
  return (
    <Routes>
      <Route index element={<ReportsList />} />
      <Route path="service-analytics" element={<ServiceAnalytics />} />
      <Route path="customer-analytics" element={<CustomerAnalytics />} />
      <Route path="financial-analytics" element={<FinancialAnalytics />} />
      <Route path="service-reports" element={<ServiceReports />} />
      <Route path="service-call-reports" element={<ServiceCallReports />} />
      <Route path="customer-reports" element={<CustomerReports />} />
      <Route path="sales-reports" element={<SalesReports />} />
      <Route path="amc-reports" element={<AmcReports />} />
      <Route path="*" element={<Navigate to="/reports" replace />} />
    </Routes>
  );
};

export default ReportsRoutes;
