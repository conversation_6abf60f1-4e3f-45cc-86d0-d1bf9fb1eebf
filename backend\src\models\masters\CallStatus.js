import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const CallStatus = sequelize.define('CallStatus', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('open', 'in_progress', 'resolved', 'closed', 'cancelled'),
      allowNull: false,
      defaultValue: 'open',
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#6c757d',
      comment: 'Color code for UI display',
    },
    is_final: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this is a final status (no further changes allowed)',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether changing to this status requires approval',
    },
    auto_close_after_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Auto-close call after specified days in this status',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether calls in this status are billable',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this is a default status that cannot be edited or deleted',
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'call_statuses',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_final'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['is_default'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  CallStatus.getDefaultStatuses = function() {
    return [
      {
        name: 'Pending',
        code: 'PENDING',
        description: 'Call is pending and waiting to be processed',
        category: 'open',
        color: '#ffc107',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        is_default: true,
        sort_order: 1,
      },
      {
        name: 'Onsite',
        code: 'ONSITE',
        description: 'Onsite visit required or in progress',
        category: 'in_progress',
        color: '#17a2b8',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        is_default: true,
        sort_order: 2,
      },
      {
        name: 'Cancelled',
        code: 'CANCELLED',
        description: 'Call has been cancelled',
        category: 'cancelled',
        color: '#dc3545',
        is_final: true,
        requires_approval: true,
        auto_close_after_days: null,
        is_billable: false,
        is_default: true,
        sort_order: 3,
      },
      {
        name: 'No Issue',
        code: 'NO_ISSUE',
        description: 'No issue found or false alarm',
        category: 'resolved',
        color: '#6c757d',
        is_final: true,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        is_default: true,
        sort_order: 4,
      },
      {
        name: 'Customization',
        code: 'CUSTOMIZATION',
        description: 'Customization work required',
        category: 'in_progress',
        color: '#e83e8c',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        is_default: true,
        sort_order: 5,
      },
      {
        name: 'Completed',
        code: 'COMPLETED',
        description: 'Call has been completed successfully',
        category: 'closed',
        color: '#28a745',
        is_final: true,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        is_default: true,
        sort_order: 6,
      },
      {
        name: 'Call Not Atten',
        code: 'CALL_NOT_ATTEN',
        description: 'Call not attended by customer',
        category: 'open',
        color: '#fd7e14',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: 3,
        is_billable: false,
        is_default: true,
        sort_order: 7,
      },
      {
        name: 'Whats Up',
        code: 'WHATS_UP',
        description: 'WhatsApp communication in progress',
        category: 'in_progress',
        color: '#25d366',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        is_default: true,
        sort_order: 8,
      },
      {
        name: 'Urgent',
        code: 'URGENT',
        description: 'Urgent priority call requiring immediate attention',
        category: 'open',
        color: '#dc3545',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        is_default: true,
        sort_order: 9,
      },
      {
        name: 'On Process',
        code: 'ON_PROCESS',
        description: 'Call is currently being processed',
        category: 'in_progress',
        color: '#6f42c1',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        is_default: true,
        sort_order: 10,
      },
      {
        name: 'Follow Up Programer',
        code: 'FOLLOW_UP_PROGRAMMER',
        description: 'Follow up required with programmer',
        category: 'in_progress',
        color: '#20c997',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: 2,
        is_billable: true,
        is_default: true,
        sort_order: 11,
      },
      {
        name: 'Follow Up Customer',
        code: 'FOLLOW_UP_CUSTOMER',
        description: 'Follow up required with customer',
        category: 'in_progress',
        color: '#fd7e14',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: 3,
        is_billable: false,
        is_default: true,
        sort_order: 12,
      },
    ];
  };

  // Instance methods
  CallStatus.prototype.canTransitionTo = function(targetStatus) {
    // Allow all status transitions except from COMPLETED status
    // COMPLETED status is locked and cannot transition to any other status
    if (this.code === 'COMPLETED') {
      return false;
    }

    // All other statuses can transition to any status (including COMPLETED)
    return true;
  };

  CallStatus.prototype.isOpen = function() {
    return ['open', 'in_progress'].includes(this.category);
  };

  CallStatus.prototype.isClosed = function() {
    return ['resolved', 'closed', 'cancelled'].includes(this.category);
  };

  // Associations
  CallStatus.associate = function(models) {
    CallStatus.hasMany(models.ServiceCall, {
      foreignKey: 'status_id',
      as: 'serviceCalls',
    });
  };

  return CallStatus;
}
