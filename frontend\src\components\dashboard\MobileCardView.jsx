import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils/helpers';
import { ServiceStatusBadge, PriorityBadge, Button } from '../ui';
import { formatRelativeDate } from '../../utils/dashboardHelpers';

/**
 * Mobile Card View Component for Dashboard
 * Provides responsive card layouts for mobile devices with accordion/collapsible sections
 */
const MobileCardView = ({
  data = [],
  type = 'service-calls', // 'service-calls' or 'customers'
  className = '',
  ...props
}) => {
  const navigate = useNavigate();
  const [expandedCards, setExpandedCards] = useState(new Set());

  const toggleCard = (id) => {
    const newExpanded = new Set(expandedCards);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedCards(newExpanded);
  };

  if (!data || data.length === 0) {
    return (
      <div className={cn('text-center py-8', className)}>
        <div className="text-gray-400 text-sm">No data available</div>
      </div>
    );
  }

  const renderServiceCallCard = (call, index) => {
    const isExpanded = expandedCards.has(call.id || index);
    
    return (
      <div
        key={call.id || index}
        className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
        style={{ width: '250px', minHeight: '200px' }}
      >
        {/* Card Header */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h6 className="font-semibold text-gray-900 text-sm mb-1">
                {call.call_number || `SC-${index + 1}`}
              </h6>
              <p className="text-xs text-gray-500 mb-2">
                {formatRelativeDate(call.created_at)}
              </p>
              <div className="flex items-center space-x-2">
                <ServiceStatusBadge status={call.status?.name || 'Open'} size="sm" />
                <PriorityBadge priority={call.priority || 'Medium'} size="sm" />
              </div>
            </div>
            <button
              onClick={() => toggleCard(call.id || index)}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              <i className={`bi bi-chevron-${isExpanded ? 'up' : 'down'} text-sm`}></i>
            </button>
          </div>
        </div>

        {/* Card Body */}
        <div className="p-4">
          {/* Customer Info */}
          <div className="flex items-center space-x-2 mb-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-blue-600 font-medium text-xs">
                {(call.customer?.company_name || call.customer?.display_name || 'U').charAt(0).toUpperCase()}
              </span>
            </div>
            <span className="text-sm text-gray-700 truncate">
              {call.customer?.company_name || call.customer?.display_name || 'Unknown Customer'}
            </span>
          </div>

          {/* Subject */}
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {call.consolidatedSubject || call.subject || 'General support'}
          </p>

          {/* Collapsible Details */}
          {isExpanded && (
            <div className="space-y-3 pt-3 border-t border-gray-100">
              {/* Executive Assignment */}
              {(call.assigned_to?.name || call.executive?.name) && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-person text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600">
                    Assigned to: {call.assigned_to?.name || call.executive?.name}
                  </span>
                </div>
              )}

              {/* Contact Info */}
              {call.customer?.phone && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-telephone text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600">{call.customer.phone}</span>
                </div>
              )}

              {call.customer?.email && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-envelope text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600 truncate">{call.customer.email}</span>
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/services/${call.id}`)}
              className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <i className="bi bi-eye mr-2"></i>
              View details
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderCustomerCard = (customer, index) => {
    const isExpanded = expandedCards.has(customer.id || index);
    
    return (
      <div
        key={customer.id || index}
        className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
        style={{ width: '250px', minHeight: '180px' }}
      >
        {/* Card Header */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 flex-1">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-blue-600 font-medium text-sm">
                  {customer.initials || (customer.company_name || customer.display_name || 'U').charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <h6 className="font-semibold text-gray-900 text-sm truncate">
                  {customer.displayName || customer.company_name || customer.display_name || 'Unknown Company'}
                </h6>
                <p className="text-xs text-gray-500">
                  {formatRelativeDate(customer.created_at)}
                </p>
              </div>
            </div>
            <button
              onClick={() => toggleCard(customer.id || index)}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              <i className={`bi bi-chevron-${isExpanded ? 'up' : 'down'} text-sm`}></i>
            </button>
          </div>
        </div>

        {/* Card Body */}
        <div className="p-4">
          {/* Primary Contact */}
          <div className="mb-3">
            <p className="text-sm text-gray-600">
              {customer.primaryContact || customer.email || customer.phone || 'No contact info'}
            </p>
          </div>

          {/* Collapsible Details */}
          {isExpanded && (
            <div className="space-y-3 pt-3 border-t border-gray-100">
              {customer.email && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-envelope text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600 truncate">{customer.email}</span>
                </div>
              )}

              {customer.phone && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-telephone text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600">{customer.phone}</span>
                </div>
              )}

              {customer.business_type && (
                <div className="flex items-center space-x-2">
                  <i className="bi bi-building text-gray-400 text-sm"></i>
                  <span className="text-sm text-gray-600 capitalize">{customer.business_type}</span>
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          <div className="mt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/customers/${customer.id}`)}
              className="w-full text-blue-600 border-blue-200 hover:bg-blue-50"
            >
              <i className="bi bi-eye mr-2"></i>
              View profile
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn('w-full', className)} {...props}>
      {/* Mobile Grid Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {data.map((item, index) => {
          if (type === 'service-calls') {
            return renderServiceCallCard(item, index);
          } else {
            return renderCustomerCard(item, index);
          }
        })}
      </div>

      {/* Expand/Collapse All Controls */}
      {data.length > 3 && (
        <div className="mt-4 flex justify-center space-x-4">
          <button
            onClick={() => setExpandedCards(new Set(data.map((item, index) => item.id || index)))}
            className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
          >
            Expand all
          </button>
          <button
            onClick={() => setExpandedCards(new Set())}
            className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            Collapse all
          </button>
        </div>
      )}
    </div>
  );
};

export default MobileCardView;
