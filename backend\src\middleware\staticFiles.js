import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import { logger } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Middleware to serve static frontend files in production
 * This enables same-origin deployment where backend serves the frontend
 */
export const serveStaticFiles = (app) => {
  // Only serve static files in production
  if (process.env.NODE_ENV === 'production') {
    const frontendDistPath = path.join(__dirname, '../../../frontend/dist');
    
    logger.info(`📁 Serving static files from: ${frontendDistPath}`);
    
    // Serve static assets with caching
    app.use(express.static(frontendDistPath, {
      maxAge: '1y', // Cache static assets for 1 year
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        // Set cache headers for different file types
        if (path.endsWith('.html')) {
          // Don't cache HTML files to ensure updates are reflected
          res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          res.setHeader('Pragma', 'no-cache');
          res.setHeader('Expires', '0');
        } else if (path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
          // Cache static assets for 1 year
          res.setHeader('Cache-Control', 'public, max-age=********, immutable');
        }
      }
    }));
    
    // Handle client-side routing - serve index.html for all non-API routes
    app.get('*', (req, res, next) => {
      // Skip API routes, webhooks, uploads, and health checks
      if (req.path.startsWith('/api/') || 
          req.path.startsWith('/webhooks') || 
          req.path.startsWith('/uploads') || 
          req.path === '/health') {
        return next();
      }
      
      // Serve index.html for all other routes (SPA routing)
      const indexPath = path.join(frontendDistPath, 'index.html');
      res.sendFile(indexPath, (err) => {
        if (err) {
          logger.error('Error serving index.html:', err);
          res.status(500).send('Internal Server Error');
        }
      });
    });
    
    logger.info('✅ Static file serving configured for production');
  } else {
    logger.info('🔧 Static file serving disabled in development mode');
  }
};

/**
 * Middleware to handle CORS for same-origin deployment
 * Disables CORS when serving frontend from same origin
 */
export const configureCorsForStaticFiles = () => {
  if (process.env.NODE_ENV === 'production' && process.env.ENABLE_CORS === 'false') {
    logger.info('🔒 CORS disabled for same-origin deployment');
    return (req, res, next) => next(); // No-op middleware
  }
  
  // Return null to use default CORS configuration
  return null;
};
