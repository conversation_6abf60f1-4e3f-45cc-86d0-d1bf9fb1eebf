import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import Footer from './Footer';
import useAppStore from '../../store/appStore';
import { apiService } from '../../services/api';

const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const { updateMasterCounts } = useAppStore();

  // Initialize master counts when layout loads
  useEffect(() => {
    const initializeMasterCounts = async () => {
      try {
        console.log('🔄 Initializing all master counts...');

        // Fetch counts for all master data types
        const [
          customersRes,
          licenseEditionsRes,
          designationsRes,
          tallyProductsRes,
          staffRolesRes,
          executivesRes,
          industriesRes,
          areasRes,
          natureOfIssuesRes,
          additionalServicesRes,
          callStatusesRes
        ] = await Promise.all([
          apiService.get('/customers', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/license-editions', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/designations', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/tally-products', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/staff-roles', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/executives', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/industries', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/areas', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/nature-of-issues', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/additional-services', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } })),
          apiService.get('/master-data/call-statuses', { params: { limit: 1 } }).catch(() => ({ data: { data: { pagination: { totalItems: 0 } } } }))
        ]);

        // Extract counts from pagination data
        const counts = {
          customers: customersRes.data?.data?.pagination?.totalItems || 0,
          'license-editions': licenseEditionsRes.data?.data?.pagination?.totalItems || 0,
          designations: designationsRes.data?.data?.pagination?.totalItems || 0,
          'tally-products': tallyProductsRes.data?.data?.pagination?.totalItems || 0,
          'staff-roles': staffRolesRes.data?.data?.pagination?.totalItems || 0,
          executives: executivesRes.data?.data?.pagination?.totalItems || 0,
          industries: industriesRes.data?.data?.pagination?.totalItems || 0,
          areas: areasRes.data?.data?.pagination?.totalItems || 0,
          'nature-of-issues': natureOfIssuesRes.data?.data?.pagination?.totalItems || 0,
          'additional-services': additionalServicesRes.data?.data?.pagination?.totalItems || 0,
          'call-statuses': callStatusesRes.data?.data?.pagination?.totalItems || 0
        };

        // Update all counts at once
        updateMasterCounts(counts);

        console.log('✅ All master counts initialized:', counts);
      } catch (error) {
        console.error('❌ Error initializing master counts:', error);
      }
    };

    initializeMasterCounts();
  }, [updateMasterCounts]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleSidebarMobile = () => {
    setSidebarVisible(!sidebarVisible);
  };

  return (
    <div className="flex min-h-screen overflow-x-hidden">
      <Sidebar
        collapsed={sidebarCollapsed}
        visible={sidebarVisible}
        onToggle={toggleSidebar}
        onClose={() => setSidebarVisible(false)}
      />

      <div className={`flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out overflow-x-hidden min-w-0 relative ${
        sidebarCollapsed ? 'md:ml-16' : 'md:ml-[220px]'
      } ml-0`}
      >
        <div className="relative z-50">
          <Header
            onToggleSidebar={toggleSidebar}
            onToggleSidebarMobile={toggleSidebarMobile}
            sidebarCollapsed={sidebarCollapsed}
          />
        </div>

        <main className="flex-1 theme-content-bg p-2 sm:p-4 overflow-x-auto overflow-y-visible w-full min-w-0">
          <div className="w-full max-w-full overflow-visible min-w-0">
            <Outlet />
          </div>
        </main>

        <Footer />
      </div>
    </div>
  );
};

export default MainLayout;
