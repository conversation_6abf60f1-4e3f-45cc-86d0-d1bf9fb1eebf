import React, { useState } from 'react';
import { NavLink, useLocation, useSearchParams } from 'react-router-dom';
import useAppStore from '../../store/appStore';
import preminfoLogo from '../../assets/preminfotechlogo.svg';

const Sidebar = ({ collapsed, visible, onToggle, onClose }) => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [mastersExpanded, setMastersExpanded] = useState(
    location.pathname.startsWith('/masters') || location.pathname.startsWith('/customers')
  );
  const [reportsExpanded, setReportsExpanded] = useState(
    location.pathname.startsWith('/reports')
  );

  const { masterCounts } = useAppStore();

  // Function to check if a master item is active
  const isMasterItemActive = (path) => {
    // Handle customers path directly
    if (path === '/customers') {
      return location.pathname.startsWith('/customers');
    }

    // Only proceed if we're on the masters page
    if (!location.pathname.startsWith('/masters')) return false;

    // Extract the tab parameter from the path
    const urlParams = new URLSearchParams(path.split('?')[1]);
    const tabFromPath = urlParams.get('tab');
    const currentTab = searchParams.get('tab');

    // Only return true if the tab parameters match exactly
    return tabFromPath === currentTab && tabFromPath !== null;
  };

  const menuItems = [
    { path: '/dashboard', icon: 'bi-speedometer2', label: 'Dashboard' },
    { path: '/leads', icon: 'bi-person-plus', label: 'Leads' },
    { path: '/services', icon: 'bi-tools', label: 'Services' },
    { path: '/sales', icon: 'bi-graph-up', label: 'Sales' },
    { path: '/attendance', icon: 'bi-clock', label: 'Staff Attendance' },
    // { path: '/billing/dashboard', icon: 'bi-credit-card', label: 'Billing' }, // Temporarily commented out
    { path: '/settings', icon: 'bi-sliders', label: 'Settings' },
  ];

  const mastersSubItems = [
    { path: '/customers', label: 'Customer List', count: masterCounts.customers || 0 },
    { path: '/masters?tab=license-editions', label: 'License Editions', count: masterCounts['license-editions'] || 0 },
    { path: '/masters?tab=designations', label: 'Designations', count: masterCounts.designations || 0 },
    { path: '/masters?tab=tally-products', label: 'Tally Products', count: masterCounts['tally-products'] || 0 },
    { path: '/masters?tab=staff-roles', label: 'Staff Roles', count: masterCounts['staff-roles'] || 0 },
    { path: '/masters?tab=executives', label: 'Executives', count: masterCounts.executives || 0 },
    { path: '/masters?tab=industries', label: 'Industries', count: masterCounts.industries || 0 },
    { path: '/masters?tab=areas', label: 'Areas', count: masterCounts.areas || 0 },
    { path: '/masters?tab=nature-of-issues', label: 'Nature of Issues', count: masterCounts['nature-of-issues'] || 0 },
    { path: '/masters?tab=additional-services', label: 'Additional Services', count: masterCounts['additional-services'] || 0 },
    { path: '/masters?tab=call-statuses', label: 'Call Statuses', count: masterCounts['call-statuses'] || 0 },
  ];

  const reportsSubItems = [
    { path: '/reports/service-analytics', label: 'Service Analytics' },
    { path: '/reports/customer-analytics', label: 'Customer Analytics' },
    { path: '/reports/financial-analytics', label: 'Financial Analytics' },
    { path: '/reports/customer-reports', label: 'Customer Reports' },
    { path: '/reports/service-reports', label: 'Service Reports' },
    { path: '/reports/sales-reports', label: 'Sales Reports' },
    { path: '/reports/amc-reports', label: 'AMC Reports' },
  ];



  return (
    <>
      {/* Mobile Overlay */}
      {visible && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 md:hidden z-modal-backdrop"
          onClick={onClose}
        />
      )}

      <div className={`sidebar ${collapsed ? 'collapsed' : ''} ${visible ? 'show' : ''}`}>
        <div className="p-4 border-b" style={{ borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
          <div className="flex items-center">
            {!collapsed && (
              <>
                <div className="flex items-center mr-auto">
                  <img
                    src={preminfoLogo}
                    alt="Preminfo Tech Logo"
                    className="h-8 object-contain"
                  />
                </div>
                <button
                  className="p-1 hidden md:block focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
                  style={{
                    color: 'var(--primary-text, #ffffff)',
                    '--tw-ring-color': 'var(--primary-text, #ffffff)'
                  }}
                  onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
                  onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
                  onClick={onToggle}
                >
                  <i className="bi bi-list text-xl"></i>
                </button>
              </>
            )}
            {collapsed && (
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mb-2 p-0" style={{ backgroundColor: '#ffffff' }}>
                  <img
                    src={preminfoLogo}
                    alt="Preminfo Logo"
                    className="w-full h-full object-contain"
                  />
                </div>
                <button
                  className="p-1 hidden md:block focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
                  style={{
                    color: 'var(--primary-text, #ffffff)',
                    '--tw-ring-color': 'var(--primary-text, #ffffff)'
                  }}
                  onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
                  onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
                  onClick={onToggle}
                >
                  <i className="bi bi-list text-xl"></i>
                </button>
              </div>
            )}
            <button
              className="p-1 ml-auto md:hidden focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded"
              style={{
                color: 'var(--primary-text, #ffffff)',
                '--tw-ring-color': 'var(--primary-text, #ffffff)'
              }}
              onMouseEnter={(e) => e.target.style.color = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)'}
              onMouseLeave={(e) => e.target.style.color = 'var(--primary-text, #ffffff)'}
              onClick={onClose}
            >
              <i className="bi bi-x-lg text-xl"></i>
            </button>
          </div>
        </div>

        <nav className="flex-1 py-3">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `nav-link ${isActive ? 'active' : ''}`
              }
              onClick={() => window.innerWidth < 768 && onClose()}
            >
              <i className={`bi ${item.icon}`}></i>
              <span>{item.label}</span>
            </NavLink>
          ))}

          {/* Masters Dropdown */}
          <div className="masters-dropdown">
            <div
              className={`nav-link cursor-pointer ${
                location.pathname.startsWith('/masters') || location.pathname.startsWith('/customers') ? 'active' : ''
              }`}
              onClick={() => setMastersExpanded(!mastersExpanded)}
            >
              <i className="bi bi-gear"></i>
              <span>Masters</span>
              {!collapsed && (
                <i className={`bi ${mastersExpanded ? 'bi-chevron-up' : 'bi-chevron-down'} ml-auto text-xs`}></i>
              )}
            </div>

            {mastersExpanded && !collapsed && (
              <div className="masters-submenu">
                {mastersSubItems.map((subItem) => {
                  // Determine if this item is active
                  let isActive = false;

                  if (subItem.path === '/customers') {
                    // For customers path
                    isActive = location.pathname.startsWith('/customers');
                  } else if (location.pathname.startsWith('/masters')) {
                    // For masters paths with tab parameter
                    const urlParams = new URLSearchParams(subItem.path.split('?')[1]);
                    const tabFromPath = urlParams.get('tab');
                    const currentTab = searchParams.get('tab');
                    isActive = tabFromPath === currentTab && tabFromPath !== null;
                  }

                  return (
                    <a
                      key={subItem.path}
                      href={subItem.path}
                      className={`nav-sub-link ${isActive ? 'active' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        window.history.pushState({}, '', subItem.path);
                        window.dispatchEvent(new PopStateEvent('popstate'));
                        if (window.innerWidth < 768) onClose();
                      }}
                    >
                      <span className="sub-item-label">{subItem.label}</span>
                      <span className="sub-item-count">{subItem.count}</span>
                    </a>
                  );
                })}
              </div>
            )}
          </div>

          {/* Reports Dropdown */}
          <div className="reports-dropdown">
            <div
              className={`nav-link cursor-pointer ${
                location.pathname.startsWith('/reports') ? 'active' : ''
              }`}
              onClick={() => setReportsExpanded(!reportsExpanded)}
            >
              <i className="bi bi-bar-chart"></i>
              <span>Reports</span>
              {!collapsed && (
                <i className={`bi ${reportsExpanded ? 'bi-chevron-up' : 'bi-chevron-down'} ml-auto text-xs`}></i>
              )}
            </div>

            {reportsExpanded && !collapsed && (
              <div className="reports-submenu">
                {reportsSubItems.map((subItem) => {
                  const isActive = location.pathname === subItem.path;

                  return (
                    <a
                      key={subItem.path}
                      href={subItem.path}
                      className={`nav-sub-link ${isActive ? 'active' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        window.history.pushState({}, '', subItem.path);
                        window.dispatchEvent(new PopStateEvent('popstate'));
                        if (window.innerWidth < 768) onClose();
                      }}
                    >
                      <span className="sub-item-label">{subItem.label}</span>
                    </a>
                  );
                })}
              </div>
            )}
          </div>


        </nav>

        {/* Profile button temporarily commented out due to 431 header size error */}
        {/*
        <div className="p-4 border-t border-white/20">
          <NavLink
            to="/profile"
            className={({ isActive }) =>
              `nav-link ${isActive ? 'active' : ''}`
            }
            onClick={() => window.innerWidth < 768 && onClose()}
          >
            <i className="bi bi-person-circle"></i>
            <span>Profile</span>
          </NavLink>
        </div>
        */}
      </div>
    </>
  );
};

export default Sidebar;
