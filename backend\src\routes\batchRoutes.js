import express from 'express';
import batchController from '../controllers/batchController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

/**
 * Batch API Routes
 * Consolidated endpoints to reduce duplicate HTTP requests
 */

/**
 * @swagger
 * /service-calls/timer-status/batch:
 *   post:
 *     summary: Get timer status for multiple service calls
 *     description: Retrieve timer status for multiple service calls in a single request to optimize performance
 *     tags: [Batch Operations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceCallIds
 *             properties:
 *               serviceCallIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: Array of service call IDs to get timer status for
 *                 example: ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"]
 *           example:
 *             serviceCallIds: ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"]
 *     responses:
 *       200:
 *         description: Timer status retrieved successfully for all service calls
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         timerStatuses:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/TimerStatus'
 *             example:
 *               success: true
 *               message: "Timer status retrieved successfully"
 *               data:
 *                 timerStatuses:
 *                   - service_call_id: "123e4567-e89b-12d3-a456-************"
 *                     timer_status: "running"
 *                     total_time_seconds: 3600
 *                     formatted_time: "1 hour 0 minutes 0 seconds"
 *                   - service_call_id: "456e7890-e89b-12d3-a456-************"
 *                     timer_status: "stopped"
 *                     total_time_seconds: 7200
 *                     formatted_time: "2 hours 0 minutes 0 seconds"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/service-calls/timer-status/batch', authenticateToken, batchController.getTimerStatusBatch);

/**
 * @swagger
 * /service-calls/batch-data:
 *   get:
 *     summary: Get service calls with batch data
 *     description: Retrieve service calls along with optional statistics and filters in a single optimized request
 *     tags: [Batch Operations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: includeStats
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include statistics in the response
 *       - in: query
 *         name: includeFilters
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include filter options in the response
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of service calls per page
 *     responses:
 *       200:
 *         description: Service calls batch data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCalls:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/ServiceCall'
 *                         stats:
 *                           type: object
 *                           description: Statistics (included if includeStats=true)
 *                         filters:
 *                           type: object
 *                           description: Filter options (included if includeFilters=true)
 *                         pagination:
 *                           $ref: '#/components/schemas/PaginationMeta'
 *             example:
 *               success: true
 *               message: "Service calls batch data retrieved successfully"
 *               data:
 *                 serviceCalls:
 *                   - id: "123e4567-e89b-12d3-a456-************"
 *                     service_number: "SER-001"
 *                     customer_name: "ABC Enterprises"
 *                     status: "Open"
 *                 stats:
 *                   totalCalls: 150
 *                   activeCalls: 25
 *                 pagination:
 *                   page: 1
 *                   limit: 10
 *                   total: 150
 *                   totalPages: 15
 *       401:
 *         $ref: '#/components/responses/401'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/service-calls/batch-data', authenticateToken, batchController.getServiceCallsBatchData);

export default router;
