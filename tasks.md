task1:

task2:



task3:
in the dashbord common search input searching only the customers not services like if i enter the govindaraji that's showing only the customer list or service list at a time what if user needs govindaraji customer and with there services, in that time the customer and services also should listing out here so you should show both and some time the user needs govindaraji customer, servides, executives also but here current scenariio is showing only one at a time i need all are (issue: all are working good but in the search bar if i search the executinve if click that executive the page is showing 404 please develop the executive view form)


task4:
if the service cal time is the amc call the amc expiry , renewal , next visit this types of data should show in the view service form page and what are all the place we are seeing that service full details in that plase i need this amc details shoud be show (issue: in the customer and service view form if the service call type is amc call the amc expiry , renewal , next visit this types of data should show in the view service form page and what are all the place we are seeing that service full details in that plase i need this amc details shoud be show but still showing only amc only but not showing the renwal and other information)


task5:
in th http://localhost:3004/reports/service-reports?tab=services service report pag services section table i wanna remov ethe priority column in fron end , and service id is SER-018 ok but this 36575b34-163d-426d-bad1-1fc48f11f6ee
 not needed in the service id column please remove this and make the amount secion rupees symbol instead of $ symbol and completed data not showing properly and hours spend not showing amount not showing fix these issues  (issue: you are doing good but every complete services have a spending hours if not please show 00:00 but 90% of services have a spendin hours but here showing only N/A and ev)