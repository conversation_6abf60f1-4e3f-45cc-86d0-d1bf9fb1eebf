import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Get tenant settings
 */
export const getTenantSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const tenant = await models.Tenant.findByPk(tenantId, {
      attributes: [
        'id',
        'name',
        'domain',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'timezone',
        'currency',
        'date_format',
        'time_format',
        'logo_url',
        'is_active',
        'settings',
      ],
    });

    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found',
      });
    }

    res.json({
      success: true,
      data: { tenant },
    });
  } catch (error) {
    logger.error('Get tenant settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve tenant settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update tenant settings
 */
export const updateTenantSettings = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      name,
      email,
      phone,
      address,
      city,
      state,
      country,
      postal_code,
      timezone,
      currency,
      date_format,
      time_format,
      logo_url,
      settings,
    } = req.body;

    const tenant = await models.Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found',
      });
    }

    // Update tenant settings
    await tenant.update({
      ...(name && { name }),
      ...(email && { email }),
      ...(phone && { phone }),
      ...(address && { address }),
      ...(city && { city }),
      ...(state && { state }),
      ...(country && { country }),
      ...(postal_code && { postal_code }),
      ...(timezone && { timezone }),
      ...(currency && { currency }),
      ...(date_format && { date_format }),
      ...(time_format && { time_format }),
      ...(logo_url && { logo_url }),
      ...(settings && { settings }),
    });

    logger.info('Tenant settings updated:', {
      tenantId,
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Tenant settings updated successfully',
      data: { tenant },
    });
  } catch (error) {
    logger.error('Update tenant settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update tenant settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get user preferences
 */
export const getUserPreferences = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await models.User.findByPk(userId, {
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar_url',
        'timezone',
        'language',
        'theme',
        'notifications_enabled',
        'email_notifications',
        'preferences',
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Extract primary_color from preferences for backward compatibility
    const userWithPrimaryColor = {
      ...user.toJSON(),
      primary_color: user.preferences?.primary_color || '#1d5795'
    };

    res.json({
      success: true,
      data: { user: userWithPrimaryColor },
    });
  } catch (error) {
    logger.error('Get user preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user preferences',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update user preferences
 */
export const updateUserPreferences = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      first_name,
      last_name,
      phone,
      avatar_url,
      timezone,
      language,
      theme,
      notifications_enabled,
      email_notifications,
      preferences,
      primary_color, // Add primary_color field
      // Notification settings
      sms_notifications,
      push_notifications,
      weekly_reports,
      monthly_reports,
      service_updates,
      sales_alerts,
      system_alerts,
      // Security settings
      two_factor_auth,
      login_alerts,
      session_timeout,
      password_expiry,
    } = req.body;

    const user = await models.User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Merge existing preferences with new ones
    const currentPreferences = user.preferences || {};
    const updatedPreferences = {
      ...currentPreferences,
      ...(primary_color !== undefined && { primary_color }), // Store primary_color in preferences
      ...(sms_notifications !== undefined && { sms_notifications }),
      ...(push_notifications !== undefined && { push_notifications }),
      ...(weekly_reports !== undefined && { weekly_reports }),
      ...(monthly_reports !== undefined && { monthly_reports }),
      ...(service_updates !== undefined && { service_updates }),
      ...(sales_alerts !== undefined && { sales_alerts }),
      ...(system_alerts !== undefined && { system_alerts }),
      ...(two_factor_auth !== undefined && { two_factor_auth }),
      ...(login_alerts !== undefined && { login_alerts }),
      ...(session_timeout !== undefined && { session_timeout }),
      ...(password_expiry !== undefined && { password_expiry }),
      ...(preferences && preferences),
    };

    // Update user preferences
    await user.update({
      ...(first_name && { first_name }),
      ...(last_name && { last_name }),
      ...(phone && { phone }),
      ...(avatar_url && { avatar_url }),
      ...(timezone && { timezone }),
      ...(language && { language }),
      ...(theme && { theme }),
      ...(notifications_enabled !== undefined && { notifications_enabled }),
      ...(email_notifications !== undefined && { email_notifications }),
      preferences: updatedPreferences,
    });

    logger.info('User preferences updated:', {
      userId,
      primary_color: primary_color || 'not provided',
    });

    // Reload user to get updated data
    const updatedUser = await models.User.findByPk(userId, {
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar_url',
        'timezone',
        'language',
        'theme',
        'notifications_enabled',
        'email_notifications',
        'preferences',
      ],
    });

    // Extract primary_color from preferences for response
    const userWithPrimaryColor = {
      ...updatedUser.toJSON(),
      primary_color: updatedUser.preferences?.primary_color || '#1d5795'
    };

    res.json({
      success: true,
      message: 'User preferences updated successfully',
      data: { user: userWithPrimaryColor },
    });
  } catch (error) {
    logger.error('Update user preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user preferences',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get system settings (admin only)
 */
export const getSystemSettings = async (req, res) => {
  try {
    // This would typically contain system-wide settings
    // For now, we'll return a placeholder response
    const systemSettings = {
      maintenance_mode: false,
      registration_enabled: true,
      email_verification_required: true,
      max_file_upload_size: 10485760, // 10MB
      allowed_file_types: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
      session_timeout: 3600, // 1 hour
      password_policy: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_special_chars: true,
      },
    };

    res.json({
      success: true,
      data: { settings: systemSettings },
    });
  } catch (error) {
    logger.error('Get system settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update system settings (admin only)
 */
export const updateSystemSettings = async (req, res) => {
  try {
    const settings = req.body;

    // In a real application, you would save these to a database
    // For now, we'll just log the update and return success
    logger.info('System settings update requested:', {
      settings,
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'System settings updated successfully',
      data: { settings },
    });
  } catch (error) {
    logger.error('Update system settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update system settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
