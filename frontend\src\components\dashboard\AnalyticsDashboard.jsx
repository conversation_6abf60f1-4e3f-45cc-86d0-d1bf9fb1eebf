/**
 * Analytics Dashboard Component
 * Enhanced dashboard with comprehensive analytics charts and metrics
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardBody, Spinner, Alert } from '../ui';
import { 
  <PERSON><PERSON>hart, 
  <PERSON>hart, 
  <PERSON>hart, 
  <PERSON>hart, 
  AreaChart,
  MetricCard,
  RevenueCard,
  CustomerCard,
  ServiceCard,
  ChartContainer
} from '../charts';
import { apiService } from '../../services/api';

const AnalyticsDashboard = ({ period = '30d' }) => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, [period]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch comprehensive analytics data
      const response = await apiService.get(`/analytics?period=${period}`);
      
      if (response.data.success) {
        setAnalyticsData(response.data.data);
      } else {
        throw new Error('Failed to fetch analytics data');
      }
    } catch (err) {
      console.error('Analytics fetch error:', err);
      setError(err.message || 'Failed to load analytics data');
      
      // Set mock data for development
      setAnalyticsData(getMockAnalyticsData());
    } finally {
      setLoading(false);
    }
  };

  // Mock data for development/fallback
  const getMockAnalyticsData = () => ({
    customerMetrics: {
      totalCustomers: 150,
      newCustomers: 12,
      customerTypeDistribution: [
        { name: 'Active', value: 120, status: 'customer' },
        { name: 'Inactive', value: 30, status: 'inactive' }
      ],
      acquisitionTrend: [
        { date: '2024-01-01', value: 5 },
        { date: '2024-01-02', value: 8 },
        { date: '2024-01-03', value: 3 },
        { date: '2024-01-04', value: 12 },
        { date: '2024-01-05', value: 7 }
      ]
    },
    serviceMetrics: {
      totalServiceCalls: 89,
      serviceCallsInPeriod: 23,
      serviceCallsByStatus: [
        { name: 'Open', value: 15, status: 'open' },
        { name: 'In Progress', value: 8, status: 'in-progress' },
        { name: 'Completed', value: 45, status: 'completed' },
        { name: 'Cancelled', value: 3, status: 'cancelled' }
      ],
      serviceCallsByPriority: [
        { name: 'High', value: 12, priority: 'high' },
        { name: 'Medium', value: 35, priority: 'medium' },
        { name: 'Low', value: 18, priority: 'low' }
      ],
      avgResolutionTimeHours: 24.5
    },
    financialMetrics: {
      totalRevenue: 125000,
      revenueInPeriod: 25000,
      revenueTrend: [
        { date: '2024-01-01', value: 5000 },
        { date: '2024-01-02', value: 7500 },
        { date: '2024-01-03', value: 4200 },
        { date: '2024-01-04', value: 8300 },
        { date: '2024-01-05', value: 6100 }
      ]
    },
    executiveMetrics: {
      serviceCallsByExecutive: [
        { name: 'John Doe', value: 25 },
        { name: 'Jane Smith', value: 18 },
        { name: 'Mike Johnson', value: 22 },
        { name: 'Sarah Wilson', value: 15 }
      ]
    }
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6">
              <Spinner className="mx-auto" />
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 h-80">
              <Spinner className="mx-auto mt-20" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="error" className="mb-6">
        <strong>Analytics Error:</strong> {error}
      </Alert>
    );
  }

  const { customerMetrics, serviceMetrics, financialMetrics, executiveMetrics } = analyticsData;

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <CustomerCard
          title="Total Customers"
          value={customerMetrics.totalCustomers}
          previousValue={customerMetrics.totalCustomers - customerMetrics.newCustomers}
          size="medium"
        />
        
        <ServiceCard
          title="Service Calls"
          value={serviceMetrics.totalServiceCalls}
          previousValue={serviceMetrics.totalServiceCalls - serviceMetrics.serviceCallsInPeriod}
          size="medium"
        />
        
        <RevenueCard
          title="Total Revenue"
          value={financialMetrics.totalRevenue}
          previousValue={financialMetrics.totalRevenue - financialMetrics.revenueInPeriod}
          size="medium"
        />
        
        <MetricCard
          title="Avg Resolution Time"
          value={serviceMetrics.avgResolutionTimeHours}
          unit="hours"
          format="decimal"
          color="warning"
          size="medium"
          icon={
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Acquisition Trend */}
        <ChartContainer
          title="Customer Acquisition Trend"
          subtitle={`New customers over the last ${period}`}
        >
          <LineChart
            data={customerMetrics.acquisitionTrend}
            lines={[{ dataKey: 'value', name: 'New Customers', color: '#1d5795' }]}
            height={300}
            formatters={{ value: (val) => `${val} customers` }}
          />
        </ChartContainer>

        {/* Service Calls by Status */}
        <ChartContainer
          title="Service Calls by Status"
          subtitle="Distribution of service call statuses"
        >
          <DonutChart
            data={serviceMetrics.serviceCallsByStatus}
            height={300}
            colorScheme="status"
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {serviceMetrics.totalServiceCalls}
                </div>
                <div className="text-sm text-gray-500">Total Calls</div>
              </div>
            }
          />
        </ChartContainer>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        <ChartContainer
          title="Revenue Trend"
          subtitle={`Revenue performance over ${period}`}
        >
          <AreaChart
            data={financialMetrics.revenueTrend}
            areas={[{ dataKey: 'value', name: 'Revenue', color: '#10b981' }]}
            height={300}
            formatters={{ value: (val) => `₹${val.toLocaleString()}` }}
          />
        </ChartContainer>

        {/* Executive Performance */}
        <ChartContainer
          title="Executive Workload"
          subtitle="Service calls handled by each executive"
        >
          <BarChart
            data={executiveMetrics.serviceCallsByExecutive}
            bars={[{ dataKey: 'value', name: 'Service Calls' }]}
            height={300}
            orientation="vertical"
            formatters={{ value: (val) => `${val} calls` }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 3 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Type Distribution */}
        <ChartContainer
          title="Customer Types"
          subtitle="Active vs Inactive customers"
          size="small"
        >
          <PieChart
            data={customerMetrics.customerTypeDistribution}
            height={250}
            colorScheme="status"
            showLabels={true}
          />
        </ChartContainer>

        {/* Service Priority Distribution */}
        <ChartContainer
          title="Service Priority"
          subtitle="Priority distribution of service calls"
          size="small"
        >
          <PieChart
            data={serviceMetrics.serviceCallsByPriority}
            height={250}
            colorScheme="priority"
            showLabels={true}
          />
        </ChartContainer>

        {/* Quick Stats */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">New Customers</span>
              <span className="font-medium text-green-600">+{customerMetrics.newCustomers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Service Calls</span>
              <span className="font-medium text-blue-600">{serviceMetrics.serviceCallsInPeriod}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Period Revenue</span>
              <span className="font-medium text-green-600">₹{financialMetrics.revenueInPeriod.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Avg Resolution</span>
              <span className="font-medium text-orange-600">{serviceMetrics.avgResolutionTimeHours}h</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
