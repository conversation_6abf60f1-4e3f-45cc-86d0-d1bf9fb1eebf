import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Badge, Spinner } from '../ui';
import { Clock, MapPin, Calendar, Camera, Wifi, WifiOff, Battery } from 'lucide-react';
import { attendanceAPI } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';

const MobileAttendance = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [todayAttendance, setTodayAttendance] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);
  const [checkingOut, setCheckingOut] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [batteryLevel, setBatteryLevel] = useState(null);

  useEffect(() => {
    fetchTodayAttendance();
    
    // Update time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Monitor online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Get battery info if available
    if ('getBattery' in navigator) {
      navigator.getBattery().then((battery) => {
        setBatteryLevel(Math.round(battery.level * 100));
        battery.addEventListener('levelchange', () => {
          setBatteryLevel(Math.round(battery.level * 100));
        });
      });
    }

    return () => {
      clearInterval(timeInterval);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const fetchTodayAttendance = async () => {
    try {
      setLoading(true);
      const response = await attendanceAPI.getTodayAttendance();
      setTodayAttendance(response.data);
    } catch (error) {
      console.error('Error fetching today attendance:', error);
      toast.error('Failed to fetch attendance data');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      setLocationLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          };
          setCurrentLocation(location);
          setLocationLoading(false);
          resolve(location);
        },
        (error) => {
          setLocationLoading(false);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000
        }
      );
    });
  };

  const handleCheckIn = async () => {
    try {
      setCheckingIn(true);
      
      // Get current location
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
        if (!confirm('Location access failed. Continue check-in without location?')) {
          return;
        }
      }

      const checkInData = {
        location: location,
        notes: '',
        device_info: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          battery: batteryLevel,
          online: isOnline
        }
      };

      await attendanceAPI.checkIn(checkInData);
      toast.success('Checked in successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-in error:', error);
      toast.error(error.response?.data?.message || 'Failed to check in');
    } finally {
      setCheckingIn(false);
    }
  };

  const handleCheckOut = async () => {
    try {
      setCheckingOut(true);
      
      // Get current location
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
      }

      const checkOutData = {
        location: location,
        notes: '',
        device_info: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          battery: batteryLevel,
          online: isOnline
        }
      };

      await attendanceAPI.checkOut(checkOutData);
      toast.success('Checked out successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-out error:', error);
      toast.error(error.response?.data?.message || 'Failed to check out');
    } finally {
      setCheckingOut(false);
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return '--:--';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getCurrentHours = () => {
    if (!todayAttendance?.attendanceRecord?.check_in_time) return '0.00';
    
    const checkIn = new Date(todayAttendance.attendanceRecord.check_in_time);
    const now = todayAttendance.attendanceRecord.check_out_time 
      ? new Date(todayAttendance.attendanceRecord.check_out_time)
      : new Date();
    
    const diffMs = now - checkIn;
    const diffHours = diffMs / (1000 * 60 * 60);
    return Math.max(0, diffHours).toFixed(2);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center p-4">
        <Spinner size="lg" />
      </div>
    );
  }

  const { hasCheckedIn, hasCheckedOut, attendanceRecord } = todayAttendance || {};

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-xl font-bold text-gray-900">Attendance</h1>
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
            {batteryLevel !== null && (
              <div className="flex items-center space-x-1">
                <Battery className="h-4 w-4 text-gray-600" />
                <span className="text-xs text-gray-600">{batteryLevel}%</span>
              </div>
            )}
          </div>
        </div>
        <p className="text-gray-600">Welcome back, {user?.first_name}!</p>
      </div>

      {/* Current Time */}
      <Card className="p-6 mb-6 text-center">
        <div className="text-3xl font-bold text-gray-900 mb-2">
          {currentTime.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
          })}
        </div>
        <div className="text-gray-600">
          {currentTime.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}
        </div>
      </Card>

      {/* Quick Action */}
      <Card className="p-6 mb-6">
        <div className="text-center">
          {!hasCheckedIn ? (
            <div>
              <div className="mb-4">
                <Clock className="mx-auto h-16 w-16 text-green-600 mb-2" />
                <h3 className="text-lg font-semibold text-gray-900">Ready to Check In?</h3>
                <p className="text-gray-600 text-sm">Start your workday</p>
              </div>
              
              <Button
                onClick={handleCheckIn}
                disabled={checkingIn || locationLoading}
                className="w-full bg-green-600 hover:bg-green-700 py-4 text-lg"
                size="lg"
              >
                {checkingIn ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Checking In...
                  </>
                ) : (
                  <>
                    <Clock className="mr-2 h-5 w-5" />
                    Check In
                  </>
                )}
              </Button>
              
              {locationLoading && (
                <div className="flex items-center justify-center mt-3 text-sm text-gray-500">
                  <MapPin className="mr-1 h-4 w-4" />
                  Getting location...
                </div>
              )}
            </div>
          ) : !hasCheckedOut ? (
            <div>
              <div className="mb-4">
                <Clock className="mx-auto h-16 w-16 text-red-600 mb-2" />
                <h3 className="text-lg font-semibold text-gray-900">Ready to Check Out?</h3>
                <p className="text-gray-600 text-sm">End your workday</p>
              </div>
              
              <Button
                onClick={handleCheckOut}
                disabled={checkingOut || locationLoading}
                className="w-full bg-red-600 hover:bg-red-700 py-4 text-lg"
                size="lg"
              >
                {checkingOut ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Checking Out...
                  </>
                ) : (
                  <>
                    <Clock className="mr-2 h-5 w-5" />
                    Check Out
                  </>
                )}
              </Button>
              
              {locationLoading && (
                <div className="flex items-center justify-center mt-3 text-sm text-gray-500">
                  <MapPin className="mr-1 h-4 w-4" />
                  Getting location...
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="mb-4">
                <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-2">
                  <span className="text-2xl">✓</span>
                </div>
                <h3 className="text-lg font-semibold text-green-600">Day Complete!</h3>
                <p className="text-gray-600 text-sm">You have completed your attendance for today</p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Today's Summary */}
      <Card className="p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Today's Summary</h3>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Status:</span>
            {attendanceRecord ? (
              <Badge className="bg-green-100 text-green-800">
                {attendanceRecord.status.replace('_', ' ').toUpperCase()}
              </Badge>
            ) : (
              <Badge className="bg-gray-100 text-gray-800">NOT MARKED</Badge>
            )}
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Check In:</span>
            <span className="font-medium">
              {formatTime(attendanceRecord?.check_in_time)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Check Out:</span>
            <span className="font-medium">
              {formatTime(attendanceRecord?.check_out_time)}
            </span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Hours Worked:</span>
            <span className="font-medium text-blue-600">
              {getCurrentHours()} hrs
            </span>
          </div>
        </div>
      </Card>

      {/* Location Info */}
      {currentLocation && (
        <Card className="p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Location Info</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Latitude:</span>
              <span className="font-mono">{currentLocation.latitude.toFixed(6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Longitude:</span>
              <span className="font-mono">{currentLocation.longitude.toFixed(6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Accuracy:</span>
              <span>{Math.round(currentLocation.accuracy)}m</span>
            </div>
          </div>
        </Card>
      )}

      {/* Weekly Stats */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">This Week</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">5</div>
            <div className="text-sm text-gray-600">Present</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">1</div>
            <div className="text-sm text-gray-600">Late</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">42.5</div>
            <div className="text-sm text-gray-600">Hours</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">2.5</div>
            <div className="text-sm text-gray-600">Overtime</div>
          </div>
        </div>
      </Card>

      {/* Offline Notice */}
      {!isOnline && (
        <div className="fixed bottom-4 left-4 right-4 bg-red-600 text-white p-3 rounded-lg shadow-lg">
          <div className="flex items-center">
            <WifiOff className="h-4 w-4 mr-2" />
            <span className="text-sm">You're offline. Some features may not work.</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileAttendance;
