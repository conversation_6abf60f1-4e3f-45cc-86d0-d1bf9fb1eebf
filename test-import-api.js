// Simple test for the import API functionality
import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:3001/api/v1';

async function testImportAPI() {
  try {
    console.log('🧪 Testing Customer Import API...');
    
    // Step 1: Test authentication
    console.log('\n1. Testing authentication...');
    const authResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    if (!authResponse.ok) {
      console.error('❌ Authentication failed:', authResponse.status, authResponse.statusText);
      const errorText = await authResponse.text();
      console.error('Error details:', errorText);
      return;
    }
    
    const authData = await authResponse.json();
    console.log('✅ Authentication successful');
    console.log('User details:', {
      id: authData.user?.id,
      email: authData.user?.email,
      roles: authData.user?.roles,
      tenant: authData.user?.tenant?.name
    });
    const token = authData.token;
    
    // Step 2: Test import preview endpoint
    console.log('\n2. Testing import preview endpoint...');
    
    // Create form data with the CSV file
    const form = new FormData();
    const csvPath = path.join(process.cwd(), 'sample_customer_import.csv');
    
    if (!fs.existsSync(csvPath)) {
      console.error('❌ CSV file not found:', csvPath);
      return;
    }
    
    form.append('file', fs.createReadStream(csvPath));
    
    const previewResponse = await fetch(`${API_BASE}/customers/import/preview`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders()
      },
      body: form
    });

    console.log('Preview response status:', previewResponse.status);
    console.log('Preview response headers:', Object.fromEntries(previewResponse.headers.entries()));

    const previewData = await previewResponse.text();
    console.log('Preview response body:', previewData);

    // Try to parse as JSON for better error details
    try {
      const jsonData = JSON.parse(previewData);
      console.log('Parsed JSON response:', JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
    
    if (previewResponse.ok) {
      console.log('✅ Import preview successful');
      const parsedData = JSON.parse(previewData);
      console.log('📊 Preview results:', {
        totalRows: parsedData.data?.totalRows,
        validRows: parsedData.data?.validRows,
        errorRows: parsedData.data?.errorRows,
        duplicateRows: parsedData.data?.duplicateRows
      });
    } else {
      console.error('❌ Import preview failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testImportAPI();
