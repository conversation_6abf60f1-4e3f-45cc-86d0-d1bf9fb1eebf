/**
 * Country codes data for international phone number support
 * Contains 195+ countries with their respective country codes and flags
 */

export const countryCodes = [
  { country: 'India', code: '+91', flag: '🇮🇳' },
  { country: 'United States', code: '+1', flag: '🇺🇸' },
  { country: 'United Kingdom', code: '+44', flag: '🇬🇧' },
  { country: 'Canada', code: '+1', flag: '🇨🇦' },
  { country: 'Australia', code: '+61', flag: '🇦🇺' },
  { country: 'Germany', code: '+49', flag: '🇩🇪' },
  { country: 'France', code: '+33', flag: '🇫🇷' },
  { country: 'Italy', code: '+39', flag: '🇮🇹' },
  { country: 'Spain', code: '+34', flag: '🇪🇸' },
  { country: 'Netherlands', code: '+31', flag: '🇳🇱' },
  { country: 'Belgium', code: '+32', flag: '🇧🇪' },
  { country: 'Switzerland', code: '+41', flag: '🇨🇭' },
  { country: 'Austria', code: '+43', flag: '🇦🇹' },
  { country: 'Sweden', code: '+46', flag: '🇸🇪' },
  { country: 'Norway', code: '+47', flag: '🇳🇴' },
  { country: 'Denmark', code: '+45', flag: '🇩🇰' },
  { country: 'Finland', code: '+358', flag: '🇫🇮' },
  { country: 'Poland', code: '+48', flag: '🇵🇱' },
  { country: 'Czech Republic', code: '+420', flag: '🇨🇿' },
  { country: 'Hungary', code: '+36', flag: '🇭🇺' },
  { country: 'Portugal', code: '+351', flag: '🇵🇹' },
  { country: 'Greece', code: '+30', flag: '🇬🇷' },
  { country: 'Turkey', code: '+90', flag: '🇹🇷' },
  { country: 'Russia', code: '+7', flag: '🇷🇺' },
  { country: 'China', code: '+86', flag: '🇨🇳' },
  { country: 'Japan', code: '+81', flag: '🇯🇵' },
  { country: 'South Korea', code: '+82', flag: '🇰🇷' },
  { country: 'Singapore', code: '+65', flag: '🇸🇬' },
  { country: 'Malaysia', code: '+60', flag: '🇲🇾' },
  { country: 'Thailand', code: '+66', flag: '🇹🇭' },
  { country: 'Indonesia', code: '+62', flag: '🇮🇩' },
  { country: 'Philippines', code: '+63', flag: '🇵🇭' },
  { country: 'Vietnam', code: '+84', flag: '🇻🇳' },
  { country: 'Hong Kong', code: '+852', flag: '🇭🇰' },
  { country: 'Taiwan', code: '+886', flag: '🇹🇼' },
  { country: 'Pakistan', code: '+92', flag: '🇵🇰' },
  { country: 'Bangladesh', code: '+880', flag: '🇧🇩' },
  { country: 'Sri Lanka', code: '+94', flag: '🇱🇰' },
  { country: 'Nepal', code: '+977', flag: '🇳🇵' },
  { country: 'Bhutan', code: '+975', flag: '🇧🇹' },
  { country: 'Maldives', code: '+960', flag: '🇲🇻' },
  { country: 'Afghanistan', code: '+93', flag: '🇦🇫' },
  { country: 'Iran', code: '+98', flag: '🇮🇷' },
  { country: 'Iraq', code: '+964', flag: '🇮🇶' },
  { country: 'Israel', code: '+972', flag: '🇮🇱' },
  { country: 'Jordan', code: '+962', flag: '🇯🇴' },
  { country: 'Lebanon', code: '+961', flag: '🇱🇧' },
  { country: 'Syria', code: '+963', flag: '🇸🇾' },
  { country: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
  { country: 'United Arab Emirates', code: '+971', flag: '🇦🇪' },
  { country: 'Qatar', code: '+974', flag: '🇶🇦' },
  { country: 'Kuwait', code: '+965', flag: '🇰🇼' },
  { country: 'Bahrain', code: '+973', flag: '🇧🇭' },
  { country: 'Oman', code: '+968', flag: '🇴🇲' },
  { country: 'Yemen', code: '+967', flag: '🇾🇪' },
  { country: 'Egypt', code: '+20', flag: '🇪🇬' },
  { country: 'Libya', code: '+218', flag: '🇱🇾' },
  { country: 'Tunisia', code: '+216', flag: '🇹🇳' },
  { country: 'Algeria', code: '+213', flag: '🇩🇿' },
  { country: 'Morocco', code: '+212', flag: '🇲🇦' },
  { country: 'Sudan', code: '+249', flag: '🇸🇩' },
  { country: 'Ethiopia', code: '+251', flag: '🇪🇹' },
  { country: 'Kenya', code: '+254', flag: '🇰🇪' },
  { country: 'Uganda', code: '+256', flag: '🇺🇬' },
  { country: 'Tanzania', code: '+255', flag: '🇹🇿' },
  { country: 'Rwanda', code: '+250', flag: '🇷🇼' },
  { country: 'Burundi', code: '+257', flag: '🇧🇮' },
  { country: 'Democratic Republic of Congo', code: '+243', flag: '🇨🇩' },
  { country: 'Republic of Congo', code: '+242', flag: '🇨🇬' },
  { country: 'Central African Republic', code: '+236', flag: '🇨🇫' },
  { country: 'Chad', code: '+235', flag: '🇹🇩' },
  { country: 'Cameroon', code: '+237', flag: '🇨🇲' },
  { country: 'Nigeria', code: '+234', flag: '🇳🇬' },
  { country: 'Ghana', code: '+233', flag: '🇬🇭' },
  { country: 'Ivory Coast', code: '+225', flag: '🇨🇮' },
  { country: 'Burkina Faso', code: '+226', flag: '🇧🇫' },
  { country: 'Mali', code: '+223', flag: '🇲🇱' },
  { country: 'Niger', code: '+227', flag: '🇳🇪' },
  { country: 'Senegal', code: '+221', flag: '🇸🇳' },
  { country: 'Guinea', code: '+224', flag: '🇬🇳' },
  { country: 'Sierra Leone', code: '+232', flag: '🇸🇱' },
  { country: 'Liberia', code: '+231', flag: '🇱🇷' },
  { country: 'Gambia', code: '+220', flag: '🇬🇲' },
  { country: 'Guinea-Bissau', code: '+245', flag: '🇬🇼' },
  { country: 'Cape Verde', code: '+238', flag: '🇨🇻' },
  { country: 'Mauritania', code: '+222', flag: '🇲🇷' },
  { country: 'Western Sahara', code: '+212', flag: '🇪🇭' },
  { country: 'South Africa', code: '+27', flag: '🇿🇦' },
  { country: 'Namibia', code: '+264', flag: '🇳🇦' },
  { country: 'Botswana', code: '+267', flag: '🇧🇼' },
  { country: 'Zimbabwe', code: '+263', flag: '🇿🇼' },
  { country: 'Zambia', code: '+260', flag: '🇿🇲' },
  { country: 'Malawi', code: '+265', flag: '🇲🇼' },
  { country: 'Mozambique', code: '+258', flag: '🇲🇿' },
  { country: 'Madagascar', code: '+261', flag: '🇲🇬' },
  { country: 'Mauritius', code: '+230', flag: '🇲🇺' },
  { country: 'Seychelles', code: '+248', flag: '🇸🇨' },
  { country: 'Comoros', code: '+269', flag: '🇰🇲' },
  { country: 'Mayotte', code: '+262', flag: '🇾🇹' },
  { country: 'Reunion', code: '+262', flag: '🇷🇪' },
  { country: 'Saint Helena', code: '+290', flag: '🇸🇭' },
  { country: 'Brazil', code: '+55', flag: '🇧🇷' },
  { country: 'Argentina', code: '+54', flag: '🇦🇷' },
  { country: 'Chile', code: '+56', flag: '🇨🇱' },
  { country: 'Colombia', code: '+57', flag: '🇨🇴' },
  { country: 'Peru', code: '+51', flag: '🇵🇪' },
  { country: 'Venezuela', code: '+58', flag: '🇻🇪' },
  { country: 'Ecuador', code: '+593', flag: '🇪🇨' },
  { country: 'Bolivia', code: '+591', flag: '🇧🇴' },
  { country: 'Paraguay', code: '+595', flag: '🇵🇾' },
  { country: 'Uruguay', code: '+598', flag: '🇺🇾' },
  { country: 'Guyana', code: '+592', flag: '🇬🇾' },
  { country: 'Suriname', code: '+597', flag: '🇸🇷' },
  { country: 'French Guiana', code: '+594', flag: '🇬🇫' },
  { country: 'Mexico', code: '+52', flag: '🇲🇽' },
  { country: 'Guatemala', code: '+502', flag: '🇬🇹' },
  { country: 'Belize', code: '+501', flag: '🇧🇿' },
  { country: 'El Salvador', code: '+503', flag: '🇸🇻' },
  { country: 'Honduras', code: '+504', flag: '🇭🇳' },
  { country: 'Nicaragua', code: '+505', flag: '🇳🇮' },
  { country: 'Costa Rica', code: '+506', flag: '🇨🇷' },
  { country: 'Panama', code: '+507', flag: '🇵🇦' },
  { country: 'Cuba', code: '+53', flag: '🇨🇺' },
  { country: 'Jamaica', code: '+1876', flag: '🇯🇲' },
  { country: 'Haiti', code: '+509', flag: '🇭🇹' },
  { country: 'Dominican Republic', code: '+1809', flag: '🇩🇴' },
  { country: 'Puerto Rico', code: '+1787', flag: '🇵🇷' },
  { country: 'Trinidad and Tobago', code: '+1868', flag: '🇹🇹' },
  { country: 'Barbados', code: '+1246', flag: '🇧🇧' },
  { country: 'Saint Lucia', code: '+1758', flag: '🇱🇨' },
  { country: 'Grenada', code: '+1473', flag: '🇬🇩' },
  { country: 'Saint Vincent and the Grenadines', code: '+1784', flag: '🇻🇨' },
  { country: 'Antigua and Barbuda', code: '+1268', flag: '🇦🇬' },
  { country: 'Dominica', code: '+1767', flag: '🇩🇲' },
  { country: 'Saint Kitts and Nevis', code: '+1869', flag: '🇰🇳' },
  { country: 'Bahamas', code: '+1242', flag: '🇧🇸' },
  { country: 'Bermuda', code: '+1441', flag: '🇧🇲' },
  { country: 'Cayman Islands', code: '+1345', flag: '🇰🇾' },
  { country: 'Turks and Caicos Islands', code: '+1649', flag: '🇹🇨' },
  { country: 'British Virgin Islands', code: '+1284', flag: '🇻🇬' },
  { country: 'US Virgin Islands', code: '+1340', flag: '🇻🇮' },
  { country: 'Anguilla', code: '+1264', flag: '🇦🇮' },
  { country: 'Montserrat', code: '+1664', flag: '🇲🇸' },
  { country: 'Guadeloupe', code: '+590', flag: '🇬🇵' },
  { country: 'Martinique', code: '+596', flag: '🇲🇶' },
  { country: 'Saint Barthelemy', code: '+590', flag: '🇧🇱' },
  { country: 'Saint Martin', code: '+590', flag: '🇲🇫' },
  { country: 'Aruba', code: '+297', flag: '🇦🇼' },
  { country: 'Curacao', code: '+599', flag: '🇨🇼' },
  { country: 'Sint Maarten', code: '+1721', flag: '🇸🇽' },
  { country: 'Bonaire', code: '+599', flag: '🇧🇶' },
  { country: 'Saba', code: '+599', flag: '🇧🇶' },
  { country: 'Sint Eustatius', code: '+599', flag: '🇧🇶' },
  { country: 'New Zealand', code: '+64', flag: '🇳🇿' },
  { country: 'Fiji', code: '+679', flag: '🇫🇯' },
  { country: 'Papua New Guinea', code: '+675', flag: '🇵🇬' },
  { country: 'Solomon Islands', code: '+677', flag: '🇸🇧' },
  { country: 'Vanuatu', code: '+678', flag: '🇻🇺' },
  { country: 'New Caledonia', code: '+687', flag: '🇳🇨' },
  { country: 'French Polynesia', code: '+689', flag: '🇵🇫' },
  { country: 'Wallis and Futuna', code: '+681', flag: '🇼🇫' },
  { country: 'Samoa', code: '+685', flag: '🇼🇸' },
  { country: 'American Samoa', code: '+1684', flag: '🇦🇸' },
  { country: 'Tonga', code: '+676', flag: '🇹🇴' },
  { country: 'Niue', code: '+683', flag: '🇳🇺' },
  { country: 'Cook Islands', code: '+682', flag: '🇨🇰' },
  { country: 'Kiribati', code: '+686', flag: '🇰🇮' },
  { country: 'Tuvalu', code: '+688', flag: '🇹🇻' },
  { country: 'Nauru', code: '+674', flag: '🇳🇷' },
  { country: 'Marshall Islands', code: '+692', flag: '🇲🇭' },
  { country: 'Micronesia', code: '+691', flag: '🇫🇲' },
  { country: 'Palau', code: '+680', flag: '🇵🇼' },
  { country: 'Northern Mariana Islands', code: '+1670', flag: '🇲🇵' },
  { country: 'Guam', code: '+1671', flag: '🇬🇺' },
  { country: 'Wake Island', code: '+1808', flag: '🇺🇲' },
  { country: 'Midway Islands', code: '+1808', flag: '🇺🇲' },
  { country: 'Johnston Atoll', code: '+1808', flag: '🇺🇲' },
  { country: 'Howland Island', code: '+1808', flag: '🇺🇲' },
  { country: 'Baker Island', code: '+1808', flag: '🇺🇲' },
  { country: 'Jarvis Island', code: '+1808', flag: '🇺🇲' },
  { country: 'Kingman Reef', code: '+1808', flag: '🇺🇲' },
  { country: 'Palmyra Atoll', code: '+1808', flag: '🇺🇲' },
  { country: 'Navassa Island', code: '+1808', flag: '🇺🇲' },
  { country: 'Antarctica', code: '+672', flag: '🇦🇶' }
];

// Default country code (India)
export const defaultCountryCode = '+91';

// Helper function to find country by code
export const findCountryByCode = (code) => {
  return countryCodes.find(country => country.code === code) || countryCodes[0];
};

// Helper function to get all unique country codes
export const getUniqueCountryCodes = () => {
  const uniqueCodes = [...new Set(countryCodes.map(country => country.code))];
  return uniqueCodes.sort();
};
