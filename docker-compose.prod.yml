version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tallycrm-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${DB_NAME:-tallycrm_prod}
      POSTGRES_USER: ${DB_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./database/scripts:/docker-entrypoint-initdb.d
    networks:
      - tallycrm-prod-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME:-postgres} -d ${DB_NAME:-tallycrm_prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis (for sessions and caching)
  redis:
    image: redis:7-alpine
    container_name: tallycrm-redis-prod
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_prod_data:/data
    networks:
      - tallycrm-prod-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # TallyCRM Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: tallycrm-app-prod
    restart: always
    env_file:
      - .env.prod
    environment:
      NODE_ENV: production
      PORT: 8080
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-tallycrm_prod}
      DB_USERNAME: ${DB_USERNAME:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_DIALECT: postgres
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      APP_URL: ${APP_URL:-https://yourdomain.com}
      ENABLE_CORS: false
      ENABLE_RATE_LIMITING: true
    ports:
      - "${APP_PORT:-8080}:8080"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    networks:
      - tallycrm-prod-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: tallycrm-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - tallycrm-prod-network
    depends_on:
      - app
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local

networks:
  tallycrm-prod-network:
    driver: bridge
