import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Toaster } from 'react-hot-toast';

// Layout Components
import MainLayout from './components/layout/MainLayout';
import AuthLayout from './components/layout/AuthLayout';
import LoadingScreen from './components/ui/LoadingScreen';

// Page Components
import Dashboard from './pages/Dashboard';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import NotFound from './pages/NotFound';

// Route Components
import CustomerRoutes from './pages/customers/CustomerRoutes';
import LeadRoutes from './pages/leads/LeadRoutes';
import ServiceRoutes from './pages/services/ServiceRoutes';
import SalesRoutes from './pages/sales/SalesRoutes';
import MastersRoutes from './pages/masters/MastersRoutes';
import ReportsRoutes from './pages/reports/ReportsRoutes';
import AttendanceRoutes from './pages/attendance/AttendanceRoutes';

import Settings from './pages/Settings';
import Profile from './pages/Profile';

// SaaS Components
import SubscriptionPlans from './pages/billing/SubscriptionPlans';
import BillingDashboard from './pages/billing/BillingDashboard';
import BillingHistory from './pages/billing/BillingHistory';
import CheckoutSuccess from './pages/billing/CheckoutSuccess';

// Hooks
import { useAuth } from './hooks/useAuth';
import { useUserPreferences } from './hooks/useUserPreferences';

// Utils
import { ProtectedRoute } from './utils/ProtectedRoute';
import themeManager from './utils/themeManager';

// Services
import socketService from './services/socketService';

// Debug components (remove in production)
// import WebSocketTest from './components/debug/WebSocketTest';

// Styles
import './styles/App.css';

function App() {
  const { isAuthenticated, isLoading } = useAuth();

  // Load user preferences including theme color
  const { preferences } = useUserPreferences(isAuthenticated);

  // Initialize theme manager
  React.useEffect(() => {
    // Always initialize theme manager
    themeManager.init();

    // Apply default theme if no theme is set
    const currentColor = themeManager.getCurrentPrimaryColor();
    if (currentColor) {
      themeManager.applyTheme(currentColor);
    }
  }, []);

  // Initialize WebSocket connection when user is authenticated
  React.useEffect(() => {
    if (isAuthenticated && !isLoading) {
      console.log('🔌 App: Initializing WebSocket connection...');
      socketService.connect();
    }

    // Cleanup on unmount or when user logs out
    return () => {
      if (!isAuthenticated) {
        console.log('🔌 App: Disconnecting WebSocket due to logout...');
        socketService.disconnect();
      }
    };
  }, [isAuthenticated, isLoading]);

  // Debug logging
  console.log('App render - isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);

  if (isLoading) {
    return (
      <LoadingScreen
        title="Loading TallyCRM..."
        subtitle="Initializing your CRM workspace"
        variant="dashboard"
      />
    );
  }

  return (
    <>
      <Helmet>
        <title>Preminfo - CRM for Tally Resellers</title>
        <meta name="description" content="Complete CRM solution for Tally Software Resellers" />
      </Helmet>

      <div className="App">
        <Routes>
          {/* Root redirect */}
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <Navigate to="/auth/login" replace />
              )
            }
          />

          {/* Public Routes */}
          <Route path="/auth" element={<AuthLayout />}>
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
            <Route path="forgot-password" element={<ForgotPassword />} />
            <Route index element={<Navigate to="login" replace />} />
          </Route>

          {/* Protected Routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Dashboard />} />
          </Route>

          <Route
            path="/customers/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<CustomerRoutes />} />
          </Route>

          <Route
            path="/leads/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<LeadRoutes />} />
          </Route>

          <Route
            path="/services/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<ServiceRoutes />} />
          </Route>

          <Route
            path="/sales/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<SalesRoutes />} />
          </Route>

          <Route
            path="/attendance/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<AttendanceRoutes />} />
          </Route>

          <Route
            path="/masters/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<MastersRoutes />} />
          </Route>

          <Route
            path="/reports/*"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route path="*" element={<ReportsRoutes />} />
          </Route>



          <Route
            path="/settings"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Settings />} />
          </Route>

          <Route
            path="/profile"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Profile />} />
          </Route>

          {/* Billing Routes */}
          <Route
            path="/billing/plans"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<SubscriptionPlans />} />
          </Route>

          <Route
            path="/billing/dashboard"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<BillingDashboard />} />
          </Route>

          <Route
            path="/billing/history"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<BillingHistory />} />
          </Route>

          <Route
            path="/billing/success"
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MainLayout />
              </ProtectedRoute>
            }
          >
            <Route index element={<CheckoutSuccess />} />
          </Route>



          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              theme: {
                primary: 'green',
                secondary: 'black',
              },
            },
          }}
        />

        {/* Debug WebSocket Test Component - Remove in production */}
        {/* {import.meta.env.DEV && isAuthenticated && <WebSocketTest />} */}
      </div>
    </>
  );
}

export default App;
