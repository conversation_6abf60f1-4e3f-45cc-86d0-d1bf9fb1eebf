import express from 'express';
import { body } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getTenantSettings,
  updateTenantSettings,
  getUserPreferences,
  updateUserPreferences,
  getSystemSettings,
  updateSystemSettings,
} from '../controllers/settingsController.js';
import emailTemplateRoutes from './emailTemplateRoutes.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/settings/tenant
 * @desc    Get tenant settings
 * @access  Private (requires settings.read permission)
 */
router.get('/tenant', [
  requirePermission('settings.read'),
], getTenantSettings);

/**
 * @route   PUT /api/settings/tenant
 * @desc    Update tenant settings
 * @access  Private (requires settings.update permission)
 */
router.put('/tenant', [
  requirePermission('settings.update'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage('Phone must be between 10 and 20 characters'),
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Address must not exceed 200 characters'),
  body('city')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('City must not exceed 50 characters'),
  body('state')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('State must not exceed 50 characters'),
  body('country')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Country must not exceed 50 characters'),
  body('postal_code')
    .optional()
    .trim()
    .isLength({ max: 20 })
    .withMessage('Postal code must not exceed 20 characters'),
  body('timezone')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Timezone must not exceed 50 characters'),
  body('currency')
    .optional()
    .trim()
    .isLength({ min: 3, max: 3 })
    .withMessage('Currency must be a 3-character code'),
  body('date_format')
    .optional()
    .isIn(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'])
    .withMessage('Invalid date format'),
  body('time_format')
    .optional()
    .isIn(['12', '24'])
    .withMessage('Time format must be 12 or 24'),
  body('logo_url')
    .optional()
    .isURL()
    .withMessage('Logo URL must be a valid URL'),
  body('settings')
    .optional()
    .isObject()
    .withMessage('Settings must be an object'),
  validateRequest,
], updateTenantSettings);

/**
 * @route   GET /api/settings/user
 * @desc    Get user preferences
 * @access  Private
 */
router.get('/user', getUserPreferences);

/**
 * @route   PUT /api/settings/user
 * @desc    Update user preferences
 * @access  Private
 */
router.put('/user', [
  body('first_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('last_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage('Phone must be between 10 and 20 characters'),
  body('avatar_url')
    .optional()
    .isURL()
    .withMessage('Avatar URL must be a valid URL'),
  body('timezone')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Timezone must not exceed 50 characters'),
  body('language')
    .optional()
    .isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'hi'])
    .withMessage('Invalid language'),
  body('theme')
    .optional()
    .isIn(['light', 'dark', 'auto'])
    .withMessage('Theme must be light, dark, or auto'),
  body('notifications_enabled')
    .optional()
    .isBoolean()
    .withMessage('Notifications enabled must be a boolean'),
  body('email_notifications')
    .optional()
    .isBoolean()
    .withMessage('Email notifications must be a boolean'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('Preferences must be an object'),
  body('primary_color')
    .optional()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .withMessage('Primary color must be a valid hex color'),
  validateRequest,
], updateUserPreferences);

/**
 * @route   GET /api/settings/system
 * @desc    Get system settings
 * @access  Private (requires admin role)
 */
router.get('/system', [
  requirePermission('system.read'),
], getSystemSettings);

/**
 * @route   PUT /api/settings/system
 * @desc    Update system settings
 * @access  Private (requires admin role)
 */
router.put('/system', [
  requirePermission('system.update'),
  body('maintenance_mode')
    .optional()
    .isBoolean()
    .withMessage('Maintenance mode must be a boolean'),
  body('registration_enabled')
    .optional()
    .isBoolean()
    .withMessage('Registration enabled must be a boolean'),
  body('email_verification_required')
    .optional()
    .isBoolean()
    .withMessage('Email verification required must be a boolean'),
  body('max_file_upload_size')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Max file upload size must be a positive integer'),
  body('allowed_file_types')
    .optional()
    .isArray()
    .withMessage('Allowed file types must be an array'),
  body('session_timeout')
    .optional()
    .isInt({ min: 300 })
    .withMessage('Session timeout must be at least 300 seconds'),
  body('password_policy')
    .optional()
    .isObject()
    .withMessage('Password policy must be an object'),
  validateRequest,
], updateSystemSettings);

/**
 * @route   PUT /api/settings/notifications
 * @desc    Update notification preferences
 * @access  Private
 */
router.put('/notifications', [
  body('email_notifications')
    .optional()
    .isBoolean()
    .withMessage('Email notifications must be a boolean'),
  body('sms_notifications')
    .optional()
    .isBoolean()
    .withMessage('SMS notifications must be a boolean'),
  body('push_notifications')
    .optional()
    .isBoolean()
    .withMessage('Push notifications must be a boolean'),
  body('weekly_reports')
    .optional()
    .isBoolean()
    .withMessage('Weekly reports must be a boolean'),
  body('monthly_reports')
    .optional()
    .isBoolean()
    .withMessage('Monthly reports must be a boolean'),
  body('service_updates')
    .optional()
    .isBoolean()
    .withMessage('Service updates must be a boolean'),
  body('sales_alerts')
    .optional()
    .isBoolean()
    .withMessage('Sales alerts must be a boolean'),
  body('system_alerts')
    .optional()
    .isBoolean()
    .withMessage('System alerts must be a boolean'),
  validateRequest,
], updateUserPreferences);

/**
 * @route   PUT /api/settings/security
 * @desc    Update security settings
 * @access  Private
 */
router.put('/security', [
  body('two_factor_auth')
    .optional()
    .isBoolean()
    .withMessage('Two factor auth must be a boolean'),
  body('login_alerts')
    .optional()
    .isBoolean()
    .withMessage('Login alerts must be a boolean'),
  body('session_timeout')
    .optional()
    .isInt({ min: 5, max: 120 })
    .withMessage('Session timeout must be between 5 and 120 minutes'),
  body('password_expiry')
    .optional()
    .isInt({ min: 30, max: 365 })
    .withMessage('Password expiry must be between 30 and 365 days'),
  validateRequest,
], updateUserPreferences);

/**
 * @route   PUT /api/settings/backup
 * @desc    Update backup settings
 * @access  Private (requires admin role)
 */
router.put('/backup', [
  requirePermission('system.update'),
  body('backup_frequency')
    .optional()
    .isIn(['daily', 'weekly', 'monthly'])
    .withMessage('Backup frequency must be daily, weekly, or monthly'),
  body('retention_period')
    .optional()
    .isInt({ min: 7, max: 365 })
    .withMessage('Retention period must be between 7 and 365 days'),
  body('auto_backup')
    .optional()
    .isBoolean()
    .withMessage('Auto backup must be a boolean'),
  validateRequest,
], updateSystemSettings);

// Email template routes
router.use('/email-templates', emailTemplateRoutes);

export default router;
