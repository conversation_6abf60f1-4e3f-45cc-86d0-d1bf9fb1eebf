import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import NotificationService from './NotificationService.js';

class AttendanceNotificationService {
  constructor() {
    this.notificationService = new NotificationService();
  }

  /**
   * Send daily attendance reminder
   */
  async sendDailyReminder(tenantId, employees = null) {
    try {
      logger.info('Sending daily attendance reminders', { tenantId });

      // Get attendance settings
      const settings = await models.AttendanceSettings.findOne({
        where: { tenant_id: tenantId }
      });

      if (!settings || !settings.send_daily_reminders) {
        logger.info('Daily reminders disabled for tenant', { tenantId });
        return { success: true, message: 'Daily reminders disabled' };
      }

      // Get employees to notify
      const whereClause = {
        tenant_id: tenantId,
        is_active: true,
        attendance_tracking_enabled: true
      };

      if (employees && employees.length > 0) {
        whereClause.id = { [Op.in]: employees };
      }

      const employeeList = await models.Executive.findAll({
        where: whereClause,
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'phone']
        }]
      });

      const results = [];
      const today = new Date().toISOString().split('T')[0];

      for (const employee of employeeList) {
        try {
          // Check if already checked in today
          const todayAttendance = await models.AttendanceRecord.findOne({
            where: {
              tenant_id: tenantId,
              employee_id: employee.id,
              date: today
            }
          });

          if (todayAttendance && todayAttendance.check_in_time) {
            logger.debug('Employee already checked in, skipping reminder', {
              employeeId: employee.id,
              employeeName: employee.getFullName()
            });
            continue;
          }

          // Prepare notification data
          const notificationData = {
            tenantId: tenantId,
            employeeName: employee.getFullName(),
            employeeCode: employee.employee_code,
            date: new Date().toLocaleDateString(),
            reminderTime: settings.reminder_time || '08:45:00'
          };

          // Send email notification if user has email
          if (employee.user && employee.user.email) {
            const emailResult = await this.notificationService.sendEmailNotification(
              { email: employee.user.email, name: employee.getFullName() },
              'attendance_reminder',
              notificationData
            );

            results.push({
              employee_id: employee.id,
              employee_name: employee.getFullName(),
              email: emailResult
            });
          }

          // Send WhatsApp notification if user has phone
          if (employee.user && employee.user.phone) {
            // Note: WhatsApp templates would need to be created for attendance notifications
            logger.info('WhatsApp attendance reminder would be sent here', {
              employeeId: employee.id,
              phone: employee.user.phone
            });
          }

        } catch (employeeError) {
          logger.error('Error sending reminder to employee', {
            employeeId: employee.id,
            error: employeeError.message
          });
          results.push({
            employee_id: employee.id,
            employee_name: employee.getFullName(),
            error: employeeError.message
          });
        }
      }

      logger.info('Daily attendance reminders sent', {
        tenantId,
        totalEmployees: employeeList.length,
        notificationsSent: results.length
      });

      return {
        success: true,
        results: results,
        summary: {
          total_employees: employeeList.length,
          notifications_sent: results.filter(r => !r.error).length,
          errors: results.filter(r => r.error).length
        }
      };

    } catch (error) {
      logger.error('Error sending daily attendance reminders', { tenantId, error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send late arrival alert
   */
  async sendLateArrivalAlert(attendanceRecord) {
    try {
      const tenantId = attendanceRecord.tenant_id;

      // Get attendance settings
      const settings = await models.AttendanceSettings.findOne({
        where: { tenant_id: tenantId }
      });

      if (!settings || !settings.send_late_arrival_alerts) {
        return { success: true, message: 'Late arrival alerts disabled' };
      }

      // Get employee and manager details
      const employee = await models.Executive.findByPk(attendanceRecord.employee_id, {
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'phone']
        }]
      });

      if (!employee) {
        return { success: false, message: 'Employee not found' };
      }

      // Get managers/HR to notify
      const managers = await models.User.findAll({
        where: { tenant_id: tenantId },
        include: [{
          model: models.Role,
          as: 'roles',
          where: { slug: ['manager', 'hr', 'admin'] },
          through: { attributes: [] }
        }]
      });

      const notificationData = {
        tenantId: tenantId,
        employeeName: employee.getFullName(),
        employeeCode: employee.employee_code,
        checkInTime: attendanceRecord.check_in_time,
        date: attendanceRecord.date,
        lateMinutes: this.calculateLateMinutes(attendanceRecord, employee)
      };

      const results = [];

      // Notify managers
      for (const manager of managers) {
        if (manager.email) {
          const emailResult = await this.notificationService.sendEmailNotification(
            { email: manager.email, name: `${manager.first_name} ${manager.last_name}` },
            'late_arrival_alert',
            notificationData
          );

          results.push({
            recipient: 'manager',
            manager_id: manager.id,
            email: emailResult
          });
        }
      }

      logger.info('Late arrival alert sent', {
        employeeId: employee.id,
        employeeName: employee.getFullName(),
        managersNotified: results.length
      });

      return {
        success: true,
        results: results
      };

    } catch (error) {
      logger.error('Error sending late arrival alert', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send absence alert
   */
  async sendAbsenceAlert(tenantId, employeeId, date) {
    try {
      // Get attendance settings
      const settings = await models.AttendanceSettings.findOne({
        where: { tenant_id: tenantId }
      });

      if (!settings || !settings.send_absence_alerts) {
        return { success: true, message: 'Absence alerts disabled' };
      }

      // Get employee details
      const employee = await models.Executive.findByPk(employeeId, {
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'phone']
        }]
      });

      if (!employee) {
        return { success: false, message: 'Employee not found' };
      }

      // Get managers/HR to notify
      const managers = await models.User.findAll({
        where: { tenant_id: tenantId },
        include: [{
          model: models.Role,
          as: 'roles',
          where: { slug: ['manager', 'hr', 'admin'] },
          through: { attributes: [] }
        }]
      });

      const notificationData = {
        tenantId: tenantId,
        employeeName: employee.getFullName(),
        employeeCode: employee.employee_code,
        date: date,
        absentDate: new Date(date).toLocaleDateString()
      };

      const results = [];

      // Notify managers
      for (const manager of managers) {
        if (manager.email) {
          const emailResult = await this.notificationService.sendEmailNotification(
            { email: manager.email, name: `${manager.first_name} ${manager.last_name}` },
            'absence_alert',
            notificationData
          );

          results.push({
            recipient: 'manager',
            manager_id: manager.id,
            email: emailResult
          });
        }
      }

      logger.info('Absence alert sent', {
        employeeId: employeeId,
        employeeName: employee.getFullName(),
        date: date,
        managersNotified: results.length
      });

      return {
        success: true,
        results: results
      };

    } catch (error) {
      logger.error('Error sending absence alert', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send leave request notification
   */
  async sendLeaveRequestNotification(leaveRequest) {
    try {
      const tenantId = leaveRequest.tenant_id;

      // Get employee and leave type details
      const employee = await models.Executive.findByPk(leaveRequest.employee_id);
      const leaveType = await models.LeaveType.findByPk(leaveRequest.leave_type_id);

      if (!employee || !leaveType) {
        return { success: false, message: 'Employee or leave type not found' };
      }

      // Get managers/HR to notify for approval
      const approvers = await models.User.findAll({
        where: { tenant_id: tenantId },
        include: [{
          model: models.Role,
          as: 'roles',
          where: { slug: ['manager', 'hr', 'admin'] },
          through: { attributes: [] }
        }]
      });

      const notificationData = {
        tenantId: tenantId,
        employeeName: employee.getFullName(),
        employeeCode: employee.employee_code,
        leaveType: leaveType.name,
        startDate: leaveRequest.start_date,
        endDate: leaveRequest.end_date,
        totalDays: leaveRequest.total_days,
        reason: leaveRequest.reason,
        requestNumber: leaveRequest.request_number,
        priority: leaveRequest.priority
      };

      const results = [];

      // Notify approvers
      for (const approver of approvers) {
        if (approver.email) {
          const emailResult = await this.notificationService.sendEmailNotification(
            { email: approver.email, name: `${approver.first_name} ${approver.last_name}` },
            'leave_request_notification',
            notificationData
          );

          results.push({
            recipient: 'approver',
            approver_id: approver.id,
            email: emailResult
          });
        }
      }

      logger.info('Leave request notification sent', {
        requestId: leaveRequest.id,
        employeeId: leaveRequest.employee_id,
        approversNotified: results.length
      });

      return {
        success: true,
        results: results
      };

    } catch (error) {
      logger.error('Error sending leave request notification', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send leave approval/rejection notification
   */
  async sendLeaveStatusNotification(leaveRequest, status, approver) {
    try {
      // Get employee details
      const employee = await models.Executive.findByPk(leaveRequest.employee_id, {
        include: [{
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'phone']
        }]
      });

      const leaveType = await models.LeaveType.findByPk(leaveRequest.leave_type_id);

      if (!employee || !leaveType) {
        return { success: false, message: 'Employee or leave type not found' };
      }

      const notificationData = {
        tenantId: leaveRequest.tenant_id,
        employeeName: employee.getFullName(),
        leaveType: leaveType.name,
        startDate: leaveRequest.start_date,
        endDate: leaveRequest.end_date,
        totalDays: leaveRequest.total_days,
        requestNumber: leaveRequest.request_number,
        status: status,
        approverName: `${approver.first_name} ${approver.last_name}`,
        managerComments: leaveRequest.manager_comments,
        rejectionReason: leaveRequest.rejection_reason
      };

      const results = [];

      // Notify employee
      if (employee.user && employee.user.email) {
        const eventType = status === 'approved' ? 'leave_approved' : 'leave_rejected';
        
        const emailResult = await this.notificationService.sendEmailNotification(
          { email: employee.user.email, name: employee.getFullName() },
          eventType,
          notificationData
        );

        results.push({
          recipient: 'employee',
          employee_id: employee.id,
          email: emailResult
        });
      }

      logger.info('Leave status notification sent', {
        requestId: leaveRequest.id,
        employeeId: leaveRequest.employee_id,
        status: status
      });

      return {
        success: true,
        results: results
      };

    } catch (error) {
      logger.error('Error sending leave status notification', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * Calculate late minutes for an attendance record
   */
  calculateLateMinutes(attendanceRecord, employee) {
    if (!attendanceRecord.check_in_time) return 0;

    const checkInTime = new Date(attendanceRecord.check_in_time);
    const shiftStartTime = employee.shift_start_time || '09:00:00';
    const shiftStart = new Date(`${attendanceRecord.date}T${shiftStartTime}`);

    if (checkInTime <= shiftStart) return 0;

    return Math.round((checkInTime - shiftStart) / (1000 * 60));
  }
}

export default AttendanceNotificationService;
