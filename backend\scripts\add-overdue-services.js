import models from '../src/models/index.js';
import { logger } from '../src/utils/logger.js';

async function addOverdueServices() {
  try {
    console.log('🔄 Adding test overdue service calls...');

    // Get the first customer and tenant
    const customer = await models.Customer.findOne();
    const tenant = await models.Tenant.findOne();
    const user = await models.User.findOne();
    
    if (!customer || !tenant || !user) {
      console.log('❌ No customer, tenant, or user found. Please create some first.');
      return;
    }

    // Get an open status
    const openStatus = await models.CallStatus.findOne({
      where: { category: 'open' }
    });

    if (!openStatus) {
      console.log('❌ No open status found');
      return;
    }

    console.log('✅ Found open status:', openStatus.name);

    // Create overdue service calls with different overdue periods
    const overdueServices = [
      {
        call_number: 'SC-OVERDUE-001',
        subject: 'Tally Installation Issue - 5 days overdue',
        description: 'Customer facing installation problems with Tally Prime',
        customer_id: customer.id,
        tenant_id: tenant.id,
        created_by: user.id,
        status_id: openStatus.id,
        call_date: new Date(),
        scheduled_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        call_billing_type: 'free_call',
        priority: 'high',
        call_type_id: null
      },
      {
        call_number: 'SC-OVERDUE-002', 
        subject: 'Data Backup Problem - 3 days overdue',
        description: 'Automatic backup not working properly',
        customer_id: customer.id,
        tenant_id: tenant.id,
        created_by: user.id,
        status_id: openStatus.id,
        call_date: new Date(),
        scheduled_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        call_billing_type: 'amc_call',
        priority: 'medium',
        call_type_id: null
      },
      {
        call_number: 'SC-OVERDUE-003',
        subject: 'Report Generation Error - 1 day overdue', 
        description: 'Unable to generate GST reports',
        customer_id: customer.id,
        tenant_id: tenant.id,
        created_by: user.id,
        status_id: openStatus.id,
        call_date: new Date(),
        scheduled_date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        call_billing_type: 'per_call',
        priority: 'low',
        call_type_id: null
      },
      {
        call_number: 'SC-OVERDUE-004',
        subject: 'Network Connectivity Issue - 7 days overdue',
        description: 'Tally not connecting to server',
        customer_id: customer.id,
        tenant_id: tenant.id,
        created_by: user.id,
        status_id: openStatus.id,
        call_date: new Date(),
        scheduled_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        call_billing_type: 'amc_call',
        priority: 'high',
        call_type_id: null
      }
    ];

    // Create the overdue services
    for (const serviceData of overdueServices) {
      try {
        const service = await models.ServiceCall.create(serviceData);
        console.log(`✅ Created overdue service: ${service.call_number} (${Math.ceil((new Date() - new Date(service.scheduled_date)) / (1000 * 60 * 60 * 24))} days overdue)`);
      } catch (error) {
        console.log(`❌ Failed to create service ${serviceData.call_number}:`, error.message);
      }
    }

    console.log('🎉 Finished adding overdue service calls!');
    
    // Query and display overdue services to verify
    const overdueCount = await models.ServiceCall.count({
      where: {
        scheduled_date: {
          [models.Sequelize.Op.lt]: new Date()
        },
        '$status.category$': 'open'
      },
      include: [{
        model: models.CallStatus,
        as: 'status',
        attributes: []
      }]
    });

    console.log(`📊 Total overdue services in database: ${overdueCount}`);

  } catch (error) {
    console.error('❌ Error adding overdue services:', error);
  } finally {
    process.exit(0);
  }
}

addOverdueServices();
