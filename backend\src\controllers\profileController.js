import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import bcrypt from 'bcrypt';

/**
 * Get user profile
 */
export const getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await models.User.findByPk(userId, {
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug', 'description'],
          include: [
            {
              model: models.Permission,
              as: 'permissions',
              attributes: ['id', 'name', 'slug', 'description'],
            },
          ],
        },
        {
          model: models.Tenant,
          as: 'tenant',
          attributes: ['id', 'name', 'domain'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar_url',
        'timezone',
        'language',
        'theme',
        'notifications_enabled',
        'email_notifications',
        'is_active',
        'is_verified',
        'last_login_at',
        'created_at',
        'updated_at',
      ],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Format user data
    const userData = {
      ...user.toJSON(),
      roles: user.roles?.map(role => ({
        id: role.id,
        name: role.name,
        slug: role.slug,
        description: role.description,
        permissions: role.permissions?.map(permission => ({
          id: permission.id,
          name: permission.name,
          slug: permission.slug,
          description: permission.description,
        })) || [],
      })) || [],
    };

    res.json({
      success: true,
      data: { user: userData },
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update user profile
 */
export const updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      first_name,
      last_name,
      phone,
      avatar_url,
      timezone,
      language,
      theme,
      notifications_enabled,
      email_notifications,
    } = req.body;

    const user = await models.User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Update user profile
    await user.update({
      ...(first_name && { first_name }),
      ...(last_name && { last_name }),
      ...(phone && { phone }),
      ...(avatar_url && { avatar_url }),
      ...(timezone && { timezone }),
      ...(language && { language }),
      ...(theme && { theme }),
      ...(notifications_enabled !== undefined && { notifications_enabled }),
      ...(email_notifications !== undefined && { email_notifications }),
    });

    logger.info('Profile updated:', {
      userId,
    });

    // Fetch updated user with associations
    const updatedUser = await models.User.findByPk(userId, {
      include: [
        {
          model: models.Role,
          as: 'roles',
          attributes: ['id', 'name', 'slug', 'description'],
        },
        {
          model: models.Tenant,
          as: 'tenant',
          attributes: ['id', 'name', 'domain'],
        },
      ],
      attributes: [
        'id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'avatar_url',
        'timezone',
        'language',
        'theme',
        'notifications_enabled',
        'email_notifications',
        'is_active',
        'is_verified',
        'last_login_at',
        'created_at',
        'updated_at',
      ],
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: updatedUser },
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Change password
 */
export const changePassword = async (req, res) => {
  try {
    const userId = req.user.id;
    const { current_password, new_password } = req.body;

    const user = await models.User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(current_password, user.password);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
      });
    }

    // Check if new password is different from current
    const isSamePassword = await bcrypt.compare(new_password, user.password);
    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        message: 'New password must be different from current password',
      });
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(new_password, saltRounds);

    // Update password
    await user.update({ password: hashedPassword });

    logger.info('Password changed:', {
      userId,
    });

    res.json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    logger.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to change password',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Upload avatar
 */
export const uploadAvatar = async (req, res) => {
  try {
    const userId = req.user.id;
    const { avatar_url } = req.body;

    if (!avatar_url) {
      return res.status(400).json({
        success: false,
        message: 'Avatar URL is required',
      });
    }

    const user = await models.User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Update avatar URL
    await user.update({ avatar_url });

    logger.info('Avatar uploaded:', {
      userId,
      avatarUrl: avatar_url,
    });

    res.json({
      success: true,
      message: 'Avatar uploaded successfully',
      data: { avatar_url },
    });
  } catch (error) {
    logger.error('Upload avatar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload avatar',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get user activity log
 */
export const getActivityLog = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;

    const offset = (page - 1) * limit;

    // This is a placeholder implementation
    // In a real application, you would have an activity log table
    const activities = [
      {
        id: 1,
        action: 'login',
        description: 'User logged in',
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: new Date(),
      },
      // Add more activity entries as needed
    ];

    res.json({
      success: true,
      data: {
        activities,
        pagination: {
          currentPage: parseInt(page),
          totalPages: 1,
          totalItems: activities.length,
          itemsPerPage: parseInt(limit),
        },
      },
    });
  } catch (error) {
    logger.error('Get activity log error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve activity log',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
