#!/usr/bin/env node

/**
 * Fix Notification Settings Script
 * 
 * This script adds the missing whatsapp_enabled field to existing notification settings
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env') });

// Import models after environment is loaded
let models;

try {
  const { default: modelsImport } = await import('./src/models/index.js');
  models = modelsImport;
} catch (error) {
  console.error('❌ Failed to import models:', error.message);
  process.exit(1);
}

async function main() {
  console.log('🔧 Fixing Notification Settings...\n');

  try {
    // Check current notification settings
    console.log('📋 Checking current notification settings...');
    const settings = await models.NotificationSettings.findAll();
    
    console.log(`Found ${settings.length} notification settings records`);

    for (const setting of settings) {
      console.log(`\n🔍 Record ID: ${setting.id}, Tenant: ${setting.tenant_id}`);
      console.log(`   whatsapp_enabled: ${setting.whatsapp_enabled}`);
      
      if (setting.whatsapp_enabled === undefined || setting.whatsapp_enabled === null) {
        console.log('   ⚠️  Missing whatsapp_enabled field, updating...');
        
        // Update the record to add whatsapp_enabled
        await setting.update({
          whatsapp_enabled: true
        });
        
        console.log('   ✅ Updated whatsapp_enabled to true');
      } else {
        console.log('   ✅ whatsapp_enabled field already exists');
      }
    }

    // Verify the fix
    console.log('\n🔍 Verifying the fix...');
    const updatedSettings = await models.NotificationSettings.findAll();
    
    for (const setting of updatedSettings) {
      console.log(`Record ID: ${setting.id} - whatsapp_enabled: ${setting.whatsapp_enabled}`);
    }

    console.log('\n✅ Notification settings fix completed!');

  } catch (error) {
    console.error('\n❌ Fix script failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the fix script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
