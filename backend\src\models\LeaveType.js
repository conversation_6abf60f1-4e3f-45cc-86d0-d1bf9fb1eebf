import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LeaveType = sequelize.define('LeaveType', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
      comment: 'Leave type name (e.g., Sick Leave, Annual Leave)',
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 20],
      },
      comment: 'Short code for leave type (e.g., SL, AL)',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed description of leave type',
    },
    annual_quota: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Annual quota of days for this leave type',
    },
    is_paid: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type is paid',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type requires approval',
    },
    advance_notice_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Minimum advance notice required in days',
    },
    max_consecutive_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum consecutive days allowed for this leave type',
    },
    min_days_per_request: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 0.5,
      comment: 'Minimum days per leave request',
    },
    max_days_per_request: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum days per leave request',
    },
    carry_forward_allowed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether unused leaves can be carried forward',
    },
    max_carry_forward: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Maximum days that can be carried forward',
    },
    carry_forward_expiry_months: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 12,
      comment: 'Months after which carried forward leaves expire',
    },
    encashment_allowed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether unused leaves can be encashed',
    },
    max_encashment_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Maximum days that can be encashed',
    },
    applicable_after_months: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Leave type applicable after these many months of employment',
    },
    gender_specific: {
      type: DataTypes.ENUM('all', 'male', 'female'),
      allowNull: false,
      defaultValue: 'all',
      comment: 'Gender applicability of leave type',
    },
    requires_document: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether supporting documents are required',
    },
    document_required_after_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Documents required if leave is more than these days',
    },
    allow_half_day: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether half-day leaves are allowed',
    },
    allow_negative_balance: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether negative balance is allowed',
    },
    max_negative_balance: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Maximum negative balance allowed',
    },
    color_code: {
      type: DataTypes.STRING(7),
      allowNull: true,
      validate: {
        is: /^#[0-9A-F]{6}$/i,
      },
      comment: 'Color code for calendar display',
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Sort order for display',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type is active',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'leave_types',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'code'],
        unique: true,
        name: 'unique_tenant_leave_code',
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  LeaveType.getDefaultLeaveTypes = function() {
    return [
      {
        name: 'Annual Leave',
        code: 'AL',
        description: 'Annual vacation leave',
        annual_quota: 21.0,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 7,
        carry_forward_allowed: true,
        max_carry_forward: 5,
        color_code: '#4CAF50',
        sort_order: 1,
      },
      {
        name: 'Sick Leave',
        code: 'SL',
        description: 'Medical leave for illness',
        annual_quota: 12.0,
        is_paid: true,
        requires_approval: false,
        advance_notice_days: 0,
        requires_document: true,
        document_required_after_days: 3,
        color_code: '#F44336',
        sort_order: 2,
      },
      {
        name: 'Casual Leave',
        code: 'CL',
        description: 'Short-term personal leave',
        annual_quota: 12.0,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 1,
        max_consecutive_days: 3,
        color_code: '#2196F3',
        sort_order: 3,
      },
      {
        name: 'Emergency Leave',
        code: 'EL',
        description: 'Emergency situations',
        annual_quota: 5.0,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 0,
        color_code: '#FF9800',
        sort_order: 4,
      },
      {
        name: 'Maternity Leave',
        code: 'ML',
        description: 'Maternity leave for female employees',
        annual_quota: 180.0,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 30,
        gender_specific: 'female',
        requires_document: true,
        color_code: '#E91E63',
        sort_order: 5,
      },
      {
        name: 'Paternity Leave',
        code: 'PL',
        description: 'Paternity leave for male employees',
        annual_quota: 15.0,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 7,
        gender_specific: 'male',
        color_code: '#9C27B0',
        sort_order: 6,
      },
      {
        name: 'Loss of Pay',
        code: 'LOP',
        description: 'Leave without pay',
        annual_quota: 0.0,
        is_paid: false,
        requires_approval: true,
        advance_notice_days: 1,
        allow_negative_balance: true,
        max_negative_balance: 30.0,
        color_code: '#607D8B',
        sort_order: 7,
      },
    ];
  };

  // Associations
  LeaveType.associate = function(models) {
    LeaveType.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    LeaveType.hasMany(models.LeaveRequest, {
      foreignKey: 'leave_type_id',
      as: 'leaveRequests',
    });

    LeaveType.hasMany(models.LeaveBalance, {
      foreignKey: 'leave_type_id',
      as: 'leaveBalances',
    });
  };

  return LeaveType;
}
