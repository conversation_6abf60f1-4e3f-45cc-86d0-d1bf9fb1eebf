import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const NotificationSchedule = sequelize.define('NotificationSchedule', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    renewal_type: {
      type: DataTypes.ENUM('amc', 'tss', 'license', 'maintenance', 'support'),
      allowNull: false,
      comment: 'Type of renewal (AMC, TSS, License, etc.)',
    },
    renewal_record_id: {
      type: DataTypes.UUID,
      allowNull: false,
      comment: 'ID of the specific renewal record (CustomerAMC.id, CustomerTSS.id, etc.)',
    },
    expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'The actual expiry date of the service',
    },
    notify_at: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Date when the notification should be sent',
    },
    days_before_expiry: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Number of days before expiry this notification is scheduled',
    },
    notification_type: {
      type: DataTypes.ENUM('reminder', 'overdue'),
      allowNull: false,
      defaultValue: 'reminder',
      comment: 'Type of notification (reminder or overdue)',
    },
    status: {
      type: DataTypes.ENUM('scheduled', 'sent', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'scheduled',
      comment: 'Status of the notification',
    },
    sent_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when notification was sent',
    },
    email_sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether email notification was sent',
    },
    sms_sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether SMS notification was sent',
    },
    whatsapp_sent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether WhatsApp notification was sent',
    },
    email_message_id: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Email message ID for tracking',
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Error message if notification failed',
    },
    retry_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Number of retry attempts',
    },
    max_retries: {
      type: DataTypes.INTEGER,
      defaultValue: 3,
      comment: 'Maximum number of retry attempts',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'notification_schedules',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['customer_id'],
      },
      {
        fields: ['renewal_type'],
      },
      {
        fields: ['renewal_record_id'],
      },
      {
        fields: ['expiry_date'],
      },
      {
        fields: ['notify_at'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['notification_type'],
      },
      {
        // Unique constraint to prevent duplicate notifications
        fields: ['customer_id', 'renewal_type', 'renewal_record_id', 'notify_at'],
        unique: true,
        name: 'unique_notification_schedule',
      },
      {
        // Index for efficient querying of pending notifications
        fields: ['notify_at', 'status'],
        where: {
          status: 'scheduled',
        },
      },
    ],
  });

  // Associations
  NotificationSchedule.associate = function(models) {
    NotificationSchedule.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    NotificationSchedule.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    NotificationSchedule.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });
  };

  // Instance methods
  NotificationSchedule.prototype.markAsSent = async function(channels = {}) {
    const updateData = {
      status: 'sent',
      sent_at: new Date(),
    };

    if (channels.email) {
      updateData.email_sent = true;
      updateData.email_message_id = channels.email.messageId;
    }
    if (channels.sms) updateData.sms_sent = true;
    if (channels.whatsapp) updateData.whatsapp_sent = true;

    return await this.update(updateData);
  };

  NotificationSchedule.prototype.markAsFailed = async function(errorMessage) {
    return await this.update({
      status: 'failed',
      error_message: errorMessage,
      retry_count: this.retry_count + 1,
    });
  };

  NotificationSchedule.prototype.canRetry = function() {
    return this.retry_count < this.max_retries && this.status === 'failed';
  };

  NotificationSchedule.prototype.isOverdue = function() {
    return new Date(this.expiry_date) < new Date() && this.notification_type === 'reminder';
  };

  // Static methods
  NotificationSchedule.getPendingNotifications = async function(date = null) {
    const targetDate = date || new Date().toISOString().split('T')[0];

    return await this.findAll({
      where: {
        notify_at: {
          [sequelize.Sequelize.Op.lte]: targetDate,
        },
        status: 'scheduled',
      },
      include: [
        {
          model: sequelize.models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'email', 'phone'],
        },
        {
          model: sequelize.models.Tenant,
          as: 'tenant',
          attributes: ['id', 'name'],
        },
      ],
      order: [['notify_at', 'ASC'], ['created_at', 'ASC']],
    });
  };

  NotificationSchedule.getFailedNotifications = async function() {
    return await this.findAll({
      where: {
        status: 'failed',
        retry_count: {
          [sequelize.Sequelize.Op.lt]: sequelize.Sequelize.col('max_retries'),
        },
      },
      include: [
        {
          model: sequelize.models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'email', 'phone'],
        },
      ],
      order: [['created_at', 'ASC']],
    });
  };

  return NotificationSchedule;
}
