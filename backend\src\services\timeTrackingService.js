import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Enhanced Time Tracking Service with seconds precision
 * Handles automatic timer management based on service call status changes
 */
class TimeTrackingService {
  
  /**
   * Handle status change and update time tracking with atomic transaction
   * This method combines time tracking updates with other service call updates
   * to prevent race conditions and data loss
   */
  static async handleStatusChangeWithTransaction(serviceCall, oldStatusCode, newStatusCode, userId, otherUpdateData = {}, transaction = null) {
    const shouldCommitTransaction = !transaction;
    if (!transaction) {
      transaction = await serviceCall.sequelize.transaction();
    }

    try {
      console.log('🔄 Starting atomic time tracking update with transaction');

      // Get current time tracking data
      const timeHistory = Array.isArray(serviceCall.time_tracking_history)
        ? [...serviceCall.time_tracking_history]
        : [];

      console.log('🔄 Processing atomic time tracking for status change:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        statusChange: `${oldStatusCode} -> ${newStatusCode}`,
        userId,
        timestamp: new Date().toISOString(),
        hasOtherUpdateData: Object.keys(otherUpdateData).length > 0,
        otherUpdateFields: Object.keys(otherUpdateData)
      });

      logger.info('Processing time tracking for status change:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        statusChange: `${oldStatusCode} -> ${newStatusCode}`,
        userId,
        timestamp: new Date().toISOString()
      });

      // Process the status change and get time tracking updates
      const timeTrackingUpdates = await this._processStatusChangeInternal(
        serviceCall, oldStatusCode, newStatusCode, userId, timeHistory, transaction
      );

      console.log('✅ Time tracking updates prepared:', {
        updateFields: Object.keys(timeTrackingUpdates),
        hasTimeHistory: !!timeTrackingUpdates.time_tracking_history,
        timeHistoryLength: timeTrackingUpdates.time_tracking_history?.length || 0
      });

      // Combine time tracking updates with other update data
      const finalUpdateData = {
        ...otherUpdateData,
        ...timeTrackingUpdates
      };

      console.log('🔍 Final atomic update data:', {
        timeTrackingHistoryLength: finalUpdateData.time_tracking_history ? finalUpdateData.time_tracking_history.length : 0,
        totalTimeSeconds: finalUpdateData.total_time_seconds,
        totalTimeMinutes: finalUpdateData.total_time_minutes,
        otherFields: Object.keys(otherUpdateData)
      });

      // Perform single atomic update with all data
      await serviceCall.update(finalUpdateData, { transaction });

      if (shouldCommitTransaction) {
        await transaction.commit();
      }

      console.log('✅ Atomic time tracking update completed successfully');
      return finalUpdateData;

    } catch (error) {
      if (shouldCommitTransaction) {
        await transaction.rollback();
      }
      console.error('❌ Atomic time tracking update failed:', error);
      throw error;
    }
  }

  /**
   * Internal method to process status change and return update data without database update
   */
  static async _processStatusChangeInternal(serviceCall, oldStatusCode, newStatusCode, userId, timeHistory, transaction = null) {
    // Create comprehensive time tracking entry with all required fields
    const timeEntry = {
      // Basic event information
      timestamp: new Date(),
      user_id: userId,
      status_from: oldStatusCode,
      status_to: newStatusCode,
      action: this.getActionType(oldStatusCode, newStatusCode),

      // Event metadata
      event_type: this.getEventType(oldStatusCode, newStatusCode),
      triggered_by: 'status_change',

      // Session tracking
      session_id: this.generateSessionId(),

      // Cumulative tracking (will be calculated based on action)
      cumulative_time_seconds: serviceCall.total_time_seconds || 0,
      cumulative_time_formatted: this.formatTimeDisplay(serviceCall.total_time_seconds || 0),

      // Additional context
      service_call_id: serviceCall.id,
      call_number: serviceCall.call_number
    };

    let updateData = {};

    // Handle different status transitions based on exact requirements
    switch (newStatusCode) {
      case 'ON_PROCESS':
      case 'PROGRESS':
      case 'IN_PROGRESS':
        // "In Progress" Status: Start timer automatically
        updateData = await this._startTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'COMPLETED':
        // "Completed" Status: Stop timer and calculate total time
        updateData = await this._stopTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'CANCELLED':
        // "Cancelled" Status: Stop timer and calculate total time up to cancellation
        updateData = await this._stopTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'CLOSED':
        // "Closed" Status: Stop timer and preserve accumulated time for potential resume
        updateData = await this._stopTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'FOLLOW_UP_CUSTOMER':
      case 'FOLLOW_UP_PROGRAMMER':
        // "Follow Up" Status: Stop timer
        updateData = await this._stopTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'ON_HOLD':
      case 'HOLD':
        // "On Hold" Status: Pause timer (stop counting but preserve current session time)
        updateData = await this._pauseTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'OPEN':
        // "Open" Status: Service is ready to serve, timer should NOT be running
        // But preserve accumulated time for potential resume (treat like pause)
        // CRITICAL FIX: Ensure accumulated time is preserved when changing to OPEN status
        console.log('🔍 Processing OPEN status - treating as pause to preserve accumulated time');
        updateData = await this._pauseTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      case 'PENDING':
        // "Pending" Status: Service is pending, timer should NOT be running
        // But preserve accumulated time for potential resume (treat like pause)
        console.log('🔍 Processing PENDING status - treating as pause to preserve accumulated time');
        updateData = await this._pauseTimerInternal(serviceCall, timeEntry, timeHistory);
        break;

      default:
        // Other Statuses: Timer should be stopped
        timeEntry.action = 'timer_stop';
        timeHistory.push(timeEntry);
        updateData = { time_tracking_history: timeHistory };
        break;
    }

    return updateData;
  }

  /**
   * Handle status change and update time tracking (legacy method for backward compatibility)
   */
  static async handleStatusChange(serviceCall, oldStatusCode, newStatusCode, userId) {
    try {
      logger.info('Processing time tracking for status change:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        statusChange: `${oldStatusCode} -> ${newStatusCode}`,
        userId
      });

      // Create time tracking entry
      const timeEntry = {
        timestamp: new Date(),
        user_id: userId,
        status_from: oldStatusCode,
        status_to: newStatusCode,
        action: this.getActionType(oldStatusCode, newStatusCode)
      };

      // Get current time tracking history
      let timeHistory = serviceCall.time_tracking_history || [];
      
      // Handle different status transitions based on exact requirements
      switch (newStatusCode) {
        case 'ON_PROCESS':
        case 'PROGRESS':
        case 'IN_PROGRESS':
          // "In Progress" Status: Start timer automatically
          await this.startTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'COMPLETED':
          // "Completed" Status: Stop timer and calculate total time
          await this.stopTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'CANCELLED':
          // "Cancelled" Status: Stop timer and calculate total time up to cancellation
          await this.stopTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'CLOSED':
          // "Closed" Status: Stop timer and preserve accumulated time for potential resume
          await this.stopTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'FOLLOW_UP_CUSTOMER':
        case 'FOLLOW_UP_PROGRAMMER':
          // "Follow Up" Status: Stop timer
          await this.stopTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'ON_HOLD':
        case 'HOLD':
          // "On Hold" Status: Pause timer (stop counting but preserve current session time)
          await this.pauseTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'OPEN':
          // "Open" Status: Service is ready to serve, timer should NOT be running
          // But preserve accumulated time for potential resume (treat like pause)
          await this.pauseTimer(serviceCall, timeEntry, timeHistory);
          break;

        case 'PENDING':
          // "Pending" Status: Service is pending, timer should NOT be running
          // But preserve accumulated time for potential resume (treat like pause)
          await this.pauseTimer(serviceCall, timeEntry, timeHistory);
          break;

        default:
          // Other Statuses: Timer should be stopped
          // Display: "00:00:00" (if no time recorded) OR accumulated time from previous sessions
          timeEntry.action = 'timer_stop';
          timeHistory.push(timeEntry);
          break;
      }

      // Note: Individual timer methods (startTimer, pauseTimer, stopTimer) handle
      // their own atomic updates including time_tracking_history. No separate update needed here.

      logger.info('Time tracking updated for service call:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        statusChange: `${oldStatusCode} -> ${newStatusCode}`,
        action: timeEntry.action
      });

    } catch (error) {
      logger.error('Error handling time tracking status change:', error);
      throw error;
    }
  }

  /**
   * Internal method to prepare start timer data without database update
   */
  static async _startTimerInternal(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_start';
    timeEntry.start_time = new Date();

    // Enhanced timer history tracking
    timeEntry.event_description = `Timer ${timeEntry.event_type.replace('timer_', '')} - Status changed from ${timeEntry.status_from} to ${timeEntry.status_to}`;
    timeEntry.duration_of_session = 0; // Will be calculated when paused/stopped

    console.log('🔍 Starting timer internal method:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      timeHistoryLength: timeHistory.length,
      statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
      eventType: timeEntry.event_type,
      timestamp: timeEntry.start_time.toISOString()
    });

    // Check if this is a resume from pause, stop, or first time start
    const isResumingFromPause = this.isTimerPaused(timeHistory);
    const isResumingFromStop = this.isTimerStopped(timeHistory);
    const hasExistingTime = serviceCall.total_time_seconds > 0 || this.calculateTotalTimeInSeconds(timeHistory) > 0;

    if (isResumingFromPause || isResumingFromStop || hasExistingTime) {
      // When resuming from pause/stop or has existing accumulated time - preserve it
      // CRITICAL FIX: Use the most accurate accumulated time calculation
      const storedSeconds = serviceCall.total_time_seconds || 0;
      const calculatedSeconds = this.calculateTotalTimeInSeconds(timeHistory);
      const accumulatedSeconds = Math.max(storedSeconds, calculatedSeconds);

      console.log('🔍 Timer resume calculation:', {
        serviceCallId: serviceCall.id,
        storedSeconds,
        calculatedSeconds,
        accumulatedSeconds,
        isResumingFromPause,
        isResumingFromStop,
        hasExistingTime,
        statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`
      });

      // Store the accumulated time at the point of resume for accurate calculation
      timeEntry.accumulated_seconds_at_start = accumulatedSeconds;
      timeEntry.cumulative_time_seconds = accumulatedSeconds;
      timeEntry.cumulative_time_formatted = this.formatTimeDisplay(accumulatedSeconds);

      // Set started_at to current time minus accumulated time for frontend calculation
      const adjustedStartTime = new Date(timeEntry.start_time.getTime() - (accumulatedSeconds * 1000));

      logger.info('Timer resumed:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        accumulatedSeconds,
        storedTotalSeconds: serviceCall.total_time_seconds,
        calculatedFromHistory: this.calculateTotalTimeInSeconds(timeHistory),
        resumeTime: timeEntry.start_time,
        adjustedStartTime,
        isResumingFromPause,
        isResumingFromStop,
        hasExistingTime
      });

      timeHistory.push(timeEntry);

      return {
        started_at: adjustedStartTime,
        time_tracking_history: timeHistory,
        updated_at: new Date()
      };
    } else {
      // First time starting - set started_at to current time
      timeEntry.accumulated_seconds_at_start = 0;
      timeEntry.cumulative_time_seconds = 0;
      timeEntry.cumulative_time_formatted = this.formatTimeDisplay(0);

      console.log('🆕 Starting timer for first time:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        startTime: timeEntry.start_time.toISOString(),
        statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
        isNewServiceCall: true
      });

      logger.info('Timer started for first time:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        startTime: timeEntry.start_time
      });

      timeHistory.push(timeEntry);

      console.log('✅ First-time timer start prepared successfully:', {
        timeHistoryLength: timeHistory.length,
        timerStartTime: timeEntry.start_time.toISOString(),
        willSetStartedAt: timeEntry.start_time.toISOString()
      });

      return {
        started_at: timeEntry.start_time,
        time_tracking_history: timeHistory,
        updated_at: new Date()
      };
    }
  }

  /**
   * Start timer when status changes to "In Progress"
   */
  static async startTimer(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_start';
    timeEntry.start_time = new Date();

    // Check if this is a resume from pause, stop, or first time start
    const isResumingFromPause = this.isTimerPaused(timeHistory);
    const isResumingFromStop = this.isTimerStopped(timeHistory);
    const hasExistingTime = serviceCall.total_time_seconds > 0 || this.calculateTotalTimeInSeconds(timeHistory) > 0;

    if (isResumingFromPause || isResumingFromStop || hasExistingTime) {
      // When resuming from pause/stop or has existing accumulated time - preserve it
      // CRITICAL FIX: Use the most accurate accumulated time calculation
      const storedSeconds = serviceCall.total_time_seconds || 0;
      const calculatedSeconds = this.calculateTotalTimeInSeconds(timeHistory);
      const accumulatedSeconds = Math.max(storedSeconds, calculatedSeconds);

      console.log('🔍 Timer resume (regular method):', {
        serviceCallId: serviceCall.id,
        storedSeconds,
        calculatedSeconds,
        accumulatedSeconds,
        statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`
      });

      // Store the accumulated time at the point of resume for accurate calculation
      timeEntry.accumulated_seconds_at_start = accumulatedSeconds;

      // Set started_at to current time minus accumulated time for frontend calculation
      // This ensures the frontend timer shows the correct total elapsed time
      const adjustedStartTime = new Date(timeEntry.start_time.getTime() - (accumulatedSeconds * 1000));

      logger.info('Timer resumed:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        accumulatedSeconds,
        storedTotalSeconds: serviceCall.total_time_seconds,
        calculatedFromHistory: this.calculateTotalTimeInSeconds(timeHistory),
        resumeTime: timeEntry.start_time,
        adjustedStartTime,
        isResumingFromPause,
        isResumingFromStop,
        hasExistingTime
      });

      // Add entry to history and update in single atomic operation
      timeHistory.push(timeEntry);

      console.log('🔍 DEBUG: About to update with timeHistory length:', timeHistory.length);
      console.log('🔍 DEBUG: timeHistory data:', JSON.stringify(timeHistory, null, 2));

      // Make atomic update with both time tracking history and started_at
      const updateResult = await serviceCall.update({
        started_at: adjustedStartTime,
        time_tracking_history: timeHistory,
        updated_at: new Date()
      });

      console.log('🔍 DEBUG: Update result:', updateResult ? 'success' : 'failed');
      console.log('🔍 DEBUG: TimeTrackingService update completed - skipping reload to prevent race condition');
    } else {
      // First time starting - set started_at to current time
      timeEntry.accumulated_seconds_at_start = 0;

      logger.info('Timer started for first time:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        startTime: timeEntry.start_time
      });

      // Add entry to history and update in single atomic operation
      timeHistory.push(timeEntry);

      console.log('🔍 DEBUG: About to update with timeHistory length:', timeHistory.length);
      console.log('🔍 DEBUG: timeHistory data:', JSON.stringify(timeHistory, null, 2));

      // Make atomic update with both time tracking history and started_at
      const updateResult = await serviceCall.update({
        started_at: timeEntry.start_time,
        time_tracking_history: timeHistory,
        updated_at: new Date()
      });

      console.log('🔍 DEBUG: Update result:', updateResult ? 'success' : 'failed');
      console.log('🔍 DEBUG: TimeTrackingService update completed - skipping reload to prevent race condition');
    }

    // Calculate the correct accumulated time for WebSocket emission
    // Use the accumulated time from the timer entry if resuming, otherwise use stored time
    let currentAccumulatedSeconds = serviceCall.total_time_seconds || 0;

    if (hasExistingTime && timeEntry.accumulated_seconds_at_start !== undefined) {
      // When resuming, use the accumulated time at start
      currentAccumulatedSeconds = timeEntry.accumulated_seconds_at_start;
    } else {
      // For first start or when no accumulated time, use calculated total
      currentAccumulatedSeconds = this.calculateTotalTimeInSeconds(timeHistory);
    }

    console.log('🔍 WebSocket emission - accumulated time:', {
      serviceCallId: serviceCall.id,
      storedTime: serviceCall.total_time_seconds,
      calculatedTime: this.calculateTotalTimeInSeconds(timeHistory),
      accumulatedAtStart: timeEntry.accumulated_seconds_at_start,
      finalAccumulatedTime: currentAccumulatedSeconds,
      hasExistingTime,
      isResuming: hasExistingTime
    });

    // Emit WebSocket event for timer started
    this.emitTimerUpdate(serviceCall.id, 'timer_started', {
      serviceCallId: serviceCall.id,
      timer: {
        is_running: true,
        is_paused: false,
        current_accumulated_seconds: currentAccumulatedSeconds,
        current_accumulated_formatted: this.formatTime(currentAccumulatedSeconds)
      },
      status: serviceCall.status,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Internal method to prepare stop timer data without database update
   */
  static async _stopTimerInternal(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_stop';
    timeEntry.end_time = new Date();

    // Enhanced timer history tracking
    timeEntry.event_description = `Timer ${timeEntry.event_type.replace('timer_', '')} - Status changed from ${timeEntry.status_from} to ${timeEntry.status_to}`;

    // CRITICAL FIX: Only calculate session duration if timer was actually running
    // Check if the timer was running before this stop (not already paused/stopped)
    const wasTimerRunning = this.isTimerRunning(timeHistory);
    let sessionDurationSeconds = 0;

    console.log('🔍 Stop timer internal - checking if timer was running:', {
      serviceCallId: serviceCall.id,
      statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
      wasTimerRunning,
      timeHistoryLength: timeHistory.length,
      lastAction: timeHistory.length > 0 ? timeHistory[timeHistory.length - 1].action : 'none'
    });

    if (wasTimerRunning) {
      // Timer was running, so calculate the session duration
      const lastStartEntry = this.findLastTimerStart(timeHistory);
      if (lastStartEntry) {
        // Calculate duration for this session in seconds for precision
        sessionDurationSeconds = this.calculateDurationInSeconds(lastStartEntry.start_time, timeEntry.end_time);
        const sessionDurationMinutes = Math.round(sessionDurationSeconds / 60);

        timeEntry.session_duration_seconds = sessionDurationSeconds;
        timeEntry.session_duration_minutes = sessionDurationMinutes;
        timeEntry.duration_of_session = sessionDurationSeconds;

        console.log('✅ Timer was running - calculated session duration:', {
          sessionDurationSeconds,
          startTime: lastStartEntry.start_time,
          endTime: timeEntry.end_time
        });
      }
    } else {
      // Timer was already paused/stopped, so no session duration to add
      timeEntry.session_duration_seconds = 0;
      timeEntry.session_duration_minutes = 0;
      timeEntry.duration_of_session = 0;

      console.log('⏹️  Timer was already stopped/paused - no session duration added');
    }

    // Add entry to history first
    timeHistory.push(timeEntry);

    // Calculate total time spent in both seconds and minutes AFTER adding to history
    const totalTimeSeconds = this.calculateTotalTimeInSeconds(timeHistory);
    const totalTimeMinutes = Math.round(totalTimeSeconds / 60);

    timeEntry.total_duration_seconds = totalTimeSeconds;
    timeEntry.total_duration_minutes = totalTimeMinutes;

    // Update cumulative time in the entry AFTER calculating total
    timeEntry.cumulative_time_seconds = totalTimeSeconds;
    timeEntry.cumulative_time_formatted = this.formatTimeDisplay(totalTimeSeconds);

    // Prepare update data
    const updateData = {
      completed_at: timeEntry.end_time,
      total_time_minutes: totalTimeMinutes,
      total_time_seconds: totalTimeSeconds,
      time_tracking_history: timeHistory,
      updated_at: new Date()
    };

    // Set closed_at for final statuses
    if (['COMPLETED', 'CANCELLED', 'NO_ISSUE'].includes(timeEntry.status_to)) {
      updateData.closed_at = timeEntry.end_time;
    }

    // Emit WebSocket event for timer update
    this.emitTimerUpdate(serviceCall.id, 'timer_update', {
      serviceCallId: serviceCall.id,
      timer: {
        is_running: this.isTimerRunning(timeHistory),
        is_paused: this.isTimerPaused(timeHistory),
        current_accumulated_seconds: totalTimeSeconds,
        current_accumulated_formatted: this.formatTime(totalTimeSeconds)
      },
      status: serviceCall.status,
      timestamp: new Date().toISOString()
    });

    return updateData;
  }

  /**
   * Stop timer when status changes to final states with seconds precision
   */
  static async stopTimer(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_stop';
    timeEntry.end_time = new Date();

    // CRITICAL FIX: Only calculate session duration if timer was actually running
    // Check if the timer was running before this stop (not already paused/stopped)
    const wasTimerRunning = this.isTimerRunning(timeHistory);
    let sessionDurationSeconds = 0;

    console.log('🔍 Stop timer - checking if timer was running:', {
      serviceCallId: serviceCall.id,
      statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
      wasTimerRunning,
      timeHistoryLength: timeHistory.length,
      lastAction: timeHistory.length > 0 ? timeHistory[timeHistory.length - 1].action : 'none'
    });

    if (wasTimerRunning) {
      // Timer was running, so calculate the session duration
      const lastStartEntry = this.findLastTimerStart(timeHistory);
      if (lastStartEntry) {
        // Calculate duration for this session in seconds for precision
        sessionDurationSeconds = this.calculateDurationInSeconds(lastStartEntry.start_time, timeEntry.end_time);
        const sessionDurationMinutes = Math.round(sessionDurationSeconds / 60);

        timeEntry.session_duration_seconds = sessionDurationSeconds;
        timeEntry.session_duration_minutes = sessionDurationMinutes;

        console.log('✅ Timer was running - calculated session duration:', {
          sessionDurationSeconds,
          startTime: lastStartEntry.start_time,
          endTime: timeEntry.end_time
        });
      }
    } else {
      // Timer was already paused/stopped, so no session duration to add
      timeEntry.session_duration_seconds = 0;
      timeEntry.session_duration_minutes = 0;

      console.log('⏹️  Timer was already stopped/paused - no session duration added');
    }
    
    // Add entry to history first
    timeHistory.push(timeEntry);

    // Calculate total time spent in both seconds and minutes AFTER adding to history
    const totalTimeSeconds = this.calculateTotalTimeInSeconds(timeHistory);
    const totalTimeMinutes = Math.round(totalTimeSeconds / 60);

    timeEntry.total_duration_seconds = totalTimeSeconds;
    timeEntry.total_duration_minutes = totalTimeMinutes;

    // Update service call completion time with seconds precision
    const updateData = {
      completed_at: timeEntry.end_time,
      total_time_minutes: totalTimeMinutes,
      total_time_seconds: totalTimeSeconds,
      time_tracking_history: timeHistory,
      updated_at: new Date()
    };

    // Set closed_at for final statuses
    if (['COMPLETED', 'CANCELLED', 'NO_ISSUE'].includes(timeEntry.status_to)) {
      updateData.closed_at = timeEntry.end_time;
    }

    await serviceCall.update(updateData);

    // Emit WebSocket event for timer stopped
    this.emitTimerUpdate(serviceCall.id, 'timer_stopped', {
      serviceCallId: serviceCall.id,
      timer: {
        is_running: false,
        is_paused: false,
        current_accumulated_seconds: totalTimeSeconds,
        current_accumulated_formatted: this.formatTime(totalTimeSeconds)
      },
      status: serviceCall.status,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Internal method to prepare pause timer data without database update
   */
  static async _pauseTimerInternal(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_pause';
    timeEntry.pause_time = new Date();

    // Enhanced timer history tracking
    timeEntry.event_description = `Timer ${timeEntry.event_type.replace('timer_', '')} - Status changed from ${timeEntry.status_from} to ${timeEntry.status_to}`;

    // CRITICAL FIX: Only calculate session duration if timer was actually running
    // Check if the timer was running before this pause (not already paused)
    const wasTimerRunning = this.isTimerRunning(timeHistory);
    let sessionDurationSeconds = 0;

    console.log('🔍 Pause timer internal - checking if timer was running:', {
      serviceCallId: serviceCall.id,
      statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
      wasTimerRunning,
      timeHistoryLength: timeHistory.length,
      lastAction: timeHistory.length > 0 ? timeHistory[timeHistory.length - 1].action : 'none'
    });

    if (wasTimerRunning) {
      // Timer was running, so calculate the session duration
      const lastStartEntry = this.findLastTimerStart(timeHistory);
      if (lastStartEntry) {
        // Calculate duration for this session in seconds for precision
        sessionDurationSeconds = this.calculateDurationInSeconds(lastStartEntry.start_time, timeEntry.pause_time);
        const sessionDurationMinutes = Math.round(sessionDurationSeconds / 60);

        timeEntry.session_duration_seconds = sessionDurationSeconds;
        timeEntry.session_duration_minutes = sessionDurationMinutes;
        timeEntry.duration_of_session = sessionDurationSeconds;

        // If this was a resumed session, calculate total properly
        if (lastStartEntry.accumulated_seconds_at_start !== undefined) {
          timeEntry.total_accumulated_at_pause = lastStartEntry.accumulated_seconds_at_start + sessionDurationSeconds;
        }

        console.log('✅ Timer was running - calculated session duration:', {
          sessionDurationSeconds,
          startTime: lastStartEntry.start_time,
          pauseTime: timeEntry.pause_time
        });
      }
    } else {
      // Timer was already paused/stopped, so no session duration to add
      timeEntry.session_duration_seconds = 0;
      timeEntry.session_duration_minutes = 0;
      timeEntry.duration_of_session = 0;

      console.log('⏸️  Timer was already paused - no session duration added');
    }

    // Add entry to history first
    timeHistory.push(timeEntry);

    // Calculate total time including this paused session AFTER adding to history
    const totalTimeSeconds = this.calculateTotalTimeInSeconds(timeHistory);
    const totalTimeMinutes = Math.round(totalTimeSeconds / 60);

    // Update cumulative time in the entry AFTER calculating total
    timeEntry.cumulative_time_seconds = totalTimeSeconds;
    timeEntry.cumulative_time_formatted = this.formatTimeDisplay(totalTimeSeconds);

    logger.info('Timer paused:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      sessionDurationSeconds,
      totalAccumulatedSeconds: totalTimeSeconds,
      pauseTime: timeEntry.pause_time
    });

    return {
      total_time_minutes: totalTimeMinutes,
      total_time_seconds: totalTimeSeconds,
      time_tracking_history: timeHistory,
      updated_at: new Date()
    };
  }

  /**
   * Pause timer when status changes to "Hold" with seconds precision
   */
  static async pauseTimer(serviceCall, timeEntry, timeHistory) {
    timeEntry.action = 'timer_pause';
    timeEntry.pause_time = new Date();

    // CRITICAL FIX: Only calculate session duration if timer was actually running
    // Check if the timer was running before this pause (not already paused)
    const wasTimerRunning = this.isTimerRunning(timeHistory);
    let sessionDurationSeconds = 0;

    console.log('🔍 Pause timer - checking if timer was running:', {
      serviceCallId: serviceCall.id,
      statusTransition: `${timeEntry.status_from} -> ${timeEntry.status_to}`,
      wasTimerRunning,
      timeHistoryLength: timeHistory.length,
      lastAction: timeHistory.length > 0 ? timeHistory[timeHistory.length - 1].action : 'none'
    });

    if (wasTimerRunning) {
      // Timer was running, so calculate the session duration
      const lastStartEntry = this.findLastTimerStart(timeHistory);
      if (lastStartEntry) {
        // Calculate duration for this session in seconds for precision
        sessionDurationSeconds = this.calculateDurationInSeconds(lastStartEntry.start_time, timeEntry.pause_time);
        const sessionDurationMinutes = Math.round(sessionDurationSeconds / 60);

        timeEntry.session_duration_seconds = sessionDurationSeconds;
        timeEntry.session_duration_minutes = sessionDurationMinutes;

        // If this was a resumed session, calculate total properly
        if (lastStartEntry.accumulated_seconds_at_start !== undefined) {
          timeEntry.total_accumulated_at_pause = lastStartEntry.accumulated_seconds_at_start + sessionDurationSeconds;
        }

        console.log('✅ Timer was running - calculated session duration:', {
          sessionDurationSeconds,
          startTime: lastStartEntry.start_time,
          pauseTime: timeEntry.pause_time
        });
      }
    } else {
      // Timer was already paused/stopped, so no session duration to add
      timeEntry.session_duration_seconds = 0;
      timeEntry.session_duration_minutes = 0;

      console.log('⏸️  Timer was already paused - no session duration added');
    }

    // Add entry to history first
    timeHistory.push(timeEntry);

    // Calculate total time including this paused session AFTER adding to history
    const totalTimeSeconds = this.calculateTotalTimeInSeconds(timeHistory);
    const totalTimeMinutes = Math.round(totalTimeSeconds / 60);

    // Update cumulative time in the entry AFTER calculating total
    timeEntry.cumulative_time_seconds = totalTimeSeconds;
    timeEntry.cumulative_time_formatted = this.formatTimeDisplay(totalTimeSeconds);

    logger.info('Timer paused:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      sessionDurationSeconds,
      totalAccumulatedSeconds: totalTimeSeconds,
      pauseTime: timeEntry.pause_time
    });

    console.log('🔍 DEBUG: About to update with timeHistory length:', timeHistory.length);
    console.log('🔍 DEBUG: timeHistory data:', JSON.stringify(timeHistory, null, 2));

    // Update service call with accumulated time up to this pause and time tracking history
    const updateResult = await serviceCall.update({
      total_time_minutes: totalTimeMinutes,
      total_time_seconds: totalTimeSeconds,
      time_tracking_history: timeHistory,
      updated_at: new Date()
    });

    console.log('🔍 DEBUG: Update result:', updateResult ? 'success' : 'failed');
    console.log('🔍 DEBUG: TimeTrackingService update completed - skipping reload to prevent race condition');

    // Emit WebSocket event for timer paused
    this.emitTimerUpdate(serviceCall.id, 'timer_paused', {
      serviceCallId: serviceCall.id,
      timer: {
        is_running: false,
        is_paused: true,
        current_accumulated_seconds: totalTimeSeconds,
        current_accumulated_formatted: this.formatTime(totalTimeSeconds)
      },
      status: serviceCall.status,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Find the last timer start entry in history
   */
  static findLastTimerStart(timeHistory) {
    for (let i = timeHistory.length - 1; i >= 0; i--) {
      if (timeHistory[i].action === 'timer_start') {
        return timeHistory[i];
      }
    }
    return null;
  }

  /**
   * Calculate duration between two timestamps in seconds for precision
   */
  static calculateDurationInSeconds(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    return Math.round(diffMs / 1000); // Convert to seconds for precision
  }

  /**
   * Calculate duration between two timestamps in minutes (legacy method)
   */
  static calculateDuration(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    return Math.round(diffMs / (1000 * 60)); // Convert to minutes
  }

  /**
   * Calculate total time spent on the service call in seconds for precision
   */
  static calculateTotalTimeInSeconds(timeHistory, currentEntry = null) {
    let totalSeconds = 0;
    
    // Add current session if provided
    if (currentEntry && currentEntry.session_duration_seconds) {
      totalSeconds += currentEntry.session_duration_seconds;
    }
    
    // Add all previous sessions
    timeHistory.forEach(entry => {
      if (entry.session_duration_seconds) {
        totalSeconds += entry.session_duration_seconds;
      }
    });
    
    return totalSeconds;
  }

  /**
   * Calculate total time spent on the service call in minutes (legacy method)
   */
  static calculateTotalTime(timeHistory, currentEntry = null) {
    let totalMinutes = 0;
    
    // Add current session if provided
    if (currentEntry && currentEntry.session_duration_minutes) {
      totalMinutes += currentEntry.session_duration_minutes;
    }
    
    // Add all previous sessions
    timeHistory.forEach(entry => {
      if (entry.session_duration_minutes) {
        totalMinutes += entry.session_duration_minutes;
      }
    });
    
    return totalMinutes;
  }

  /**
   * Get action type based on status transition
   */
  static getActionType(oldStatus, newStatus) {
    // Timer logic based on exact requirements:
    // - "Open": Service ready to serve, timer NOT running, show accumulated time
    // - "In Progress": Timer START automatically, show live running timer
    // - "On Hold": Timer PAUSE, show paused time from current session
    // - "Completed": Timer STOP, show final total time
    // - "Cancelled": Timer STOP, show total time up to cancellation
    // - "Follow Up": Timer STOP, show total time served so far
    // - Other Statuses: Timer stopped, show accumulated time or 00:00:00

    if (['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(newStatus)) return 'timer_start';
    if (['COMPLETED'].includes(newStatus)) return 'timer_stop';
    if (['CANCELLED'].includes(newStatus)) return 'timer_stop';
    if (['CLOSED'].includes(newStatus)) return 'timer_stop'; // CLOSED status stops timer but preserves time
    if (['FOLLOW_UP_CUSTOMER', 'FOLLOW_UP_PROGRAMMER'].includes(newStatus)) return 'timer_stop';
    if (['ON_HOLD', 'HOLD', 'OPEN', 'PENDING'].includes(newStatus)) return 'timer_pause'; // OPEN and PENDING now treated as pause
    return 'timer_stop'; // Default: stop timer for other statuses
  }

  /**
   * Get event type for comprehensive timer history tracking
   */
  static getEventType(oldStatus, newStatus) {
    if (['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(newStatus)) {
      // Check if this is a resume from pause, stop, or first start
      if (['ON_HOLD', 'HOLD', 'OPEN', 'PENDING'].includes(oldStatus)) {
        return 'timer_resumed';
      } else if (['CLOSED', 'COMPLETED', 'CANCELLED'].includes(oldStatus)) {
        return 'timer_resumed';
      } else {
        return 'timer_started';
      }
    } else if (['ON_HOLD', 'HOLD'].includes(newStatus)) {
      return 'timer_paused';
    } else if (newStatus === 'OPEN') {
      // CRITICAL FIX: OPEN status should be treated as pause to preserve accumulated time
      return 'timer_paused';
    } else if (newStatus === 'PENDING') {
      // CRITICAL FIX: PENDING status should be treated as pause to preserve accumulated time
      return 'timer_paused';
    } else if (['COMPLETED'].includes(newStatus)) {
      return 'timer_completed';
    } else if (['CANCELLED'].includes(newStatus)) {
      return 'timer_cancelled';
    } else if (['CLOSED'].includes(newStatus)) {
      return 'timer_closed';
    } else if (['FOLLOW_UP_CUSTOMER', 'FOLLOW_UP_PROGRAMMER'].includes(newStatus)) {
      return 'timer_stopped';
    } else {
      return 'timer_stopped';
    }
  }

  /**
   * Generate unique session ID for tracking pause/resume cycles
   */
  static generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Format time display in HH:MM:SS format
   */
  static formatTimeDisplay(totalSeconds) {
    if (!totalSeconds || totalSeconds === 0) return '00:00:00';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }

  /**
   * Format duration in seconds to human readable format with precision
   */
  static formatDurationFromSeconds(totalSeconds) {
    if (!totalSeconds || totalSeconds === 0) return '0 seconds';
    
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    const parts = [];
    
    if (hours > 0) {
      parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    }
    
    if (minutes > 0) {
      parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
    }
    
    if (seconds > 0 || parts.length === 0) {
      parts.push(`${seconds} second${seconds !== 1 ? 's' : ''}`);
    }
    
    return parts.join(' ');
  }

  /**
   * Format duration in minutes to human readable format (legacy method)
   */
  static formatDuration(minutes) {
    if (!minutes || minutes === 0) return '0 minutes';
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours === 0) {
      return `${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    } else if (remainingMinutes === 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else {
      return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Get time tracking summary for a service call with seconds precision
   */
  static getTimeTrackingSummary(serviceCall) {
    const timeHistory = serviceCall.time_tracking_history || [];
    const totalTimeMinutes = serviceCall.total_time_minutes || 0;
    const totalTimeSeconds = serviceCall.total_time_seconds || 0;

    // Calculate total time in seconds from time history for precision if not stored
    const calculatedTotalSeconds = totalTimeSeconds > 0 ? totalTimeSeconds : this.calculateTotalTimeInSeconds(timeHistory);

    // Get current accumulated time including any active session
    const currentAccumulatedTime = this.getCurrentAccumulatedTime(serviceCall);

    const summary = {
      total_time_minutes: Math.round(calculatedTotalSeconds / 60),
      total_time_seconds: calculatedTotalSeconds,
      current_accumulated_time: currentAccumulatedTime, // Real-time accumulated time
      total_time_formatted: calculatedTotalSeconds > 0 ? this.formatDurationFromSeconds(calculatedTotalSeconds) : this.formatDuration(totalTimeMinutes),
      current_time_formatted: this.formatDurationFromSeconds(currentAccumulatedTime),
      sessions: [],
      current_status: serviceCall.status?.code || 'UNKNOWN',
      is_timer_running: this.isTimerRunning(timeHistory),
      is_timer_paused: this.isTimerPaused(timeHistory),
      started_at: serviceCall.started_at,
      completed_at: serviceCall.completed_at,
      // Add debug info for troubleshooting
      time_calculation_source: serviceCall.started_at && serviceCall.completed_at ? 'database_timestamps' :
        (totalTimeSeconds > 0 ? 'stored_total' : 'calculated_from_history'),
      debug_info: {
        db_started_at: serviceCall.started_at,
        db_completed_at: serviceCall.completed_at,
        stored_total_seconds: totalTimeSeconds,
        calculated_from_history_seconds: this.calculateTotalTimeInSeconds(timeHistory),
        current_accumulated_seconds: currentAccumulatedTime
      }
    };

    // Process time history into sessions
    let currentSession = null;

    timeHistory.forEach(entry => {
      if (entry.action === 'timer_start') {
        currentSession = {
          start_time: entry.start_time,
          start_status: entry.status_to,
          user_id: entry.user_id
        };
      } else if (entry.action === 'timer_stop' || entry.action === 'timer_pause') {
        if (currentSession) {
          currentSession.end_time = entry.end_time || entry.pause_time;
          currentSession.end_status = entry.status_to;
          currentSession.duration_minutes = entry.session_duration_minutes || 0;
          currentSession.duration_seconds = entry.session_duration_seconds || 0;
          currentSession.duration_formatted = currentSession.duration_seconds > 0 ?
            this.formatDurationFromSeconds(currentSession.duration_seconds) :
            this.formatDuration(currentSession.duration_minutes);
          summary.sessions.push(currentSession);
          currentSession = null;
        }
      }
    });

    return summary;
  }

  /**
   * Check if timer is currently running
   */
  static isTimerRunning(timeHistory) {
    if (timeHistory.length === 0) return false;

    const lastEntry = timeHistory[timeHistory.length - 1];
    return lastEntry.action === 'timer_start';
  }

  /**
   * Check if timer is currently paused (on hold)
   */
  static isTimerPaused(timeHistory) {
    if (timeHistory.length === 0) return false;

    const lastEntry = timeHistory[timeHistory.length - 1];
    return lastEntry.action === 'timer_pause';
  }

  /**
   * Check if timer is currently stopped
   */
  static isTimerStopped(timeHistory) {
    if (timeHistory.length === 0) return false;

    const lastEntry = timeHistory[timeHistory.length - 1];
    return lastEntry.action === 'timer_stop';
  }

  /**
   * Get current accumulated time for a service call including any active session
   */
  static getCurrentAccumulatedTime(serviceCall) {
    const timeHistory = serviceCall.time_tracking_history || [];
    const storedTotalSeconds = serviceCall.total_time_seconds || 0;

    // If timer is currently running, calculate time including current session
    if (this.isTimerRunning(timeHistory)) {
      const lastStartEntry = this.findLastTimerStart(timeHistory);
      if (lastStartEntry) {
        // CRITICAL FIX: Calculate current session duration from the actual resume time
        // Use the start_time from the last timer_start entry (which is the resume time)
        const resumeTime = new Date(lastStartEntry.start_time);
        const currentTime = new Date();
        const currentSessionSeconds = this.calculateDurationInSeconds(resumeTime, currentTime);

        // If this is a resumed session, use the accumulated time at start + current session
        if (lastStartEntry.accumulated_seconds_at_start !== undefined) {
          const totalTime = lastStartEntry.accumulated_seconds_at_start + currentSessionSeconds;

          console.log('🔍 Timer running - resumed session calculation:', {
            serviceCallId: serviceCall.id,
            resumeTime: resumeTime.toISOString(),
            currentTime: currentTime.toISOString(),
            currentSessionSeconds,
            accumulatedAtStart: lastStartEntry.accumulated_seconds_at_start,
            totalTime,
            storedTotalSeconds
          });

          return totalTime;
        }

        // For first-time sessions, add current session to stored total
        const totalTime = storedTotalSeconds + currentSessionSeconds;

        console.log('🔍 Timer running - first session calculation:', {
          serviceCallId: serviceCall.id,
          startTime: resumeTime.toISOString(),
          currentTime: currentTime.toISOString(),
          currentSessionSeconds,
          storedTotalSeconds,
          totalTime
        });

        return totalTime;
      }
    }

    // CRITICAL FIX: For paused or stopped timers, calculate from time history if available
    // This ensures accumulated time is preserved even if database hasn't been updated yet
    if (this.isTimerPaused(timeHistory) || this.isTimerStopped(timeHistory)) {
      // Calculate total from time history (more reliable than stored value during transitions)
      const calculatedFromHistory = this.calculateTotalTimeInSeconds(timeHistory);

      // Use the maximum of stored value and calculated value to ensure no time is lost
      const accumulatedTime = Math.max(storedTotalSeconds, calculatedFromHistory);

      // Debug logging for troubleshooting
      if (storedTotalSeconds !== calculatedFromHistory) {
        console.log('🔍 Timer accumulated time mismatch detected:', {
          serviceCallId: serviceCall.id,
          storedTotalSeconds,
          calculatedFromHistory,
          usingValue: accumulatedTime,
          timerState: this.isTimerPaused(timeHistory) ? 'paused' : 'stopped'
        });
      }

      return accumulatedTime;
    }

    // Fallback: return stored total time
    return storedTotalSeconds;
  }

  /**
   * Get time tracking statistics for dashboard
   */
  static async getTimeTrackingStats(tenantId, dateFrom = null, dateTo = null) {
    try {
      const where = { tenant_id: tenantId };

      if (dateFrom && dateTo) {
        where.created_at = {
          [models.Sequelize.Op.between]: [new Date(dateFrom), new Date(dateTo)]
        };
      }

      const serviceCalls = await models.ServiceCall.findAll({
        where,
        attributes: ['id', 'call_number', 'total_time_minutes', 'total_time_seconds', 'started_at', 'completed_at', 'time_tracking_history'],
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['code', 'name']
          }
        ]
      });

      const stats = {
        total_calls: serviceCalls.length,
        calls_with_time_tracking: serviceCalls.filter(call => (call.total_time_minutes > 0 || call.total_time_seconds > 0)).length,
        total_time_minutes: serviceCalls.reduce((sum, call) => sum + (call.total_time_minutes || 0), 0),
        total_time_seconds: serviceCalls.reduce((sum, call) => sum + (call.total_time_seconds || 0), 0),
        average_time_minutes: 0,
        average_time_seconds: 0,
        active_timers: serviceCalls.filter(call => this.isTimerRunning(call.time_tracking_history || [])).length
      };

      if (stats.calls_with_time_tracking > 0) {
        stats.average_time_minutes = Math.round(stats.total_time_minutes / stats.calls_with_time_tracking);
        stats.average_time_seconds = Math.round(stats.total_time_seconds / stats.calls_with_time_tracking);
      }

      stats.total_time_formatted = stats.total_time_seconds > 0 ? this.formatDurationFromSeconds(stats.total_time_seconds) : this.formatDuration(stats.total_time_minutes);
      stats.average_time_formatted = stats.average_time_seconds > 0 ? this.formatDurationFromSeconds(stats.average_time_seconds) : this.formatDuration(stats.average_time_minutes);

      return stats;
    } catch (error) {
      logger.error('Error getting time tracking stats:', error);
      throw error;
    }
  }

  /**
   * Emit WebSocket event for timer updates
   */
  static emitTimerUpdate(serviceCallId, eventType, data) {
    try {
      if (global.io) {
        const roomName = `service_${serviceCallId}`;
        global.io.to(roomName).emit(eventType, data);
        console.log(`📡 Emitted ${eventType} to room ${roomName}:`, data);
      }
    } catch (error) {
      console.error('Error emitting timer update:', error);
    }
  }

  /**
   * Format time in HH:MM:SS format
   */
  static formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }


}

export default TimeTrackingService;
