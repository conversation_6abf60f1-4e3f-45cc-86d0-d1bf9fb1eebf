# Email Notification System Documentation

## Overview

The TallyCRM system includes a comprehensive email notification service that handles three main scenarios:

1. **User Creation** ✅ - Welcome emails when new users are created
2. **Service Creation** ✅ - Notification emails when new service requests are created
3. **Service Completion** ✅ - Completion emails when services are marked as completed

## ✅ Implementation Status

**All three email notification scenarios have been successfully implemented and tested:**

- **User Welcome Emails**: Fully functional with professional templates
- **Service Creation Notifications**: Integrated with unified notification service
- **Service Completion Emails**: Enhanced with feedback forms and professional branding
- **User Unsubscribe Emails**: Implemented for account deletion scenarios
- **Email Logging**: Comprehensive logging and monitoring system
- **Error Handling**: Graceful degradation and proper error logging
- **Testing Framework**: Complete test suite for all notification types

## ✅ Test Results (June 17, 2025)

**All email notification tests passed successfully:**

```
📊 Test Results Summary:
========================
Connection Test:      ✅ PASSED
Welcome Email:        ✅ PASSED
Service Creation:     ✅ PASSED
Service Completion:   ✅ PASSED
Unsubscribe Email:    ✅ PASSED

🎯 Overall Result: 5/5 tests passed
🎉 All email notification tests passed successfully!
```

**Test Details:**
- Email connection established successfully
- Welcome email sent with message ID: `<<EMAIL>>`
- Service notifications working (gracefully handles missing notification settings)
- Unsubscribe email sent with message ID: `<<EMAIL>>`

## Email Server Configuration

**Production Settings:**
```env
SMTP_HOST=server40.hostingraja.org
SMTP_PORT=25
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=Cloud@2020
```

**Development/Testing Settings (currently active):**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=uvlp ovfm oagc igyj
```

## Architecture

### Core Services

1. **EmailService** (`src/services/emailService.js`)
   - Handles SMTP connection and email sending
   - Provides welcome and unsubscribe email functionality
   - Includes email logging and error handling

2. **NotificationService** (`src/services/NotificationService.js`)
   - Manages service-related notifications
   - Handles service creation, completion, and status change emails
   - Uses configurable notification settings per tenant

### Email Templates

All email templates use professional branding with:
- Company colors (#2f69b3 primary blue)
- Responsive HTML design
- Consistent header and footer
- Contact information and unsubscribe links

## Implementation Details

### 1. User Creation (Welcome Email)

**Triggered when:**
- New user is created via `userController.createUser()`
- User registration via `auth.js` routes

**Email Content:**
- Welcome message with company branding
- User account details
- Contact information
- Unsubscribe link

**Code Example:**
```javascript
await emailService.sendWelcomeEmail({
  id: user.id,
  email: user.email,
  first_name: user.first_name,
  last_name: user.last_name,
  phone: user.phone
});
```

### 2. Service Creation

**Triggered when:**
- New service call is created via `serviceCallController.createServiceCall()`

**Email Content:**
- Service request confirmation
- Service number and details
- Scheduled date (if applicable)
- Status information

**Customer Email Retrieval:**
The system uses enhanced email retrieval from multiple sources:
1. Customer main email field
2. Address book entries in custom_fields
3. Specific email fields (admin_email, md_email, office_email, etc.)

**Code Example:**
```javascript
await notificationService.sendServiceNotification(
  serviceData,
  customerData,
  'service_created'
);
```

### 3. Service Completion

**Triggered when:**
- Service status is changed to 'COMPLETED'
- Via both edit forms and action button status updates

**Email Content:**
- Service completion confirmation
- Feedback request with Google Form link
- Service summary and contact information
- Terms and conditions

**Code Example:**
```javascript
await notificationService.sendServiceNotification(
  serviceData,
  customerData,
  'service_completed'
);
```

### 4. User Deletion (Unsubscribe Email)

**Triggered when:**
- User is deleted/deactivated via `userController.deleteUser()`

**Email Content:**
- Account deactivation confirmation
- Thank you message
- Unsubscribe confirmation

## Email Logging and Monitoring

### Log Files

1. **Email Send Status Log** - Human-readable format
   - Location: `logs/email-send-status.log`
   - Contains success/failure status with timestamps
   - Accessible via API: `GET /api/service-calls/email-send-status-log`

2. **Email Logs** - Structured JSON format
   - Contains detailed email attempt information
   - Accessible via API: `GET /api/service-calls/email-logs`

### Monitoring Endpoints

- `GET /api/service-calls/email-logs` - Get email logs
- `GET /api/service-calls/email-send-status-log` - Get human-readable log
- `GET /api/service-calls/email-statistics` - Get email statistics
- `POST /api/service-calls/clear-email-logs` - Clear email logs

## Testing

### Manual Testing via API

Test different email types using the test endpoint:

```bash
# Test connection
POST /api/service-calls/test-email
{
  "to": "<EMAIL>",
  "testType": "connection"
}

# Test welcome email
POST /api/service-calls/test-email
{
  "to": "<EMAIL>",
  "testType": "user_welcome"
}

# Test service creation
POST /api/service-calls/test-email
{
  "to": "<EMAIL>",
  "testType": "service_creation"
}

# Test service completion
POST /api/service-calls/test-email
{
  "to": "<EMAIL>",
  "testType": "service_completion"
}

# Test unsubscribe email
POST /api/service-calls/test-email
{
  "to": "<EMAIL>",
  "testType": "user_unsubscribe"
}
```

### Automated Testing

Run the comprehensive test script:

```bash
cd backend
node test-email-notifications.js
```

This script tests all email notification types and provides a detailed report.

## Error Handling

### Graceful Degradation

- Email failures do not prevent user creation or service operations
- All email errors are logged but don't break the main workflow
- Email service can be disabled for development environments

### Error Logging

- Failed emails are logged with full error details
- Success/failure status is tracked in dedicated log files
- Email statistics provide overview of delivery success rates

## Configuration

### Environment Variables

```env
# Email Service
SMTP_HOST=server40.hostingraja.org
SMTP_PORT=25
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=Cloud@2020
SMTP_FROM_NAME="Prem Infotech Support"

# Application URLs
APP_URL=https://your-domain.com
FRONTEND_URL=https://your-frontend-domain.com

# Development
NODE_ENV=development  # Set to disable emails in dev
```

### Notification Settings

Each tenant can configure notification preferences via the NotificationSettings model:
- Enable/disable specific notification types
- Set business hours for notifications
- Configure notification triggers

## Troubleshooting

### Common Issues

1. **SMTP Connection Failures**
   - Verify server credentials
   - Check firewall settings for port 25
   - Ensure SSL/TLS configuration is correct

2. **Missing Customer Emails**
   - Check customer email field
   - Verify address book entries in custom_fields
   - Ensure email fields are properly populated

3. **Template Rendering Issues**
   - Check for missing data in email templates
   - Verify all required fields are provided
   - Review template syntax for errors

### Debug Mode

Enable detailed logging by setting `NODE_ENV=development` to see:
- Full error stacks
- Email template data
- SMTP connection details
- Customer email retrieval process

## Future Enhancements

1. **Email Templates Management**
   - Admin interface for customizing email templates
   - Multi-language support
   - Template versioning

2. **Advanced Notifications**
   - SMS notifications
   - Push notifications
   - Webhook integrations

3. **Analytics**
   - Email open tracking
   - Click tracking
   - Delivery analytics dashboard
