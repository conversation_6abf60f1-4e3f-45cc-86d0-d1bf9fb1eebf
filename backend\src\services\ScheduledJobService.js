import cron from 'node-cron';
import { logger } from '../utils/logger.js';
import RenewalNotificationService from './RenewalNotificationService.js';

/**
 * Scheduled Job Service for managing automated tasks
 * Handles renewal notifications, cleanup tasks, and other scheduled operations
 */
class ScheduledJobService {
  constructor() {
    this.jobs = new Map();
    this.renewalService = new RenewalNotificationService();
    this.isInitialized = false;
  }

  /**
   * Initialize all scheduled jobs
   */
  async initialize() {
    if (this.isInitialized) {
      logger.warn('⚠️ Scheduled jobs already initialized');
      return;
    }

    try {
      logger.info('🚀 Initializing scheduled jobs...');

      // Initialize renewal notification service
      await this.renewalService.initialize();

      // Schedule all jobs
      this.scheduleRenewalNotificationJobs();
      this.scheduleMaintenanceJobs();

      this.isInitialized = true;
      logger.info('✅ All scheduled jobs initialized successfully');
    } catch (error) {
      logger.error('❌ Error initializing scheduled jobs:', error);
      throw error;
    }
  }

  /**
   * Schedule renewal notification related jobs
   */
  scheduleRenewalNotificationJobs() {
    // Daily renewal notification processing (runs at 9:00 AM)
    const dailyNotificationJob = cron.schedule('0 9 * * *', async () => {
      logger.info('🔄 Starting daily renewal notification processing...');
      try {
        const result = await this.renewalService.processPendingNotifications();
        logger.info(`✅ Daily notification processing completed: ${JSON.stringify(result)}`);
      } catch (error) {
        logger.error('❌ Error in daily notification processing:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    // Weekly renewal schedule generation (runs every Sunday at 6:00 AM)
    const weeklyScheduleJob = cron.schedule('0 6 * * 0', async () => {
      logger.info('🔄 Starting weekly renewal schedule generation...');
      try {
        const scheduled = await this.renewalService.scheduleAllRenewalNotifications();
        logger.info(`✅ Weekly schedule generation completed: ${scheduled} notifications scheduled`);
      } catch (error) {
        logger.error('❌ Error in weekly schedule generation:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    // Retry failed notifications (runs every 2 hours)
    const retryFailedJob = cron.schedule('0 */2 * * *', async () => {
      logger.info('🔄 Starting failed notification retry process...');
      try {
        await this.retryFailedNotifications();
      } catch (error) {
        logger.error('❌ Error in failed notification retry:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    // Store jobs for management
    this.jobs.set('dailyNotifications', dailyNotificationJob);
    this.jobs.set('weeklySchedule', weeklyScheduleJob);
    this.jobs.set('retryFailed', retryFailedJob);

    // Start the jobs
    dailyNotificationJob.start();
    weeklyScheduleJob.start();
    retryFailedJob.start();

    logger.info('📅 Renewal notification jobs scheduled:');
    logger.info('   - Daily notifications: 9:00 AM IST');
    logger.info('   - Weekly schedule generation: Sunday 6:00 AM IST');
    logger.info('   - Failed notification retry: Every 2 hours');
  }

  /**
   * Schedule maintenance and cleanup jobs
   */
  scheduleMaintenanceJobs() {
    // Monthly cleanup of old notification schedules (runs on 1st of every month at 2:00 AM)
    const monthlyCleanupJob = cron.schedule('0 2 1 * *', async () => {
      logger.info('🧹 Starting monthly cleanup of old notification schedules...');
      try {
        const cleaned = await this.renewalService.cleanupOldSchedules(90);
        logger.info(`✅ Monthly cleanup completed: ${cleaned} old schedules removed`);
      } catch (error) {
        logger.error('❌ Error in monthly cleanup:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    // Daily health check (runs every day at 12:00 PM)
    const healthCheckJob = cron.schedule('0 12 * * *', async () => {
      logger.info('🏥 Running daily system health check...');
      try {
        await this.performHealthCheck();
      } catch (error) {
        logger.error('❌ Error in health check:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    // Store jobs for management
    this.jobs.set('monthlyCleanup', monthlyCleanupJob);
    this.jobs.set('healthCheck', healthCheckJob);

    // Start the jobs
    monthlyCleanupJob.start();
    healthCheckJob.start();

    logger.info('🔧 Maintenance jobs scheduled:');
    logger.info('   - Monthly cleanup: 1st of month at 2:00 AM IST');
    logger.info('   - Daily health check: 12:00 PM IST');
  }

  /**
   * Retry failed notifications
   */
  async retryFailedNotifications() {
    try {
      const models = (await import('../models/index.js')).default;
      const failedNotifications = await models.NotificationSchedule.getFailedNotifications();

      if (failedNotifications.length === 0) {
        logger.info('✅ No failed notifications to retry');
        return;
      }

      logger.info(`🔄 Retrying ${failedNotifications.length} failed notifications...`);

      let retrySuccessful = 0;
      let retryFailed = 0;

      for (const notification of failedNotifications) {
        if (notification.canRetry()) {
          try {
            const result = await this.renewalService.sendRenewalNotification(notification);
            if (result.success) {
              retrySuccessful++;
              await notification.markAsSent({ email: result.email });
              logger.info(`✅ Retry successful for notification ${notification.id}`);
            } else {
              retryFailed++;
              await notification.markAsFailed(result.error || 'Retry failed');
              logger.warn(`⚠️ Retry failed for notification ${notification.id}: ${result.error}`);
            }
          } catch (error) {
            retryFailed++;
            await notification.markAsFailed(error.message);
            logger.error(`❌ Retry error for notification ${notification.id}:`, error);
          }
        } else {
          logger.warn(`⚠️ Notification ${notification.id} exceeded max retries`);
        }
      }

      logger.info(`✅ Retry process completed: ${retrySuccessful} successful, ${retryFailed} failed`);
    } catch (error) {
      logger.error('❌ Error in retry failed notifications:', error);
      throw error;
    }
  }

  /**
   * Perform system health check
   */
  async performHealthCheck() {
    try {
      const models = (await import('../models/index.js')).default;
      
      // Check database connectivity
      await models.sequelize.authenticate();
      logger.info('✅ Database connection healthy');

      // Check pending notifications count
      const pendingCount = await models.NotificationSchedule.count({
        where: { status: 'scheduled' }
      });
      logger.info(`📊 Pending notifications: ${pendingCount}`);

      // Check failed notifications count
      const failedCount = await models.NotificationSchedule.count({
        where: { status: 'failed' }
      });
      logger.info(`📊 Failed notifications: ${failedCount}`);

      // Alert if too many failed notifications
      if (failedCount > 50) {
        logger.warn(`⚠️ High number of failed notifications: ${failedCount}`);
      }

      logger.info('✅ System health check completed');
    } catch (error) {
      logger.error('❌ System health check failed:', error);
      throw error;
    }
  }

  /**
   * Manually trigger renewal notification processing
   */
  async triggerRenewalNotifications(date = null) {
    try {
      logger.info('🔄 Manually triggering renewal notification processing...');
      const result = await this.renewalService.processPendingNotifications(date);
      logger.info(`✅ Manual trigger completed: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      logger.error('❌ Error in manual trigger:', error);
      throw error;
    }
  }

  /**
   * Manually trigger schedule generation
   */
  async triggerScheduleGeneration(tenantId = null) {
    try {
      logger.info('🔄 Manually triggering schedule generation...');
      const scheduled = await this.renewalService.scheduleAllRenewalNotifications(tenantId);
      logger.info(`✅ Manual schedule generation completed: ${scheduled} notifications scheduled`);
      return scheduled;
    } catch (error) {
      logger.error('❌ Error in manual schedule generation:', error);
      throw error;
    }
  }

  /**
   * Get job status
   */
  getJobStatus() {
    const status = {};
    for (const [name, job] of this.jobs) {
      status[name] = {
        running: job.running,
        scheduled: job.scheduled,
        destroyed: job.destroyed
      };
    }
    return status;
  }

  /**
   * Stop all jobs
   */
  stopAllJobs() {
    logger.info('🛑 Stopping all scheduled jobs...');
    for (const [name, job] of this.jobs) {
      job.stop();
      logger.info(`   - Stopped: ${name}`);
    }
    logger.info('✅ All scheduled jobs stopped');
  }

  /**
   * Start all jobs
   */
  startAllJobs() {
    logger.info('▶️ Starting all scheduled jobs...');
    for (const [name, job] of this.jobs) {
      job.start();
      logger.info(`   - Started: ${name}`);
    }
    logger.info('✅ All scheduled jobs started');
  }

  /**
   * Destroy all jobs
   */
  destroyAllJobs() {
    logger.info('💥 Destroying all scheduled jobs...');
    for (const [name, job] of this.jobs) {
      job.destroy();
      logger.info(`   - Destroyed: ${name}`);
    }
    this.jobs.clear();
    this.isInitialized = false;
    logger.info('✅ All scheduled jobs destroyed');
  }
}

// Create singleton instance
const scheduledJobService = new ScheduledJobService();

export default scheduledJobService;
