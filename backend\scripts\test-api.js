import fetch from 'node-fetch';

async function testServiceAPI() {
  try {
    console.log('🔍 Testing service API...');

    // Test the service calls API endpoint
    const response = await fetch('http://localhost:8080/api/v1/service-calls?limit=20', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // You might need to add authentication headers here
      }
    });

    if (!response.ok) {
      console.log(`❌ API request failed: ${response.status} ${response.statusText}`);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response received');
    console.log('📊 Total services:', data.data?.length || 0);

    if (data.data && data.data.length > 0) {
      console.log('\n📋 Service details:');
      data.data.forEach((service, index) => {
        const scheduledDate = service.scheduled_date ? new Date(service.scheduled_date) : null;
        const today = new Date();
        const isOverdue = scheduledDate && scheduledDate < today && service.status?.category === 'open';
        const daysOverdue = isOverdue ? Math.ceil((today - scheduledDate) / (1000 * 60 * 60 * 24)) : 0;

        console.log(`\n${index + 1}. ${service.call_number || 'No Number'}`);
        console.log(`   Customer: ${service.customer?.company_name || 'Unknown'}`);
        console.log(`   Scheduled: ${scheduledDate ? scheduledDate.toISOString() : 'Not scheduled'}`);
        console.log(`   Status: ${service.status?.name || 'No status'} (${service.status?.category || 'No category'})`);
        console.log(`   Is Overdue: ${isOverdue ? `YES (${daysOverdue} days)` : 'NO'}`);
      });
    } else {
      console.log('❌ No services found in API response');
    }

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

testServiceAPI();
