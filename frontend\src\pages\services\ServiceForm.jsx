import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService, masterDataAPI } from '../../services/api';
import { FaSave, FaTimes, FaUser, FaTools, FaCalendar, FaRupeeSign, FaPlus, FaCheckCircle } from 'react-icons/fa';

const ServiceForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  // Helper function to map service types
  const mapServiceType = (type) => {
    const mapping = {
      'Installation': 'onsite',
      'Support': 'online',
      'Training': 'onsite',
      'Maintenance': 'onsite',
      'Remote Support': 'online',
      'Phone Support': 'phone'
    };
    return mapping[type] || 'online';
  };

  // Reverse mapping function for edit mode
  const reverseMapServiceType = (callType) => {
    const reverseMapping = {
      'onsite': 'Installation',
      'online': 'Support',
      'phone': 'Phone Support'
    };
    return reverseMapping[callType] || 'Support';
  };

  const [loading, setLoading] = useState(false);
  const [statusUpdating, setStatusUpdating] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [technicians, setTechnicians] = useState([]);
  const [callStatuses, setCallStatuses] = useState([]);
  const [isServiceCompleted, setIsServiceCompleted] = useState(false);
  const [selectedCustomerAMC, setSelectedCustomerAMC] = useState(null);
  const [formData, setFormData] = useState({
    // Service Information
    serviceNumber: '',
    customerId: '',
    type: '',
    typeOfCall: '', // New field for Type of Call
    category: '',
    description: '',
    priority: 'medium',
    status: '', // Will be set to default status UUID after loading call statuses

    // Assignment
    assignedTo: '',

    // Scheduling
    scheduledDate: '',
    scheduledTime: '',
    estimatedHours: '',

    // Location
    serviceLocation: 'customer-site',
    address: '',
    city: '',
    state: '',

    // Financial
    amount: '',
    currency: 'INR',
    paymentStatus: 'pending',

    // Additional Details
    requirements: '',
    notes: '',

    // Customer Contact
    contactPerson: '',
    contactPhone: '',
    contactEmail: ''
  });

  const [errors, setErrors] = useState({});

  // Fetch data for dropdowns and edit mode
  useEffect(() => {
    fetchCustomers();
    fetchTechnicians();
    fetchCallStatuses();

    if (isEdit) {
      fetchService();
    } else {
      // Generate service number for new services
      const serviceNumber = `SRV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, serviceNumber }));
    }
  }, [isEdit, id]);

  const fetchCustomers = async () => {
    try {
      const response = await apiService.get('/customers');
      if (response.data?.success && response.data?.data?.customers) {
        setCustomers(response.data.data.customers.map(customer => {
          const customFields = customer.custom_fields || {};
          const hasActiveAMC = customFields.amc_status === 'active' || customFields.amc_status === 'YES';

          return {
            id: customer.id,
            name: customer.company_name,
            contactPerson: customer.contact_person,
            defaultCallType: hasActiveAMC ? 'amc_call' : (customFields.default_call_type || 'free_call'),
            hasActiveAMC
          };
        }));
      }
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
    }
  };

  const fetchTechnicians = async () => {
    try {
      const response = await apiService.get('/executives');
      if (response.data?.success && response.data?.data?.executives) {
        setTechnicians(response.data.data.executives.map(executive => ({
          id: executive.id,
          name: `${executive.first_name} ${executive.last_name}`,
          specialization: executive.department || 'Support'
        })));
      }
    } catch (error) {
      console.error('Error fetching technicians:', error);
      // Fallback to mock data with UUID-like IDs
      setTechnicians([
        { id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', name: 'Raj Kumar', specialization: 'Installation' },
        { id: 'b2c3d4e5-f6g7-8901-bcde-f23456789012', name: 'Priya Sharma', specialization: 'Support' },
        { id: 'c3d4e5f6-g7h8-9012-cdef-************', name: 'Amit Singh', specialization: 'Training' },
        { id: 'd4e5f6g7-h8i9-0123-def0-************', name: 'Vikash Gupta', specialization: 'Maintenance' }
      ]);
    }
  };

  const fetchCallStatuses = async () => {
    try {
      // Fetch ALL call statuses without pagination limits
      const response = await masterDataAPI.getCallStatuses({
        limit: 100, // Increase limit to get all statuses
        page: 1,
        isActive: true, // Only get active statuses
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success) {
        const statuses = response.data.data.callstatus || [];
        setCallStatuses(statuses);
        console.log('📋 Loaded call statuses:', statuses.length, 'statuses found');
        console.log('📋 Status details:', statuses.map(s => ({ id: s.id, name: s.name, code: s.code })));

        // Set default status for new service creation
        if (!isEdit && statuses.length > 0) {
          const defaultStatus = statuses.find(status =>
            status.code === 'PENDING' ||
            status.name.toLowerCase() === 'pending'
          );

          if (defaultStatus) {
            setFormData(prev => ({
              ...prev,
              status: defaultStatus.id
            }));
            console.log('🔄 Default status set to "Pending" for new service:', defaultStatus);
          }
        }
      } else {
        console.error('❌ API response indicates failure:', response.data);
        throw new Error(response.data?.message || 'Failed to fetch call statuses');
      }
    } catch (error) {
      console.error('❌ Error fetching call statuses:', error);
      // Fallback to hardcoded statuses with proper UUIDs (these should be replaced with actual UUIDs from your database)
      setCallStatuses([
        { id: 'pending-uuid', name: 'Pending', code: 'PENDING' },
        { id: 'scheduled-uuid', name: 'Scheduled', code: 'SCHEDULED' },
        { id: 'in-progress-uuid', name: 'In Progress', code: 'IN_PROGRESS' },
        { id: 'completed-uuid', name: 'Completed', code: 'COMPLETED' },
        { id: 'cancelled-uuid', name: 'Cancelled', code: 'CANCELLED' }
      ]);
    }
  };

  const fetchCustomerAMCDetails = async (customerId) => {
    try {
      const response = await apiService.get(`/customers/${customerId}`);
      if (response.data?.success && response.data?.data?.customer) {
        const customer = response.data.data.customer;
        const customFields = customer.custom_fields || {};

        // Check if customer has active AMC from custom_fields
        const hasActiveAMC = customFields.amc_status === 'active' || customFields.amc_status === 'YES';

        if (hasActiveAMC && customFields.amc_to_date) {
          // Calculate next visit date based on number of visits per year
          const noOfVisits = customFields.no_of_visits || 4; // Default to 4 visits per year
          const monthsBetweenVisits = Math.floor(12 / noOfVisits);
          const nextVisitDate = new Date();
          nextVisitDate.setMonth(nextVisitDate.getMonth() + monthsBetweenVisits);

          setSelectedCustomerAMC({
            expiryDate: customFields.amc_to_date,
            renewalDate: customFields.renewal_date,
            nextVisit: nextVisitDate.toISOString().split('T')[0],
            fromDate: customFields.amc_from_date,
            noOfVisits: customFields.no_of_visits,
            currentAmount: customFields.current_amc_amount,
            lastYearAmount: customFields.last_year_amc_amount
          });
        } else {
          setSelectedCustomerAMC(null);
        }
      }
    } catch (error) {
      console.error('Error fetching customer AMC details:', error);
      setSelectedCustomerAMC(null);
    }
  };

  const fetchService = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${id}`);
      if (response.data?.success && response.data?.data?.serviceCall) {
        const service = response.data.data.serviceCall;

        console.log('🔍 Fetched service data for edit (ServiceForm):', service);

        setFormData({
          serviceNumber: service.call_number || `SRV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`,
          customerId: service.customer_id || '',
          type: reverseMapServiceType(service.call_type) || '',
          typeOfCall: service.call_billing_type || service.type_of_call || '', // Load call Call type or fallback to legacy field
          category: service.category || '',
          priority: service.priority ? service.priority.charAt(0).toUpperCase() + service.priority.slice(1) : 'Medium',
          status: service.status_id || service.status?.id || '',
          description: service.description || service.subject || '',
          assignedTo: service.assigned_to || '',
          scheduledDate: service.scheduled_date ? service.scheduled_date.split('T')[0] : '',
          scheduledTime: service.scheduled_time || '',
          estimatedHours: service.estimated_hours || '',
          serviceLocation: service.service_location || 'customer-site',
          address: service.address || '',
          city: service.city || '',
          state: service.state || '',
          amount: service.service_charges || '',
          currency: service.currency || 'INR',
          paymentStatus: service.payment_status || 'pending',
          requirements: service.customer_reported_issue || '',
          notes: service.internal_notes || '',
          contactPerson: service.contactPerson ?
            `${service.contactPerson.first_name || ''} ${service.contactPerson.last_name || ''}`.trim() :
            service.contact_person || '',
          contactPhone: service.contactPerson?.phone || service.contact_phone || '',
          contactEmail: service.contactPerson?.email || service.contact_email || ''
        });

        // Check if service is in a completed/final status
        const currentStatus = service.status;
        const isCompleted = currentStatus && (
          currentStatus.is_final ||
          ['COMPLETED', 'NO_ISSUE', 'CANCELLED'].includes(currentStatus.code) ||
          currentStatus.name.toLowerCase() === 'completed'
        );
        setIsServiceCompleted(isCompleted);

        if (isCompleted) {
          console.log('🔒 Service is completed/final - editing will be restricted:', {
            statusName: currentStatus.name,
            statusCode: currentStatus.code,
            isFinal: currentStatus.is_final
          });
        }

        toast.success('Service data loaded successfully');
      } else {
        toast.error('Service call not found');
      }
    } catch (error) {
      console.error('Error fetching service:', error);
      toast.error('Failed to load service data');
      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Handle status changes separately to avoid form submission and validation
    if (name === 'status') {
      handleStatusUpdate(value);
      // Clear any status-related errors
      if (errors.status) {
        setErrors(prev => ({
          ...prev,
          status: ''
        }));
      }
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Auto-fill customer details when customer is selected
    if (name === 'customerId' && value) {
      const selectedCustomer = customers.find(c => c.id === value);
      if (selectedCustomer) {
        const callType = selectedCustomer.defaultCallType || 'free_call';
        setFormData(prev => ({
          ...prev,
          contactPerson: selectedCustomer.contactPerson,
          typeOfCall: callType
        }));

        // Fetch customer AMC details if customer has active AMC
        if (selectedCustomer.hasActiveAMC) {
          fetchCustomerAMCDetails(value);
          console.log('✅ Customer has active AMC - Auto-selected AMC Call type');
        } else {
          setSelectedCustomerAMC(null);
          console.log('ℹ️ Customer has no active AMC - Auto-selected Free Call type');
        }
      }
    }

    // Handle call type change - fetch AMC details when AMC Call is selected
    if (name === 'typeOfCall' && value === 'amc_call' && formData.customerId) {
      fetchCustomerAMCDetails(formData.customerId);
    } else if (name === 'typeOfCall' && value !== 'amc_call') {
      // Clear AMC details when call type is not AMC Call
      setSelectedCustomerAMC(null);
    }
  };

  // Separate handler for status updates to avoid form submission
  const handleStatusUpdate = async (newStatus) => {
    if (!isEdit) {
      // For new services, just update the local state
      setFormData(prev => ({
        ...prev,
        status: newStatus
      }));
      return;
    }

    try {
      setStatusUpdating(true);

      // Optimistically update the UI
      const previousStatus = formData.status;
      setFormData(prev => ({
        ...prev,
        status: newStatus
      }));

      // Create the update payload with just the status_id (UUID)
      const statusData = {
        status_id: newStatus
      };

      console.log('🔄 Updating service status:', { id, newStatus, statusData });

      // Make API call to update only the status
      const response = await apiService.put(`/service-calls/${id}`, statusData);

      if (response.data?.success) {
        const statusName = callStatuses.find(s => s.id === newStatus)?.name || newStatus;
        toast.success(`Status updated to ${statusName}`);
        console.log('✅ Status update successful:', response.data);

        // Clear any existing form errors since status update was successful
        setErrors({});
      } else {
        // Revert on failure
        setFormData(prev => ({
          ...prev,
          status: previousStatus
        }));

        // Handle specific validation errors
        if (response.data?.errors && typeof response.data.errors === 'object') {
          const errorMessages = Object.values(response.data.errors).flat();
          toast.error(`Validation Error: ${errorMessages.join(', ')}`);
        } else {
          toast.error(response.data?.message || 'Failed to update status');
        }
      }
    } catch (error) {
      console.error('❌ Error updating status:', error);
      // Revert on error
      setFormData(prev => ({
        ...prev,
        status: previousStatus
      }));

      // Handle different types of errors
      if (error.response?.data?.errors && typeof error.response.data.errors === 'object') {
        const errorMessages = Object.values(error.response.data.errors).flat();
        toast.error(`Validation Error: ${errorMessages.join(', ')}`);
      } else if (error.response?.data?.details?.userFriendlyMessage) {
        toast.error(error.response.data.details.userFriendlyMessage);
      } else if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to update status. Please check your connection and try again.');
      }
    } finally {
      setStatusUpdating(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Only customer is truly required
    if (!formData.customerId) {
      newErrors.customerId = 'Please select a customer';
    }

    // Optional field validations - only validate if they have values
    if (formData.amount && formData.amount.trim()) {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount < 0) {
        newErrors.amount = 'Please enter a valid amount (0 or greater)';
      }
    }

    if (formData.estimatedHours && formData.estimatedHours.trim()) {
      const hours = parseFloat(formData.estimatedHours);
      if (isNaN(hours) || hours < 0 || hours > 24) {
        newErrors.estimatedHours = 'Please enter valid estimated hours (0-24)';
      }
    }

    // Description length validation
    if (formData.description && formData.description.length > 2000) {
      newErrors.description = 'Description must not exceed 2000 characters';
    }

    // Date validation - if provided, should not be in the past
    if (formData.scheduledDate) {
      const selectedDate = new Date(formData.scheduledDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        newErrors.scheduledDate = 'Scheduled date cannot be in the past';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Prevent updates to completed services
    if (isEdit && isServiceCompleted) {
      toast.error('Cannot update completed service. This service is already completed and cannot be modified.');
      return;
    }

    if (!validateForm()) {
      // Show specific validation errors
      const errorMessages = Object.values(errors).filter(Boolean);
      if (errorMessages.length > 0) {
        toast.error(`Please fix the following errors: ${errorMessages.join(', ')}`);
      } else {
        toast.error('Please check the form for errors');
      }
      return;
    }

    setLoading(true);

    try {
      // Prepare data for API - only include non-empty values
      const serviceData = {};

      // Required fields
      if (formData.customerId) serviceData.customer_id = formData.customerId;
      if (formData.description) {
        serviceData.subject = formData.description.substring(0, 200); // Use description as subject
        serviceData.description = formData.description;
      }

      // Optional fields - only include if they have values
      if (formData.assignedTo && formData.assignedTo !== '') {
        serviceData.assigned_to = formData.assignedTo;
        console.log('🔍 Setting assigned_to:', { value: formData.assignedTo, type: typeof formData.assignedTo });
      } else {
        console.log('🔍 assigned_to is empty, not including in request');
      }
      if (formData.type) serviceData.call_type = mapServiceType(formData.type);
      // Always set call_billing_type, even if empty (backend will set default)
      serviceData.call_billing_type = formData.typeOfCall || '';
      if (formData.typeOfCall) {
        serviceData.type_of_call = formData.typeOfCall; // Legacy field
      }
      if (formData.priority) serviceData.priority = formData.priority.toLowerCase();
      if (formData.status) serviceData.status_id = formData.status; // Send status as UUID
      if (formData.scheduledDate) serviceData.scheduled_date = formData.scheduledDate;
      if (formData.estimatedHours) serviceData.estimated_hours = parseFloat(formData.estimatedHours);
      if (formData.amount) serviceData.service_charges = parseFloat(formData.amount);

      // Map other fields
      if (formData.requirements) serviceData.customer_reported_issue = formData.requirements;
      if (formData.notes) serviceData.internal_notes = formData.notes;

      console.log('📤 Sending service data to API:', serviceData);

      let response;
      if (isEdit) {
        response = await apiService.put(`/service-calls/${id}`, serviceData);
      } else {
        response = await apiService.post('/service-calls', serviceData);
      }

      if (response.data?.success) {
        toast.success(isEdit ? 'Service request updated successfully' : 'Service request created successfully');
        navigate('/services');
      } else {
        toast.error(response.data?.message || 'Failed to save service request');
      }
    } catch (error) {
      console.error('Error saving service request:', error);

      // Handle validation errors from backend
      if (error.response?.status === 400 && error.response?.data?.errors) {
        const backendErrors = error.response.data.errors;
        setErrors(prevErrors => ({ ...prevErrors, ...backendErrors }));

        // Show user-friendly error message
        const errorMessages = Object.values(backendErrors).filter(Boolean);
        if (errorMessages.length > 0) {
          toast.error(`Validation failed: ${errorMessages.join(', ')}`);
        } else {
          toast.error('Please check the form for errors');
        }
      } else {
        // Show general error message, prioritizing user-friendly message
        const errorMessage = error.response?.data?.details?.userFriendlyMessage ||
                            error.response?.data?.message ||
                            'Something went wrong. Please try again.';
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/services');
  };

  if (loading && isEdit) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                    <FaTools className="text-xl" style={{ color: 'var(--primary-text)' }} />
                  </div>
                  {isEdit ? 'Edit Service Request' : 'New Service Request'}
                </h2>
                <p className="text-lg" style={{ color: 'rgba(var(--primary-text-rgb), 0.8)' }}>
                  {isEdit ? 'Update service request details and assignments' : 'Create a new service request for customer support'}
                </p>
              </div>
              <div className="flex gap-3">
                <Link
                  to="/customers/new"
                  className="inline-flex items-center px-6 py-3 border-2 border-white border-opacity-30 text-sm font-medium rounded-xl text-white bg-emerald-500 bg-opacity-90 hover:bg-opacity-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Add New Customer
                </Link>
                <button
                  type="button"
                  className="inline-flex items-center px-6 py-3 border-2 border-white border-opacity-30 text-sm font-medium rounded-xl text-white bg-white bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                  onClick={handleCancel}
                >
                  <FaTimes className="mr-2 h-4 w-4" />
                  Cancel
                </button>
                <button
                  type="submit"
                  form="serviceForm"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-green-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg disabled:opacity-50"
                  disabled={loading || isServiceCompleted}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-2"></div>
                      {isEdit ? 'Updating...' : 'Creating...'}
                    </>
                  ) : isServiceCompleted ? (
                    <>
                      <FaCheckCircle className="mr-2 h-4 w-4" />
                      Service Completed
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2 h-4 w-4" />
                      {isEdit ? 'Update Service' : 'Create Service'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Completion Status Banner */}
        {isEdit && isServiceCompleted && (
          <div className="mb-8 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center">
              <FaCheckCircle className="text-orange-600 mr-3" />
              <div>
                <h3 className="text-orange-800 font-semibold">Service Completed</h3>
                <p className="text-orange-700 text-sm">
                  This service call has been completed and is now read-only. No further modifications are allowed.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form id="serviceForm" onSubmit={handleSubmit} className="space-y-8">
          {/* Service Information */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaTools className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Service Information
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Service Number */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">Service Number</label>
                  <input
                    type="text"
                    className="block w-full px-4 py-3 border-2 border-green-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-gray-50 transition-all duration-200 sm:text-sm"
                    name="serviceNumber"
                    value={formData.serviceNumber}
                    readOnly
                    placeholder="Auto-generated"
                  />
                </div>

                {/* Customer */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">Customer *</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.customerId
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="customerId"
                    value={formData.customerId}
                    onChange={handleInputChange}
                  >
                    <option value="">Select customer</option>
                    {customers.map(customer => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                  {errors.customerId && <p className="mt-2 text-sm text-red-600 font-medium">{errors.customerId}</p>}
                </div>

                {/* AMC Details - Show only when call type is AMC Call */}
                {formData.typeOfCall === 'amc_call' && selectedCustomerAMC && (
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <h4 className="text-sm font-bold text-blue-700 mb-3 flex items-center">
                      <FaCheckCircle className="mr-2" />
                      AMC Contract Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-blue-600 mb-1">AMC Expiry Date</label>
                        <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                          {selectedCustomerAMC.expiryDate ? new Date(selectedCustomerAMC.expiryDate).toLocaleDateString() : 'N/A'}
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-blue-600 mb-1">Renewal Date</label>
                        <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                          {selectedCustomerAMC.renewalDate ? new Date(selectedCustomerAMC.renewalDate).toLocaleDateString() : 'N/A'}
                        </div>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-blue-600 mb-1">Next Visit Date</label>
                        <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                          {selectedCustomerAMC.nextVisit ? new Date(selectedCustomerAMC.nextVisit).toLocaleDateString() : 'N/A'}
                        </div>
                      </div>
                      {selectedCustomerAMC.noOfVisits && (
                        <div>
                          <label className="block text-xs font-medium text-blue-600 mb-1">Visits Per Year</label>
                          <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                            {selectedCustomerAMC.noOfVisits}
                          </div>
                        </div>
                      )}
                      {selectedCustomerAMC.currentAmount && (
                        <div>
                          <label className="block text-xs font-medium text-blue-600 mb-1">Current AMC Amount</label>
                          <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                            ₹{parseFloat(selectedCustomerAMC.currentAmount).toLocaleString()}
                          </div>
                        </div>
                      )}
                      {selectedCustomerAMC.fromDate && (
                        <div>
                          <label className="block text-xs font-medium text-blue-600 mb-1">AMC From Date</label>
                          <div className="text-sm text-gray-800 bg-white px-3 py-2 rounded-lg border">
                            {new Date(selectedCustomerAMC.fromDate).toLocaleDateString()}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Service Type */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Service Type</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.type
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-purple-200 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300'
                    }`}
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                  >
                    <option value="">Select service type</option>
                    <option value="Installation">🔧 Installation</option>
                    <option value="Support">🛠️ Support</option>
                    <option value="Training">📚 Training</option>
                    <option value="Maintenance">⚙️ Maintenance</option>
                    <option value="Consultation">💡 Consultation</option>
                    <option value="Data Migration">📊 Data Migration</option>
                  </select>
                  {errors.type && <p className="mt-2 text-sm text-red-600 font-medium">{errors.type}</p>}
                </div>

                {/* Call Type */}
                <div>
                  <label className="block text-sm font-bold text-blue-700 mb-2">Call Type</label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.typeOfCall
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="typeOfCall"
                    value={formData.typeOfCall}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Call type</option>
                    <option value="free_call">Free Call</option>
                    <option value="amc_call">AMC Call</option>
                    <option value="per_call">Per Call</option>
                  </select>
                  {errors.typeOfCall && <p className="mt-2 text-sm text-red-600 font-medium">{errors.typeOfCall}</p>}
                </div>


                {/* Priority */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Priority</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300 transition-all duration-200 sm:text-sm"
                    name="priority"
                    value={formData.priority}
                    onChange={handleInputChange}
                  >
                    <option value="low">🟢 Low</option>
                    <option value="medium">🟡 Medium</option>
                    <option value="high">🟠 High</option>
                    <option value="urgent">🔴 Urgent</option>
                  </select>
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">
                    Status
                    {statusUpdating && (
                      <span className="ml-2 text-xs text-blue-600">
                        <span className="animate-spin inline-block w-3 h-3 border border-blue-600 border-t-transparent rounded-full mr-1"></span>
                        Updating...
                      </span>
                    )}
                  </label>
                  <select
                    className={`block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 sm:text-sm ${
                      isServiceCompleted || statusUpdating ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:border-indigo-300'
                    }`}
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    disabled={isServiceCompleted || statusUpdating}
                  >
                    <option value="">Select status...</option>
                    {callStatuses.map((status) => (
                      <option key={status.id} value={status.id}>
                        {status.name} {status.code && `(${status.code})`}
                      </option>
                    ))}
                  </select>
                  {isServiceCompleted && (
                    <p className="text-orange-600 text-xs mt-1">
                      🔒 Status cannot be changed - service is completed
                    </p>
                  )}
                  {statusUpdating && (
                    <p className="text-blue-600 text-xs mt-1">
                      🔄 Updating status...
                    </p>
                  )}
                </div>

                {/* Description */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-gray-700 mb-2">
                    Description
                    <span className="text-gray-500 font-normal text-xs ml-2">(Optional - max 2000 characters)</span>
                  </label>
                  <textarea
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 ${
                      errors.description
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-gray-200 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300'
                    }`}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="Describe the service requirements in detail (optional)"
                    maxLength="2000"
                  >
                  </textarea>
                  {formData.description && (
                    <p className="text-xs text-gray-500 mt-1">
                      {formData.description.length}/2000 characters
                    </p>
                  )}
                  {errors.description && <p className="mt-2 text-sm text-red-600 font-medium">{errors.description}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Assignment & Scheduling */}
          <div className="bg-white shadow-xl rounded-2xl overflow-hidden form-section-border">
            <div className="form-section-header px-6 py-4">
              <h3 className="text-xl font-bold flex items-center text-primary-dynamic">
                <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb), 0.2)' }}>
                  <FaUser className="h-4 w-4" style={{ color: 'var(--primary-text)' }} />
                </div>
                Assignment & Scheduling
              </h3>
            </div>
            <div className="p-6 form-section-background">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Assigned Technician */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-blue-700 mb-2">
                    Assigned Technician
                    <span className="text-gray-500 font-normal text-xs ml-2">(Optional - can be assigned later)</span>
                  </label>
                  <select
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.assignedTo
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-blue-200 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300'
                    }`}
                    name="assignedTo"
                    value={formData.assignedTo}
                    onChange={handleInputChange}
                  >
                    <option value="">Select technician (optional)</option>
                    {technicians.map(tech => (
                      <option key={tech.id} value={tech.id}>
                        {tech.name} ({tech.specialization})
                      </option>
                    ))}
                  </select>
                  {errors.assignedTo && <p className="mt-2 text-sm text-red-600 font-medium">{errors.assignedTo}</p>}
                </div>

                {/* Scheduled Date */}
                <div>
                  <label className="block text-sm font-bold text-green-700 mb-2">
                    Scheduled Date
                    <span className="text-gray-500 font-normal text-xs ml-2">(Optional)</span>
                  </label>
                  <input
                    type="date"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.scheduledDate
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-green-200 focus:ring-green-500 focus:border-green-500 bg-white hover:border-green-300'
                    }`}
                    name="scheduledDate"
                    value={formData.scheduledDate}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                  />
                  {errors.scheduledDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.scheduledDate}</p>}
                </div>

                {/* Scheduled Time */}
                <div>
                  <label className="block text-sm font-bold text-purple-700 mb-2">Scheduled Time</label>
                  <input
                    type="time"
                    className="block w-full px-4 py-3 border-2 border-purple-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white hover:border-purple-300 transition-all duration-200 sm:text-sm"
                    name="scheduledTime"
                    value={formData.scheduledTime}
                    onChange={handleInputChange}
                  />
                </div>

                {/* Estimated Hours */}
                <div>
                  <label className="block text-sm font-bold text-orange-700 mb-2">Estimated Hours</label>
                  <input
                    type="number"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.estimatedHours
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-orange-200 focus:ring-orange-500 focus:border-orange-500 bg-white hover:border-orange-300'
                    }`}
                    name="estimatedHours"
                    value={formData.estimatedHours}
                    onChange={handleInputChange}
                    placeholder="0"
                    min="0"
                    step="0.5"
                  />
                  {errors.estimatedHours && <p className="mt-2 text-sm text-red-600 font-medium">{errors.estimatedHours}</p>}
                </div>

                {/* Service Amount */}
                <div>
                  <label className="block text-sm font-bold text-teal-700 mb-2">
                    Service Amount
                    <span className="text-gray-500 font-normal text-xs ml-2">(Optional - ₹)</span>
                  </label>
                  <input
                    type="number"
                    className={`block w-full px-4 py-3 border-2 rounded-xl shadow-sm focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm ${
                      errors.amount
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50'
                        : 'border-teal-200 focus:ring-teal-500 focus:border-teal-500 bg-white hover:border-teal-300'
                    }`}
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    placeholder="₹ 0 (optional)"
                    min="0"
                    step="0.01"
                  />
                  {errors.amount && <p className="mt-2 text-sm text-red-600 font-medium">{errors.amount}</p>}
                </div>

                {/* Service Location */}
                <div>
                  <label className="block text-sm font-bold text-indigo-700 mb-2">Service Location</label>
                  <select
                    className="block w-full px-4 py-3 border-2 border-indigo-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white hover:border-indigo-300 transition-all duration-200 sm:text-sm"
                    name="serviceLocation"
                    value={formData.serviceLocation}
                    onChange={handleInputChange}
                  >
                    <option value="customer-site">🏢 Customer Site</option>
                    <option value="office">🏬 Our Office</option>
                    <option value="remote">💻 Remote</option>
                  </select>
                </div>

                {/* Additional Notes */}
                <div className="md:col-span-3">
                  <label className="block text-sm font-bold text-gray-700 mb-2">Additional Notes</label>
                  <textarea
                    className="block w-full px-4 py-3 border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500 bg-white hover:border-gray-300 transition-all duration-200"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows="3"
                    placeholder="Any additional notes or special instructions"
                  >
                  </textarea>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServiceForm;
