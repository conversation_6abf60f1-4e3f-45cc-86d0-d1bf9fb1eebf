/**
 * Update Service Call Amounts
 * Simple script to add revenue data to existing service calls
 */

import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create database connection
const sequelize = new Sequelize(
  process.env.DB_NAME || 'tallycrm',
  process.env.DB_USER || 'postgres', 
  process.env.DB_PASSWORD || 'password',
  {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: false
  }
);

async function updateServiceAmounts() {
  try {
    console.log('🚀 Updating service call amounts...\n');

    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');

    // Get service calls that need updating
    const getServiceCallsQuery = `
      SELECT id FROM service_calls
      WHERE total_amount = 0 OR total_amount IS NULL
      ORDER BY created_at DESC
      LIMIT 200;
    `;

    const [serviceCalls] = await sequelize.query(getServiceCallsQuery);
    console.log(`📋 Found ${serviceCalls.length} service calls to update`);

    let updatedCount = 0;

    // Update each service call individually
    for (let i = 0; i < serviceCalls.length; i++) {
      const call = serviceCalls[i];
      const billingTypeIndex = i % 3;

      let billingType, serviceCharges, travelCharges, isBillable, hourlyRate;

      switch (billingTypeIndex) {
        case 0: // Free call
          billingType = 'free_call';
          serviceCharges = 0;
          travelCharges = 0;
          isBillable = false;
          hourlyRate = null;
          break;
        case 1: // AMC call
          billingType = 'amc_call';
          serviceCharges = Math.floor(Math.random() * 2000) + 500; // 500-2500
          travelCharges = Math.floor(Math.random() * 500); // 0-500
          isBillable = true;
          hourlyRate = null;
          break;
        case 2: // Per call
          billingType = 'per_call';
          serviceCharges = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
          travelCharges = Math.floor(Math.random() * 1000); // 0-1000
          isBillable = true;
          hourlyRate = Math.floor(Math.random() * 500) + 200; // 200-700
          break;
      }

      const totalAmount = serviceCharges + travelCharges;

      const updateIndividualQuery = `
        UPDATE service_calls
        SET
          call_billing_type = $1,
          service_charges = $2,
          travel_charges = $3,
          total_amount = $4,
          is_billable = $5,
          hourly_rate = $6,
          updated_at = NOW()
        WHERE id = $7;
      `;

      await sequelize.query(updateIndividualQuery, {
        bind: [billingType, serviceCharges, travelCharges, totalAmount, isBillable, hourlyRate, call.id]
      });

      updatedCount++;
    }

    console.log(`✅ Updated ${updatedCount} service calls with revenue data`);



    // Get summary statistics
    const summaryQuery = `
      SELECT 
        COUNT(*) as total_calls,
        COUNT(CASE WHEN call_billing_type = 'free_call' THEN 1 END) as free_calls,
        COUNT(CASE WHEN call_billing_type = 'amc_call' THEN 1 END) as amc_calls,
        COUNT(CASE WHEN call_billing_type = 'per_call' THEN 1 END) as per_calls,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as avg_revenue_per_call,
        COUNT(CASE WHEN total_amount > 0 THEN 1 END) as billable_calls
      FROM service_calls 
      WHERE tenant_id IS NOT NULL;
    `;

    const [summaryResults] = await sequelize.query(summaryQuery);
    const stats = summaryResults[0];

    console.log('\n📊 Service Calls Revenue Summary:');
    console.log(`   💰 Total Revenue: ₹${parseFloat(stats.total_revenue || 0).toLocaleString()}`);
    console.log(`   📞 Total Service Calls: ${stats.total_calls}`);
    console.log(`   💵 Average Revenue per Call: ₹${parseFloat(stats.avg_revenue_per_call || 0).toFixed(2)}`);
    console.log(`   💸 Billable Calls: ${stats.billable_calls}`);
    console.log(`   🆓 Free Calls: ${stats.free_calls}`);
    console.log(`   🔧 AMC Calls: ${stats.amc_calls}`);
    console.log(`   💳 Per Calls: ${stats.per_calls}`);

    // Create some sample sales if none exist
    const salesCountQuery = 'SELECT COUNT(*) as count FROM sales;';
    const [salesCount] = await sequelize.query(salesCountQuery);
    
    if (salesCount[0].count == 0) {
      console.log('\n📈 Creating sample sales data...');
      
      const createSalesQuery = `
        INSERT INTO sales (
          id, tenant_id, customer_id, created_by, sale_number, total_amount, 
          sale_date, payment_status, sale_type, created_at, updated_at
        )
        SELECT 
          gen_random_uuid(),
          sc.tenant_id,
          sc.customer_id,
          sc.created_by,
          'SALE' || LPAD((ROW_NUMBER() OVER())::TEXT, 6, '0'),
          (RANDOM() * 50000 + 10000)::DECIMAL(10,2),
          sc.call_date,
          CASE 
            WHEN RANDOM() < 0.7 THEN 'paid'
            WHEN RANDOM() < 0.9 THEN 'pending'
            ELSE 'overdue'
          END,
          'new',
          sc.created_at,
          NOW()
        FROM (
          SELECT DISTINCT ON (customer_id) 
            tenant_id, customer_id, created_by, call_date, created_at
          FROM service_calls 
          WHERE tenant_id IS NOT NULL 
            AND customer_id IS NOT NULL 
            AND created_by IS NOT NULL
          ORDER BY customer_id, created_at DESC
          LIMIT 30
        ) sc;
      `;

      const [salesResults] = await sequelize.query(createSalesQuery);
      console.log(`✅ Created ${salesResults} sample sales records`);

      // Get sales summary
      const salesSummaryQuery = `
        SELECT 
          COUNT(*) as total_sales,
          SUM(total_amount) as total_sales_revenue,
          AVG(total_amount) as avg_sale_amount
        FROM sales;
      `;

      const [salesSummary] = await sequelize.query(salesSummaryQuery);
      const salesStats = salesSummary[0];

      console.log('\n💼 Sales Summary:');
      console.log(`   💰 Total Sales Revenue: ₹${parseFloat(salesStats.total_sales_revenue || 0).toLocaleString()}`);
      console.log(`   📊 Total Sales: ${salesStats.total_sales}`);
      console.log(`   💵 Average Sale Amount: ₹${parseFloat(salesStats.avg_sale_amount || 0).toFixed(2)}`);

      const combinedRevenue = parseFloat(stats.total_revenue || 0) + parseFloat(salesStats.total_sales_revenue || 0);
      console.log('\n🎯 Combined Revenue:');
      console.log(`   💰 Total Combined Revenue: ₹${combinedRevenue.toLocaleString()}`);
    }

    console.log('\n🎉 Service call amounts updated successfully!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Refresh the analytics pages in the frontend');
    console.log('2. Check the Financial Analytics page for actual revenue data');
    console.log('3. Verify Service Analytics shows billing type distribution');

  } catch (error) {
    console.error('❌ Error updating service amounts:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the script
updateServiceAmounts();
