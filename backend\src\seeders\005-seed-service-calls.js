#!/usr/bin/env node

/**
 * Service Calls Sample Data Seeder
 * Seeds sample service calls with different billing types for testing analytics
 */

import models from '../models/index.js';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

const seedServiceCalls = async () => {
  try {
    logger.info('🔧 Seeding sample service calls...');

    // Get any available tenant
    let tenant = await models.Tenant.findOne({ where: { slug: 'demo' } });
    if (!tenant) {
      tenant = await models.Tenant.findOne({ where: { slug: 'default' } });
    }
    if (!tenant) {
      tenant = await models.Tenant.findOne({
        where: { is_active: true },
        order: [['created_at', 'ASC']]
      });
    }

    if (!tenant) {
      logger.warn('⚠️ No tenant found for service calls. Skipping service calls seeding.');
      return;
    }

    // Get required data
    const customers = await models.Customer.findAll({
      where: { tenant_id: tenant.id },
      limit: 10
    });
    const executives = await models.Executive.findAll({
      where: { tenant_id: tenant.id },
      limit: 5
    });
    const callStatuses = await models.CallStatus.findAll({ limit: 5 });
    const typeOfCalls = await models.TypeOfCall.findAll({ limit: 3 });
    const users = await models.User.findAll({
      where: { tenant_id: tenant.id },
      limit: 5
    });

    if (customers.length === 0 || executives.length === 0 || callStatuses.length === 0 || users.length === 0) {
      logger.warn('⚠️ Missing required data for service calls. Please ensure customers, executives, call statuses, and users exist.');
      return;
    }

    logger.info(`📋 Using tenant: ${tenant.name} with ${customers.length} customers, ${executives.length} executives`);

    // Generate service calls for the last 30 days
    const serviceCallsData = [];
    const today = new Date();
    
    for (let i = 0; i < 50; i++) {
      // Random date within last 30 days
      const daysAgo = Math.floor(Math.random() * 30);
      const callDate = new Date(today);
      callDate.setDate(callDate.getDate() - daysAgo);

      // Random customer, executive, and user
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const executive = executives[Math.floor(Math.random() * executives.length)];
      const status = callStatuses[Math.floor(Math.random() * callStatuses.length)];
      const typeOfCall = typeOfCalls[Math.floor(Math.random() * typeOfCalls.length)];
      const user = users[Math.floor(Math.random() * users.length)];

      // Random billing type distribution (40% free, 35% AMC, 25% paid)
      const rand = Math.random();
      let billingType, isUnderAmc, isBillable, serviceCharges;
      
      if (rand < 0.4) {
        // Free calls
        billingType = 'free_call';
        isUnderAmc = false;
        isBillable = false;
        serviceCharges = 0;
      } else if (rand < 0.75) {
        // AMC calls
        billingType = 'amc_call';
        isUnderAmc = true;
        isBillable = false;
        serviceCharges = 0;
      } else {
        // Per calls
        billingType = 'per_call';
        isUnderAmc = false;
        isBillable = true;
        serviceCharges = Math.floor(Math.random() * 5000) + 500; // 500-5500
      }

      // Random priority
      const priorities = ['low', 'medium', 'high', 'critical'];
      const priority = priorities[Math.floor(Math.random() * priorities.length)];

      // Random call type
      const callTypes = ['online', 'onsite', 'phone', 'email'];
      const callType = callTypes[Math.floor(Math.random() * callTypes.length)];

      // Generate call number
      const callNumber = `SC-${String(i + 1).padStart(3, '0')}`;

      const serviceCall = {
        id: uuidv4(),
        tenant_id: tenant.id,
        customer_id: customer.id,
        assigned_to: executive.id,
        status_id: status.id,
        created_by: user.id,
        type_of_call_id: typeOfCall?.id,
        call_number: callNumber,
        call_billing_type: billingType,
        is_under_amc: isUnderAmc,
        is_billable: isBillable,
        service_charges: serviceCharges,
        call_type: callType,
        priority: priority,
        subject: `Service call for ${customer.company_name} - ${typeOfCall?.name || 'General Support'}`,
        description: `${billingType.replace('_', ' ')} service call for ${customer.company_name}`,
        customer_reported_issue: 'Tally software issue reported by customer',
        contact_number: customer.phone || '+91-9876543210',
        company_name: customer.company_name,
        tally_serial_number: customer.tally_serial_number,
        created_at: callDate,
        updated_at: callDate,
        call_date: callDate,
        // Add some timing data for completed calls
        started_at: status.category === 'completed' ? new Date(callDate.getTime() + (Math.random() * 2 * 60 * 60 * 1000)) : null, // 0-2 hours after creation
        completed_at: status.category === 'completed' ? new Date(callDate.getTime() + (Math.random() * 8 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000)) : null, // 2-10 hours after creation
        total_time_seconds: status.category === 'completed' ? Math.floor(Math.random() * 14400) + 1800 : 0, // 30 minutes to 4 hours
        total_time_minutes: status.category === 'completed' ? Math.floor((Math.floor(Math.random() * 14400) + 1800) / 60) : 0,
      };

      serviceCallsData.push(serviceCall);
    }

    // Create service calls
    let createdCount = 0;
    for (const serviceCallData of serviceCallsData) {
      try {
        const [serviceCall, created] = await models.ServiceCall.findOrCreate({
          where: {
            call_number: serviceCallData.call_number,
            tenant_id: tenant.id
          },
          defaults: serviceCallData
        });
        
        if (created) {
          createdCount++;
        }
      } catch (error) {
        logger.warn(`⚠️ Failed to create service call ${serviceCallData.call_number}:`, error.message);
      }
    }

    logger.info('✅ Service calls seeded successfully');
    logger.info(`📊 Created ${createdCount} sample service calls for tenant: ${tenant.name}`);
    
    // Log distribution
    const freeCount = serviceCallsData.filter(sc => sc.call_billing_type === 'free_call').length;
    const amcCount = serviceCallsData.filter(sc => sc.call_billing_type === 'amc_call').length;
    const paidCount = serviceCallsData.filter(sc => sc.call_billing_type === 'per_call').length;
    
    logger.info(`📈 Distribution: ${freeCount} Free, ${amcCount} AMC, ${paidCount} Per calls`);

  } catch (error) {
    logger.error('❌ Error seeding service calls:', error);
    throw error;
  }
};

export default seedServiceCalls;
