import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create lead_statuses table
  await queryInterface.createTable('lead_statuses', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('new', 'active', 'interested', 'not_interested', 'converted', 'lost'),
      allowNull: false,
      defaultValue: 'new',
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#6c757d',
    },
    is_final: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    auto_follow_up_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('lead_statuses', ['code'], { unique: true });
  await queryInterface.addIndex('lead_statuses', ['category']);
  await queryInterface.addIndex('lead_statuses', ['is_active']);
  await queryInterface.addIndex('lead_statuses', ['sort_order']);

  console.log('✅ Created lead_statuses table with indexes');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('lead_statuses');
  console.log('✅ Dropped lead_statuses table');
};
