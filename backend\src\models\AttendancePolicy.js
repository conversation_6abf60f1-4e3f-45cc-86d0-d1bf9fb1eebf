import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const AttendancePolicy = sequelize.define('AttendancePolicy', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
      comment: 'Policy name',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Policy description',
    },
    shift_start_time: {
      type: DataTypes.TIME,
      allowNull: false,
      defaultValue: '09:00:00',
      comment: 'Standard shift start time',
    },
    shift_end_time: {
      type: DataTypes.TIME,
      allowNull: false,
      defaultValue: '18:00:00',
      comment: 'Standard shift end time',
    },
    grace_period_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 15,
      comment: 'Grace period for late arrival in minutes',
    },
    minimum_hours_full_day: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 8.0,
      comment: 'Minimum hours required for full day',
    },
    minimum_hours_half_day: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 4.0,
      comment: 'Minimum hours required for half day',
    },
    overtime_threshold_hours: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 8.0,
      comment: 'Hours after which overtime calculation starts',
    },
    weekly_off_days: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: ['sunday'],
      comment: 'Array of weekly off days',
    },
    allow_early_check_in: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Allow check-in before shift start time',
    },
    allow_late_check_out: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Allow check-out after shift end time',
    },
    require_location_verification: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Require GPS location verification for check-in/out',
    },
    allowed_locations: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Array of allowed GPS locations with radius',
    },
    auto_check_out_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Automatically check out employees at shift end time',
    },
    break_time_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 60,
      comment: 'Standard break time in minutes',
    },
    late_arrival_deduction_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Minutes to deduct for late arrival',
    },
    early_departure_deduction_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Minutes to deduct for early departure',
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is the default policy for new employees',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this policy is active',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'attendance_policies',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['is_default'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['tenant_id', 'name'],
        unique: true,
        name: 'unique_tenant_policy_name',
      },
    ],
  });

  // Instance methods
  AttendancePolicy.prototype.isWithinAllowedLocation = function(latitude, longitude, radiusMeters = 100) {
    if (!this.require_location_verification || !this.allowed_locations) {
      return true;
    }

    for (const location of this.allowed_locations) {
      const distance = this.calculateDistance(
        latitude, longitude,
        location.latitude, location.longitude
      );
      
      if (distance <= (location.radius || radiusMeters)) {
        return true;
      }
    }
    
    return false;
  };

  AttendancePolicy.prototype.calculateDistance = function(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  };

  AttendancePolicy.prototype.isLateArrival = function(checkInTime) {
    const shiftStart = new Date(`1970-01-01T${this.shift_start_time}`);
    const checkIn = new Date(`1970-01-01T${checkInTime}`);
    const graceEnd = new Date(shiftStart.getTime() + this.grace_period_minutes * 60000);
    
    return checkIn > graceEnd;
  };

  AttendancePolicy.prototype.isEarlyDeparture = function(checkOutTime) {
    const shiftEnd = new Date(`1970-01-01T${this.shift_end_time}`);
    const checkOut = new Date(`1970-01-01T${checkOutTime}`);
    
    return checkOut < shiftEnd;
  };

  // Class methods
  AttendancePolicy.getDefaultPolicy = function() {
    return {
      name: 'Standard Policy',
      description: 'Default attendance policy for all employees',
      shift_start_time: '09:00:00',
      shift_end_time: '18:00:00',
      grace_period_minutes: 15,
      minimum_hours_full_day: 8.0,
      minimum_hours_half_day: 4.0,
      overtime_threshold_hours: 8.0,
      weekly_off_days: ['sunday'],
      allow_early_check_in: true,
      allow_late_check_out: true,
      require_location_verification: false,
      auto_check_out_enabled: false,
      break_time_minutes: 60,
      late_arrival_deduction_minutes: 0,
      early_departure_deduction_minutes: 0,
      is_default: true,
      is_active: true,
    };
  };

  // Associations
  AttendancePolicy.associate = function(models) {
    AttendancePolicy.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    AttendancePolicy.hasMany(models.Executive, {
      foreignKey: 'attendance_policy_id',
      as: 'employees',
    });
  };

  return AttendancePolicy;
}
