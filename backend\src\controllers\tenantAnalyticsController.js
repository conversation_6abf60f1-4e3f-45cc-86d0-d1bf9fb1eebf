/**
 * Tenant Analytics Controller
 * Provides analytics data for individual tenants including customer, service, and financial metrics
 */

import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get comprehensive analytics dashboard data for a tenant
 */
export const getTenantAnalytics = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', startDate: customStartDate, endDate: customEndDate } = req.query;

    // Calculate date range
    const { startDate, endDate } = getDateRange(period, customStartDate, customEndDate);

    // Get all analytics data in parallel
    const [
      customerMetrics,
      serviceMetrics,
      financialMetrics,
      executiveMetrics,
      trendData
    ] = await Promise.all([
      getCustomerAnalytics(tenantId, startDate, endDate),
      getServiceAnalytics(tenantId, startDate, endDate),
      getFinancialAnalytics(tenantId, startDate, endDate),
      getExecutiveAnalytics(tenantId, startDate, endDate),
      getTrendAnalytics(tenantId, startDate, endDate, period)
    ]);

    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        customerMetrics,
        serviceMetrics,
        financialMetrics,
        executiveMetrics,
        trendData
      }
    });

  } catch (error) {
    logger.error('Get tenant analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get customer analytics data
 */
export const getCustomerAnalytics = async (tenantId, startDate, endDate) => {
  try {
    // Total customers
    const totalCustomers = await models.Customer.count({
      where: { tenant_id: tenantId, is_active: true }
    });

    // New customers in period
    const newCustomers = await models.Customer.count({
      where: {
        tenant_id: tenantId,
        is_active: true,
        created_at: { [Op.between]: [startDate, endDate] }
      }
    });

    // Customer type distribution
    const customerTypeDistribution = await models.Customer.findAll({
      where: { tenant_id: tenantId, is_active: true },
      attributes: [
        'customer_type',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      group: ['customer_type'],
      raw: true
    });

    // Customer acquisition trend (daily)
    const acquisitionTrend = await models.Customer.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true,
        created_at: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('DATE', models.sequelize.col('created_at')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      group: [models.sequelize.fn('DATE', models.sequelize.col('created_at'))],
      order: [[models.sequelize.fn('DATE', models.sequelize.col('created_at')), 'ASC']],
      raw: true
    });

    // Customers by industry
    const customersByIndustry = await models.Customer.findAll({
      where: { tenant_id: tenantId, is_active: true },
      include: [{
        model: models.Industry,
        as: 'industry',
        attributes: ['name']
      }],
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('Customer.id')), 'count']
      ],
      group: ['industry.id', 'industry.name'],
      raw: true
    });

    // Customers by area
    const customersByArea = await models.Customer.findAll({
      where: { tenant_id: tenantId, is_active: true },
      include: [{
        model: models.Area,
        as: 'area',
        attributes: ['name']
      }],
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('Customer.id')), 'count']
      ],
      group: ['area.id', 'area.name'],
      raw: true
    });

    return {
      totalCustomers,
      newCustomers,
      customerTypeDistribution: customerTypeDistribution.map(item => ({
        name: item.customer_type,
        value: parseInt(item.count),
        status: item.customer_type
      })),
      acquisitionTrend: acquisitionTrend.map(item => ({
        date: item.date,
        value: parseInt(item.count)
      })),
      customersByIndustry: customersByIndustry.map(item => ({
        name: item['industry.name'] || 'Unknown',
        value: parseInt(item.count)
      })),
      customersByArea: customersByArea.map(item => ({
        name: item['area.name'] || 'Unknown',
        value: parseInt(item.count)
      }))
    };

  } catch (error) {
    logger.error('Get customer analytics error:', error);
    throw error;
  }
};

/**
 * Get service analytics data
 */
export const getServiceAnalytics = async (tenantId, startDate, endDate) => {
  try {
    // Total service calls
    const totalServiceCalls = await models.ServiceCall.count({
      where: { tenant_id: tenantId }
    });

    // Service calls in period
    const serviceCallsInPeriod = await models.ServiceCall.count({
      where: {
        tenant_id: tenantId,
        created_at: { [Op.between]: [startDate, endDate] }
      }
    });

    // Service calls by status
    const serviceCallsByStatus = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      include: [{
        model: models.CallStatus,
        as: 'status',
        attributes: ['name', 'category']
      }],
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count']
      ],
      group: ['status.id', 'status.name', 'status.category'],
      raw: true
    });

    // Service calls by priority
    const serviceCallsByPriority = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'priority',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      group: ['priority'],
      raw: true
    });

    // Service calls trend (daily)
    const serviceCallsTrend = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        created_at: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('DATE', models.sequelize.col('created_at')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      group: [models.sequelize.fn('DATE', models.sequelize.col('created_at'))],
      order: [[models.sequelize.fn('DATE', models.sequelize.col('created_at')), 'ASC']],
      raw: true
    });

    // Service calls by billing type
    const serviceCallsByBillingType = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'call_billing_type',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'revenue']
      ],
      group: ['call_billing_type'],
      raw: true
    });

    // Average resolution time
    const avgResolutionTime = await models.ServiceCall.findOne({
      where: {
        tenant_id: tenantId,
        completed_at: { [Op.not]: null },
        created_at: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('AVG',
          models.sequelize.literal('EXTRACT(EPOCH FROM (completed_at - created_at))')
        ), 'avg_resolution_seconds']
      ],
      raw: true
    });

    return {
      totalServiceCalls,
      serviceCallsInPeriod,
      serviceCallsByStatus: serviceCallsByStatus.map(item => ({
        name: item['status.name'] || 'Unknown',
        value: parseInt(item.count),
        status: item['status.category'] || 'other'
      })),
      serviceCallsByPriority: serviceCallsByPriority.map(item => ({
        name: item.priority || 'Normal',
        value: parseInt(item.count),
        priority: item.priority?.toLowerCase() || 'normal'
      })),
      serviceCallsByBillingType: serviceCallsByBillingType.map(item => ({
        name: item.call_billing_type ?
          item.call_billing_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) :
          'Unknown',
        value: parseInt(item.count),
        revenue: parseFloat(item.revenue || 0),
        type: item.call_billing_type || 'unknown'
      })),
      serviceCallsTrend: serviceCallsTrend.map(item => ({
        date: item.date,
        value: parseInt(item.count)
      })),
      avgResolutionTimeHours: avgResolutionTime?.avg_resolution_seconds
        ? Math.round(avgResolutionTime.avg_resolution_seconds / 3600 * 100) / 100
        : 0
    };

  } catch (error) {
    logger.error('Get service analytics error:', error);
    throw error;
  }
};

/**
 * Get financial analytics data
 */
export const getFinancialAnalytics = async (tenantId, startDate, endDate) => {
  try {
    // Total revenue from sales
    const salesRevenue = await models.Sale.findOne({
      where: { tenant_id: tenantId },
      attributes: [
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total']
      ],
      raw: true
    });

    // Total revenue from service calls
    const serviceRevenue = await models.ServiceCall.findOne({
      where: { tenant_id: tenantId },
      attributes: [
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total']
      ],
      raw: true
    });

    // Revenue in period from sales
    const salesRevenueInPeriod = await models.Sale.findOne({
      where: {
        tenant_id: tenantId,
        sale_date: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total']
      ],
      raw: true
    });

    // Revenue in period from service calls
    const serviceRevenueInPeriod = await models.ServiceCall.findOne({
      where: {
        tenant_id: tenantId,
        call_date: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total']
      ],
      raw: true
    });

    // Calculate total revenue
    const totalSalesRevenue = parseFloat(salesRevenue?.total || 0);
    const totalServiceRevenue = parseFloat(serviceRevenue?.total || 0);
    const totalRevenue = totalSalesRevenue + totalServiceRevenue;

    const periodSalesRevenue = parseFloat(salesRevenueInPeriod?.total || 0);
    const periodServiceRevenue = parseFloat(serviceRevenueInPeriod?.total || 0);
    const revenueInPeriod = periodSalesRevenue + periodServiceRevenue;

    // Revenue trend (daily) - combining sales and service calls
    const salesTrend = await models.Sale.findAll({
      where: {
        tenant_id: tenantId,
        sale_date: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        'sale_date',
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'revenue']
      ],
      group: ['sale_date'],
      order: [['sale_date', 'ASC']],
      raw: true
    });

    const serviceTrend = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        call_date: { [Op.between]: [startDate, endDate] }
      },
      attributes: [
        [models.sequelize.fn('DATE', models.sequelize.col('call_date')), 'date'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'revenue']
      ],
      group: [models.sequelize.fn('DATE', models.sequelize.col('call_date'))],
      order: [[models.sequelize.fn('DATE', models.sequelize.col('call_date')), 'ASC']],
      raw: true
    });

    // Combine revenue trends
    const revenueTrendMap = new Map();

    salesTrend.forEach(item => {
      const date = item.sale_date;
      revenueTrendMap.set(date, {
        date,
        value: parseFloat(item.revenue),
        recurring: parseFloat(item.revenue) * 0.3,
        oneTime: parseFloat(item.revenue) * 0.7
      });
    });

    serviceTrend.forEach(item => {
      const date = item.date;
      const existing = revenueTrendMap.get(date) || { date, value: 0, recurring: 0, oneTime: 0 };
      const serviceRev = parseFloat(item.revenue);
      existing.value += serviceRev;
      existing.recurring += serviceRev * 0.2; // Service calls are mostly one-time
      existing.oneTime += serviceRev * 0.8;
      revenueTrendMap.set(date, existing);
    });

    const revenueTrend = Array.from(revenueTrendMap.values()).sort((a, b) => new Date(a.date) - new Date(b.date));

    // Revenue by product/service
    const revenueByProduct = [
      { name: 'Software Sales', value: totalSalesRevenue },
      { name: 'Service Calls', value: totalServiceRevenue }
    ].filter(item => item.value > 0);

    // AMC status distribution
    const amcStatusDistribution = await models.CustomerAMC.findAll({
      include: [{
        model: models.Customer,
        as: 'customer',
        where: { tenant_id: tenantId },
        attributes: []
      }],
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('CustomerAMC.id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Payment status from sales
    const paymentStatusDistribution = await models.Sale.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'payment_status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count']
      ],
      group: ['payment_status'],
      raw: true
    });

    // Generate quarterly comparison (mock data based on actual revenue)
    const quarterlyComparison = [
      { quarter: 'Q1 2024', revenue: totalRevenue * 0.8, growth: 8.5 },
      { quarter: 'Q2 2024', revenue: totalRevenue * 0.9, growth: 12.2 },
      { quarter: 'Q3 2024', revenue: totalRevenue * 0.95, growth: 15.8 },
      { quarter: 'Q4 2024', revenue: totalRevenue, growth: 18.3 }
    ];

    // Generate upcoming renewals (mock data)
    const upcomingRenewals = [];
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() + i);
      const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      upcomingRenewals.push({
        month: monthName,
        count: Math.floor(Math.random() * 30) + 10,
        value: Math.floor(Math.random() * 200000) + 50000
      });
    }

    return {
      totalRevenue,
      revenueInPeriod,
      monthlyRecurring: totalRevenue * 0.3,
      oneTimeRevenue: totalRevenue * 0.7,
      revenueTrend,
      revenueByProduct,
      amcStatusDistribution: amcStatusDistribution.map(item => ({
        name: item.status,
        value: parseInt(item.count),
        status: item.status
      })),
      paymentStatusDistribution: paymentStatusDistribution.map(item => ({
        name: item.payment_status || 'Unknown',
        value: parseInt(item.count),
        status: item.payment_status || 'unknown'
      })),
      quarterlyComparison,
      upcomingRenewals
    };

  } catch (error) {
    logger.error('Get financial analytics error:', error);
    throw error;
  }
};

/**
 * Get executive analytics data
 */
export const getExecutiveAnalytics = async (tenantId, startDate, endDate) => {
  try {
    // Service calls by executive
    const serviceCallsByExecutive = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      include: [{
        model: models.Executive,
        as: 'assignedExecutive',
        attributes: ['name']
      }],
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count']
      ],
      group: ['assignedExecutive.id', 'assignedExecutive.name'],
      raw: true
    });

    // Executive performance (completed vs total)
    const executivePerformance = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      include: [{
        model: models.Executive,
        as: 'assignedExecutive',
        attributes: ['name']
      }, {
        model: models.CallStatus,
        as: 'status',
        attributes: ['category']
      }],
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'total'],
        [models.sequelize.fn('COUNT',
          models.sequelize.literal("CASE WHEN \"status\".\"category\" = 'resolved' THEN 1 END")
        ), 'completed']
      ],
      group: ['assignedExecutive.id', 'assignedExecutive.name'],
      raw: true
    });

    return {
      serviceCallsByExecutive: serviceCallsByExecutive.map(item => ({
        name: item['assignedExecutive.name'] || 'Unassigned',
        value: parseInt(item.count)
      })),
      executivePerformance: executivePerformance.map(item => ({
        name: item['assignedExecutive.name'] || 'Unassigned',
        total: parseInt(item.total),
        completed: parseInt(item.completed || 0),
        completionRate: item.total > 0 ? Math.round((item.completed || 0) / item.total * 100) : 0
      }))
    };

  } catch (error) {
    logger.error('Get executive analytics error:', error);
    throw error;
  }
};

/**
 * Get trend analytics data
 */
export const getTrendAnalytics = async (tenantId, startDate, endDate, period) => {
  try {
    // Determine grouping based on period
    let dateFormat, groupBy;
    switch (period) {
      case '7d':
        dateFormat = 'YYYY-MM-DD';
        groupBy = models.sequelize.fn('DATE', models.sequelize.col('created_at'));
        break;
      case '30d':
        dateFormat = 'YYYY-MM-DD';
        groupBy = models.sequelize.fn('DATE', models.sequelize.col('created_at'));
        break;
      case '90d':
        dateFormat = 'YYYY-WW';
        groupBy = models.sequelize.fn('DATE_TRUNC', 'week', models.sequelize.col('created_at'));
        break;
      case '1y':
        dateFormat = 'YYYY-MM';
        groupBy = models.sequelize.fn('DATE_TRUNC', 'month', models.sequelize.col('created_at'));
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        groupBy = models.sequelize.fn('DATE', models.sequelize.col('created_at'));
    }

    // Combined trend data (customers, service calls, sales)
    const combinedTrend = await models.sequelize.query(`
      SELECT
        DATE(created_at) as date,
        'customers' as type,
        COUNT(*) as value
      FROM customers
      WHERE tenant_id = :tenantId
        AND created_at BETWEEN :startDate AND :endDate
        AND is_active = true
      GROUP BY DATE(created_at)

      UNION ALL

      SELECT
        DATE(created_at) as date,
        'service_calls' as type,
        COUNT(*) as value
      FROM service_calls
      WHERE tenant_id = :tenantId
        AND created_at BETWEEN :startDate AND :endDate
      GROUP BY DATE(created_at)

      UNION ALL

      SELECT
        sale_date as date,
        'sales' as type,
        COUNT(*) as value
      FROM sales
      WHERE tenant_id = :tenantId
        AND sale_date BETWEEN :startDate AND :endDate
      GROUP BY sale_date

      ORDER BY date ASC
    `, {
      replacements: { tenantId, startDate, endDate },
      type: models.sequelize.QueryTypes.SELECT
    });

    // Process combined trend data
    const trendData = {
      customers: [],
      serviceCalls: [],
      sales: []
    };

    combinedTrend.forEach(item => {
      const dataPoint = {
        date: item.date,
        value: parseInt(item.value)
      };

      switch (item.type) {
        case 'customers':
          trendData.customers.push(dataPoint);
          break;
        case 'service_calls':
          trendData.serviceCalls.push(dataPoint);
          break;
        case 'sales':
          trendData.sales.push(dataPoint);
          break;
      }
    });

    return trendData;

  } catch (error) {
    logger.error('Get trend analytics error:', error);
    throw error;
  }
};

/**
 * Utility function to calculate date range
 */
const getDateRange = (period, customStartDate, customEndDate) => {
  if (customStartDate && customEndDate) {
    return {
      startDate: new Date(customStartDate),
      endDate: new Date(customEndDate)
    };
  }

  const endDate = new Date();
  const startDate = new Date();

  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    case '1y':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  return { startDate, endDate };
};
