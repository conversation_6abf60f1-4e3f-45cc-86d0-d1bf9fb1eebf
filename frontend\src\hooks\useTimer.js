import { useState, useEffect, useRef } from 'react';
import { apiService } from '../services/api';
import socketService from '../services/socketService';

/**
 * Custom hook for managing service call timer functionality
 * Uses WebSocket for real-time updates instead of HTTP polling
 * All timer logic is handled by the backend - frontend only displays the data
 */
export const useTimer = (serviceId) => {
  const [timerData, setTimerData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const intervalRef = useRef(null);
  const fallbackIntervalRef = useRef(null);

  // Fetch timer status from backend
  const fetchTimerStatus = async () => {
    if (!serviceId) return;

    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${serviceId}/timer-status`);

      if (response.data?.success) {
        setTimerData(response.data.data);
        setError(null);
      } else {
        setError('Failed to fetch timer status');
      }
    } catch (err) {
      console.error('Error fetching timer status:', err);
      setError(err.message || 'Failed to fetch timer status');
    } finally {
      setLoading(false);
    }
  };

  // WebSocket connection and timer updates
  useEffect(() => {
    if (!serviceId) return;

    // Ensure WebSocket is connected (but don't force new connection)
    if (!socketService.isSocketConnected()) {
      socketService.connect();
    }

    // Initial fetch
    fetchTimerStatus();

    // Subscribe to timer updates via WebSocket
    const handleTimerUpdate = (data) => {
      console.log('📡 Received timer update:', data);
      setTimerData(data);
      setError(null);
    };

    socketService.subscribeToTimer(serviceId, handleTimerUpdate);

    // Fallback polling for when WebSocket is not available
    const startFallbackPolling = () => {
      if (fallbackIntervalRef.current) {
        clearInterval(fallbackIntervalRef.current);
      }

      // Only start fallback if WebSocket is not connected
      if (!socketService.isSocketConnected()) {
        console.log('⚠️ WebSocket not connected, starting fallback polling');
        fallbackIntervalRef.current = setInterval(() => {
          if (!socketService.isSocketConnected()) {
            fetchTimerStatus();
          }
        }, 5000); // Poll every 5 seconds as fallback
      }
    };

    // Start fallback polling after a short delay
    const fallbackTimeout = setTimeout(startFallbackPolling, 2000);

    // Cleanup on unmount
    return () => {
      clearTimeout(fallbackTimeout);
      socketService.unsubscribeFromTimer(serviceId);

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (fallbackIntervalRef.current) {
        clearInterval(fallbackIntervalRef.current);
      }
    };
  }, [serviceId]);

  // Optimized polling for running timers - much less aggressive
  useEffect(() => {
    if (!serviceId || !timerData?.timer?.is_running) return;

    // For running timers, add a backup polling every 5 seconds (reduced from 1 second)
    // This ensures the timer display updates even if WebSocket has delays
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      // Only fetch if WebSocket is not providing updates
      const lastUpdate = timerData?.timestamp ? new Date(timerData.timestamp) : null;
      const now = new Date();
      const timeSinceLastUpdate = lastUpdate ? (now - lastUpdate) / 1000 : 999;

      // If no WebSocket update in last 10 seconds, fetch via HTTP (increased threshold)
      if (timeSinceLastUpdate > 10) {
        console.log('⚠️ No recent WebSocket update, fetching via HTTP');
        fetchTimerStatus();
      }
    }, 5000); // Check every 5 seconds for running timers (reduced frequency)

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [serviceId, timerData?.timer?.is_running, timerData?.timestamp]);

  // Manual refresh function
  const refreshTimer = () => {
    fetchTimerStatus();
  };

  // Helper functions for timer display
  const getDisplayTime = () => {
    if (!timerData?.timer) return 0;
    return timerData.timer.current_accumulated_seconds || 0;
  };

  const getFormattedTime = () => {
    if (!timerData?.timer) return '00:00:00';
    return timerData.timer.current_accumulated_formatted || '00:00:00';
  };

  const isTimerRunning = () => {
    return timerData?.timer?.is_running || false;
  };

  const isTimerPaused = () => {
    return timerData?.timer?.is_paused || false;
  };

  const getTimerStatus = () => {
    if (isTimerRunning()) return 'running';
    if (isTimerPaused()) return 'paused';
    return 'stopped';
  };

  const getStatusInfo = () => {
    return timerData?.status || null;
  };

  return {
    // Timer data
    timerData,
    loading,
    error,

    // Timer state
    isTimerRunning: isTimerRunning(),
    isTimerPaused: isTimerPaused(),
    timerStatus: getTimerStatus(),

    // Time values
    displayTime: getDisplayTime(),
    formattedTime: getFormattedTime(),

    // Status info
    statusInfo: getStatusInfo(),

    // Actions
    refreshTimer,

    // Raw data for advanced usage
    timeTrackingSummary: timerData?.time_tracking_summary,
    timestamp: timerData?.timestamp
  };
};

export default useTimer;
