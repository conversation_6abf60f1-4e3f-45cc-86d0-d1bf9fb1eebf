import { DataTypes } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';

export const up = async (queryInterface) => {
  console.log('🔄 Populating call statuses with proper data...');

  // Define the default statuses we want to ensure exist
  const defaultStatuses = [
    {
      id: uuidv4(),
      name: 'Pending',
      code: 'PENDING',
      description: 'Call is pending and waiting to be processed',
      category: 'open',
      color: '#ffc107',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Onsite',
      code: 'ONSITE',
      description: 'Onsite visit required or in progress',
      category: 'in_progress',
      color: '#17a2b8',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 2,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Cancelled',
      code: 'CANCELLED',
      description: 'Call has been cancelled',
      category: 'cancelled',
      color: '#dc3545',
      is_final: true,
      requires_approval: true,
      auto_close_after_days: null,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 3,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'No Issue',
      code: 'NO_ISSUE',
      description: 'No issue found or false alarm',
      category: 'resolved',
      color: '#6c757d',
      is_final: true,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 4,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Customization',
      code: 'CUSTOMIZATION',
      description: 'Customization work required',
      category: 'in_progress',
      color: '#e83e8c',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 5,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Completed',
      code: 'COMPLETED',
      description: 'Call has been completed successfully',
      category: 'closed',
      color: '#28a745',
      is_final: true,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 6,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Call Not Atten',
      code: 'CALL_NOT_ATTEN',
      description: 'Call not attended by customer',
      category: 'open',
      color: '#fd7e14',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: 3,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 7,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Whats Up',
      code: 'WHATS_UP',
      description: 'WhatsApp communication in progress',
      category: 'in_progress',
      color: '#25d366',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 8,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Urgent',
      code: 'URGENT',
      description: 'Urgent priority call requiring immediate attention',
      category: 'open',
      color: '#dc3545',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 9,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'On Process',
      code: 'ON_PROCESS',
      description: 'Call is currently being processed',
      category: 'in_progress',
      color: '#6f42c1',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: null,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 10,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Follow Up Programer',
      code: 'FOLLOW_UP_PROGRAMMER',
      description: 'Follow up required with programmer',
      category: 'in_progress',
      color: '#20c997',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: 2,
      is_billable: true,
      is_default: true,
      is_active: true,
      sort_order: 11,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: uuidv4(),
      name: 'Follow Up Customer',
      code: 'FOLLOW_UP_CUSTOMER',
      description: 'Follow up required with customer',
      category: 'in_progress',
      color: '#fd7e14',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: 3,
      is_billable: false,
      is_default: true,
      is_active: true,
      sort_order: 12,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  // Insert or update default statuses safely
  for (const status of defaultStatuses) {
    try {
      // Check if status with this code already exists
      const existingStatus = await queryInterface.sequelize.query(
        'SELECT id FROM call_statuses WHERE code = :code',
        {
          replacements: { code: status.code },
          type: queryInterface.sequelize.QueryTypes.SELECT
        }
      );

      if (existingStatus.length > 0) {
        // Update existing status
        console.log(`Updating existing status: ${status.code}`);
        await queryInterface.sequelize.query(
          `UPDATE call_statuses SET
            name = :name,
            description = :description,
            category = :category,
            color = :color,
            is_final = :is_final,
            requires_approval = :requires_approval,
            auto_close_after_days = :auto_close_after_days,
            is_billable = :is_billable,
            is_default = :is_default,
            is_active = :is_active,
            sort_order = :sort_order,
            updated_at = :updated_at
          WHERE code = :code`,
          {
            replacements: { ...status },
            type: queryInterface.sequelize.QueryTypes.UPDATE
          }
        );
      } else {
        // Insert new status
        console.log(`Inserting new status: ${status.code}`);
        await queryInterface.bulkInsert('call_statuses', [status]);
      }
    } catch (error) {
      console.error(`Error processing status ${status.code}:`, error.message);
      // Continue with next status instead of failing completely
    }
  }

  console.log('✅ Default call statuses populated successfully');
};

export const down = async (queryInterface) => {
  console.log('🔄 Removing default call statuses...');
  
  // Only remove default statuses that are not referenced by service calls
  try {
    await queryInterface.sequelize.query(`
      DELETE FROM call_statuses 
      WHERE is_default = true 
      AND id NOT IN (
        SELECT DISTINCT status_id 
        FROM service_calls 
        WHERE status_id IS NOT NULL
      )
    `);
    console.log('✅ Default call statuses removed');
  } catch (error) {
    console.error('Error removing default call statuses:', error.message);
  }
};
