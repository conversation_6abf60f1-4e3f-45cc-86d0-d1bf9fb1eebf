import React from 'react';
import { Outlet } from 'react-router-dom';
import { Container } from '../ui';
import preminfoLogo from '../../assets/preminfotechlogo.svg';

const AuthLayout = () => {
  return (
    <div className="min-h-screen flex">
      {/* Left Side - Theme Background with Welcome Message */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden header-gradient">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-48 h-48 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white/15 rounded-full blur-xl animate-pulse delay-500"></div>
          <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-white/20 rounded-full blur-lg animate-pulse delay-700"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center items-start p-16 text-white">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center mb-6">
              <img
                src={preminfoLogo}
                alt="Preminfo Tech Logo"
                className="h-12 object-contain"
              />
            </div>
            <h1 className="text-5xl font-bold mb-4 leading-tight text-white">
              WELCOME<br />
              <span className="text-white/80">AGAIN</span>
            </h1>
            <p className="text-white/90 text-xl leading-relaxed max-w-md">
              Manage your Tally business with our comprehensive CRM solution.
              Track customers, services, and grow your revenue.
            </p>
          </div>

          {/* Decorative Elements */}
          <div className="absolute bottom-16 left-16">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-white/40 rounded-full"></div>
              <div className="w-3 h-3 bg-white/60 rounded-full"></div>
              <div className="w-3 h-3 bg-white/80 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <Container className="max-w-md w-full">
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            {/* Logo Section for Mobile */}
            <div className="text-center mb-8 lg:hidden">
              <div className="inline-flex items-center justify-center mb-4">
                <img
                  src={preminfoLogo}
                  alt="Preminfo Tech Logo"
                  className="h-10 object-contain"
                />
              </div>
              <p className="text-gray-600 text-lg">CRM for Tally Resellers</p>
              <div className="w-16 h-1 rounded-full mx-auto mt-3" style={{ backgroundColor: 'var(--primary-color)' }}></div>
            </div>

            <Outlet />
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-gray-500 text-sm">
              © 2024 Preminfo. All rights reserved.
            </p>
          </div>
        </Container>
      </div>
    </div>
  );
};

export default AuthLayout;
