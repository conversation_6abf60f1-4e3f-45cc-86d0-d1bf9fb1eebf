import { Op } from 'sequelize';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Get attendance analytics
 */
export const getAttendanceAnalytics = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      start_date,
      end_date,
      employee_id,
      department,
      analytics_type = 'overview'
    } = req.query;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    const whereClause = { tenant_id: tenantId };

    if (!canViewAll) {
      whereClause.employee_id = req.user.executive_id;
    } else if (employee_id) {
      whereClause.employee_id = employee_id;
    }

    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date]
      };
    }

    const includeClause = [
      {
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department'],
        where: department ? { department: department } : {}
      }
    ];

    const attendanceRecords = await models.AttendanceRecord.findAll({
      where: whereClause,
      include: includeClause,
      order: [['date', 'ASC']]
    });

    let analytics = {};

    if (analytics_type === 'overview') {
      const totalRecords = attendanceRecords.length;
      const statusCounts = attendanceRecords.reduce((acc, record) => {
        acc[record.status] = (acc[record.status] || 0) + 1;
        return acc;
      }, {});

      const totalHours = attendanceRecords.reduce((sum, record) =>
        sum + parseFloat(record.total_hours || 0), 0);
      const totalOvertimeHours = attendanceRecords.reduce((sum, record) =>
        sum + parseFloat(record.overtime_hours || 0), 0);

      analytics = {
        total_records: totalRecords,
        attendance_percentage: totalRecords > 0 ?
          ((statusCounts.present || 0) + (statusCounts.late || 0) + (statusCounts.work_from_home || 0)) / totalRecords * 100 : 0,
        status_breakdown: statusCounts,
        average_hours_per_day: totalRecords > 0 ? totalHours / totalRecords : 0,
        total_overtime_hours: totalOvertimeHours,
        late_arrivals: statusCounts.late || 0,
        absences: statusCounts.absent || 0,
        work_from_home_days: statusCounts.work_from_home || 0
      };
    } else if (analytics_type === 'trends') {
      // Group by date for trend analysis
      const dailyStats = attendanceRecords.reduce((acc, record) => {
        const date = record.date;
        if (!acc[date]) {
          acc[date] = {
            date: date,
            total: 0,
            present: 0,
            absent: 0,
            late: 0,
            total_hours: 0,
            overtime_hours: 0
          };
        }

        acc[date].total++;
        acc[date].total_hours += parseFloat(record.total_hours || 0);
        acc[date].overtime_hours += parseFloat(record.overtime_hours || 0);

        if (['present', 'late', 'work_from_home'].includes(record.status)) {
          acc[date].present++;
        }
        if (record.status === 'absent') {
          acc[date].absent++;
        }
        if (record.status === 'late') {
          acc[date].late++;
        }

        return acc;
      }, {});

      analytics = {
        daily_trends: Object.values(dailyStats).map(day => ({
          ...day,
          attendance_rate: day.total > 0 ? (day.present / day.total) * 100 : 0,
          average_hours: day.total > 0 ? day.total_hours / day.total : 0
        }))
      };
    } else if (analytics_type === 'department') {
      // Group by department
      const departmentStats = attendanceRecords.reduce((acc, record) => {
        const dept = record.employee.department || 'Unknown';
        if (!acc[dept]) {
          acc[dept] = {
            department: dept,
            total_records: 0,
            present: 0,
            absent: 0,
            late: 0,
            total_hours: 0,
            overtime_hours: 0,
            employees: new Set()
          };
        }

        acc[dept].total_records++;
        acc[dept].total_hours += parseFloat(record.total_hours || 0);
        acc[dept].overtime_hours += parseFloat(record.overtime_hours || 0);
        acc[dept].employees.add(record.employee_id);

        if (['present', 'late', 'work_from_home'].includes(record.status)) {
          acc[dept].present++;
        }
        if (record.status === 'absent') {
          acc[dept].absent++;
        }
        if (record.status === 'late') {
          acc[dept].late++;
        }

        return acc;
      }, {});

      analytics = {
        department_breakdown: Object.values(departmentStats).map(dept => ({
          department: dept.department,
          total_records: dept.total_records,
          employee_count: dept.employees.size,
          attendance_rate: dept.total_records > 0 ? (dept.present / dept.total_records) * 100 : 0,
          average_hours: dept.total_records > 0 ? dept.total_hours / dept.total_records : 0,
          total_overtime: dept.overtime_hours,
          late_percentage: dept.total_records > 0 ? (dept.late / dept.total_records) * 100 : 0
        }))
      };
    }

    res.json({
      success: true,
      data: {
        analytics_type: analytics_type,
        period: { start_date, end_date },
        filters: { employee_id, department },
        ...analytics
      }
    });

  } catch (error) {
    logger.error('Get attendance analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get leave analytics
 */
export const getLeaveAnalytics = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      start_date,
      end_date,
      employee_id,
      department,
      year = new Date().getFullYear()
    } = req.query;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    const whereClause = { tenant_id: tenantId };

    if (!canViewAll) {
      whereClause.employee_id = req.user.executive_id;
    } else if (employee_id) {
      whereClause.employee_id = employee_id;
    }

    if (start_date && end_date) {
      whereClause[Op.or] = [
        {
          start_date: {
            [Op.between]: [start_date, end_date]
          }
        },
        {
          end_date: {
            [Op.between]: [start_date, end_date]
          }
        }
      ];
    }

    const includeClause = [
      {
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department'],
        where: department ? { department: department } : {}
      },
      {
        model: models.LeaveType,
        as: 'leaveType',
        attributes: ['id', 'name', 'code', 'color_code']
      }
    ];

    const leaveRequests = await models.LeaveRequest.findAll({
      where: whereClause,
      include: includeClause
    });

    // Get leave balances for the year
    const balanceWhereClause = {
      tenant_id: tenantId,
      year: year,
      is_active: true
    };

    if (!canViewAll) {
      balanceWhereClause.employee_id = req.user.executive_id;
    } else if (employee_id) {
      balanceWhereClause.employee_id = employee_id;
    }

    const leaveBalances = await models.LeaveBalance.findAll({
      where: balanceWhereClause,
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department'],
          where: department ? { department: department } : {}
        },
        {
          model: models.LeaveType,
          as: 'leaveType',
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    // Calculate analytics
    const totalRequests = leaveRequests.length;
    const statusBreakdown = leaveRequests.reduce((acc, request) => {
      acc[request.status] = (acc[request.status] || 0) + 1;
      return acc;
    }, {});

    const leaveTypeBreakdown = leaveRequests.reduce((acc, request) => {
      const typeName = request.leaveType.name;
      if (!acc[typeName]) {
        acc[typeName] = {
          name: typeName,
          code: request.leaveType.code,
          total_requests: 0,
          total_days: 0,
          approved_requests: 0,
          approved_days: 0
        };
      }

      acc[typeName].total_requests++;
      acc[typeName].total_days += parseFloat(request.total_days);

      if (request.status === 'approved') {
        acc[typeName].approved_requests++;
        acc[typeName].approved_days += parseFloat(request.total_days);
      }

      return acc;
    }, {});

    const utilizationStats = leaveBalances.reduce((acc, balance) => {
      const typeName = balance.leaveType.name;
      if (!acc[typeName]) {
        acc[typeName] = {
          name: typeName,
          total_allocated: 0,
          total_used: 0,
          total_remaining: 0,
          employee_count: 0
        };
      }

      acc[typeName].total_allocated += parseFloat(balance.allocated_days);
      acc[typeName].total_used += parseFloat(balance.used_days);
      acc[typeName].total_remaining += balance.getRemainingDays();
      acc[typeName].employee_count++;

      return acc;
    }, {});

    const analytics = {
      summary: {
        total_requests: totalRequests,
        approved_requests: statusBreakdown.approved || 0,
        pending_requests: statusBreakdown.pending || 0,
        rejected_requests: statusBreakdown.rejected || 0,
        approval_rate: totalRequests > 0 ? ((statusBreakdown.approved || 0) / totalRequests) * 100 : 0
      },
      status_breakdown: statusBreakdown,
      leave_type_breakdown: Object.values(leaveTypeBreakdown),
      utilization_stats: Object.values(utilizationStats).map(stat => ({
        ...stat,
        utilization_percentage: stat.total_allocated > 0 ? (stat.total_used / stat.total_allocated) * 100 : 0,
        average_allocated: stat.employee_count > 0 ? stat.total_allocated / stat.employee_count : 0,
        average_used: stat.employee_count > 0 ? stat.total_used / stat.employee_count : 0
      }))
    };

    res.json({
      success: true,
      data: {
        period: { start_date, end_date, year },
        filters: { employee_id, department },
        ...analytics
      }
    });

  } catch (error) {
    logger.error('Get leave analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get customer reports
 */
export const getCustomerReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
      searchTerm,
      profileStatus,
      joinedDateFrom,
      joinedDateTo,
      page = 1,
      limit = 50,
    } = req.query;

    const tenantId = req.user.tenant.id;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (customerType) whereConditions.customer_type = customerType;
    if (industryId) whereConditions.industry_id = industryId;
    if (areaId) whereConditions.area_id = areaId;
    if (assignedExecutiveId) whereConditions.assigned_executive_id = assignedExecutiveId;

    // Profile status filter (active/inactive)
    if (profileStatus) {
      whereConditions.is_active = profileStatus === 'active';
    }

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.created_at = {};
      if (dateFrom) whereConditions.created_at[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.created_at[Op.lte] = new Date(dateTo);
    }

    // Joined date filter (separate from general date filter)
    if (joinedDateFrom || joinedDateTo) {
      if (!whereConditions.created_at) whereConditions.created_at = {};
      if (joinedDateFrom) whereConditions.created_at[Op.gte] = new Date(joinedDateFrom);
      if (joinedDateTo) whereConditions.created_at[Op.lte] = new Date(joinedDateTo);
    }

    // Search functionality - Enhanced vector search with partial matching
    if (searchTerm) {
      const searchPattern = `%${searchTerm}%`;
      whereConditions[Op.or] = [
        { company_name: { [Op.iLike]: searchPattern } },
        { customer_code: { [Op.iLike]: searchPattern } },
        { display_name: { [Op.iLike]: searchPattern } },
        { contact_person: { [Op.iLike]: searchPattern } },
        { email: { [Op.iLike]: searchPattern } },
        { phone: { [Op.iLike]: searchPattern } },
        { tally_serial_number: { [Op.iLike]: searchPattern } },
        { gst_number: { [Op.iLike]: searchPattern } },
        { city: { [Op.iLike]: searchPattern } },
        { state: { [Op.iLike]: searchPattern } },
        // Search in executive names using proper include syntax
        {
          '$assignedExecutive.first_name$': { [Op.iLike]: searchPattern }
        },
        {
          '$assignedExecutive.last_name$': { [Op.iLike]: searchPattern }
        },
        {
          '$assignedExecutive.employee_code$': { [Op.iLike]: searchPattern }
        }
      ];
    }

    const { count, rows: customers } = await models.Customer.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    // Generate summary statistics from actual database totals (not paginated results)
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const startOfMonth = new Date(currentYear, currentMonth, 1);
    const endOfMonth = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);

    // Calculate summary statistics from database totals (ignoring pagination)
    const summaryWhereConditions = {
      tenant_id: tenantId,
    };

    // Apply the same filters as the main query for consistency
    if (customerType) summaryWhereConditions.customer_type = customerType;
    if (industryId) summaryWhereConditions.industry_id = industryId;
    if (areaId) summaryWhereConditions.area_id = areaId;
    if (assignedExecutiveId) summaryWhereConditions.assigned_executive_id = assignedExecutiveId;
    if (profileStatus) summaryWhereConditions.is_active = profileStatus === 'active';
    if (dateFrom || dateTo) {
      summaryWhereConditions.created_at = {};
      if (dateFrom) summaryWhereConditions.created_at[Op.gte] = new Date(dateFrom);
      if (dateTo) summaryWhereConditions.created_at[Op.lte] = new Date(dateTo);
    }
    if (joinedDateFrom || joinedDateTo) {
      if (!summaryWhereConditions.created_at) summaryWhereConditions.created_at = {};
      if (joinedDateFrom) summaryWhereConditions.created_at[Op.gte] = new Date(joinedDateFrom);
      if (joinedDateTo) summaryWhereConditions.created_at[Op.lte] = new Date(joinedDateTo);
    }
    if (searchTerm) {
      const searchPattern = `%${searchTerm}%`;
      summaryWhereConditions[Op.or] = [
        { company_name: { [Op.iLike]: searchPattern } },
        { customer_code: { [Op.iLike]: searchPattern } },
        { display_name: { [Op.iLike]: searchPattern } },
        { contact_person: { [Op.iLike]: searchPattern } },
        { email: { [Op.iLike]: searchPattern } },
        { phone: { [Op.iLike]: searchPattern } },
        { tally_serial_number: { [Op.iLike]: searchPattern } },
        { gst_number: { [Op.iLike]: searchPattern } },
        { city: { [Op.iLike]: searchPattern } },
        { state: { [Op.iLike]: searchPattern } },
      ];
    }

    // Get actual database totals for summary
    const [
      totalCustomers,
      activeCustomers,
      prospects,
      newThisMonth
    ] = await Promise.all([
      models.Customer.count({ where: summaryWhereConditions }),
      models.Customer.count({ where: { ...summaryWhereConditions, is_active: true } }),
      models.Customer.count({ where: { ...summaryWhereConditions, customer_type: 'prospect' } }),
      models.Customer.count({
        where: {
          ...summaryWhereConditions,
          created_at: {
            [Op.between]: [startOfMonth, endOfMonth]
          }
        }
      })
    ]);

    const summary = {
      totalCustomers, // Use database total, not paginated result length
      activeCustomers,
      prospects,
      newThisMonth,
      byType: customers.reduce((acc, customer) => {
        acc[customer.customer_type] = (acc[customer.customer_type] || 0) + 1;
        return acc;
      }, {}),
      byIndustry: customers.reduce((acc, customer) => {
        const industry = customer.industry?.name || 'Unknown';
        acc[industry] = (acc[industry] || 0) + 1;
        return acc;
      }, {}),
      byArea: customers.reduce((acc, customer) => {
        const area = customer.area?.name || 'Unknown';
        acc[area] = (acc[area] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        customers: customers.map(customer => ({
          id: customer.id,
          companyName: customer.company_name,
          customerCode: customer.customer_code,
          customerType: customer.customer_type,
          profileStatus: customer.is_active ? 'active' : 'inactive',
          location: customer.city || customer.area?.name,
          industry: customer.industry?.name,
          area: customer.area,
          assignedExecutive: customer.assignedExecutive ? {
            ...customer.assignedExecutive.toJSON(),
            name: `${customer.assignedExecutive.first_name || ''} ${customer.assignedExecutive.last_name || ''}`.trim()
          } : null,
          contactPerson: customer.contact_person,
          email: customer.email,
          phone: customer.phone,
          tallySerialNumber: customer.tally_serial_number,
          createdAt: customer.created_at,
        })),
        summary,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalRecords: count,
          limit: parseInt(limit),
          hasNextPage: parseInt(page) < Math.ceil(count / limit),
          hasPrevPage: parseInt(page) > 1,
        },
      },
    });
  } catch (error) {
    logger.error('Get customer reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate customer reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get service call reports
 */
export const getServiceCallReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      statusId,
      priority,
      assignedTo,
      customerId,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (statusId) whereConditions.status_id = statusId;
    if (priority) whereConditions.priority = priority;
    if (assignedTo) whereConditions.assigned_to = assignedTo;
    if (customerId) whereConditions.customer_id = customerId;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.created_at = {};
      if (dateFrom) {
        // Set to start of day in local timezone
        const startDate = new Date(dateFrom + 'T00:00:00');
        whereConditions.created_at[Op.gte] = startDate;
      }
      if (dateTo) {
        // Set to end of day in local timezone
        const endDate = new Date(dateTo + 'T23:59:59');
        whereConditions.created_at[Op.lte] = endDate;
      }
    }

    const serviceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['call_date', 'DESC']],
    });

    // Generate summary statistics
    const summary = {
      totalCalls: serviceCalls.length,
      byStatus: serviceCalls.reduce((acc, call) => {
        const status = call.status?.name || 'Unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {}),
      byPriority: serviceCalls.reduce((acc, call) => {
        acc[call.priority] = (acc[call.priority] || 0) + 1;
        return acc;
      }, {}),
      avgResolutionTime: 0, // This would need to be calculated based on actual resolution times
    };

    res.json({
      success: true,
      data: {
        serviceCalls: serviceCalls.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          customer: call.customer,
          subject: call.subject,
          priority: call.priority,
          status: call.status,
          assignedExecutive: call.assignedExecutive ? {
            ...call.assignedExecutive.toJSON(),
            name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
          } : null,
          callDate: call.call_date,
          createdAt: call.created_at,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get service call reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service call reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get sales reports
 */
export const getSalesReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      salesExecutiveId,
      customerId,
      saleType,
      status,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    if (salesExecutiveId) whereConditions.sales_executive_id = salesExecutiveId;
    if (customerId) whereConditions.customer_id = customerId;
    if (saleType) whereConditions.sale_type = saleType;
    if (status) whereConditions.status = status;

    // Date range filter
    if (dateFrom || dateTo) {
      whereConditions.sale_date = {};
      if (dateFrom) whereConditions.sale_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.sale_date[Op.lte] = new Date(dateTo);
    }

    const sales = await models.Sale.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'salesExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
      ],
      order: [['sale_date', 'DESC']],
    });

    // Generate summary statistics
    const totalRevenue = sales.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0);
    const totalPaid = sales.reduce((sum, sale) => sum + parseFloat(sale.paid_amount), 0);
    const totalOutstanding = sales.reduce((sum, sale) => sum + parseFloat(sale.balance_amount), 0);

    const summary = {
      totalSales: sales.length,
      totalRevenue,
      totalPaid,
      totalOutstanding,
      byType: sales.reduce((acc, sale) => {
        acc[sale.sale_type] = (acc[sale.sale_type] || 0) + 1;
        return acc;
      }, {}),
      byStatus: sales.reduce((acc, sale) => {
        acc[sale.status] = (acc[sale.status] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        sales: sales.map(sale => ({
          id: sale.id,
          saleNumber: sale.sale_number,
          customer: sale.customer,
          salesExecutive: sale.salesExecutive ? {
            ...sale.salesExecutive.toJSON(),
            name: `${sale.salesExecutive.first_name || ''} ${sale.salesExecutive.last_name || ''}`.trim()
          } : null,
          saleType: sale.sale_type,
          saleDate: sale.sale_date,
          totalAmount: parseFloat(sale.total_amount),
          paidAmount: parseFloat(sale.paid_amount),
          balanceAmount: parseFloat(sale.balance_amount),
          status: sale.status,
          paymentStatus: sale.payment_status,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get sales reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate sales reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get AMC reports
 */
export const getAmcReports = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      customerId,
      status,
      renewalDateFrom,
      renewalDateTo,
    } = req.query;

    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      '$customer.tenant_id$': tenantId,
    };

    if (customerId) whereConditions.customer_id = customerId;
    if (status) whereConditions.status = status;

    // Date range filter for start date
    if (dateFrom || dateTo) {
      whereConditions.start_date = {};
      if (dateFrom) whereConditions.start_date[Op.gte] = new Date(dateFrom);
      if (dateTo) whereConditions.start_date[Op.lte] = new Date(dateTo);
    }

    // Date range filter for renewal date
    if (renewalDateFrom || renewalDateTo) {
      whereConditions.renewal_date = {};
      if (renewalDateFrom) whereConditions.renewal_date[Op.gte] = new Date(renewalDateFrom);
      if (renewalDateTo) whereConditions.renewal_date[Op.lte] = new Date(renewalDateTo);
    }

    const amcs = await models.CustomerAMC.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
          where: { tenant_id: tenantId },
        },
      ],
      order: [['start_date', 'DESC']],
    });

    // Generate summary statistics
    const totalValue = amcs.reduce((sum, amc) => sum + parseFloat(amc.contract_value), 0);
    const activeAmcs = amcs.filter(amc => amc.status === 'active').length;
    const expiringAmcs = amcs.filter(amc => {
      const renewalDate = new Date(amc.renewal_date);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      return renewalDate <= thirtyDaysFromNow && amc.status === 'active';
    }).length;

    const summary = {
      totalAmcs: amcs.length,
      activeAmcs,
      expiringAmcs,
      totalValue,
      byStatus: amcs.reduce((acc, amc) => {
        acc[amc.status] = (acc[amc.status] || 0) + 1;
        return acc;
      }, {}),
    };

    res.json({
      success: true,
      data: {
        amcs: amcs.map(amc => ({
          id: amc.id,
          customer: amc.customer,
          contractValue: parseFloat(amc.contract_value),
          startDate: amc.start_date,
          endDate: amc.end_date,
          renewalDate: amc.renewal_date,
          status: amc.status,
          numberOfVisits: amc.number_of_visits,
          visitedCount: amc.visited_count,
        })),
        summary,
      },
    });
  } catch (error) {
    logger.error('Get AMC reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AMC reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};



/**
 * Export reports to Excel
 */
export const exportReports = async (req, res) => {
  try {
    const { reportType, filters, format = 'xlsx' } = req.body;

    // This is a placeholder implementation
    // In a real application, you would use a library like ExcelJS or xlsx
    // to generate actual Excel files

    logger.info('Export report requested:', {
      reportType,
      filters,
      format,
      userId: req.user.id,
      tenantId: req.user.tenant.id,
    });

    res.json({
      success: true,
      message: 'Report export initiated',
      data: {
        downloadUrl: `/api/v1/reports/download/${reportType}-${Date.now()}.${format}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });
  } catch (error) {
    logger.error('Export reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export reports',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Request Report
 * List of all service requests with filters
 */
export const getServiceRequestReport = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      customerId,
      priority,
      statusId,
      assignedTo,
      page = 1,
      limit = 50,
      // New filter parameters
      callBillingType,
      isOverdue,
      statusCategory,
      scheduledDateFrom,
      scheduledDateTo,
    } = req.query;

    const tenantId = req.user.tenant.id;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = { tenant_id: tenantId };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    if (customerId) whereConditions.customer_id = customerId;
    if (priority) whereConditions.priority = priority;
    if (statusId) whereConditions.status_id = statusId;
    if (assignedTo) whereConditions.assigned_to = assignedTo;

    // New filter conditions
    if (callBillingType) {
      if (callBillingType === 'free_call') {
        // Free call filter - match multiple conditions like in analytics
        whereConditions[Op.or] = [
          { call_billing_type: 'free_call' },
          {
            [Op.and]: [
              { is_billable: false },
              { is_under_amc: false }
            ]
          },
          { service_charges: { [Op.or]: [0, null] } }
        ];
      } else {
        whereConditions.call_billing_type = callBillingType;
      }
    }

    // Scheduled date range filter
    if (scheduledDateFrom || scheduledDateTo) {
      whereConditions.scheduled_date = {};
      if (scheduledDateFrom) whereConditions.scheduled_date[Op.gte] = new Date(scheduledDateFrom);
      if (scheduledDateTo) whereConditions.scheduled_date[Op.lte] = new Date(scheduledDateTo);
    }

    // Handle overdue filter - must be done after includes are set up
    if (isOverdue === 'true') {
      whereConditions.scheduled_date = {
        ...whereConditions.scheduled_date,
        [Op.lt]: new Date(), // Scheduled date is in the past
      };
      whereConditions['$status.category$'] = 'open'; // Still open/pending
    }

    // Handle status category filter
    if (statusCategory) {
      whereConditions['$status.category$'] = statusCategory;
    }

    // Fetch service calls with related data
    const { count, rows: serviceCalls } = await models.ServiceCall.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code', 'contact_person'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.TypeOfCall,
          as: 'typeOfCall',
          attributes: ['id', 'name'],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    // Calculate summary statistics
    const summary = {
      total: count,
      byStatus: {},
      byPriority: {},
      avgResolutionTime: 0,
    };

    // Group by status and priority
    serviceCalls.forEach(call => {
      const statusName = call.status?.name || 'Unknown';
      const priority = call.priority || 'medium';

      summary.byStatus[statusName] = (summary.byStatus[statusName] || 0) + 1;
      summary.byPriority[priority] = (summary.byPriority[priority] || 0) + 1;
    });

    // Calculate average resolution time for completed calls
    const completedCalls = serviceCalls.filter(call =>
      call.completed_at && call.created_at
    );

    if (completedCalls.length > 0) {
      const totalResolutionTime = completedCalls.reduce((sum, call) => {
        const resolutionTime = new Date(call.completed_at) - new Date(call.created_at);
        return sum + (resolutionTime / (1000 * 60 * 60)); // Convert to hours
      }, 0);
      summary.avgResolutionTime = (totalResolutionTime / completedCalls.length).toFixed(2);
    }

    res.json({
      success: true,
      data: {
        serviceCalls: serviceCalls.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          customer: call.customer,
          subject: call.subject,
          description: call.description,
          priority: call.priority,
          status: call.status,
          assignedExecutive: call.assignedExecutive ? {
            ...call.assignedExecutive.toJSON(),
            name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
          } : null,
          callType: call.typeOfCall,
          natureOfIssue: call.natureOfIssue,
          callDate: call.call_date,
          scheduledDate: call.scheduled_date,
          startedAt: call.started_at,
          completedAt: call.completed_at,
          estimatedHours: call.estimated_hours,
          actualHours: call.actual_hours,
          customerSatisfaction: call.customer_satisfaction,
          createdAt: call.created_at,
          agingDays: Math.floor((new Date() - new Date(call.created_at)) / (1000 * 60 * 60 * 24)),
        })),
        summary,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Get service request report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service request report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Status Summary Report
 * Count of service tickets by status
 */
export const getServiceStatusSummary = async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = { tenant_id: tenantId };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    // Get status summary with counts
    const statusSummary = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
      ],
      attributes: [
        [models.sequelize.col('status.name'), 'statusName'],
        [models.sequelize.col('status.category'), 'statusCategory'],
        [models.sequelize.col('status.color'), 'statusColor'],
        [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
      ],
      group: ['status.id', 'status.name', 'status.category', 'status.color'],
      raw: true,
    });

    // Get aging analysis for open tickets
    const openTickets = await models.ServiceCall.findAll({
      where: {
        ...whereConditions,
        '$status.category$': { [Op.in]: ['open', 'in_progress'] },
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['category'],
        },
      ],
      attributes: ['id', 'created_at'],
    });

    // Calculate aging buckets
    const agingBuckets = {
      '0-1 days': 0,
      '2-7 days': 0,
      '8-30 days': 0,
      '31+ days': 0,
    };

    openTickets.forEach(ticket => {
      const agingDays = Math.floor((new Date() - new Date(ticket.created_at)) / (1000 * 60 * 60 * 24));

      if (agingDays <= 1) agingBuckets['0-1 days']++;
      else if (agingDays <= 7) agingBuckets['2-7 days']++;
      else if (agingDays <= 30) agingBuckets['8-30 days']++;
      else agingBuckets['31+ days']++;
    });

    // Calculate SLA metrics
    const totalTickets = statusSummary.reduce((sum, status) => sum + parseInt(status.count), 0);
    const completedTickets = statusSummary
      .filter(status => status.statusCategory === 'completed')
      .reduce((sum, status) => sum + parseInt(status.count), 0);

    const slaCompliance = totalTickets > 0 ? ((completedTickets / totalTickets) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        statusSummary: statusSummary.map(status => ({
          statusName: status.statusName,
          statusCategory: status.statusCategory,
          statusColor: status.statusColor,
          count: parseInt(status.count),
          percentage: totalTickets > 0 ? ((parseInt(status.count) / totalTickets) * 100).toFixed(2) : 0,
        })),
        agingAnalysis: agingBuckets,
        slaMetrics: {
          totalTickets,
          completedTickets,
          openTickets: openTickets.length,
          slaCompliance: parseFloat(slaCompliance),
        },
        summary: {
          totalTickets,
          avgTicketsPerDay: dateFrom && dateTo ?
            (totalTickets / Math.ceil((new Date(dateTo) - new Date(dateFrom)) / (1000 * 60 * 60 * 24))).toFixed(2) : 0,
        },
      },
    });
  } catch (error) {
    logger.error('Get service status summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service status summary',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Request Aging Report
 * Shows how long service tickets have remained open
 */
export const getServiceAgingReport = async (req, res) => {
  try {
    const { statusCategory = 'open,in_progress' } = req.query;
    const tenantId = req.user.tenant.id;

    const statusCategories = statusCategory.split(',');

    // Get open/in-progress tickets with aging information
    const openTickets = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        '$status.category$': { [Op.in]: statusCategories },
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name'],
        },
      ],
      order: [['created_at', 'ASC']], // Oldest first
    });

    // Calculate aging and categorize
    const agingData = openTickets.map(ticket => {
      const agingDays = Math.floor((new Date() - new Date(ticket.created_at)) / (1000 * 60 * 60 * 24));
      const agingHours = Math.floor((new Date() - new Date(ticket.created_at)) / (1000 * 60 * 60));

      let agingCategory = 'Normal';
      let riskLevel = 'low';

      if (agingDays > 30) {
        agingCategory = 'Critical';
        riskLevel = 'critical';
      } else if (agingDays > 7) {
        agingCategory = 'High';
        riskLevel = 'high';
      } else if (agingDays > 3) {
        agingCategory = 'Medium';
        riskLevel = 'medium';
      }

      return {
        id: ticket.id,
        callNumber: ticket.call_number,
        customer: ticket.customer,
        subject: ticket.subject,
        priority: ticket.priority,
        status: ticket.status,
        assignedExecutive: ticket.assignedExecutive ? {
          ...ticket.assignedExecutive.toJSON(),
          name: `${ticket.assignedExecutive.first_name || ''} ${ticket.assignedExecutive.last_name || ''}`.trim()
        } : null,
        createdAt: ticket.created_at,
        agingDays,
        agingHours,
        agingCategory,
        riskLevel,
      };
    });

    // Group by aging categories
    const agingBuckets = {
      'Normal (0-3 days)': agingData.filter(t => t.agingDays <= 3).length,
      'Medium (4-7 days)': agingData.filter(t => t.agingDays > 3 && t.agingDays <= 7).length,
      'High (8-30 days)': agingData.filter(t => t.agingDays > 7 && t.agingDays <= 30).length,
      'Critical (31+ days)': agingData.filter(t => t.agingDays > 30).length,
    };

    // Calculate average aging
    const avgAging = agingData.length > 0 ?
      (agingData.reduce((sum, ticket) => sum + ticket.agingDays, 0) / agingData.length).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        tickets: agingData,
        agingBuckets,
        summary: {
          totalOpenTickets: agingData.length,
          avgAgingDays: parseFloat(avgAging),
          oldestTicketDays: agingData.length > 0 ? Math.max(...agingData.map(t => t.agingDays)) : 0,
          criticalTickets: agingData.filter(t => t.riskLevel === 'critical').length,
          highRiskTickets: agingData.filter(t => t.riskLevel === 'high').length,
        },
      },
    });
  } catch (error) {
    logger.error('Get service aging report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service aging report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Technician/Employee Service Performance Report
 * Performance metrics for each technician
 */
export const getTechnicianPerformanceReport = async (req, res) => {
  try {
    const { dateFrom, dateTo, executiveId } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = { tenant_id: tenantId };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    if (executiveId) {
      whereConditions.assigned_to = executiveId;
    }

    // Get all executives
    const executives = await models.Executive.findAll({
      where: { tenant_id: tenantId },
      attributes: ['id', 'first_name', 'last_name', 'employee_code', 'email'],
    });

    // Get service calls for performance analysis
    const serviceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category'],
        },
      ],
      attributes: [
        'id', 'assigned_to', 'created_at', 'started_at', 'completed_at',
        'actual_hours', 'customer_satisfaction', 'priority'
      ],
    });

    // Calculate performance metrics for each executive
    const performanceData = executives.map(executive => {
      const executiveCalls = serviceCalls.filter(call =>
        call.assigned_to === executive.id
      );

      const completedCalls = executiveCalls.filter(call =>
        call.status?.category === 'closed' || call.status?.category === 'resolved'
      );

      const totalCalls = executiveCalls.length;
      const completedCount = completedCalls.length;
      const completionRate = totalCalls > 0 ? ((completedCount / totalCalls) * 100).toFixed(2) : 0;

      // Calculate average resolution time
      const resolutionTimes = completedCalls
        .filter(call => call.completed_at && call.created_at)
        .map(call => {
          const resolutionTime = new Date(call.completed_at) - new Date(call.created_at);
          return resolutionTime / (1000 * 60 * 60); // Convert to hours
        });

      const avgResolutionTime = resolutionTimes.length > 0 ?
        (resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length).toFixed(2) : 0;

      // Calculate average customer satisfaction
      const satisfactionRatings = executiveCalls
        .filter(call => call.customer_satisfaction)
        .map(call => call.customer_satisfaction);

      const avgSatisfaction = satisfactionRatings.length > 0 ?
        (satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length).toFixed(2) : 0;

      // Calculate total billable hours
      const totalBillableHours = executiveCalls
        .filter(call => call.actual_hours)
        .reduce((sum, call) => sum + parseFloat(call.actual_hours), 0);

      // Priority distribution
      const priorityDistribution = {
        low: executiveCalls.filter(call => call.priority === 'low').length,
        medium: executiveCalls.filter(call => call.priority === 'medium').length,
        high: executiveCalls.filter(call => call.priority === 'high').length,
        critical: executiveCalls.filter(call => call.priority === 'critical').length,
      };

      return {
        executive: {
          id: executive.id,
          name: `${executive.first_name || ''} ${executive.last_name || ''}`.trim(),
          employeeCode: executive.employee_code,
          email: executive.email,
        },
        metrics: {
          totalCalls,
          completedCalls: completedCount,
          completionRate: parseFloat(completionRate),
          avgResolutionTimeHours: parseFloat(avgResolutionTime),
          avgCustomerSatisfaction: parseFloat(avgSatisfaction),
          totalBillableHours: totalBillableHours.toFixed(2),
          satisfactionRatingsCount: satisfactionRatings.length,
        },
        priorityDistribution,
        recentCalls: executiveCalls
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 5)
          .map(call => ({
            id: call.id,
            createdAt: call.created_at,
            completedAt: call.completed_at,
            priority: call.priority,
            status: call.status?.name,
            customerSatisfaction: call.customer_satisfaction,
          })),
      };
    });

    // Sort by completion rate (best performers first)
    performanceData.sort((a, b) => b.metrics.completionRate - a.metrics.completionRate);

    // Calculate team averages
    const teamMetrics = {
      avgCompletionRate: performanceData.length > 0 ?
        (performanceData.reduce((sum, exec) => sum + exec.metrics.completionRate, 0) / performanceData.length).toFixed(2) : 0,
      avgResolutionTime: performanceData.length > 0 ?
        (performanceData.reduce((sum, exec) => sum + exec.metrics.avgResolutionTimeHours, 0) / performanceData.length).toFixed(2) : 0,
      avgSatisfaction: performanceData.length > 0 ?
        (performanceData.reduce((sum, exec) => sum + exec.metrics.avgCustomerSatisfaction, 0) / performanceData.length).toFixed(2) : 0,
      totalTeamCalls: performanceData.reduce((sum, exec) => sum + exec.metrics.totalCalls, 0),
    };

    res.json({
      success: true,
      data: {
        performanceData,
        teamMetrics,
        summary: {
          totalExecutives: executives.length,
          activeExecutives: performanceData.filter(exec => exec.metrics.totalCalls > 0).length,
          topPerformer: performanceData.length > 0 ? performanceData[0].executive : null,
        },
      },
    });
  } catch (error) {
    logger.error('Get technician performance report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate technician performance report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Customer Service History Report
 * All service interactions for a specific customer
 */
export const getCustomerServiceHistory = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { dateFrom, dateTo, limit = 100 } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
      customer_id: customerId,
    };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    // Get customer details
    const customer = await models.Customer.findOne({
      where: { id: customerId, tenant_id: tenantId },
      attributes: ['id', 'company_name', 'customer_code', 'contact_person', 'email', 'mobile_number'],
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Get service history
    const serviceHistory = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'color'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.TypeOfCall,
          as: 'callType',
          attributes: ['id', 'name'],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
    });

    // Calculate customer metrics
    const totalCalls = serviceHistory.length;
    const completedCalls = serviceHistory.filter(call =>
      call.status?.category === 'closed' || call.status?.category === 'resolved'
    ).length;
    const openCalls = serviceHistory.filter(call => call.status?.category === 'open').length;
    const inProgressCalls = serviceHistory.filter(call => call.status?.category === 'in_progress').length;

    // Calculate average satisfaction
    const satisfactionRatings = serviceHistory
      .filter(call => call.customer_satisfaction)
      .map(call => call.customer_satisfaction);

    const avgSatisfaction = satisfactionRatings.length > 0 ?
      (satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length).toFixed(2) : 0;

    // Calculate total service hours
    const totalServiceHours = serviceHistory
      .filter(call => call.actual_hours)
      .reduce((sum, call) => sum + parseFloat(call.actual_hours), 0);

    // Group by issue types
    const issueTypes = {};
    serviceHistory.forEach(call => {
      const issueType = call.natureOfIssue?.name || 'Other';
      issueTypes[issueType] = (issueTypes[issueType] || 0) + 1;
    });

    // Recent activity timeline
    const timeline = serviceHistory.slice(0, 10).map(call => ({
      id: call.id,
      callNumber: call.call_number,
      subject: call.subject,
      status: call.status,
      assignedExecutive: call.assignedExecutive ? {
        name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim(),
        employeeCode: call.assignedExecutive.employee_code,
      } : null,
      callType: call.callType,
      natureOfIssue: call.natureOfIssue,
      priority: call.priority,
      createdAt: call.created_at,
      completedAt: call.completed_at,
      customerSatisfaction: call.customer_satisfaction,
      actualHours: call.actual_hours,
    }));

    res.json({
      success: true,
      data: {
        customer,
        serviceHistory: serviceHistory.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          subject: call.subject,
          description: call.description,
          status: call.status,
          assignedExecutive: call.assignedExecutive ? {
            ...call.assignedExecutive.toJSON(),
            name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
          } : null,
          callType: call.callType,
          natureOfIssue: call.natureOfIssue,
          priority: call.priority,
          createdAt: call.created_at,
          completedAt: call.completed_at,
          customerSatisfaction: call.customer_satisfaction,
          actualHours: call.actual_hours,
          solutionProvided: call.solution_provided,
        })),
        summary: {
          totalCalls,
          completedCalls,
          openCalls,
          inProgressCalls,
          avgSatisfaction: parseFloat(avgSatisfaction),
          totalServiceHours: totalServiceHours.toFixed(2),
          satisfactionRatingsCount: satisfactionRatings.length,
        },
        analytics: {
          issueTypes,
          timeline,
          callTrend: {
            // This could be expanded to show monthly/weekly trends
            lastMonth: serviceHistory.filter(call => {
              const callDate = new Date(call.created_at);
              const lastMonth = new Date();
              lastMonth.setMonth(lastMonth.getMonth() - 1);
              return callDate >= lastMonth;
            }).length,
          },
        },
      },
    });
  } catch (error) {
    logger.error('Get customer service history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate customer service history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Turnaround Time (TAT) Report
 * Average time taken to resolve issues by type or category
 */
export const getServiceTurnaroundTimeReport = async (req, res) => {
  try {
    const { dateFrom, dateTo, groupBy = 'issue_type' } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions for completed calls only
    const whereConditions = {
      tenant_id: tenantId,
      completed_at: { [Op.not]: null },
    };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    // Get completed service calls with resolution times
    const completedCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name'],
        },
        {
          model: models.TypeOfCall,
          as: 'callType',
          attributes: ['id', 'name'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category'],
        },
      ],
      attributes: ['id', 'call_number', 'priority', 'created_at', 'completed_at', 'actual_hours'],
    });

    // Calculate turnaround times
    const tatData = completedCalls.map(call => {
      const resolutionTimeMs = new Date(call.completed_at) - new Date(call.created_at);
      const resolutionTimeHours = resolutionTimeMs / (1000 * 60 * 60);
      const resolutionTimeDays = resolutionTimeMs / (1000 * 60 * 60 * 24);

      return {
        id: call.id,
        callNumber: call.call_number,
        priority: call.priority,
        issueType: call.natureOfIssue?.name || 'Other',
        callType: call.callType?.name || 'General',
        createdAt: call.created_at,
        completedAt: call.completed_at,
        resolutionTimeHours: resolutionTimeHours.toFixed(2),
        resolutionTimeDays: resolutionTimeDays.toFixed(2),
        actualHours: call.actual_hours,
      };
    });

    // Group by specified criteria
    const groupedData = {};

    tatData.forEach(call => {
      let groupKey;

      switch (groupBy) {
        case 'issue_type':
          groupKey = call.issueType;
          break;
        case 'call_type':
          groupKey = call.callType;
          break;
        case 'priority':
          groupKey = call.priority;
          break;
        default:
          groupKey = call.issueType;
      }

      if (!groupedData[groupKey]) {
        groupedData[groupKey] = {
          groupName: groupKey,
          calls: [],
          totalCalls: 0,
          avgTatHours: 0,
          avgTatDays: 0,
          minTatHours: Infinity,
          maxTatHours: 0,
        };
      }

      groupedData[groupKey].calls.push(call);
      groupedData[groupKey].totalCalls++;
    });

    // Calculate statistics for each group
    Object.values(groupedData).forEach(group => {
      const resolutionTimes = group.calls.map(call => parseFloat(call.resolutionTimeHours));

      group.avgTatHours = (resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length).toFixed(2);
      group.avgTatDays = (parseFloat(group.avgTatHours) / 24).toFixed(2);
      group.minTatHours = Math.min(...resolutionTimes).toFixed(2);
      group.maxTatHours = Math.max(...resolutionTimes).toFixed(2);

      // SLA compliance (assuming 24 hours SLA)
      const slaCompliantCalls = group.calls.filter(call => parseFloat(call.resolutionTimeHours) <= 24).length;
      group.slaCompliance = ((slaCompliantCalls / group.totalCalls) * 100).toFixed(2);
    });

    // Overall statistics
    const overallStats = {
      totalCompletedCalls: tatData.length,
      avgTatHours: tatData.length > 0 ?
        (tatData.reduce((sum, call) => sum + parseFloat(call.resolutionTimeHours), 0) / tatData.length).toFixed(2) : 0,
      avgTatDays: 0,
      slaCompliance: 0,
    };

    overallStats.avgTatDays = (parseFloat(overallStats.avgTatHours) / 24).toFixed(2);

    const slaCompliantCalls = tatData.filter(call => parseFloat(call.resolutionTimeHours) <= 24).length;
    overallStats.slaCompliance = tatData.length > 0 ?
      ((slaCompliantCalls / tatData.length) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        groupedData: Object.values(groupedData).sort((a, b) => parseFloat(b.avgTatHours) - parseFloat(a.avgTatHours)),
        overallStats,
        rawData: tatData.slice(0, 50), // Limit raw data for performance
        summary: {
          groupBy,
          totalGroups: Object.keys(groupedData).length,
          dateRange: { dateFrom, dateTo },
        },
      },
    });
  } catch (error) {
    logger.error('Get service turnaround time report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service turnaround time report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get First Response Time Report
 * Time between request creation and first customer response
 */
export const getFirstResponseTimeReport = async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
      started_at: { [Op.not]: null }, // Only calls that have been started
    };

    if (dateFrom && dateTo) {
      whereConditions.created_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    // Get service calls with first response times
    const serviceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name'],
        },
      ],
      attributes: ['id', 'call_number', 'priority', 'created_at', 'started_at'],
      order: [['created_at', 'DESC']],
    });

    // Calculate first response times
    const responseTimeData = serviceCalls.map(call => {
      const responseTimeMs = new Date(call.started_at) - new Date(call.created_at);
      const responseTimeHours = responseTimeMs / (1000 * 60 * 60);
      const responseTimeMinutes = responseTimeMs / (1000 * 60);

      let responseCategory = 'Excellent';
      if (responseTimeHours > 24) responseCategory = 'Poor';
      else if (responseTimeHours > 8) responseCategory = 'Average';
      else if (responseTimeHours > 2) responseCategory = 'Good';

      return {
        id: call.id,
        callNumber: call.call_number,
        customer: call.customer,
        assignedExecutive: call.assignedExecutive ? {
          name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
        } : null,
        priority: call.priority,
        createdAt: call.created_at,
        startedAt: call.started_at,
        responseTimeHours: responseTimeHours.toFixed(2),
        responseTimeMinutes: responseTimeMinutes.toFixed(0),
        responseCategory,
      };
    });

    // Group by response categories
    const responseBuckets = {
      'Excellent (≤2h)': responseTimeData.filter(call => parseFloat(call.responseTimeHours) <= 2).length,
      'Good (2-8h)': responseTimeData.filter(call => parseFloat(call.responseTimeHours) > 2 && parseFloat(call.responseTimeHours) <= 8).length,
      'Average (8-24h)': responseTimeData.filter(call => parseFloat(call.responseTimeHours) > 8 && parseFloat(call.responseTimeHours) <= 24).length,
      'Poor (>24h)': responseTimeData.filter(call => parseFloat(call.responseTimeHours) > 24).length,
    };

    // Calculate overall statistics
    const avgResponseTime = responseTimeData.length > 0 ?
      (responseTimeData.reduce((sum, call) => sum + parseFloat(call.responseTimeHours), 0) / responseTimeData.length).toFixed(2) : 0;

    const slaTarget = 2; // 2 hours SLA
    const slaCompliantCalls = responseTimeData.filter(call => parseFloat(call.responseTimeHours) <= slaTarget).length;
    const slaCompliance = responseTimeData.length > 0 ?
      ((slaCompliantCalls / responseTimeData.length) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        responseTimeData: responseTimeData.slice(0, 100), // Limit for performance
        responseBuckets,
        summary: {
          totalCalls: responseTimeData.length,
          avgResponseTimeHours: parseFloat(avgResponseTime),
          slaCompliance: parseFloat(slaCompliance),
          slaTarget,
          excellentResponses: responseBuckets['Excellent (≤2h)'],
          poorResponses: responseBuckets['Poor (>24h)'],
        },
      },
    });
  } catch (error) {
    logger.error('Get first response time report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate first response time report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get AMC Tracking Report
 * Active/expired AMCs per customer with renewal tracking
 */
export const getAmcTrackingReport = async (req, res) => {
  try {
    const { status = 'all', daysToExpiry = 30 } = req.query;
    const tenantId = req.user.tenant.id;

    // This would typically come from an AMC table, but we'll simulate with customer data
    // In a real implementation, you'd have a separate AMC/Contract table

    // For now, we'll create a mock AMC report based on customers
    const customers = await models.Customer.findAll({
      where: { tenant_id: tenantId },
      attributes: ['id', 'company_name', 'customer_code', 'contact_person', 'email', 'mobile_number'],
    });

    // Mock AMC data - in real implementation, this would come from AMC table
    const amcData = customers.map(customer => {
      // Generate mock AMC data
      const startDate = new Date();
      startDate.setFullYear(startDate.getFullYear() - 1);

      const endDate = new Date();
      endDate.setFullYear(endDate.getFullYear() + Math.random() > 0.3 ? 1 : -1); // 70% active, 30% expired

      const daysToExpiry = Math.floor((endDate - new Date()) / (1000 * 60 * 60 * 24));

      let amcStatus = 'active';
      if (daysToExpiry < 0) amcStatus = 'expired';
      else if (daysToExpiry <= 30) amcStatus = 'expiring_soon';

      return {
        customer,
        amcDetails: {
          id: `AMC-${customer.customer_code}`,
          startDate,
          endDate,
          daysToExpiry,
          status: amcStatus,
          amcValue: Math.floor(Math.random() * 50000) + 10000, // Random AMC value
          renewalReminders: daysToExpiry <= 30 && daysToExpiry > 0 ? 2 : 0,
        },
      };
    });

    // Filter based on status
    let filteredData = amcData;
    if (status !== 'all') {
      filteredData = amcData.filter(item => item.amcDetails.status === status);
    }

    // Group by status
    const statusSummary = {
      active: amcData.filter(item => item.amcDetails.status === 'active').length,
      expiring_soon: amcData.filter(item => item.amcDetails.status === 'expiring_soon').length,
      expired: amcData.filter(item => item.amcDetails.status === 'expired').length,
    };

    // Calculate revenue metrics
    const totalAmcValue = amcData.reduce((sum, item) => sum + item.amcDetails.amcValue, 0);
    const activeAmcValue = amcData
      .filter(item => item.amcDetails.status === 'active')
      .reduce((sum, item) => sum + item.amcDetails.amcValue, 0);

    res.json({
      success: true,
      data: {
        amcData: filteredData,
        statusSummary,
        revenueMetrics: {
          totalAmcValue,
          activeAmcValue,
          potentialLoss: amcData
            .filter(item => item.amcDetails.status === 'expired')
            .reduce((sum, item) => sum + item.amcDetails.amcValue, 0),
          renewalOpportunity: amcData
            .filter(item => item.amcDetails.status === 'expiring_soon')
            .reduce((sum, item) => sum + item.amcDetails.amcValue, 0),
        },
        summary: {
          totalCustomers: customers.length,
          totalAmcs: amcData.length,
          renewalsDue: statusSummary.expiring_soon,
          expiredAmcs: statusSummary.expired,
        },
      },
    });
  } catch (error) {
    logger.error('Get AMC tracking report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AMC tracking report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Billing Report
 * Invoices generated for service calls or maintenance visits
 */
export const getServiceBillingReport = async (req, res) => {
  try {
    const { dateFrom, dateTo, customerId, paymentStatus = 'all' } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions for billable service calls
    const whereConditions = {
      tenant_id: tenantId,
      billable_hours: { [Op.gt]: 0 }, // Only billable calls
    };

    if (dateFrom && dateTo) {
      whereConditions.completed_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    if (customerId) {
      whereConditions.customer_id = customerId;
    }

    // Get billable service calls
    const billableServiceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code', 'contact_person'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name'],
        },
      ],
      attributes: [
        'id', 'call_number', 'subject', 'billable_hours', 'completed_at',
        'created_at', 'hourly_rate', 'priority'
      ],
    });

    // Calculate billing information
    const billingData = billableServiceCalls.map(call => {
      const hourlyRate = call.hourly_rate || 1000; // Default rate if not set
      const billableAmount = parseFloat(call.billable_hours) * hourlyRate;

      // Mock invoice data - in real implementation, this would come from invoice table
      const invoiceNumber = `INV-${call.call_number}`;
      const invoiceDate = call.completed_at;
      const dueDate = new Date(invoiceDate);
      dueDate.setDate(dueDate.getDate() + 30); // 30 days payment terms

      const isPaid = Math.random() > 0.3; // 70% paid, 30% pending
      const paymentDate = isPaid ? new Date(invoiceDate.getTime() + Math.random() * 20 * 24 * 60 * 60 * 1000) : null;

      return {
        serviceCall: {
          id: call.id,
          callNumber: call.call_number,
          subject: call.subject,
          priority: call.priority,
          completedAt: call.completed_at,
        },
        customer: call.customer,
        assignedExecutive: call.assignedExecutive ? {
          name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
        } : null,
        billing: {
          billableHours: parseFloat(call.billable_hours),
          hourlyRate,
          billableAmount,
          invoiceNumber,
          invoiceDate,
          dueDate,
          paymentStatus: isPaid ? 'paid' : 'pending',
          paymentDate,
          overdueDays: !isPaid && new Date() > dueDate ?
            Math.floor((new Date() - dueDate) / (1000 * 60 * 60 * 24)) : 0,
        },
      };
    });

    // Filter by payment status
    let filteredData = billingData;
    if (paymentStatus !== 'all') {
      filteredData = billingData.filter(item => item.billing.paymentStatus === paymentStatus);
    }

    // Calculate summary metrics
    const totalBillableAmount = billingData.reduce((sum, item) => sum + item.billing.billableAmount, 0);
    const paidAmount = billingData
      .filter(item => item.billing.paymentStatus === 'paid')
      .reduce((sum, item) => sum + item.billing.billableAmount, 0);
    const pendingAmount = billingData
      .filter(item => item.billing.paymentStatus === 'pending')
      .reduce((sum, item) => sum + item.billing.billableAmount, 0);
    const overdueAmount = billingData
      .filter(item => item.billing.paymentStatus === 'pending' && item.billing.overdueDays > 0)
      .reduce((sum, item) => sum + item.billing.billableAmount, 0);

    // Group by customer
    const customerBilling = {};
    billingData.forEach(item => {
      const customerId = item.customer.id;
      if (!customerBilling[customerId]) {
        customerBilling[customerId] = {
          customer: item.customer,
          totalAmount: 0,
          paidAmount: 0,
          pendingAmount: 0,
          serviceCallsCount: 0,
        };
      }

      customerBilling[customerId].totalAmount += item.billing.billableAmount;
      customerBilling[customerId].serviceCallsCount++;

      if (item.billing.paymentStatus === 'paid') {
        customerBilling[customerId].paidAmount += item.billing.billableAmount;
      } else {
        customerBilling[customerId].pendingAmount += item.billing.billableAmount;
      }
    });

    res.json({
      success: true,
      data: {
        billingData: filteredData,
        summary: {
          totalServiceCalls: billingData.length,
          totalBillableHours: billingData.reduce((sum, item) => sum + item.billing.billableHours, 0),
          totalBillableAmount,
          paidAmount,
          pendingAmount,
          overdueAmount,
          collectionRate: totalBillableAmount > 0 ? ((paidAmount / totalBillableAmount) * 100).toFixed(2) : 0,
        },
        customerBilling: Object.values(customerBilling)
          .sort((a, b) => b.totalAmount - a.totalAmount),
        paymentAnalysis: {
          paid: billingData.filter(item => item.billing.paymentStatus === 'paid').length,
          pending: billingData.filter(item => item.billing.paymentStatus === 'pending').length,
          overdue: billingData.filter(item => item.billing.overdueDays > 0).length,
        },
      },
    });
  } catch (error) {
    logger.error('Get service billing report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service billing report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Trend Analysis
 * Volume of tickets by month, category, or team with visual trends
 */
export const getServiceTrendAnalysis = async (req, res) => {
  try {
    const {
      dateFrom,
      dateTo,
      groupBy = 'month', // month, week, day
      trendType = 'volume' // volume, resolution_time, satisfaction
    } = req.query;
    const tenantId = req.user.tenant.id;

    // Default to last 12 months if no date range provided
    const endDate = dateTo ? new Date(dateTo) : new Date();
    const startDate = dateFrom ? new Date(dateFrom) : new Date(endDate.getFullYear() - 1, endDate.getMonth(), endDate.getDate());

    // Get service calls within date range
    const serviceCalls = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category'],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name'],
        },
      ],
      attributes: [
        'id', 'created_at', 'completed_at', 'customer_satisfaction',
        'priority', 'actual_hours'
      ],
    });

    // Generate time periods based on groupBy
    const periods = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      let periodKey;
      let periodLabel;

      switch (groupBy) {
        case 'day':
          periodKey = current.toISOString().split('T')[0];
          periodLabel = current.toLocaleDateString();
          current.setDate(current.getDate() + 1);
          break;
        case 'week':
          const weekStart = new Date(current);
          weekStart.setDate(current.getDate() - current.getDay());
          periodKey = weekStart.toISOString().split('T')[0];
          periodLabel = `Week of ${weekStart.toLocaleDateString()}`;
          current.setDate(current.getDate() + 7);
          break;
        case 'month':
        default:
          periodKey = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`;
          periodLabel = current.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
          current.setMonth(current.getMonth() + 1);
          break;
      }

      periods.push({ key: periodKey, label: periodLabel, data: [] });
    }

    // Group service calls by periods
    serviceCalls.forEach(call => {
      const callDate = new Date(call.created_at);
      let periodKey;

      switch (groupBy) {
        case 'day':
          periodKey = callDate.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(callDate);
          weekStart.setDate(callDate.getDate() - callDate.getDay());
          periodKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
        default:
          periodKey = `${callDate.getFullYear()}-${String(callDate.getMonth() + 1).padStart(2, '0')}`;
          break;
      }

      const period = periods.find(p => p.key === periodKey);
      if (period) {
        period.data.push(call);
      }
    });

    // Calculate trend data based on trendType
    const trendData = periods.map(period => {
      const calls = period.data;
      let value = 0;

      switch (trendType) {
        case 'resolution_time':
          const completedCalls = calls.filter(call => call.completed_at);
          if (completedCalls.length > 0) {
            const totalResolutionTime = completedCalls.reduce((sum, call) => {
              const resolutionTime = new Date(call.completed_at) - new Date(call.created_at);
              return sum + (resolutionTime / (1000 * 60 * 60)); // Convert to hours
            }, 0);
            value = (totalResolutionTime / completedCalls.length).toFixed(2);
          }
          break;
        case 'satisfaction':
          const ratedCalls = calls.filter(call => call.customer_satisfaction);
          if (ratedCalls.length > 0) {
            const totalSatisfaction = ratedCalls.reduce((sum, call) => sum + call.customer_satisfaction, 0);
            value = (totalSatisfaction / ratedCalls.length).toFixed(2);
          }
          break;
        case 'volume':
        default:
          value = calls.length;
          break;
      }

      return {
        period: period.label,
        periodKey: period.key,
        value: parseFloat(value),
        callsCount: calls.length,
        breakdown: {
          byPriority: {
            low: calls.filter(call => call.priority === 'low').length,
            medium: calls.filter(call => call.priority === 'medium').length,
            high: calls.filter(call => call.priority === 'high').length,
            critical: calls.filter(call => call.priority === 'critical').length,
          },
          byStatus: calls.reduce((acc, call) => {
            const status = call.status?.category || 'unknown';
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          }, {}),
        },
      };
    });

    // Calculate trend indicators
    const trendIndicators = {
      direction: 'stable',
      percentage: 0,
      isImproving: false,
    };

    if (trendData.length >= 2) {
      const recent = trendData.slice(-3).reduce((sum, period) => sum + period.value, 0) / Math.min(3, trendData.length);
      const previous = trendData.slice(-6, -3).reduce((sum, period) => sum + period.value, 0) / Math.min(3, trendData.length);

      if (previous > 0) {
        const change = ((recent - previous) / previous) * 100;
        trendIndicators.percentage = Math.abs(change).toFixed(2);

        if (Math.abs(change) > 5) {
          trendIndicators.direction = change > 0 ? 'increasing' : 'decreasing';
          // For satisfaction, increasing is good; for volume/resolution_time, it depends on context
          trendIndicators.isImproving = trendType === 'satisfaction' ? change > 0 : change < 0;
        }
      }
    }

    res.json({
      success: true,
      data: {
        trendData,
        trendIndicators,
        summary: {
          totalPeriods: periods.length,
          totalCalls: serviceCalls.length,
          avgPerPeriod: serviceCalls.length / periods.length,
          trendType,
          groupBy,
          dateRange: { startDate, endDate },
        },
      },
    });
  } catch (error) {
    logger.error('Get service trend analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service trend analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Customer Satisfaction Report (CSAT)
 * Based on post-service surveys and ratings
 */
export const getCustomerSatisfactionReport = async (req, res) => {
  try {
    const { dateFrom, dateTo, customerId, executiveId } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions for calls with satisfaction ratings
    const whereConditions = {
      tenant_id: tenantId,
      customer_satisfaction: { [Op.not]: null },
    };

    if (dateFrom && dateTo) {
      whereConditions.completed_at = {
        [Op.between]: [new Date(dateFrom), new Date(dateTo)],
      };
    }

    if (customerId) whereConditions.customer_id = customerId;
    if (executiveId) whereConditions.assigned_to = executiveId;

    // Get service calls with satisfaction ratings
    const ratedServiceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code', 'contact_person'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name'],
        },
      ],
      attributes: [
        'id', 'call_number', 'subject', 'customer_satisfaction',
        'customer_feedback', 'completed_at', 'priority'
      ],
      order: [['completed_at', 'DESC']],
    });

    // Calculate satisfaction metrics
    const satisfactionData = ratedServiceCalls.map(call => ({
      id: call.id,
      callNumber: call.call_number,
      subject: call.subject,
      customer: call.customer,
      assignedExecutive: call.assignedExecutive ? {
        ...call.assignedExecutive.toJSON(),
        name: `${call.assignedExecutive.first_name || ''} ${call.assignedExecutive.last_name || ''}`.trim()
      } : null,
      natureOfIssue: call.natureOfIssue,
      priority: call.priority,
      rating: call.customer_satisfaction,
      feedback: call.customer_feedback,
      completedAt: call.completed_at,
      ratingCategory: call.customer_satisfaction >= 4 ? 'Satisfied' :
                     call.customer_satisfaction >= 3 ? 'Neutral' : 'Dissatisfied',
    }));

    // Group by rating categories
    const ratingDistribution = {
      'Excellent (5)': satisfactionData.filter(call => call.rating === 5).length,
      'Good (4)': satisfactionData.filter(call => call.rating === 4).length,
      'Average (3)': satisfactionData.filter(call => call.rating === 3).length,
      'Poor (2)': satisfactionData.filter(call => call.rating === 2).length,
      'Very Poor (1)': satisfactionData.filter(call => call.rating === 1).length,
    };

    // Calculate overall metrics
    const totalRatings = satisfactionData.length;
    const avgRating = totalRatings > 0 ?
      (satisfactionData.reduce((sum, call) => sum + call.rating, 0) / totalRatings).toFixed(2) : 0;

    const satisfiedCustomers = satisfactionData.filter(call => call.rating >= 4).length;
    const satisfactionRate = totalRatings > 0 ? ((satisfiedCustomers / totalRatings) * 100).toFixed(2) : 0;

    const npsPromoters = satisfactionData.filter(call => call.rating >= 4).length;
    const npsDetractors = satisfactionData.filter(call => call.rating <= 2).length;
    const npsScore = totalRatings > 0 ? (((npsPromoters - npsDetractors) / totalRatings) * 100).toFixed(2) : 0;

    // Group by customer
    const customerSatisfaction = {};
    satisfactionData.forEach(call => {
      const customerId = call.customer.id;
      if (!customerSatisfaction[customerId]) {
        customerSatisfaction[customerId] = {
          customer: call.customer,
          ratings: [],
          avgRating: 0,
          totalRatings: 0,
        };
      }

      customerSatisfaction[customerId].ratings.push(call.rating);
      customerSatisfaction[customerId].totalRatings++;
    });

    // Calculate average ratings per customer
    Object.values(customerSatisfaction).forEach(customerData => {
      customerData.avgRating = (customerData.ratings.reduce((sum, rating) => sum + rating, 0) / customerData.ratings.length).toFixed(2);
    });

    // Group by executive
    const executiveSatisfaction = {};
    satisfactionData.forEach(call => {
      if (call.assignedExecutive) {
        const executiveId = call.assignedExecutive.id;
        if (!executiveSatisfaction[executiveId]) {
          executiveSatisfaction[executiveId] = {
            executive: call.assignedExecutive,
            ratings: [],
            avgRating: 0,
            totalRatings: 0,
          };
        }

        executiveSatisfaction[executiveId].ratings.push(call.rating);
        executiveSatisfaction[executiveId].totalRatings++;
      }
    });

    // Calculate average ratings per executive
    Object.values(executiveSatisfaction).forEach(executiveData => {
      executiveData.avgRating = (executiveData.ratings.reduce((sum, rating) => sum + rating, 0) / executiveData.ratings.length).toFixed(2);
    });

    // Get feedback analysis
    const feedbackAnalysis = {
      totalFeedbacks: satisfactionData.filter(call => call.feedback && call.feedback.trim()).length,
      positiveFeedbacks: satisfactionData.filter(call => call.rating >= 4 && call.feedback && call.feedback.trim()).length,
      negativeFeedbacks: satisfactionData.filter(call => call.rating <= 2 && call.feedback && call.feedback.trim()).length,
      recentFeedbacks: satisfactionData
        .filter(call => call.feedback && call.feedback.trim())
        .slice(0, 10)
        .map(call => ({
          callNumber: call.callNumber,
          customer: call.customer.company_name,
          rating: call.rating,
          feedback: call.feedback,
          completedAt: call.completedAt,
        })),
    };

    res.json({
      success: true,
      data: {
        satisfactionData: satisfactionData.slice(0, 100), // Limit for performance
        ratingDistribution,
        overallMetrics: {
          totalRatings,
          avgRating: parseFloat(avgRating),
          satisfactionRate: parseFloat(satisfactionRate),
          npsScore: parseFloat(npsScore),
          responseRate: totalRatings, // This would be calculated against total completed calls
        },
        customerSatisfaction: Object.values(customerSatisfaction)
          .sort((a, b) => parseFloat(b.avgRating) - parseFloat(a.avgRating)),
        executiveSatisfaction: Object.values(executiveSatisfaction)
          .sort((a, b) => parseFloat(b.avgRating) - parseFloat(a.avgRating)),
        feedbackAnalysis,
        summary: {
          excellentRatings: ratingDistribution['Excellent (5)'],
          poorRatings: ratingDistribution['Very Poor (1)'] + ratingDistribution['Poor (2)'],
          improvementOpportunities: satisfactionData.filter(call => call.rating <= 3).length,
        },
      },
    });
  } catch (error) {
    logger.error('Get customer satisfaction report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate customer satisfaction report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get Service Analytics
 * Comprehensive analytics for service operations
 */
export const getServiceAnalytics = async (req, res) => {
  try {
    const {
      dateRange = '30',
      groupBy = 'day',
      // New filter parameters
      dateFrom,
      dateTo,
      statusId,
      priority,
      assignedTo,
      customerId,
      callBillingType,
      isOverdue,
      statusCategory,
      scheduledDateFrom,
      scheduledDateTo,
    } = req.query;
    const tenantId = req.user.tenant.id;

    // Build where conditions
    const whereConditions = {
      tenant_id: tenantId,
    };

    // Initialize date range variables
    let startDate, endDate;

    // Date range filter - use custom dates if provided, otherwise use dateRange
    if (dateFrom || dateTo) {
      whereConditions.created_at = {};
      if (dateFrom) {
        startDate = new Date(dateFrom);
        whereConditions.created_at[Op.gte] = startDate;
      }
      if (dateTo) {
        endDate = new Date(dateTo);
        whereConditions.created_at[Op.lte] = endDate;
      }

      // Set default values if only one date is provided
      if (!startDate && endDate) {
        startDate = new Date();
        startDate.setDate(startDate.getDate() - parseInt(dateRange));
      }
      if (!endDate && startDate) {
        endDate = new Date();
      }
    } else {
      // Calculate date range from dateRange parameter
      endDate = new Date();
      startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(dateRange));
      whereConditions.created_at = {
        [Op.between]: [startDate, endDate],
      };
    }

    // Additional filter conditions
    if (customerId) whereConditions.customer_id = customerId;
    if (priority) whereConditions.priority = priority;
    if (statusId) whereConditions.status_id = statusId;
    if (assignedTo) whereConditions.assigned_to = assignedTo;
    if (callBillingType) {
      if (callBillingType === 'free_call') {
        // Free call filter - match multiple conditions like in analytics
        whereConditions[Op.or] = [
          { call_billing_type: 'free_call' },
          {
            [Op.and]: [
              { is_billable: false },
              { is_under_amc: false }
            ]
          },
          { service_charges: { [Op.or]: [0, null] } }
        ];
      } else {
        whereConditions.call_billing_type = callBillingType;
      }
    }

    // Scheduled date range filter
    if (scheduledDateFrom || scheduledDateTo) {
      whereConditions.scheduled_date = {};
      if (scheduledDateFrom) whereConditions.scheduled_date[Op.gte] = new Date(scheduledDateFrom);
      if (scheduledDateTo) whereConditions.scheduled_date[Op.lte] = new Date(scheduledDateTo);
    }

    // Handle overdue filter - must be done after includes are set up
    if (isOverdue === 'true') {
      whereConditions.scheduled_date = {
        ...whereConditions.scheduled_date,
        [Op.lt]: new Date(), // Scheduled date is in the past
      };
      whereConditions['$status.category$'] = 'open'; // Still open/pending
    }

    // Handle status category filter
    if (statusCategory) {
      whereConditions['$status.category$'] = statusCategory;
    }

    // Get service calls with filters
    const serviceCalls = await models.ServiceCall.findAll({
      where: whereConditions,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'employee_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category'],
        },
        {
          model: models.TypeOfCall,
          as: 'typeOfCall',
          attributes: ['id', 'name'],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    // Calculate summary metrics
    const totalCalls = serviceCalls.length;
    const completedCalls = serviceCalls.filter(call => {
      // Check for completed status by name or category
      const statusName = call.status?.name?.toLowerCase();
      const statusCategory = call.status?.category?.toLowerCase();
      const statusCode = call.status?.code?.toLowerCase();

      return statusName === 'completed' ||
             statusCode === 'completed' ||
             statusCategory === 'closed' ||
             statusCategory === 'resolved' ||
             call.completed_at !== null; // Also check if completed_at is set
    }).length;
    const openCalls = serviceCalls.filter(call => call.status?.category === 'open').length;
    const inProgressCalls = serviceCalls.filter(call => call.status?.category === 'in_progress').length;
    const completionRate = totalCalls > 0 ? ((completedCalls / totalCalls) * 100).toFixed(1) : 0;

    // Calculate call billing types with more flexible matching
    const freeCalls = serviceCalls.filter(call => {
      const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
      return (
        billingType === 'free_call' ||
        billingType === 'free call' ||
        billingType === 'free' ||
        billingType === null || // Include NULL billing types as free calls
        billingType === '' ||
        (!call.is_billable && !call.is_under_amc) ||
        (call.service_charges === 0 || call.service_charges === null)
      );
    }).length;

    const amcCalls = serviceCalls.filter(call => {
      const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
      return (
        billingType === 'amc_call' ||
        billingType === 'amc call' ||
        billingType === 'amc' ||
        call.is_under_amc === true
      );
    }).length;

    const paidCalls = serviceCalls.filter(call => {
      const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
      return (
        billingType === 'per_call' ||
        billingType === 'per call' ||
        billingType === 'paid_call' ||
        billingType === 'paid call' ||
        billingType === 'paid' ||
        (call.is_billable === true && !call.is_under_amc && call.service_charges > 0)
      );
    }).length;

    // Calculate trends based on groupBy
    const trends = {};
    serviceCalls.forEach(call => {
      let dateKey;
      const callDate = new Date(call.created_at);

      switch (groupBy) {
        case 'week':
          const weekStart = new Date(callDate);
          weekStart.setDate(callDate.getDate() - callDate.getDay());
          dateKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          dateKey = `${callDate.getFullYear()}-${String(callDate.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'day':
        default:
          dateKey = callDate.toISOString().split('T')[0];
          break;
      }

      trends[dateKey] = (trends[dateKey] || 0) + 1;
    });

    // Calculate call types distribution
    const callTypes = {};
    serviceCalls.forEach(call => {
      const type = call.typeOfCall?.name || 'Unknown';
      callTypes[type] = (callTypes[type] || 0) + 1;
    });

    // Calculate billing types distribution
    const billingTypes = {
      'Free Calls': freeCalls,
      'AMC Calls': amcCalls,
      'Per Calls': paidCalls,
    };

    // Get all executives for performance calculation
    const executives = await models.Executive.findAll({
      where: { tenant_id: tenantId },
      attributes: ['id', 'first_name', 'last_name', 'employee_code'],
    });

    // Calculate technician performance
    const technicianPerformance = {};
    executives.forEach(exec => {
      const execCalls = serviceCalls.filter(call => call.assigned_to === exec.id);
      const execCompleted = execCalls.filter(call =>
        call.status?.category === 'closed' || call.status?.category === 'resolved'
      ).length;
      const execCompletionRate = execCalls.length > 0 ? ((execCompleted / execCalls.length) * 100).toFixed(1) : 0;

      // Calculate average response time
      const responseTimes = execCalls
        .filter(call => call.started_at && call.created_at)
        .map(call => {
          const responseTime = new Date(call.started_at) - new Date(call.created_at);
          return responseTime / (1000 * 60 * 60); // Convert to hours
        });

      const avgResponseTime = responseTimes.length > 0 ?
        (responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length).toFixed(2) : 0;

      technicianPerformance[exec.id] = {
        name: `${exec.first_name || ''} ${exec.last_name || ''}`.trim(),
        totalCalls: execCalls.length,
        completedCalls: execCompleted,
        completionRate: parseFloat(execCompletionRate),
        avgResponseTime: parseFloat(avgResponseTime),
      };
    });

    // Calculate response and resolution times
    const responseTimes = serviceCalls
      .filter(call => call.started_at && call.created_at)
      .map(call => {
        const responseTime = new Date(call.started_at) - new Date(call.created_at);
        return responseTime / (1000 * 60 * 60); // Convert to hours
      });

    const resolutionTimes = serviceCalls
      .filter(call => call.completed_at && call.created_at)
      .map(call => {
        const resolutionTime = new Date(call.completed_at) - new Date(call.created_at);
        return resolutionTime / (1000 * 60 * 60); // Convert to hours
      });

    const avgResponseTime = responseTimes.length > 0 ?
      (responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length).toFixed(2) : 0;

    const avgResolutionTime = resolutionTimes.length > 0 ?
      (resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length).toFixed(2) : 0;

    // Debug logging for service analytics
    console.log('📊 Service Analytics Debug:', {
      totalServiceCalls: serviceCalls.length,
      freeCalls,
      amcCalls,
      paidCalls,
      sampleBillingTypes: serviceCalls.slice(0, 5).map(call => ({
        id: call.id,
        call_billing_type: call.call_billing_type,
        is_billable: call.is_billable,
        is_under_amc: call.is_under_amc,
        service_charges: call.service_charges
      }))
    });

    res.json({
      success: true,
      data: {
        summary: {
          totalCalls,
          completedCalls,
          openCalls,
          inProgressCalls,
          freeCalls,
          amcCalls,
          paidCalls,
          completionRate: parseFloat(completionRate),
          avgResponseTime: parseFloat(avgResponseTime),
          avgResolutionTime: parseFloat(avgResolutionTime),
        },
        trends,
        callTypes,
        billingTypes,
        technicianPerformance,
        responseTime: {
          average: parseFloat(avgResponseTime),
          data: responseTimes,
        },
        resolution: {
          average: parseFloat(avgResolutionTime),
          data: resolutionTimes,
        },
        dateRange: {
          startDate,
          endDate,
          days: parseInt(dateRange),
        },
      },
    });
  } catch (error) {
    logger.error('Get service analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate service analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};