# TallyCRM Troubleshooting Guide

Quick solutions to common issues and problems

## 🔍 Before You Start

### Quick Checklist
Before reporting an issue, try these basic steps:
- ✅ Refresh your browser (F5 or Ctrl+R)
- ✅ Check your internet connection
- ✅ Ensure you're using a supported browser
- ✅ Clear your browser cache if needed
- ✅ Try logging out and back in

---

## 🚪 Login Issues

### Cannot Login / Invalid Credentials

**Problem**: "Invalid username or password" error

**Solutions**:
1. **Check Credentials**
   - Verify username spelling
   - Ensure password is correct
   - Check if Caps Lock is on
   - Try typing password in a text editor first

2. **Browser Issues**
   - Clear browser cache and cookies
   - Try incognito/private browsing mode
   - Use a different browser

3. **Account Issues**
   - Contact your administrator - account may be locked
   - Password may have expired
   - Account may be deactivated

### Page Won't Load After Login

**Problem**: Blank page or loading forever

**Solutions**:
1. **Browser Compatibility**
   - Use Chrome, Firefox, Safari, or Edge
   - Update your browser to latest version
   - Enable JavaScript in browser settings

2. **Network Issues**
   - Check internet connection
   - Try accessing from different network
   - Contact IT about firewall settings

---

## 💾 Data Entry Problems

### Form Won't Save / Validation Errors

**Problem**: "Required field" errors or form won't submit

**Solutions**:
1. **Check Required Fields**
   - Look for red asterisks (*) next to field names
   - Ensure all mandatory fields are filled
   - Check for proper email format
   - Verify phone number format

2. **Field-Specific Issues**
   - **Email**: Must include @ and domain (e.g., <EMAIL>)
   - **Phone**: Include country code (+91 for India)
   - **Dates**: Use proper date format (YYYY-MM-DD)
   - **Numbers**: Don't include currency symbols or commas

3. **Form Behavior**
   - Scroll up to see error messages
   - Try filling fields in order
   - Don't use special characters in names

### Data Not Appearing

**Problem**: Information you entered doesn't show up

**Solutions**:
1. **Refresh and Wait**
   - Refresh the page
   - Wait a few seconds for data to sync
   - Check if you're on the correct page

2. **Search and Filter**
   - Use search function to find your data
   - Check if filters are hiding your data
   - Look in different sections (customers vs leads)

---

## 📞 Service Call Issues

### Timer Not Working

**Problem**: Timer doesn't start, stop, or shows wrong time

**Solutions**:
1. **Status Check**
   - Timer only works when status is "In Progress"
   - Change status to start timer
   - Ensure you have permission to modify service calls

2. **Browser Issues**
   - Refresh the page
   - Check if multiple tabs are open
   - Clear browser cache

3. **Manual Time Entry**
   - Contact administrator to adjust time manually
   - Document actual time worked for correction

### Cannot Update Service Call Status

**Problem**: Status dropdown is disabled or changes don't save

**Solutions**:
1. **Permission Check**
   - Verify you have permission to update service calls
   - Check if service call is assigned to you
   - Contact administrator about access rights

2. **Service Call State**
   - Completed service calls may be locked
   - Check if someone else is editing the same record
   - Refresh page and try again

---

## 👥 Customer Management Issues

### Duplicate Customer Warning

**Problem**: System says customer already exists

**Solutions**:
1. **Search First**
   - Use search function to find existing customer
   - Check different spellings of company name
   - Look for similar Tally serial numbers

2. **Verify Information**
   - Confirm this is actually a new customer
   - Check if it's a different branch/location
   - Contact administrator if you're sure it's new

### Contact Information Not Saving

**Problem**: Phone numbers or emails won't save properly

**Solutions**:
1. **Format Check**
   - Phone: Include country code (+91 9876543210)
   - Email: Must be valid format (<EMAIL>)
   - Remove extra spaces or special characters

2. **Field Limits**
   - Check character limits for each field
   - Don't exceed maximum allowed length
   - Use standard formats only

---

## 📊 Reports and Data Issues

### Report Not Generating

**Problem**: Report shows no data or won't load

**Solutions**:
1. **Date Range**
   - Check if date range includes relevant data
   - Expand date range to include more records
   - Ensure start date is before end date

2. **Filters**
   - Remove or adjust filters
   - Check if selected criteria match existing data
   - Try generating without filters first

### Export Not Working

**Problem**: Cannot download Excel or PDF files

**Solutions**:
1. **Browser Settings**
   - Check if downloads are blocked
   - Allow pop-ups for TallyCRM site
   - Check download folder permissions

2. **File Size**
   - Large reports may take time to generate
   - Try smaller date ranges
   - Contact administrator for large exports

---

## 🔔 Notification Issues

### Not Receiving Email Notifications

**Problem**: Missing email alerts for service calls or leads

**Solutions**:
1. **Email Settings**
   - Check spam/junk folder
   - Verify email address in profile
   - Ensure notifications are enabled in settings

2. **System Settings**
   - Contact administrator about email server
   - Check if notifications are enabled system-wide
   - Verify your role has notification permissions

### WhatsApp Notifications Not Working

**Problem**: Not receiving WhatsApp messages

**Solutions**:
1. **Phone Number**
   - Verify phone number format in profile
   - Include country code correctly
   - Ensure WhatsApp is installed and active

2. **System Configuration**
   - Contact administrator about WhatsApp setup
   - Check if feature is enabled for your account
   - Verify business WhatsApp account is active

---

## 📱 Mobile Device Issues

### Layout Problems on Mobile

**Problem**: Pages don't display correctly on phone/tablet

**Solutions**:
1. **Browser and Orientation**
   - Use landscape mode for better experience
   - Try different mobile browser
   - Update browser to latest version

2. **Display Settings**
   - Adjust zoom level (100% recommended)
   - Clear mobile browser cache
   - Restart browser app

### Touch/Click Issues

**Problem**: Buttons or links don't respond to touch

**Solutions**:
1. **Touch Accuracy**
   - Tap directly on buttons/links
   - Avoid accidental multi-touch
   - Try using stylus if available

2. **Browser Issues**
   - Close other browser tabs
   - Restart browser app
   - Clear cache and cookies

---

## 🔧 Performance Issues

### Slow Loading Pages

**Problem**: Pages take long time to load

**Solutions**:
1. **Internet Connection**
   - Check internet speed
   - Try different network (WiFi vs mobile data)
   - Close other bandwidth-heavy applications

2. **Browser Optimization**
   - Close unnecessary browser tabs
   - Clear browser cache
   - Disable browser extensions temporarily

### System Freezing or Hanging

**Problem**: System becomes unresponsive

**Solutions**:
1. **Immediate Actions**
   - Wait 30 seconds before taking action
   - Refresh page (F5 or Ctrl+R)
   - Close and reopen browser

2. **Prevention**
   - Don't open multiple TallyCRM tabs
   - Save work frequently
   - Log out properly when finished

---

## 🆘 When to Contact Support

### Contact Your Administrator When:
- ✅ Login issues persist after trying solutions
- ✅ Data appears to be lost or corrupted
- ✅ System-wide features aren't working
- ✅ You need additional permissions or access
- ✅ Multiple users report the same issue

### Information to Provide:
1. **Problem Description**
   - What you were trying to do
   - What happened instead
   - Error messages (take screenshots)

2. **System Information**
   - Browser name and version
   - Operating system
   - Time when problem occurred
   - Steps to reproduce the issue

3. **User Details**
   - Your username (never share password)
   - Your role/department
   - Which features you were using

---

## 📋 Prevention Tips

### Best Practices
1. **Regular Habits**
   - Log out properly when finished
   - Save work frequently
   - Keep browser updated
   - Use bookmarks for quick access

2. **Data Management**
   - Enter complete information
   - Use consistent naming conventions
   - Verify data before saving
   - Regular data cleanup

3. **Security**
   - Don't share login credentials
   - Lock screen when away
   - Report suspicious activity
   - Use strong passwords

---

## 🎯 Quick Reference

### Emergency Contacts
- **System Administrator**: [Your internal IT contact]
- **Department Manager**: [Your supervisor]
- **Help Desk**: [Internal support number]

### Useful Keyboard Shortcuts
- **Ctrl+R**: Refresh page
- **Ctrl+F**: Search on page
- **Ctrl+S**: Save (where applicable)
- **F5**: Refresh page
- **Ctrl+Shift+Delete**: Clear browser data

### Browser Requirements
- **Recommended**: Chrome, Firefox, Safari, Edge
- **JavaScript**: Must be enabled
- **Cookies**: Must be enabled
- **Pop-ups**: Allow for TallyCRM domain

---

*Remember: Most issues can be resolved with simple solutions. Don't hesitate to try the basic troubleshooting steps before contacting support.*
