import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Add online_call_type_id column to service_calls table
  await queryInterface.addColumn('service_calls', 'online_call_type_id', {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'online_call_types',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL',
    comment: 'Reference to OnlineCallType for online service calls',
  });

  // Add index for performance
  await queryInterface.addIndex('service_calls', ['online_call_type_id']);
};

export const down = async (queryInterface) => {
  // Remove index first
  await queryInterface.removeIndex('service_calls', ['online_call_type_id']);
  
  // Remove column
  await queryInterface.removeColumn('service_calls', 'online_call_type_id');
};
