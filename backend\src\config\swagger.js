import appConfig from '../../config/app.js';

/**
 * Swagger/OpenAPI 3.0 Configuration for TallyCRM Backend API
 */
export const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'TallyCRM Backend API',
    version: appConfig.app.version,
    description: `
      Comprehensive REST API for TallyCRM - A SaaS CRM solution for Tally Resellers.
      
      ## Features
      - Customer Management with Professional Contacts
      - Service Call Tracking with Timer Functionality
      - Master Data Management
      - Dashboard Analytics
      - Real-time Notifications
      - File Upload Support (TDL & Addons)
      - Multi-tenant Architecture
      
      ## Authentication
      All endpoints (except authentication) require a valid JWT token in the Authorization header.
      
      ## Rate Limiting
      API requests are limited to ${appConfig.api.rateLimit} requests per 15 minutes per IP address.
    `,
    contact: {
      name: 'Cloudstier Solutions',
      email: '<EMAIL>',
      url: 'https://cloudstier.com'
    },
    license: {
      name: 'Proprietary',
      url: 'https://cloudstier.com/license'
    }
  },
  servers: [
    {
      url: `${appConfig.app.url}${appConfig.api.prefix}`,
      description: 'Development Server'
    },
    {
      url: `https://api.tallycrm.com${appConfig.api.prefix}`,
      description: 'Production Server'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token obtained from /auth/login endpoint'
      }
    },
    schemas: {
      // Common Response Schemas
      SuccessResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully'
          },
          data: {
            type: 'object',
            description: 'Response data (varies by endpoint)'
          }
        }
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          message: {
            type: 'string',
            example: 'An error occurred'
          },
          errors: {
            type: 'object',
            description: 'Validation errors or error details'
          }
        }
      },
      PaginationMeta: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1
          },
          limit: {
            type: 'integer',
            example: 10
          },
          total: {
            type: 'integer',
            example: 100
          },
          totalPages: {
            type: 'integer',
            example: 10
          }
        }
      },
      PaginatedResponse: {
        allOf: [
          { $ref: '#/components/schemas/SuccessResponse' },
          {
            type: 'object',
            properties: {
              pagination: {
                $ref: '#/components/schemas/PaginationMeta'
              }
            }
          }
        ]
      },
      // Authentication Schemas
      LoginRequest: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>'
          },
          password: {
            type: 'string',
            minLength: 6,
            example: 'password123'
          }
        }
      },
      LoginResponse: {
        allOf: [
          { $ref: '#/components/schemas/SuccessResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  user: {
                    $ref: '#/components/schemas/User'
                  },
                  accessToken: {
                    type: 'string',
                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                  },
                  refreshToken: {
                    type: 'string',
                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                  }
                }
              }
            }
          }
        ]
      },
      // User Schema
      User: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            example: '123e4567-e89b-12d3-a456-************'
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>'
          },
          first_name: {
            type: 'string',
            example: 'John'
          },
          last_name: {
            type: 'string',
            example: 'Doe'
          },
          is_active: {
            type: 'boolean',
            example: true
          },
          tenant_id: {
            type: 'string',
            format: 'uuid'
          },
          roles: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Role'
            }
          },
          created_at: {
            type: 'string',
            format: 'date-time'
          },
          updated_at: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      Role: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid'
          },
          name: {
            type: 'string',
            example: 'admin'
          },
          permissions: {
            type: 'array',
            items: {
              type: 'string',
              example: 'customers.read'
            }
          }
        }
      },
      // Customer Schemas
      Customer: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            example: '123e4567-e89b-12d3-a456-************'
          },
          company_name: {
            type: 'string',
            example: 'ABC Enterprises Pvt Ltd',
            nullable: true
          },
          tally_serial_number: {
            type: 'string',
            example: 'TSN001',
            description: 'Unique Tally Serial Number'
          },
          contact_person: {
            type: 'string',
            example: 'John Doe'
          },
          mobile_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            nullable: true
          },
          address: {
            type: 'string',
            example: '123 Business Street, City, State - 123456',
            nullable: true
          },
          gst_number: {
            type: 'string',
            example: '29ABCDE1234F1Z5',
            nullable: true
          },
          area_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          executive_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          status: {
            type: 'string',
            enum: ['Active', 'Inactive', 'Suspended'],
            example: 'Active'
          },
          created_at: {
            type: 'string',
            format: 'date-time'
          },
          updated_at: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      CustomerCreateRequest: {
        type: 'object',
        required: ['tally_serial_number', 'contact_person', 'mobile_number'],
        properties: {
          company_name: {
            type: 'string',
            example: 'ABC Enterprises Pvt Ltd',
            nullable: true
          },
          tally_serial_number: {
            type: 'string',
            example: 'TSN001'
          },
          contact_person: {
            type: 'string',
            example: 'John Doe'
          },
          mobile_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            nullable: true
          },
          address: {
            type: 'string',
            example: '123 Business Street, City, State - 123456',
            nullable: true
          },
          gst_number: {
            type: 'string',
            example: '29ABCDE1234F1Z5',
            nullable: true
          },
          area_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          executive_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          status: {
            type: 'string',
            enum: ['Active', 'Inactive', 'Suspended'],
            example: 'Active'
          }
        }
      },
      CustomerUpdateRequest: {
        type: 'object',
        properties: {
          company_name: {
            type: 'string',
            example: 'ABC Enterprises Pvt Ltd',
            nullable: true
          },
          contact_person: {
            type: 'string',
            example: 'John Doe'
          },
          mobile_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
            nullable: true
          },
          address: {
            type: 'string',
            example: '123 Business Street, City, State - 123456',
            nullable: true
          },
          gst_number: {
            type: 'string',
            example: '29ABCDE1234F1Z5',
            nullable: true
          },
          area_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          executive_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          status: {
            type: 'string',
            enum: ['Active', 'Inactive', 'Suspended'],
            example: 'Active'
          }
        }
      },
      CustomerStats: {
        type: 'object',
        properties: {
          total_customers: {
            type: 'integer',
            example: 150
          },
          active_customers: {
            type: 'integer',
            example: 140
          },
          inactive_customers: {
            type: 'integer',
            example: 10
          },
          new_this_month: {
            type: 'integer',
            example: 5
          }
        }
      },
      // Service Call Schemas
      ServiceCall: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            example: '123e4567-e89b-12d3-a456-************'
          },
          service_number: {
            type: 'string',
            example: 'SER-001',
            description: 'Auto-generated service number'
          },
          customer_id: {
            type: 'string',
            format: 'uuid'
          },
          customer_name: {
            type: 'string',
            example: 'ABC Enterprises Pvt Ltd'
          },
          contact_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          call_type: {
            type: 'string',
            enum: ['Free Call', 'AMC Call', 'Paid Call'],
            example: 'AMC Call'
          },
          status: {
            type: 'string',
            example: 'Open'
          },
          subject: {
            type: 'string',
            example: 'Tally installation issue',
            nullable: true
          },
          description: {
            type: 'string',
            example: 'Customer facing issues with Tally installation on new system',
            nullable: true
          },
          scheduled_date: {
            type: 'string',
            format: 'date',
            example: '2024-01-15',
            nullable: true
          },
          products_issues_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          },
          timer_status: {
            type: 'string',
            enum: ['stopped', 'running', 'paused'],
            example: 'stopped'
          },
          total_time_seconds: {
            type: 'integer',
            example: 3600,
            description: 'Total accumulated time in seconds'
          },
          time_tracking_history: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                action: {
                  type: 'string',
                  example: 'started'
                },
                timestamp: {
                  type: 'string',
                  format: 'date-time'
                },
                duration: {
                  type: 'integer',
                  example: 1800
                }
              }
            }
          },
          created_at: {
            type: 'string',
            format: 'date-time'
          },
          updated_at: {
            type: 'string',
            format: 'date-time'
          }
        }
      },
      ServiceCallCreateRequest: {
        type: 'object',
        required: ['customer_id', 'contact_number', 'call_type'],
        properties: {
          customer_id: {
            type: 'string',
            format: 'uuid'
          },
          contact_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          call_type: {
            type: 'string',
            enum: ['Free Call', 'AMC Call', 'Paid Call'],
            example: 'AMC Call'
          },
          status: {
            type: 'string',
            example: 'Open'
          },
          subject: {
            type: 'string',
            example: 'Tally installation issue',
            nullable: true
          },
          description: {
            type: 'string',
            example: 'Customer facing issues with Tally installation on new system',
            nullable: true
          },
          scheduled_date: {
            type: 'string',
            format: 'date',
            example: '2024-01-15',
            nullable: true
          },
          products_issues_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          }
        }
      },
      ServiceCallUpdateRequest: {
        type: 'object',
        properties: {
          contact_number: {
            type: 'string',
            example: '+91-9876543210'
          },
          call_type: {
            type: 'string',
            enum: ['Free Call', 'AMC Call', 'Paid Call'],
            example: 'AMC Call'
          },
          status: {
            type: 'string',
            example: 'In Progress'
          },
          subject: {
            type: 'string',
            example: 'Tally installation issue',
            nullable: true
          },
          description: {
            type: 'string',
            example: 'Customer facing issues with Tally installation on new system',
            nullable: true
          },
          scheduled_date: {
            type: 'string',
            format: 'date',
            example: '2024-01-15',
            nullable: true
          },
          products_issues_id: {
            type: 'string',
            format: 'uuid',
            nullable: true
          }
        }
      },
      TimerStatus: {
        type: 'object',
        properties: {
          service_call_id: {
            type: 'string',
            format: 'uuid'
          },
          timer_status: {
            type: 'string',
            enum: ['stopped', 'running', 'paused'],
            example: 'running'
          },
          total_time_seconds: {
            type: 'integer',
            example: 3600
          },
          formatted_time: {
            type: 'string',
            example: '1 hour 0 minutes 0 seconds'
          },
          current_session_start: {
            type: 'string',
            format: 'date-time',
            nullable: true
          }
        }
      }
    },
    responses: {
      200: {
        description: 'Success',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/SuccessResponse'
            }
          }
        }
      },
      201: {
        description: 'Created',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/SuccessResponse'
            }
          }
        }
      },
      400: {
        description: 'Bad Request',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Validation failed',
              errors: {
                email: 'Email is required',
                password: 'Password must be at least 6 characters'
              }
            }
          }
        }
      },
      401: {
        description: 'Unauthorized',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Authentication required',
              errors: {}
            }
          }
        }
      },
      403: {
        description: 'Forbidden',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Insufficient permissions',
              errors: {}
            }
          }
        }
      },
      404: {
        description: 'Not Found',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Resource not found',
              errors: {}
            }
          }
        }
      },
      422: {
        description: 'Unprocessable Entity',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Validation failed',
              errors: {
                field: 'Field validation error message'
              }
            }
          }
        }
      },
      500: {
        description: 'Internal Server Error',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/ErrorResponse'
            },
            example: {
              success: false,
              message: 'Internal server error',
              errors: {}
            }
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ],
  tags: [
    {
      name: 'Health',
      description: 'Health check endpoints'
    },
    {
      name: 'Authentication',
      description: 'User authentication and authorization'
    },
    {
      name: 'Customers',
      description: 'Customer management operations'
    },
    {
      name: 'Service Calls',
      description: 'Service call tracking and timer functionality'
    },
    {
      name: 'Master Data',
      description: 'Master data management (call statuses, products, etc.)'
    },
    {
      name: 'Dashboard',
      description: 'Dashboard analytics and statistics'
    },
    {
      name: 'Notifications',
      description: 'Notification settings and testing'
    },
    {
      name: 'File Upload',
      description: 'File upload operations for TDL & Addons'
    },
    {
      name: 'Batch Operations',
      description: 'Batch API operations for performance optimization'
    }
  ]
};

export default swaggerDefinition;
