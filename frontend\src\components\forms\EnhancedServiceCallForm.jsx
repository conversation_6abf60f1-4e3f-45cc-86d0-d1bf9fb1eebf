import React, { useState, useEffect } from 'react';
import { validateServiceCallForm, validateField, formatInput } from '../../utils/validation';
import { frontendValidations } from '../../config/validationConfig';
import { serviceCallAPI } from '../../services/api';

const EnhancedServiceCallForm = ({ onSubmit, initialData = {}, isEdit = false }) => {
  const [formData, setFormData] = useState({
    customerSerialNumber: '',
    subject: '',
    description: '',
    serviceNumber: '',
    contactPersonId: '',
    callType: '',
    priority: 'medium',
    scheduledDate: '',
    estimatedHours: '',
    ...initialData
  });

  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [loading, setLoading] = useState(false);
  const [customerInfo, setCustomerInfo] = useState(null);

  // Real-time validation for specific fields
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Format input based on field type
    let formattedValue = value;
    if (name === 'customerSerialNumber') {
      formattedValue = formatInput.customerSerial(value);
    }

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Real-time validation for specific fields
    if (['customerSerialNumber', 'subject', 'description'].includes(name)) {
      const validationConfig = frontendValidations.serviceCall[name];
      const validation = validateField(name, formattedValue, validationConfig);

      setErrors(prev => ({
        ...prev,
        [name]: validation.isValid ? '' : validation.message
      }));
    }

    // Auto-fetch customer info when serial number is entered
    if (name === 'customerSerialNumber' && formattedValue.length >= 3) {
      fetchCustomerInfo(formattedValue);
    } else if (name === 'customerSerialNumber' && formattedValue.length < 3) {
      setCustomerInfo(null);
    }
  };

  // Fetch customer information by serial number
  const fetchCustomerInfo = async (serialNumber) => {
    try {
      console.log('Fetching customer info for serial number:', serialNumber);
      const response = await serviceCallAPI.getCustomerBySerial(serialNumber);
      console.log('Customer lookup response:', response);

      if (response.data.success) {
        setCustomerInfo(response.data.data.customer);
        // Clear customer serial number error if customer found
        setErrors(prev => ({
          ...prev,
          customerSerialNumber: ''
        }));
        console.log('Customer found:', response.data.data.customer);
      }
    } catch (error) {
      console.error('Customer lookup error:', error);
      if (error.response?.status === 404) {
        setCustomerInfo(null);
        setErrors(prev => ({
          ...prev,
          customerSerialNumber: `Customer not found with serial number: ${serialNumber}`
        }));
      } else if (error.response?.status === 401) {
        setErrors(prev => ({
          ...prev,
          customerSerialNumber: 'Authentication required. Please log in again.'
        }));
      } else {
        console.error('Unexpected error during customer lookup:', error);
        setErrors(prev => ({
          ...prev,
          customerSerialNumber: 'Error looking up customer. Please try again.'
        }));
      }
    }
  };

  // Handle field blur for validation
  const handleBlur = (e) => {
    const { name, value } = e.target;

    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    const validationConfig = frontendValidations.serviceCall[name];
    if (validationConfig) {
      const validation = validateField(name, value, validationConfig);
      setErrors(prev => ({
        ...prev,
        [name]: validation.isValid ? '' : validation.message
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate entire form
      const validation = validateServiceCallForm(formData);

      if (!validation.isValid) {
        setErrors(validation.errors);
        setLoading(false);
        return;
      }

      // Clear errors
      setErrors({});

      // Prepare data for submission
      const submitData = {
        customer_serial_number: formData.customerSerialNumber,
        subject: formData.subject || null,
        description: formData.description || null,
        call_number: formData.serviceNumber || null,
        contact_person_id: formData.contactPersonId || null,
        call_type: formData.callType || null,
        priority: formData.priority || 'medium',
        scheduled_date: formData.scheduledDate || null,
        estimated_hours: formData.estimatedHours ? parseFloat(formData.estimatedHours) : null
      };

      // Submit to API
      console.log('Submitting service call data:', submitData);
      const response = await serviceCallAPI.create(submitData);
      console.log('Service call creation response:', response);

      if (response.data.success) {
        // Call parent submit handler if provided
        if (onSubmit) {
          await onSubmit(response.data);
        }

        // Show success message
        alert('Service call created successfully!');

        // Reset form
        setFormData({
          customerSerialNumber: '',
          subject: '',
          description: '',
          serviceNumber: '',
          contactPersonId: '',
          callType: '',
          priority: 'medium',
          scheduledDate: '',
          estimatedHours: ''
        });
        setCustomerInfo(null);
      }

    } catch (error) {
      console.error('Form submission error:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');

      // Check for auth errors first
      if (ErrorHandler.handleAuthError(error)) {
        setErrors({
          general: 'Your session has expired. Please log in again.'
        });
        return;
      }

      // Handle form validation errors using the enhanced error handler
      const hasFieldErrors = ErrorHandler.handleFormErrors(error, setErrors);

      // If no field-specific errors were handled, show general error
      if (!hasFieldErrors) {
        const { message } = ErrorHandler.parseApiError(error);
        setErrors({ general: message });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="enhanced-service-call-form">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* General Error Message */}
        {errors.general && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {errors.general}
          </div>
        )}

        {/* Customer Serial Number */}
        <div>
          <label htmlFor="customerSerialNumber" className="block text-sm font-medium text-gray-700 mb-1">
            Customer Serial Number *
          </label>
          <input
            type="text"
            id="customerSerialNumber"
            name="customerSerialNumber"
            value={formData.customerSerialNumber}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.customerSerialNumber ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter customer serial number"
            maxLength={20}
          />
          {errors.customerSerialNumber && (
            <p className="mt-1 text-sm text-red-600">{errors.customerSerialNumber}</p>
          )}

          {/* Customer Info Display */}
          {customerInfo && (
            <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded">
              <p className="text-sm text-green-800">
                <strong>Customer Found:</strong> {customerInfo.company_name}
              </p>
              {customerInfo.area && (
                <p className="text-sm text-green-700">
                  Location: {customerInfo.area.name}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Subject */}
        <div>
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
            Subject
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.subject ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter subject (optional)"
            maxLength={200}
          />
          {errors.subject && (
            <p className="mt-1 text-sm text-red-600">{errors.subject}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            {formData.subject.length}/200 characters
          </p>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            onBlur={handleBlur}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter description (optional)"
            maxLength={2000}
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600">{errors.description}</p>
          )}
          <p className="mt-1 text-sm text-gray-500">
            {formData.description.length}/2000 characters
          </p>
        </div>

        {/* Service Number */}
        <div>
          <label htmlFor="serviceNumber" className="block text-sm font-medium text-gray-700 mb-1">
            Service Number
          </label>
          <input
            type="text"
            id="serviceNumber"
            name="serviceNumber"
            value={formData.serviceNumber}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.serviceNumber ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="e.g., SER-001, SE-003S (optional)"
            maxLength={20}
          />
          {errors.serviceNumber && (
            <p className="mt-1 text-sm text-red-600">{errors.serviceNumber}</p>
          )}
        </div>

        {/* Call Type */}
        <div>
          <label htmlFor="callType" className="block text-sm font-medium text-gray-700 mb-1">
            Call Type
          </label>
          <select
            id="callType"
            name="callType"
            value={formData.callType}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select call type</option>
            <option value="online">Online</option>
            <option value="onsite">Onsite</option>
            <option value="phone">Phone</option>
            <option value="email">Email</option>
          </select>
        </div>

        {/* Priority */}
        <div>
          <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <select
            id="priority"
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>

        {/* Scheduled Date */}
        <div>
          <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
            Scheduled Date
          </label>
          <input
            type="datetime-local"
            id="scheduledDate"
            name="scheduledDate"
            value={formData.scheduledDate}
            onChange={handleInputChange}
            onBlur={handleBlur}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.scheduledDate ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.scheduledDate && (
            <p className="mt-1 text-sm text-red-600">{errors.scheduledDate}</p>
          )}
        </div>

        {/* Estimated Hours */}
        <div>
          <label htmlFor="estimatedHours" className="block text-sm font-medium text-gray-700 mb-1">
            Estimated Hours
          </label>
          <input
            type="number"
            id="estimatedHours"
            name="estimatedHours"
            value={formData.estimatedHours}
            onChange={handleInputChange}
            onBlur={handleBlur}
            min="0"
            max="24"
            step="0.5"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.estimatedHours ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter estimated hours"
          />
          {errors.estimatedHours && (
            <p className="mt-1 text-sm text-red-600">{errors.estimatedHours}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            onClick={() => window.history.back()}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Submitting...' : (isEdit ? 'Update Service Call' : 'Create Service Call')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EnhancedServiceCallForm;
