// Simple authentication test
import fetch from 'node-fetch';

async function testAuth() {
  try {
    console.log('Testing authentication...');
    
    const response = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    console.log('Response status:', response.status);
    const data = await response.text();
    console.log('Response data:', data);
    
    if (response.ok) {
      const jsonData = JSON.parse(data);
      console.log('Token:', jsonData.token);
      console.log('User:', jsonData.user);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testAuth();
