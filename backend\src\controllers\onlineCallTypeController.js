import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all online call types
 */
export const getOnlineCallTypes = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      page = 1,
      limit = 50,
      search = '',
      category = '',
      isActive = '',
      sortBy = 'category',
      sortOrder = 'asc',
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const where = { tenant_id: tenantId };

    // Apply filters
    if (search) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { category: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (category) {
      where.category = category;
    }

    if (isActive !== '') {
      where.is_active = isActive === 'true';
    }

    const { count, rows: onlineCallTypes } = await models.OnlineCallType.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        onlineCallTypes,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get online call types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch online call types',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get online call type by ID
 */
export const getOnlineCallTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const onlineCallType = await models.OnlineCallType.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!onlineCallType) {
      return res.status(404).json({
        success: false,
        message: 'Online call type not found',
      });
    }

    res.json({
      success: true,
      data: { onlineCallType },
    });

  } catch (error) {
    logger.error('Get online call type by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch online call type',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new online call type
 */
export const createOnlineCallType = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { name, category, description, is_active = true, sort_order } = req.body;

    // Check if name already exists for this tenant
    const existingCallType = await models.OnlineCallType.findOne({
      where: {
        name,
        tenant_id: tenantId,
      },
    });

    if (existingCallType) {
      return res.status(400).json({
        success: false,
        message: 'Online call type with this name already exists',
      });
    }

    const onlineCallType = await models.OnlineCallType.create({
      name,
      category,
      description,
      is_active,
      sort_order,
      tenant_id: tenantId,
    });

    logger.info('Online call type created:', {
      id: onlineCallType.id,
      name: onlineCallType.name,
      category: onlineCallType.category,
      tenantId,
      userId: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: 'Online call type created successfully',
      data: { onlineCallType },
    });

  } catch (error) {
    logger.error('Create online call type error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create online call type',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update online call type
 */
export const updateOnlineCallType = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;
    const { name, category, description, is_active, sort_order } = req.body;

    const onlineCallType = await models.OnlineCallType.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!onlineCallType) {
      return res.status(404).json({
        success: false,
        message: 'Online call type not found',
      });
    }

    // Check if name already exists for this tenant (excluding current record)
    if (name && name !== onlineCallType.name) {
      const existingCallType = await models.OnlineCallType.findOne({
        where: {
          name,
          tenant_id: tenantId,
          id: { [Op.ne]: id },
        },
      });

      if (existingCallType) {
        return res.status(400).json({
          success: false,
          message: 'Online call type with this name already exists',
        });
      }
    }

    await onlineCallType.update({
      name: name || onlineCallType.name,
      category: category !== undefined ? category : onlineCallType.category,
      description: description !== undefined ? description : onlineCallType.description,
      is_active: is_active !== undefined ? is_active : onlineCallType.is_active,
      sort_order: sort_order !== undefined ? sort_order : onlineCallType.sort_order,
    });

    logger.info('Online call type updated:', {
      id: onlineCallType.id,
      name: onlineCallType.name,
      category: onlineCallType.category,
      tenantId,
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Online call type updated successfully',
      data: { onlineCallType },
    });

  } catch (error) {
    logger.error('Update online call type error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update online call type',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete online call type (soft delete)
 */
export const deleteOnlineCallType = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const onlineCallType = await models.OnlineCallType.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!onlineCallType) {
      return res.status(404).json({
        success: false,
        message: 'Online call type not found',
      });
    }

    // Check if call type is being used by any service calls
    const serviceCallsCount = await models.ServiceCall.count({
      where: {
        online_call_type_id: id,
        tenant_id: tenantId,
      },
    });

    if (serviceCallsCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete online call type. It is being used by ${serviceCallsCount} service call(s).`,
      });
    }

    // Soft delete by setting is_active to false
    await onlineCallType.update({ is_active: false });

    logger.info('Online call type deleted:', {
      id: onlineCallType.id,
      name: onlineCallType.name,
      tenantId,
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: 'Online call type deleted successfully',
    });

  } catch (error) {
    logger.error('Delete online call type error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete online call type',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Search online call types for dropdown
 */
export const searchOnlineCallTypes = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { q = '', category = '', limit = 20 } = req.query;

    const where = {
      tenant_id: tenantId,
      is_active: true,
    };

    if (q) {
      where[Op.or] = [
        { name: { [Op.iLike]: `%${q}%` } },
        { category: { [Op.iLike]: `%${q}%` } },
      ];
    }

    if (category) {
      where.category = category;
    }

    const onlineCallTypes = await models.OnlineCallType.findAll({
      where,
      limit: parseInt(limit),
      order: [['category', 'ASC'], ['sort_order', 'ASC'], ['name', 'ASC']],
      attributes: ['id', 'name', 'category', 'description'],
    });

    res.json({
      success: true,
      data: onlineCallTypes,
    });

  } catch (error) {
    logger.error('Search online call types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search online call types',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get categories
 */
export const getCategories = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const categories = await models.OnlineCallType.getCategories(tenantId);

    res.json({
      success: true,
      data: categories,
    });

  } catch (error) {
    logger.error('Get categories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Bulk update status
 */
export const bulkUpdateStatus = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { ids, is_active } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide valid IDs array',
      });
    }

    const [updatedCount] = await models.OnlineCallType.bulkUpdateStatus(ids, is_active, tenantId);

    logger.info('Bulk status update:', {
      updatedCount,
      is_active,
      tenantId,
      userId: req.user.id,
    });

    res.json({
      success: true,
      message: `${updatedCount} online call type(s) updated successfully`,
      data: { updatedCount },
    });

  } catch (error) {
    logger.error('Bulk update status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update online call types',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
