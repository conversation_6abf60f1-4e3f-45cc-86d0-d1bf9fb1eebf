import React, { useState } from 'react';
import { FaCalendarAlt, FaTimes } from 'react-icons/fa';

const DateRangeFilter = ({ onDateRangeChange, label = "Date Range" }) => {
  const [selectedRange, setSelectedRange] = useState('all');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [showCustomRange, setShowCustomRange] = useState(false);

  // Predefined date ranges
  const dateRanges = [
    { value: 'all', label: 'All Time' },
    { value: 'last7days', label: 'Last 7 Days' },
    { value: 'last30days', label: 'Last 30 Days' },
    { value: 'lastyear', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  // Calculate date ranges
  const getDateRange = (range) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (range) {
      case 'last7days':
        const last7Days = new Date(today);
        last7Days.setDate(today.getDate() - 7);
        return { startDate: last7Days, endDate: today };
      
      case 'last30days':
        const last30Days = new Date(today);
        last30Days.setDate(today.getDate() - 30);
        return { startDate: last30Days, endDate: today };
      
      case 'lastyear':
        const lastYear = new Date(today);
        lastYear.setFullYear(today.getFullYear() - 1);
        return { startDate: lastYear, endDate: today };
      
      case 'custom':
        if (customStartDate && customEndDate) {
          return { 
            startDate: new Date(customStartDate), 
            endDate: new Date(customEndDate) 
          };
        }
        return null;
      
      default:
        return null; // All time
    }
  };

  const handleRangeChange = (range) => {
    setSelectedRange(range);
    
    if (range === 'custom') {
      setShowCustomRange(true);
      // Don't trigger change until custom dates are set
      return;
    } else {
      setShowCustomRange(false);
      setCustomStartDate('');
      setCustomEndDate('');
    }

    const dateRange = getDateRange(range);
    onDateRangeChange(dateRange);
  };

  const handleCustomDateChange = () => {
    if (customStartDate && customEndDate) {
      const dateRange = getDateRange('custom');
      onDateRangeChange(dateRange);
    }
  };

  const clearCustomDates = () => {
    setCustomStartDate('');
    setCustomEndDate('');
    setSelectedRange('all');
    setShowCustomRange(false);
    onDateRangeChange(null);
  };

  const formatDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString();
  };

  const getDisplayText = () => {
    if (selectedRange === 'all') return 'All Time';
    if (selectedRange === 'custom' && customStartDate && customEndDate) {
      return `${formatDate(new Date(customStartDate))} - ${formatDate(new Date(customEndDate))}`;
    }
    
    const range = dateRanges.find(r => r.value === selectedRange);
    return range ? range.label : 'All Time';
  };

  return (
    <div className="relative date-range-picker" style={{ zIndex: 5000 }}>
      {/* Main Filter Button */}
      <div className="relative">
        <button
          type="button"
          className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          onClick={() => setShowCustomRange(!showCustomRange)}
        >
          <FaCalendarAlt className="mr-2 h-4 w-4 text-gray-400" />
          {label}: {getDisplayText()}
        </button>

        {/* Active filter indicator */}
        {selectedRange !== 'all' && (
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-blue-500 rounded-full"></span>
        )}
      </div>

      {/* Dropdown Menu */}
      {showCustomRange && (
        <div
          className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 dropdown-menu p-4"
          style={{ zIndex: 5001 }}
        >
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-sm font-medium text-gray-900">Select Date Range</h3>
            <button
              onClick={() => setShowCustomRange(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <FaTimes size={14} />
            </button>
          </div>

          {/* Predefined Ranges */}
          <div className="space-y-2 mb-4">
            {dateRanges.map((range) => (
              <button
                key={range.value}
                onClick={() => handleRangeChange(range.value)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                  selectedRange === range.value
                    ? 'bg-blue-100 text-blue-800 font-medium'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>

          {/* Custom Date Range Inputs */}
          {selectedRange === 'custom' && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex justify-between mt-3">
                <button
                  onClick={clearCustomDates}
                  className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
                >
                  Clear
                </button>
                <button
                  onClick={() => {
                    handleCustomDateChange();
                    setShowCustomRange(false);
                  }}
                  disabled={!customStartDate || !customEndDate}
                  className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                >
                  Apply
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;
