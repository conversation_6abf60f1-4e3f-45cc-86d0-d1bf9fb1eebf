import React from 'react';
import { cn } from '../../utils/helpers';
import { ServiceStatusBadge } from '../ui';

/**
 * Executive Workload Component
 * Displays executive assignment information and workload distribution
 */
const ExecutiveWorkload = ({
  workloadData = [],
  size = 'md',
  showDetails = true,
  className = '',
  ...props
}) => {
  if (!workloadData || workloadData.length === 0) {
    return (
      <div className={cn('text-center py-6', className)}>
        <div className="text-gray-400 text-sm">No executive assignments found</div>
      </div>
    );
  }

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'space-y-2',
      text: 'text-xs',
      avatar: 'w-6 h-6',
      badge: 'text-xs px-2 py-0.5'
    },
    md: {
      container: 'space-y-3',
      text: 'text-sm',
      avatar: 'w-8 h-8',
      badge: 'text-xs px-2 py-1'
    },
    lg: {
      container: 'space-y-4',
      text: 'text-base',
      avatar: 'w-10 h-10',
      badge: 'text-sm px-3 py-1'
    }
  };

  const config = sizeConfig[size];

  // Calculate totals
  const totalCalls = workloadData.reduce((sum, exec) => sum + exec.totalCalls, 0);
  const totalOpen = workloadData.reduce((sum, exec) => sum + exec.openCalls, 0);
  const totalOverdue = workloadData.reduce((sum, exec) => sum + exec.overdueCallsCount, 0);

  return (
    <div className={cn('w-full', config.container, className)} {...props}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h6 className={cn('font-semibold text-gray-900', config.text)}>
          Executive workload
        </h6>
        <div className="flex items-center space-x-4">
          <span className={cn('text-gray-500', config.text)}>
            {workloadData.length} executives
          </span>
          <span className={cn('text-gray-500', config.text)}>
            {totalCalls} total calls
          </span>
        </div>
      </div>

      {/* Executive List */}
      <div className="space-y-3">
        {workloadData.map((executive, index) => {
          const completionRate = executive.totalCalls > 0 
            ? Math.round((executive.completedCalls / executive.totalCalls) * 100) 
            : 0;
          
          const workloadPercentage = totalCalls > 0 
            ? Math.round((executive.totalCalls / totalCalls) * 100) 
            : 0;

          return (
            <div 
              key={executive.name || index}
              className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              {/* Executive Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className={cn(
                    'rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0',
                    config.avatar
                  )}>
                    <span className="text-blue-600 font-medium text-xs">
                      {executive.name === 'Unassigned' ? 'U' : executive.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  
                  {/* Name and Status */}
                  <div>
                    <h6 className={cn('font-medium text-gray-900', config.text)}>
                      {executive.name}
                    </h6>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={cn('text-gray-500', config.text)}>
                        {executive.totalCalls} calls
                      </span>
                      {executive.overdueCallsCount > 0 && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {executive.overdueCallsCount} overdue
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Completion Rate */}
                <div className="text-right">
                  <div className={cn('font-semibold text-gray-900', config.text)}>
                    {completionRate}%
                  </div>
                  <div className={cn('text-gray-500', config.text)}>
                    completion
                  </div>
                </div>
              </div>

              {/* Workload Details */}
              {showDetails && (
                <div className="space-y-2">
                  {/* Progress Bar */}
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className={cn('text-gray-600', config.text)}>
                        Workload distribution
                      </span>
                      <span className={cn('text-gray-500', config.text)}>
                        {workloadPercentage}% of total
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${workloadPercentage}%` }}
                      />
                    </div>
                  </div>

                  {/* Call Status Breakdown */}
                  <div className="grid grid-cols-3 gap-3 mt-3">
                    <div className="text-center">
                      <div className={cn('font-semibold text-green-600', config.text)}>
                        {executive.completedCalls}
                      </div>
                      <div className={cn('text-gray-500', config.text)}>
                        Completed
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={cn('font-semibold text-blue-600', config.text)}>
                        {executive.openCalls}
                      </div>
                      <div className={cn('text-gray-500', config.text)}>
                        Open
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={cn(
                        'font-semibold',
                        executive.overdueCallsCount > 0 ? 'text-red-600' : 'text-gray-400',
                        config.text
                      )}>
                        {executive.overdueCallsCount}
                      </div>
                      <div className={cn('text-gray-500', config.text)}>
                        Overdue
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Summary Footer */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className={cn('font-semibold text-gray-900', config.text)}>
              {totalOpen}
            </div>
            <div className={cn('text-gray-500', config.text)}>
              Total open
            </div>
          </div>
          <div>
            <div className={cn('font-semibold text-gray-900', config.text)}>
              {totalCalls - totalOpen}
            </div>
            <div className={cn('text-gray-500', config.text)}>
              Total completed
            </div>
          </div>
          <div>
            <div className={cn(
              'font-semibold',
              totalOverdue > 0 ? 'text-red-600' : 'text-green-600',
              config.text
            )}>
              {totalOverdue}
            </div>
            <div className={cn('text-gray-500', config.text)}>
              Total overdue
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Compact Executive Summary Component
 */
export const ExecutiveSummary = ({
  workloadData = [],
  className = '',
  ...props
}) => {
  if (!workloadData || workloadData.length === 0) {
    return null;
  }

  const totalCalls = workloadData.reduce((sum, exec) => sum + exec.totalCalls, 0);
  const totalOverdue = workloadData.reduce((sum, exec) => sum + exec.overdueCallsCount, 0);
  const topPerformer = workloadData.reduce((top, exec) => 
    exec.completedCalls > (top?.completedCalls || 0) ? exec : top, null
  );

  return (
    <div className={cn('bg-gray-50 rounded-lg p-4', className)} {...props}>
      <h6 className="font-medium text-gray-900 text-sm mb-3">Executive summary</h6>
      
      <div className="space-y-2 text-sm">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Active executives:</span>
          <span className="font-medium text-gray-900">{workloadData.length}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Total assignments:</span>
          <span className="font-medium text-gray-900">{totalCalls}</span>
        </div>
        
        {totalOverdue > 0 && (
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Overdue calls:</span>
            <span className="font-medium text-red-600">{totalOverdue}</span>
          </div>
        )}
        
        {topPerformer && (
          <div className="flex items-center justify-between pt-2 border-t border-gray-200">
            <span className="text-gray-600">Top performer:</span>
            <span className="font-medium text-blue-600">{topPerformer.name}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExecutiveWorkload;
