/**
 * Add Service Revenue Data
 * Script to add revenue amounts to service calls for analytics testing
 */

import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

async function addServiceRevenueData() {
  try {
    console.log('🚀 Adding service revenue data...\n');

    // Find active tenants
    const tenants = await models.Tenant.findAll({
      where: { status: 'active' },
      limit: 5
    });

    if (tenants.length === 0) {
      console.log('❌ No active tenants found');
      return;
    }

    console.log(`✅ Found ${tenants.length} active tenants`);

    for (const tenant of tenants) {
      console.log(`\n📊 Processing tenant: ${tenant.name}`);

      // Find customers for this tenant
      const customers = await models.Customer.findAll({
        where: { tenant_id: tenant.id },
        limit: 20
      });

      if (customers.length === 0) {
        console.log(`   ⚠️ No customers found for tenant ${tenant.name}`);
        continue;
      }

      // Find users for this tenant (for created_by)
      const users = await models.User.findAll({
        where: { tenant_id: tenant.id },
        limit: 5
      });

      if (users.length === 0) {
        console.log(`   ⚠️ No users found for tenant ${tenant.name}`);
        continue;
      }

      // Find executives for this tenant
      const executives = await models.Executive.findAll({
        where: { tenant_id: tenant.id },
        limit: 10
      });

      // Find service call statuses
      const statuses = await models.ServiceCallStatus.findAll({
        limit: 10
      });

      // Find call types
      const callTypes = await models.TypeOfCall.findAll({
        where: { tenant_id: tenant.id },
        limit: 5
      });

      console.log(`   📋 Found ${customers.length} customers, ${users.length} users, ${executives.length} executives`);

      // Update existing service calls with revenue data
      const existingCalls = await models.ServiceCall.findAll({
        where: { 
          tenant_id: tenant.id,
          total_amount: 0 // Only update calls without amounts
        },
        limit: 50
      });

      console.log(`   💰 Updating ${existingCalls.length} existing service calls with revenue data...`);

      for (const call of existingCalls) {
        const billingTypes = ['free_call', 'amc_call', 'per_call'];
        const billingType = billingTypes[Math.floor(Math.random() * billingTypes.length)];
        
        let serviceCharges = 0;
        let travelCharges = 0;
        let totalAmount = 0;

        // Set amounts based on billing type
        switch (billingType) {
          case 'free_call':
            serviceCharges = 0;
            travelCharges = 0;
            totalAmount = 0;
            break;
          case 'amc_call':
            serviceCharges = Math.floor(Math.random() * 2000) + 500; // 500-2500
            travelCharges = Math.floor(Math.random() * 500); // 0-500
            totalAmount = serviceCharges + travelCharges;
            break;
          case 'per_call':
            serviceCharges = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
            travelCharges = Math.floor(Math.random() * 1000); // 0-1000
            totalAmount = serviceCharges + travelCharges;
            break;
        }

        await call.update({
          call_billing_type: billingType,
          service_charges: serviceCharges,
          travel_charges: travelCharges,
          total_amount: totalAmount,
          is_billable: totalAmount > 0,
          hourly_rate: billingType === 'per_call' ? Math.floor(Math.random() * 500) + 200 : null
        });
      }

      // Create new service calls with revenue data
      const newCallsCount = Math.min(200 - existingCalls.length, 150);
      console.log(`   🆕 Creating ${newCallsCount} new service calls with revenue data...`);

      for (let i = 0; i < newCallsCount; i++) {
        const customer = customers[Math.floor(Math.random() * customers.length)];
        const user = users[Math.floor(Math.random() * users.length)];
        const executive = executives.length > 0 ? executives[Math.floor(Math.random() * executives.length)] : null;
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const callType = callTypes.length > 0 ? callTypes[Math.floor(Math.random() * callTypes.length)] : null;

        const billingTypes = ['free_call', 'amc_call', 'per_call'];
        const billingType = billingTypes[Math.floor(Math.random() * billingTypes.length)];
        
        let serviceCharges = 0;
        let travelCharges = 0;
        let totalAmount = 0;

        // Set amounts based on billing type
        switch (billingType) {
          case 'free_call':
            serviceCharges = 0;
            travelCharges = 0;
            totalAmount = 0;
            break;
          case 'amc_call':
            serviceCharges = Math.floor(Math.random() * 2000) + 500; // 500-2500
            travelCharges = Math.floor(Math.random() * 500); // 0-500
            totalAmount = serviceCharges + travelCharges;
            break;
          case 'per_call':
            serviceCharges = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
            travelCharges = Math.floor(Math.random() * 1000); // 0-1000
            totalAmount = serviceCharges + travelCharges;
            break;
        }

        // Generate call number
        const callNumber = `SC${tenant.id.slice(-4).toUpperCase()}${Date.now().toString().slice(-6)}${i.toString().padStart(3, '0')}`;

        // Random date within last 6 months
        const randomDate = new Date();
        randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 180));

        const serviceCallData = {
          tenant_id: tenant.id,
          customer_id: customer.id,
          created_by: user.id,
          assigned_to: executive?.id || null,
          call_number: callNumber,
          type_of_call_id: callType?.id || null,
          status_id: status?.id || null,
          priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
          call_type: ['online', 'onsite'][Math.floor(Math.random() * 2)],
          call_billing_type: billingType,
          service_charges: serviceCharges,
          travel_charges: travelCharges,
          total_amount: totalAmount,
          is_billable: totalAmount > 0,
          hourly_rate: billingType === 'per_call' ? Math.floor(Math.random() * 500) + 200 : null,
          problem_description: `Service call for ${customer.company_name} - ${billingType.replace('_', ' ')}`,
          solution_provided: totalAmount > 0 ? 'Service completed successfully with billing' : 'Free service provided',
          call_date: randomDate,
          scheduled_date: randomDate,
          created_at: randomDate,
          updated_at: randomDate
        };

        try {
          await models.ServiceCall.create(serviceCallData);
        } catch (error) {
          console.log(`   ⚠️ Error creating service call ${i + 1}: ${error.message}`);
        }
      }

      console.log(`   ✅ Completed processing for tenant: ${tenant.name}`);
    }

    // Generate summary statistics
    console.log('\n📊 Generating revenue summary...');
    
    for (const tenant of tenants) {
      const revenueStats = await models.ServiceCall.findAll({
        where: { tenant_id: tenant.id },
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'totalCalls'],
          [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'totalRevenue'],
          [models.sequelize.fn('AVG', models.sequelize.col('total_amount')), 'avgRevenue'],
          [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN total_amount > 0 THEN 1 END")), 'billableCalls'],
          [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'free_call' THEN 1 END")), 'freeCalls'],
          [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'amc_call' THEN 1 END")), 'amcCalls'],
          [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'per_call' THEN 1 END")), 'perCalls']
        ],
        raw: true
      });

      const stats = revenueStats[0];
      
      console.log(`\n📈 Revenue Summary for ${tenant.name}:`);
      console.log(`   💰 Total Revenue: ₹${parseFloat(stats.totalRevenue || 0).toLocaleString()}`);
      console.log(`   📞 Total Service Calls: ${stats.totalCalls}`);
      console.log(`   💵 Average Revenue per Call: ₹${parseFloat(stats.avgRevenue || 0).toFixed(2)}`);
      console.log(`   💸 Billable Calls: ${stats.billableCalls}`);
      console.log(`   🆓 Free Calls: ${stats.freeCalls}`);
      console.log(`   🔧 AMC Calls: ${stats.amcCalls}`);
      console.log(`   💳 Per Calls: ${stats.perCalls}`);
    }

    console.log('\n🎉 Service revenue data added successfully!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Refresh the analytics pages in the frontend');
    console.log('2. Check the Financial Analytics page for revenue data');
    console.log('3. Verify Service Analytics shows billing type distribution');

  } catch (error) {
    console.error('❌ Error adding service revenue data:', error);
    logger.error('Service revenue data error:', error);
  } finally {
    // Close database connection
    await models.sequelize.close();
  }
}

// Run the script
addServiceRevenueData();
