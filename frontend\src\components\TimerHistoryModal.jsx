import { useState, useEffect } from 'react';
import { FaClock, FaPlay, FaPause, FaStop, FaCheckCircle, FaTimes, FaHistory } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import api from '../services/api';
import LoadingScreen from './ui/LoadingScreen';

const TimerHistoryModal = ({ isOpen, onClose, serviceCallId, serviceCallNumber }) => {
  const [timerHistory, setTimerHistory] = useState([]);
  const [timerSummary, setTimerSummary] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleClose = (e) => {
    e?.preventDefault();
    e?.stopPropagation();
    if (onClose && typeof onClose === 'function') {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen && serviceCallId) {
      fetchTimerHistory();
    }
  }, [isOpen, serviceCallId]);

  // Handle Escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const fetchTimerHistory = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/service-calls/${serviceCallId}/timer-history`);

      if (response.data.success) {
        setTimerHistory(response.data.data.timer_history || []);
        setTimerSummary(response.data.data.timer_summary || null);
        setStatistics(response.data.data.statistics || null);
      } else {
        toast.error('Failed to fetch timer history');
      }
    } catch (error) {
      console.error('Error fetching timer history:', error);
      toast.error('Failed to fetch timer history');
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'timer_started':
        return <FaPlay className="text-black-600" />;
      case 'timer_resumed':
        return <FaPlay className="text-blue-600" />;
      case 'timer_paused':
        return <FaPause className="text-yellow-600" />;
      case 'timer_completed':
        return <FaCheckCircle className="text-black-600" />;
      case 'timer_stopped':
      case 'timer_cancelled':
        return <FaStop className="text-red-600" />;
      default:
        return <FaClock className="text-gray-600" />;
    }
  };

  const getEventColor = (eventType) => {
    switch (eventType) {
      case 'timer_started':
        return 'success';
      case 'timer_resumed':
        return 'info';
      case 'timer_paused':
        return 'warning';
      case 'timer_completed':
        return 'success';
      case 'timer_stopped':
      case 'timer_cancelled':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const formatEventType = (eventType) => {
    return eventType.replace('timer_', '').replace('_', ' ').toUpperCase();
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 timer-history-modal"
      style={{ zIndex: 60000 }}
      onClick={handleClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] min-h-[60vh] overflow-hidden mx-auto flex flex-col"
        style={{ zIndex: 60001 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FaHistory className="h-6 w-6 text-gray-900" />
            <h2 className="text-xl font-semibold text-gray-900">Timer History - {serviceCallNumber}</h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="h-6 w-6" />
          </button>
        </div>

        {/* Body */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <LoadingScreen
              title="Loading Timer History..."
              subtitle="Fetching service call timer data"
              variant="modal"
            />
          ) : (
            <div className="timer-history-content">
              {/* Timer Summary */}
              {timerSummary && (
                <div className="mx-6 mt-6 mb-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-bold text-gray-900 flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                          <FaClock className="text-blue-600" size={16} />
                        </div>
                        Timer Summary
                      </h3>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100 timer-summary-card">
                        <div className={`font-bold text-blue-600 mb-2 timer-value ${
                          timerSummary.total_time_formatted && timerSummary.total_time_formatted.length > 15
                            ? 'text-sm leading-tight'
                            : timerSummary.total_time_formatted && timerSummary.total_time_formatted.length > 10
                            ? 'text-lg'
                            : 'text-2xl'
                        }`}>
                          {timerSummary.total_time_formatted}
                        </div>
                        <div className="text-xs font-medium text-gray-600">Total Time</div>
                      </div>
                      <div className="bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100 timer-summary-card">
                        <div className={`font-bold text-green-600 mb-2 timer-value ${
                          timerSummary.current_status && timerSummary.current_status.length > 12
                            ? 'text-sm leading-tight'
                            : 'text-lg'
                        }`}>
                          {timerSummary.current_status}
                        </div>
                        <div className="text-xs font-medium text-gray-600">Current Status</div>
                      </div>
                      <div className="bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100 timer-summary-card">
                        <div className="text-2xl font-bold text-purple-600 mb-2 timer-value">{statistics?.total_sessions || 0}</div>
                        <div className="text-xs font-medium text-gray-600">Total Sessions</div>
                      </div>
                      <div className="bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100 timer-summary-card">
                        <div className="text-2xl font-bold text-orange-600 mb-2 timer-value">{statistics?.pause_events || 0}</div>
                        <div className="text-xs font-medium text-gray-600">Pause/Resume Cycles</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Event Statistics */}
              {statistics && (
                <div className="mx-6 mb-4 bg-white border border-gray-200 rounded-xl shadow-sm">
                  <div className="p-4">
                    <h3 className="text-md font-bold text-gray-900 mb-3 flex items-center">
                      <div className="w-6 h-6 bg-gray-100 rounded-lg flex items-center justify-center mr-2 text-xs">
                        📊
                      </div>
                      Event Statistics
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-full px-3 py-2 rounded-lg bg-green-50 border border-green-200">
                          <span className="text-md font-bold text-green-700 mr-1">{statistics.start_events || 0}</span>
                          <span className="text-xs font-medium text-green-600">Starts</span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-full px-3 py-2 rounded-lg bg-yellow-50 border border-yellow-200">
                          <span className="text-md font-bold text-yellow-700 mr-1">{statistics.pause_events || 0}</span>
                          <span className="text-xs font-medium text-yellow-600">Pauses</span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-full px-3 py-2 rounded-lg bg-blue-50 border border-blue-200">
                          <span className="text-md font-bold text-blue-700 mr-1">{statistics.resume_events || 0}</span>
                          <span className="text-xs font-medium text-blue-600">Resumes</span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="inline-flex items-center justify-center w-full px-3 py-2 rounded-lg bg-red-50 border border-red-200">
                          <span className="text-md font-bold text-red-700 mr-1">{statistics.stop_events || 0}</span>
                          <span className="text-xs font-medium text-red-600">Stops</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Timer History Timeline */}
              <div className="mx-6 mb-6">
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
                  <div className="p-4">
                    <h3 className="text-md font-bold text-gray-900 mb-4 flex items-center">
                      <div className="w-6 h-6 bg-gray-100 rounded-lg flex items-center justify-center mr-2 text-xs">
                        🕒
                      </div>
                      Timer Events Timeline
                    </h3>
                    {timerHistory.length === 0 ? (
                      <div className="text-center text-gray-500 py-12">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <FaClock size={24} className="text-gray-400" />
                        </div>
                        <p className="text-lg font-medium">No timer history available</p>
                        <p className="text-sm text-gray-400 mt-1">Timer events will appear here once you start tracking time</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {timerHistory.map((event, index) => (
                          <div key={event.id} className="relative">
                            {/* Timeline connector */}
                            {index < timerHistory.length - 1 && (
                              <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200"></div>
                            )}

                            <div className={`relative bg-gray-50 border-l-4 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                              getEventColor(event.event_type) === 'success' ? 'border-green-500 bg-green-50' :
                                getEventColor(event.event_type) === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                                  getEventColor(event.event_type) === 'info' ? 'border-blue-500 bg-blue-50' : 'border-gray-500 bg-gray-50'
                            }`}>
                              <div className="p-4">
                                <div className="flex justify-between items-start">
                                  <div className="flex items-start flex-1">
                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                                      getEventColor(event.event_type) === 'success' ? 'bg-green-100' :
                                        getEventColor(event.event_type) === 'warning' ? 'bg-yellow-100' :
                                          getEventColor(event.event_type) === 'info' ? 'bg-blue-100' : 'bg-gray-100'
                                    }`}>
                                      {getEventIcon(event.event_type)}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center mb-2">
                                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mr-3 ${
                                          getEventColor(event.event_type) === 'success' ? 'bg-green-100 text-green-800' :
                                            getEventColor(event.event_type) === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                              getEventColor(event.event_type) === 'info' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                                        }`}>
                                          {formatEventType(event.event_type)}
                                        </span>
                                        <span className="text-sm font-medium text-gray-700">
                                          {event.status_from} → {event.status_to}
                                        </span>
                                      </div>
                                      <div className="text-sm text-gray-600 mb-1">
                                        {formatTimestamp(event.timestamp)}
                                      </div>
                                      {event.event_description && (
                                        <div className="text-sm text-gray-500">
                                          {event.event_description}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                  <div className="text-right ml-4">
                                    {event.duration_formatted && event.duration_formatted !== '00:00:00' && (
                                      <div className="text-lg font-bold text-blue-600 mb-1">
                                        {event.duration_formatted}
                                      </div>
                                    )}
                                    {event.cumulative_time_formatted && (
                                      <div className="text-xs text-gray-500">
                                        Total: {event.cumulative_time_formatted}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 bg-white">
          <button
            onClick={handleClose}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium shadow-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TimerHistoryModal;
