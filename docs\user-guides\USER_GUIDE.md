# TallyCRM User Guide

Welcome to TallyCRM - Your Complete Customer Relationship Management Solution

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Customer Management](#customer-management)
4. [Service Calls](#service-calls)
5. [Leads Management](#leads-management)
6. [Reports](#reports)
7. [Settings](#settings)
8. [Frequently Asked Questions](#frequently-asked-questions)

---

## Getting Started

### Logging In
1. Open your web browser and navigate to your TallyCRM URL
2. Enter your username and password
3. Click "Login" to access the system

### First Time Setup
After logging in for the first time:
1. Complete your profile information
2. Set up your notification preferences
3. Familiarize yourself with the dashboard

---

## Dashboard Overview

The dashboard is your central hub for monitoring business activities:

### Key Metrics
- **Total Customers**: View your complete customer base
- **Active Service Calls**: Monitor ongoing support requests
- **Pending Leads**: Track potential business opportunities
- **Monthly Revenue**: Monitor financial performance

### Quick Actions
- Create new customer records
- Log service calls
- Add new leads
- Generate reports

---

## Customer Management

### Adding a New Customer

1. **Navigate to Customers**
   - Click "Customers" in the main menu
   - Click "Add New Customer" button

2. **Required Information**
   - Company Name
   - Tally Serial Number
   - Admin Email Address
   - MD Contact Person
   - MD Phone Number

3. **Optional Information**
   - GST Number
   - Office Address
   - Professional Contacts (Auditor, IT Person, Tax Consultant)
   - Business Information

4. **Address Book**
   - Add multiple contacts for the customer
   - Include designation, name, mobile number, and email
   - Some designations may be mandatory

### Editing Customer Information
1. Find the customer using search or browse
2. Click on the customer name or "Edit" button
3. Update the required information
4. Click "Save Changes"

### Customer Features
- **File Uploads**: Attach TDL files, addons, and other documents
- **Service History**: View all service calls for the customer
- **Contact Management**: Maintain multiple contacts per customer
- **Notes**: Add important notes and remarks

---

## Service Calls

### Creating a Service Call

1. **Basic Information**
   - Select customer (auto-fills contact details)
   - Choose call type (Online/Onsite)
   - Select products/issues from dropdown
   - Add description of the problem

2. **Contact Details**
   - Customer phone numbers are auto-populated
   - Add additional contact numbers if needed

3. **Assignment**
   - Assign to an executive
   - Set priority level
   - Add any special instructions

### Service Call Status

Service calls progress through different statuses:
- **Open**: Newly created service call
- **In Progress**: Currently being worked on
- **On Hold**: Temporarily paused
- **Completed**: Successfully resolved
- **Cancelled**: Cancelled for various reasons

### Timer Functionality
- Automatic time tracking for service calls
- Timer starts when status changes to "In Progress"
- Pause/resume capability
- Detailed time logs for billing purposes

### Managing Service Calls
- **View All**: See complete list of service calls
- **Filter**: Filter by status, customer, executive, or date
- **Search**: Quick search by customer name or service details
- **Update Status**: Change service call status as work progresses

---

## Leads Management

### Adding a New Lead

1. **Required Information**
   - Customer Name (mandatory)
   - Contact Number (mandatory)

2. **Optional Information**
   - Products/Services of interest
   - Potential deal amount
   - Follow-up date
   - Assigned executive
   - Remarks and notes
   - Reference contact details

### Lead Status Tracking
- **New**: Recently added lead
- **Follow Up**: Requires follow-up action
- **Call Not Attended**: Contact attempt unsuccessful
- **Interested**: Prospect shows interest
- **Not Interested**: Prospect not interested
- **Converted**: Successfully converted to customer
- **Lost**: Opportunity lost

### Lead Management Features
- **Follow-up Reminders**: Set dates for follow-up actions
- **Executive Assignment**: Assign leads to team members
- **Conversion Tracking**: Monitor lead-to-customer conversion
- **Reference Management**: Track referral sources

---

## Reports

### Available Reports
1. **Customer Reports**
   - Customer list with contact details
   - Customer activity summary
   - New customer acquisitions

2. **Service Reports**
   - Service call summary
   - Executive performance
   - Response time analysis
   - Customer satisfaction metrics

3. **Lead Reports**
   - Lead conversion rates
   - Lead source analysis
   - Executive lead performance
   - Follow-up pending reports

4. **Financial Reports**
   - Revenue tracking
   - Service billing summary
   - Customer payment status

### Generating Reports
1. Navigate to "Reports" section
2. Select the desired report type
3. Choose date range and filters
4. Click "Generate Report"
5. Export to Excel or PDF if needed

---

## Settings

### User Profile
- Update personal information
- Change password
- Set notification preferences
- Configure dashboard layout

### System Settings
- Manage master data (executives, call types, products)
- Configure notification settings
- Set up email templates
- Manage user permissions

### Notification Settings
- Email notifications for service updates
- WhatsApp notifications (if configured)
- Dashboard alerts
- Follow-up reminders

---

## Frequently Asked Questions

### General Questions

**Q: How do I reset my password?**
A: Contact your system administrator or use the "Forgot Password" link on the login page.

**Q: Can I access TallyCRM on mobile devices?**
A: Yes, TallyCRM is responsive and works on tablets and smartphones.

**Q: How do I export data?**
A: Most lists and reports have export options. Look for "Export" or "Download" buttons.

### Customer Management

**Q: What if I enter duplicate customer information?**
A: The system will warn you about potential duplicates based on company name or Tally serial number.

**Q: Can I delete a customer?**
A: Customer records are typically archived rather than deleted to maintain data integrity.

**Q: How do I handle multiple locations for one customer?**
A: Use the address book feature to add multiple contacts and locations.

### Service Calls

**Q: What happens if I forget to stop the timer?**
A: You can manually adjust time entries or contact your administrator for corrections.

**Q: Can I assign multiple executives to one service call?**
A: Currently, each service call is assigned to one primary executive.

**Q: How do I handle emergency service calls?**
A: Use the priority settings and notify the assigned executive immediately.

### Leads

**Q: How long should I keep leads in the system?**
A: Keep leads as long as there's potential for conversion. Archive old leads periodically.

**Q: Can I convert a lead to a customer automatically?**
A: The system tracks conversions, but customer creation requires complete information entry.

**Q: What if a lead becomes a customer through another channel?**
A: Update the lead status to "Converted" and link it to the customer record.

---

## Support and Help

### Getting Help
- **In-App Help**: Look for help icons (?) throughout the system
- **User Manual**: This comprehensive guide
- **System Administrator**: Contact your internal admin for technical issues
- **Training**: Request additional training sessions if needed

### Best Practices
1. **Regular Updates**: Keep customer information current
2. **Timely Follow-ups**: Respond to leads and service calls promptly
3. **Accurate Data Entry**: Ensure all information is complete and correct
4. **Regular Backups**: Your administrator handles this, but report any data concerns
5. **Security**: Log out when finished and don't share login credentials

### System Requirements
- **Browser**: Modern web browser (Chrome, Firefox, Safari, Edge)
- **Internet**: Stable internet connection
- **Screen Resolution**: 1024x768 minimum (1920x1080 recommended)
- **JavaScript**: Must be enabled

---

## Contact Information

For technical support or questions about TallyCRM:
- **System Administrator**: Contact your internal IT team
- **User Training**: Request additional training sessions
- **Feature Requests**: Submit suggestions through your administrator

---

## Additional Documentation

For more detailed information, please refer to:
- **Quick Start Guide**: Get up and running in 5 minutes
- **Troubleshooting Guide**: Solutions to common problems
- **Features Overview**: Complete list of system capabilities

*This user guide is designed to help you make the most of TallyCRM. For the latest updates and features, please refer to the system announcements and contact your administrator.*
