import express from 'express';
import {
  getLeaveTypes,
  submitLeaveRequest,
  getLeaveRequests,
  approveLeaveRequest,
  rejectLeaveRequest,
  cancelLeaveRequest,
  getLeaveBalance,
  getLeaveCalendar
} from '../controllers/leaveController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validateTenantExists } from '../middleware/tenantValidation.js';
import { requirePermission } from '../middleware/auth.js';
import { body, query, param, validationResult } from 'express-validator';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }
  next();
};

// Leave request validation
const leaveRequestValidation = [
  body('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  body('leave_type_id').isUUID().withMessage('Valid leave type ID is required'),
  body('start_date').isISO8601().withMessage('Valid start date is required'),
  body('end_date').isISO8601().withMessage('Valid end date is required'),
  body('is_half_day').optional().isBoolean().withMessage('is_half_day must be boolean'),
  body('half_day_period').optional().isIn(['first_half', 'second_half'])
    .withMessage('Invalid half day period'),
  body('reason').isString().isLength({ min: 10, max: 1000 })
    .withMessage('Reason must be between 10 and 1000 characters'),
  body('emergency_contact').optional().isString().withMessage('Emergency contact must be string'),
  body('work_handover_to').optional().isUUID().withMessage('Invalid handover employee ID'),
  body('handover_notes').optional().isString().withMessage('Handover notes must be string'),
  body('priority').optional().isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('documents').optional().isArray().withMessage('Documents must be an array')
];

// Approval/rejection validation
const approvalValidation = [
  param('id').isUUID().withMessage('Valid request ID is required'),
  body('manager_comments').optional().isString().withMessage('Manager comments must be string')
];

const rejectionValidation = [
  param('id').isUUID().withMessage('Valid request ID is required'),
  body('rejection_reason').isString().isLength({ min: 10, max: 500 })
    .withMessage('Rejection reason must be between 10 and 500 characters'),
  body('manager_comments').optional().isString().withMessage('Manager comments must be string')
];

// Query validation for leave requests
const leaveRequestsQueryValidation = [
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  query('status').optional().isIn(['pending', 'approved', 'rejected', 'cancelled', 'withdrawn'])
    .withMessage('Invalid status'),
  query('leave_type_id').optional().isUUID().withMessage('Invalid leave type ID'),
  query('start_date').optional().isISO8601().withMessage('Invalid start date'),
  query('end_date').optional().isISO8601().withMessage('Invalid end date'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('sort_by').optional().isIn(['applied_at', 'start_date', 'end_date', 'status'])
    .withMessage('Invalid sort field'),
  query('sort_order').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC')
];

// Leave balance query validation
const leaveBalanceQueryValidation = [
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  query('year').optional().isInt({ min: 2020, max: 2030 }).withMessage('Invalid year')
];

// Leave calendar query validation
const leaveCalendarQueryValidation = [
  query('start_date').optional().isISO8601().withMessage('Invalid start date'),
  query('end_date').optional().isISO8601().withMessage('Invalid end date'),
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID')
];

// Apply middleware to all routes
router.use(authenticateToken);
router.use(validateTenantExists);

/**
 * @route GET /api/leaves/types
 * @desc Get all active leave types
 * @access Private (All authenticated users)
 */
router.get('/types',
  requirePermission('leaves.read'),
  getLeaveTypes
);

/**
 * @route POST /api/leaves/request
 * @desc Submit a new leave request
 * @access Private (Employee)
 */
router.post('/request',
  requirePermission('leaves.create'),
  leaveRequestValidation,
  handleValidationErrors,
  submitLeaveRequest
);

/**
 * @route GET /api/leaves/requests
 * @desc Get leave requests
 * @access Private (Employee can view own, Manager can view team, Admin can view all)
 */
router.get('/requests',
  requirePermission('leaves.read'),
  leaveRequestsQueryValidation,
  handleValidationErrors,
  getLeaveRequests
);

/**
 * @route PUT /api/leaves/requests/:id/approve
 * @desc Approve a leave request
 * @access Private (Manager/HR/Admin only)
 */
router.put('/requests/:id/approve',
  requirePermission('leaves.update'),
  approvalValidation,
  handleValidationErrors,
  approveLeaveRequest
);

/**
 * @route PUT /api/leaves/requests/:id/reject
 * @desc Reject a leave request
 * @access Private (Manager/HR/Admin only)
 */
router.put('/requests/:id/reject',
  requirePermission('leaves.update'),
  rejectionValidation,
  handleValidationErrors,
  rejectLeaveRequest
);

/**
 * @route PUT /api/leaves/requests/:id/cancel
 * @desc Cancel a leave request
 * @access Private (Employee can cancel own, Manager/HR/Admin can cancel any)
 */
router.put('/requests/:id/cancel',
  requirePermission('leaves.update'),
  param('id').isUUID().withMessage('Valid request ID is required'),
  handleValidationErrors,
  cancelLeaveRequest
);

/**
 * @route GET /api/leaves/balance
 * @desc Get leave balance for an employee
 * @access Private (Employee can view own, Manager/HR/Admin can view any)
 */
router.get('/balance',
  requirePermission('leaves.read'),
  leaveBalanceQueryValidation,
  handleValidationErrors,
  getLeaveBalance
);

/**
 * @route GET /api/leaves/calendar
 * @desc Get leave calendar
 * @access Private (Employee can view own, Manager/HR/Admin can view team/all)
 */
router.get('/calendar',
  requirePermission('leaves.read'),
  leaveCalendarQueryValidation,
  handleValidationErrors,
  getLeaveCalendar
);

export default router;
