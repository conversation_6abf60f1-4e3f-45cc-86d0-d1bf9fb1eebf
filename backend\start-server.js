import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Import configurations
import appConfig from './config/app.js';
import { logger } from './src/utils/logger.js';
import { errorHandler, notFoundHandler } from './src/middleware/errorHandler.js';
import { requestLogger } from './src/middleware/requestLogger.js';

// Import routes
import healthRoutes from './src/routes/health.js';
import authRoutes from './src/routes/auth.js';
import userRoutes from './src/routes/users.js';
import customerRoutes from './src/routes/customers.js';
import serviceCallRoutes from './src/routes/serviceCalls.js';
import masterDataRoutes from './src/routes/masterData.js';
import executiveRoutes from './src/routes/executives.js';
import dashboardRoutes from './src/routes/dashboard.js';

// Load environment variables
dotenv.config();

const startServer = async () => {
  try {
    // Create Express application
    const app = express();

    // Trust proxy for accurate IP addresses
    app.set('trust proxy', 1);

    // Security middleware
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"],
          fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS configuration (conditionally applied)
    if (appConfig.frontend.enableCors) {
      app.use(cors({
        origin: appConfig.frontend.corsOrigins,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        maxAge: 86400, // Cache preflight responses for 24 hours
        preflightContinue: false,
        optionsSuccessStatus: 204
      }));
      logger.info('🌐 CORS enabled for origins:', appConfig.frontend.corsOrigins);
    } else {
      // For same-origin deployments, allow all origins
      app.use(cors({
        origin: true,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        maxAge: 86400, // Cache preflight responses for 24 hours
        preflightContinue: false,
        optionsSuccessStatus: 204
      }));
      logger.info('🌐 CORS disabled - allowing all origins (same-origin deployment)');
    }

    // Rate limiting (conditionally applied)
    if (appConfig.security.enableRateLimiting) {
      const limiter = rateLimit({
        windowMs: appConfig.security.rateLimitWindowMs,
        max: appConfig.security.rateLimitMaxRequests,
        message: {
          error: 'Too many requests from this IP, please try again later.',
        },
        standardHeaders: true,
        legacyHeaders: false,
      });
      app.use(limiter);
      logger.info('🛡️ Rate limiting enabled');
    } else {
      logger.info('⚠️ Rate limiting disabled');
    }

    // Body parsing middleware
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    app.use(cookieParser());

    // Compression middleware
    app.use(compression());

    // Request logging middleware
    app.use(requestLogger);

    // API routes
    app.use('/health', healthRoutes);
    app.use(`${appConfig.api.prefix}/auth`, authRoutes);
    app.use(`${appConfig.api.prefix}/users`, userRoutes);
    app.use(`${appConfig.api.prefix}/customers`, customerRoutes);
    app.use(`${appConfig.api.prefix}/service-calls`, serviceCallRoutes);
    app.use(`${appConfig.api.prefix}/master-data`, masterDataRoutes);
    app.use(`${appConfig.api.prefix}/executives`, executiveRoutes);
    app.use(`${appConfig.api.prefix}/dashboard`, dashboardRoutes);

    // Serve static files (uploads, etc.)
    app.use('/uploads', express.static('uploads'));

    // 404 handler
    app.use(notFoundHandler);

    // Global error handler
    app.use(errorHandler);

    // Start server
    const PORT = appConfig.app.port;
    const server = app.listen(PORT, () => {
      logger.info(`🚀 TallyCRM Backend Server started successfully!`);
      logger.info(`📍 Environment: ${appConfig.app.env}`);
      logger.info(`🌐 Server running on: ${appConfig.app.url}`);
      logger.info(`📡 API Base URL: ${appConfig.app.url}${appConfig.api.prefix}`);
    });

    return server;
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
