# 🚀 TallyCRM SaaS Setup Guide

This guide will help you set up TallyCRM as a complete SaaS (Software as a Service) product with multi-tenancy, subscription management, billing, and usage tracking.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Database Setup](#database-setup)
4. [Stripe Configuration](#stripe-configuration)
5. [Environment Configuration](#environment-configuration)
6. [Running Migrations](#running-migrations)
7. [Seeding Data](#seeding-data)
8. [Testing SaaS Features](#testing-saas-features)
9. [Production Deployment](#production-deployment)

## 🎯 Overview

TallyCRM has been transformed into a complete SaaS solution with the following features:

### ✅ SaaS Features Implemented

- **Multi-Tenant Architecture**: Complete tenant isolation with secure data separation
- **Subscription Management**: Multiple subscription plans with different feature sets
- **Billing & Invoicing**: Automated billing with Stripe integration
- **Usage Tracking**: Real-time monitoring of tenant resource usage
- **Payment Processing**: Secure payment handling with Stripe
- **Plan Upgrades/Downgrades**: Seamless plan changes
- **Trial Management**: Free trial periods for new tenants
- **Usage Limits**: Automatic enforcement of plan-based limits
- **SaaS Admin Panel**: Administrative tools for managing tenants
- **Webhook Integration**: Real-time sync with Stripe events

### 📊 Subscription Plans

1. **Starter Plan** - ₹999/month
   - 3 Users, 50 Customers, 25 Service Calls/month, 1GB Storage
   
2. **Professional Plan** - ₹2,499/month (Most Popular)
   - 10 Users, 200 Customers, 100 Service Calls/month, 5GB Storage
   
3. **Business Plan** - ₹4,999/month
   - 25 Users, 500 Customers, 250 Service Calls/month, 15GB Storage
   
4. **Enterprise Plan** - ₹9,999/month
   - 100 Users, 2000 Customers, 1000 Service Calls/month, 50GB Storage

## 🔧 Prerequisites

- Node.js 18+ and npm
- PostgreSQL 13+
- Stripe Account (for payment processing)
- Redis (optional, for caching)

## 🗄️ Database Setup

1. **Create the database:**
```sql
CREATE DATABASE tallycrm_saas;
CREATE USER tallycrm_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE tallycrm_saas TO tallycrm_user;
```

2. **Update your database connection:**
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm_saas
DB_USERNAME=tallycrm_user
DB_PASSWORD=your_secure_password
```

## 💳 Stripe Configuration

1. **Create a Stripe Account:**
   - Go to [Stripe Dashboard](https://dashboard.stripe.com)
   - Create an account or log in

2. **Get API Keys:**
   - Navigate to Developers > API keys
   - Copy your Publishable key and Secret key

3. **Create Products and Prices:**
   ```bash
   # You can create these manually in Stripe Dashboard or use the API
   # The seeder will create local subscription plans
   ```

4. **Set up Webhooks:**
   - Go to Developers > Webhooks
   - Add endpoint: `https://yourdomain.com/webhooks/stripe`
   - Select events:
     - `checkout.session.completed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.created`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
     - `payment_intent.succeeded`
     - `payment_intent.payment_failed`

## ⚙️ Environment Configuration

1. **Copy the environment file:**
```bash
cp backend/.env.example backend/.env
```

2. **Update SaaS-specific variables:**
```env
# SaaS Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# SaaS Features
ENABLE_USAGE_TRACKING=true
ENABLE_BILLING=true
ENABLE_SUBSCRIPTIONS=true
ENABLE_MULTI_TENANT=true

# Frontend URL for redirects
FRONTEND_URL=http://localhost:3000
```

## 🔄 Running Migrations

1. **Install dependencies:**
```bash
cd backend
npm install
```

2. **Run database migrations:**
```bash
npm run migrate
```

This will create all the necessary tables including:
- `subscription_plans`
- `subscriptions`
- `invoices`
- `payments`
- `usage_records`

## 🌱 Seeding Data

1. **Seed subscription plans:**
```bash
npm run seed
```

This will create the 4 subscription plans (Starter, Professional, Business, Enterprise) with their features and pricing.

## 🧪 Testing SaaS Features

### 1. **Test Tenant Registration:**
```bash
# Start the backend
cd backend
npm run dev

# Start the frontend
cd frontend
npm start
```

### 2. **Test Subscription Flow:**
1. Register a new tenant
2. Navigate to `/billing/plans`
3. Select a plan and test checkout (use Stripe test cards)
4. Verify subscription creation in `/billing/dashboard`

### 3. **Test Usage Tracking:**
1. Create users, customers, service calls
2. Check usage in billing dashboard
3. Test limit enforcement

### 4. **Test Stripe Webhooks:**
1. Use Stripe CLI for local testing:
```bash
stripe listen --forward-to localhost:3001/webhooks/stripe
```

### 5. **Stripe Test Cards:**
```
Success: ****************
Decline: ****************
Insufficient Funds: ****************
```

## 🚀 Production Deployment

### 1. **Environment Setup:**
```env
NODE_ENV=production
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_secret
```

### 2. **Database Migration:**
```bash
NODE_ENV=production npm run migrate
NODE_ENV=production npm run seed
```

### 3. **SSL Configuration:**
- Ensure HTTPS is enabled
- Update webhook endpoints to use HTTPS
- Configure proper CORS settings

### 4. **Monitoring:**
- Set up application monitoring
- Configure error tracking
- Monitor usage metrics
- Set up billing alerts

## 📊 SaaS Admin Features

### Admin Dashboard Access:
- Create a super admin user
- Access admin features at `/admin` (to be implemented)
- Monitor tenant usage and billing
- Manage subscription plans

### Key Admin Functions:
- View all tenants and their status
- Monitor usage across all tenants
- Handle billing issues
- Manage subscription plans
- View revenue metrics

## 🔒 Security Considerations

1. **Data Isolation**: Each tenant's data is completely isolated
2. **API Security**: All endpoints require authentication and tenant context
3. **Payment Security**: All payments processed securely through Stripe
4. **Usage Limits**: Automatic enforcement prevents abuse
5. **Webhook Security**: Stripe webhook signatures are verified

## 📈 Scaling Considerations

1. **Database**: Consider read replicas for high traffic
2. **Caching**: Implement Redis for session and data caching
3. **CDN**: Use CDN for static assets
4. **Load Balancing**: Implement load balancing for multiple instances
5. **Monitoring**: Set up comprehensive monitoring and alerting

## 🆘 Troubleshooting

### Common Issues:

1. **Webhook Failures:**
   - Check webhook endpoint URL
   - Verify webhook secret
   - Check server logs

2. **Payment Issues:**
   - Verify Stripe keys
   - Check test vs live mode
   - Review Stripe dashboard

3. **Usage Tracking:**
   - Ensure usage tracking is enabled
   - Check database connections
   - Verify tenant context

### Support:
- Check application logs
- Review Stripe dashboard
- Monitor database performance
- Use Stripe CLI for debugging

## 🎉 Congratulations!

You now have a fully functional SaaS version of TallyCRM with:
- Multi-tenant architecture
- Subscription management
- Automated billing
- Usage tracking
- Payment processing
- Admin tools

Your TallyCRM is ready to serve multiple customers as a SaaS product!
