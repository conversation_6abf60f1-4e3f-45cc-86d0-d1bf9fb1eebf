import { useState, useCallback, useRef } from 'react';
import { apiService } from '../services/api';

/**
 * Hook for server-side area/location search
 * Provides debounced search functionality for area selection components
 */
export const useAreaSearch = () => {
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(null);

  // Timeout ref for proper debouncing
  const timeoutRef = useRef(null);

  // Debounced search function
  const searchAreas = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const response = await apiService.get('/master-data/areas', {
        params: {
          search: searchTerm,
          limit: 20, // Limit results for performance
          page: 1,
          sortBy: 'name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data?.area) {
        // Transform area data for SearchableSelect
        const areas = response.data.data.area.map(area => ({
          id: area.id,
          name: area.name || 'Unknown Area',
          city: area.city || 'N/A',
          state: area.state || 'N/A',
          description: area.description || '',
          // Include original area data
          originalData: area
        }));

        setSearchResults(areas);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Area search error:', error);
      setSearchError(error.message || 'Failed to search areas');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Properly debounced search with timeout management
  const debouncedSearch = useCallback((searchTerm) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      searchAreas(searchTerm);
    }, 150); // 150ms debounce - fast but prevents infinite loops
  }, [searchAreas]);

  // Reset search state
  const resetSearch = useCallback(() => {
    setSearchResults(null);
    setIsSearching(false);
    setSearchError(null);
  }, []);

  return {
    searchResults,
    isSearching,
    searchError,
    searchAreas: debouncedSearch,
    resetSearch
  };
};

export default useAreaSearch;
