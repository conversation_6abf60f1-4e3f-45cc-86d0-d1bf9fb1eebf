import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get leave types
 */
export const getLeaveTypes = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const leaveTypes = await models.LeaveType.findAll({
      where: {
        tenant_id: tenantId,
        is_active: true
      },
      order: [['sort_order', 'ASC'], ['name', 'ASC']]
    });

    res.json({
      success: true,
      data: { leaveTypes }
    });

  } catch (error) {
    logger.error('Get leave types error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave types',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Submit leave request
 */
export const submitLeaveRequest = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      leave_type_id,
      start_date,
      end_date,
      is_half_day,
      half_day_period,
      reason,
      emergency_contact,
      work_handover_to,
      handover_notes,
      priority = 'normal',
      documents
    } = req.body;

    const employeeId = employee_id || req.user.executive_id;

    // Validate employee
    const employee = await models.Executive.findOne({
      where: {
        id: employeeId,
        tenant_id: tenantId,
        is_active: true
      }
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Validate leave type
    const leaveType = await models.LeaveType.findOne({
      where: {
        id: leave_type_id,
        tenant_id: tenantId,
        is_active: true
      }
    });

    if (!leaveType) {
      return res.status(404).json({
        success: false,
        message: 'Leave type not found'
      });
    }

    // Calculate total days
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    let totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    
    if (is_half_day) {
      totalDays = 0.5;
    }

    // Validate minimum advance notice
    const today = new Date();
    const daysDifference = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24));
    
    if (daysDifference < leaveType.advance_notice_days) {
      return res.status(400).json({
        success: false,
        message: `This leave type requires ${leaveType.advance_notice_days} days advance notice`
      });
    }

    // Check for overlapping leave requests
    const overlappingRequests = await models.LeaveRequest.findAll({
      where: {
        tenant_id: tenantId,
        employee_id: employeeId,
        status: ['pending', 'approved'],
        [Op.or]: [
          {
            start_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            end_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            [Op.and]: [
              { start_date: { [Op.lte]: start_date } },
              { end_date: { [Op.gte]: end_date } }
            ]
          }
        ]
      }
    });

    if (overlappingRequests.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'You have overlapping leave requests for this period'
      });
    }

    // Check leave balance
    const currentYear = new Date().getFullYear();
    const leaveBalance = await models.LeaveBalance.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employeeId,
        leave_type_id: leave_type_id,
        year: currentYear
      }
    });

    if (leaveBalance && !leaveBalance.canTakeLeave(totalDays)) {
      return res.status(400).json({
        success: false,
        message: 'Insufficient leave balance',
        data: {
          requestedDays: totalDays,
          availableDays: leaveBalance.getAvailableDays()
        }
      });
    }

    // Generate request number
    const requestNumber = models.LeaveRequest.generateRequestNumber(tenantId, currentYear);

    // Create leave request
    const leaveRequest = await models.LeaveRequest.create({
      tenant_id: tenantId,
      employee_id: employeeId,
      leave_type_id: leave_type_id,
      request_number: requestNumber,
      start_date: start_date,
      end_date: end_date,
      total_days: totalDays,
      is_half_day: is_half_day || false,
      half_day_period: half_day_period,
      reason: reason,
      emergency_contact: emergency_contact,
      work_handover_to: work_handover_to,
      handover_notes: handover_notes,
      priority: priority,
      documents: documents,
      requested_by: req.user.id,
      status: leaveType.requires_approval ? 'pending' : 'approved',
      auto_approved: !leaveType.requires_approval
    });

    // Update leave balance pending days
    if (leaveBalance) {
      await leaveBalance.addPendingDays(totalDays);
    }

    // Log the request
    logger.info('Leave request submitted:', {
      requestId: leaveRequest.id,
      requestNumber: requestNumber,
      employeeId: employeeId,
      leaveTypeId: leave_type_id,
      totalDays: totalDays,
      tenantId: tenantId
    });

    // Load the complete request with associations
    const completeRequest = await models.LeaveRequest.findByPk(leaveRequest.id, {
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name']
        },
        {
          model: models.LeaveType,
          as: 'leaveType',
          attributes: ['id', 'name', 'code', 'color_code']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Leave request submitted successfully',
      data: { leaveRequest: completeRequest }
    });

  } catch (error) {
    logger.error('Submit leave request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit leave request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get leave requests
 */
export const getLeaveRequests = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      status,
      leave_type_id,
      start_date,
      end_date,
      page = 1,
      limit = 50,
      sort_by = 'applied_at',
      sort_order = 'DESC'
    } = req.query;

    const whereClause = { tenant_id: tenantId };

    // Check user permissions to determine which requests they can see
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role => 
      ['admin', 'hr'].includes(role.slug)
    );
    const canViewTeam = userRoles.some(role => 
      ['manager'].includes(role.slug)
    );

    if (!canViewAll && !canViewTeam) {
      // Regular employee can only see their own requests
      whereClause.employee_id = req.user.executive_id;
    } else if (employee_id) {
      whereClause.employee_id = employee_id;
    }

    if (status) {
      whereClause.status = status;
    }

    if (leave_type_id) {
      whereClause.leave_type_id = leave_type_id;
    }

    if (start_date && end_date) {
      whereClause[Op.or] = [
        {
          start_date: {
            [Op.between]: [start_date, end_date]
          }
        },
        {
          end_date: {
            [Op.between]: [start_date, end_date]
          }
        }
      ];
    }

    const offset = (page - 1) * limit;

    const { count, rows: leaveRequests } = await models.LeaveRequest.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
        },
        {
          model: models.LeaveType,
          as: 'leaveType',
          attributes: ['id', 'name', 'code', 'color_code']
        },
        {
          model: models.User,
          as: 'approver',
          attributes: ['id', 'first_name', 'last_name']
        },
        {
          model: models.Executive,
          as: 'handoverEmployee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name']
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        leaveRequests,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Get leave requests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave requests',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Approve leave request
 */
export const approveLeaveRequest = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;
    const { manager_comments } = req.body;

    const leaveRequest = await models.LeaveRequest.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
        status: 'pending'
      },
      include: [
        {
          model: models.Executive,
          as: 'employee'
        },
        {
          model: models.LeaveType,
          as: 'leaveType'
        }
      ]
    });

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found or already processed'
      });
    }

    // Check if user has permission to approve
    const userRoles = await req.user.getRoles();
    const canApprove = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    if (!canApprove) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to approve leave requests'
      });
    }

    // Update leave request
    await leaveRequest.update({
      status: 'approved',
      approved_by: req.user.id,
      approved_at: new Date(),
      manager_comments: manager_comments
    });

    // Update leave balance
    const leaveBalance = await models.LeaveBalance.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: leaveRequest.employee_id,
        leave_type_id: leaveRequest.leave_type_id,
        year: new Date().getFullYear()
      }
    });

    if (leaveBalance) {
      await leaveBalance.removePendingDays(leaveRequest.total_days);
      await leaveBalance.addUsedDays(leaveRequest.total_days);
    }

    // Log the approval
    logger.info('Leave request approved:', {
      requestId: id,
      employeeId: leaveRequest.employee_id,
      approvedBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Leave request approved successfully',
      data: { leaveRequest }
    });

  } catch (error) {
    logger.error('Approve leave request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve leave request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Reject leave request
 */
export const rejectLeaveRequest = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;
    const { rejection_reason, manager_comments } = req.body;

    if (!rejection_reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const leaveRequest = await models.LeaveRequest.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
        status: 'pending'
      },
      include: [
        {
          model: models.Executive,
          as: 'employee'
        }
      ]
    });

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found or already processed'
      });
    }

    // Check if user has permission to reject
    const userRoles = await req.user.getRoles();
    const canReject = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    if (!canReject) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to reject leave requests'
      });
    }

    // Update leave request
    await leaveRequest.update({
      status: 'rejected',
      approved_by: req.user.id,
      approved_at: new Date(),
      rejection_reason: rejection_reason,
      manager_comments: manager_comments
    });

    // Remove pending days from leave balance
    const leaveBalance = await models.LeaveBalance.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: leaveRequest.employee_id,
        leave_type_id: leaveRequest.leave_type_id,
        year: new Date().getFullYear()
      }
    });

    if (leaveBalance) {
      await leaveBalance.removePendingDays(leaveRequest.total_days);
    }

    // Log the rejection
    logger.info('Leave request rejected:', {
      requestId: id,
      employeeId: leaveRequest.employee_id,
      rejectedBy: req.user.id,
      reason: rejection_reason,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Leave request rejected successfully',
      data: { leaveRequest }
    });

  } catch (error) {
    logger.error('Reject leave request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject leave request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Cancel leave request
 */
export const cancelLeaveRequest = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;

    const leaveRequest = await models.LeaveRequest.findOne({
      where: {
        id: id,
        tenant_id: tenantId,
        status: ['pending', 'approved']
      }
    });

    if (!leaveRequest) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found or cannot be cancelled'
      });
    }

    // Check if user can cancel (employee can cancel their own, admin/hr/manager can cancel any)
    const userRoles = await req.user.getRoles();
    const canCancel = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    ) || leaveRequest.requested_by === req.user.id;

    if (!canCancel) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to cancel this leave request'
      });
    }

    // Check if leave has already started
    const today = new Date();
    const startDate = new Date(leaveRequest.start_date);

    if (startDate <= today) {
      return res.status(400).json({
        success: false,
        message: 'Cannot cancel leave request that has already started'
      });
    }

    // Update leave request
    await leaveRequest.update({
      status: 'cancelled'
    });

    // Update leave balance
    const leaveBalance = await models.LeaveBalance.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: leaveRequest.employee_id,
        leave_type_id: leaveRequest.leave_type_id,
        year: new Date().getFullYear()
      }
    });

    if (leaveBalance) {
      if (leaveRequest.status === 'pending') {
        await leaveBalance.removePendingDays(leaveRequest.total_days);
      } else if (leaveRequest.status === 'approved') {
        // Return used days back to balance
        await leaveBalance.addUsedDays(-leaveRequest.total_days);
      }
    }

    // Log the cancellation
    logger.info('Leave request cancelled:', {
      requestId: id,
      employeeId: leaveRequest.employee_id,
      cancelledBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Leave request cancelled successfully',
      data: { leaveRequest }
    });

  } catch (error) {
    logger.error('Cancel leave request error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel leave request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get leave balance
 */
export const getLeaveBalance = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { employee_id, year } = req.query;
    const currentYear = year || new Date().getFullYear();
    const employeeId = employee_id || req.user.executive_id;

    // Check permissions
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role =>
      ['admin', 'hr'].includes(role.slug)
    );

    if (!canViewAll && employeeId !== req.user.executive_id) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to view this employee\'s leave balance'
      });
    }

    const leaveBalances = await models.LeaveBalance.findAll({
      where: {
        tenant_id: tenantId,
        employee_id: employeeId,
        year: currentYear,
        is_active: true
      },
      include: [
        {
          model: models.LeaveType,
          as: 'leaveType',
          attributes: ['id', 'name', 'code', 'color_code', 'is_paid']
        },
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name']
        }
      ],
      order: [['leaveType', 'sort_order', 'ASC']]
    });

    // Calculate summary
    const summary = leaveBalances.map(balance => ({
      ...balance.toJSON(),
      remaining_days: balance.getRemainingDays(),
      available_days: balance.getAvailableDays(),
      total_days: balance.getTotalDays(),
      usage_percentage: balance.getUsagePercentage()
    }));

    res.json({
      success: true,
      data: {
        leaveBalances: summary,
        year: currentYear,
        employee_id: employeeId
      }
    });

  } catch (error) {
    logger.error('Get leave balance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave balance',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get leave calendar
 */
export const getLeaveCalendar = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { start_date, end_date, employee_id } = req.query;

    const whereClause = {
      tenant_id: tenantId,
      status: 'approved'
    };

    if (start_date && end_date) {
      whereClause[Op.or] = [
        {
          start_date: {
            [Op.between]: [start_date, end_date]
          }
        },
        {
          end_date: {
            [Op.between]: [start_date, end_date]
          }
        },
        {
          [Op.and]: [
            { start_date: { [Op.lte]: start_date } },
            { end_date: { [Op.gte]: end_date } }
          ]
        }
      ];
    }

    // Check permissions for employee filter
    const userRoles = await req.user.getRoles();
    const canViewAll = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    if (employee_id) {
      if (!canViewAll && employee_id !== req.user.executive_id) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions to view this employee\'s calendar'
        });
      }
      whereClause.employee_id = employee_id;
    } else if (!canViewAll) {
      whereClause.employee_id = req.user.executive_id;
    }

    const leaveRequests = await models.LeaveRequest.findAll({
      where: whereClause,
      include: [
        {
          model: models.Executive,
          as: 'employee',
          attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
        },
        {
          model: models.LeaveType,
          as: 'leaveType',
          attributes: ['id', 'name', 'code', 'color_code']
        }
      ],
      order: [['start_date', 'ASC']]
    });

    // Format for calendar
    const calendarEvents = leaveRequests.map(request => ({
      id: request.id,
      title: `${request.employee.first_name} ${request.employee.last_name} - ${request.leaveType.name}`,
      start: request.start_date,
      end: request.end_date,
      color: request.leaveType.color_code,
      employee: request.employee,
      leaveType: request.leaveType,
      totalDays: request.total_days,
      isHalfDay: request.is_half_day,
      halfDayPeriod: request.half_day_period
    }));

    res.json({
      success: true,
      data: {
        events: calendarEvents,
        period: { start_date, end_date }
      }
    });

  } catch (error) {
    logger.error('Get leave calendar error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave calendar',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
