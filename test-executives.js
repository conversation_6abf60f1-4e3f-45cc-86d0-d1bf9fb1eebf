// Test script to check the executives API validation fix
async function testExecutivesAPI() {
    console.log('🧪 Testing Executives API validation fix...');
    
    try {
        // Test data with empty date_of_birth field
        const testData = {
            first_name: 'Test',
            last_name: 'Executive',
            employee_code: 'TEST001',
            email: '<EMAIL>',
            phone: '+91-9876543210',
            designation_id: '123e4567-e89b-12d3-a456-************', // You may need to use a valid UUID
            department: 'sales',
            date_of_joining: '2024-01-01',
            date_of_birth: '', // Empty string - should be converted to null
            address: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            postal_code: '123456',
            salary: 50000.00,
            commission_rate: 5.00,
            target_amount: 100000.00,
            is_active: true
        };
        
        console.log('📤 Sending test data:', testData);
        
        // Test creating an executive with empty date_of_birth
        const response = await fetch('http://localhost:8082/api/v1/executives', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Add auth header if needed
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify(testData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ Executive created successfully!');
            console.log('📊 Response:', {
                success: result.success,
                message: result.message,
                dateOfBirth: result.data?.executive?.date_of_birth,
                dateOfJoining: result.data?.executive?.date_of_joining
            });
            
            // Clean up - delete the test record
            if (result.data?.executive?.id) {
                const deleteResponse = await fetch(`http://localhost:8082/api/v1/executives/${result.data.executive.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (deleteResponse.ok) {
                    console.log('🗑️ Test record cleaned up successfully');
                } else {
                    console.log('⚠️ Could not clean up test record');
                }
            }
        } else {
            console.error('❌ Executive creation failed:');
            console.error('Status:', response.status);
            console.error('Response:', result);
        }
        
        // Test updating with empty date_of_birth
        console.log('\n🔄 Testing update with empty date_of_birth...');
        
        const updateData = {
            first_name: 'Updated Test',
            date_of_birth: '',      // Empty string - should be converted to null
            date_of_joining: '   '  // Whitespace only - should be converted to null
        };
        
        console.log('📤 Sending update data:', updateData);
        
        // Note: This test assumes there's an existing executive
        // In a real test, you'd create a record first, then update it
        const updateResponse = await fetch('http://localhost:8082/api/v1/executives/123e4567-e89b-12d3-a456-************', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });
        
        const updateResult = await updateResponse.json();
        
        if (updateResponse.ok) {
            console.log('✅ Executive updated successfully!');
            console.log('📊 Update Response:', {
                success: updateResult.success,
                message: updateResult.message,
                dateOfBirth: updateResult.data?.executive?.date_of_birth,
                dateOfJoining: updateResult.data?.executive?.date_of_joining
            });
        } else {
            console.log('ℹ️ Update test result (may fail if record doesn\'t exist):');
            console.log('Status:', updateResponse.status);
            console.log('Response:', updateResult);
        }
        
        // Test with various empty value formats
        console.log('\n🧪 Testing various empty value formats...');
        
        const emptyValueTests = [
            { date_of_birth: '' },              // Empty string
            { date_of_birth: '   ' },           // Whitespace only
            { date_of_birth: null },            // Null
            { date_of_birth: undefined },       // Undefined (will be omitted from JSON)
            { date_of_birth: '1990-01-01' },    // Valid date
        ];
        
        for (let i = 0; i < emptyValueTests.length; i++) {
            const testCase = emptyValueTests[i];
            console.log(`\n📋 Test case ${i + 1}:`, testCase);
            
            const testResponse = await fetch('http://localhost:8082/api/v1/executives', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    first_name: 'Test',
                    last_name: `Executive ${i + 1}`,
                    employee_code: `TEST00${i + 1}`,
                    email: `test${i + 1}@example.com`,
                    phone: `+91-987654321${i}`,
                    designation_id: '123e4567-e89b-12d3-a456-************',
                    department: 'sales',
                    address: '123 Test Street',
                    city: 'Test City',
                    state: 'Test State',
                    postal_code: '123456',
                    salary: 50000.00,
                    commission_rate: 5.00,
                    ...testCase
                })
            });
            
            const testResult = await testResponse.json();
            
            if (testResponse.ok) {
                console.log(`✅ Test case ${i + 1} passed`);
                // Clean up
                if (testResult.data?.executive?.id) {
                    await fetch(`http://localhost:8082/api/v1/executives/${testResult.data.executive.id}`, {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
            } else {
                console.log(`❌ Test case ${i + 1} failed:`, testResult.message);
            }
        }
        
    } catch (error) {
        console.error('❌ Error testing API:', error.message);
    }
}

// Run the test
testExecutivesAPI();
