import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import { validateTenantExists } from '../middleware/tenantValidation.js';
import { withUsageTracking, trackApiUsage } from '../middleware/usageTracking.js';
import { uploadCustomerFiles, uploadImportFile, handleUploadError } from '../middleware/fileUpload.js';
import {
  getCustomers,
  getCustomerById,
  getCustomerBySerialNumber,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStats,
  getCustomerStatistics,
  searchMDContact,
  previewCustomerImport,
  executeCustomerImport,
  exportCustomers,
} from '../controllers/customerController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(validateTenantExists); // Validate tenant exists before processing requests
router.use(requireTenantAccess);
// Track API usage for all routes
router.use(trackApiUsage());

/**
 * @swagger
 * /customers:
 *   get:
 *     summary: Get all customers
 *     description: Retrieve a paginated list of customers with optional filtering and sorting
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of customers per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search term for company name, contact person, or customer code
 *       - in: query
 *         name: customerType
 *         schema:
 *           type: string
 *           enum: [prospect, customer, inactive, blacklisted]
 *         description: Filter by customer type
 *       - in: query
 *         name: industryId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by industry ID
 *       - in: query
 *         name: areaId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by area ID
 *       - in: query
 *         name: assignedExecutiveId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by assigned executive ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, company_name, customer_code]
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC, asc, desc]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         customers:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Customer'
 *             example:
 *               success: true
 *               message: "Customers retrieved successfully"
 *               data:
 *                 customers:
 *                   - id: "123e4567-e89b-12d3-a456-************"
 *                     company_name: "ABC Enterprises Pvt Ltd"
 *                     tally_serial_number: "TSN001"
 *                     contact_person: "John Doe"
 *                     mobile_number: "+91-9876543210"
 *                     email: "<EMAIL>"
 *                     status: "Active"
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 total: 150
 *                 totalPages: 15
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/', [
  requirePermission('customers.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must be less than 100 characters'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  query('assignedExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'company_name', 'customer_code'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
  validateRequest,
], getCustomers);

/**
 * @swagger
 * /customers/serial/{serialNumber}:
 *   get:
 *     summary: Get customer by serial number
 *     description: Retrieve customer details using their unique serial number (customer_code)
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serialNumber
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[A-Z0-9]+$'
 *           minLength: 2
 *           maxLength: 20
 *         description: Customer serial number (uppercase letters and numbers only)
 *         example: "TSN001"
 *     responses:
 *       200:
 *         description: Customer found successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         customer:
 *                           $ref: '#/components/schemas/Customer'
 *             example:
 *               success: true
 *               message: "Customer found successfully"
 *               data:
 *                 customer:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   company_name: "ABC Enterprises Pvt Ltd"
 *                   tally_serial_number: "TSN001"
 *                   contact_person: "John Doe"
 *                   mobile_number: "+91-9876543210"
 *                   email: "<EMAIL>"
 *                   status: "Active"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Customer not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Customer not found with serial number: TSN001"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/serial/:serialNumber', [
  requirePermission('customers.read'),
  param('serialNumber')
    .notEmpty()
    .withMessage('Customer serial number is required')
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer serial number must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer serial number must contain only uppercase letters and numbers'),
  validateRequest,
], getCustomerBySerialNumber);

/**
 * @route   GET /api/customers/search-md-contact
 * @desc    Search MD contact information for auto-fill functionality
 * @access  Private (requires customers.read permission)
 */
router.get('/search-md-contact', [
  requirePermission('customers.read'),
  query('name')
    .notEmpty()
    .withMessage('MD contact name is required')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('MD contact name must be between 2 and 100 characters'),
  validateRequest,
], searchMDContact);

/**
 * @swagger
 * /customers/stats:
 *   get:
 *     summary: Get customer statistics
 *     description: Retrieve comprehensive statistics about customers including counts, growth metrics, and breakdowns
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for statistics calculation
 *         example: "2024-01-01"
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for statistics calculation
 *         example: "2024-12-31"
 *       - in: query
 *         name: customerType
 *         schema:
 *           type: string
 *           enum: [prospect, customer, inactive, blacklisted]
 *         description: Filter statistics by customer type
 *       - in: query
 *         name: industryId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter statistics by industry ID
 *       - in: query
 *         name: areaId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter statistics by area ID
 *     responses:
 *       200:
 *         description: Customer statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CustomerStats'
 *             example:
 *               success: true
 *               message: "Customer statistics retrieved successfully"
 *               data:
 *                 total_customers: 150
 *                 active_customers: 140
 *                 inactive_customers: 10
 *                 new_this_month: 5
 *                 growth_percentage: 3.45
 *                 by_type:
 *                   prospect: 30
 *                   customer: 110
 *                   inactive: 10
 *                 by_industry:
 *                   - industry_name: "Manufacturing"
 *                     count: 45
 *                   - industry_name: "Retail"
 *                     count: 35
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/stats', [
  requirePermission('customers.read'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  validateRequest,
], getCustomerStats);

/**
 * @route   GET /api/customers/new
 * @desc    Get new customer form data (for frontend form initialization)
 * @access  Private (requires customers.read permission)
 */
router.get('/new', [
  requirePermission('customers.read'),
], (req, res) => {
  // Return empty customer object for form initialization
  res.json({
    success: true,
    data: {
      customer: {
        company_name: '',
        customer_code: '',
        business_type: 'individual',
        contact_person: '',
        email: '',
        phone: '',
        is_active: true
      }
    },
    message: 'New customer form data retrieved successfully'
  });
});

/**
 * @route   GET /api/customers/new/statistics
 * @desc    Get statistics for new customer form (empty stats)
 * @access  Private (requires customers.read permission)
 */
router.get('/new/statistics', [
  requirePermission('customers.read'),
], (req, res) => {
  // Return empty statistics for new customer
  res.json({
    success: true,
    data: {
      totalServices: 0,
      completedServices: 0,
      pendingServices: 0,
      totalRevenue: 0,
      totalPayments: 0,
      averageServiceValue: 0,
      servicesByStatus: {},
      recentServices: []
    },
    message: 'New customer statistics retrieved successfully'
  });
});

/**
 * @route   GET /api/customers/export
 * @desc    Export customers data to CSV/Excel
 * @access  Private (requires customers.read permission)
 */
router.get('/export', [
  requirePermission('customers.read'),
  query('format')
    .optional()
    .isIn(['csv', 'json'])
    .withMessage('Format must be csv or json'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  query('assignedExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Assigned Executive ID must be a valid UUID'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  // Additional filter validations
  query('amcStatus')
    .optional()
    .isIn(['all', 'active', 'expired', 'expiring_soon'])
    .withMessage('Invalid AMC status'),
  query('tssStatus')
    .optional()
    .isIn(['all', 'active', 'expired', 'expiring_soon'])
    .withMessage('Invalid TSS status'),
  query('licenseEdition')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('License edition must be less than 50 characters'),
  query('productType')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Product type must be less than 50 characters'),
  validateRequest,
], exportCustomers);

/**
 * @route   GET /api/customers/:id
 * @desc    Get customer by ID
 * @access  Private (requires customers.read permission)
 */
router.get('/:id', [
  requirePermission('customers.read'),
  param('id')
    .notEmpty()
    .withMessage('Customer ID is required')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('includeRelations')
    .optional()
    .isBoolean()
    .withMessage('includeRelations must be a boolean'),
  query('includeStats')
    .optional()
    .isBoolean()
    .withMessage('includeStats must be a boolean'),
  validateRequest,
], getCustomerById);

/**
 * @route   GET /api/customers/:id/statistics
 * @desc    Get customer statistics (services and payments)
 * @access  Private (requires customers.read permission)
 */
router.get('/:id/statistics', [
  requirePermission('customers.read'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  validateRequest,
], getCustomerStatistics);

/**
 * @swagger
 * /customers:
 *   post:
 *     summary: Create new customer
 *     description: Create a new customer with optional file uploads (TDL & Addons) and professional contact information
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - tally_serial_number
 *               - contact_person
 *               - mobile_number
 *             properties:
 *               company_name:
 *                 type: string
 *                 example: "ABC Enterprises Pvt Ltd"
 *                 nullable: true
 *               tally_serial_number:
 *                 type: string
 *                 example: "TSN001"
 *                 description: "Unique Tally Serial Number"
 *               contact_person:
 *                 type: string
 *                 example: "John Doe"
 *               mobile_number:
 *                 type: string
 *                 example: "+91-9876543210"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *                 nullable: true
 *               address:
 *                 type: string
 *                 example: "123 Business Street, City, State - 123456"
 *                 nullable: true
 *               gst_number:
 *                 type: string
 *                 example: "29ABCDE1234F1Z5"
 *                 nullable: true
 *               area_id:
 *                 type: string
 *                 format: uuid
 *                 nullable: true
 *               executive_id:
 *                 type: string
 *                 format: uuid
 *                 nullable: true
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive, Suspended]
 *                 example: "Active"
 *               tdl_file:
 *                 type: string
 *                 format: binary
 *                 description: "TDL file upload (optional)"
 *               addons_file:
 *                 type: string
 *                 format: binary
 *                 description: "Addons file upload (optional)"
 *               tdl_expiry_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-31"
 *                 description: "TDL expiry date (required if TDL file uploaded)"
 *               addons_expiry_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-31"
 *                 description: "Addons expiry date (required if Addons file uploaded)"
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerCreateRequest'
 *           example:
 *             company_name: "ABC Enterprises Pvt Ltd"
 *             tally_serial_number: "TSN001"
 *             contact_person: "John Doe"
 *             mobile_number: "+91-9876543210"
 *             email: "<EMAIL>"
 *             address: "123 Business Street, City, State - 123456"
 *             gst_number: "29ABCDE1234F1Z5"
 *             status: "Active"
 *     responses:
 *       201:
 *         description: Customer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         customer:
 *                           $ref: '#/components/schemas/Customer'
 *                         files:
 *                           type: object
 *                           properties:
 *                             tdl_uploaded:
 *                               type: boolean
 *                               example: true
 *                             addons_uploaded:
 *                               type: boolean
 *                               example: false
 *             example:
 *               success: true
 *               message: "Customer created successfully"
 *               data:
 *                 customer:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   company_name: "ABC Enterprises Pvt Ltd"
 *                   tally_serial_number: "TSN001"
 *                   contact_person: "John Doe"
 *                   mobile_number: "+91-9876543210"
 *                   email: "<EMAIL>"
 *                   status: "Active"
 *                   created_at: "2024-01-15T10:30:00Z"
 *                 files:
 *                   tdl_uploaded: true
 *                   addons_uploaded: false
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       409:
 *         description: Customer already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Customer with this Tally Serial Number already exists"
 *               errors:
 *                 tally_serial_number: "TSN001 is already registered"
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/', [
  requirePermission('customers.create'),
  uploadCustomerFiles,
  handleUploadError,
  ...withUsageTracking('customers', { operation: 'create' }),
  body('company_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\&\(\)\,\/\@\#\$\%\^\*\+\=\[\]\{\}\|\\\:\;\"\'\<\>\?\_\~\`]+$/)
    .withMessage('Company name contains invalid characters'),
  body('customer_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer code must contain only uppercase letters and numbers'),
  body('display_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['individual', 'partnership', 'private_limited', 'public_limited', 'llp', 'proprietorship', 'trust', 'society', 'government', 'other'])
    .withMessage('Invalid business type'),
  body('contact_person')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Contact person must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\.]+$/)
    .withMessage('Contact person name contains invalid characters'),
  body('email')
    .optional({ nullable: true, checkFalsy: true })
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  body('phone')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }

      const cleanPhone = value.trim();

      // International phone validation regex (supports country codes with spaces)
      const internationalPhoneRegex = /^[+]\d{1,4}[\s-]?\d{4,15}$/;

      // Check for international format first (with country code)
      if (internationalPhoneRegex.test(cleanPhone.replace(/[\s-]/g, ''))) {
        return true;
      }

      // Fallback to Indian format validation (for backward compatibility)
      const digitsOnly = cleanPhone.replace(/\D/g, '');
      if (digitsOnly.length === 10 && /^[6-9]\d{9}$/.test(digitsOnly)) {
        return true;
      }

      // More flexible phone validation that accepts various formats
      const flexiblePhoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
      if (flexiblePhoneRegex.test(cleanPhone)) {
        return true;
      }

      throw new Error('Please provide a valid phone number');
    }),
  body('gst_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 15) {
        throw new Error('GST number must be exactly 15 characters');
      }
      if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value)) {
        throw new Error('Invalid GST number format');
      }
      return true;
    }),
  body('pan_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 10) {
        throw new Error('PAN number must be exactly 10 characters');
      }
      if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
        throw new Error('Invalid PAN number format');
      }
      return true;
    }),
  body('address_line_1')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Address line 1 must be between 1 and 200 characters'),
  // City field is optional and not validated since frontend sends area_id as city
  body('city')
    .optional({ nullable: true, checkFalsy: true }),
  // State field is optional and not validated
  body('state')
    .optional({ nullable: true, checkFalsy: true }),
  // Postal code field is optional and not validated
  body('postal_code')
    .optional({ nullable: true, checkFalsy: true }),
  body('country')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('Country name contains invalid characters'),
  body('industry_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('tally_serial_number')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .customSanitizer(value => {
      // Convert to uppercase
      return value ? value.toUpperCase() : value;
    })
    .isLength({ min: 1, max: 50 })
    .withMessage('Tally Serial Number must be between 1 and 50 characters'),
  body('assigned_executive_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Credit days must be between 0 and 365'),
  body('website')
    .optional({ nullable: true, checkFalsy: true })
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('employee_count')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee count must be a positive integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('notes')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters'),
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be an object'),
  validateRequest,
], createCustomer);

/**
 * @swagger
 * /customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     description: Retrieve detailed information about a specific customer including service call statistics
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Customer ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         customer:
 *                           $ref: '#/components/schemas/Customer'
 *                         statistics:
 *                           type: object
 *                           properties:
 *                             total_service_calls:
 *                               type: integer
 *                               example: 15
 *                             active_service_calls:
 *                               type: integer
 *                               example: 3
 *                             completed_service_calls:
 *                               type: integer
 *                               example: 12
 *                             total_service_time:
 *                               type: integer
 *                               example: 18000
 *                               description: "Total time in seconds"
 *             example:
 *               success: true
 *               message: "Customer retrieved successfully"
 *               data:
 *                 customer:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   company_name: "ABC Enterprises Pvt Ltd"
 *                   tally_serial_number: "TSN001"
 *                   contact_person: "John Doe"
 *                   mobile_number: "+91-9876543210"
 *                   email: "<EMAIL>"
 *                   status: "Active"
 *                 statistics:
 *                   total_service_calls: 15
 *                   active_service_calls: 3
 *                   completed_service_calls: 12
 *                   total_service_time: 18000
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Customer not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Customer not found"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 *   put:
 *     summary: Update customer
 *     description: Update customer information with optional file uploads and professional contact details
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Customer ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               company_name:
 *                 type: string
 *                 example: "ABC Enterprises Pvt Ltd - Updated"
 *                 nullable: true
 *               contact_person:
 *                 type: string
 *                 example: "John Doe"
 *               mobile_number:
 *                 type: string
 *                 example: "+91-9876543210"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *                 nullable: true
 *               address:
 *                 type: string
 *                 example: "456 Updated Business Street, City, State - 123456"
 *                 nullable: true
 *               gst_number:
 *                 type: string
 *                 example: "29ABCDE1234F1Z5"
 *                 nullable: true
 *               area_id:
 *                 type: string
 *                 format: uuid
 *                 nullable: true
 *               executive_id:
 *                 type: string
 *                 format: uuid
 *                 nullable: true
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive, Suspended]
 *                 example: "Active"
 *               tdl_file:
 *                 type: string
 *                 format: binary
 *                 description: "New TDL file upload (optional)"
 *               addons_file:
 *                 type: string
 *                 format: binary
 *                 description: "New Addons file upload (optional)"
 *               tdl_expiry_date:
 *                 type: string
 *                 format: date
 *                 example: "2025-12-31"
 *               addons_expiry_date:
 *                 type: string
 *                 format: date
 *                 example: "2025-12-31"
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerUpdateRequest'
 *           example:
 *             company_name: "ABC Enterprises Pvt Ltd - Updated"
 *             contact_person: "John Doe"
 *             mobile_number: "+91-9876543210"
 *             email: "<EMAIL>"
 *             address: "456 Updated Business Street, City, State - 123456"
 *             status: "Active"
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         customer:
 *                           $ref: '#/components/schemas/Customer'
 *                         files:
 *                           type: object
 *                           properties:
 *                             tdl_updated:
 *                               type: boolean
 *                               example: true
 *                             addons_updated:
 *                               type: boolean
 *                               example: false
 *                             previous_files_backed_up:
 *                               type: boolean
 *                               example: true
 *             example:
 *               success: true
 *               message: "Customer updated successfully"
 *               data:
 *                 customer:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   company_name: "ABC Enterprises Pvt Ltd - Updated"
 *                   contact_person: "John Doe"
 *                   mobile_number: "+91-9876543210"
 *                   email: "<EMAIL>"
 *                   status: "Active"
 *                   updated_at: "2024-01-15T11:30:00Z"
 *                 files:
 *                   tdl_updated: true
 *                   addons_updated: false
 *                   previous_files_backed_up: true
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Customer not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Customer not found"
 *               errors: {}
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.put('/:id', [
  requirePermission('customers.update'),
  uploadCustomerFiles,
  handleUploadError,
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('company_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\&\(\)\,\/\@\#\$\%\^\*\+\=\[\]\{\}\|\\\:\;\"\'\<\>\?\_\~\`]+$/)
    .withMessage('Company name contains invalid characters'),
  body('customer_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Customer code must contain only uppercase letters and numbers'),
  body('display_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['individual', 'partnership', 'private_limited', 'public_limited', 'llp', 'proprietorship', 'trust', 'society', 'government', 'other'])
    .withMessage('Invalid business type'),
  body('contact_person')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Contact person must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s\.]+$/)
    .withMessage('Contact person name contains invalid characters'),
  body('email')
    .optional({ nullable: true, checkFalsy: true })
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  body('phone')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }

      const cleanPhone = value.trim();

      // International phone validation regex (supports country codes with spaces)
      const internationalPhoneRegex = /^[+]\d{1,4}[\s-]?\d{4,15}$/;

      // Check for international format first (with country code)
      if (internationalPhoneRegex.test(cleanPhone.replace(/[\s-]/g, ''))) {
        return true;
      }

      // Fallback to Indian format validation (for backward compatibility)
      const digitsOnly = cleanPhone.replace(/\D/g, '');
      if (digitsOnly.length === 10 && /^[6-9]\d{9}$/.test(digitsOnly)) {
        return true;
      }

      // More flexible phone validation that accepts various formats
      const flexiblePhoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
      if (flexiblePhoneRegex.test(cleanPhone)) {
        return true;
      }

      throw new Error('Please provide a valid phone number');
    }),
  body('gst_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 15) {
        throw new Error('GST number must be exactly 15 characters');
      }
      if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(value)) {
        throw new Error('Invalid GST number format');
      }
      return true;
    }),
  body('pan_number')
    .optional({ nullable: true, checkFalsy: true })
    .customSanitizer((value) => {
      // Convert empty strings to null
      if (!value || typeof value !== 'string' || value.trim() === '') {
        return null;
      }
      return value.trim().toUpperCase();
    })
    .custom((value) => {
      // Skip validation if value is null or empty
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (value.length !== 10) {
        throw new Error('PAN number must be exactly 10 characters');
      }
      if (!/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(value)) {
        throw new Error('Invalid PAN number format');
      }
      return true;
    }),
  body('address_line_1')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Address line 1 must be between 1 and 200 characters'),
  // City field is optional and not validated since frontend sends area_id as city
  body('city')
    .optional({ nullable: true, checkFalsy: true }),
  // State field is optional and not validated
  body('state')
    .optional({ nullable: true, checkFalsy: true }),
  // Postal code field is optional and not validated
  body('postal_code')
    .optional({ nullable: true, checkFalsy: true }),
  body('country')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Country must be between 1 and 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('Country name contains invalid characters'),
  body('industry_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('tally_serial_number')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .customSanitizer(value => {
      // Convert to uppercase
      return value ? value.toUpperCase() : value;
    })
    .isLength({ min: 1, max: 50 })
    .withMessage('Tally Serial Number must be between 1 and 50 characters'),
  body('assigned_executive_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('credit_limit')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Credit limit must be a positive number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0, max: 365 })
    .withMessage('Credit days must be between 0 and 365'),
  body('website')
    .optional({ nullable: true, checkFalsy: true })
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('annual_revenue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Annual revenue must be a positive number'),
  body('employee_count')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee count must be a positive integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  body('notes')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Notes must not exceed 1000 characters'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('tags.*')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters'),
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be an object'),
  validateRequest,
], updateCustomer);

/**
 * @swagger
 * /customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     description: Delete a customer (soft delete by default, hard delete with force parameter). This will also handle associated service calls and files.
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Customer ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: force
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Force hard delete (permanently removes customer and all data)
 *         example: false
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         deletedCustomer:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: string
 *                               format: uuid
 *                               example: "123e4567-e89b-12d3-a456-************"
 *                             company_name:
 *                               type: string
 *                               example: "ABC Enterprises Pvt Ltd"
 *                             tally_serial_number:
 *                               type: string
 *                               example: "TSN001"
 *                             deletion_type:
 *                               type: string
 *                               enum: [soft, hard]
 *                               example: "soft"
 *                         associatedData:
 *                           type: object
 *                           properties:
 *                             service_calls_affected:
 *                               type: integer
 *                               example: 5
 *                               description: "Number of service calls affected by deletion"
 *                             files_removed:
 *                               type: array
 *                               items:
 *                                 type: string
 *                               example: ["tdl_file.tdl", "addons_file.zip"]
 *                             can_be_restored:
 *                               type: boolean
 *                               example: true
 *                               description: "Whether the customer can be restored (soft delete only)"
 *             example:
 *               success: true
 *               message: "Customer deleted successfully (soft delete)"
 *               data:
 *                 deletedCustomer:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   company_name: "ABC Enterprises Pvt Ltd"
 *                   tally_serial_number: "TSN001"
 *                   deletion_type: "soft"
 *                 associatedData:
 *                   service_calls_affected: 5
 *                   files_removed: []
 *                   can_be_restored: true
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Customer not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Customer not found"
 *               errors: {}
 *       409:
 *         description: Cannot delete customer
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Cannot delete customer with active service calls"
 *               errors:
 *                 active_service_calls: "Customer has 3 active service calls. Complete or cancel them before deletion."
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.delete('/:id', [
  requirePermission('customers.delete'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('force')
    .optional()
    .isBoolean()
    .withMessage('Force delete must be a boolean'),
  query('reason')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Deletion reason must be between 5 and 200 characters'),
  validateRequest,
], deleteCustomer);

/**
 * @route   POST /api/customers/import/preview
 * @desc    Preview customer import data
 * @access  Private (requires customers.create permission)
 */
router.post('/import/preview', [
  requirePermission('customers.create'),
  uploadImportFile,
  handleUploadError,
], previewCustomerImport);

/**
 * @route   POST /api/customers/import/execute
 * @desc    Execute customer import
 * @access  Private (requires customers.create permission)
 */
router.post('/import/execute', [
  requirePermission('customers.create'),
  uploadImportFile,
  handleUploadError,
], executeCustomerImport);



export default router;
