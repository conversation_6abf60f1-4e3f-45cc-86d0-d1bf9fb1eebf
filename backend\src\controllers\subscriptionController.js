import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

/**
 * Get all subscription plans
 */
export const getSubscriptionPlans = async (req, res) => {
  try {
    const plans = await models.SubscriptionPlan.findAll({
      where: { is_active: true },
      order: [['sort_order', 'ASC']],
    });

    res.json({
      success: true,
      data: { plans },
    });
  } catch (error) {
    logger.error('Get subscription plans error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subscription plans',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get current tenant subscription
 */
export const getCurrentSubscription = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
      include: [
        {
          model: models.SubscriptionPlan,
          as: 'plan',
        },
        {
          model: models.Invoice,
          as: 'invoices',
          limit: 5,
          order: [['created_at', 'DESC']],
        },
      ],
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'No subscription found for this tenant',
      });
    }

    // Get usage summary
    const usageSummary = await models.UsageRecord.getUsageSummary(tenantId);

    res.json({
      success: true,
      data: {
        subscription,
        usage: usageSummary,
      },
    });
  } catch (error) {
    logger.error('Get current subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subscription',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create subscription checkout session
 */
export const createCheckoutSession = async (req, res) => {
  try {
    const { planId, interval = 'monthly' } = req.body;
    const tenantId = req.user.tenant.id;

    // Get the plan
    const plan = await models.SubscriptionPlan.findByPk(planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Subscription plan not found',
      });
    }

    // Get tenant
    const tenant = await models.Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found',
      });
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.getStripePrice(interval),
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.FRONTEND_URL}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL}/billing/plans`,
      customer_email: tenant.email || req.user.email,
      metadata: {
        tenant_id: tenantId,
        plan_id: planId,
        interval,
      },
    });

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url,
      },
    });
  } catch (error) {
    logger.error('Create checkout session error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create checkout session',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Cancel subscription
 */
export const cancelSubscription = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { cancelAtPeriodEnd = true } = req.body;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'No subscription found',
      });
    }

    // Cancel in Stripe if it exists
    if (subscription.stripe_subscription_id) {
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: cancelAtPeriodEnd,
      });
    }

    // Update local subscription
    await subscription.update({
      cancel_at_period_end: cancelAtPeriodEnd,
      canceled_at: cancelAtPeriodEnd ? null : new Date(),
    });

    logger.info('Subscription canceled:', {
      tenantId,
      subscriptionId: subscription.id,
      cancelAtPeriodEnd,
    });

    res.json({
      success: true,
      message: cancelAtPeriodEnd 
        ? 'Subscription will be canceled at the end of the current period'
        : 'Subscription canceled immediately',
      data: { subscription },
    });
  } catch (error) {
    logger.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Reactivate subscription
 */
export const reactivateSubscription = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'No subscription found',
      });
    }

    // Reactivate in Stripe if it exists
    if (subscription.stripe_subscription_id) {
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        cancel_at_period_end: false,
      });
    }

    // Update local subscription
    await subscription.update({
      cancel_at_period_end: false,
      canceled_at: null,
    });

    logger.info('Subscription reactivated:', {
      tenantId,
      subscriptionId: subscription.id,
    });

    res.json({
      success: true,
      message: 'Subscription reactivated successfully',
      data: { subscription },
    });
  } catch (error) {
    logger.error('Reactivate subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reactivate subscription',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get subscription usage
 */
export const getSubscriptionUsage = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { months = 3 } = req.query;

    const subscription = await models.Subscription.findOne({
      where: { tenant_id: tenantId },
      include: [
        {
          model: models.SubscriptionPlan,
          as: 'plan',
        },
      ],
    });

    if (!subscription) {
      return res.status(404).json({
        success: false,
        message: 'No subscription found',
      });
    }

    // Get current usage
    const currentUsage = await models.UsageRecord.getUsageSummary(tenantId);

    // Get usage history
    const usageHistory = {};
    const metrics = ['users', 'customers', 'service_calls', 'storage_gb'];
    
    for (const metric of metrics) {
      usageHistory[metric] = await models.UsageRecord.getUsageHistory(
        tenantId,
        metric,
        parseInt(months)
      );
    }

    // Check limits
    const limits = {
      users: subscription.plan.max_users,
      customers: subscription.plan.max_customers,
      service_calls: subscription.plan.max_service_calls,
      storage_gb: subscription.plan.max_storage_gb,
    };

    const limitCheck = await models.UsageRecord.checkLimits(tenantId, limits);

    res.json({
      success: true,
      data: {
        subscription,
        currentUsage,
        usageHistory,
        limits,
        limitCheck,
      },
    });
  } catch (error) {
    logger.error('Get subscription usage error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve subscription usage',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
