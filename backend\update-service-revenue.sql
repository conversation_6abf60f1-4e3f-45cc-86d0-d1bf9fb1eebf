-- Update Service Calls with Revenue Data
-- This script adds revenue amounts to existing service calls for analytics testing

-- Update service calls with random revenue amounts based on billing type
UPDATE service_calls 
SET 
  call_billing_type = CASE 
    WHEN id % 3 = 0 THEN 'free_call'
    WHEN id % 3 = 1 THEN 'amc_call' 
    ELSE 'per_call'
  END,
  service_charges = CASE 
    WHEN id % 3 = 0 THEN 0  -- Free calls
    WHEN id % 3 = 1 THEN (RANDOM() * 2000 + 500)::DECIMAL(10,2)  -- AMC calls: 500-2500
    ELSE (RANDOM() * 5000 + 1000)::DECIMAL(10,2)  -- Per calls: 1000-6000
  END,
  travel_charges = CASE 
    WHEN id % 3 = 0 THEN 0  -- Free calls
    WHEN id % 3 = 1 THEN (RANDOM() * 500)::DECIMAL(8,2)  -- AMC calls: 0-500
    ELSE (RANDOM() * 1000)::DECIMAL(8,2)  -- Per calls: 0-1000
  END,
  is_billable = CASE 
    WHEN id % 3 = 0 THEN false  -- Free calls
    ELSE true
  END,
  hourly_rate = CASE 
    WHEN id % 3 = 2 THEN (RANDOM() * 500 + 200)::DECIMAL(8,2)  -- Per calls only
    ELSE NULL
  END
WHERE total_amount = 0 OR total_amount IS NULL;

-- Update total_amount based on service_charges + travel_charges
UPDATE service_calls 
SET total_amount = COALESCE(service_charges, 0) + COALESCE(travel_charges, 0)
WHERE total_amount = 0 OR total_amount IS NULL;

-- Add some sample sales data if the sales table exists and is empty
INSERT INTO sales (
  id, tenant_id, customer_id, created_by, sale_number, total_amount, 
  sale_date, payment_status, sale_type, created_at, updated_at
)
SELECT 
  gen_random_uuid(),
  tenant_id,
  customer_id,
  created_by,
  'SALE' || LPAD((ROW_NUMBER() OVER())::TEXT, 6, '0'),
  (RANDOM() * 50000 + 10000)::DECIMAL(10,2),
  call_date,
  CASE 
    WHEN RANDOM() < 0.7 THEN 'paid'
    WHEN RANDOM() < 0.9 THEN 'pending'
    ELSE 'overdue'
  END,
  'new',
  created_at,
  updated_at
FROM service_calls 
WHERE tenant_id IS NOT NULL 
  AND customer_id IS NOT NULL 
  AND created_by IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM sales WHERE sales.customer_id = service_calls.customer_id)
LIMIT 50;

-- Show summary of revenue data
SELECT 
  'Service Calls Revenue Summary' as summary_type,
  COUNT(*) as total_calls,
  COUNT(CASE WHEN call_billing_type = 'free_call' THEN 1 END) as free_calls,
  COUNT(CASE WHEN call_billing_type = 'amc_call' THEN 1 END) as amc_calls,
  COUNT(CASE WHEN call_billing_type = 'per_call' THEN 1 END) as per_calls,
  SUM(total_amount) as total_revenue,
  AVG(total_amount) as avg_revenue_per_call
FROM service_calls 
WHERE tenant_id IS NOT NULL;

SELECT 
  'Sales Revenue Summary' as summary_type,
  COUNT(*) as total_sales,
  SUM(total_amount) as total_sales_revenue,
  AVG(total_amount) as avg_sale_amount
FROM sales 
WHERE tenant_id IS NOT NULL;
