/**
 * Chart Themes and Color Schemes for TallyCRM
 * Consistent color palettes and styling for all chart components
 */

// Primary color palette based on TallyCRM theme
export const COLORS = {
  primary: '#1d5795',
  secondary: '#2563eb',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  gray: '#6b7280',
  light: '#f3f4f6'
};

// Chart color schemes
export const CHART_COLORS = {
  // Primary palette for main charts
  primary: [
    '#1d5795', '#2563eb', '#10b981', '#f59e0b', '#ef4444', 
    '#06b6d4', '#8b5cf6', '#f97316', '#84cc16', '#ec4899'
  ],
  
  // Status colors
  status: {
    active: '#10b981',
    inactive: '#6b7280',
    pending: '#f59e0b',
    completed: '#10b981',
    cancelled: '#ef4444',
    'in-progress': '#2563eb',
    open: '#06b6d4',
    closed: '#6b7280'
  },
  
  // Priority colors
  priority: {
    high: '#ef4444',
    urgent: '#dc2626',
    medium: '#f59e0b',
    normal: '#2563eb',
    low: '#10b981'
  },
  
  // Revenue/Financial colors
  financial: {
    revenue: '#10b981',
    expense: '#ef4444',
    profit: '#1d5795',
    loss: '#dc2626'
  },
  
  // Gradient definitions
  gradients: {
    primary: ['#1d5795', '#2563eb'],
    success: ['#10b981', '#059669'],
    warning: ['#f59e0b', '#d97706'],
    danger: ['#ef4444', '#dc2626']
  }
};

// Chart styling themes
export const CHART_THEMES = {
  default: {
    backgroundColor: '#ffffff',
    textColor: '#374151',
    gridColor: '#e5e7eb',
    tooltipBg: '#ffffff',
    tooltipBorder: '#d1d5db',
    fontSize: 12,
    fontFamily: 'Inter, system-ui, sans-serif'
  },
  
  dark: {
    backgroundColor: '#1f2937',
    textColor: '#f9fafb',
    gridColor: '#374151',
    tooltipBg: '#374151',
    tooltipBorder: '#4b5563',
    fontSize: 12,
    fontFamily: 'Inter, system-ui, sans-serif'
  }
};

// Responsive breakpoints for charts
export const CHART_BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
  large: 1280
};

// Default chart dimensions
export const CHART_DIMENSIONS = {
  small: { width: 300, height: 200 },
  medium: { width: 400, height: 250 },
  large: { width: 600, height: 350 },
  xlarge: { width: 800, height: 400 }
};

// Animation configurations
export const CHART_ANIMATIONS = {
  duration: 750,
  easing: 'ease-out',
  delay: 0
};

// Common chart margins
export const CHART_MARGINS = {
  small: { top: 10, right: 10, bottom: 20, left: 20 },
  medium: { top: 20, right: 20, bottom: 40, left: 40 },
  large: { top: 30, right: 30, bottom: 60, left: 60 }
};
