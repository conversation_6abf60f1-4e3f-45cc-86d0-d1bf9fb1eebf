import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';
import emailService from '../services/emailService.js';
import TimeTrackingService from '../services/timeTrackingService.js';
import { notificationService } from '../services/NotificationService.js';

/**
 * Get all service calls with pagination and filters
 */
export const getServiceCalls = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerId,
      statusId,
      assignedTo,
      callType,
      priority,
      isUnderAmc,
      dateFrom,
      dateTo,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      // New filter parameters
      callBillingType,
      isOverdue,
      statusCategory,
      scheduledDateFrom,
      scheduledDateTo,
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // Add tenant filter if user has tenant
    if (req.user && req.user.tenant && req.user.tenant.id) {
      where.tenant_id = req.user.tenant.id;
    }

    // Apply filters with performance optimization
    if (search) {
      const searchTerm = search.trim();

      // If search term is short, limit the search scope for better performance
      if (searchTerm.length < 3) {
        // For short terms, only search in indexed fields
        where[Op.or] = [
          { call_number: { [Op.iLike]: `%${searchTerm}%` } },
          { '$customer.customer_code$': { [Op.iLike]: `%${searchTerm}%` } },
        ];
      } else {
        // For longer terms, use full search with optimized order (indexed fields first)
        where[Op.or] = [
          // Indexed fields first for better performance
          { call_number: { [Op.iLike]: `%${searchTerm}%` } },
          { tally_serial_number: { [Op.iLike]: `%${searchTerm}%` } },
          { subject: { [Op.iLike]: `%${searchTerm}%` } },
          { description: { [Op.iLike]: `%${searchTerm}%` } },
          // Customer fields (with joins)
          { '$customer.company_name$': { [Op.iLike]: `%${searchTerm}%` } },
          { '$customer.display_name$': { [Op.iLike]: `%${searchTerm}%` } },
          { '$customer.customer_code$': { [Op.iLike]: `%${searchTerm}%` } },
          // Executive fields (with joins)
          { '$assignedExecutive.first_name$': { [Op.iLike]: `%${searchTerm}%` } },
          { '$assignedExecutive.last_name$': { [Op.iLike]: `%${searchTerm}%` } },
          // Status and type fields (with joins)
          { '$status.name$': { [Op.iLike]: `%${searchTerm}%` } },
          { '$typeOfCall.name$': { [Op.iLike]: `%${searchTerm}%` } },
        ];
      }
    }

    if (customerId) {
      where.customer_id = customerId;
    }

    if (statusId) {
      where.status_id = statusId;
    }

    // Handle status filtering by code (for frontend compatibility)
    if (req.query.status && req.query.status !== 'all') {
      const statusCode = req.query.status.toUpperCase();
      const status = await models.CallStatus.findOne({
        where: { code: statusCode, is_active: true },
        attributes: ['id']
      });
      if (status) {
        where.status_id = status.id;
      }
    }

    if (assignedTo) {
      where.assigned_to = assignedTo;
    }

    if (callType) {
      where.call_type = callType;
    }

    if (priority) {
      where.priority = priority;
    }

    if (isUnderAmc !== undefined) {
      where.is_under_amc = isUnderAmc === 'true';
    }

    if (dateFrom || dateTo) {
      where.created_at = {};
      if (dateFrom) {
        // Set to start of day in local timezone
        const startDate = new Date(dateFrom + 'T00:00:00');
        where.created_at[Op.gte] = startDate;
      }
      if (dateTo) {
        // Set to end of day in local timezone
        const endDate = new Date(dateTo + 'T23:59:59');
        where.created_at[Op.lte] = endDate;
      }
    }

    // Call billing type filter
    if (callBillingType) {
      if (callBillingType === 'free_call') {
        // Free call filter - match multiple conditions like in analytics
        where[Op.or] = [
          { call_billing_type: 'free_call' },
          {
            [Op.and]: [
              { is_billable: false },
              { is_under_amc: false }
            ]
          },
          { service_charges: { [Op.or]: [0, null] } }
        ];
      } else {
        where.call_billing_type = callBillingType;
      }
    }

    // Scheduled date range filter
    if (scheduledDateFrom || scheduledDateTo) {
      where.scheduled_date = {};
      if (scheduledDateFrom) where.scheduled_date[Op.gte] = new Date(scheduledDateFrom);
      if (scheduledDateTo) where.scheduled_date[Op.lte] = new Date(scheduledDateTo);
    }

    // Build includes array dynamically to handle missing associations
    const includes = [
      {
        model: models.Customer,
        as: 'customer',
        attributes: ['id', 'customer_code', 'company_name', 'phone', 'email'],
        required: false,
      },
      {
        model: models.CustomerContact,
        as: 'contactPerson',
        attributes: ['id', 'first_name', 'last_name', 'phone', 'email'],
        required: false,
      },
      {
        model: models.CallStatus,
        as: 'status',
        attributes: ['id', 'name', 'code', 'color', 'category'],
        required: false,
      },
      {
        model: models.Executive,
        as: 'assignedExecutive',
        attributes: ['id', 'first_name', 'last_name', 'phone', 'email'],
        required: false,
      },
      {
        model: models.NatureOfIssue,
        as: 'natureOfIssue',
        attributes: ['id', 'name', 'category', 'severity'],
        required: false,
      },
      {
        model: models.Area,
        as: 'area',
        attributes: ['id', 'name', 'city', 'state'],
        required: false,
      },
      {
        model: models.User,
        as: 'creator',
        attributes: ['id', 'first_name', 'last_name', 'email'],
        required: false,
      },
    ];

    // Add TypeOfCall association if it exists (after migration)
    if (models.TypeOfCall) {
      includes.push({
        model: models.TypeOfCall,
        as: 'typeOfCall',
        attributes: ['id', 'name', 'code', 'category', 'service_type'],
        required: false,
      });
    }

    // Add ProductsIssues association if it exists
    if (models.ProductsIssues) {
      includes.push({
        model: models.ProductsIssues,
        as: 'productsIssue',
        attributes: ['id', 'name', 'category', 'description'],
        required: false,
      });
    }

    // Handle overdue filter - must be done after includes are set up
    if (isOverdue === 'true') {
      where.scheduled_date = {
        ...where.scheduled_date,
        [Op.lt]: new Date(), // Scheduled date is in the past
      };
      where['$status.category$'] = 'open'; // Still open/pending
    }

    // Handle status category filter
    if (statusCategory) {
      where['$status.category$'] = statusCategory;
    }

    const { count, rows: serviceCalls } = await models.ServiceCall.findAndCountAll({
      where,
      include: includes,
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    // Enhance service calls with calculated time tracking values
    const enhancedServiceCalls = serviceCalls.map(serviceCall => {
      const timeTrackingSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);

      // Calculate hours spent from actual_hours or time tracking data
      let hoursSpent = parseFloat(serviceCall.actual_hours || 0);
      if (hoursSpent === 0 && timeTrackingSummary.total_time_seconds > 0) {
        hoursSpent = (timeTrackingSummary.total_time_seconds / 3600);
      }

      // Calculate estimated hours
      let estimatedHours = parseFloat(serviceCall.estimated_hours || 0);

      // Calculate progress percentage
      let progressPercentage = 0;
      if (estimatedHours > 0 && hoursSpent > 0) {
        progressPercentage = Math.round((hoursSpent / estimatedHours) * 100);
      } else if (serviceCall.status?.code === 'COMPLETED') {
        progressPercentage = 100;
      }

      // Add calculated values to the service call
      return {
        ...serviceCall.toJSON(),
        calculated_hours_spent: parseFloat(hoursSpent.toFixed(2)),
        calculated_estimated_hours: parseFloat(estimatedHours.toFixed(2)),
        calculated_progress_percentage: progressPercentage,
        formatted_duration: timeTrackingSummary.total_time_formatted,
        // Include time tracking data for frontend timer display
        total_time_seconds: timeTrackingSummary.total_time_seconds,
        total_time_minutes: timeTrackingSummary.total_time_minutes,
        time_tracking_summary: timeTrackingSummary
      };
    });

    res.json({
      success: true,
      data: {
        serviceCalls: enhancedServiceCalls,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get service calls error:', error);

    // Provide more detailed error information in development
    const errorDetails = process.env.NODE_ENV === 'development' ? {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      name: error.name
    } : undefined;

    res.status(500).json({
      success: false,
      message: 'Unable to load service calls. Please try again.',
      error: errorDetails,
      details: {
        userFriendlyMessage: 'Unable to load service calls. Please try again.'
      }
    });
  }
};

/**
 * Get service call by ID
 */
export const getServiceCallById = async (req, res) => {
  try {
    const { id } = req.params;

    // Build includes array with conditional associations
    const includes = [
      {
        model: models.Customer,
        as: 'customer',
        include: [
          {
            model: models.Industry,
            as: 'industry',
          },
          {
            model: models.Area,
            as: 'area',
          },
        ],
      },
      {
        model: models.CustomerContact,
        as: 'contactPerson',
        include: [
          {
            model: models.Designation,
            as: 'designation',
          },
        ],
      },
      {
        model: models.CustomerTSS,
        as: 'tss',
        include: [
          {
            model: models.LicenseEdition,
            as: 'licenseEdition',
          },
        ],
      },
      {
        model: models.CustomerAMC,
        as: 'amc',
      },
      {
        model: models.CallStatus,
        as: 'status',
      },
      {
        model: models.Executive,
        as: 'assignedExecutive',
        include: [
          {
            model: models.Designation,
            as: 'designation',
          },
        ],
      },
      {
        model: models.NatureOfIssue,
        as: 'natureOfIssue',
      },
      {
        model: models.Area,
        as: 'area',
      },
      {
        model: models.ServiceCallItem,
        as: 'items',
        include: [
          {
            model: models.TallyProduct,
            as: 'product',
          },
          {
            model: models.AdditionalService,
            as: 'additionalService',
          },
        ],
      },
      {
        model: models.User,
        as: 'creator',
        attributes: ['id', 'first_name', 'last_name', 'email'],
      },
    ];

    // Add conditional associations
    if (models.TypeOfCall) {
      includes.push({
        model: models.TypeOfCall,
        as: 'typeOfCall',
        attributes: ['id', 'name', 'code', 'category', 'service_type'],
        required: false,
      });
    }

    if (models.ProductsIssues) {
      includes.push({
        model: models.ProductsIssues,
        as: 'productsIssue',
        attributes: ['id', 'name', 'category', 'description'],
        required: false,
      });
    }

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: includes,
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    // Calculate additional time tracking values for frontend display
    const timeTrackingSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);

    // Calculate hours spent from actual_hours or time tracking data
    let hoursSpent = parseFloat(serviceCall.actual_hours || 0);
    if (hoursSpent === 0 && timeTrackingSummary.total_time_seconds > 0) {
      hoursSpent = (timeTrackingSummary.total_time_seconds / 3600);
    }

    // Calculate estimated hours
    let estimatedHours = parseFloat(serviceCall.estimated_hours || 0);

    // Calculate progress percentage
    let progressPercentage = 0;
    if (estimatedHours > 0 && hoursSpent > 0) {
      progressPercentage = Math.round((hoursSpent / estimatedHours) * 100);
    } else if (serviceCall.status.code === 'COMPLETED') {
      progressPercentage = 100;
    }

    // Add calculated values to the response
    const enhancedServiceCall = {
      ...serviceCall.toJSON(),
      calculated_hours_spent: parseFloat(hoursSpent.toFixed(2)),
      calculated_estimated_hours: parseFloat(estimatedHours.toFixed(2)),
      calculated_progress_percentage: progressPercentage,
      formatted_duration: timeTrackingSummary.total_time_formatted,
      time_tracking_summary: timeTrackingSummary
    };

    res.json({
      success: true,
      data: { serviceCall: enhancedServiceCall },
    });

  } catch (error) {
    logger.error('Get service call by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service call',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new service call
 */
export const createServiceCall = async (req, res) => {
  try {
    // Handle default status logic based on customer type
    let defaultStatusId = req.body.status_id;

    if (!defaultStatusId) {
      // Determine if this is a lead (no Tally Serial Number) or existing customer
      const isLead = !req.body.tally_serial_number || req.body.tally_serial_number.trim() === '';

      if (isLead) {
        // For leads: Default status = "Follow Up Customer"
        const followUpStatus = await models.CallStatus.findOne({
          where: {
            tenant_id: req.user.tenant.id,
            [models.Sequelize.Op.or]: [
              { code: 'FOLLOW_UP_CUSTOMER' },
              { name: { [models.Sequelize.Op.iLike]: '%follow up customer%' } }
            ]
          }
        });
        defaultStatusId = followUpStatus?.id;
      } else {
        // For existing customers: Default status = "Open"
        const openStatus = await models.CallStatus.findOne({
          where: {
            tenant_id: req.user.tenant.id,
            [models.Sequelize.Op.or]: [
              { code: 'OPEN' },
              { name: { [models.Sequelize.Op.iLike]: '%open%' } }
            ]
          }
        });
        defaultStatusId = openStatus?.id;
      }
    }

    const serviceCallData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
      status_id: defaultStatusId,
      // Provide default values for required fields that might be missing
      subject: req.body.subject || 'Service Call', // Default subject
      description: req.body.description || 'Service call created via system', // Default description
      // Auto-populate additional customer information in background
      email_address: req.body.email_address,
      designation: req.body.designation,
      tally_version: req.body.tally_version,
      tss_status: req.body.tss_status,
      tss_expiry: req.body.tss_expiry,
    };

    // Set default call_billing_type if not provided
    if (!serviceCallData.call_billing_type) {
      // Determine billing type based on available information
      if (serviceCallData.is_under_amc === true || serviceCallData.amc_id) {
        serviceCallData.call_billing_type = 'amc_call';
      } else if (serviceCallData.service_charges > 0 || serviceCallData.is_billable === true) {
        serviceCallData.call_billing_type = 'per_call';
      } else {
        // Default to free call
        serviceCallData.call_billing_type = 'free_call';
      }
    }

    // Debug: Log the data being sent to database
    console.log('🔍 Service call data being sent to database:');
    console.log('Field count:', Object.keys(serviceCallData).length);

    // Check for date fields specifically and clean them up
    const dateFields = [
      'started_at', 'call_end_time', 'scheduled_date', 'booking_date', 'call_date',
      'completed_at', 'closed_at', 'follow_up_date', 'tss_expiry'
    ];
    dateFields.forEach(field => {
      if (serviceCallData[field] !== undefined) {
        console.log(`${field}:`, serviceCallData[field], typeof serviceCallData[field]);

        // If it's a string, check if it's a valid date
        if (typeof serviceCallData[field] === 'string') {
          if (serviceCallData[field] === '' || serviceCallData[field].includes('Invalid')) {
            console.log(`❌ Removing invalid date field: ${field}`);
            delete serviceCallData[field];
          } else {
            // Try to parse it
            const dateValue = new Date(serviceCallData[field]);
            if (isNaN(dateValue.getTime())) {
              console.log(`❌ Removing unparseable date field: ${field}`);
              delete serviceCallData[field];
            } else {
              console.log(`✅ Converting string date to Date object: ${field}`);
              serviceCallData[field] = dateValue;
            }
          }
        } else if (serviceCallData[field] instanceof Date) {
          console.log(`  ${field} is valid date:`, !isNaN(serviceCallData[field].getTime()));
          if (isNaN(serviceCallData[field].getTime())) {
            console.log(`❌ Removing invalid Date object: ${field}`);
            delete serviceCallData[field];
          }
        }
      }
    });

    // Check for UUID fields and clean up empty strings
    const uuidFields = [
      'contact_person_id', 'tss_id', 'amc_id', 'type_of_call_id',
      'nature_of_issue_id', 'products_issues_id', 'area_id', 'assigned_to'
    ];
    // Validate UUID fields and check foreign key references
    for (const field of uuidFields) {
      if (serviceCallData[field] !== undefined) {
        console.log(`${field}:`, serviceCallData[field], typeof serviceCallData[field]);

        // If it's an empty string, remove it (will be set to null by database)
        if (serviceCallData[field] === '' || serviceCallData[field] === null) {
          console.log(`❌ Removing empty UUID field: ${field}`);
          delete serviceCallData[field];
        }
        // If it's a string, validate UUID format
        else if (typeof serviceCallData[field] === 'string') {
          const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
          if (!uuidRegex.test(serviceCallData[field])) {
            console.log(`❌ Removing invalid UUID field: ${field}`);
            delete serviceCallData[field];
          } else {
            console.log(`✅ Valid UUID field: ${field}`);

            // Check foreign key references for specific fields
            if (field === 'products_issues_id') {
              try {
                const productsIssue = await models.ProductsIssues.findByPk(serviceCallData[field]);
                if (!productsIssue) {
                  console.log(`❌ ProductsIssues not found for ID: ${serviceCallData[field]}`);
                  delete serviceCallData[field]; // Remove invalid reference
                } else {
                  console.log(`✅ Valid ProductsIssues reference: ${productsIssue.name}`);
                }
              } catch (error) {
                console.log(`❌ Error validating ProductsIssues reference:`, error.message);
                delete serviceCallData[field];
              }
            }
          }
        }
      }
    }

    // Check for numeric fields and clean up empty strings
    const numericFields = [
      'actual_hours', 'estimated_hours', 'billable_hours', 'hourly_rate',
      'service_charges', 'travel_charges', 'total_amount', 'hourly_rate_amount',
      'credit_limit', 'credit_days', 'annual_turnover', 'employee_count'
    ];
    numericFields.forEach(field => {
      if (serviceCallData[field] !== undefined) {
        console.log(`${field}:`, serviceCallData[field], typeof serviceCallData[field]);

        // If it's an empty string, remove it (will be set to null/default by database)
        if (serviceCallData[field] === '' || serviceCallData[field] === null) {
          console.log(`❌ Removing empty numeric field: ${field}`);
          delete serviceCallData[field];
        }
        // If it's a string, try to convert to number
        else if (typeof serviceCallData[field] === 'string') {
          const numValue = parseFloat(serviceCallData[field]);
          if (isNaN(numValue)) {
            console.log(`❌ Removing invalid numeric field: ${field}`);
            delete serviceCallData[field];
          } else {
            console.log(`✅ Converting string to number: ${field}`);
            serviceCallData[field] = numValue;
          }
        }
      }
    });

    // Check for enum fields and clean up empty strings
    const enumFields = [
      'call_type', 'priority', 'customer_feedback_type', 'charging_type',
      'tss_status', 'customer_type', 'business_type'
    ];
    enumFields.forEach(field => {
      if (serviceCallData[field] !== undefined) {
        console.log(`${field}:`, serviceCallData[field], typeof serviceCallData[field]);

        // If it's an empty string, remove it (will be set to null/default by database)
        if (serviceCallData[field] === '' || serviceCallData[field] === null) {
          console.log(`❌ Removing empty enum field: ${field}`);
          delete serviceCallData[field];
        }
      }
    });

    // General cleanup for any remaining "Invalid date" strings
    Object.keys(serviceCallData).forEach(key => {
      if (typeof serviceCallData[key] === 'string' && serviceCallData[key].includes('Invalid date')) {
        console.log(`❌ Removing field with "Invalid date": ${key}`);
        delete serviceCallData[key];
      }
    });

    // Generate or validate call number with retry logic
    const generateUniqueCallNumber = async () => {
      let attempts = 0;
      const maxAttempts = 10;

      while (attempts < maxAttempts) {
        // Find the highest existing call number for this tenant
        const lastCall = await models.ServiceCall.findOne({
          where: {
            tenant_id: req.user.tenant.id,
            call_number: {
              [Op.like]: 'SC%'
            }
          },
          order: [
            [models.sequelize.literal("CAST(SUBSTRING(call_number FROM '[0-9]+') AS INTEGER)"), 'DESC']
          ],
        });

        const nextNumber = lastCall ?
          parseInt(lastCall.call_number.replace(/\D/g, '')) + 1 : 1;
        const newCallNumber = `SC${nextNumber.toString().padStart(6, '0')}`;

        // Check if this number already exists
        const existingCall = await models.ServiceCall.findOne({
          where: {
            call_number: newCallNumber,
            tenant_id: req.user.tenant.id
          }
        });

        if (!existingCall) {
          return newCallNumber;
        }

        attempts++;
        console.log(`⚠️ Call number ${newCallNumber} already exists, attempt ${attempts}/${maxAttempts}`);
      }

      // Fallback: use timestamp-based number
      const timestamp = Date.now().toString().slice(-6);
      return `SC${timestamp}`;
    };

    if (!serviceCallData.call_number) {
      // Generate new call number
      serviceCallData.call_number = await generateUniqueCallNumber();
      console.log(`✅ Generated new call number: ${serviceCallData.call_number}`);
    } else {
      // Check if provided call number already exists
      const existingCall = await models.ServiceCall.findOne({
        where: {
          call_number: serviceCallData.call_number,
          tenant_id: req.user.tenant.id
        }
      });

      if (existingCall) {
        // Generate a new unique call number
        console.log(`⚠️ Call number ${serviceCallData.call_number} already exists, generating new one`);
        serviceCallData.call_number = await generateUniqueCallNumber();
        console.log(`✅ Generated new call number: ${serviceCallData.call_number}`);
      }
    }

    // Validate that status_id is provided (required field)
    if (!serviceCallData.status_id) {
      return res.status(400).json({
        success: false,
        message: 'Call status is required',
        errors: {
          status_id: 'Call status must be selected'
        }
      });
    }

    // Check if customer exists and belongs to tenant
    const customer = await models.Customer.findOne({
      where: {
        id: serviceCallData.customer_id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check AMC if specified
    if (serviceCallData.amc_id) {
      const amc = await models.CustomerAMC.findOne({
        where: {
          id: serviceCallData.amc_id,
          customer_id: serviceCallData.customer_id,
        },
      });

      if (!amc) {
        return res.status(404).json({
          success: false,
          message: 'AMC contract not found for this customer',
        });
      }

      if (!amc.canCreateServiceCall()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot create service call under this AMC contract',
          details: {
            status: amc.status,
            callsUsed: amc.calls_used,
            callsAllowed: amc.calls_allowed,
          },
        });
      }

      serviceCallData.is_under_amc = true;
    }

    const transaction = await models.sequelize.transaction();

    try {
      // Create service call
      const serviceCall = await models.ServiceCall.create(serviceCallData, { transaction });

      // Handle time tracking if status is set to "In Progress" during creation
      if (serviceCallData.status_id) {
        const status = await models.CallStatus.findByPk(serviceCallData.status_id);
        console.log('🔍 Service call creation - Status check:', {
          statusId: serviceCallData.status_id,
          statusFound: !!status,
          statusName: status?.name,
          statusCode: status?.code,
          isInProgressStatus: status && ['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(status.code)
        });

        if (status && ['ON_PROCESS', 'PROGRESS', 'IN_PROGRESS'].includes(status.code)) {
          try {
            console.log('🕐 Starting timer for new service call with In Progress status...', {
              serviceCallId: serviceCall.id,
              callNumber: serviceCall.call_number,
              statusName: status.name,
              statusCode: status.code,
              userId: req.user.id
            });

            // Use the atomic transaction method to ensure timer starts properly
            await TimeTrackingService.handleStatusChangeWithTransaction(
              serviceCall,
              'PENDING', // Assume initial status is PENDING for new service calls
              status.code,
              req.user.id,
              {}, // No additional update data needed
              transaction // Use the existing transaction
            );

            console.log('✅ Timer started successfully for new service call:', {
              serviceCallId: serviceCall.id,
              callNumber: serviceCall.call_number,
              statusCode: status.code
            });
          } catch (timeTrackingError) {
            console.error('❌ Time tracking error during creation:', {
              error: timeTrackingError.message,
              stack: timeTrackingError.stack,
              serviceCallId: serviceCall.id,
              statusCode: status.code,
              userId: req.user.id
            });
            logger.error('Time tracking error during service call creation:', {
              error: timeTrackingError.message,
              stack: timeTrackingError.stack,
              serviceCallId: serviceCall.id,
              statusCode: status.code,
              userId: req.user.id
            });
            // Don't fail the creation if time tracking fails, but log it prominently
            console.warn('⚠️ Service call created successfully but timer failed to start. Timer can be started manually by changing status.');
          }
        } else {
          console.log('ℹ️ Service call created with non-progress status, no timer action needed:', {
            statusName: status?.name || 'Unknown',
            statusCode: status?.code || 'Unknown'
          });
        }
      } else {
        console.log('ℹ️ Service call created without status, no timer action needed');
      }

      // Update AMC usage if applicable
      if (serviceCallData.amc_id) {
        await models.CustomerAMC.increment('calls_used', {
          by: 1,
          where: { id: serviceCallData.amc_id },
          transaction,
        });
      }

      // Create service call items if provided
      if (req.body.items && req.body.items.length > 0) {
        const items = req.body.items.map(item => ({
          ...item,
          service_call_id: serviceCall.id,
        }));

        for (const item of items) {
          const serviceCallItem = await models.ServiceCallItem.create(item, { transaction });
          serviceCallItem.updateCalculatedFields();
          await serviceCallItem.save({ transaction });
        }
      }

      await transaction.commit();

      // Fetch the created service call with associations
      const createdServiceCall = await models.ServiceCall.findByPk(serviceCall.id, {
        include: [
          {
            model: models.Customer,
            as: 'customer',
          },
          {
            model: models.CallStatus,
            as: 'status',
          },
          {
            model: models.Executive,
            as: 'assignedExecutive',
          },
          {
            model: models.ServiceCallItem,
            as: 'items',
          },
        ],
      });

      logger.info('Service call created successfully:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        customerId: serviceCall.customer_id,
        createdBy: req.user.id,
      });

      // Send service creation notification
      try {
        const serviceData = {
          id: createdServiceCall.id,
          serviceNumber: createdServiceCall.call_number,
          service_number: createdServiceCall.call_number,
          service_type: createdServiceCall.call_type,
          status: createdServiceCall.status,
          scheduled_date: createdServiceCall.scheduled_date,
          description: createdServiceCall.description,
          tenant_id: createdServiceCall.tenant_id
        };

        // Enhanced customer email retrieval - check multiple sources
        let customerEmail = createdServiceCall.customer.email;

        // If no main email, check address book in custom_fields
        if (!customerEmail && createdServiceCall.customer.custom_fields?.address_book) {
          const addressBook = createdServiceCall.customer.custom_fields.address_book;
          // Find first email from address book
          for (const contact of addressBook) {
            if (contact.email && contact.email.trim() !== '') {
              customerEmail = contact.email.trim();
              logger.info('Using email from address book:', {
                customerEmail,
                contactType: contact.type,
                serviceNumber: createdServiceCall.call_number
              });
              break;
            }
          }
        }

        // If still no email, check custom fields for specific email fields
        if (!customerEmail && createdServiceCall.customer.custom_fields) {
          const emailFields = ['admin_email', 'md_email', 'office_email', 'auditor_email', 'tax_consultant_email', 'it_email'];
          for (const field of emailFields) {
            if (createdServiceCall.customer.custom_fields[field] && createdServiceCall.customer.custom_fields[field].trim() !== '') {
              customerEmail = createdServiceCall.customer.custom_fields[field].trim();
              logger.info('Using email from custom fields:', {
                customerEmail,
                field,
                serviceNumber: createdServiceCall.call_number
              });
              break;
            }
          }
        }

        // Enhanced customer data with phone numbers for WhatsApp
        let customerPhone = createdServiceCall.customer.phone;
        let customerMobile = createdServiceCall.customer.mobile;

        // If no main phone/mobile, check address book in custom_fields
        if ((!customerPhone || !customerMobile) && createdServiceCall.customer.custom_fields?.address_book) {
          const addressBook = createdServiceCall.customer.custom_fields.address_book;
          // Find phone numbers from address book
          for (const contact of addressBook) {
            if (!customerPhone && contact.phone && contact.phone.trim() !== '') {
              customerPhone = contact.phone.trim();
            }
            if (!customerMobile && contact.mobile && contact.mobile.trim() !== '') {
              customerMobile = contact.mobile.trim();
            }
            // Break if we have both
            if (customerPhone && customerMobile) break;
          }
        }

        const customerData = {
          id: createdServiceCall.customer.id,
          name: createdServiceCall.customer.company_name || createdServiceCall.customer.customer_code,
          company_name: createdServiceCall.customer.company_name,
          email: customerEmail,
          phone: customerPhone,
          mobile: customerMobile
        };

        // Log notification details
        logger.info('Service creation notification details:', {
          serviceNumber: createdServiceCall.call_number,
          customerId: createdServiceCall.customer.id,
          customerName: createdServiceCall.customer.company_name,
          emailFound: !!customerEmail,
          emailSource: customerEmail ? (createdServiceCall.customer.email ? 'main_email' : 'address_book_or_custom_fields') : 'none',
          email: customerEmail,
          phoneFound: !!customerPhone,
          mobileFound: !!customerMobile,
          phone: customerPhone,
          mobile: customerMobile
        });

        if (customerEmail) {
          // Send notification for service creation
          await notificationService.sendServiceNotification(
            serviceData,
            customerData,
            'service_created'
          );
        } else {
          logger.warn('No email found for customer - skipping email notification:', {
            serviceNumber: createdServiceCall.call_number,
            customerId: createdServiceCall.customer.id,
            customerName: createdServiceCall.customer.company_name
          });
        }
      } catch (notificationError) {
        logger.error('Failed to send service creation notification:', notificationError);
        // Don't fail the creation if notification fails
      }

      res.status(201).json({
        success: true,
        message: 'Service call created successfully',
        data: { serviceCall: createdServiceCall },
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    logger.error('Create service call error:', error);
    console.error('Service call creation error details:', {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      original: error.original
    });

    // Handle specific database errors
    let errorMessage = 'Unable to create service call. Please check your information and try again.';

    if (error.name === 'SequelizeForeignKeyConstraintError') {
      // Handle foreign key constraint violations
      if (error.fields?.includes('products_issues_id')) {
        errorMessage = 'The selected issue type is not valid. Please choose a different issue type and try again.';
      } else if (error.fields?.includes('status_id')) {
        errorMessage = 'The selected status is not valid. Please choose a different status and try again.';
      } else if (error.fields?.includes('assigned_to')) {
        errorMessage = 'The selected executive is not valid. Please choose a different executive and try again.';
      } else if (error.fields?.includes('customer_id')) {
        errorMessage = 'The selected customer is not valid. Please choose a different customer and try again.';
      } else {
        errorMessage = 'One or more selected options are not valid. Please check your selections and try again.';
      }
      console.error('❌ Foreign key constraint error detected:', {
        constraint: error.index,
        fields: error.fields,
        detail: error.original?.detail
      });
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message, // Always show error message for debugging
      details: {
        userFriendlyMessage: errorMessage,
        stack: error.stack,
        sql: error.sql,
        original: error.original
      }
    });
  }
};

/**
 * Update service call
 */
export const updateServiceCall = async (req, res) => {
  try {
    const { id } = req.params;
    let updateData = req.body;

    console.log('🔄 Starting service call update:', {
      serviceCallId: id,
      requestBody: req.body,
      userId: req.user?.id,
      tenantId: req.user?.tenant?.id
    });

    // Map camelCase fields to snake_case for database compatibility
    const fieldMapping = {
      'statusId': 'status_id',
      'customerId': 'customer_id',
      'contactPersonId': 'contact_person_id',
      'assignedTo': 'assigned_to',
      'typeOfCallId': 'type_of_call_id',
      'natureOfIssueId': 'nature_of_issue_id',
      'areaId': 'area_id',
      'tssId': 'tss_id',
      'amcId': 'amc_id',
      'callType': 'call_type',
      'callBillingType': 'call_billing_type',
      'callNumber': 'call_number',
      'serviceCharges': 'service_charges',
      'travelCharges': 'travel_charges',
      'totalAmount': 'total_amount',
      'estimatedHours': 'estimated_hours',
      'actualHours': 'actual_hours',
      'billableHours': 'billable_hours',
      'hourlyRate': 'hourly_rate',
      'scheduledDate': 'scheduled_date',
      'startedAt': 'started_at',
      'completedAt': 'completed_at',
      'closedAt': 'closed_at',
      'customerReportedIssue': 'customer_reported_issue',
      'actualIssueFound': 'actual_issue_found',
      'solutionProvided': 'solution_provided',
      'customerSatisfaction': 'customer_satisfaction',
      'customerFeedback': 'customer_feedback',
      'internalNotes': 'internal_notes',
      'followUpRequired': 'follow_up_required',
      'followUpDate': 'follow_up_date',
      'followUpNotes': 'follow_up_notes',
      'isBillable': 'is_billable',
      'isUnderAmc': 'is_under_amc',
      'contactNumber': 'contact_number',
      'tallySerialNumber': 'tally_serial_number',
      'customerName': 'customer_name',
      'serviceLocation': 'service_location',
      'executiveRemarks': 'executive_remarks',
      'statusId': 'status_id',
      'companyName': 'company_name',
      'emailAddress': 'email_address',
      'tallyVersion': 'tally_version',
      'tssStatus': 'tss_status',
      'tssExpiry': 'tss_expiry'
    };

    // Convert camelCase fields to snake_case (if not already in snake_case)
    const mappedData = {};
    Object.keys(updateData).forEach(key => {
      // If the field is already in snake_case, keep it as is
      // If it's in camelCase, map it to snake_case
      const mappedKey = fieldMapping[key] || key;
      mappedData[mappedKey] = updateData[key];
    });

    updateData = mappedData;

    console.log('🔄 Field mapping applied:', {
      originalKeys: Object.keys(req.body),
      mappedKeys: Object.keys(updateData),
      statusUpdate: {
        original: req.body.statusId,
        mapped: updateData.status_id
      },
      originalData: req.body,
      mappedData: updateData
    });

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
        },
      ],
    });

    if (!serviceCall) {
      console.log('❌ Service call not found:', id);
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    // Capture original status ID for notification logic
    const originalStatusId = serviceCall.status_id;

    console.log('✅ Service call found:', {
      id: serviceCall.id,
      callNumber: serviceCall.call_number,
      originalStatusId: originalStatusId,
      currentStatus: serviceCall.status ? {
        id: serviceCall.status.id,
        name: serviceCall.status.name,
        code: serviceCall.status.code
      } : 'No status loaded'
    });

    // Ensure status is loaded
    if (!serviceCall.status) {
      console.log('❌ Service call status not loaded, fetching...');
      const statusRecord = await models.CallStatus.findByPk(serviceCall.status_id);
      if (!statusRecord) {
        console.log('❌ Status record not found for ID:', serviceCall.status_id);
        return res.status(500).json({
          success: false,
          message: 'Service call status data is corrupted',
        });
      }
      serviceCall.status = statusRecord;
    }

    // Check if service call is already completed and prevent any updates
    if (serviceCall.status && serviceCall.status.code === 'COMPLETED') {
      console.log('❌ Attempted to update completed service call:', {
        serviceCallId: id,
        currentStatus: serviceCall.status.name,
        statusCode: serviceCall.status.code
      });
      return res.status(400).json({
        success: false,
        message: 'This service call cannot be modified because it has already been completed',
        details: {
          currentStatus: {
            name: serviceCall.status.name,
            code: serviceCall.status.code
          },
          reason: 'Service calls that are completed cannot be modified',
          userFriendlyMessage: 'This service call cannot be modified because it has already been completed'
        }
      });
    }

    // Check if status change is valid and handle time tracking
    if (updateData.status_id && updateData.status_id !== serviceCall.status_id) {
      console.log('🔄 Processing status change (backend):', {
        serviceCallId: id,
        callNumber: serviceCall.call_number,
        currentStatusId: serviceCall.status_id,
        currentStatusName: serviceCall.status.name,
        currentStatusCode: serviceCall.status.code,
        newStatusId: updateData.status_id,
        requestSource: req.headers['user-agent']?.includes('axios') ? 'frontend' : 'unknown',
        timestamp: new Date().toISOString()
      });

      // ENHANCED: Add UUID validation debugging
      console.log('🔍 Status ID validation:', {
        statusId: updateData.status_id,
        statusIdType: typeof updateData.status_id,
        isString: typeof updateData.status_id === 'string',
        isValidUUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(updateData.status_id)
      });

      const newStatus = await models.CallStatus.findByPk(updateData.status_id);

      if (!newStatus) {
        console.log('❌ New status not found:', {
          providedStatusId: updateData.status_id,
          statusIdType: typeof updateData.status_id,
          isValidUUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(updateData.status_id)
        });
        return res.status(400).json({
          success: false,
          message: 'The selected status is not valid. Please choose a different status and try again.',
          details: {
            field: 'status_id',
            value: updateData.status_id,
            type: typeof updateData.status_id,
            reason: 'Invalid or non-existent status ID',
            userFriendlyMessage: 'The selected status is not valid. Please choose a different status and try again.'
          }
        });
      }

      console.log('✅ New status found:', {
        id: newStatus.id,
        name: newStatus.name,
        code: newStatus.code,
        category: newStatus.category
      });

      // Allow all status transitions - no validation restrictions
      console.log('✅ Status transition allowed:', {
        from: `${serviceCall.status.name} (${serviceCall.status.code})`,
        to: `${newStatus.name} (${newStatus.code})`
      });

      // Handle time tracking for status change with atomic transaction
      try {
        console.log('🕐 Starting atomic time tracking for status change...');

        // Use atomic transaction to combine time tracking with status update
        const transaction = await serviceCall.sequelize.transaction();

        try {
          await TimeTrackingService.handleStatusChangeWithTransaction(
            serviceCall,
            serviceCall.status.code,
            newStatus.code,
            req.user.id,
            updateData, // Include other update data in the atomic transaction
            transaction
          );

          await transaction.commit();
          console.log('✅ Atomic time tracking and status update completed successfully');

          // Skip the regular update since it was done atomically
          updateData = {}; // Clear update data since it was already applied

        } catch (transactionError) {
          await transaction.rollback();
          throw transactionError;
        }

      } catch (timeTrackingError) {
        console.error('❌ Atomic time tracking error:', timeTrackingError);
        logger.error('Atomic time tracking error:', {
          error: timeTrackingError.message,
          stack: timeTrackingError.stack,
          serviceCallId: serviceCall.id,
          statusChange: `${serviceCall.status.code} -> ${newStatus.code}`
        });
        // Don't fail the update if time tracking fails - fall back to regular update
        console.log('⚠️ Falling back to regular update without time tracking');
      }

      // Set completion/closure timestamps
      if (newStatus.category === 'resolved' && !serviceCall.completed_at) {
        updateData.completed_at = new Date();
      }

      if (newStatus.category === 'closed' && !serviceCall.closed_at) {
        updateData.closed_at = new Date();
      }
    }

    // Clean up empty strings for UUID and date fields before database update
    const cleanedUpdateData = { ...updateData };

    // Helper function to validate if a date string is valid
    const isValidDate = (dateString) => {
      if (!dateString || typeof dateString !== 'string' || dateString.trim() === '') return false;
      if (dateString.includes('Invalid') || dateString === 'NaN' || dateString === 'null' || dateString === 'undefined') return false;
      const date = new Date(dateString);
      return date instanceof Date && !isNaN(date.getTime());
    };

    // UUID fields that should be null instead of empty strings
    const uuidFields = [
      'contact_person_id', 'tss_id', 'amc_id', 'type_of_call_id',
      'nature_of_issue_id', 'products_issues_id', 'area_id', 'assigned_to', 'created_by'
    ];

    // Validate UUID fields and check foreign key references
    for (const field of uuidFields) {
      if (cleanedUpdateData[field] === '' || cleanedUpdateData[field] === null) {
        delete cleanedUpdateData[field]; // Let database handle null
      } else if (cleanedUpdateData[field]) {
        // Validate UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(cleanedUpdateData[field])) {
          console.log(`❌ Invalid UUID format for ${field}:`, cleanedUpdateData[field]);
          delete cleanedUpdateData[field];
          continue;
        }

        // Check foreign key references for specific fields
        if (field === 'products_issues_id') {
          try {
            const productsIssue = await models.ProductsIssues.findByPk(cleanedUpdateData[field]);
            if (!productsIssue) {
              console.log(`❌ ProductsIssues not found for ID: ${cleanedUpdateData[field]}`);
              delete cleanedUpdateData[field]; // Remove invalid reference
            } else {
              console.log(`✅ Valid ProductsIssues reference: ${productsIssue.name}`);
            }
          } catch (error) {
            console.log(`❌ Error validating ProductsIssues reference:`, error.message);
            delete cleanedUpdateData[field];
          }
        }
      }
    }

    // Date fields that should be null instead of empty strings or "Invalid date"
    const dateFields = [
      'scheduled_date', 'started_at', 'completed_at', 'closed_at',
      'follow_up_date', 'call_date', 'booking_date', 'tss_expiry'
    ];

    dateFields.forEach(field => {
      if (cleanedUpdateData[field] !== undefined) {
        const value = cleanedUpdateData[field];

        // Check for invalid date values
        if (value === '' ||
            value === null ||
            value === undefined ||
            (typeof value === 'string' && value.trim() === '') ||
            !isValidDate(value)) {
          console.log(`🧹 Cleaning invalid date field '${field}':`, value);
          delete cleanedUpdateData[field]; // Let database handle null
        } else {
          // Ensure valid dates are properly formatted
          try {
            const date = new Date(value);
            if (isNaN(date.getTime())) {
              console.log(`🧹 Invalid date detected for field '${field}', removing:`, value);
              delete cleanedUpdateData[field];
            } else {
              cleanedUpdateData[field] = date.toISOString();
              console.log(`✅ Date field '${field}' validated and formatted:`, cleanedUpdateData[field]);
            }
          } catch (dateError) {
            console.log(`🧹 Error formatting date field '${field}', removing:`, value);
            delete cleanedUpdateData[field];
          }
        }
      }
    });

    // Text fields that should be null instead of empty strings
    const textFields = [
      'customer_reported_issue', 'actual_issue_found', 'solution_provided',
      'hourly_rate', 'follow_up_notes', 'customer_feedback', 'internal_notes',
      'executive_remarks', 'customer_feedback_comments'
    ];

    textFields.forEach(field => {
      if (cleanedUpdateData[field] === '') {
        delete cleanedUpdateData[field]; // Let database handle null
      }
    });

    // Final validation before database update
    console.log('🔍 Final data validation before database update...');
    const finalValidatedData = {};

    Object.keys(cleanedUpdateData).forEach(key => {
      const value = cleanedUpdateData[key];

      // Always include status_id if it exists (critical for status updates)
      if (key === 'status_id' && value) {
        finalValidatedData[key] = value;
        console.log(`✅ Including critical status_id field:`, value);
        return;
      }

      // Skip null or undefined values for other fields
      if (value === null || value === undefined) {
        return;
      }

      // Special validation for date fields
      if (dateFields.includes(key)) {
        if (typeof value === 'string' && value.includes('Invalid')) {
          console.log(`❌ Skipping invalid date field '${key}':`, value);
          return;
        }
      }

      // Add validated field to final data
      finalValidatedData[key] = value;
    });

    console.log('🚀 Updating service call with final validated data:', finalValidatedData);

    // Log specifically if status is being updated
    if (finalValidatedData.status_id) {
      console.log('📊 Status update details:', {
        serviceCallId: serviceCall.id,
        oldStatusId: serviceCall.status_id,
        newStatusId: finalValidatedData.status_id,
        oldStatusName: serviceCall.status?.name,
        updateFields: Object.keys(finalValidatedData)
      });
    }

    // Only perform regular update if not already updated atomically with time tracking
    if (Object.keys(finalValidatedData).length > 0) {
      console.log('🔄 Performing regular update for non-time-tracking fields...');
      await serviceCall.update(finalValidatedData);
      console.log('✅ Service call updated with regular update');
    } else {
      console.log('ℹ️ Skipping regular update - already updated atomically with time tracking');
    }

    // Verify the status was actually updated
    if (finalValidatedData.status_id) {
      await serviceCall.reload();
      console.log('🔍 Post-update verification:', {
        currentStatusId: serviceCall.status_id,
        expectedStatusId: finalValidatedData.status_id,
        statusUpdateSuccessful: serviceCall.status_id === finalValidatedData.status_id
      });
    }

    // Note: Service completion emails are now handled by the unified notification service below
    // This prevents duplicate emails and ensures consistent notification handling

    // Fetch updated service call with associations
    const updatedServiceCall = await models.ServiceCall.findByPk(serviceCall.id, {
      include: [
        {
          model: models.Customer,
          as: 'customer',
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'color', 'category'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
      ],
    });

    // Log final status after reload to verify update
    console.log('📋 Final service call status after update and reload:', {
      id: updatedServiceCall.id,
      status_id: updatedServiceCall.status_id,
      statusName: updatedServiceCall.status?.name,
      statusCode: updatedServiceCall.status?.code,
      statusCategory: updatedServiceCall.status?.category
    });

    // Send status change notification if status was updated OR service was completed
    // Check multiple conditions for when to send notifications:
    // 1. Status was changed in finalValidatedData (regular update)
    // 2. Status was changed through atomic time tracking update
    // 3. Service was completed (completed_at timestamp set) regardless of status change
    const statusWasChanged = finalValidatedData.status_id ||
                            (originalStatusId && originalStatusId !== updatedServiceCall.status_id);

    const serviceWasCompleted = finalValidatedData.completed_at ||
                               (updatedServiceCall.status?.code === 'COMPLETED' &&
                                updatedServiceCall.completed_at);

    const shouldSendNotification = statusWasChanged || serviceWasCompleted;

    console.log('🔔 Checking notification conditions:', {
      hasStatusIdInFinalData: !!finalValidatedData.status_id,
      statusWasChangedAtomically: originalStatusId && originalStatusId !== updatedServiceCall.status_id,
      statusWasChanged: statusWasChanged,
      serviceWasCompleted: serviceWasCompleted,
      completedAtInFinalData: !!finalValidatedData.completed_at,
      currentStatusCode: updatedServiceCall.status?.code,
      hasCompletedTimestamp: !!updatedServiceCall.completed_at,
      shouldSendNotification: shouldSendNotification,
      hasCustomer: !!updatedServiceCall.customer,
      statusCode: updatedServiceCall.status?.code,
      customerEmail: updatedServiceCall.customer?.email,
      tenantId: updatedServiceCall.tenant_id,
      originalStatusId: originalStatusId,
      currentStatusId: updatedServiceCall.status_id
    });

    if (shouldSendNotification && updatedServiceCall.customer) {
      console.log('✅ Notification conditions met, preparing to send notification...');
      try {
        const serviceData = {
          id: updatedServiceCall.id,
          serviceNumber: updatedServiceCall.call_number,
          service_number: updatedServiceCall.call_number,
          service_type: updatedServiceCall.call_type,
          status: updatedServiceCall.status,
          scheduled_date: updatedServiceCall.scheduled_date,
          description: updatedServiceCall.description,
          tenant_id: updatedServiceCall.tenant_id
        };

        // Enhanced customer email retrieval for status change notifications
        let customerEmail = updatedServiceCall.customer.email;

        // If no main email, check address book in custom_fields
        if (!customerEmail && updatedServiceCall.customer.custom_fields?.address_book) {
          const addressBook = updatedServiceCall.customer.custom_fields.address_book;
          // Find first email from address book
          for (const contact of addressBook) {
            if (contact.email && contact.email.trim() !== '') {
              customerEmail = contact.email.trim();
              logger.info('Using email from address book for status change:', {
                customerEmail,
                contactType: contact.type,
                serviceNumber: updatedServiceCall.call_number
              });
              break;
            }
          }
        }

        // If still no email, check custom fields for specific email fields
        if (!customerEmail && updatedServiceCall.customer.custom_fields) {
          const emailFields = ['admin_email', 'md_email', 'office_email', 'auditor_email', 'tax_consultant_email', 'it_email'];
          for (const field of emailFields) {
            if (updatedServiceCall.customer.custom_fields[field] && updatedServiceCall.customer.custom_fields[field].trim() !== '') {
              customerEmail = updatedServiceCall.customer.custom_fields[field].trim();
              logger.info('Using email from custom fields for status change:', {
                customerEmail,
                field,
                serviceNumber: updatedServiceCall.call_number
              });
              break;
            }
          }
        }

        // Enhanced customer data with phone numbers for WhatsApp
        let customerPhone = updatedServiceCall.customer.phone;
        let customerMobile = updatedServiceCall.customer.mobile;

        // If no main phone/mobile, check address book in custom_fields
        if ((!customerPhone || !customerMobile) && updatedServiceCall.customer.custom_fields?.address_book) {
          const addressBook = updatedServiceCall.customer.custom_fields.address_book;
          // Find phone numbers from address book
          for (const contact of addressBook) {
            if (!customerPhone && contact.phone && contact.phone.trim() !== '') {
              customerPhone = contact.phone.trim();
            }
            if (!customerMobile && contact.mobile && contact.mobile.trim() !== '') {
              customerMobile = contact.mobile.trim();
            }
            // Break if we have both
            if (customerPhone && customerMobile) break;
          }
        }

        const customerData = {
          id: updatedServiceCall.customer.id,
          name: updatedServiceCall.customer.company_name || updatedServiceCall.customer.customer_code,
          company_name: updatedServiceCall.customer.company_name,
          email: customerEmail,
          phone: customerPhone,
          mobile: customerMobile
        };

        // Log status change notification details
        logger.info('Service status change notification details:', {
          serviceNumber: updatedServiceCall.call_number,
          customerId: updatedServiceCall.customer.id,
          customerName: updatedServiceCall.customer.company_name,
          statusCode: updatedServiceCall.status?.code,
          statusName: updatedServiceCall.status?.name,
          emailFound: !!customerEmail,
          emailSource: customerEmail ? (updatedServiceCall.customer.email ? 'main_email' : 'address_book_or_custom_fields') : 'none',
          email: customerEmail,
          phoneFound: !!customerPhone,
          mobileFound: !!customerMobile,
          phone: customerPhone,
          mobile: customerMobile
        });

        // Map status codes to notification event types
        const statusToEventMap = {
          'IN_PROGRESS': 'service_started',
          'ON_PROCESS': 'service_on_process',
          'PROGRESS': 'service_started',
          'COMPLETED': 'service_completed',
          'CANCELLED': 'service_cancelled',
          'ON_HOLD': 'service_on_hold',
          'HOLD': 'service_on_hold',
          'PENDING': 'service_pending',
          'FOLLOW_UP': 'service_follow_up',
          'ONSITE': 'service_onsite'
        };

        const eventType = statusToEventMap[updatedServiceCall.status?.code] || 'service_started';

        console.log('📧 Sending notification:', {
          serviceNumber: serviceData.service_number,
          eventType,
          customerEmail: customerData.email,
          statusCode: updatedServiceCall.status?.code
        });

        // Send notification for status change
        const notificationResult = await notificationService.sendServiceNotification(
          serviceData,
          customerData,
          eventType
        );

        console.log('✅ Notification sent successfully:', {
          success: notificationResult.success,
          messageId: notificationResult.messageId,
          eventType
        });
      } catch (notificationError) {
        console.error('❌ Notification error:', notificationError);
        logger.error('Failed to send status change notification:', notificationError);
        // Don't fail the update if notification fails
      }
    } else {
      console.log('❌ Notification conditions not met:', {
        hasStatusIdInFinalData: !!finalValidatedData.status_id,
        statusWasChangedAtomically: originalStatusId && originalStatusId !== updatedServiceCall.status_id,
        statusWasChanged: statusWasChanged,
        serviceWasCompleted: serviceWasCompleted,
        shouldSendNotification: shouldSendNotification,
        hasCustomer: !!updatedServiceCall.customer,
        reason: !shouldSendNotification ? 'No status change or completion detected' : 'No customer data'
      });
    }

    logger.info('Service call updated successfully:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Service call updated successfully',
      data: { serviceCall: updatedServiceCall },
    });

  } catch (error) {
    logger.error('Update service call error:', error);
    console.error('Service call update error details:', {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      original: error.original,
      serviceCallId: id,
      updateData: req.body
    });

    // Handle specific database errors
    let errorMessage = 'Unable to update service call. Please check your information and try again.';

    if (error.name === 'SequelizeDatabaseError' && error.original?.code === '22007') {
      errorMessage = 'Invalid date format detected. Please check your date entries and try again.';
      console.error('❌ PostgreSQL timestamp error detected:', {
        errorCode: error.original.code,
        sqlState: error.original.sqlState,
        detail: error.original.detail,
        hint: error.original.hint
      });
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      // Handle foreign key constraint violations
      if (error.fields?.includes('products_issues_id')) {
        errorMessage = 'The selected issue type is not valid. Please choose a different issue type and try again.';
      } else if (error.fields?.includes('status_id')) {
        errorMessage = 'The selected status is not valid. Please choose a different status and try again.';
      } else if (error.fields?.includes('assigned_to')) {
        errorMessage = 'The selected executive is not valid. Please choose a different executive and try again.';
      } else {
        errorMessage = 'One or more selected options are not valid. Please check your selections and try again.';
      }
      console.error('❌ Foreign key constraint error detected:', {
        constraint: error.index,
        fields: error.fields,
        detail: error.original?.detail
      });
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message, // Always show error message for debugging
      details: {
        userFriendlyMessage: errorMessage,
        stack: error.stack,
        sql: error.sql,
        original: error.original,
        errorCode: error.original?.code,
        sqlState: error.original?.sqlState
      }
    });
  }
};

/**
 * Get service call statistics
 */
export const getServiceCallStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Date ranges for comparison
    const currentMonthStart = new Date(currentYear, currentMonth, 1);
    const lastMonthStart = new Date(currentYear, currentMonth - 1, 1);
    const lastMonthEnd = new Date(currentYear, currentMonth, 0);
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Helper function to safely execute queries with error handling
    const safeQuery = async (queryFn, fallbackValue = []) => {
      try {
        const result = await queryFn();
        return result || fallbackValue;
      } catch (error) {
        logger.warn('Query failed in getServiceCallStats:', error.message);
        return fallbackValue;
      }
    };

    const stats = await Promise.all([
      // Total service calls
      safeQuery(() => models.ServiceCall.count({
        where: { tenant_id: tenantId },
      }), 0),

      // Calls by status with proper joins and error handling
      safeQuery(() => models.ServiceCall.findAll({
        where: { tenant_id: tenantId },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'category', 'color'],
            required: false, // LEFT JOIN to handle missing status
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
        ],
        group: ['status.id', 'status.name', 'status.category', 'status.color'],
        raw: true,
      }), []),

      // Calls by call type (Free, AMC, Paid) - include null values
      safeQuery(() => models.ServiceCall.findAll({
        where: {
          tenant_id: tenantId,
          // Include all calls, including those with null billing types
        },
        attributes: [
          'call_billing_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['call_billing_type'],
        raw: true,
      }), []),

      // Recent calls (last 30 days)
      safeQuery(() => models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.gte]: last30Days,
          },
        },
      }), 0),

      // Current month calls by status
      safeQuery(() => models.ServiceCall.findAll({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.gte]: currentMonthStart,
          },
        },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'category'],
            required: false,
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
        ],
        group: ['status.id', 'status.name', 'status.category'],
        raw: true,
      }), []),

      // Last month calls by status for comparison
      safeQuery(() => models.ServiceCall.findAll({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.between]: [lastMonthStart, lastMonthEnd],
          },
        },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'category'],
            required: false,
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
        ],
        group: ['status.id', 'status.name', 'status.category'],
        raw: true,
      }), []),

      // Current month calls by type
      safeQuery(() => models.ServiceCall.findAll({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.gte]: currentMonthStart,
          },
          // Include all calls, including those with null billing types
        },
        attributes: [
          'call_billing_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['call_billing_type'],
        raw: true,
      }), []),

      // Last month calls by type for comparison
      safeQuery(() => models.ServiceCall.findAll({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.between]: [lastMonthStart, lastMonthEnd],
          },
          // Include all calls, including those with null billing types
        },
        attributes: [
          'call_billing_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['call_billing_type'],
        raw: true,
      }), []),
    ]);

    const [
      totalCalls,
      callsByStatus,
      callsByType,
      recentCalls,
      currentMonthByStatus,
      lastMonthByStatus,
      currentMonthByType,
      lastMonthByType
    ] = stats;

    // Helper function to calculate growth percentage with better error handling
    const calculateGrowth = (current, previous) => {
      // Handle null, undefined, or non-numeric values
      const currentNum = Number(current) || 0;
      const previousNum = Number(previous) || 0;

      if (previousNum === 0) {
        return currentNum > 0 ? 100 : 0;
      }
      return Math.round(((currentNum - previousNum) / previousNum) * 100);
    };

    // Process call types data with consistent structure
    const callTypeStats = {
      freeCalls: 0,
      amcCalls: 0,
      paidCalls: 0
    };

    // Ensure callsByType is an array and process safely
    console.log('📊 Processing callsByType data:', callsByType);
    if (Array.isArray(callsByType)) {
      callsByType.forEach(item => {
        if (item && item.count) {
          const count = parseInt(item.count) || 0;
          const billingType = item.call_billing_type ? String(item.call_billing_type).toLowerCase().trim() : 'null';
          console.log(`📊 Processing call type: ${billingType} with count: ${count}`);

          switch (billingType) {
            case 'free call':
            case 'free_call':
            case 'free':
            case 'null': // Treat NULL billing types as free calls
            case '': // Treat empty strings as free calls
              callTypeStats.freeCalls += count;
              console.log(`📊 Added ${count} to freeCalls, total now: ${callTypeStats.freeCalls}`);
              break;
            case 'amc call':
            case 'amc_call':
            case 'amc':
              callTypeStats.amcCalls += count;
              console.log(`📊 Added ${count} to amcCalls, total now: ${callTypeStats.amcCalls}`);
              break;
            case 'per call':
            case 'per_call':
            case 'paid call':
            case 'paid_call':
            case 'paid':
              callTypeStats.paidCalls += count;
              console.log(`📊 Added ${count} to paidCalls, total now: ${callTypeStats.paidCalls}`);
              break;
            default:
              console.log(`📊 Unknown billing type: ${billingType}, treating as free call`);
              callTypeStats.freeCalls += count; // Default unknown types to free calls
          }
        }
      });
    } else {
      console.log('📊 callsByType is not an array:', typeof callsByType, callsByType);
    }

    // Calculate monthly growth for call types with safe processing
    const currentMonthTypes = {};
    const lastMonthTypes = {};

    if (Array.isArray(currentMonthByType)) {
      currentMonthByType.forEach(item => {
        if (item && item.count) {
          const billingType = item.call_billing_type ? String(item.call_billing_type).toLowerCase().trim() : 'null';
          currentMonthTypes[billingType] = parseInt(item.count) || 0;
        }
      });
    }

    if (Array.isArray(lastMonthByType)) {
      lastMonthByType.forEach(item => {
        if (item && item.count) {
          const billingType = item.call_billing_type ? String(item.call_billing_type).toLowerCase().trim() : 'null';
          lastMonthTypes[billingType] = parseInt(item.count) || 0;
        }
      });
    }

    // Calculate monthly growth for statuses with safe processing
    const currentMonthStatuses = {};
    const lastMonthStatuses = {};

    if (Array.isArray(currentMonthByStatus)) {
      currentMonthByStatus.forEach(item => {
        if (item && item['status.name']) {
          const statusName = String(item['status.name']).toLowerCase().replace(/\s+/g, '-').trim();
          currentMonthStatuses[statusName] = parseInt(item.count) || 0;
        }
      });
    }

    if (Array.isArray(lastMonthByStatus)) {
      lastMonthByStatus.forEach(item => {
        if (item && item['status.name']) {
          const statusName = String(item['status.name']).toLowerCase().replace(/\s+/g, '-').trim();
          lastMonthStatuses[statusName] = parseInt(item.count) || 0;
        }
      });
    }

    // Process callsByStatus with safe data handling
    const processedCallsByStatus = Array.isArray(callsByStatus)
      ? callsByStatus.map(item => ({
          status: item['status.name'] || 'Unknown',
          category: item['status.category'] || 'unknown',
          color: item['status.color'] || '#6b7280',
          count: parseInt(item.count) || 0,
        }))
      : [];

    // Calculate total current and last month calls safely
    const currentMonthTotal = Array.isArray(currentMonthByStatus)
      ? currentMonthByStatus.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)
      : 0;

    const lastMonthTotal = Array.isArray(lastMonthByStatus)
      ? lastMonthByStatus.reduce((sum, item) => sum + (parseInt(item.count) || 0), 0)
      : 0;

    // Ensure consistent response structure
    const responseData = {
      totalCalls: Number(totalCalls) || 0,
      recentCalls: Number(recentCalls) || 0,
      callsByStatus: processedCallsByStatus,
      callsByType: {
        freeCalls: callTypeStats.freeCalls,
        amcCalls: callTypeStats.amcCalls,
        paidCalls: callTypeStats.paidCalls
      },
      monthlyGrowth: {
        total: calculateGrowth(currentMonthTotal, lastMonthTotal),
        inProgress: calculateGrowth(
          currentMonthStatuses['in-progress'] || currentMonthStatuses['on-process'] || 0,
          lastMonthStatuses['in-progress'] || lastMonthStatuses['on-process'] || 0
        ),
        scheduled: calculateGrowth(
          currentMonthStatuses['scheduled'] || 0,
          lastMonthStatuses['scheduled'] || 0
        ),
        completed: calculateGrowth(
          currentMonthStatuses['completed'] || 0,
          lastMonthStatuses['completed'] || 0
        ),
        freeCalls: calculateGrowth(
          (currentMonthTypes['free call'] || 0) + (currentMonthTypes['free_call'] || 0) + (currentMonthTypes['null'] || 0),
          (lastMonthTypes['free call'] || 0) + (lastMonthTypes['free_call'] || 0) + (lastMonthTypes['null'] || 0)
        ),
        amcCalls: calculateGrowth(
          currentMonthTypes['amc call'] || currentMonthTypes['amc_call'] || 0,
          lastMonthTypes['amc call'] || lastMonthTypes['amc_call'] || 0
        ),
        paidCalls: calculateGrowth(
          currentMonthTypes['per call'] || currentMonthTypes['per_call'] || currentMonthTypes['paid call'] || 0,
          lastMonthTypes['per call'] || lastMonthTypes['per_call'] || lastMonthTypes['paid call'] || 0
        ),
        recentCalls: calculateGrowth(recentCalls, 0) // Compare with baseline
      },
      // Add metadata for debugging and frontend compatibility
      metadata: {
        hasError: false,
        errorMessage: null,
        lastUpdated: new Date().toISOString(),
        currentMonth: currentMonthStart.toISOString().split('T')[0],
        lastMonth: lastMonthStart.toISOString().split('T')[0],
        generatedAt: new Date().toISOString(),
        tenantId: tenantId
      }
    };

    console.log('📊 Final response data - callsByType:', responseData.callsByType);
    console.log('📊 Final response data - totalCalls:', responseData.totalCalls);

    res.json({
      success: true,
      message: 'Service call statistics retrieved successfully',
      data: responseData,
    });

  } catch (error) {
    logger.error('Get service call stats error:', error);

    // Return fallback data structure to prevent frontend crashes
    const fallbackData = {
      totalCalls: 0,
      recentCalls: 0,
      callsByStatus: [],
      callsByType: {
        freeCalls: 0,
        amcCalls: 0,
        paidCalls: 0
      },
      monthlyGrowth: {
        total: 0,
        inProgress: 0,
        scheduled: 0,
        completed: 0,
        freeCalls: 0,
        amcCalls: 0,
        paidCalls: 0,
        recentCalls: 0
      },
      metadata: {
        error: true,
        errorMessage: 'Data temporarily unavailable',
        generatedAt: new Date().toISOString(),
        tenantId: req.user?.tenant?.id || null
      }
    };

    res.status(500).json({
      success: false,
      message: 'Failed to fetch service call statistics',
      data: fallbackData, // Include fallback data
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack
      } : undefined,
    });
  }
};

/**
 * Test email functionality
 */
export const testEmail = async (req, res) => {
  try {
    const { to = '<EMAIL>', testType = 'connection' } = req.body;

    logger.info('Testing email functionality', { to, testType });

    // Test email connection first
    const connectionTest = await emailService.testEmailConnection();

    logger.info('Email connection test result:', connectionTest);

    if (!connectionTest.success) {
      return res.json({
        success: false,
        message: 'Email service connection failed',
        details: connectionTest
      });
    }

    let emailResult = null;

    // Send different types of test emails based on testType
    if (testType === 'service_creation') {
      // Test service creation notification
      const testServiceData = {
        id: 'TEST-001',
        serviceNumber: 'SRV-2024-TEST001',
        service_number: 'SRV-2024-TEST001',
        service_type: 'onsite',
        status: { name: 'Open', code: 'OPEN' },
        scheduled_date: new Date().toISOString().split('T')[0],
        description: 'Test service creation notification',
        tenant_id: req.user?.tenant?.id || 1
      };

      const testCustomerData = {
        id: 'test-customer',
        name: 'Test Customer',
        company_name: 'Test Company',
        email: to
      };

      emailResult = await emailService.sendServiceNotificationEmail(
        testServiceData,
        testCustomerData,
        'service_created'
      );

    } else if (testType === 'service_completion') {
      // Test service completion email via notification service (unified approach)
      const testServiceData = {
        id: 'TEST-002',
        serviceNumber: 'SRV-2024-TEST002',
        service_number: 'SRV-2024-TEST002',
        service_type: 'onsite',
        status: { name: 'Completed', code: 'COMPLETED' },
        scheduled_date: new Date().toISOString().split('T')[0],
        description: 'Test service completion notification',
        tenant_id: req.user?.tenant?.id || 1
      };

      const testCustomerData = {
        id: 'test-customer-2',
        name: 'Test Customer',
        company_name: 'Test Company',
        email: to
      };

      emailResult = await notificationService.sendServiceNotification(
        testServiceData,
        testCustomerData,
        'service_completed'
      );

    } else if (testType === 'user_welcome') {
      // Test user welcome email
      const testUserData = {
        id: 'test-user-001',
        email: to,
        first_name: 'Test',
        last_name: 'User',
        phone: '+91 9876543210'
      };

      emailResult = await emailService.sendWelcomeEmail(testUserData);

    } else if (testType === 'user_unsubscribe') {
      // Test user unsubscribe email
      const testUserData = {
        id: 'test-user-002',
        email: to,
        first_name: 'Test',
        last_name: 'User',
        phone: '+91 9876543210'
      };

      emailResult = await emailService.sendUnsubscribeEmail(testUserData);

    } else if (testType === 'customer_welcome') {
      // Test customer welcome email
      const testCustomerData = {
        id: 'test-customer-003',
        customer_code: 'CUST0001',
        company_name: 'Test Company Pvt Ltd',
        contact_person: 'Test Contact Person',
        phone: '+91 9876543210',
        email: to
      };

      emailResult = await emailService.sendCustomerWelcomeEmail(testCustomerData);

    } else {
      // Simple connection test - send basic test email
      emailResult = {
        success: true,
        message: 'Connection test successful - no email sent',
        messageId: 'connection-test-only'
      };
    }

    res.json({
      success: true,
      message: 'Email test completed successfully',
      details: {
        connectionTest,
        emailResult,
        configuration: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT,
          secure: process.env.SMTP_SECURE,
          user: process.env.SMTP_USER,
          from: process.env.EMAIL_FROM
        }
      }
    });

  } catch (error) {
    logger.error('Email test error:', error);
    res.status(500).json({
      success: false,
      message: 'Email test failed',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    });
  }
};

/**
 * Get email logs
 */
export const getEmailLogs = async (req, res) => {
  try {
    const { limit = 100 } = req.query;

    const result = await emailService.getEmailLogs(parseInt(limit));

    res.json({
      success: true,
      message: 'Email logs retrieved successfully',
      data: result.logs,
      total: result.total || 0
    });
  } catch (error) {
    logger.error('Get email logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve email logs',
      error: error.message
    });
  }
};

/**
 * Clear email logs
 */
export const clearEmailLogs = async (req, res) => {
  try {
    const result = await emailService.clearEmailLogs();

    res.json({
      success: true,
      message: 'Email logs cleared successfully'
    });
  } catch (error) {
    logger.error('Clear email logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear email logs',
      error: error.message
    });
  }
};

/**
 * Get email send status log (human-readable format)
 */
export const getEmailSendStatusLog = async (req, res) => {
  try {
    const { lines = 50 } = req.query;

    const result = await emailService.getEmailSendStatusLog(parseInt(lines));

    res.json({
      success: true,
      message: 'Email send status log retrieved successfully',
      data: {
        log: result.log,
        entries: result.lines,
        total: result.total || 0
      }
    });
  } catch (error) {
    logger.error('Get email send status log error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve email send status log',
      error: error.message
    });
  }
};

/**
 * Get email send statistics
 */
export const getEmailSendStatistics = async (req, res) => {
  try {
    const result = await emailService.getEmailSendStatistics();

    res.json({
      success: true,
      message: 'Email send statistics retrieved successfully',
      data: result.stats
    });
  } catch (error) {
    logger.error('Get email send statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve email send statistics',
      error: error.message
    });
  }
};

/**
 * Delete service call
 */
export const deleteServiceCall = async (req, res) => {
  try {
    const { id } = req.params;
    const tenantId = req.user.tenant.id;

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: tenantId,
      },
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    // Check if service call can be deleted (business logic)
    // For now, allow deletion of any service call, but you can add restrictions here
    // For example: only allow deletion of draft or pending service calls

    await serviceCall.destroy();

    logger.info('Service call deleted successfully:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      deletedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Service call deleted successfully',
    });

  } catch (error) {
    logger.error('Delete service call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete service call',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Debug endpoint to check database status
 */
export const debugDatabase = async (req, res) => {
  try {
    // Check if service_calls table exists and its structure
    const tableInfo = await models.sequelize.query(
      "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'service_calls' ORDER BY ordinal_position",
      { type: models.sequelize.QueryTypes.SELECT }
    );

    // Check if type_of_calls table exists
    const typeOfCallsExists = await models.sequelize.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'type_of_calls')",
      { type: models.sequelize.QueryTypes.SELECT }
    );

    // Check service calls count
    const serviceCallsCount = await models.ServiceCall.count();

    // Check if TypeOfCall model is available
    const typeOfCallModelExists = !!models.TypeOfCall;

    res.json({
      success: true,
      data: {
        serviceCallsTableColumns: tableInfo,
        typeOfCallsTableExists: typeOfCallsExists[0]?.exists || false,
        serviceCallsCount,
        typeOfCallModelExists,
        modelsAvailable: Object.keys(models).filter(key => key !== 'sequelize' && key !== 'Sequelize'),
      }
    });

  } catch (error) {
    logger.error('Database debug error:', error);
    res.status(500).json({
      success: false,
      message: 'Database debug failed',
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack,
        sql: error.sql
      } : undefined,
    });
  }
};

/**
 * Get time tracking summary for a service call
 */
export const getTimeTrackingSummary = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'category'],
        },
      ],
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    const timeTrackingSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);

    res.json({
      success: true,
      message: 'Time tracking summary retrieved successfully',
      data: timeTrackingSummary,
    });
  } catch (error) {
    logger.error('Error getting time tracking summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get time tracking summary',
      error: error.message,
    });
  }
};

/**
 * Get time tracking statistics for dashboard
 */
export const getTimeTrackingStats = async (req, res) => {
  try {
    const { dateFrom, dateTo } = req.query;

    const stats = await TimeTrackingService.getTimeTrackingStats(
      req.user.tenant.id,
      dateFrom,
      dateTo
    );

    res.json({
      success: true,
      message: 'Time tracking statistics retrieved successfully',
      data: stats,
    });
  } catch (error) {
    logger.error('Error getting time tracking stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get time tracking statistics',
      error: error.message,
    });
  }
};

/**
 * Get real-time timer status for a service call
 * This endpoint provides current timer state and accumulated time
 */
export const getTimerStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'color', 'category']
        }
      ]
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    // Get real-time timer data from backend
    const timeTrackingSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);
    const currentAccumulatedTime = TimeTrackingService.getCurrentAccumulatedTime(serviceCall);
    const isTimerRunning = TimeTrackingService.isTimerRunning(serviceCall.time_tracking_history || []);
    const isTimerPaused = TimeTrackingService.isTimerPaused(serviceCall.time_tracking_history || []);

    // Format time for display
    const formatTimeDisplay = (seconds) => {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    };

    // Debug logging for timer status API
    console.log('🔍 Timer Status API Debug:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      statusCode: serviceCall.status?.code,
      statusName: serviceCall.status?.name,
      isTimerRunning,
      isTimerPaused,
      currentAccumulatedTime,
      storedTotalSeconds: serviceCall.total_time_seconds,
      timeHistoryLength: (serviceCall.time_tracking_history || []).length,
      lastHistoryAction: (serviceCall.time_tracking_history || []).length > 0 ?
        serviceCall.time_tracking_history[serviceCall.time_tracking_history.length - 1].action : 'none'
    });

    const timerData = {
      service_call_id: serviceCall.id,
      call_number: serviceCall.call_number,
      status: {
        id: serviceCall.status?.id,
        name: serviceCall.status?.name,
        code: serviceCall.status?.code,
        color: serviceCall.status?.color
      },
      timer: {
        is_running: isTimerRunning,
        is_paused: isTimerPaused,
        current_accumulated_seconds: currentAccumulatedTime,
        current_accumulated_formatted: formatTimeDisplay(currentAccumulatedTime),
        started_at: serviceCall.started_at,
        total_time_seconds: serviceCall.total_time_seconds || 0,
        total_time_formatted: formatTimeDisplay(serviceCall.total_time_seconds || 0)
      },
      time_tracking_summary: timeTrackingSummary,
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      message: 'Timer status retrieved successfully',
      data: timerData,
    });

  } catch (error) {
    logger.error('Error getting timer status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get timer status',
      error: error.message,
    });
  }
};

/**
 * Get filter options for service calls (statuses, types, etc.)
 */
export const getServiceCallFilters = async (req, res) => {
  try {
    // Get all active call statuses
    const callStatuses = await models.CallStatus.findAll({
      where: { is_active: true },
      attributes: ['id', 'name', 'code', 'category', 'color'],
      order: [['sort_order', 'ASC'], ['name', 'ASC']],
    });

    // Get all active type of calls if available
    let typeOfCalls = [];
    if (models.TypeOfCall) {
      typeOfCalls = await models.TypeOfCall.findAll({
        where: { is_active: true },
        attributes: ['id', 'name', 'code', 'category', 'service_type'],
        order: [['sort_order', 'ASC'], ['name', 'ASC']],
      });
    }

    // Get priority options
    const priorities = [
      { value: 'low', label: 'Low', color: '#10b981' },
      { value: 'medium', label: 'Medium', color: '#f59e0b' },
      { value: 'high', label: 'High', color: '#ef4444' },
      { value: 'critical', label: 'Critical', color: '#dc2626' },
    ];

    // Get call type options
    const callTypes = [
      { value: 'online', label: 'Online', icon: '💻' },
      { value: 'onsite', label: 'Onsite', icon: '🏢' },
      { value: 'phone', label: 'Phone', icon: '📞' },
      { value: 'email', label: 'Email', icon: '📧' },
    ];

    // Get call billing type options
    const callBillingTypes = [
      { value: 'free_call', label: 'Free Call', icon: '🆓' },
      { value: 'amc_call', label: 'AMC Call', icon: '🔧' },
      { value: 'per_call', label: 'Per Call', icon: '💰' },
    ];

    res.json({
      success: true,
      message: 'Service call filters retrieved successfully',
      data: {
        statuses: callStatuses.map(status => ({
          id: status.id,
          value: status.code,
          label: status.name,
          category: status.category,
          color: status.color,
        })),
        typeOfCalls: typeOfCalls.map(type => ({
          id: type.id,
          value: type.code,
          label: type.name,
          category: type.category,
          serviceType: type.service_type,
        })),
        priorities,
        callTypes,
        callBillingTypes,
      },
    });
  } catch (error) {
    logger.error('Error getting service call filters:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get service call filters',
      error: error.message,
    });
  }
};

/**
 * Get comprehensive timer history for a service call
 */
export const getServiceCallTimerHistory = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🕐 Getting timer history for service call:', {
      serviceCallId: id,
      userId: req.user.id,
      timestamp: new Date().toISOString()
    });

    // Get service call with timer history
    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'color', 'category']
        },
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'display_name']
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found'
      });
    }

    // Get comprehensive timer history
    const timeHistory = serviceCall.time_tracking_history || [];

    // Process timer history with enhanced details
    const processedHistory = timeHistory.map((entry, index) => {
      return {
        id: index + 1,
        timestamp: entry.timestamp,
        event_type: entry.event_type || entry.action,
        action: entry.action,
        status_from: entry.status_from,
        status_to: entry.status_to,
        user_id: entry.user_id,

        // Session details
        session_id: entry.session_id,
        start_time: entry.start_time,
        end_time: entry.end_time || entry.pause_time,
        duration_seconds: entry.session_duration_seconds || entry.duration_of_session || 0,
        duration_formatted: entry.session_duration_seconds ?
          TimeTrackingService.formatTimeDisplay(entry.session_duration_seconds) :
          (entry.duration_of_session ? TimeTrackingService.formatTimeDisplay(entry.duration_of_session) : '00:00:00'),

        // Cumulative tracking
        cumulative_time_seconds: entry.cumulative_time_seconds || 0,
        cumulative_time_formatted: entry.cumulative_time_formatted || TimeTrackingService.formatTimeDisplay(entry.cumulative_time_seconds || 0),

        // Event description
        event_description: entry.event_description || `Timer ${(entry.event_type || entry.action).replace('timer_', '')} - Status changed from ${entry.status_from} to ${entry.status_to}`,

        // Additional metadata
        triggered_by: entry.triggered_by || 'status_change',
        accumulated_seconds_at_start: entry.accumulated_seconds_at_start || 0
      };
    });

    // Get current timer summary
    const timerSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);

    const response = {
      success: true,
      data: {
        service_call: {
          id: serviceCall.id,
          call_number: serviceCall.call_number,
          customer: serviceCall.customer?.company_name || serviceCall.customer?.display_name,
          status: serviceCall.status,
          assigned_to: serviceCall.creator ?
            `${serviceCall.creator.first_name} ${serviceCall.creator.last_name}` :
            'Unassigned'
        },
        timer_history: processedHistory,
        timer_summary: timerSummary,
        statistics: {
          total_events: processedHistory.length,
          start_events: processedHistory.filter(e => e.event_type === 'timer_started').length,
          pause_events: processedHistory.filter(e => e.event_type === 'timer_paused').length,
          resume_events: processedHistory.filter(e => e.event_type === 'timer_resumed').length,
          stop_events: processedHistory.filter(e => ['timer_completed', 'timer_stopped', 'timer_cancelled'].includes(e.event_type)).length,
          total_sessions: processedHistory.filter(e => e.duration_seconds > 0).length,
          current_status: serviceCall.status?.name || 'Unknown'
        }
      }
    };

    console.log('✅ Timer history retrieved successfully:', {
      serviceCallId: id,
      totalEvents: processedHistory.length,
      currentStatus: serviceCall.status?.name
    });

    res.json(response);

  } catch (error) {
    console.error('❌ Error getting timer history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get timer history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
