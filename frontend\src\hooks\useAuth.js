import { useState, useEffect } from 'react';
import { authAPI } from '../services/api';
import { STORAGE_KEYS } from '../utils/constants';
import { authManager } from '../utils/authManager';

// Real authentication hook with backend integration
export const useAuth = () => {
  const [authState, setAuthState] = useState(authManager.getAuthState());

  useEffect(() => {
    // Initialize auth manager and subscribe to changes
    const initializeAuth = async () => {
      await authManager.initialize();
    };

    // Subscribe to auth state changes
    const unsubscribe = authManager.subscribe((newState) => {
      setAuthState(newState);
    });

    initializeAuth();

    return unsubscribe;
  }, []);

  const login = async (credentials) => {
    try {
      authManager.setAuthState({ isLoading: true });
      console.log('Attempting login for:', credentials.email);

      // Make real API call to backend
      const response = await authAPI.login(credentials);
      console.log('Login response:', response.data);

      // Handle different response structures
      let token, refreshToken, user;

      if (response.data && response.data.token) {
        // Backend returns { status: 'success', token, refreshToken, user }
        token = response.data.token;
        refreshToken = response.data.refreshToken;
        user = response.data.user;
      } else if (response.data && response.data.data) {
        // Backend returns { status: 'success', data: { token, user, ... } }
        token = response.data.data.token;
        refreshToken = response.data.data.refreshToken;
        user = response.data.data.user;
      } else {
        throw new Error('Invalid response from server');
      }

      if (token && user) {
        // Store tokens and user data using consistent keys
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        if (refreshToken) {
          localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        }
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));

        // Update auth manager state
        authManager.setAuthState({
          isAuthenticated: true,
          user,
          isLoading: false
        });

        console.log('Login successful, user authenticated:', user.email || user.name);

        return { success: true, user };
      }
      throw new Error('Missing token or user data in response');

    } catch (error) {
      console.error('Login failed:', error);
      authManager.setAuthState({ isLoading: false });
      return { success: false, error: error.response?.data?.message || error.message };
    }
  };

  const logout = async () => {
    try {
      // Call backend logout endpoint to invalidate token
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      if (token) {
        try {
          await authAPI.logout();
        } catch (error) {
          console.warn('Logout API call failed:', error.message);
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear localStorage and state
      authManager.clearAuthData();

      // Update auth manager state
      authManager.setAuthState({
        isAuthenticated: false,
        user: null,
        isLoading: false
      });

      // Force redirect to login
      window.location.href = '/auth/login';
    }
  };

  const updateUser = (userData) => {
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(userData));
    authManager.setAuthState({
      user: userData
    });
  };

  return {
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    user: authState.user,
    login,
    logout,
    updateUser
  };
};
