import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { swaggerDefinition } from '../config/swagger.js';
import { logger } from './logger.js';
import appConfig from '../../config/app.js';

/**
 * Swagger JSDoc configuration options
 */
const swaggerOptions = {
  definition: swaggerDefinition,
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './src/models/*.js'
  ]
};

/**
 * Generate OpenAPI specification
 */
export const generateSwaggerSpec = () => {
  try {
    const specs = swaggerJsdoc(swaggerOptions);
    logger.info('✅ Swagger specification generated successfully');
    return specs;
  } catch (error) {
    logger.error('❌ Failed to generate Swagger specification:', error);
    throw error;
  }
};

/**
 * Swagger UI configuration options
 */
const swaggerUiOptions = {
  explorer: true,
  swaggerOptions: {
    docExpansion: 'none',
    filter: true,
    showRequestHeaders: true,
    showCommonExtensions: true,
    tryItOutEnabled: true,
    requestInterceptor: (req) => {
      // Add any request interceptors here
      return req;
    },
    responseInterceptor: (res) => {
      // Add any response interceptors here
      return res;
    }
  },
  customCss: `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info { margin: 20px 0; }
    .swagger-ui .info .title { color: #2f69b3; }
    .swagger-ui .scheme-container { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    .swagger-ui .btn.authorize { background-color: #2f69b3; border-color: #2f69b3; }
    .swagger-ui .btn.authorize:hover { background-color: #1e4a7a; border-color: #1e4a7a; }
    .swagger-ui .opblock.opblock-post { border-color: #28a745; background: rgba(40, 167, 69, 0.1); }
    .swagger-ui .opblock.opblock-get { border-color: #007bff; background: rgba(0, 123, 255, 0.1); }
    .swagger-ui .opblock.opblock-put { border-color: #ffc107; background: rgba(255, 193, 7, 0.1); }
    .swagger-ui .opblock.opblock-delete { border-color: #dc3545; background: rgba(220, 53, 69, 0.1); }
    .swagger-ui .opblock.opblock-patch { border-color: #6c757d; background: rgba(108, 117, 125, 0.1); }
  `,
  customSiteTitle: 'TallyCRM API Documentation',
  customfavIcon: '/favicon.ico'
};

/**
 * Setup Swagger documentation for Express app
 * @param {Express} app - Express application instance
 */
export const setupSwagger = (app) => {
  try {
    // Generate the OpenAPI specification
    const specs = generateSwaggerSpec();
    
    // Setup Swagger UI
    app.use(
      appConfig.development.swaggerPath,
      swaggerUi.serve,
      swaggerUi.setup(specs, swaggerUiOptions)
    );
    
    // Provide JSON endpoint for the spec
    app.get(`${appConfig.development.swaggerPath}.json`, (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(specs);
    });
    
    logger.info(`📚 Swagger UI available at: ${appConfig.app.url}${appConfig.development.swaggerPath}`);
    logger.info(`📄 OpenAPI spec available at: ${appConfig.app.url}${appConfig.development.swaggerPath}.json`);
    
    return specs;
  } catch (error) {
    logger.error('❌ Failed to setup Swagger documentation:', error);
    throw error;
  }
};

/**
 * Middleware to add Swagger documentation to routes
 * This can be used to conditionally enable Swagger only in development
 * @param {Express} app - Express application instance
 */
export const conditionalSwagger = (app) => {
  if (appConfig.development.enableSwagger && appConfig.app.env === 'development') {
    setupSwagger(app);
    return true;
  } else {
    logger.info('📚 Swagger documentation disabled');
    return false;
  }
};

/**
 * Generate example responses for common HTTP status codes
 */
export const generateCommonResponses = () => {
  return {
    200: {
      description: 'Success',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/SuccessResponse'
          }
        }
      }
    },
    201: {
      description: 'Created',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/SuccessResponse'
          }
        }
      }
    },
    400: {
      description: 'Bad Request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Validation failed',
            errors: {
              email: 'Email is required',
              password: 'Password must be at least 6 characters'
            }
          }
        }
      }
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Authentication required',
            errors: {}
          }
        }
      }
    },
    403: {
      description: 'Forbidden',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Insufficient permissions',
            errors: {}
          }
        }
      }
    },
    404: {
      description: 'Not Found',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Resource not found',
            errors: {}
          }
        }
      }
    },
    422: {
      description: 'Unprocessable Entity',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Validation failed',
            errors: {
              field: 'Field validation error message'
            }
          }
        }
      }
    },
    500: {
      description: 'Internal Server Error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse'
          },
          example: {
            success: false,
            message: 'Internal server error',
            errors: {}
          }
        }
      }
    }
  };
};

export default {
  setupSwagger,
  conditionalSwagger,
  generateSwaggerSpec,
  generateCommonResponses
};
