import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('leave_types', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Leave type name (e.g., Sick Leave, Annual Leave)',
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Short code for leave type (e.g., SL, AL)',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Detailed description of leave type',
    },
    annual_quota: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Annual quota of days for this leave type',
    },
    is_paid: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type is paid',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type requires approval',
    },
    advance_notice_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Minimum advance notice required in days',
    },
    max_consecutive_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum consecutive days allowed for this leave type',
    },
    min_days_per_request: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 0.5,
      comment: 'Minimum days per leave request',
    },
    max_days_per_request: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum days per leave request',
    },
    carry_forward_allowed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether unused leaves can be carried forward',
    },
    max_carry_forward: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Maximum days that can be carried forward',
    },
    carry_forward_expiry_months: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 12,
      comment: 'Months after which carried forward leaves expire',
    },
    encashment_allowed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether unused leaves can be encashed',
    },
    max_encashment_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Maximum days that can be encashed',
    },
    applicable_after_months: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Leave type applicable after these many months of employment',
    },
    gender_specific: {
      type: DataTypes.ENUM('all', 'male', 'female'),
      allowNull: false,
      defaultValue: 'all',
      comment: 'Gender applicability of leave type',
    },
    requires_document: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether supporting documents are required',
    },
    document_required_after_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Documents required if leave is more than these days',
    },
    allow_half_day: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether half-day leaves are allowed',
    },
    allow_negative_balance: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether negative balance is allowed',
    },
    max_negative_balance: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Maximum negative balance allowed',
    },
    color_code: {
      type: DataTypes.STRING(7),
      allowNull: true,
      comment: 'Color code for calendar display',
    },
    sort_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Sort order for display',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this leave type is active',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('leave_types', ['tenant_id']);
  await queryInterface.addIndex('leave_types', ['tenant_id', 'code'], {
    unique: true,
    name: 'unique_tenant_leave_code',
  });
  await queryInterface.addIndex('leave_types', ['is_active']);
  await queryInterface.addIndex('leave_types', ['sort_order']);

  console.log('✅ Created leave_types table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('leave_types');
  console.log('✅ Dropped leave_types table');
};
