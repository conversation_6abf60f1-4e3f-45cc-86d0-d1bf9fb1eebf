import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';
import moment from 'moment';

/**
 * Simple test to check validation errors
 */
async function testValidation() {
  try {
    console.log('🔍 Testing validation errors...');

    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    const user = await models.User.findOne({
      where: { tenant_id: tenant.id },
      attributes: ['id']
    });

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);
    console.log(`✅ Using user: ${user?.id || 'No user found'}`);

    // Test 1: Create customer
    console.log('\n📝 Test 1: Creating customer...');
    try {
      const customer = await models.Customer.create({
        tenant_id: tenant.id,
        customer_code: 'TEST999',
        company_name: 'Test Validation Customer',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        is_active: true,
        created_by: user?.id,
      });
      console.log(`✅ Customer created: ${customer.id}`);

      // Test 2: Create TSS
      console.log('\n📝 Test 2: Creating TSS...');
      try {
        const tss = await models.CustomerTSS.create({
          customer_id: customer.id,
          tss_number: 'TSS-TEST999',
          start_date: moment().subtract(365, 'days').format('YYYY-MM-DD'),
          expiry_date: moment().add(30, 'days').format('YYYY-MM-DD'),
          renewal_date: moment().add(30, 'days').format('YYYY-MM-DD'),
          amount: 15000,
          status: 'active',
          is_active: true,
          created_by: user?.id,
        });
        console.log(`✅ TSS created: ${tss.id}`);

        // Test 3: Create AMC
        console.log('\n📝 Test 3: Creating AMC...');
        try {
          const amc = await models.CustomerAMC.create({
            customer_id: customer.id,
            tss_id: tss.id,
            amc_number: 'AMC-TEST999',
            start_date: moment().subtract(365, 'days').format('YYYY-MM-DD'),
            end_date: moment().add(30, 'days').format('YYYY-MM-DD'),
            renewal_date: moment().add(30, 'days').format('YYYY-MM-DD'),
            contract_value: 25000,
            status: 'active',
            is_active: true,
            created_by: user?.id,
          });
          console.log(`✅ AMC created: ${amc.id}`);

          // Test 4: Create notification schedule
          console.log('\n📝 Test 4: Creating notification schedule...');
          try {
            const schedule = await models.NotificationSchedule.create({
              tenant_id: tenant.id,
              customer_id: customer.id,
              renewal_type: 'amc',
              renewal_record_id: amc.id,
              expiry_date: moment().add(30, 'days').format('YYYY-MM-DD'),
              notify_at: moment().add(1, 'day').format('YYYY-MM-DD'),
              days_before_expiry: 30,
              status: 'scheduled',
            });
            console.log(`✅ Notification schedule created: ${schedule.id}`);

            // Test 5: Try to create duplicate
            console.log('\n📝 Test 5: Testing duplicate prevention...');
            try {
              await models.NotificationSchedule.create({
                tenant_id: tenant.id,
                customer_id: customer.id,
                renewal_type: 'amc',
                renewal_record_id: amc.id,
                expiry_date: moment().add(30, 'days').format('YYYY-MM-DD'),
                notify_at: moment().add(1, 'day').format('YYYY-MM-DD'),
                days_before_expiry: 30,
                status: 'scheduled',
              });
              console.log('❌ Duplicate was created (should have been prevented)');
            } catch (duplicateError) {
              console.log(`✅ Duplicate prevented: ${duplicateError.message}`);
            }

            console.log('\n🎉 All validation tests passed!');

          } catch (scheduleError) {
            console.error('❌ Notification schedule creation failed:', scheduleError.message);
            console.error('Full error:', scheduleError);
          }

        } catch (amcError) {
          console.error('❌ AMC creation failed:', amcError.message);
          console.error('Full error:', amcError);
        }

      } catch (tssError) {
        console.error('❌ TSS creation failed:', tssError.message);
        console.error('Full error:', tssError);
      }

    } catch (customerError) {
      console.error('❌ Customer creation failed:', customerError.message);
      console.error('Full error:', customerError);
    }

  } catch (error) {
    console.error('❌ Validation test failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting Simple Validation Test\n');
    await testValidation();
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
main();
