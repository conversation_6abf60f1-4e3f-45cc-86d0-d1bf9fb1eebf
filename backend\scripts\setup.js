#!/usr/bin/env node

/**
 * Database Setup Script
 *
 * This script sets up the database for TallyCRM by:
 * 1. Running all migrations
 * 2. Seeding master data
 * 3. Creating a default admin user (optional)
 */

import { logger } from '../src/utils/logger.js';
import { runMigrations } from '../src/migrations/migrate.js';
import seedMasterData from '../src/seeders/001-seed-master-data.js';
import seedSubscriptionPlans from '../src/seeders/002-seed-subscription-plans.js';
import models from '../src/models/index.js';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

const setupDatabase = async () => {
  try {
    logger.info('🚀 Starting TallyCRM database setup...');

    // Step 1: Run migrations
    logger.info('📋 Step 1: Running database migrations...');
    await runMigrations();
    logger.info('✅ Migrations completed successfully');

    // Step 2: Seed master data
    logger.info('🌱 Step 2: Seeding master data...');
    await seedMasterData();
    logger.info('✅ Master data seeded successfully');

    // Step 2.5: Seed subscription plans
    logger.info('🌱 Step 2.5: Seeding subscription plans...');
    await seedSubscriptionPlans();
    logger.info('✅ Subscription plans seeded successfully');

    // Step 3: Create default admin user (if requested)
    const createAdmin = process.argv.includes('--create-admin');
    if (createAdmin) {
      logger.info('👤 Step 3: Creating default admin user...');
      await createDefaultAdmin();
      logger.info('✅ Default admin user created successfully');
    }

    logger.info('🎉 Database setup completed successfully!');

    if (createAdmin) {
      logger.info('');
      logger.info('📝 Default Admin Credentials:');
      logger.info('   Email: <EMAIL>');
      logger.info('   Password: Admin@123');
      logger.info('   Company: TallyCRM Demo');
      logger.info('');
      logger.info('⚠️  Please change the default password after first login!');
    }

    process.exit(0);

  } catch (error) {
    logger.error('❌ Database setup failed:', error);
    process.exit(1);
  }
};

const createDefaultAdmin = async () => {
  try {
    // Check if admin user already exists
    const existingAdmin = await models.User.findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      logger.info('ℹ️  Default admin user already exists, skipping creation');
      return;
    }

    // Create default tenant
    let tenant = await models.Tenant.findOne({
      where: { slug: 'tallycrm-demo' }
    });

    if (!tenant) {
      tenant = await models.Tenant.create({
        name: 'TallyCRM Demo',
        slug: 'tallycrm-demo',
        subscription_plan: 'enterprise',
        subscription_status: 'active',
        subscription_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        max_users: 100,
        max_customers: 10000,
        is_active: true,
        settings: {
          timezone: 'Asia/Kolkata',
          currency: 'INR',
          dateFormat: 'DD/MM/YYYY',
          timeFormat: '24h',
        },
      });
    }

    // Create admin user - password will be hashed automatically by the model hook
    const emailVerificationToken = uuidv4();

    const adminUser = await models.User.create({
      tenant_id: tenant.id,
      email: '<EMAIL>',
      password: 'Admin@123', // Pass plain text - model will hash it
      first_name: 'Admin',
      last_name: 'User',
      phone: '+91-9999999999',
      is_active: true,
      is_verified: true, // Pre-verified for demo
      email_verification_token: emailVerificationToken,
      email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: {
          email: true,
          browser: true,
          sms: false,
        },
      },
    });

    // Assign admin role
    const adminRole = await models.Role.findOne({
      where: { slug: 'admin' }
    });

    if (adminRole) {
      await models.UserRole.create({
        user_id: adminUser.id,
        role_id: adminRole.id,
        assigned_at: new Date(),
        is_active: true,
      });
    }

    // Create a sample executive record
    await models.Executive.create({
      tenant_id: tenant.id,
      user_id: adminUser.id,
      employee_code: 'EMP001',
      first_name: 'Admin',
      last_name: 'User',
      email: '<EMAIL>',
      phone: '+91-9999999999',
      department: 'management',
      date_of_joining: new Date(),
      is_active: true,
      skills: ['Administration', 'Management', 'CRM'],
      areas_covered: [],
    });

    logger.info('✅ Default admin user and tenant created successfully');

  } catch (error) {
    logger.error('❌ Failed to create default admin user:', error);
    throw error;
  }
};

// Show usage information
const showUsage = () => {
  console.log('');
  console.log('TallyCRM Database Setup Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/setup.js                 # Run migrations and seed data');
  console.log('  node scripts/setup.js --create-admin  # Also create default admin user');
  console.log('');
  console.log('Options:');
  console.log('  --create-admin    Create a default admin user for testing');
  console.log('  --help           Show this help message');
  console.log('');
};

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
  process.exit(0);
}

// Run the setup
setupDatabase();
