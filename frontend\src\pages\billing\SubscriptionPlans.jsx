import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { subscriptionService, formatCurrency } from '../../services/subscriptionService';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Spinner } from '../../components/ui/Spinner';

const SubscriptionPlans = () => {
  const [plans, setPlans] = useState([]);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState(null);
  const [billingInterval, setBillingInterval] = useState('monthly');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [plansResponse, subscriptionResponse] = await Promise.all([
        subscriptionService.getPlans(),
        subscriptionService.getCurrentSubscription().catch(() => ({ data: null })),
      ]);

      setPlans(plansResponse.data.plans || []);
      setCurrentSubscription(subscriptionResponse.data?.subscription || null);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load subscription plans');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId) => {
    try {
      setProcessingPlan(planId);
      const response = await subscriptionService.createCheckoutSession(planId, billingInterval);

      if (response.data.url) {
        // Redirect to Stripe Checkout
        window.location.href = response.data.url;
      } else {
        toast.error('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to start checkout process');
    } finally {
      setProcessingPlan(null);
    }
  };

  const getPlanPrice = (plan) => {
    return billingInterval === 'yearly' ? plan.price_yearly : plan.price_monthly;
  };

  const getYearlySavings = (plan) => {
    if (!plan.price_yearly) return 0;
    const monthlyTotal = plan.price_monthly * 12;
    const savings = monthlyTotal - plan.price_yearly;
    return Math.round((savings / monthlyTotal) * 100);
  };

  const isCurrentPlan = (planSlug) => {
    return currentSubscription?.plan?.slug === planSlug;
  };

  const canUpgradeTo = (plan) => {
    if (!currentSubscription?.plan) return true;
    return currentSubscription.plan.sort_order < plan.sort_order;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Subscription Plans - TallyCRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h1>
          <p className="text-lg text-gray-600 mb-8">
            Select the perfect plan for your Tally reseller business
          </p>

          {/* Billing Toggle */}
          <div className="inline-flex items-center bg-gray-100 rounded-lg p-1 mb-8">
            <button
              onClick={() => setBillingInterval('monthly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingInterval === 'monthly'
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingInterval('yearly')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                billingInterval === 'yearly'
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Yearly
              <span className="ml-1 text-xs text-green-600 font-semibold">Save up to 20%</span>
            </button>
          </div>
        </div>

        {/* Current Subscription Alert */}
        {currentSubscription && (
          <Card className="bg-blue-50 border-blue-200">
            <div className="p-4">
              <div className="flex items-center">
                <i className="bi bi-info-circle text-blue-600 mr-3"></i>
                <div>
                  <p className="text-blue-800 font-medium">
                    You're currently on the {currentSubscription.plan.name} plan
                  </p>
                  <p className="text-blue-600 text-sm">
                    {currentSubscription.status === 'trial'
                      ? `Trial ends on ${new Date(currentSubscription.trial_end).toLocaleDateString()}`
                      : `Next billing date: ${new Date(currentSubscription.current_period_end).toLocaleDateString()}`
                    }
                  </p>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${
                plan.is_popular
                  ? 'border-theme-500 shadow-lg scale-105'
                  : 'border-gray-200'
              } ${
                isCurrentPlan(plan.slug)
                  ? 'ring-2 ring-success-500 ring-opacity-50'
                  : ''
              }`}
            >
              {plan.is_popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge variant="primary" className="px-3 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}

              {isCurrentPlan(plan.slug) && (
                <div className="absolute -top-3 right-4">
                  <Badge variant="success" className="px-3 py-1">
                    Current Plan
                  </Badge>
                </div>
              )}

              <div className="p-6">
                {/* Plan Header */}
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                  <div className="mb-4">
                    <span className="text-3xl font-bold text-gray-900">
                      {formatCurrency(getPlanPrice(plan))}
                    </span>
                    <span className="text-gray-600 ml-1">
                      /{billingInterval === 'yearly' ? 'year' : 'month'}
                    </span>
                  </div>

                  {billingInterval === 'yearly' && plan.price_yearly && (
                    <div className="text-sm text-green-600 font-medium">
                      Save {getYearlySavings(plan)}% annually
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm">
                    <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                    <span>{plan.max_users} Users</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                    <span>{plan.max_customers} Customers</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                    <span>{plan.max_service_calls} Service Calls/month</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                    <span>{plan.max_storage_gb}GB Storage</span>
                  </div>

                  {/* Feature flags */}
                  {plan.features.advanced_reports && (
                    <div className="flex items-center text-sm">
                      <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                      <span>Advanced Reports</span>
                    </div>
                  )}
                  {plan.features.api_access && (
                    <div className="flex items-center text-sm">
                      <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                      <span>API Access</span>
                    </div>
                  )}
                  {plan.features.priority_support && (
                    <div className="flex items-center text-sm">
                      <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                      <span>Priority Support</span>
                    </div>
                  )}
                </div>

                {/* Action Button */}
                <div className="text-center">
                  {isCurrentPlan(plan.slug) ? (
                    <Button variant="outline" disabled className="w-full">
                      Current Plan
                    </Button>
                  ) : canUpgradeTo(plan) ? (
                    <Button
                      onClick={() => handleUpgrade(plan.id)}
                      disabled={processingPlan === plan.id}
                      className="w-full btn-theme-primary"
                    >
                      {processingPlan === plan.id ? (
                        <>
                          <Spinner size="sm" className="mr-2" />
                          Processing...
                        </>
                      ) : (
                        'Upgrade Now'
                      )}
                    </Button>
                  ) : (
                    <Button variant="outline" disabled className="w-full">
                      Contact Sales
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Trial Info */}
        <Card className="bg-gray-50">
          <div className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Start Your Free Trial Today
            </h3>
            <p className="text-gray-600 mb-4">
              All plans come with a 14-day free trial. No credit card required to start.
            </p>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center">
                <i className="bi bi-shield-check text-green-500 mr-2"></i>
                <span>No Setup Fees</span>
              </div>
              <div className="flex items-center">
                <i className="bi bi-arrow-repeat text-green-500 mr-2"></i>
                <span>Cancel Anytime</span>
              </div>
              <div className="flex items-center">
                <i className="bi bi-headset text-green-500 mr-2"></i>
                <span>24/7 Support</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

export default SubscriptionPlans;
