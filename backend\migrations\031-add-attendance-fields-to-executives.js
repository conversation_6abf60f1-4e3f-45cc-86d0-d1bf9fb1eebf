export const up = async (queryInterface, Sequelize) => {
  // Add attendance-related fields to executives table
  await queryInterface.addColumn('executives', 'employee_code', {
    type: Sequelize.STRING(20),
    allowNull: true,
    unique: true,
  });

  await queryInterface.addColumn('executives', 'shift_start_time', {
    type: Sequelize.TIME,
    allowNull: true,
    defaultValue: '09:00:00',
  });

  await queryInterface.addColumn('executives', 'shift_end_time', {
    type: Sequelize.TIME,
    allowNull: true,
    defaultValue: '18:00:00',
  });

  await queryInterface.addColumn('executives', 'attendance_policy_id', {
    type: Sequelize.UUID,
    allowNull: true,
    references: {
      model: 'attendance_policies',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL',
  });

  await queryInterface.addColumn('executives', 'attendance_tracking_enabled', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  });

  await queryInterface.addColumn('executives', 'manager_id', {
    type: Sequelize.UUID,
    allowNull: true,
    references: {
      model: 'executives',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL',
  });

  await queryInterface.addColumn('executives', 'joining_date', {
    type: Sequelize.DATEONLY,
    allowNull: true,
  });

  await queryInterface.addColumn('executives', 'employment_type', {
    type: Sequelize.ENUM('full_time', 'part_time', 'contract', 'intern'),
    allowNull: false,
    defaultValue: 'full_time',
  });

  await queryInterface.addColumn('executives', 'work_location', {
    type: Sequelize.ENUM('office', 'remote', 'hybrid'),
    allowNull: false,
    defaultValue: 'office',
  });

  await queryInterface.addColumn('executives', 'probation_period_months', {
    type: Sequelize.INTEGER,
    allowNull: false,
    defaultValue: 6,
  });

  await queryInterface.addColumn('executives', 'notice_period_days', {
    type: Sequelize.INTEGER,
    allowNull: false,
    defaultValue: 30,
  });

  // Add indexes
  await queryInterface.addIndex('executives', ['employee_code'], { unique: true });
  await queryInterface.addIndex('executives', ['manager_id']);
  await queryInterface.addIndex('executives', ['attendance_policy_id']);
  await queryInterface.addIndex('executives', ['employment_type']);
  await queryInterface.addIndex('executives', ['work_location']);
};

export const down = async (queryInterface, Sequelize) => {
  // Remove the added columns
  await queryInterface.removeColumn('executives', 'employee_code');
  await queryInterface.removeColumn('executives', 'shift_start_time');
  await queryInterface.removeColumn('executives', 'shift_end_time');
  await queryInterface.removeColumn('executives', 'attendance_policy_id');
  await queryInterface.removeColumn('executives', 'attendance_tracking_enabled');
  await queryInterface.removeColumn('executives', 'manager_id');
  await queryInterface.removeColumn('executives', 'joining_date');
  await queryInterface.removeColumn('executives', 'employment_type');
  await queryInterface.removeColumn('executives', 'work_location');
  await queryInterface.removeColumn('executives', 'probation_period_months');
  await queryInterface.removeColumn('executives', 'notice_period_days');
};
