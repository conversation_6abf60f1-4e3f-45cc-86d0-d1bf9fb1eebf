import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Add lead permissions
  const leadPermissions = [
    {
      id: '88888888-8888-8888-8888-888888888881',
      name: 'Leads Create',
      slug: 'leads.create',
      description: 'Create leads',
      module: 'leads',
      action: 'create',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '88888888-8888-8888-8888-888888888882',
      name: 'Leads Read',
      slug: 'leads.read',
      description: 'Read leads',
      module: 'leads',
      action: 'read',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '88888888-8888-8888-8888-888888888883',
      name: 'Leads Update',
      slug: 'leads.update',
      description: 'Update leads',
      module: 'leads',
      action: 'update',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '88888888-8888-8888-8888-888888888884',
      name: 'Leads Delete',
      slug: 'leads.delete',
      description: 'Delete leads',
      module: 'leads',
      action: 'delete',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '88888888-8888-8888-8888-888888888885',
      name: 'Leads Export',
      slug: 'leads.export',
      description: 'Export leads',
      module: 'leads',
      action: 'export',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '88888888-8888-8888-8888-888888888886',
      name: 'Leads Import',
      slug: 'leads.import',
      description: 'Import leads',
      module: 'leads',
      action: 'import',
      resource: 'leads',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  // Insert lead permissions
  await queryInterface.bulkInsert('permissions', leadPermissions);

  console.log('✅ Added lead permissions');
};

export const down = async (queryInterface) => {
  // Remove lead permissions
  await queryInterface.bulkDelete('permissions', {
    module: 'leads',
  });

  console.log('✅ Removed lead permissions');
};
