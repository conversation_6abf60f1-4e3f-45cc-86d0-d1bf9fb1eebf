import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Middleware to validate that the tenant in the JWT token actually exists
 * This prevents foreign key constraint errors when creating records
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const validateTenantExists = async (req, res, next) => {
  try {
    // Skip validation if no user (handled by auth middleware)
    if (!req.user || !req.user.tenant || !req.user.tenant.id) {
      return next();
    }

    const tenantId = req.user.tenant.id;

    // Check if tenant exists in database
    const tenant = await models.Tenant.findByPk(tenantId, {
      attributes: ['id', 'name', 'slug', 'is_active', 'subscription_status']
    });

    if (!tenant) {
      logger.warn('Tenant validation failed - tenant not found:', {
        tenantId,
        userId: req.user.id,
        userEmail: req.user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      return res.status(401).json({
        status: 'error',
        message: 'Your session is invalid. Please log in again to continue.',
        code: 'INVALID_TENANT',
        details: {
          userFriendlyMessage: 'Your session is invalid. Please log in again to continue.',
          reason: 'tenant_not_found',
          action: 'login_required'
        }
      });
    }

    // Check if tenant is active
    if (!tenant.is_active) {
      logger.warn('Tenant validation failed - tenant inactive:', {
        tenantId,
        tenantName: tenant.name,
        userId: req.user.id,
        userEmail: req.user.email,
        ip: req.ip
      });

      return res.status(403).json({
        status: 'error',
        message: 'Your company account has been deactivated. Please contact support for assistance.',
        code: 'TENANT_INACTIVE',
        details: {
          userFriendlyMessage: 'Your company account has been deactivated. Please contact support for assistance.',
          reason: 'tenant_inactive'
        }
      });
    }

    // Check subscription status
    if (tenant.subscription_status === 'suspended') {
      logger.warn('Tenant validation failed - subscription suspended:', {
        tenantId,
        tenantName: tenant.name,
        subscriptionStatus: tenant.subscription_status,
        userId: req.user.id,
        userEmail: req.user.email,
        ip: req.ip
      });

      return res.status(403).json({
        status: 'error',
        message: 'Your company subscription has been suspended. Please contact support to reactivate your account.',
        code: 'SUBSCRIPTION_SUSPENDED',
        details: {
          userFriendlyMessage: 'Your company subscription has been suspended. Please contact support to reactivate your account.',
          reason: 'subscription_suspended'
        }
      });
    }

    // Add full tenant info to request for use in controllers
    req.user.tenant = {
      id: tenant.id,
      name: tenant.name,
      slug: tenant.slug,
      is_active: tenant.is_active,
      subscription_status: tenant.subscription_status
    };

    next();
  } catch (error) {
    logger.error('Tenant validation error:', {
      error: error.message,
      stack: error.stack,
      tenantId: req.user?.tenant?.id,
      userId: req.user?.id,
      ip: req.ip
    });

    return res.status(500).json({
      status: 'error',
      message: 'Server error during tenant validation. Please try again.',
      code: 'TENANT_VALIDATION_ERROR',
      details: {
        userFriendlyMessage: 'Server error during tenant validation. Please try again.'
      }
    });
  }
};

/**
 * Middleware to log tenant validation warnings without blocking requests
 * Useful for debugging tenant-related issues
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const logTenantValidation = async (req, res, next) => {
  try {
    if (req.user && req.user.tenant && req.user.tenant.id) {
      const tenantId = req.user.tenant.id;
      
      // Quick check without blocking the request
      const tenant = await models.Tenant.findByPk(tenantId, {
        attributes: ['id', 'name', 'is_active']
      });

      if (!tenant) {
        logger.warn('No subscription found for tenant:', {
          tenantId,
          userId: req.user.id,
          endpoint: req.path,
          method: req.method
        });
      }
    }
  } catch (error) {
    // Log error but don't block request
    logger.debug('Tenant validation logging error:', error.message);
  }
  
  next();
};
