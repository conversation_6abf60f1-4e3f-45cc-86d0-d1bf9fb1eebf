// Test script to verify executive update fix
const API_BASE_URL = 'http://localhost:8082/api/v1';

async function testExecutiveUpdate() {
    console.log('🧪 Testing Executive Update Fix...');
    
    // Test payload with the problematic 'name' field
    const testPayload = {
        "name": "<PERSON><PERSON>",
        "id": "4b23bc45-79c7-47b1-9242-33e3750fa29b",
        "tenant_id": "0b5c9744-27f8-44b6-a138-96e6084b104c",
        "user_id": null,
        "employee_code": "EXE001",
        "first_name": "<PERSON><PERSON>",
        "last_name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+919402795036",
        "alternate_phone": null,
        "designation_id": "04192771-e7a4-4c52-8b51-476cdf104e24",
        "staff_role_id": null,
        "department": "technical",
        "date_of_joining": "2024-08-28",
        "date_of_birth": null,
        "address": "474 Indiranagar",
        "city": "Hyderabad",
        "state": "Delhi",
        "postal_code": "260428",
        "emergency_contact_name": null,
        "emergency_contact_phone": null,
        "salary": "26903.00",
        "commission_rate": "8.00",
        "target_amount": "126757.00",
        "skills": [
            "Tally",
            "Customer Support",
            "Technical Troubleshooting",
            "Data Migration",
            "Training"
        ],
        "areas_covered": [],
        "is_active": true,
        "profile_image": null,
        "notes": null
    };
    
    try {
        console.log('📤 Sending PUT request to update executive...');
        const response = await fetch(`${API_BASE_URL}/executives/4b23bc45-79c7-47b1-9242-33e3750fa29b`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                // Note: This will fail due to authentication, but we can see if the error changes
            },
            body: JSON.stringify(testPayload)
        });
        
        const data = await response.json();
        
        console.log('📊 Response Status:', response.status);
        console.log('📊 Response Data:', JSON.stringify(data, null, 2));
        
        if (response.status === 401) {
            console.log('✅ Expected authentication error - this means the endpoint is working');
            console.log('✅ The 500 error should be fixed now');
        } else if (response.status === 500) {
            console.log('❌ Still getting 500 error - fix may not be working');
        } else {
            console.log('✅ Got different response - fix appears to be working');
        }
        
    } catch (error) {
        console.error('❌ Request failed:', error.message);
    }
}

// Run the test
testExecutiveUpdate();
