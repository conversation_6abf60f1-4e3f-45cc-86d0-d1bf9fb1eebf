task1 .1.1 reports page overview, sales performance, customer analytics, revenue analysis wanna make row wire to column at under Reports & Analytics
Comprehensive insights into your business performance text  and make th #root > div > div.flex.min-h-screen.overflow-x-hidden > div.flex-1.flex.flex-col.min-h-screen.transition-all.duration-300.ease-in-out.overflow-x-hidden.md\:ml-\[220px\].ml-0 > main > div > div > div.grid.grid-cols-1.xl\:grid-cols-4.gap-3.sm\:gap-4.mb-4.sm\:mb-6 > div.xl\:col-span-3 the section full width i wanna move the 4 rows reports page overview, sales performance, customer analytics, revenue analysis  to top left to top
1.2 the filters export pdf and excel not working 
1.3 entire page wanna make from db values via api 
1.4 sales performance no table or analitics there is no UI so fix that  i seen only empty at that page 
1.5 customer analitics also no table or data or content to show the ddata
1.6 recenue analytics also needs to be fixed no data to show
task 2.
2.1 in settings general page CRUD 
2.2 purple theme make it CRUD to change entire page theme 
2.3 settings->profile CRUD
2.4 notifications
2.5 database and secuirity make  something here please
2.6 reduce the text font size for Settings
Manage your application preferences and configurations

5
Settings Sections

Active
System Status

Daily
Backup Schedule

Purple
Current Theme

 theses texts and also others pages also 
 task 3. profile page
 3.1 make a CRUD including the profile image 
 task 4 . dashboard text sizes for header and other every page text should be same format for heading description and tables of contents

 task5. in thedrop downs and others data if anything mocked please change that from db and check the instructions page what are all the data defined by masters and what are all the data comes form database that will tell you 