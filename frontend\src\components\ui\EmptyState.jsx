import React from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '../../utils/helpers';
import Button from './Button';

/**
 * Enhanced Empty State Component
 * Provides actionable guidance and CTAs instead of generic "No data" messages
 */
const EmptyState = ({
  type = 'general',
  title,
  description,
  icon: CustomIcon,
  actions = [],
  className = '',
  size = 'md',
  ...props
}) => {
  const navigate = useNavigate();

  // Predefined empty state configurations
  const emptyStateConfigs = {
    'service-calls': {
      icon: '📞',
      title: 'No service calls yet',
      description: 'Start by creating your first service call to track customer requests and support tickets.',
      actions: [
        {
          label: 'Create Service Call',
          variant: 'primary',
          icon: 'bi-plus-circle',
          onClick: () => navigate('/services/add')
        },
        {
          label: 'View All Services',
          variant: 'outline',
          icon: 'bi-list-ul',
          onClick: () => navigate('/services')
        }
      ]
    },
    'customers': {
      icon: '👥',
      title: 'No customers found',
      description: 'Add your first customer to start managing relationships and service requests.',
      actions: [
        {
          label: 'Add Customer',
          variant: 'primary',
          icon: 'bi-person-plus',
          onClick: () => navigate('/customers/add')
        },
        {
          label: 'Import Customers',
          variant: 'outline',
          icon: 'bi-upload',
          onClick: () => navigate('/customers/import')
        }
      ]
    },
    'recent-activity': {
      icon: '📊',
      title: 'No recent activity',
      description: 'Activity will appear here as you create service calls, add customers, and record sales.',
      actions: [
        {
          label: 'Create Service Call',
          variant: 'primary',
          icon: 'bi-telephone-plus',
          onClick: () => navigate('/services/add')
        }
      ]
    },
    'dashboard-data': {
      icon: '📈',
      title: 'Getting started',
      description: 'Your dashboard will show insights once you start adding customers and service calls.',
      actions: [
        {
          label: 'Add First Customer',
          variant: 'primary',
          icon: 'bi-person-plus',
          onClick: () => navigate('/customers/add')
        },
        {
          label: 'Quick Setup Guide',
          variant: 'outline',
          icon: 'bi-question-circle',
          onClick: () => navigate('/help/getting-started')
        }
      ]
    },
    'search-results': {
      icon: '🔍',
      title: 'No results found',
      description: 'Try adjusting your search criteria or filters to find what you\'re looking for.',
      actions: [
        {
          label: 'Clear Filters',
          variant: 'outline',
          icon: 'bi-x-circle',
          onClick: () => window.location.reload()
        }
      ]
    },
    'reports': {
      icon: '📋',
      title: 'No data for report',
      description: 'Generate reports once you have service calls and customer data in the system.',
      actions: [
        {
          label: 'Add Sample Data',
          variant: 'primary',
          icon: 'bi-database-add',
          onClick: () => navigate('/setup/sample-data')
        }
      ]
    }
  };

  // Get configuration or use custom props
  const config = emptyStateConfigs[type] || {
    icon: CustomIcon || '📄',
    title: title || 'No data available',
    description: description || 'There\'s nothing to show here right now.',
    actions: actions
  };

  // Size configurations
  const sizeClasses = {
    sm: {
      container: 'py-8 px-4',
      icon: 'w-12 h-12 text-4xl',
      title: 'text-base',
      description: 'text-sm',
      button: 'text-sm px-3 py-2'
    },
    md: {
      container: 'py-12 px-6',
      icon: 'w-16 h-16 text-5xl',
      title: 'text-lg',
      description: 'text-base',
      button: 'text-sm px-4 py-2'
    },
    lg: {
      container: 'py-16 px-8',
      icon: 'w-20 h-20 text-6xl',
      title: 'text-xl',
      description: 'text-lg',
      button: 'text-base px-6 py-3'
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div
      className={cn(
        'text-center',
        currentSize.container,
        className
      )}
      {...props}
    >
      {/* Icon */}
      <div className={cn(
        'mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-6',
        currentSize.icon
      )}>
        {typeof config.icon === 'string' ? (
          <span className={currentSize.icon}>{config.icon}</span>
        ) : (
          config.icon && <config.icon className="text-gray-400" />
        )}
      </div>

      {/* Title */}
      <h3 className={cn(
        'font-semibold text-gray-900 mb-2',
        currentSize.title
      )}>
        {config.title}
      </h3>

      {/* Description */}
      <p className={cn(
        'text-gray-500 mb-6 max-w-md mx-auto',
        currentSize.description
      )}>
        {config.description}
      </p>

      {/* Actions */}
      {config.actions && config.actions.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
          {config.actions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'primary'}
              onClick={action.onClick}
              className={cn(
                'inline-flex items-center',
                currentSize.button
              )}
            >
              {action.icon && (
                <i className={cn(action.icon, 'mr-2')} />
              )}
              {action.label}
            </Button>
          ))}
        </div>
      )}

      {/* Additional Help Text */}
      {type === 'search-results' && (
        <div className="mt-6 text-sm text-gray-400 space-y-1">
          <p>• Check your spelling and try again</p>
          <p>• Use different keywords</p>
          <p>• Remove some filters</p>
        </div>
      )}

      {type === 'dashboard-data' && (
        <div className="mt-6 text-sm text-gray-400 space-y-1">
          <p>• Add customers to track relationships</p>
          <p>• Create service calls to manage support</p>
          <p>• Record sales to monitor revenue</p>
        </div>
      )}
    </div>
  );
};

/**
 * Specialized Empty State Components
 */
export const ServiceCallsEmptyState = (props) => (
  <EmptyState type="service-calls" {...props} />
);

export const CustomersEmptyState = (props) => (
  <EmptyState type="customers" {...props} />
);

export const RecentActivityEmptyState = (props) => (
  <EmptyState type="recent-activity" {...props} />
);

export const DashboardEmptyState = (props) => (
  <EmptyState type="dashboard-data" {...props} />
);

export const SearchResultsEmptyState = (props) => (
  <EmptyState type="search-results" {...props} />
);

export const ReportsEmptyState = (props) => (
  <EmptyState type="reports" {...props} />
);

export default EmptyState;
