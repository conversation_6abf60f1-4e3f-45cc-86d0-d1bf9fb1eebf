import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Clock, 
  Calendar, 
  BarChart3, 
  Settings, 
  Users, 
  DollarSign,
  Menu,
  X,
  Home
} from 'lucide-react';
import { Button } from '../ui';
import { useAuth } from '../../hooks/useAuth';

const AttendanceNavigation = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/attendance',
      icon: Home,
      description: 'Overview and quick actions'
    },
    {
      name: 'Check In/Out',
      href: '/attendance/checkin',
      icon: Clock,
      description: 'Mark attendance'
    },
    {
      name: 'Leave Management',
      href: '/attendance/leaves',
      icon: Calendar,
      description: 'Request and manage leaves'
    },
    {
      name: 'Reports',
      href: '/attendance/reports',
      icon: BarChart3,
      description: 'Attendance analytics'
    },
    {
      name: 'Payroll',
      href: '/attendance/payroll',
      icon: DollarSign,
      description: 'Salary and payroll',
      roles: ['admin', 'hr', 'manager']
    },
    {
      name: 'Team Management',
      href: '/attendance/team',
      icon: Users,
      description: 'Manage team attendance',
      roles: ['admin', 'hr', 'manager']
    },
    {
      name: 'Configuration',
      href: '/attendance/config',
      icon: Settings,
      description: 'System settings',
      roles: ['admin', 'hr']
    }
  ];

  const hasPermission = (item) => {
    if (!item.roles) return true;
    return item.roles.some(role => user?.roles?.includes(role));
  };

  const isActive = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const handleNavigation = (href) => {
    navigate(href);
    setMobileMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <div className="lg:hidden bg-white shadow-sm border-b">
        <div className="flex items-center justify-between p-4">
          <h1 className="text-lg font-semibold text-gray-900">Attendance</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0">
          <div className="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-4">
              <h1 className="text-xl font-semibold text-gray-900">Attendance Management</h1>
            </div>
            
            <div className="mt-8 flex-grow flex flex-col">
              <nav className="flex-1 px-2 space-y-1">
                {navigationItems.filter(hasPermission).map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.name}
                      onClick={() => handleNavigation(item.href)}
                      className={`
                        group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left
                        ${isActive(item.href)
                          ? 'bg-blue-100 text-blue-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `}
                    >
                      <Icon
                        className={`
                          mr-3 flex-shrink-0 h-5 w-5
                          ${isActive(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}
                        `}
                      />
                      <div>
                        <div>{item.name}</div>
                        <div className="text-xs text-gray-500">{item.description}</div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {mobileMenuOpen && (
          <div className="lg:hidden fixed inset-0 z-40 flex">
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setMobileMenuOpen(false)} />
            
            <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
              <div className="absolute top-0 right-0 -mr-12 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMobileMenuOpen(false)}
                  className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                >
                  <X className="h-6 w-6 text-white" />
                </Button>
              </div>
              
              <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                <div className="flex-shrink-0 flex items-center px-4">
                  <h1 className="text-lg font-semibold text-gray-900">Attendance</h1>
                </div>
                
                <nav className="mt-5 px-2 space-y-1">
                  {navigationItems.filter(hasPermission).map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.name}
                        onClick={() => handleNavigation(item.href)}
                        className={`
                          group flex items-center px-2 py-2 text-base font-medium rounded-md w-full text-left
                          ${isActive(item.href)
                            ? 'bg-blue-100 text-blue-900'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }
                        `}
                      >
                        <Icon
                          className={`
                            mr-4 flex-shrink-0 h-6 w-6
                            ${isActive(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'}
                          `}
                        />
                        <div>
                          <div>{item.name}</div>
                          <div className="text-sm text-gray-500">{item.description}</div>
                        </div>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="lg:pl-64 flex flex-col flex-1">
          <main className="flex-1">
            <div className="py-6">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-30">
        <div className="grid grid-cols-4 gap-1">
          {navigationItems.filter(hasPermission).slice(0, 4).map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => handleNavigation(item.href)}
                className={`
                  flex flex-col items-center justify-center py-2 px-1 text-xs
                  ${isActive(item.href)
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-900'
                  }
                `}
              >
                <Icon className="h-5 w-5 mb-1" />
                <span className="truncate">{item.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Bottom padding for mobile navigation */}
      <div className="lg:hidden h-16" />
    </div>
  );
};

export default AttendanceNavigation;
