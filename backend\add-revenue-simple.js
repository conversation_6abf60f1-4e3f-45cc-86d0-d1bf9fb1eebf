/**
 * Simple Revenue Data Addition Script
 * Adds revenue amounts to service calls for analytics testing
 */

import models from './src/models/index.js';

async function addRevenueData() {
  try {
    console.log('🚀 Adding revenue data to service calls...\n');

    // Get all service calls without revenue
    const serviceCalls = await models.ServiceCall.findAll({
      where: {
        total_amount: 0
      },
      limit: 200
    });

    console.log(`📊 Found ${serviceCalls.length} service calls to update`);

    let updatedCount = 0;
    
    for (const call of serviceCalls) {
      // Determine billing type randomly
      const billingTypes = ['free_call', 'amc_call', 'per_call'];
      const billingType = billingTypes[Math.floor(Math.random() * billingTypes.length)];
      
      let serviceCharges = 0;
      let travelCharges = 0;
      let totalAmount = 0;
      let isBillable = false;

      // Set amounts based on billing type
      switch (billingType) {
        case 'free_call':
          serviceCharges = 0;
          travelCharges = 0;
          totalAmount = 0;
          isBillable = false;
          break;
        case 'amc_call':
          serviceCharges = Math.floor(Math.random() * 2000) + 500; // 500-2500
          travelCharges = Math.floor(Math.random() * 500); // 0-500
          totalAmount = serviceCharges + travelCharges;
          isBillable = true;
          break;
        case 'per_call':
          serviceCharges = Math.floor(Math.random() * 5000) + 1000; // 1000-6000
          travelCharges = Math.floor(Math.random() * 1000); // 0-1000
          totalAmount = serviceCharges + travelCharges;
          isBillable = true;
          break;
      }

      // Update the service call
      await call.update({
        call_billing_type: billingType,
        service_charges: serviceCharges,
        travel_charges: travelCharges,
        total_amount: totalAmount,
        is_billable: isBillable,
        hourly_rate: billingType === 'per_call' ? Math.floor(Math.random() * 500) + 200 : null
      });

      updatedCount++;
    }

    console.log(`✅ Updated ${updatedCount} service calls with revenue data`);

    // Create some sales data if none exists
    const existingSales = await models.Sale.count();
    
    if (existingSales === 0) {
      console.log('\n📈 Creating sample sales data...');
      
      // Get some customers and users for sales
      const customers = await models.Customer.findAll({ limit: 20 });
      const users = await models.User.findAll({ limit: 5 });
      
      if (customers.length > 0 && users.length > 0) {
        for (let i = 0; i < 30; i++) {
          const customer = customers[Math.floor(Math.random() * customers.length)];
          const user = users[Math.floor(Math.random() * users.length)];
          
          const saleAmount = Math.floor(Math.random() * 50000) + 10000; // 10K-60K
          const saleDate = new Date();
          saleDate.setDate(saleDate.getDate() - Math.floor(Math.random() * 90)); // Last 90 days
          
          const paymentStatuses = ['paid', 'pending', 'overdue', 'partial'];
          const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
          
          await models.Sale.create({
            tenant_id: customer.tenant_id,
            customer_id: customer.id,
            created_by: user.id,
            sale_number: `SALE${Date.now().toString().slice(-6)}${i.toString().padStart(3, '0')}`,
            total_amount: saleAmount,
            sale_date: saleDate,
            payment_status: paymentStatus,
            sale_type: 'new',
            status: 'completed'
          });
        }
        
        console.log('✅ Created 30 sample sales records');
      }
    }

    // Generate summary
    console.log('\n📊 Revenue Summary:');
    
    const revenueStats = await models.ServiceCall.findAll({
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'totalCalls'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'totalRevenue'],
        [models.sequelize.fn('AVG', models.sequelize.col('total_amount')), 'avgRevenue'],
        [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN total_amount > 0 THEN 1 END")), 'billableCalls'],
        [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'free_call' THEN 1 END")), 'freeCalls'],
        [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'amc_call' THEN 1 END")), 'amcCalls'],
        [models.sequelize.fn('COUNT', models.sequelize.literal("CASE WHEN call_billing_type = 'per_call' THEN 1 END")), 'perCalls']
      ],
      raw: true
    });

    const salesStats = await models.Sale.findAll({
      attributes: [
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'totalSales'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'totalSalesRevenue'],
        [models.sequelize.fn('AVG', models.sequelize.col('total_amount')), 'avgSaleAmount']
      ],
      raw: true
    });

    const serviceStats = revenueStats[0];
    const saleStats = salesStats[0];
    
    console.log('\n🔧 Service Calls:');
    console.log(`   💰 Total Revenue: ₹${parseFloat(serviceStats.totalRevenue || 0).toLocaleString()}`);
    console.log(`   📞 Total Calls: ${serviceStats.totalCalls}`);
    console.log(`   💵 Average Revenue per Call: ₹${parseFloat(serviceStats.avgRevenue || 0).toFixed(2)}`);
    console.log(`   💸 Billable Calls: ${serviceStats.billableCalls}`);
    console.log(`   🆓 Free Calls: ${serviceStats.freeCalls}`);
    console.log(`   🔧 AMC Calls: ${serviceStats.amcCalls}`);
    console.log(`   💳 Per Calls: ${serviceStats.perCalls}`);
    
    console.log('\n💼 Sales:');
    console.log(`   💰 Total Sales Revenue: ₹${parseFloat(saleStats.totalSalesRevenue || 0).toLocaleString()}`);
    console.log(`   📊 Total Sales: ${saleStats.totalSales}`);
    console.log(`   💵 Average Sale Amount: ₹${parseFloat(saleStats.avgSaleAmount || 0).toFixed(2)}`);
    
    const combinedRevenue = parseFloat(serviceStats.totalRevenue || 0) + parseFloat(saleStats.totalSalesRevenue || 0);
    console.log('\n🎯 Combined Revenue:');
    console.log(`   💰 Total Combined Revenue: ₹${combinedRevenue.toLocaleString()}`);

    console.log('\n🎉 Revenue data added successfully!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Refresh the analytics pages in the frontend');
    console.log('2. Check the Financial Analytics page for revenue data');
    console.log('3. Verify Service Analytics shows billing type distribution');

  } catch (error) {
    console.error('❌ Error adding revenue data:', error);
  } finally {
    // Close database connection
    await models.sequelize.close();
  }
}

// Run the script
addRevenueData();
