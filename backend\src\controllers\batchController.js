import models from '../models/index.js';
import { Op } from 'sequelize';
import TimeTrackingService from '../services/timeTrackingService.js';

/**
 * Batch API Controller
 * Provides consolidated endpoints to reduce duplicate HTTP requests
 */

/**
 * Get timer status for multiple service calls in a single request
 * Reduces N individual timer status requests to 1 batch request
 */
const getTimerStatusBatch = async (req, res) => {
  try {
    console.log('🔍 Batch timer status request:', {
      body: req.body,
      user: req.user ? { id: req.user.id, tenant_id: req.user.tenant_id } : 'No user',
      headers: req.headers.authorization ? 'Has auth header' : 'No auth header'
    });

    const { serviceIds } = req.body;
    const tenantId = req.user?.tenant_id || req.user?.tenant?.id;

    if (!tenantId) {
      return res.status(401).json({
        success: false,
        message: 'User tenant information not found'
      });
    }

    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Service IDs array is required'
      });
    }

    // Limit batch size to prevent abuse
    if (serviceIds.length > 50) {
      return res.status(400).json({
        success: false,
        message: 'Maximum 50 service IDs allowed per batch request'
      });
    }

    // Fetch all service calls in one query
    const serviceCalls = await models.ServiceCall.findAll({
      where: {
        id: {
          [Op.in]: serviceIds
        },
        tenant_id: tenantId
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'category']
        }
      ]
    });

    console.log('🔍 Found service calls:', serviceCalls.length);

    // Generate timer status for each service call
    const results = serviceCalls.map((serviceCall, index) => {
      try {
        console.log(`🔍 Processing service call ${index + 1}/${serviceCalls.length}:`, {
          id: serviceCall.id,
          call_number: serviceCall.call_number,
          status: serviceCall.status?.code,
          has_time_history: !!(serviceCall.time_tracking_history && serviceCall.time_tracking_history.length > 0)
        });

        const timerSummary = TimeTrackingService.getTimeTrackingSummary(serviceCall);

        console.log(`✅ Timer summary generated for ${serviceCall.call_number}:`, {
          is_running: timerSummary.is_timer_running,
          is_paused: timerSummary.is_timer_paused,
          current_time: timerSummary.current_accumulated_time
        });

        return {
          serviceId: serviceCall.id,
          data: {
            timer: {
              is_running: timerSummary.is_timer_running,
              is_paused: timerSummary.is_timer_paused,
              current_accumulated_seconds: timerSummary.current_accumulated_time,
              current_accumulated_formatted: TimeTrackingService.formatTime(timerSummary.current_accumulated_time)
            },
            status: serviceCall.status,
            time_tracking_summary: timerSummary,
            timestamp: new Date().toISOString()
          }
        };
      } catch (error) {
        console.error(`❌ Error processing service call ${serviceCall.id}:`, error);
        throw error; // Re-throw to be caught by outer try-catch
      }
    });

    res.json({
      success: true,
      data: results,
      message: `Timer status retrieved for ${results.length} service calls`
    });

  } catch (error) {
    console.error('❌ Error in batch timer status:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    res.status(500).json({
      success: false,
      message: 'Failed to get batch timer status',
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined
    });
  }
};

/**
 * Get service calls with stats and filters in a single request
 * Consolidates multiple API calls into one
 */
const getServiceCallsBatchData = async (req, res) => {
  try {
    console.log('🔍 Batch service calls request:', {
      user: req.user ? { id: req.user.id, tenant_id: req.user.tenant_id, tenant: req.user.tenant } : 'No user',
      headers: req.headers.authorization ? 'Has auth header' : 'No auth header'
    });

    const tenantId = req.user?.tenant_id || req.user?.tenant?.id;

    if (!tenantId) {
      return res.status(401).json({
        success: false,
        message: 'User tenant information not found'
      });
    }
    const {
      page = 1,
      limit = 10,
      search,
      status,
      serviceType,
      includeStats = false,
      includeFilters = false
    } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = { tenant_id: tenantId };

    // Add search filter
    if (search) {
      whereClause[Op.or] = [
        { call_number: { [Op.iLike]: `%${search}%` } },
        { '$customer.company_name$': { [Op.iLike]: `%${search}%` } },
        { '$customer.display_name$': { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Add status filter
    if (status && status !== 'all') {
      whereClause['$status.code$'] = status;
    }

    // Add service type filter
    if (serviceType && serviceType !== 'all') {
      whereClause.call_billing_type = serviceType;
    }

    // Prepare parallel requests
    const requests = [];

    // 1. Service calls query
    requests.push(
      models.ServiceCall.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: models.Customer,
            as: 'customer',
            attributes: ['id', 'company_name', 'display_name', 'contact_person', 'city', 'state']
          },
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['id', 'name', 'code', 'category']
          },
          {
            model: models.Executive,
            as: 'assignedExecutive',
            attributes: ['id', 'first_name', 'last_name', 'employee_code']
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['created_at', 'DESC']]
      })
    );

    // 2. Stats query (if requested)
    if (includeStats) {
      requests.push(
        models.ServiceCall.findAll({
          where: { tenant_id: tenantId },
          include: [
            {
              model: models.CallStatus,
              as: 'status',
              attributes: ['name', 'code']
            }
          ],
          attributes: [
            'id',
            'call_billing_type',
            'created_at',
            'total_time_seconds'
          ]
        })
      );
    } else {
      requests.push(Promise.resolve(null));
    }

    // 3. Filter options query (if requested)
    if (includeFilters) {
      requests.push(
        Promise.all([
          models.CallStatus.findAll({
            where: { is_active: true },
            attributes: ['id', 'name', 'code'],
            order: [['sort_order', 'ASC']]
          }),
          models.ServiceCall.findAll({
            where: { tenant_id: tenantId },
            attributes: ['call_billing_type'],
            group: ['call_billing_type'],
            raw: true
          })
        ])
      );
    } else {
      requests.push(Promise.resolve(null));
    }

    // Execute all requests in parallel
    const [serviceCallsResult, statsData, filtersData] = await Promise.all(requests);

    // Process service calls
    const serviceCalls = serviceCallsResult.rows.map(serviceCall => ({
      ...serviceCall.toJSON(),
      time_tracking_summary: TimeTrackingService.getTimeTrackingSummary(serviceCall)
    }));

    const pagination = {
      page: parseInt(page),
      limit: parseInt(limit),
      total: serviceCallsResult.count,
      totalPages: Math.ceil(serviceCallsResult.count / limit)
    };

    // Process stats (if requested)
    let stats = {};
    if (includeStats && statsData) {
      const now = new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

      // Debug: Log sample billing types to understand the data
      console.log('📊 Batch API Debug - Sample billing types:', {
        totalCalls: statsData.length,
        sampleBillingTypes: statsData.slice(0, 10).map(call => ({
          id: call.id,
          call_billing_type: call.call_billing_type,
          is_billable: call.is_billable,
          is_under_amc: call.is_under_amc,
          service_charges: call.service_charges
        }))
      });

      stats = {
        totalCalls: statsData.length,
        recentCalls: statsData.filter(call => new Date(call.created_at) >= lastMonth).length,
        callsByStatus: statsData.reduce((acc, call) => {
          const status = call.status?.name || 'Unknown';
          const existing = acc.find(item => item.status === status);
          if (existing) {
            existing.count++;
          } else {
            acc.push({ status, count: 1 });
          }
          return acc;
        }, []),
        callsByType: {
          freeCalls: statsData.filter(call => {
            const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
            return (
              billingType === 'free_call' ||
              billingType === 'free call' ||
              billingType === 'free' ||
              billingType === null || // Include NULL billing types as free calls
              billingType === '' ||
              (!call.is_billable && !call.is_under_amc) ||
              (call.service_charges === 0 || call.service_charges === null)
            );
          }).length,
          amcCalls: statsData.filter(call => {
            const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
            return (
              billingType === 'amc_call' ||
              billingType === 'amc call' ||
              billingType === 'amc' ||
              call.is_under_amc === true
            );
          }).length,
          paidCalls: statsData.filter(call => {
            const billingType = call.call_billing_type ? String(call.call_billing_type).toLowerCase().trim() : null;
            return (
              billingType === 'per_call' ||
              billingType === 'per call' ||
              billingType === 'paid_call' ||
              billingType === 'paid call' ||
              billingType === 'paid' ||
              billingType === 'billable' ||
              (call.is_billable === true && !call.is_under_amc)
            );
          }).length
        }
      };

      // Debug: Log the calculated results
      console.log('📊 Batch API Calculated Results:', {
        totalCalls: stats.totalCalls,
        freeCalls: stats.callsByType.freeCalls,
        amcCalls: stats.callsByType.amcCalls,
        paidCalls: stats.callsByType.paidCalls
      });
    }

    // Process filters (if requested)
    let filters = {};
    if (includeFilters && filtersData) {
      const [statuses, callTypes] = filtersData;
      
      filters = {
        statuses: statuses.map(status => ({
          value: status.code,
          label: status.name
        })),
        typeOfCalls: [...new Set(callTypes.map(ct => ct.call_billing_type).filter(Boolean))]
          .map(type => ({
            value: type,
            label: type.split(' ').map(word => 
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ')
          }))
      };
    }

    res.json({
      success: true,
      data: {
        serviceCalls,
        pagination,
        ...(includeStats && { stats }),
        ...(includeFilters && { filters })
      },
      message: 'Service calls data retrieved successfully'
    });

  } catch (error) {
    console.error('Error in batch service calls data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get batch service calls data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  getTimerStatusBatch,
  getServiceCallsBatchData
};
