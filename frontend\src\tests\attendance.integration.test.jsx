import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import AttendanceDashboard from '../components/attendance/AttendanceDashboard';
import LeaveManagement from '../components/leave/LeaveManagement';
import PayrollManagement from '../components/payroll/PayrollManagement';
import MobileAttendance from '../components/attendance/MobileAttendance';
import { attendanceAPI, leaveAPI, payrollAPI } from '../services/api';

// Mock the API services
jest.mock('../services/api', () => ({
  attendanceAPI: {
    getTodayAttendance: jest.fn(),
    checkIn: jest.fn(),
    checkOut: jest.fn(),
    getRecords: jest.fn(),
    getSummary: jest.fn()
  },
  leaveAPI: {
    getTypes: jest.fn(),
    getRequests: jest.fn(),
    getBalance: jest.fn(),
    submitRequest: jest.fn(),
    cancelRequest: jest.fn(),
    getCalendar: jest.fn()
  },
  payrollAPI: {
    getRecords: jest.fn(),
    processPayroll: jest.fn(),
    approvePayroll: jest.fn(),
    markPaid: jest.fn()
  }
}));

// Mock geolocation
const mockGeolocation = {
  getCurrentPosition: jest.fn(),
  watchPosition: jest.fn()
};

Object.defineProperty(global.navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true
});

// Mock user context
const mockUser = {
  id: '1',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  roles: ['employee'],
  executive_id: '1'
};

const MockAuthProvider = ({ children }) => {
  return (
    <AuthProvider value={{ user: mockUser, isAuthenticated: true }}>
      {children}
    </AuthProvider>
  );
};

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <MockAuthProvider>
        {component}
      </MockAuthProvider>
    </BrowserRouter>
  );
};

describe('Attendance Management Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful geolocation
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 12.9716,
          longitude: 77.5946,
          accuracy: 10
        }
      });
    });
  });

  describe('AttendanceDashboard Integration', () => {
    it('should render dashboard and handle check-in flow', async () => {
      // Mock API responses
      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });

      attendanceAPI.checkIn.mockResolvedValue({
        data: {
          attendanceRecord: {
            id: '1',
            check_in_time: new Date().toISOString(),
            status: 'present'
          }
        }
      });

      renderWithProviders(<AttendanceDashboard />);

      // Wait for component to load
      await waitFor(() => {
        expect(screen.getByText('Attendance Dashboard')).toBeInTheDocument();
      });

      // Check if check-in button is present
      const checkInButton = screen.getByText('Check In');
      expect(checkInButton).toBeInTheDocument();

      // Simulate check-in
      await act(async () => {
        fireEvent.click(checkInButton);
      });

      // Verify API was called
      await waitFor(() => {
        expect(attendanceAPI.checkIn).toHaveBeenCalledWith({
          location: {
            latitude: 12.9716,
            longitude: 77.5946,
            accuracy: 10,
            timestamp: expect.any(String)
          },
          notes: ''
        });
      });
    });

    it('should show checked-in state after successful check-in', async () => {
      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: true,
          hasCheckedOut: false,
          attendanceRecord: {
            id: '1',
            check_in_time: '2024-01-01T09:00:00Z',
            status: 'present'
          }
        }
      });

      renderWithProviders(<AttendanceDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Check Out')).toBeInTheDocument();
      });

      // Should show check-in time
      expect(screen.getByText('09:00 AM')).toBeInTheDocument();
    });

    it('should handle check-in errors gracefully', async () => {
      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });

      attendanceAPI.checkIn.mockRejectedValue({
        response: {
          data: {
            message: 'You are outside the allowed check-in area'
          }
        }
      });

      renderWithProviders(<AttendanceDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Check In')).toBeInTheDocument();
      });

      const checkInButton = screen.getByText('Check In');
      
      await act(async () => {
        fireEvent.click(checkInButton);
      });

      // Should handle error (would show toast in real app)
      expect(attendanceAPI.checkIn).toHaveBeenCalled();
    });
  });

  describe('LeaveManagement Integration', () => {
    it('should render leave management and display leave balance', async () => {
      leaveAPI.getRequests.mockResolvedValue({
        data: {
          leaveRequests: []
        }
      });

      leaveAPI.getBalance.mockResolvedValue({
        data: {
          leaveBalances: [
            {
              id: '1',
              leaveType: { name: 'Annual Leave', code: 'AL' },
              allocated_days: 20,
              used_days: 5,
              remaining_days: 15
            }
          ]
        }
      });

      leaveAPI.getTypes.mockResolvedValue({
        data: {
          leaveTypes: [
            {
              id: '1',
              name: 'Annual Leave',
              code: 'AL',
              annual_quota: 20,
              is_paid: true
            }
          ]
        }
      });

      renderWithProviders(<LeaveManagement />);

      await waitFor(() => {
        expect(screen.getByText('Leave Management')).toBeInTheDocument();
      });

      // Should display leave balance
      expect(screen.getByText('15')).toBeInTheDocument(); // Total balance
      expect(screen.getByText('Annual Leave')).toBeInTheDocument();
    });

    it('should handle leave request submission', async () => {
      // Setup mocks
      leaveAPI.getRequests.mockResolvedValue({ data: { leaveRequests: [] } });
      leaveAPI.getBalance.mockResolvedValue({ data: { leaveBalances: [] } });
      leaveAPI.getTypes.mockResolvedValue({
        data: {
          leaveTypes: [
            {
              id: '1',
              name: 'Annual Leave',
              code: 'AL',
              annual_quota: 20,
              is_paid: true,
              allow_half_day: true
            }
          ]
        }
      });

      leaveAPI.submitRequest.mockResolvedValue({
        data: {
          leaveRequest: {
            id: '1',
            status: 'pending'
          }
        }
      });

      renderWithProviders(<LeaveManagement />);

      await waitFor(() => {
        expect(screen.getByText('New Request')).toBeInTheDocument();
      });

      // Click new request button
      const newRequestButton = screen.getByText('New Request');
      fireEvent.click(newRequestButton);

      // Form should be visible (in modal)
      await waitFor(() => {
        expect(screen.getByText('New Leave Request')).toBeInTheDocument();
      });
    });
  });

  describe('PayrollManagement Integration', () => {
    it('should render payroll management with records', async () => {
      payrollAPI.getRecords.mockResolvedValue({
        data: {
          payrollRecords: [
            {
              id: '1',
              employee: {
                first_name: 'John',
                last_name: 'Doe',
                employee_code: 'EMP001'
              },
              payroll_number: 'PAY-2024-01-001',
              gross_salary: 75000,
              total_deductions: 15000,
              net_salary: 60000,
              status: 'calculated'
            }
          ]
        }
      });

      renderWithProviders(<PayrollManagement />);

      await waitFor(() => {
        expect(screen.getByText('Payroll Management')).toBeInTheDocument();
      });

      // Should display payroll record
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('PAY-2024-01-001')).toBeInTheDocument();
    });

    it('should handle payroll processing', async () => {
      payrollAPI.getRecords.mockResolvedValue({
        data: { payrollRecords: [] }
      });

      payrollAPI.processPayroll.mockResolvedValue({
        data: {
          processed: [
            {
              employee_id: '1',
              payroll_number: 'PAY-2024-01-001'
            }
          ]
        }
      });

      renderWithProviders(<PayrollManagement />);

      await waitFor(() => {
        expect(screen.getByText('Process Payroll')).toBeInTheDocument();
      });

      const processButton = screen.getByText('Process Payroll');
      fireEvent.click(processButton);

      // Modal should open
      await waitFor(() => {
        expect(screen.getByText('Process Payroll')).toBeInTheDocument();
      });
    });
  });

  describe('MobileAttendance Integration', () => {
    it('should render mobile attendance interface', async () => {
      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });

      renderWithProviders(<MobileAttendance />);

      await waitFor(() => {
        expect(screen.getByText('Attendance')).toBeInTheDocument();
      });

      // Should show current time
      expect(screen.getByText(/\d{1,2}:\d{2}:\d{2}/)).toBeInTheDocument();

      // Should show check-in button
      expect(screen.getByText('Check In')).toBeInTheDocument();
    });

    it('should handle offline state', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });

      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });

      renderWithProviders(<MobileAttendance />);

      await waitFor(() => {
        expect(screen.getByText('Attendance')).toBeInTheDocument();
      });

      // Should show offline indicator
      // Note: This would depend on the actual implementation
    });
  });

  describe('Cross-component Integration', () => {
    it('should maintain state consistency across components', async () => {
      // This test would verify that state changes in one component
      // are reflected in other components that depend on the same data
      
      const mockAttendanceData = {
        hasCheckedIn: true,
        hasCheckedOut: false,
        attendanceRecord: {
          id: '1',
          check_in_time: '2024-01-01T09:00:00Z',
          status: 'present'
        }
      };

      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: mockAttendanceData
      });

      // Render dashboard
      const { rerender } = renderWithProviders(<AttendanceDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Check Out')).toBeInTheDocument();
      });

      // Rerender with mobile component
      rerender(
        <BrowserRouter>
          <MockAuthProvider>
            <MobileAttendance />
          </MockAuthProvider>
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(screen.getByText('Check Out')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      attendanceAPI.getTodayAttendance.mockRejectedValue(
        new Error('Network error')
      );

      renderWithProviders(<AttendanceDashboard />);

      // Should handle error without crashing
      await waitFor(() => {
        // Component should still render even with API error
        expect(screen.getByText('Attendance Dashboard')).toBeInTheDocument();
      });
    });

    it('should handle geolocation errors', async () => {
      mockGeolocation.getCurrentPosition.mockImplementation((success, error) => {
        error({
          code: 1,
          message: 'User denied geolocation'
        });
      });

      attendanceAPI.getTodayAttendance.mockResolvedValue({
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });

      renderWithProviders(<AttendanceDashboard />);

      await waitFor(() => {
        expect(screen.getByText('Check In')).toBeInTheDocument();
      });

      const checkInButton = screen.getByText('Check In');
      
      await act(async () => {
        fireEvent.click(checkInButton);
      });

      // Should handle geolocation error gracefully
      // (Implementation would show confirmation dialog)
    });
  });
});
