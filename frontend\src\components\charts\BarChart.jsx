/**
 * Bar Chart Component
 * Responsive bar chart using Recharts for categorical data visualization
 */

import React from 'react';
import {
  BarChart as RechartsBar<PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import { CHART_COLORS, CHART_THEMES } from './chartThemes';
import { formatNumber, generateTooltipContent } from './chartUtils';

const BarChart = ({
  data = [],
  bars = [], // Array of bar configurations: [{ dataKey, name, color }]
  xAxisKey = 'name',
  height = 300,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  orientation = 'vertical', // 'vertical' or 'horizontal'
  barSize = null, // Auto-size if null
  formatters = {}, // Custom formatters for tooltip values
  colorScheme = 'primary', // 'primary', 'status', 'priority'
  theme = 'default',
  className = '',
  ...props
}) => {
  // Default bar configuration if none provided
  const defaultBars = bars.length > 0 ? bars : [
    { dataKey: 'value', name: 'Value' }
  ];

  // Theme configuration
  const currentTheme = CHART_THEMES[theme] || CHART_THEMES.default;

  // Get colors based on scheme
  const getBarColor = (index, dataKey, item) => {
    if (colorScheme === 'status' && item.status) {
      return CHART_COLORS.status[item.status.toLowerCase()] || CHART_COLORS.primary[index];
    }
    if (colorScheme === 'priority' && item.priority) {
      return CHART_COLORS.priority[item.priority.toLowerCase()] || CHART_COLORS.primary[index];
    }
    return CHART_COLORS.primary[index % CHART_COLORS.primary.length];
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900 mb-2">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between space-x-4 mb-1">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-sm" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600">{entry.name}:</span>
            </div>
            <span className="text-sm font-medium text-gray-900">
              {formatters[entry.dataKey] 
                ? formatters[entry.dataKey](entry.value)
                : formatNumber(entry.value)
              }
            </span>
          </div>
        ))}
      </div>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 text-sm">No data available for chart</p>
      </div>
    );
  }

  return (
    <div className={className} {...props}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsBarChart
          data={data}
          layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={currentTheme.gridColor}
              opacity={0.5}
            />
          )}
          
          <XAxis 
            type={orientation === 'horizontal' ? 'number' : 'category'}
            dataKey={orientation === 'horizontal' ? undefined : xAxisKey}
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={orientation === 'horizontal' 
              ? (value) => formatNumber(value, 'compact')
              : undefined
            }
          />
          
          <YAxis 
            type={orientation === 'horizontal' ? 'category' : 'number'}
            dataKey={orientation === 'horizontal' ? xAxisKey : undefined}
            tick={{ fontSize: currentTheme.fontSize, fill: currentTheme.textColor }}
            tickLine={{ stroke: currentTheme.gridColor }}
            axisLine={{ stroke: currentTheme.gridColor }}
            tickFormatter={orientation === 'horizontal' 
              ? undefined
              : (value) => formatNumber(value, 'compact')
            }
          />
          
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          
          {showLegend && defaultBars.length > 1 && (
            <Legend 
              wrapperStyle={{ 
                fontSize: currentTheme.fontSize,
                color: currentTheme.textColor 
              }}
            />
          )}
          
          {defaultBars.map((bar, index) => (
            <Bar
              key={bar.dataKey}
              dataKey={bar.dataKey}
              name={bar.name}
              fill={bar.color || CHART_COLORS.primary[index % CHART_COLORS.primary.length]}
              radius={[2, 2, 0, 0]}
              maxBarSize={barSize}
            >
              {/* Individual cell colors for single bar charts */}
              {defaultBars.length === 1 && data.map((entry, cellIndex) => (
                <Cell 
                  key={`cell-${cellIndex}`} 
                  fill={getBarColor(cellIndex, bar.dataKey, entry)}
                />
              ))}
            </Bar>
          ))}
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChart;
