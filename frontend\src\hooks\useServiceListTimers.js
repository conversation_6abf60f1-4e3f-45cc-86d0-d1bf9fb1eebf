import { useState, useEffect, useRef } from 'react';
import apiService from '../services/api';
import socketService from '../services/socketService';

/**
 * Custom hook for managing real-time timer updates for multiple services in a list
 * Uses WebSocket for real-time updates with HTTP polling as fallback
 */
export const useServiceListTimers = (services) => {
  const [timerData, setTimerData] = useState({});
  const intervalRef = useRef(null);
  const subscribedServices = useRef(new Set());

  // Get services with running timers
  const getRunningTimerServices = () => {
    return services.filter(service => {
      const timeTrackingSummary = service.time_tracking_summary || {};
      return timeTrackingSummary.is_timer_running;
    });
  };

  // WebSocket timer update handler
  const handleTimerUpdate = (serviceId) => (data) => {
    console.log(`📡 ServiceList: Timer update for service ${serviceId}:`, data);
    setTimerData(prev => ({
      ...prev,
      [serviceId]: data
    }));
  };

  // Request deduplication cache
  const requestCache = useRef(new Map());
  const lastFetchTime = useRef(0);

  // Fetch timer status for multiple services (optimized with deduplication)
  const fetchTimerStatuses = async (serviceIds) => {
    if (!serviceIds || serviceIds.length === 0) return;

    // Skip if WebSocket is connected and providing recent updates
    if (socketService.isSocketConnected()) {
      const hasRecentWsData = serviceIds.some(serviceId => {
        const existingData = timerData[serviceId];
        const existingTimestamp = existingData?.timestamp ? new Date(existingData.timestamp) : null;
        const timeSinceLastWsUpdate = existingTimestamp ? (Date.now() - existingTimestamp) / 1000 : 999;
        return timeSinceLastWsUpdate < 10; // Skip if WebSocket data is less than 10 seconds old
      });

      if (hasRecentWsData) {
        console.log('📡 ServiceList: Skipping HTTP fetch - WebSocket data is recent');
        return;
      }
    }

    // Throttle requests - don't fetch more than once every 2 seconds
    const now = Date.now();
    if (now - lastFetchTime.current < 2000) {
      console.log('⏱️ ServiceList: Throttling HTTP requests');
      return;
    }
    lastFetchTime.current = now;

    // Deduplicate requests for same service IDs
    const cacheKey = serviceIds.sort().join(',');
    if (requestCache.current.has(cacheKey)) {
      console.log('🔄 ServiceList: Request already in progress, skipping duplicate');
      return requestCache.current.get(cacheKey);
    }

    try {
      // Create consolidated request promise
      const requestPromise = (async () => {
        // Use batch API call instead of individual requests
        const response = await apiService.post('/service-calls/timer-status/batch', {
          serviceIds: serviceIds
        });

        if (response.data?.success) {
          return response.data.data;
        } else {
          // Fallback to individual requests if batch API not available
          const promises = serviceIds.map(serviceId =>
            apiService.get(`/service-calls/${serviceId}/timer-status`)
              .then(response => ({
                serviceId,
                data: response.data?.success ? response.data.data : null
              }))
              .catch(error => ({
                serviceId,
                data: null,
                error: error.message
              }))
          );
          return await Promise.all(promises);
        }
      })();

      // Cache the request
      requestCache.current.set(cacheKey, requestPromise);

      const results = await requestPromise;

      // Clear cache after request completes
      requestCache.current.delete(cacheKey);

      // Update timer data state only if WebSocket data is not available
      const newTimerData = {};
      (Array.isArray(results) ? results : []).forEach(result => {
        if (result.data) {
          // Only update if we don't have recent WebSocket data
          const existingData = timerData[result.serviceId];
          const existingTimestamp = existingData?.timestamp ? new Date(existingData.timestamp) : null;
          const now = new Date();
          const timeSinceLastWsUpdate = existingTimestamp ? (now - existingTimestamp) / 1000 : 999;

          // Use HTTP data if no WebSocket data or WebSocket data is old
          if (!existingData || timeSinceLastWsUpdate > 10) {
            newTimerData[result.serviceId] = result.data;
          }
        }
      });

      if (Object.keys(newTimerData).length > 0) {
        setTimerData(prev => ({
          ...prev,
          ...newTimerData
        }));
      }
    } catch (error) {
      console.error('Error fetching timer statuses:', error);
      // Clear cache on error
      requestCache.current.delete(cacheKey);
    }
  };

  // Set up WebSocket subscriptions and smart polling
  useEffect(() => {
    // Ensure WebSocket is connected (but don't force new connection)
    if (!socketService.isSocketConnected()) {
      socketService.connect();
    }

    const runningServices = getRunningTimerServices();
    const runningServiceIds = runningServices.map(service => service.id);
    const allServiceIds = services.map(service => service.id);

    // Subscribe to WebSocket updates for all services
    allServiceIds.forEach(serviceId => {
      if (!subscribedServices.current.has(serviceId)) {
        console.log(`📡 ServiceList: Subscribing to timer updates for service ${serviceId}`);
        socketService.subscribeToTimer(serviceId, handleTimerUpdate(serviceId));
        subscribedServices.current.add(serviceId);
      }
    });

    // Clean up subscriptions for services no longer in the list
    subscribedServices.current.forEach(serviceId => {
      if (!allServiceIds.includes(serviceId)) {
        console.log(`📡 ServiceList: Unsubscribing from timer updates for service ${serviceId}`);
        socketService.unsubscribeFromTimer(serviceId);
        subscribedServices.current.delete(serviceId);
      }
    });

    if (runningServiceIds.length > 0) {
      // Initial fetch for running timers
      fetchTimerStatuses(runningServiceIds);

      // Set up smart polling interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Optimized polling: very infrequent when WebSocket is working
      const getPollingInterval = () => {
        return socketService.isSocketConnected() ? 30000 : 5000; // 30s with WebSocket, 5s without
      };

      intervalRef.current = setInterval(() => {
        const currentRunningServices = getRunningTimerServices();
        const currentRunningServiceIds = currentRunningServices.map(service => service.id);

        if (currentRunningServiceIds.length > 0) {
          fetchTimerStatuses(currentRunningServiceIds);
        }
      }, getPollingInterval());
    } else {
      // No running timers, clear interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    // Cleanup interval on unmount or when services change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Unsubscribe from all WebSocket updates
      subscribedServices.current.forEach(serviceId => {
        socketService.unsubscribeFromTimer(serviceId);
      });
      subscribedServices.current.clear();
    };
  }, [services]);

  // Get timer data for a specific service (prioritize WebSocket data)
  const getTimerDataForService = (serviceId) => {
    const wsData = timerData[serviceId];
    if (wsData) {
      return wsData;
    }

    // Fallback to service data if no WebSocket data
    const service = services.find(s => s.id === serviceId);
    return service?.time_tracking_summary || null;
  };

  // Get current accumulated time for a service
  const getCurrentAccumulatedTime = (serviceId) => {
    const data = getTimerDataForService(serviceId);
    return data?.timer?.current_accumulated_seconds || data?.current_accumulated_seconds || 0;
  };

  // Check if timer is running for a service
  const isTimerRunning = (serviceId) => {
    const data = getTimerDataForService(serviceId);
    return data?.timer?.is_running || data?.is_timer_running || false;
  };

  // Check if timer is paused for a service
  const isTimerPaused = (serviceId) => {
    const data = getTimerDataForService(serviceId);
    return data?.timer?.is_paused || data?.is_timer_paused || false;
  };

  // Get formatted time for a service
  const getFormattedTime = (serviceId) => {
    const data = getTimerDataForService(serviceId);
    return data?.timer?.current_accumulated_formatted || data?.current_accumulated_formatted || '00:00:00';
  };

  return {
    timerData,
    getTimerDataForService,
    getCurrentAccumulatedTime,
    isTimerRunning,
    isTimerPaused,
    getFormattedTime,
    hasRunningTimers: getRunningTimerServices().length > 0
  };
};

export default useServiceListTimers;
