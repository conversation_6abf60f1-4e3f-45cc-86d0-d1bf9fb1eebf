import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import app from '../src/server.js';
import models from '../src/models/index.js';
import { createTestUser, createTestTenant, getAuthToken } from './helpers/testHelpers.js';

describe('Attendance Management System', () => {
  let testTenant, testUser, testEmployee, authToken;

  beforeEach(async () => {
    // Create test tenant and user
    testTenant = await createTestTenant();
    testUser = await createTestUser(testTenant.id, 'employee');
    
    // Create test employee
    testEmployee = await models.Executive.create({
      tenant_id: testTenant.id,
      user_id: testUser.id,
      first_name: 'Test',
      last_name: 'Employee',
      employee_code: 'EMP001',
      email: '<EMAIL>',
      phone: '1234567890',
      department: 'IT',
      designation: 'Developer',
      shift_start_time: '09:00:00',
      shift_end_time: '18:00:00',
      attendance_tracking_enabled: true,
      is_active: true
    });

    authToken = await getAuthToken(testUser);
  });

  afterEach(async () => {
    // Clean up test data
    await models.AttendanceRecord.destroy({ where: { tenant_id: testTenant.id } });
    await models.Executive.destroy({ where: { tenant_id: testTenant.id } });
    await models.User.destroy({ where: { tenant_id: testTenant.id } });
    await models.Tenant.destroy({ where: { id: testTenant.id } });
  });

  describe('Attendance Check-in/Check-out', () => {
    it('should allow employee to check in', async () => {
      const checkInData = {
        location: {
          latitude: 12.9716,
          longitude: 77.5946,
          accuracy: 10
        },
        notes: 'Regular check-in'
      };

      const response = await request(app)
        .post('/api/v1/attendance/check-in')
        .set('Authorization', `Bearer ${authToken}`)
        .send(checkInData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.attendanceRecord).toBeDefined();
      expect(response.body.data.attendanceRecord.check_in_time).toBeDefined();
      expect(response.body.data.attendanceRecord.status).toBe('present');
    });

    it('should prevent duplicate check-in on same day', async () => {
      // First check-in
      await request(app)
        .post('/api/v1/attendance/check-in')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 }
        })
        .expect(201);

      // Second check-in should fail
      const response = await request(app)
        .post('/api/v1/attendance/check-in')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already checked in');
    });

    it('should allow employee to check out after check-in', async () => {
      // First check-in
      await request(app)
        .post('/api/v1/attendance/check-in')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 }
        })
        .expect(201);

      // Then check-out
      const response = await request(app)
        .post('/api/v1/attendance/check-out')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 }
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.attendanceRecord.check_out_time).toBeDefined();
      expect(response.body.data.attendanceRecord.total_hours).toBeGreaterThan(0);
    });

    it('should prevent check-out without check-in', async () => {
      const response = await request(app)
        .post('/api/v1/attendance/check-out')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 }
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('not checked in');
    });

    it('should mark late arrival correctly', async () => {
      // Create attendance policy
      await models.AttendancePolicy.create({
        tenant_id: testTenant.id,
        name: 'Standard Policy',
        shift_start_time: '09:00:00',
        shift_end_time: '18:00:00',
        grace_period_minutes: 15,
        is_default: true,
        is_active: true
      });

      // Mock late check-in (after 9:15 AM)
      const lateCheckInTime = new Date();
      lateCheckInTime.setHours(9, 30, 0, 0); // 9:30 AM

      const response = await request(app)
        .post('/api/v1/attendance/check-in')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          location: { latitude: 12.9716, longitude: 77.5946, accuracy: 10 },
          check_in_time: lateCheckInTime.toISOString()
        })
        .expect(201);

      expect(response.body.data.attendanceRecord.status).toBe('late');
      expect(response.body.data.attendanceRecord.late_minutes).toBeGreaterThan(0);
    });
  });

  describe('Attendance Records', () => {
    it('should fetch today\'s attendance', async () => {
      // Create attendance record
      await models.AttendanceRecord.create({
        tenant_id: testTenant.id,
        employee_id: testEmployee.id,
        date: new Date().toISOString().split('T')[0],
        check_in_time: new Date(),
        status: 'present'
      });

      const response = await request(app)
        .get('/api/v1/attendance/today')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.attendanceRecord).toBeDefined();
      expect(response.body.data.hasCheckedIn).toBe(true);
    });

    it('should fetch attendance records with pagination', async () => {
      // Create multiple attendance records
      const dates = ['2024-01-01', '2024-01-02', '2024-01-03'];
      for (const date of dates) {
        await models.AttendanceRecord.create({
          tenant_id: testTenant.id,
          employee_id: testEmployee.id,
          date: date,
          check_in_time: new Date(`${date}T09:00:00`),
          check_out_time: new Date(`${date}T18:00:00`),
          status: 'present',
          total_hours: 8.0
        });
      }

      const response = await request(app)
        .get('/api/v1/attendance/records?page=1&limit=2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.attendanceRecords).toHaveLength(2);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.total).toBe(3);
    });
  });

  describe('Leave Management', () => {
    let leaveType;

    beforeEach(async () => {
      leaveType = await models.LeaveType.create({
        tenant_id: testTenant.id,
        name: 'Annual Leave',
        code: 'AL',
        annual_quota: 20,
        is_paid: true,
        requires_approval: true,
        advance_notice_days: 1,
        is_active: true
      });

      // Create leave balance
      await models.LeaveBalance.create({
        tenant_id: testTenant.id,
        employee_id: testEmployee.id,
        leave_type_id: leaveType.id,
        year: new Date().getFullYear(),
        allocated_days: 20,
        used_days: 0,
        remaining_days: 20
      });
    });

    it('should allow employee to submit leave request', async () => {
      const leaveData = {
        leave_type_id: leaveType.id,
        start_date: '2024-12-25',
        end_date: '2024-12-27',
        total_days: 3,
        reason: 'Christmas vacation',
        priority: 'normal'
      };

      const response = await request(app)
        .post('/api/v1/leaves/request')
        .set('Authorization', `Bearer ${authToken}`)
        .send(leaveData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.leaveRequest).toBeDefined();
      expect(response.body.data.leaveRequest.status).toBe('pending');
    });

    it('should prevent leave request with insufficient balance', async () => {
      const leaveData = {
        leave_type_id: leaveType.id,
        start_date: '2024-12-01',
        end_date: '2024-12-31',
        total_days: 25, // More than available balance
        reason: 'Extended vacation',
        priority: 'normal'
      };

      const response = await request(app)
        .post('/api/v1/leaves/request')
        .set('Authorization', `Bearer ${authToken}`)
        .send(leaveData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('insufficient leave balance');
    });

    it('should fetch leave balance', async () => {
      const response = await request(app)
        .get(`/api/v1/leaves/balance?year=${new Date().getFullYear()}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.leaveBalances).toHaveLength(1);
      expect(response.body.data.leaveBalances[0].remaining_days).toBe(20);
    });
  });

  describe('Payroll Integration', () => {
    it('should calculate salary based on attendance', async () => {
      // Create salary structure
      const salaryStructure = await models.SalaryStructure.create({
        tenant_id: testTenant.id,
        employee_id: testEmployee.id,
        basic_salary: 50000,
        hra: 15000,
        transport_allowance: 2000,
        medical_allowance: 1500,
        special_allowance: 5000,
        pf_employee_percentage: 12,
        esi_employee_percentage: 0.75,
        professional_tax: 200,
        effective_from: new Date(),
        is_active: true
      });

      // Create attendance records for the month
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      
      for (let day = 1; day <= 20; day++) {
        await models.AttendanceRecord.create({
          tenant_id: testTenant.id,
          employee_id: testEmployee.id,
          date: `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
          check_in_time: new Date(`${currentYear}-${currentMonth}-${day}T09:00:00`),
          check_out_time: new Date(`${currentYear}-${currentMonth}-${day}T18:00:00`),
          status: 'present',
          total_hours: 8.0
        });
      }

      const response = await request(app)
        .post('/api/v1/payroll/process')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          month: currentMonth,
          year: currentYear,
          employee_ids: [testEmployee.id]
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.processed).toHaveLength(1);
      
      const payrollRecord = response.body.data.processed[0];
      expect(payrollRecord.gross_salary).toBeGreaterThan(0);
      expect(payrollRecord.net_salary).toBeGreaterThan(0);
      expect(payrollRecord.present_days).toBe(20);
    });
  });

  describe('Role-based Access Control', () => {
    let managerUser, managerToken;

    beforeEach(async () => {
      managerUser = await createTestUser(testTenant.id, 'manager');
      managerToken = await getAuthToken(managerUser);
    });

    it('should allow manager to view team attendance', async () => {
      const response = await request(app)
        .get('/api/v1/attendance/team')
        .set('Authorization', `Bearer ${managerToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should prevent employee from accessing admin endpoints', async () => {
      const response = await request(app)
        .get('/api/v1/attendance/team')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('permission');
    });

    it('should allow manager to approve leave requests', async () => {
      // Create a leave request
      const leaveRequest = await models.LeaveRequest.create({
        tenant_id: testTenant.id,
        employee_id: testEmployee.id,
        leave_type_id: (await models.LeaveType.findOne({ where: { tenant_id: testTenant.id } })).id,
        start_date: '2024-12-25',
        end_date: '2024-12-27',
        total_days: 3,
        reason: 'Test leave',
        status: 'pending',
        request_number: 'LR-001'
      });

      const response = await request(app)
        .put(`/api/v1/leaves/requests/${leaveRequest.id}/approve`)
        .set('Authorization', `Bearer ${managerToken}`)
        .send({
          manager_comments: 'Approved for Christmas vacation'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.leaveRequest.status).toBe('approved');
    });
  });

  describe('Analytics and Reporting', () => {
    beforeEach(async () => {
      // Create sample attendance data
      const dates = ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'];
      for (const date of dates) {
        await models.AttendanceRecord.create({
          tenant_id: testTenant.id,
          employee_id: testEmployee.id,
          date: date,
          check_in_time: new Date(`${date}T09:00:00`),
          check_out_time: new Date(`${date}T18:00:00`),
          status: 'present',
          total_hours: 8.0
        });
      }
    });

    it('should generate attendance analytics', async () => {
      const response = await request(app)
        .get('/api/v1/reports/attendance-analytics?analytics_type=overview&start_date=2024-01-01&end_date=2024-01-31')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.total_records).toBeGreaterThan(0);
      expect(response.body.data.attendance_percentage).toBeGreaterThan(0);
    });

    it('should generate leave analytics', async () => {
      const response = await request(app)
        .get('/api/v1/reports/leave-analytics?start_date=2024-01-01&end_date=2024-12-31')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
    });
  });

  describe('Notifications', () => {
    it('should send attendance reminder notifications', async () => {
      // This would typically test the notification service
      // For now, we'll test that the endpoint exists and responds correctly
      const response = await request(app)
        .post('/api/v1/attendance/send-reminders')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          employee_ids: [testEmployee.id]
        })
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });
});
