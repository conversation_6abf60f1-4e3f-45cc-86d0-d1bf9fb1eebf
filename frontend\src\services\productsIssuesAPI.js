import { apiService } from './api.js';

/**
 * Products/Issues API Service
 * Handles all API calls related to products and issues management
 */

const ENDPOINTS = {
  BASE: '/master-data/products-issues',
  SEARCH: '/master-data/products-issues/search',
  CATEGORIES: '/master-data/products-issues/categories',
};

/**
 * Get all products/issues with pagination and filtering
 */
export const getProductsIssues = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();

    // Add pagination
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);

    // Add search and filters
    if (params.search) queryParams.append('search', params.search);
    if (params.category) queryParams.append('category', params.category);
    if (params.isActive !== undefined) queryParams.append('isActive', params.isActive);

    // Add sorting
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `${ENDPOINTS.BASE}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiService.get(url);

    return response.data;
  } catch (error) {
    console.error('Error fetching products/issues:', error);
    throw error;
  }
};

/**
 * Get products/issues by ID
 */
export const getProductsIssuesById = async (id) => {
  try {
    const response = await apiService.get(`${ENDPOINTS.BASE}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching products/issues by ID:', error);
    throw error;
  }
};

/**
 * Create new products/issues
 */
export const createProductsIssues = async (data) => {
  try {
    const response = await apiService.post(ENDPOINTS.BASE, data);
    return response.data;
  } catch (error) {
    console.error('Error creating products/issues:', error);
    throw error;
  }
};

/**
 * Update products/issues
 */
export const updateProductsIssues = async (id, data) => {
  try {
    const response = await apiService.put(`${ENDPOINTS.BASE}/${id}`, data);
    return response.data;
  } catch (error) {
    console.error('Error updating products/issues:', error);
    throw error;
  }
};

/**
 * Delete products/issues
 */
export const deleteProductsIssues = async (id) => {
  try {
    const response = await apiService.delete(`${ENDPOINTS.BASE}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting products/issues:', error);
    throw error;
  }
};

/**
 * Search products/issues for dropdown/autocomplete
 */
export const searchProductsIssues = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();

    if (params.q) queryParams.append('q', params.q);
    if (params.category) queryParams.append('category', params.category);
    if (params.limit) queryParams.append('limit', params.limit);

    const url = `${ENDPOINTS.SEARCH}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiService.get(url);

    return response.data;
  } catch (error) {
    console.error('Error searching products/issues:', error);
    throw error;
  }
};

/**
 * Get all categories with counts
 */
export const getCategories = async () => {
  try {
    const response = await apiService.get(ENDPOINTS.CATEGORIES);
    return response.data;
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
};

/**
 * Bulk update status of products/issues
 */
export const bulkUpdateStatus = async (ids, isActive) => {
  try {
    const response = await apiService.patch(`${ENDPOINTS.BASE}/bulk-status`, {
      ids,
      is_active: isActive,
    });
    return response.data;
  } catch (error) {
    console.error('Error bulk updating status:', error);
    throw error;
  }
};

/**
 * Get products/issues for dropdown (active only)
 */
export const getProductsIssuesForDropdown = async (category = null) => {
  try {
    const params = {
      limit: 1000,
      isActive: true,
    };

    if (category) {
      params.category = category;
    }

    const response = await searchProductsIssues(params);
    return response.data || [];
  } catch (error) {
    console.error('Error fetching products/issues for dropdown:', error);
    return [];
  }
};

/**
 * Get products/issues by category
 */
export const getProductsIssuesByCategory = async (category) => {
  try {
    const response = await getProductsIssues({
      category,
      isActive: true,
      limit: 1000,
      sortBy: 'sort_order',
      sortOrder: 'asc',
    });

    return response.data?.productsIssues || [];
  } catch (error) {
    console.error('Error fetching products/issues by category:', error);
    return [];
  }
};

/**
 * Validate products/issues name uniqueness
 */
export const validateName = async (name, excludeId = null) => {
  try {
    const response = await searchProductsIssues({ q: name, limit: 1 });
    const existing = response.data?.find(item =>
      item.name.toLowerCase() === name.toLowerCase() &&
      (!excludeId || item.id !== excludeId)
    );

    return !existing; // Returns true if name is unique
  } catch (error) {
    console.error('Error validating name:', error);
    return true; // Assume valid if validation fails
  }
};

// Export all functions as default object
const productsIssuesAPI = {
  getProductsIssues,
  getProductsIssuesById,
  createProductsIssues,
  updateProductsIssues,
  deleteProductsIssues,
  searchProductsIssues,
  getCategories,
  bulkUpdateStatus,
  getProductsIssuesForDropdown,
  getProductsIssuesByCategory,
  validateName,
};

export default productsIssuesAPI;
