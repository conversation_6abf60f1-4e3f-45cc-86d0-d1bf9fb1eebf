import { DataTypes } from 'sequelize';

export default (sequelize) => {
  const ProductsIssues = sequelize.define('ProductsIssues', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(200),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: {
          msg: 'Name is required',
        },
        len: {
          args: [2, 200],
          msg: 'Name must be between 2 and 200 characters',
        },
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      validate: {
        len: {
          args: [0, 1000],
          msg: 'Description must be less than 1000 characters',
        },
      },
    },
    category: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: 'Category must be less than 100 characters',
        },
      },
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: {
          args: [0],
          msg: 'Sort order must be a non-negative integer',
        },
      },
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Default items cannot be deleted',
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    updated_by: {
      type: DataTypes.UUID,
      allowNull: true,
    },
  }, {
    tableName: 'products_issues',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['sort_order'],
      },
      {
        fields: ['name'],
      },
      {
        unique: true,
        fields: ['name', 'tenant_id'],
        name: 'products_issues_name_tenant_unique',
      },
    ],
    hooks: {
      beforeValidate: (productsIssues) => {
        // Trim whitespace from string fields
        if (productsIssues.name) {
          productsIssues.name = productsIssues.name.trim();
        }
        if (productsIssues.category) {
          productsIssues.category = productsIssues.category.trim();
        }
        if (productsIssues.description) {
          productsIssues.description = productsIssues.description.trim();
        }
      },
    },
  });

  // Define associations
  ProductsIssues.associate = (models) => {
    // Belongs to tenant
    ProductsIssues.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    // Belongs to user (created by)
    ProductsIssues.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    // Belongs to user (updated by)
    ProductsIssues.belongsTo(models.User, {
      foreignKey: 'updated_by',
      as: 'updater',
    });
  };

  // Instance methods
  ProductsIssues.prototype.toJSON = function() {
    const values = { ...this.get() };
    
    // Add computed fields
    values.displayName = values.category ? `${values.category} - ${values.name}` : values.name;
    values.statusText = values.is_active ? 'Active' : 'Inactive';
    values.canDelete = !values.is_default;
    
    return values;
  };

  // Class methods
  ProductsIssues.getActiveForDropdown = async function(options = {}) {
    const { tenant_id, category, limit = 100 } = options;
    
    const whereClause = {
      is_active: true,
    };
    
    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }
    
    if (category) {
      whereClause.category = category;
    }
    
    return await this.findAll({
      where: whereClause,
      order: [['sort_order', 'ASC'], ['name', 'ASC']],
      limit,
      attributes: ['id', 'name', 'category', 'description'],
    });
  };

  ProductsIssues.getCategories = async function(options = {}) {
    const { tenant_id } = options;
    
    const whereClause = {
      is_active: true,
    };
    
    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }
    
    const categories = await this.findAll({
      where: whereClause,
      attributes: [
        'category',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      group: ['category'],
      order: [['category', 'ASC']],
      raw: true,
    });
    
    return categories.filter(cat => cat.category !== null);
  };

  ProductsIssues.searchByName = async function(query, options = {}) {
    const { tenant_id, category, limit = 50 } = options;
    
    const whereClause = {
      is_active: true,
      name: {
        [sequelize.Sequelize.Op.iLike]: `%${query}%`,
      },
    };
    
    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }
    
    if (category) {
      whereClause.category = category;
    }
    
    return await this.findAll({
      where: whereClause,
      order: [['sort_order', 'ASC'], ['name', 'ASC']],
      limit,
      attributes: ['id', 'name', 'category', 'description'],
    });
  };

  return ProductsIssues;
};
