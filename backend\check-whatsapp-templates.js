#!/usr/bin/env node

/**
 * Check WhatsApp Business Templates
 * 
 * This script checks what message templates are available for the WhatsApp Business account
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import axios from 'axios';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env') });

console.log('📱 WhatsApp Business Account Configuration:');
console.log(`   Phone Number ID: ${process.env.WHATSAPP_PHONE_NUMBER_ID}`);
console.log(`   Account ID: ${process.env.WHATSAPP_ACCOUNT_ID}`);
console.log(`   Business ID: ${process.env.WHATSAPP_BUSINESS_ID}`);
console.log(`   API Version: ${process.env.WHATSAPP_API_VERSION}`);
console.log(`   Has Access Token: ${!!process.env.WHATSAPP_ACCESS_TOKEN}`);

async function checkWhatsAppTemplates() {
  console.log('\n🔍 Checking WhatsApp Message Templates...\n');

  try {
    // Get message templates for the WhatsApp Business Account
    const templatesUrl = `https://graph.facebook.com/${process.env.WHATSAPP_API_VERSION}/${process.env.WHATSAPP_BUSINESS_ID}/message_templates`;
    
    console.log('📋 Fetching templates from:', templatesUrl);
    
    const response = await axios.get(templatesUrl, {
      headers: {
        'Authorization': `Bearer ${process.env.WHATSAPP_ACCESS_TOKEN}`,
        'Content-Type': 'application/json'
      },
      params: {
        limit: 100 // Get up to 100 templates
      }
    });

    const templates = response.data.data || [];
    
    console.log(`\n✅ Found ${templates.length} message templates:\n`);

    if (templates.length === 0) {
      console.log('❌ No message templates found!');
      console.log('\n📝 You need to create message templates in your WhatsApp Business Manager:');
      console.log('   1. Go to https://business.facebook.com/');
      console.log('   2. Navigate to WhatsApp Manager');
      console.log('   3. Go to Message Templates');
      console.log('   4. Create templates for service notifications');
      console.log('\n💡 Common template categories needed:');
      console.log('   - SERVICE_CREATED (for new service requests)');
      console.log('   - SERVICE_COMPLETED (for completed services)');
      console.log('   - SERVICE_STATUS_UPDATE (for status changes)');
      return;
    }

    // Display all templates
    templates.forEach((template, index) => {
      console.log(`📋 Template ${index + 1}:`);
      console.log(`   Name: ${template.name}`);
      console.log(`   Status: ${template.status}`);
      console.log(`   Category: ${template.category}`);
      console.log(`   Language: ${template.language}`);
      
      if (template.components && template.components.length > 0) {
        console.log(`   Components:`);
        template.components.forEach((component, compIndex) => {
          console.log(`     ${compIndex + 1}. Type: ${component.type}`);
          if (component.text) {
            console.log(`        Text: ${component.text}`);
          }
          if (component.parameters) {
            console.log(`        Parameters: ${component.parameters.length}`);
          }
        });
      }
      console.log('');
    });

    // Check for specific service-related templates
    console.log('\n🔍 Analyzing templates for service notifications...\n');
    
    const serviceTemplates = templates.filter(template => 
      template.name.toLowerCase().includes('service') ||
      template.category === 'TRANSACTIONAL' ||
      template.category === 'UTILITY'
    );

    if (serviceTemplates.length > 0) {
      console.log(`✅ Found ${serviceTemplates.length} service-related templates:`);
      serviceTemplates.forEach(template => {
        console.log(`   - ${template.name} (${template.status})`);
      });
    } else {
      console.log('❌ No service-related templates found.');
      console.log('\n📝 Recommended templates to create:');
      console.log('   1. service_created - For new service requests');
      console.log('   2. service_completed - For completed services');
      console.log('   3. service_status_update - For status changes');
    }

    // Test sending a template message if available
    const approvedTemplates = templates.filter(template => template.status === 'APPROVED');
    
    if (approvedTemplates.length > 0) {
      console.log(`\n🧪 Testing with first approved template: ${approvedTemplates[0].name}`);
      
      // This is just a demonstration - we won't actually send
      console.log('📱 Template message structure would be:');
      console.log(`   Template Name: ${approvedTemplates[0].name}`);
      console.log(`   Language: ${approvedTemplates[0].language}`);
      console.log(`   Category: ${approvedTemplates[0].category}`);
    }

  } catch (error) {
    console.error('\n❌ Error checking WhatsApp templates:', error.message);
    
    if (error.response) {
      console.error('📋 Error details:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
      
      if (error.response.status === 401) {
        console.log('\n🔑 Authentication Error:');
        console.log('   - Check if your WHATSAPP_ACCESS_TOKEN is valid');
        console.log('   - Verify the token has the required permissions');
        console.log('   - Make sure the token is not expired');
      }
      
      if (error.response.status === 403) {
        console.log('\n🚫 Permission Error:');
        console.log('   - Check if your app has WhatsApp Business Management permissions');
        console.log('   - Verify the Business ID is correct');
        console.log('   - Ensure your app is approved for WhatsApp Business API');
      }
    }
  }
}

// Run the template check
checkWhatsAppTemplates().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
