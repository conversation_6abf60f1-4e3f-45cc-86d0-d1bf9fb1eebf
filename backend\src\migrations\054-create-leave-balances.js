import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('leave_balances', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Employee for whom balance is maintained',
    },
    leave_type_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
      comment: 'Type of leave',
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Calendar year for this balance',
    },
    allocated_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Total days allocated for the year',
    },
    used_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days already used/taken',
    },
    pending_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days in pending leave requests',
    },
    carry_forward_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days carried forward from previous year',
    },
    carry_forward_used: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Carry forward days already used',
    },
    carry_forward_expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When carry forward days expire',
    },
    encashed_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days encashed for money',
    },
    lapsed_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days that lapsed without use',
    },
    adjustment_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Manual adjustments (can be positive or negative)',
    },
    adjustment_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for manual adjustment',
    },
    adjusted_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who made the adjustment',
    },
    adjusted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When adjustment was made',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this balance record is active',
    },
    last_calculated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When balance was last calculated',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('leave_balances', ['tenant_id']);
  await queryInterface.addIndex('leave_balances', ['employee_id']);
  await queryInterface.addIndex('leave_balances', ['leave_type_id']);
  await queryInterface.addIndex('leave_balances', ['year']);
  await queryInterface.addIndex('leave_balances', ['tenant_id', 'employee_id', 'leave_type_id', 'year'], {
    unique: true,
    name: 'unique_employee_leave_year',
  });
  await queryInterface.addIndex('leave_balances', ['is_active']);
  await queryInterface.addIndex('leave_balances', ['last_calculated_at']);

  console.log('✅ Created leave_balances table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('leave_balances');
  console.log('✅ Dropped leave_balances table');
};
