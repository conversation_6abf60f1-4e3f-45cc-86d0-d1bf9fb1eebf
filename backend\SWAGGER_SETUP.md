# TallyCRM Backend API - Swagger/OpenAPI Documentation Setup

## Overview

This document describes the comprehensive Swagger/OpenAPI 3.0 documentation setup for the TallyCRM Backend API. The implementation provides interactive API documentation with testing capabilities for all endpoints.

## Features

✅ **Complete API Coverage**
- Authentication endpoints (login, register, refresh, profile)
- Customer management (CRUD operations, search, statistics)
- Service call management (CRUD, timer functionality, status updates)
- Master data endpoints (call statuses, products/issues, etc.)
- Dashboard analytics (overview, charts, trends)
- Notification settings (configuration, testing)
- File upload operations (TDL & Addons)
- Batch operations (performance optimization)

✅ **Interactive Testing**
- Built-in request/response testing
- JWT authentication support
- Real-time API exploration
- Example requests and responses

✅ **Comprehensive Documentation**
- Detailed endpoint descriptions
- Request/response schemas
- Validation rules and constraints
- Error response examples
- Authentication requirements

## Access Points

### Development Environment
- **Swagger UI**: `http://**************:5373/api-docs` ✅ **ACTIVE**
- **OpenAPI JSON**: `http://**************:5373/api-docs.json` ✅ **ACTIVE**

### Production Environment
- **Swagger UI**: `https://your-domain.com/api-docs`
- **OpenAPI JSON**: `https://your-domain.com/api-docs.json`

## ✅ **SETUP COMPLETE - READY TO USE**

The Swagger/OpenAPI documentation is now **fully operational** and accessible at the development URL above. You can immediately start testing all API endpoints through the interactive Swagger UI interface.

## Configuration

### Environment Variables
```bash
# Enable/disable Swagger documentation
ENABLE_SWAGGER=true

# Swagger UI path
SWAGGER_PATH=/api-docs

# API base configuration
API_PREFIX=/api/v1
APP_URL=http://localhost:3001
```

### Files Structure
```
backend/
├── src/
│   ├── config/
│   │   └── swagger.js          # OpenAPI 3.0 specification
│   ├── utils/
│   │   └── swagger.js          # Swagger setup utilities
│   ├── routes/
│   │   ├── auth.js            # Authentication endpoints
│   │   ├── customers.js       # Customer management
│   │   ├── serviceCalls.js    # Service call operations
│   │   ├── dashboard.js       # Dashboard analytics
│   │   ├── notifications.js   # Notification settings
│   │   └── batchRoutes.js     # Batch operations
│   └── server.js              # Main server with Swagger integration
```

## API Documentation Highlights

### Authentication
- **POST** `/auth/login` - User authentication with JWT tokens
- **POST** `/auth/refresh` - Refresh access tokens
- **POST** `/auth/register` - User registration
- **GET** `/auth/me` - Get current user profile

### Customer Management
- **GET** `/customers` - List customers with pagination and filters
- **POST** `/customers` - Create new customer with file uploads
- **GET** `/customers/{id}` - Get customer details
- **PUT** `/customers/{id}` - Update customer information
- **GET** `/customers/serial/{serialNumber}` - Find by serial number
- **GET** `/customers/stats` - Customer statistics

### Service Call Management
- **GET** `/service-calls` - List service calls with comprehensive filters
- **POST** `/service-calls` - Create new service call
- **GET** `/service-calls/{id}` - Get service call details
- **PUT** `/service-calls/{id}` - Update service call
- **GET** `/service-calls/stats` - Service call statistics
- **GET** `/service-calls/{id}/timer-status` - Real-time timer status
- **GET** `/service-calls/{id}/timer-history` - Timer history tracking

### Dashboard Analytics
- **GET** `/dashboard/overview` - Comprehensive dashboard statistics
- **GET** `/dashboard/charts` - Trend data for charts

### Notification System
- **GET** `/notifications/settings` - Get notification preferences
- **PUT** `/notifications/settings` - Update notification settings
- **POST** `/notifications/test` - Send test notifications

### Batch Operations
- **POST** `/service-calls/timer-status/batch` - Bulk timer status
- **GET** `/service-calls/batch-data` - Optimized data retrieval

## Key Features

### JWT Authentication
All endpoints (except authentication) require JWT Bearer tokens:
```
Authorization: Bearer <your-jwt-token>
```

### Request/Response Schemas
- Standardized response format with `success`, `message`, `data` fields
- Comprehensive error handling with detailed validation messages
- Pagination support with `page`, `limit`, `total`, `totalPages`

### Timer Functionality
Advanced timer tracking for service calls:
- Real-time status updates (`running`, `paused`, `stopped`)
- Accumulated time preservation across status changes
- Detailed session history with timestamps
- WebSocket integration for live updates

### File Upload Support
- TDL & Addons file uploads for customers
- Proper file validation and storage
- Secure file serving with access controls

## 🚀 **Quick Start Testing Guide**

### Step 1: Access Swagger UI
1. Open your browser and navigate to: `http://**************:5373/api-docs`
2. You'll see the interactive TallyCRM API documentation

### Step 2: Authenticate
1. Click on the **"Authentication"** section
2. Expand the **POST /auth/login** endpoint
3. Click **"Try it out"**
4. Use these test credentials:
   ```json
   {
     "email": "<EMAIL>",
     "password": "password123"
   }
   ```
5. Click **"Execute"**
6. Copy the `accessToken` from the response

### Step 3: Authorize Swagger UI
1. Click the **"Authorize"** button at the top of the page
2. Enter: `Bearer <your-access-token>`
3. Click **"Authorize"**
4. Now you can test all protected endpoints!

### Step 4: Test Key Endpoints
- **GET /customers** - List all customers
- **GET /service-calls** - List service calls with timer functionality
- **GET /dashboard/overview** - Dashboard statistics
- **GET /service-calls/{id}/timer-status** - Real-time timer status

## Usage Examples

### Authentication (via cURL)
```bash
# Login
curl -X POST http://**************:5373/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Use returned token for subsequent requests
curl -X GET http://**************:5373/api/v1/customers \
  -H "Authorization: Bearer <your-jwt-token>"
```

### Service Call Timer (via cURL)
```bash
# Get timer status
curl -X GET http://**************:5373/api/v1/service-calls/{id}/timer-status \
  -H "Authorization: Bearer <your-jwt-token>"

# Get timer history
curl -X GET http://**************:5373/api/v1/service-calls/{id}/timer-history \
  -H "Authorization: Bearer <your-jwt-token>"
```

## Development Notes

### Adding New Endpoints
1. Add Swagger documentation comments to route files
2. Define request/response schemas in `swagger.js`
3. Include proper authentication and validation
4. Test using Swagger UI interface

### Schema Management
- Common schemas defined in `src/config/swagger.js`
- Reusable components for consistent documentation
- Error response templates for standardized error handling

### Performance Optimization
- Batch endpoints for reducing HTTP requests
- Efficient pagination and filtering
- Optimized database queries with proper indexing

## Security Considerations

- JWT token validation on all protected endpoints
- Role-based access control (RBAC) integration
- Input validation and sanitization
- Rate limiting and request throttling
- Secure file upload handling

## Troubleshooting

### Common Issues
1. **Swagger UI not loading**: Check `ENABLE_SWAGGER=true` in environment
2. **Authentication errors**: Ensure JWT token is properly formatted
3. **Schema validation errors**: Verify request body matches documented schema
4. **CORS issues**: Configure proper CORS origins in environment

### Debug Mode
Set `NODE_ENV=development` for detailed error messages and stack traces.

## Future Enhancements

- [ ] API versioning support
- [ ] Rate limiting documentation
- [ ] WebSocket endpoint documentation
- [ ] Advanced filtering examples
- [ ] Performance metrics integration
- [ ] API testing automation

---

For the latest API documentation and testing, visit the Swagger UI at `/api-docs` when the server is running.
