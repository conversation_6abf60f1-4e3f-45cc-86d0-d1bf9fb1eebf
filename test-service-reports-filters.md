# Service Reports Filter Testing Guide

## Prerequisites
- Frontend server running on http://localhost:3004
- Backend server running on http://localhost:8080
- Valid authentication token (logged in user)

## Test Cases

### Test Case 1: Status Filter
**Objective**: Verify Status dropdown is properly populated and filters work

**Steps**:
1. Navigate to `/reports/service-reports`
2. Click on "Services" tab
3. Locate the Status dropdown in the header bar (next to checkmark icon)
4. Click the Status dropdown
5. Verify all available statuses are listed (e.g., Open, In Progress, Completed, Cancelled)
6. Select a specific status (e.g., "Completed")
7. Verify the data updates to show only services with that status
8. Select "All Status" to reset the filter
9. Verify all services are shown again
10. Repeat for "Service Calls" tab

**Expected Results**:
- Status dropdown shows all available statuses from backend
- Selecting a status filters the data immediately
- "All Status" option resets the filter
- Filter works on both tabs

### Test Case 2: Priority Filter  
**Objective**: Verify Priority dropdown filters work correctly

**Steps**:
1. Navigate to `/reports/service-reports`
2. Click on "Services" tab
3. Locate the Priority dropdown in the header bar (next to warning triangle icon)
4. Click the Priority dropdown
5. Verify options: All Priority, Low, Medium, High, Critical
6. Select "High" priority
7. Verify only high-priority services are displayed
8. Select "Critical" priority
9. Verify only critical-priority services are displayed
10. Select "All Priority" to reset
11. Repeat for "Service Calls" tab

**Expected Results**:
- Priority dropdown shows all priority levels
- Each priority selection filters data correctly
- "All Priority" option resets the filter
- Filter works on both tabs

### Test Case 3: Service Type Filter
**Objective**: Verify Service Type dropdown filters work correctly

**Steps**:
1. Navigate to `/reports/service-reports`
2. Click on "Services" tab
3. Locate the Service Type dropdown in the header bar (next to tools icon)
4. Click the Service Type dropdown
5. Verify options: All Types, Free Call, AMC Call, Per Call
6. Select "AMC Call"
7. Verify only AMC services are displayed
8. Select "Free Call"
9. Verify only free call services are displayed
10. Select "All Types" to reset
11. Repeat for "Service Calls" tab

**Expected Results**:
- Service Type dropdown shows all billing types
- Each type selection filters data correctly
- "All Types" option resets the filter
- Filter works on both tabs

### Test Case 4: Combined Filters
**Objective**: Verify multiple filters work together

**Steps**:
1. Navigate to `/reports/service-reports`
2. Click on "Services" tab
3. Select Status = "In Progress"
4. Select Priority = "High"
5. Verify data shows only high-priority, in-progress services
6. Add Service Type = "AMC Call"
7. Verify data shows only high-priority, in-progress AMC services
8. Reset one filter at a time and verify data updates
9. Reset all filters and verify all data is shown

**Expected Results**:
- Multiple filters work together (AND logic)
- Data updates immediately when filters change
- Resetting individual filters works correctly
- Resetting all filters shows all data

### Test Case 5: Real-time Updates
**Objective**: Verify filters apply immediately without manual refresh

**Steps**:
1. Navigate to `/reports/service-reports`
2. Open browser developer tools (F12)
3. Go to Console tab
4. Click on "Services" tab
5. Change Status filter and observe console logs
6. Verify API calls are made automatically
7. Verify data updates without page refresh
8. Test rapid filter changes to verify debouncing

**Expected Results**:
- Console shows debug logs for filter changes
- API calls are made automatically after filter changes
- Data updates without manual refresh
- Rapid changes are debounced (500ms delay)

### Test Case 6: Error Handling
**Objective**: Verify graceful handling of API errors

**Steps**:
1. Navigate to `/reports/service-reports`
2. Open browser developer tools (F12)
3. Go to Network tab
4. Temporarily disconnect internet or block API calls
5. Try changing filters
6. Verify error messages are shown
7. Restore connection and verify filters work again

**Expected Results**:
- Graceful error handling when API fails
- User-friendly error messages
- Filters work normally when connection is restored

## Debug Information

### Console Logs to Monitor
- "Filter options response:" - Shows backend filter data
- "Mapped statuses:" - Shows processed status data
- "Filter changed:" - Shows individual filter changes
- "Filters changed, refetching data..." - Shows centralized filter handling
- "Fetching service data with params:" - Shows API parameters

### Network Requests to Monitor
- `GET /api/v1/service-calls/filters` - Filter options
- `GET /api/v1/service-calls?statusId=...&priority=...` - Filtered data

### Common Issues to Check
1. Empty dropdowns - Check filter options API response
2. Filters not applying - Check API parameters in network tab
3. Multiple API calls - Check debouncing is working
4. Data not updating - Check for JavaScript errors in console

## Performance Verification

### Expected Behavior
- Filter changes should be debounced (max 1 API call per 500ms)
- Data should update within 1-2 seconds of filter change
- No memory leaks from timeout handlers
- Smooth user experience without lag

### Performance Metrics
- API response time: < 2 seconds
- UI update time: < 500ms after API response
- Memory usage: Stable (no continuous growth)
- Network requests: Minimal and efficient
