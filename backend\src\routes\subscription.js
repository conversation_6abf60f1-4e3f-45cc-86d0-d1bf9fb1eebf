import express from 'express';
import { authenticate, requireTenant } from '../middleware/auth.js';
import {
  getSubscriptionPlans,
  getCurrentSubscription,
  createCheckoutSession,
  cancelSubscription,
  reactivateSubscription,
  getSubscriptionUsage,
} from '../controllers/subscriptionController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);
router.use(requireTenant);

/**
 * @swagger
 * /api/subscription/plans:
 *   get:
 *     summary: Get all subscription plans
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of subscription plans
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     plans:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/SubscriptionPlan'
 */
router.get('/plans', getSubscriptionPlans);

/**
 * @swagger
 * /api/subscription/current:
 *   get:
 *     summary: Get current tenant subscription
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current subscription details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 *                     usage:
 *                       type: object
 */
router.get('/current', getCurrentSubscription);

/**
 * @swagger
 * /api/subscription/checkout:
 *   post:
 *     summary: Create checkout session for subscription
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - planId
 *             properties:
 *               planId:
 *                 type: string
 *                 format: uuid
 *               interval:
 *                 type: string
 *                 enum: [monthly, yearly]
 *                 default: monthly
 *     responses:
 *       200:
 *         description: Checkout session created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                     url:
 *                       type: string
 */
router.post('/checkout', createCheckoutSession);

/**
 * @swagger
 * /api/subscription/cancel:
 *   post:
 *     summary: Cancel subscription
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               cancelAtPeriodEnd:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Subscription canceled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 */
router.post('/cancel', cancelSubscription);

/**
 * @swagger
 * /api/subscription/reactivate:
 *   post:
 *     summary: Reactivate subscription
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Subscription reactivated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 */
router.post('/reactivate', reactivateSubscription);

/**
 * @swagger
 * /api/subscription/usage:
 *   get:
 *     summary: Get subscription usage details
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: months
 *         schema:
 *           type: integer
 *           default: 3
 *         description: Number of months of usage history to retrieve
 *     responses:
 *       200:
 *         description: Subscription usage details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     subscription:
 *                       $ref: '#/components/schemas/Subscription'
 *                     currentUsage:
 *                       type: object
 *                     usageHistory:
 *                       type: object
 *                     limits:
 *                       type: object
 *                     limitCheck:
 *                       type: object
 */
router.get('/usage', getSubscriptionUsage);

export default router;
