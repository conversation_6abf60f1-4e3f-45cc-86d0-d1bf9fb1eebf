<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Service Cards Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        .warning { background-color: #fff3cd; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 Service Cards Data Debug</h1>
    
    <div id="results"></div>
    
    <script>
        const API_BASE_URL = 'http://localhost:8080/api/v1';
        
        async function debugServiceData() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test 1: Check if we can access the service analytics API
                resultsDiv.innerHTML += '<div class="card"><h3>🧪 Testing Service Analytics API</h3><p>Loading...</p></div>';
                
                const response = await fetch(`${API_BASE_URL}/reports/service-analytics?dateRange=30&groupBy=day`, {
                    headers: {
                        'Authorization': 'Bearer ' + localStorage.getItem('token') || '',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`API failed with status: ${response.status}`);
                }
                
                const data = await response.json();
                
                resultsDiv.innerHTML += `
                    <div class="card success">
                        <h3>✅ Service Analytics API Response</h3>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p><strong>Has Data:</strong> ${!!data.data}</p>
                        <p><strong>Has Summary:</strong> ${!!data.data?.summary}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                // Test 2: Check the specific values we need
                if (data.success && data.data && data.data.summary) {
                    const summary = data.data.summary;
                    resultsDiv.innerHTML += `
                        <div class="card success">
                            <h3>📊 Card Values</h3>
                            <p><strong>Free Calls:</strong> ${summary.freeCalls || 0}</p>
                            <p><strong>AMC Calls:</strong> ${summary.amcCalls || 0}</p>
                            <p><strong>Per Calls:</strong> ${summary.paidCalls || 0}</p>
                            <p><strong>Total Calls:</strong> ${summary.totalCalls || 0}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="card error">
                            <h3>❌ Data Structure Issue</h3>
                            <p>The API response doesn't have the expected structure.</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `
                    <div class="card error">
                        <h3>❌ API Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>This might be due to authentication issues or the API not being available.</p>
                    </div>
                `;
            }
        }
        
        // Run the debug test
        debugServiceData();
    </script>
</body>
</html>
