import React, { useState } from 'react';
import { FaTimes, FaUserPlus, FaBuilding, FaExclamationTriangle } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { leadAPI } from '../../services/api';

const ConvertToCustomerModal = ({ isOpen, onClose, lead, onConversionSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    tallySerialNumber: '',
    // Additional customer fields can be added here
    email: '',
    address: '',
    city: '',
    state: '',
    pincode: '',
    gstNo: '',
  });
  const [errors, setErrors] = useState({});
  const [duplicateWarning, setDuplicateWarning] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // Special handling for tally serial number - convert to uppercase
    const processedValue = name === 'tallySerialNumber' ? value.toUpperCase() : value;

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.tallySerialNumber || formData.tallySerialNumber.trim() === '') {
      newErrors.tallySerialNumber = 'Tally Serial Number is required';
    }

    // Optional: Add more validation rules
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.gstNo && formData.gstNo.length > 0 && formData.gstNo.length !== 15) {
      newErrors.gstNo = 'GST Number must be 15 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e, force = false) => {
    if (e) e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      setLoading(true);
      const submitData = { ...formData };
      if (force) {
        submitData.force = true;
      }

      const response = await leadAPI.convertToCustomer(lead.id, submitData);

      if (response.data?.success) {
        toast.success('Lead converted to customer successfully!');
        if (onConversionSuccess) {
          onConversionSuccess(response.data.data);
        }
        onClose();
        // Reset form
        setFormData({
          tallySerialNumber: '',
          email: '',
          address: '',
          city: '',
          state: '',
          pincode: '',
          gstNo: '',
        });
        setErrors({});
        setDuplicateWarning(null);
        setShowConfirmation(false);
      } else {
        toast.error(response.data?.message || 'Failed to convert lead to customer');
        if (response.data?.errors) {
          setErrors(response.data.errors);
        }
      }
    } catch (error) {
      console.error('Error converting lead to customer:', error);

      // Handle duplicate warning
      if (error.response?.status === 409 && error.response?.data?.requiresConfirmation) {
        setDuplicateWarning(error.response.data);
        setShowConfirmation(true);
        setLoading(false);
        return;
      }

      // Handle validation errors from backend
      if (error.response?.data?.errors) {
        setErrors(error.response.data.errors);
        toast.error('Please fix the validation errors');
      } else {
        toast.error('Failed to convert lead to customer');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmConversion = () => {
    setShowConfirmation(false);
    handleSubmit(null, true); // Force conversion
  };

  const handleCancelConfirmation = () => {
    setShowConfirmation(false);
    setDuplicateWarning(null);
  };

  const handleClose = () => {
    setFormData({
      tallySerialNumber: '',
      email: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      gstNo: '',
    });
    setErrors({});
    setDuplicateWarning(null);
    setShowConfirmation(false);
    onClose();
  };

  if (!isOpen || !lead) return null;

  return (
    <div className="fixed inset-0 z-modal bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
              <FaUserPlus className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Convert Lead to Customer</h2>
              <p className="text-sm text-gray-500">Convert "{lead.customer_name}" to a customer</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
          >
            <FaTimes className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
          {/* Lead Summary */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-900 mb-2 flex items-center">
              <FaBuilding className="mr-2 h-4 w-4" />
              Lead Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <span className="font-medium text-blue-800">Customer Name:</span>
                <span className="ml-2 text-blue-700">{lead.customer_name}</span>
              </div>
              <div>
                <span className="font-medium text-blue-800">Contact:</span>
                <span className="ml-2 text-blue-700">
                  {lead.country_code} {lead.contact_no}
                </span>
              </div>
              {lead.amount && (
                <div>
                  <span className="font-medium text-blue-800">Amount:</span>
                  <span className="ml-2 text-blue-700">₹{parseFloat(lead.amount).toLocaleString()}</span>
                </div>
              )}
              {lead.productsIssues && (
                <div>
                  <span className="font-medium text-blue-800">Product/Service:</span>
                  <span className="ml-2 text-blue-700">{lead.productsIssues.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Conversion Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tally Serial Number *
              </label>
              <input
                type="text"
                name="tallySerialNumber"
                value={formData.tallySerialNumber}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                  errors.tallySerialNumber ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter Tally Serial Number (will be converted to uppercase)"
                required
              />
              {errors.tallySerialNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.tallySerialNumber}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                💡 Tally Serial Numbers are automatically converted to uppercase (e.g., "abc123" becomes "ABC123")
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  GST Number
                </label>
                <input
                  type="text"
                  name="gstNo"
                  value={formData.gstNo}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                    errors.gstNo ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="15-digit GST number"
                  maxLength={15}
                />
                {errors.gstNo && (
                  <p className="mt-1 text-sm text-red-600">{errors.gstNo}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Enter complete address"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="City"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  State
                </label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="State"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pincode
                </label>
                <input
                  type="text"
                  name="pincode"
                  value={formData.pincode}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Pincode"
                  maxLength={6}
                />
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Converting...
              </>
            ) : (
              <>
                <FaUserPlus className="mr-2 h-4 w-4" />
                Convert to Customer
              </>
            )}
          </button>
        </div>
      </div>

      {/* Duplicate Warning Modal */}
      {showConfirmation && duplicateWarning && (
        <div className="fixed inset-0 z-modal-overlay bg-black bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                  <FaExclamationTriangle className="h-5 w-5 text-yellow-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Duplicate Customer Found</h3>
              </div>
            </div>

            <div className="p-6">
              <p className="text-sm text-gray-600 mb-4">
                We found existing customers with similar information:
              </p>

              <div className="space-y-3 mb-6">
                {duplicateWarning.duplicates?.map((duplicate, index) => (
                  <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="text-sm">
                      <span className="font-medium text-yellow-800">
                        {duplicate.field === 'company_name' ? 'Company Name' :
                         duplicate.field === 'email' ? 'Email' : 'Phone Number'}:
                      </span>
                      <span className="ml-2 text-yellow-700">{duplicate.value}</span>
                    </div>
                    <div className="text-xs text-yellow-600 mt-1">
                      Existing Customer: {duplicate.existing.company_name} ({duplicate.existing.customer_code})
                    </div>
                  </div>
                ))}
              </div>

              <p className="text-sm text-gray-600 mb-4">
                Do you want to proceed anyway and create a duplicate customer?
              </p>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                type="button"
                onClick={handleCancelConfirmation}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmConversion}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-yellow-600 hover:bg-yellow-700 flex items-center"
              >
                <FaUserPlus className="mr-2 h-4 w-4" />
                Add Anyway
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConvertToCustomerModal;
