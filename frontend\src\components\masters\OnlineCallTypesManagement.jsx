import React, { useState, useEffect } from 'react';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaFilter,
  FaEye,
  FaEyeSlash,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaTh,
  Fa<PERSON>ist,
  FaEllipsisV
} from 'react-icons/fa';
import toast from 'react-hot-toast';
import onlineCallTypeAPI from '../../services/onlineCallTypeAPI';
import productsIssuesAPI from '../../services/productsIssuesAPI';
import Spinner from '../ui/Feedback/Spinner';
import ConfirmDialog from '../ui/ConfirmDialog';
import OnlineCallTypeForm from './OnlineCallTypeForm';
import { Card, CardBody } from '../ui/Card';

const OnlineCallTypesManagement = () => {
  // State management
  const [callTypes, setCallTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  });

  // Filter and search state
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    isActive: '',
    sortBy: 'category',
    sortOrder: 'asc',
  });

  // UI state
  const [showForm, setShowForm] = useState(false);
  const [editingCallType, setEditingCallType] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [categories, setCategories] = useState([]);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'
  const [dropdownOpen, setDropdownOpen] = useState({});

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Load data on component mount and filter changes
  useEffect(() => {
    loadCallTypes();
    loadCategories();
  }, [filters, pagination.currentPage]);

  /**
   * Load online call types with current filters
   */
  const loadCallTypes = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.currentPage,
        limit: pagination.itemsPerPage,
        ...filters,
      };

      const response = await onlineCallTypeAPI.getAll(params);

      setCallTypes(response.data.onlineCallTypes || []);
      setPagination(response.data.pagination || pagination);
    } catch (error) {
      console.error('Error loading call types:', error);
      toast.error('Failed to load online call types');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load categories for filter dropdown
   */
  const loadCategories = async () => {
    try {
      const categoriesData = await onlineCallTypeAPI.getCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  /**
   * Handle filter changes
   */
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  /**
   * Handle sorting
   */
  const handleSort = (field) => {
    const newSortOrder = filters.sortBy === field && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    setFilters(prev => ({
      ...prev,
      sortBy: field,
      sortOrder: newSortOrder,
    }));
  };

  /**
   * Get sort icon for column headers
   */
  const getSortIcon = (field) => {
    if (filters.sortBy !== field) return <FaSort className="text-gray-400" />;
    return filters.sortOrder === 'asc' ?
      <FaSortUp className="text-blue-500" /> :
      <FaSortDown className="text-blue-500" />;
  };

  /**
   * Handle pagination
   */
  const handlePageChange = (page) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  /**
   * Handle create new call type
   */
  const handleCreate = () => {
    setEditingCallType(null);
    setShowForm(true);
  };

  /**
   * Handle edit call type
   */
  const handleEdit = (callType) => {
    setEditingCallType(callType);
    setShowForm(true);
  };

  /**
   * Handle form submit (create/update)
   */
  const handleFormSubmit = async (data) => {
    try {
      if (editingCallType) {
        await onlineCallTypeAPI.update(editingCallType.id, data);
        toast.success('Online call type updated successfully');
      } else {
        await onlineCallTypeAPI.create(data);
        toast.success('Online call type created successfully');
      }

      setShowForm(false);
      setEditingCallType(null);
      loadCallTypes();
    } catch (error) {
      console.error('Error saving call type:', error);
      const errorMessage = error.response?.data?.message || 'Failed to save online call type';
      toast.error(errorMessage);
    }
  };

  /**
   * Handle delete call type
   */
  const handleDelete = (callType) => {
    setConfirmAction({
      type: 'delete',
      callType,
      message: `Are you sure you want to delete "${callType.name}"? This action cannot be undone.`,
    });
    setShowConfirmDialog(true);
  };

  /**
   * Handle bulk status update
   */
  const handleBulkStatusUpdate = (isActive) => {
    if (selectedItems.length === 0) {
      toast.warning('Please select items to update');
      return;
    }

    setConfirmAction({
      type: 'bulkStatus',
      isActive,
      message: `Are you sure you want to ${isActive ? 'activate' : 'deactivate'} ${selectedItems.length} selected item(s)?`,
    });
    setShowConfirmDialog(true);
  };

  /**
   * Execute confirmed action
   */
  const executeConfirmAction = async () => {
    try {
      if (confirmAction.type === 'delete') {
        await onlineCallTypeAPI.delete(confirmAction.callType.id);
        toast.success('Online call type deleted successfully');
      } else if (confirmAction.type === 'bulkStatus') {
        await onlineCallTypeAPI.bulkUpdateStatus(selectedItems, confirmAction.isActive);
        toast.success(`${selectedItems.length} item(s) updated successfully`);
        setSelectedItems([]);
      }

      loadCallTypes();
    } catch (error) {
      console.error('Error executing action:', error);
      const errorMessage = error.response?.data?.message || 'Failed to execute action';
      toast.error(errorMessage);
    } finally {
      setShowConfirmDialog(false);
      setConfirmAction(null);
    }
  };

  /**
   * Handle select all checkbox
   */
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(callTypes.map(ct => ct.id));
    } else {
      setSelectedItems([]);
    }
  };

  /**
   * Handle individual item selection
   */
  const handleSelectItem = (id, checked) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  /**
   * Clear all filters
   */
  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      isActive: '',
      sortBy: 'category',
      sortOrder: 'asc',
    });
  };

  // Toggle dropdown for actions
  const toggleDropdown = (itemId) => {
    setDropdownOpen(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen({});
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Card View Component
  const CallTypeCard = ({ callType }) => (
    <Card className="hover:shadow-lg transition-all duration-200 border border-gray-200">
      <CardBody className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <span className="text-blue-600 font-bold text-sm">📞</span>
            </div>
            <div>
              <h6 className="font-bold text-gray-900 mb-1">{callType.name}</h6>
              <p className="text-sm text-gray-600 mb-0">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {callType.category || 'Uncategorized'}
                </span>
              </p>
            </div>
          </div>
          <div className="relative">
            <button
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                toggleDropdown(callType.id);
              }}
            >
              <FaEllipsisV className="h-4 w-4" />
            </button>
            {dropdownOpen[callType.id] && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      handleEdit(callType);
                      setDropdownOpen({});
                    }}
                  >
                    <FaEdit className="mr-3 h-4 w-4 text-blue-600" />
                    Edit
                  </button>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    onClick={() => {
                      handleDelete(callType);
                      setDropdownOpen({});
                    }}
                  >
                    <FaTrash className="mr-3 h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Description:</span>
            <span className="text-sm font-medium">{callType.description || 'N/A'}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              callType.is_active
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}
            >
              {callType.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            checked={selectedItems.includes(callType.id)}
            onChange={(e) => handleSelectItem(callType.id, e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
          />
          <span className="text-sm text-gray-600">Select for bulk actions</span>
        </div>
      </CardBody>
    </Card>
  );

  if (loading && callTypes.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Online Call Types Management</h2>
          <p className="text-gray-600 mt-1">
            Manage online call types and issues for service calls
          </p>
        </div>
        <button
          onClick={handleCreate}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
        >
          <FaPlus />
          <span>Add Call Type</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search call types..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <FaFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.name} value={category.name}>
                  {category.name} ({category.count})
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <select
            value={filters.isActive}
            onChange={(e) => handleFilterChange('isActive', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>

          {/* Clear Filters */}
          <button
            onClick={clearFilters}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Clear Filters
          </button>
        </div>

        {/* View Mode Toggle */}
        <div className="flex justify-end mt-4">
          {/* View Mode Toggle - Hidden on mobile, only show card view */}
          <div className="hidden sm:flex gap-1">
            <button
              className={`inline-flex items-center justify-center px-3 py-2 text-sm font-medium border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                viewMode === 'table'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setViewMode('table')}
            >
              <FaList className="h-4 w-4" />
            </button>
            <button
              className={`inline-flex items-center justify-center px-3 py-2 text-sm font-medium border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${
                viewMode === 'card'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
              onClick={() => setViewMode('card')}
            >
              <FaTh className="h-4 w-4" />
            </button>
          </div>

          {/* Mobile - Force card view only */}
          <div className="sm:hidden">
            <div className="inline-flex items-center justify-center px-3 py-2 text-sm font-medium bg-gray-100 text-gray-500 border border-gray-300 rounded-md cursor-not-allowed">
              <FaTh className="h-4 w-4" />
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <span className="text-blue-800 font-medium">
              {selectedItems.length} item(s) selected
            </span>
            <div className="space-x-2">
              <button
                onClick={() => handleBulkStatusUpdate(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                <FaEye className="inline mr-1" />
                Activate
              </button>
              <button
                onClick={() => handleBulkStatusUpdate(false)}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                <FaEyeSlash className="inline mr-1" />
                Deactivate
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Conditional View Rendering */}
      {/* Table View - Hidden on mobile */}
      {viewMode === 'table' && (
        <div className="hidden sm:block">
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectedItems.length === callTypes.length && callTypes.length > 0}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Name</span>
                        {getSortIcon('name')}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('category')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Category</span>
                        {getSortIcon('category')}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSort('is_active')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Status</span>
                        {getSortIcon('is_active')}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {callTypes.map((callType) => (
                    <tr key={callType.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedItems.includes(callType.id)}
                          onChange={(e) => handleSelectItem(callType.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {callType.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {callType.category || 'Uncategorized'}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900 max-w-xs truncate">
                          {callType.description || '-'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          callType.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                        >
                          {callType.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(callType)}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="Edit"
                          >
                            <FaEdit />
                          </button>
                          <button
                            onClick={() => handleDelete(callType)}
                            className="text-red-600 hover:text-red-900 transition-colors"
                            title="Delete"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Card View - Always visible on mobile, toggleable on desktop */}
      {viewMode === 'card' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {callTypes.map(callType => (
            <CallTypeCard key={callType.id} callType={callType} />
          ))}
        </div>
      )}

      {/* Mobile Card View - Force card view on mobile */}
      <div className="sm:hidden">
        <div className="grid grid-cols-1 gap-4">
          {callTypes.map(callType => (
            <CallTypeCard key={callType.id} callType={callType} />
          ))}
        </div>
      </div>

      {/* Empty State */}
      {callTypes.length === 0 && !loading && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg mb-2">No online call types found</div>
            <p className="text-gray-400 mb-4">
              {filters.search || filters.category || filters.isActive
                ? 'Try adjusting your filters'
                : 'Get started by creating your first online call type'
              }
            </p>
            {!filters.search && !filters.category && !filters.isActive && (
              <button
                onClick={handleCreate}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaPlus className="inline mr-2" />
                Add First Call Type
              </button>
            )}
          </div>
        </div>
      )}


      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {((pagination.currentPage - 1) * pagination.itemsPerPage) + 1} to{' '}
            {Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems)} of{' '}
            {pagination.totalItems} results
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
              className="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-2 text-sm border rounded-lg ${
                  page === pagination.currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => handlePageChange(pagination.currentPage + 1)}
              disabled={pagination.currentPage === pagination.totalPages}
              className="px-3 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Form Modal */}
      {showForm && (
        <OnlineCallTypeForm
          callType={editingCallType}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingCallType(null);
          }}
        />
      )}

      {/* Confirm Dialog */}
      {showConfirmDialog && (
        <ConfirmDialog
          title={confirmAction?.type === 'delete' ? 'Delete Call Type' : 'Update Status'}
          message={confirmAction?.message}
          onConfirm={executeConfirmAction}
          onCancel={() => {
            setShowConfirmDialog(false);
            setConfirmAction(null);
          }}
          confirmText={confirmAction?.type === 'delete' ? 'Delete' : 'Update'}
          confirmButtonClass={confirmAction?.type === 'delete' ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700'}
        />
      )}

      {/* Loading Overlay */}
      {loading && callTypes.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-modal">
          <Spinner size="xl" variant="white" />
        </div>
      )}
    </div>
  );
};

export default OnlineCallTypesManagement;
