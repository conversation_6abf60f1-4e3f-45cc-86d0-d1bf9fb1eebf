/**
 * Test file to verify customer search functionality
 * This can be run in the browser console to test the search
 */

import { apiService } from '../services/api';

// Test function to verify customer search API
export const testCustomerSearch = async (searchTerm = 'test') => {
  console.log(`🔍 Testing customer search with term: "${searchTerm}"`);
  
  try {
    const response = await apiService.get('/customers', {
      params: {
        search: searchTerm,
        limit: 1000,
        page: 1,
        sortBy: 'company_name',
        sortOrder: 'ASC'
      }
    });

    if (response.data?.success && response.data?.data?.customers) {
      const customers = response.data.data.customers;
      console.log(`✅ Search successful: Found ${customers.length} customers`);
      console.log('📋 Sample results:', customers.slice(0, 5));
      
      // Test comprehensive customer details
      if (customers.length > 0) {
        const firstCustomer = customers[0];
        console.log('🔍 First customer details:', {
          id: firstCustomer.id,
          company_name: firstCustomer.company_name,
          customer_code: firstCustomer.customer_code,
          phone: firstCustomer.phone,
          email: firstCustomer.email,
          tally_serial_number: firstCustomer.tally_serial_number,
          custom_fields: firstCustomer.custom_fields
        });
      }
      
      return {
        success: true,
        count: customers.length,
        customers: customers
      };
    } else {
      console.log('❌ Search failed: No customers found or invalid response');
      return {
        success: false,
        error: 'No customers found or invalid response'
      };
    }
  } catch (error) {
    console.error('❌ Search error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Test function to verify all customers can be fetched
export const testAllCustomers = async () => {
  console.log('🔍 Testing fetch all customers...');
  
  try {
    const response = await apiService.get('/customers', {
      params: {
        limit: 1000,
        page: 1,
        sortBy: 'company_name',
        sortOrder: 'ASC'
      }
    });

    if (response.data?.success && response.data?.data?.customers) {
      const customers = response.data.data.customers;
      console.log(`✅ Fetch all successful: Found ${customers.length} total customers`);
      
      return {
        success: true,
        totalCount: customers.length,
        customers: customers
      };
    } else {
      console.log('❌ Fetch all failed: Invalid response');
      return {
        success: false,
        error: 'Invalid response'
      };
    }
  } catch (error) {
    console.error('❌ Fetch all error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Test function to verify specific customer search
export const testSpecificCustomerSearch = async () => {
  console.log('🔍 Testing specific customer searches...');
  
  const testTerms = ['power', 'pandi', 'test', 'company', 'ltd'];
  const results = [];
  
  for (const term of testTerms) {
    const result = await testCustomerSearch(term);
    results.push({
      searchTerm: term,
      ...result
    });
    
    // Wait a bit between searches to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('📊 Search results summary:', results);
  return results;
};

// Export for use in browser console
window.testCustomerSearch = testCustomerSearch;
window.testAllCustomers = testAllCustomers;
window.testSpecificCustomerSearch = testSpecificCustomerSearch;

console.log('🧪 Customer search test functions loaded. Available functions:');
console.log('- testCustomerSearch(searchTerm)');
console.log('- testAllCustomers()');
console.log('- testSpecificCustomerSearch()');
