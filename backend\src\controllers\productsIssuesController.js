import { Op } from 'sequelize';
import models from '../models/index.js';
import { AppError } from '../middleware/errorHandler.js';
import { logger } from '../utils/logger.js';

const { ProductsIssues } = models;

/**
 * Get all products/issues with pagination and filtering
 */
export const getProductsIssues = async (req, res, next) => {
  try {
    logger.info('getProductsIssues called with:', {
      query: req.query,
      user: req.user ? {
        id: req.user.id,
        email: req.user.email,
        tenant_id: req.user.tenant_id,
        tenantId: req.user.tenantId
      } : null
    });

    const {
      page = 1,
      limit = 10,
      search,
      category,
      isActive,
      sortBy = 'sort_order',
      sortOrder = 'asc',
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const whereClause = {};

    // Add tenant filter if user has tenant
    const userTenantId = req.user?.tenant_id || req.user?.tenantId;
    if (userTenantId) {
      whereClause[Op.or] = [
        { tenant_id: userTenantId },
        { tenant_id: null }, // Include global/default items
      ];
      logger.info('Added tenant filter for tenant_id:', userTenantId);
    } else {
      logger.info('No tenant filter applied - user has no tenant_id');
    }

    // Add search filter
    if (search) {
      // If we already have an Op.or for tenant, we need to restructure
      if (whereClause[Op.or]) {
        whereClause[Op.and] = [
          { [Op.or]: whereClause[Op.or] }, // Wrap existing tenant filter
          {
            [Op.or]: [
              { name: { [Op.iLike]: `%${search}%` } },
              { description: { [Op.iLike]: `%${search}%` } },
            ],
          },
        ];
        delete whereClause[Op.or]; // Remove the original Op.or
      } else {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
        ];
      }
    }

    // Add category filter
    if (category) {
      whereClause.category = category;
    }

    // Add active status filter
    if (isActive !== undefined) {
      whereClause.is_active = isActive === 'true';
    }

    // Define valid sort fields
    const validSortFields = ['name', 'category', 'sort_order', 'created_at', 'updated_at'];
    const orderField = validSortFields.includes(sortBy) ? sortBy : 'sort_order';
    const orderDirection = sortOrder.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    logger.info('Executing query with:', {
      whereClause: JSON.stringify(whereClause),
      orderField,
      orderDirection,
      limit: parseInt(limit),
      offset
    });

    // Simplified query without includes first to debug
    const { count, rows: productsIssues } = await ProductsIssues.findAndCountAll({
      where: whereClause,
      order: [[orderField, orderDirection], ['name', 'ASC']],
      limit: parseInt(limit),
      offset,
    });

    logger.info('Query successful:', { count, rowsReturned: productsIssues.length });

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        productsIssues,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching products/issues:', error);
    logger.error('Error details:', error.message);
    logger.error('Stack trace:', error.stack);
    next(new AppError('Failed to fetch products/issues', 500));
  }
};

/**
 * Get products/issues by ID
 */
export const getProductsIssuesById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const productsIssues = await ProductsIssues.findByPk(id, {
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'name', 'email'],
        },
        {
          model: models.User,
          as: 'updater',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });

    if (!productsIssues) {
      return next(new AppError('Products/Issues not found', 404));
    }

    // Check tenant access
    if (req.user?.tenant_id && productsIssues.tenant_id && productsIssues.tenant_id !== req.user.tenant_id) {
      return next(new AppError('Access denied', 403));
    }

    res.json({
      success: true,
      data: { productsIssues },
    });
  } catch (error) {
    logger.error('Error fetching products/issues by ID:', error);
    next(new AppError('Failed to fetch products/issues', 500));
  }
};

/**
 * Create new products/issues
 */
export const createProductsIssues = async (req, res, next) => {
  try {
    const { name, description, category, is_active = true, sort_order = 0 } = req.body;

    // Check for duplicate name within tenant
    const existingItem = await ProductsIssues.findOne({
      where: {
        name: name.trim(),
        [Op.or]: [
          { tenant_id: req.user?.tenant_id },
          { tenant_id: null },
        ],
      },
    });

    if (existingItem) {
      return next(new AppError('Products/Issues with this name already exists', 400));
    }

    const productsIssues = await ProductsIssues.create({
      name: name.trim(),
      description: description?.trim(),
      category: category?.trim(),
      is_active,
      sort_order,
      tenant_id: req.user?.tenant_id,
      created_by: req.user?.id,
      updated_by: req.user?.id,
    });

    res.status(201).json({
      success: true,
      message: 'Products/Issues created successfully',
      data: { productsIssues },
    });
  } catch (error) {
    logger.error('Error creating products/issues:', error);
    if (error.name === 'SequelizeValidationError') {
      const messages = error.errors.map(err => err.message);
      return next(new AppError(messages.join(', '), 400));
    }
    next(new AppError('Failed to create products/issues', 500));
  }
};

/**
 * Update products/issues
 */
export const updateProductsIssues = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, category, is_active, sort_order } = req.body;

    const productsIssues = await ProductsIssues.findByPk(id);

    if (!productsIssues) {
      return next(new AppError('Products/Issues not found', 404));
    }

    // Check tenant access
    if (req.user?.tenant_id && productsIssues.tenant_id && productsIssues.tenant_id !== req.user.tenant_id) {
      return next(new AppError('Access denied', 403));
    }

    // Check for duplicate name (excluding current record)
    if (name && name.trim() !== productsIssues.name) {
      const existingItem = await ProductsIssues.findOne({
        where: {
          name: name.trim(),
          id: { [Op.ne]: id },
          [Op.or]: [
            { tenant_id: req.user?.tenant_id },
            { tenant_id: null },
          ],
        },
      });

      if (existingItem) {
        return next(new AppError('Products/Issues with this name already exists', 400));
      }
    }

    // Update fields
    const updateData = {
      updated_by: req.user?.id,
    };

    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (category !== undefined) updateData.category = category?.trim();
    if (is_active !== undefined) updateData.is_active = is_active;
    if (sort_order !== undefined) updateData.sort_order = sort_order;

    await productsIssues.update(updateData);

    res.json({
      success: true,
      message: 'Products/Issues updated successfully',
      data: { productsIssues },
    });
  } catch (error) {
    logger.error('Error updating products/issues:', error);
    if (error.name === 'SequelizeValidationError') {
      const messages = error.errors.map(err => err.message);
      return next(new AppError(messages.join(', '), 400));
    }
    next(new AppError('Failed to update products/issues', 500));
  }
};

/**
 * Delete products/issues (soft delete)
 */
export const deleteProductsIssues = async (req, res, next) => {
  try {
    const { id } = req.params;

    const productsIssues = await ProductsIssues.findByPk(id);

    if (!productsIssues) {
      return next(new AppError('Products/Issues not found', 404));
    }

    // Check tenant access
    if (req.user?.tenant_id && productsIssues.tenant_id && productsIssues.tenant_id !== req.user.tenant_id) {
      return next(new AppError('Access denied', 403));
    }

    // Prevent deletion of default items
    if (productsIssues.is_default) {
      return next(new AppError('Cannot delete default products/issues', 400));
    }

    await productsIssues.destroy();

    res.json({
      success: true,
      message: 'Products/Issues deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting products/issues:', error);
    next(new AppError('Failed to delete products/issues', 500));
  }
};

/**
 * Search products/issues for dropdown
 */
export const searchProductsIssues = async (req, res, next) => {
  try {
    const { q = '', category, limit = 50 } = req.query;

    const whereClause = {
      is_active: true,
    };

    // Add tenant filter
    if (req.user?.tenant_id) {
      whereClause[Op.or] = [
        { tenant_id: req.user.tenant_id },
        { tenant_id: null },
      ];
    }

    // Add search filter
    if (q) {
      whereClause[Op.and] = [
        ...(whereClause[Op.and] || []),
        {
          name: {
            [Op.iLike]: `%${q}%`,
          },
        },
      ];
    }

    // Add category filter
    if (category) {
      whereClause.category = category;
    }

    const productsIssues = await ProductsIssues.findAll({
      where: whereClause,
      order: [['sort_order', 'ASC'], ['name', 'ASC']],
      limit: parseInt(limit),
      attributes: ['id', 'name', 'category', 'description'],
    });

    res.json({
      success: true,
      data: productsIssues,
    });
  } catch (error) {
    logger.error('Error searching products/issues:', error);
    next(new AppError('Failed to search products/issues', 500));
  }
};

/**
 * Get all categories with counts
 */
export const getCategories = async (req, res, next) => {
  try {
    const whereClause = {
      is_active: true,
      category: { [Op.ne]: null },
    };

    // Add tenant filter
    if (req.user?.tenant_id) {
      whereClause[Op.or] = [
        { tenant_id: req.user.tenant_id },
        { tenant_id: null },
      ];
    }

    const categories = await ProductsIssues.findAll({
      where: whereClause,
      attributes: [
        'category',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['category'],
      order: [['category', 'ASC']],
      raw: true,
    });

    res.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    logger.error('Error fetching categories:', error);
    next(new AppError('Failed to fetch categories', 500));
  }
};

/**
 * Toggle active status of products/issues
 */
export const toggleActiveProductsIssues = async (req, res, next) => {
  try {
    const { id } = req.params;

    const whereClause = { id };

    // Add tenant filter
    if (req.user?.tenant_id) {
      whereClause[Op.or] = [
        { tenant_id: req.user.tenant_id },
        { tenant_id: null },
      ];
    }

    const productsIssues = await ProductsIssues.findOne({
      where: whereClause,
    });

    if (!productsIssues) {
      return next(new AppError('Products/Issues not found', 404));
    }

    const previousStatus = productsIssues.is_active;
    const newStatus = !previousStatus;

    await productsIssues.update({
      is_active: newStatus,
      updated_by: req.user?.id,
    });

    logger.info('Products/Issues status toggled:', {
      id: productsIssues.id,
      name: productsIssues.name,
      previousStatus,
      newStatus,
      updatedBy: req.user?.id,
    });

    res.json({
      success: true,
      message: 'Products/Issues status updated successfully',
      data: {
        productIssue: productsIssues,
        previousStatus,
        newStatus,
      },
    });
  } catch (error) {
    logger.error('Error toggling products/issues status:', error);
    next(new AppError('Failed to toggle products/issues status', 500));
  }
};

/**
 * Bulk update status of products/issues
 */
export const bulkUpdateStatus = async (req, res, next) => {
  try {
    const { ids, is_active } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return next(new AppError('IDs array is required', 400));
    }

    const whereClause = {
      id: { [Op.in]: ids },
    };

    // Add tenant filter
    if (req.user?.tenant_id) {
      whereClause[Op.and] = [
        ...(whereClause[Op.and] || []),
        {
          [Op.or]: [
            { tenant_id: req.user.tenant_id },
            { tenant_id: null },
          ],
        },
      ];
    }

    const [updatedCount] = await ProductsIssues.update(
      {
        is_active,
        updated_by: req.user?.id,
      },
      {
        where: whereClause,
      }
    );

    res.json({
      status: 'success',
      message: `${updatedCount} products/issues updated successfully`,
      data: { updatedCount },
    });
  } catch (error) {
    logger.error('Error bulk updating products/issues status:', error);
    next(new AppError('Failed to update products/issues status', 500));
  }
};
