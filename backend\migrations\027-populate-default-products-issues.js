import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  const defaultProductsIssues = [
    // License Types
    { name: 'SU', category: 'License', sort_order: 1 },
    { name: 'MU', category: 'License', sort_order: 2 },
    { name: 'TSS SU', category: 'License', sort_order: 3 },
    { name: 'MU SU', category: 'License', sort_order: 4 },
    
    // Service Types
    { name: 'Customization', category: 'Service', sort_order: 5 },
    { name: 'Onsite', category: 'Service', sort_order: 6 },
    { name: 'AMC- Multi User', category: 'Service', sort_order: 7 },
    { name: 'AMC- Single User', category: 'Service', sort_order: 8 },
    
    // Technical Issues
    { name: 'Bank Doubts', category: 'Technical', sort_order: 9 },
    { name: 'Cheque Print', category: 'Technical', sort_order: 10 },
    { name: 'Crack Version', category: 'Technical', sort_order: 11 },
    { name: 'Data Issue', category: 'Technical', sort_order: 12 },
    { name: 'Data Migration', category: 'Technical', sort_order: 13 },
    { name: 'Data Restore', category: 'Technical', sort_order: 14 },
    { name: 'Data Splitting', category: 'Technical', sort_order: 15 },
    { name: 'Data Synchronization', category: 'Technical', sort_order: 16 },
    { name: 'Email Configuration', category: 'Technical', sort_order: 17 },
    
    // GST Related
    { name: 'E-Way Bill Doubts', category: 'GST', sort_order: 18 },
    { name: 'GST 3B Doubts', category: 'GST', sort_order: 19 },
    { name: 'GST CALCULATION ISSUE', category: 'GST', sort_order: 20 },
    { name: 'GST Doubts', category: 'GST', sort_order: 21 },
    { name: 'GST Filing Doubts', category: 'GST', sort_order: 22 },
    { name: 'GST Mismatch', category: 'GST', sort_order: 23 },
    { name: 'GSTR 1 Doubts', category: 'GST', sort_order: 24 },
    { name: 'GSTR 2A', category: 'GST', sort_order: 25 },
    { name: 'GST Portal Filing Doubts', category: 'GST', sort_order: 26 },
    { name: 'HSN Code Issue', category: 'GST', sort_order: 27 },
    
    // Import/Export
    { name: 'Excel to Tally Import', category: 'Import/Export', sort_order: 28 },
    { name: 'Excel to Tally Import Doubts', category: 'Import/Export', sort_order: 29 },
    
    // Mobile App
    { name: 'Mobile App', category: 'Mobile', sort_order: 30 },
    { name: 'Mobile App Demo', category: 'Mobile', sort_order: 31 },
    { name: 'Mobile App Sync Issue', category: 'Mobile', sort_order: 32 },
    
    // Installation & Updates
    { name: 'New Installation', category: 'Installation', sort_order: 33 },
    { name: 'New Release Update', category: 'Installation', sort_order: 34 },
    { name: 'Old Release Installation', category: 'Installation', sort_order: 35 },
    { name: 'Re Installation', category: 'Installation', sort_order: 36 },
    
    // Configuration
    { name: 'Print Configure', category: 'Configuration', sort_order: 37 },
    { name: 'Proxy Configuration', category: 'Configuration', sort_order: 38 },
    { name: 'Logo Configuration', category: 'Configuration', sort_order: 39 },
    
    // Account Related
    { name: 'Ledger Creation Doubts', category: 'Accounts', sort_order: 40 },
    { name: 'Ledger Opening Balance', category: 'Accounts', sort_order: 41 },
    { name: 'Voucher Class Doubts', category: 'Accounts', sort_order: 42 },
    { name: 'Voucher Entry Doubts', category: 'Accounts', sort_order: 43 },
    
    // Reports
    { name: 'Statement Issue', category: 'Reports', sort_order: 44 },
    { name: 'Stock Item Issue', category: 'Reports', sort_order: 45 },
    { name: 'Stock Summary Reports Issue', category: 'Reports', sort_order: 46 },
    
    // Proposals
    { name: 'SU Proposal', category: 'Proposal', sort_order: 47 },
    { name: 'MU Proposal', category: 'Proposal', sort_order: 48 },
    { name: 'TSS SU Proposal', category: 'Proposal', sort_order: 49 },
    { name: 'TSS MU Proposal', category: 'Proposal', sort_order: 50 },
    
    // E-Invoice
    { name: 'E Invoice', category: 'E-Invoice', sort_order: 51 },
    { name: 'E Invoice Doubts', category: 'E-Invoice', sort_order: 52 },
    { name: 'E Invoice Config', category: 'E-Invoice', sort_order: 53 },
    
    // Payments
    { name: 'Payment', category: 'Payment', sort_order: 54 },
    { name: 'TSS SU Payment', category: 'Payment', sort_order: 55 },
    { name: 'TSS MU Payment', category: 'Payment', sort_order: 56 },
    { name: 'AMC SU Payment', category: 'Payment', sort_order: 57 },
    { name: 'AMC MU Payment', category: 'Payment', sort_order: 58 },
    
    // Demo
    { name: 'Single User Demo', category: 'Demo', sort_order: 59 },
    { name: 'Multi User Demo', category: 'Demo', sort_order: 60 },
    { name: 'Prime Demo', category: 'Demo', sort_order: 61 },
    { name: 'Prime Demo TSS', category: 'Demo', sort_order: 62 },
    { name: 'Prime Demo Pack', category: 'Demo', sort_order: 63 },
    
    // Training
    { name: 'Tally Training', category: 'Training', sort_order: 64 },
    { name: 'Training', category: 'Training', sort_order: 65 },
    
    // TDS/TCS
    { name: 'TDS Doubts', category: 'Tax', sort_order: 66 },
    { name: 'TCS Doubt', category: 'Tax', sort_order: 67 },
    
    // Cloud & Backup
    { name: 'Tally Cloud', category: 'Cloud', sort_order: 68 },
    { name: 'Tally Cloud Backup', category: 'Cloud', sort_order: 69 },
    { name: 'Tally Cloud Renewal', category: 'Cloud', sort_order: 70 },
    { name: 'Tally Cloud Demo', category: 'Cloud', sort_order: 71 },
    { name: 'Cloud Storage', category: 'Cloud', sort_order: 72 },
    
    // Server Related
    { name: 'Server Changing', category: 'Server', sort_order: 73 },
    { name: 'Server Issue', category: 'Server', sort_order: 74 },
    { name: 'Tally.Server 9 South Asia', category: 'Server', sort_order: 75 },
    { name: 'Tally Server Trail', category: 'Server', sort_order: 76 },
    { name: 'Tally Server Licence', category: 'Server', sort_order: 77 },

    // Additional Technical Issues
    { name: 'INTEREST CALCULATION ISSUE', category: 'Technical', sort_order: 78 },
    { name: 'Invoice Isssue', category: 'Technical', sort_order: 79 },
    { name: 'License Reactivation', category: 'Technical', sort_order: 80 },
    { name: 'Mail Id Change', category: 'Technical', sort_order: 81 },
    { name: 'Web Broswer Doubts', category: 'Technical', sort_order: 82 },
    { name: 'Remote Access', category: 'Technical', sort_order: 83 },
    { name: 'Rewrite', category: 'Technical', sort_order: 84 },
    { name: 'Security Control', category: 'Technical', sort_order: 85 },
    { name: 'Tally Data Recovery', category: 'Technical', sort_order: 86 },
    { name: 'Tally Slow Issue', category: 'Technical', sort_order: 87 },

    // Additional Items
    { name: 'Free Addons', category: 'Addons', sort_order: 88 },
    { name: 'Monthly Visit', category: 'Service', sort_order: 89 },
    { name: 'Tally Doubts', category: 'General', sort_order: 90 },
    { name: 'Purchase Order', category: 'Orders', sort_order: 91 },
    { name: 'Sales Order', category: 'Orders', sort_order: 92 },
    { name: 'Tss MU', category: 'TSS', sort_order: 93 },
    { name: 'Tss SU', category: 'TSS', sort_order: 94 },
    { name: 'SU to Mu Upgrade', category: 'Upgrade', sort_order: 95 },
    { name: 'Client Connecting', category: 'Technical', sort_order: 96 },
    { name: 'Cost Centre', category: 'Accounts', sort_order: 97 },
    { name: 'Data Rewrite', category: 'Technical', sort_order: 98 },
    { name: 'Data Merge', category: 'Technical', sort_order: 99 },
    { name: 'SU + TDL', category: 'TDL', sort_order: 100 },
  ];

  // Insert default products/issues
  for (const item of defaultProductsIssues) {
    await queryInterface.bulkInsert('products_issues', [{
      id: Sequelize.literal('gen_random_uuid()'),
      name: item.name,
      category: item.category,
      sort_order: item.sort_order,
      is_active: true,
      is_default: true,
      created_at: new Date(),
      updated_at: new Date(),
    }]);
  }

  console.log(`✅ Inserted ${defaultProductsIssues.length} default products/issues`);
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.bulkDelete('products_issues', {
    is_default: true
  });
  console.log('✅ Removed default products/issues');
};
