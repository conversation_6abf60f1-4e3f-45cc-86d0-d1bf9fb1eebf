import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  try {
    // First, check if the global unique constraint exists and remove it
    const constraints = await queryInterface.showConstraint('products_issues');
    
    // Look for the global unique constraint on name
    const globalUniqueConstraint = constraints.find(constraint => 
      constraint.constraintType === 'UNIQUE' && 
      constraint.columnNames && 
      constraint.columnNames.length === 1 && 
      constraint.columnNames[0] === 'name'
    );
    
    if (globalUniqueConstraint) {
      console.log('Removing global unique constraint on name field...');
      await queryInterface.removeConstraint('products_issues', globalUniqueConstraint.constraintName);
      console.log('✅ Global unique constraint removed');
    }
    
    // Ensure the composite unique constraint exists (name + tenant_id)
    const compositeConstraint = constraints.find(constraint => 
      constraint.constraintName === 'products_issues_name_tenant_unique'
    );
    
    if (!compositeConstraint) {
      console.log('Adding composite unique constraint (name + tenant_id)...');
      await queryInterface.addConstraint('products_issues', {
        fields: ['name', 'tenant_id'],
        type: 'unique',
        name: 'products_issues_name_tenant_unique',
      });
      console.log('✅ Composite unique constraint added');
    }
    
    console.log('✅ Products/Issues unique constraints fixed successfully');
  } catch (error) {
    console.error('Error fixing products/issues constraints:', error);
    
    // Fallback: Try to add the composite constraint anyway
    try {
      await queryInterface.addConstraint('products_issues', {
        fields: ['name', 'tenant_id'],
        type: 'unique',
        name: 'products_issues_name_tenant_unique',
      });
      console.log('✅ Composite unique constraint added (fallback)');
    } catch (fallbackError) {
      console.log('Composite constraint may already exist, continuing...');
    }
  }
};

export const down = async (queryInterface, Sequelize) => {
  try {
    // Remove the composite unique constraint
    await queryInterface.removeConstraint('products_issues', 'products_issues_name_tenant_unique');
    
    // Add back the global unique constraint
    await queryInterface.addConstraint('products_issues', {
      fields: ['name'],
      type: 'unique',
      name: 'products_issues_name_unique',
    });
    
    console.log('✅ Reverted to global unique constraint on name');
  } catch (error) {
    console.error('Error reverting products/issues constraints:', error);
  }
};
