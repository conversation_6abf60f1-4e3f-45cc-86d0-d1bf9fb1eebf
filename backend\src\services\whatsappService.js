import axios from 'axios';
import { logger } from '../utils/logger.js';
import appConfig from '../../config/app.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WhatsAppService {
  constructor() {
    this.config = appConfig.whatsapp;
    this.isEnabled = this.config.enabled && this.config.accessToken && this.config.phoneNumberId;
    this.logFilePath = path.join(__dirname, '../../logs/whatsapp.log');

    // Template names (using OneClick API approved templates)
    this.templates = {
      service_created: 'oneclick_new_request',
      service_completed: 'oneclick_feedback_request',
      service_status_update: 'oneclick_new_request', // Reuse for status updates
      awareness: 'oneclick_missuse_awareness'
    };

    // Ensure logs directory exists
    const logsDir = path.dirname(this.logFilePath);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    if (this.isEnabled) {
      logger.info('WhatsApp service initialized successfully', {
        phoneNumberId: this.config.phoneNumberId,
        apiVersion: this.config.apiVersion,
        baseUrl: this.config.baseUrl,
        templates: this.templates
      });
    } else {
      logger.warn('WhatsApp service disabled - missing configuration', {
        hasAccessToken: !!this.config.accessToken,
        hasPhoneNumberId: !!this.config.phoneNumberId,
        enabled: this.config.enabled
      });
    }
  }

  /**
   * Log WhatsApp API attempts
   */
  logWhatsAppAttempt(messageType, phoneNumber, success, details = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      messageType,
      phoneNumber: phoneNumber ? `+91${phoneNumber}` : 'N/A',
      success,
      ...details
    };

    // Log to file
    try {
      const logLine = JSON.stringify(logEntry) + '\n';
      fs.appendFileSync(this.logFilePath, logLine);
    } catch (error) {
      logger.error('Failed to write WhatsApp log:', error);
    }

    // Log to console
    if (success) {
      logger.info('WhatsApp message sent successfully', logEntry);
    } else {
      logger.error('WhatsApp message failed', logEntry);
    }
  }

  /**
   * Format phone number for WhatsApp API
   */
  formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return null;
    
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // If already starts with 91, return as is
    if (cleaned.startsWith('91') && cleaned.length === 12) {
      return cleaned;
    }
    
    // If 10 digits, add 91 prefix
    if (cleaned.length === 10) {
      return '91' + cleaned;
    }
    
    // If 11 digits and starts with 0, remove 0 and add 91
    if (cleaned.length === 11 && cleaned.startsWith('0')) {
      return '91' + cleaned.substring(1);
    }
    
    return cleaned;
  }

  /**
   * Send WhatsApp template message using Facebook Graph API
   */
  async sendTemplateMessage(phoneNumber, templateName, parameters = []) {
    if (!this.isEnabled) {
      this.logWhatsAppAttempt('disabled', phoneNumber, false, {
        error: 'WhatsApp service disabled'
      });
      return {
        success: true,
        message: 'WhatsApp service disabled for development'
      };
    }

    const formattedPhone = this.formatPhoneNumber(phoneNumber);
    if (!formattedPhone) {
      this.logWhatsAppAttempt('invalid_phone', phoneNumber, false, {
        error: 'Invalid phone number format'
      });
      return {
        success: false,
        message: 'Invalid phone number format'
      };
    }

    try {
      const url = `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.phoneNumberId}/messages`;

      const payload = {
        messaging_product: 'whatsapp',
        to: formattedPhone,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'en'
          },
          components: this.buildTemplateComponents(templateName, parameters)
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      this.logWhatsAppAttempt('template_sent', phoneNumber, true, {
        templateName,
        messageId: response.data.messages?.[0]?.id,
        response: response.data
      });

      return {
        success: true,
        messageId: response.data.messages?.[0]?.id,
        message: 'WhatsApp template message sent successfully'
      };

    } catch (error) {
      const errorDetails = {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        templateName
      };

      this.logWhatsAppAttempt('template_failed', phoneNumber, false, errorDetails);

      return {
        success: false,
        message: 'Failed to send WhatsApp template message',
        error: errorDetails
      };
    }
  }

  /**
   * Build template components based on template name and parameters
   */
  buildTemplateComponents(templateName, parameters) {
    const components = [];

    // Add header with image for all templates
    components.push({
      type: 'header',
      parameters: [
        {
          type: 'image',
          image: {
            link: 'https://raw.githubusercontent.com/yuvarajrma/fakeapi/main/onclick_wa_logo.jpeg'
          }
        }
      ]
    });

    // Add body parameters for templates that need them
    if (templateName === 'oneclick_new_request' && parameters.length >= 2) {
      components.push({
        type: 'body',
        parameters: [
          {
            type: 'text',
            text: parameters[0] // Customer name
          },
          {
            type: 'text',
            text: parameters[1] // Service/Request ID
          }
        ]
      });

      // Add button parameter if provided (for dynamic links)
      if (parameters[2]) {
        components.push({
          type: 'button',
          sub_type: 'url',
          index: '0',
          parameters: [
            {
              type: 'text',
              text: parameters[2] // Dynamic link suffix
            }
          ]
        });
      }
    }

    if (templateName === 'oneclick_feedback_request' && parameters.length >= 1) {
      components.push({
        type: 'button',
        sub_type: 'url',
        index: '0',
        parameters: [
          {
            type: 'text',
            text: parameters[0] // Feedback link
          }
        ]
      });
    }

    return components;
  }

  /**
   * Send service creation notification using template
   */
  async sendServiceCreatedMessage(serviceData, customerData) {
    const phoneNumber = this.getCustomerPhoneNumber(customerData);
    if (!phoneNumber) {
      return {
        success: false,
        message: 'Customer phone number not available'
      };
    }

    // Ensure we have required parameters
    const customerName = customerData.company_name || customerData.name || 'Customer';
    const serviceNumber = serviceData.service_number || serviceData.id || 'SERVICE-001';

    logger.info('🔍 Service creation WhatsApp parameters:', {
      customerName,
      serviceNumber,
      phoneNumber,
      serviceDataKeys: Object.keys(serviceData),
      customerDataKeys: Object.keys(customerData)
    });

    // Use the exact OneClick API format
    return await this.sendOneClickNewRequestTemplate(
      phoneNumber,
      customerName,
      serviceNumber,
      'service-details'
    );
  }

  /**
   * Send service completion notification using OneClick feedback template
   */
  async sendServiceCompletedMessage(serviceData, customerData) {
    const phoneNumber = this.getCustomerPhoneNumber(customerData);
    if (!phoneNumber) {
      return {
        success: false,
        message: 'Customer phone number not available'
      };
    }

    // Use OneClick feedback request template for service completion
    return await this.sendOneClickFeedbackTemplate(
      phoneNumber,
      customerData.company_name || customerData.name,
      serviceData.service_number || serviceData.id,
      'feedback-link'
    );
  }

  /**
   * Send service status update notification using OneClick template
   */
  async sendServiceStatusUpdateMessage(serviceData, customerData, eventType) {
    const phoneNumber = this.getCustomerPhoneNumber(customerData);
    if (!phoneNumber) {
      return {
        success: false,
        message: 'Customer phone number not available'
      };
    }

    // Use OneClick new request template for status updates (reusing the working template)
    return await this.sendOneClickNewRequestTemplate(
      phoneNumber,
      customerData.company_name || customerData.name,
      serviceData.service_number || serviceData.id,
      `status-${eventType}`
    );
  }

  /**
   * Get customer phone number from various sources
   */
  getCustomerPhoneNumber(customerData) {
    // Priority order: mobile, phone, alternate_phone
    if (customerData.mobile) {
      return customerData.mobile;
    }
    
    if (customerData.phone) {
      return customerData.phone;
    }
    
    if (customerData.alternate_phone) {
      return customerData.alternate_phone;
    }

    // Check address book in custom_fields
    if (customerData.custom_fields?.address_book) {
      const addressBook = customerData.custom_fields.address_book;
      
      // Check each address book entry for phone/mobile
      for (const entry of addressBook) {
        if (entry.mobile) {
          return entry.mobile;
        }
        if (entry.phone) {
          return entry.phone;
        }
      }
    }

    return null;
  }

  /**
   * Send OneClick feedback request template (for service completion)
   */
  async sendOneClickFeedbackTemplate(phoneNumber, customerName, requestId, dynamicLinkSuffix = 'feedback-link') {
    if (!this.isEnabled) {
      return {
        success: true,
        message: 'WhatsApp service disabled for development'
      };
    }

    const formattedPhone = this.formatPhoneNumber(phoneNumber);
    if (!formattedPhone) {
      return {
        success: false,
        message: 'Invalid phone number format'
      };
    }

    // Validate and sanitize parameters
    const sanitizedCustomerName = customerName || 'Customer';
    const sanitizedRequestId = requestId || 'SERVICE-001';
    const sanitizedLinkSuffix = dynamicLinkSuffix || 'feedback-link';

    // Debug parameters
    logger.info('🔍 WhatsApp feedback template parameters:', {
      phoneNumber: formattedPhone,
      customerName: sanitizedCustomerName,
      requestId: sanitizedRequestId,
      linkSuffix: sanitizedLinkSuffix
    });

    try {
      const url = `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.phoneNumberId}/messages`;

      // Use OneClick feedback request template with button parameter
      const payload = {
        messaging_product: 'whatsapp',
        to: formattedPhone,
        type: 'template',
        template: {
          name: 'oneclick_feedback_request',
          language: {
            code: 'en'
          },
          components: [
            {
              type: 'header',
              parameters: [
                {
                  type: 'image',
                  image: {
                    link: 'https://raw.githubusercontent.com/yuvarajrma/fakeapi/main/onclick_wa_logo.jpeg'
                  }
                }
              ]
            },
            {
              type: 'button',
              sub_type: 'url',
              index: '0',
              parameters: [
                {
                  type: 'text',
                  text: sanitizedLinkSuffix
                }
              ]
            }
          ]
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      this.logWhatsAppAttempt('oneclick_feedback_sent', phoneNumber, true, {
        templateName: 'oneclick_feedback_request',
        messageId: response.data.messages?.[0]?.id,
        response: response.data
      });

      return {
        success: true,
        messageId: response.data.messages?.[0]?.id,
        message: 'WhatsApp feedback template sent successfully'
      };

    } catch (error) {
      const errorDetails = {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        templateName: 'oneclick_feedback_request'
      };

      this.logWhatsAppAttempt('oneclick_feedback_failed', phoneNumber, false, errorDetails);

      return {
        success: false,
        message: 'Failed to send WhatsApp feedback template',
        error: errorDetails
      };
    }
  }

  /**
   * Send OneClick new request template (exact working format)
   */
  async sendOneClickNewRequestTemplate(phoneNumber, customerName, requestId, dynamicLinkSuffix = 'service-details') {
    if (!this.isEnabled) {
      return {
        success: true,
        message: 'WhatsApp service disabled for development'
      };
    }

    const formattedPhone = this.formatPhoneNumber(phoneNumber);
    if (!formattedPhone) {
      return {
        success: false,
        message: 'Invalid phone number format'
      };
    }

    // Validate and sanitize parameters
    const sanitizedCustomerName = customerName || 'Customer';
    const sanitizedRequestId = requestId || 'SERVICE-001';
    const sanitizedLinkSuffix = dynamicLinkSuffix || 'service-details';

    // Debug parameters
    logger.info('🔍 WhatsApp template parameters:', {
      phoneNumber: formattedPhone,
      originalCustomerName: customerName,
      originalRequestId: requestId,
      sanitizedCustomerName,
      sanitizedRequestId,
      sanitizedLinkSuffix,
      customerNameType: typeof customerName,
      requestIdType: typeof requestId
    });

    try {
      const url = `${this.config.baseUrl}/${this.config.apiVersion}/${this.config.phoneNumberId}/messages`;

      // Use the exact working format from successful test
      const payload = {
        messaging_product: 'whatsapp',
        to: formattedPhone,
        type: 'template',
        template: {
          name: 'oneclick_new_request',
          language: {
            code: 'en'
          },
          components: [
            {
              type: 'header',
              parameters: [
                {
                  type: 'image',
                  image: {
                    link: 'https://raw.githubusercontent.com/yuvarajrma/fakeapi/main/onclick_wa_logo.jpeg'
                  }
                }
              ]
            },
            {
              type: 'body',
              parameters: [
                {
                  type: 'text',
                  text: sanitizedCustomerName
                },
                {
                  type: 'text',
                  text: sanitizedRequestId
                }
              ]
            },
            {
              type: 'button',
              sub_type: 'url',
              index: '0',
              parameters: [
                {
                  type: 'text',
                  text: sanitizedLinkSuffix
                }
              ]
            }
          ]
        }
      };

      const response = await axios.post(url, payload, {
        headers: {
          'Authorization': `Bearer ${this.config.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      this.logWhatsAppAttempt('oneclick_template_sent', phoneNumber, true, {
        templateName: 'oneclick_new_request',
        messageId: response.data.messages?.[0]?.id,
        response: response.data
      });

      return {
        success: true,
        messageId: response.data.messages?.[0]?.id,
        message: 'WhatsApp template message sent successfully'
      };

    } catch (error) {
      const errorDetails = {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data,
        templateName: 'oneclick_new_request'
      };

      this.logWhatsAppAttempt('oneclick_template_failed', phoneNumber, false, errorDetails);

      return {
        success: false,
        message: 'Failed to send WhatsApp template message',
        error: errorDetails
      };
    }
  }

  /**
   * Send test WhatsApp template message
   */
  async sendTestMessage(phoneNumber, testType = 'service_creation') {
    if (testType === 'service_creation') {
      return await this.sendOneClickNewRequestTemplate(phoneNumber, 'Test Customer', 'TEST-001', 'test-link');
    }

    const testTemplates = {
      service_completion: {
        template: this.templates.service_completed,
        parameters: ['feedback-test']
      },
      awareness: {
        template: this.templates.awareness,
        parameters: []
      }
    };

    const testConfig = testTemplates[testType];
    if (testConfig) {
      return await this.sendTemplateMessage(phoneNumber, testConfig.template, testConfig.parameters);
    }

    // Default to service creation
    return await this.sendOneClickNewRequestTemplate(phoneNumber, 'Test Customer', 'TEST-001', 'test-link');
  }
}

export default new WhatsAppService();
