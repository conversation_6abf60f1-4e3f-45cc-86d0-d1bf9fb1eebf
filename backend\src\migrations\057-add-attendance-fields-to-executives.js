import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  try {
    // Check if columns already exist before adding them
    const tableDescription = await queryInterface.describeTable('executives');

    // Add attendance_policy_id if it doesn't exist
    if (!tableDescription.attendance_policy_id) {
      await queryInterface.addColumn('executives', 'attendance_policy_id', {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'attendance_policies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'Reference to attendance policy for this employee',
      });
      console.log('✅ Added attendance_policy_id column to executives');
    }

    // Add shift_start_time if it doesn't exist
    if (!tableDescription.shift_start_time) {
      await queryInterface.addColumn('executives', 'shift_start_time', {
        type: DataTypes.TIME,
        allowNull: true,
        defaultValue: '09:00:00',
        comment: 'Employee specific shift start time',
      });
      console.log('✅ Added shift_start_time column to executives');
    }

    // Add shift_end_time if it doesn't exist
    if (!tableDescription.shift_end_time) {
      await queryInterface.addColumn('executives', 'shift_end_time', {
        type: DataTypes.TIME,
        allowNull: true,
        defaultValue: '18:00:00',
        comment: 'Employee specific shift end time',
      });
      console.log('✅ Added shift_end_time column to executives');
    }

    // Add weekly_off_days if it doesn't exist
    if (!tableDescription.weekly_off_days) {
      await queryInterface.addColumn('executives', 'weekly_off_days', {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: '["sunday"]',
        comment: 'Employee specific weekly off days',
      });
      console.log('✅ Added weekly_off_days column to executives');
    }

    // Add probation_period_months if it doesn't exist
    if (!tableDescription.probation_period_months) {
      await queryInterface.addColumn('executives', 'probation_period_months', {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 6,
        comment: 'Probation period in months',
      });
      console.log('✅ Added probation_period_months column to executives');
    }

    // Add confirmation_date if it doesn't exist
    if (!tableDescription.confirmation_date) {
      await queryInterface.addColumn('executives', 'confirmation_date', {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: 'Date when employee was confirmed after probation',
      });
      console.log('✅ Added confirmation_date column to executives');
    }

    // Add employee_status if it doesn't exist
    if (!tableDescription.employee_status) {
      await queryInterface.addColumn('executives', 'employee_status', {
        type: DataTypes.ENUM('probation', 'confirmed', 'notice_period', 'terminated'),
        allowNull: false,
        defaultValue: 'probation',
        comment: 'Current employment status',
      });
      console.log('✅ Added employee_status column to executives');
    }

    // Add notice_period_days if it doesn't exist
    if (!tableDescription.notice_period_days) {
      await queryInterface.addColumn('executives', 'notice_period_days', {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 30,
        comment: 'Notice period required in days',
      });
      console.log('✅ Added notice_period_days column to executives');
    }

    // Add last_working_date if it doesn't exist
    if (!tableDescription.last_working_date) {
      await queryInterface.addColumn('executives', 'last_working_date', {
        type: DataTypes.DATEONLY,
        allowNull: true,
        comment: 'Last working date for terminated employees',
      });
      console.log('✅ Added last_working_date column to executives');
    }

    // Add attendance_tracking_enabled if it doesn't exist
    if (!tableDescription.attendance_tracking_enabled) {
      await queryInterface.addColumn('executives', 'attendance_tracking_enabled', {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether attendance tracking is enabled for this employee',
      });
      console.log('✅ Added attendance_tracking_enabled column to executives');
    }

    // Add biometric_id if it doesn't exist
    if (!tableDescription.biometric_id) {
      await queryInterface.addColumn('executives', 'biometric_id', {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Biometric device ID for attendance',
      });
      console.log('✅ Added biometric_id column to executives');
    }

    // Add rfid_card_number if it doesn't exist
    if (!tableDescription.rfid_card_number) {
      await queryInterface.addColumn('executives', 'rfid_card_number', {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'RFID card number for attendance',
      });
      console.log('✅ Added rfid_card_number column to executives');
    }

    // Add indexes for new columns
    try {
      await queryInterface.addIndex('executives', ['attendance_policy_id'], {
        name: 'idx_executives_attendance_policy',
      });
      console.log('✅ Added index for attendance_policy_id');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    try {
      await queryInterface.addIndex('executives', ['employee_status'], {
        name: 'idx_executives_employee_status',
      });
      console.log('✅ Added index for employee_status');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    try {
      await queryInterface.addIndex('executives', ['attendance_tracking_enabled'], {
        name: 'idx_executives_attendance_tracking',
      });
      console.log('✅ Added index for attendance_tracking_enabled');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    try {
      await queryInterface.addIndex('executives', ['biometric_id'], {
        name: 'idx_executives_biometric_id',
      });
      console.log('✅ Added index for biometric_id');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    console.log('✅ Successfully added attendance-related fields to executives table');

  } catch (error) {
    console.error('❌ Error adding attendance fields to executives:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  try {
    // Remove indexes first
    await queryInterface.removeIndex('executives', 'idx_executives_attendance_policy');
    await queryInterface.removeIndex('executives', 'idx_executives_employee_status');
    await queryInterface.removeIndex('executives', 'idx_executives_attendance_tracking');
    await queryInterface.removeIndex('executives', 'idx_executives_biometric_id');

    // Remove columns
    await queryInterface.removeColumn('executives', 'attendance_policy_id');
    await queryInterface.removeColumn('executives', 'shift_start_time');
    await queryInterface.removeColumn('executives', 'shift_end_time');
    await queryInterface.removeColumn('executives', 'weekly_off_days');
    await queryInterface.removeColumn('executives', 'probation_period_months');
    await queryInterface.removeColumn('executives', 'confirmation_date');
    await queryInterface.removeColumn('executives', 'employee_status');
    await queryInterface.removeColumn('executives', 'notice_period_days');
    await queryInterface.removeColumn('executives', 'last_working_date');
    await queryInterface.removeColumn('executives', 'attendance_tracking_enabled');
    await queryInterface.removeColumn('executives', 'biometric_id');
    await queryInterface.removeColumn('executives', 'rfid_card_number');

    console.log('✅ Removed attendance-related fields from executives table');
  } catch (error) {
    console.error('❌ Error removing attendance fields from executives:', error);
    throw error;
  }
};
