/* TallyCRM App Specific Styles */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--theme-content-bg);
}

/* Layout Styles - Tailwind Compatible */
.sidebar {
  @apply w-[220px] transition-all duration-300 ease-in-out fixed top-0 left-0 h-screen overflow-y-auto;
  background: var(--sidebar-background, #1d5795);
  color: var(--primary-text, #ffffff);
  z-index: var(--z-modal);
}

.sidebar.collapsed {
  @apply w-16;
}

.header {
  @apply h-16 border-b border-gray-200 flex items-center px-6 sticky top-0 z-40;
  background-color: var(--theme-card-bg);
}

.footer {
  @apply h-16 border-t border-gray-200 flex items-center justify-center px-6 mt-auto;
  background-color: var(--theme-card-bg);
}

/* Auth Layout */
.auth-layout {
  @apply min-h-screen flex items-center justify-center p-8;
  background: var(--auth-background, #1d5795);
}

/* Navigation Links with Dynamic Text Color */
.nav-link {
  @apply flex items-center px-4 py-3 mx-2 my-1 rounded-lg transition-all duration-200 no-underline;
  color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.8);
}

.nav-link:hover {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.1);
}

.nav-link.active {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Primary Button Styles with Dynamic Text Color */
.btn-primary {
  background: var(--button-background, #1d5795);
  border-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}

.btn-primary:hover {
  background: var(--primary-dark, #6b21a8);
  border-color: var(--primary-dark, #6b21a8);
  color: var(--primary-text, #ffffff);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary, 0 0.5rem 1rem rgba(124, 58, 237, 0.15));
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.25);
  color: var(--primary-text, #ffffff);
}

/* Outline Primary Button */
.btn-outline-primary {
  color: var(--primary-color, #1d5795);
  border-color: var(--primary-color, #1d5795);
}

.btn-outline-primary:hover {
  background: var(--primary-color, #1d5795);
  border-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary, 0 0.5rem 1rem rgba(124, 58, 237, 0.15));
}

.btn-outline-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.25);
}

/* Ghost Button */
.btn-ghost {
  color: var(--primary-color, #1d5795);
}

.btn-ghost:hover {
  background: rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  color: var(--primary-dark, #6b21a8);
}

/* Link Button */
.btn-link {
  color: var(--primary-color, #1d5795);
}

.btn-link:hover {
  color: var(--primary-dark, #6b21a8);
}

/* Settings Page Specific Styles with Dynamic Text Color */
.stats-card-primary {
  background: var(--sidebar-background, #1d5795);
  color: var(--primary-text, #ffffff);
}

.header-gradient {
  background: var(--sidebar-background, #1d5795);
  color: var(--primary-text, #ffffff);
}

.settings-nav-active {
  background: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
  border-right: 4px solid var(--primary-dark, #6b21a8);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-nav-active .h-5 {
  color: var(--primary-text, #ffffff);
}

/* Spinner Styles */
.spinner-primary {
  border-color: var(--primary-color, #1d5795);
}

.spinner-primary-text {
  color: var(--primary-color, #1d5795);
}

/* Alert Styles */
.alert-primary {
  background: var(--theme-alert-bg);
  border-color: var(--theme-icon-color);
  color: var(--primary-dark, #6b21a8);
}

/* Dashboard Icon Styles - Now uses theme colors consistently */
.dashboard-icon-bg {
  background: var(--theme-icon-bg);
}

.dashboard-icon-text {
  color: var(--theme-icon-color);
}

/* Theme-consistent icon styles for all backgrounds */
.theme-icon-bg {
  background: var(--theme-icon-bg);
}

.theme-icon-bg:hover {
  background: var(--theme-icon-bg-hover);
}

.theme-icon-text {
  color: var(--theme-icon-color);
}

/* Legacy classes updated to use theme colors */
.white-stats-icon-bg {
  background: var(--theme-icon-bg);
}

.white-stats-icon-text {
  color: var(--theme-icon-color);
}

/* Toggle switch theme styles */
.toggle-switch {
  background-color: #e5e7eb;
}

.toggle-switch:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3);
}

.toggle-switch.checked {
  background-color: var(--theme-icon-color);
}

/* Form focus styles using theme colors */
.focus-primary:focus {
  outline: none;
  border-color: var(--theme-icon-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Theme-consistent form inputs */
.form-input-theme {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s;
}

.form-input-theme:focus {
  outline: none;
  border-color: var(--theme-icon-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Global override for all form inputs to use theme colors */
input:focus,
select:focus,
textarea:focus {
  outline: none !important;
  border-color: var(--theme-icon-color) !important;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1) !important;
}

/* Toggle switch theme styles */
.peer:checked ~ .toggle-bg {
  background-color: var(--theme-icon-color) !important;
}

.peer:focus ~ .toggle-bg {
  box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.3) !important;
}

/* Theme-consistent background classes */
.theme-content-bg {
  background-color: var(--theme-content-bg);
}

.theme-modal-bg {
  background-color: var(--theme-modal-bg);
}

.theme-card-bg {
  background-color: var(--theme-card-bg);
}

/* Override default white backgrounds with theme colors - more specific selectors */
.main-content .bg-white,
.modal .bg-white,
.card.bg-white {
  background-color: var(--theme-card-bg) !important;
}

/* Modal and dialog theme backgrounds */
.modal-content {
  background-color: var(--theme-modal-bg);
}

.dialog-content {
  background-color: var(--theme-modal-bg);
}

/* Badge Styles */
.badge-primary {
  background: var(--theme-badge-bg);
  color: var(--theme-icon-color);
}

/* Focus Ring Colors */
.focus-primary:focus {
  @apply outline-none;
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.1);
  border-color: var(--primary-color, #1d5795);
}

/* Link Colors */
.link-primary {
  color: var(--primary-color, #1d5795);
}

.link-primary:hover {
  color: var(--primary-dark, #6b21a8);
}

/* Dynamic Text Color Classes */
.text-primary-dynamic {
  color: var(--primary-text, #ffffff);
}

.bg-primary-dynamic {
  background-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}

.border-primary-dynamic {
  border-color: var(--primary-color, #1d5795);
}

/* Form Theme Classes */
.form-label-primary {
  color: var(--primary-color, #1d5795);
}

.form-input-primary {
  border-color: rgba(var(--primary-rgb, 124, 58, 237), 0.3);
  transition: all 0.2s ease-in-out;
}

.form-input-primary:focus {
  border-color: var(--primary-color, #1d5795);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.1);
}

.form-input-primary:hover {
  border-color: rgba(var(--primary-rgb, 124, 58, 237), 0.5);
}

.form-section-header {
  background-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}

.form-section-border {
  border-color: rgba(var(--primary-rgb, 124, 58, 237), 0.2);
}

.form-section-background {
  background-color: rgba(var(--primary-rgb, 124, 58, 237), 0.02);
}

/* Toggle/Switch Theme Classes */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: var(--primary-color, #1d5795);
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-switch input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Checkbox Theme Classes */
.checkbox-primary {
  accent-color: var(--primary-color, #1d5795);
}

.checkbox-primary:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 124, 58, 237), 0.1);
}

/* Button Theme Classes */
.btn-theme-primary {
  background-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
  border-color: var(--primary-color, #1d5795);
}

.btn-theme-primary:hover {
  background-color: rgba(var(--primary-rgb, 124, 58, 237), 0.9);
  border-color: rgba(var(--primary-rgb, 124, 58, 237), 0.9);
}

.btn-theme-outline {
  background-color: transparent;
  color: var(--primary-color, #1d5795);
  border-color: var(--primary-color, #1d5795);
}

.btn-theme-outline:hover {
  background-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}



/* Stats Card Theme Classes */
.stats-card-primary {
  background-color: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}

/* Dynamic Tailwind Color Utilities */
.bg-theme-50 { background-color: rgb(var(--theme-50) / 1); }
.bg-theme-100 { background-color: rgb(var(--theme-100) / 1); }
.bg-theme-200 { background-color: rgb(var(--theme-200) / 1); }
.bg-theme-300 { background-color: rgb(var(--theme-300) / 1); }
.bg-theme-400 { background-color: rgb(var(--theme-400) / 1); }
.bg-theme-500 { background-color: rgb(var(--theme-500) / 1); }
.bg-theme-600 { background-color: rgb(var(--theme-600) / 1); }
.bg-theme-700 { background-color: rgb(var(--theme-700) / 1); }
.bg-theme-800 { background-color: rgb(var(--theme-800) / 1); }
.bg-theme-900 { background-color: rgb(var(--theme-900) / 1); }

.text-theme-50 { color: rgb(var(--theme-50) / 1); }
.text-theme-100 { color: rgb(var(--theme-100) / 1); }
.text-theme-200 { color: rgb(var(--theme-200) / 1); }
.text-theme-300 { color: rgb(var(--theme-300) / 1); }
.text-theme-400 { color: rgb(var(--theme-400) / 1); }
.text-theme-500 { color: rgb(var(--theme-500) / 1); }
.text-theme-600 { color: rgb(var(--theme-600) / 1); }
.text-theme-700 { color: rgb(var(--theme-700) / 1); }
.text-theme-800 { color: rgb(var(--theme-800) / 1); }
.text-theme-900 { color: rgb(var(--theme-900) / 1); }

.border-theme-50 { border-color: rgb(var(--theme-50) / 1); }
.border-theme-100 { border-color: rgb(var(--theme-100) / 1); }
.border-theme-200 { border-color: rgb(var(--theme-200) / 1); }
.border-theme-300 { border-color: rgb(var(--theme-300) / 1); }
.border-theme-400 { border-color: rgb(var(--theme-400) / 1); }
.border-theme-500 { border-color: rgb(var(--theme-500) / 1); }
.border-theme-600 { border-color: rgb(var(--theme-600) / 1); }
.border-theme-700 { border-color: rgb(var(--theme-700) / 1); }
.border-theme-800 { border-color: rgb(var(--theme-800) / 1); }
.border-theme-900 { border-color: rgb(var(--theme-900) / 1); }

.divide-theme-200 > :not([hidden]) ~ :not([hidden]) { border-color: rgb(var(--theme-200) / 1); }

/* Navigation with Dynamic Text */
.nav-primary {
  background: var(--primary-color, #1d5795);
  color: var(--primary-text, #ffffff);
}

.nav-primary .nav-link {
  color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.8);
}

.nav-primary .nav-link:hover {
  color: var(--primary-text, #ffffff);
}

.nav-primary .nav-link.active {
  color: var(--primary-text, #ffffff);
  background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
}

.nav-link i {
  @apply w-5 text-center mr-3;
}

.sidebar.collapsed .nav-link span {
  @apply hidden;
}

.sidebar.collapsed .nav-link {
  @apply justify-center mx-2 px-3;
}

.sidebar.collapsed .nav-link i {
  @apply mr-0;
}

.auth-card {
  @apply w-full max-w-md bg-white rounded-lg shadow-lg p-8;
}

.auth-logo {
  @apply text-center mb-8;
}

/* Navigation Styles */
.nav-link {
  @apply text-white text-opacity-80 py-3 px-4 flex items-center no-underline transition-all duration-300 rounded mx-2 my-1;
}

.nav-link:hover {
  @apply text-white bg-white bg-opacity-10 no-underline;
}

.nav-link.active {
  @apply text-white bg-purple-600;
}

.nav-link i {
  @apply mr-3 w-5 text-center;
}

.sidebar.collapsed .nav-link span {
  @apply hidden;
}

.sidebar.collapsed .nav-link {
  @apply justify-center;
}

.sidebar.collapsed .nav-link i {
  @apply mr-0;
}

/* Dashboard Styles */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--brand-primary);
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.stat-card .stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--brand-dark);
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: var(--muted);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Table Styles */
.table-responsive {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  background: white;
}

.table th {
  background-color: var(--bg-secondary);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

/* Form Styles */
.form-floating > label {
  color: var(--muted);
}

.form-control:focus + label {
  color: var(--brand-primary);
}

/* Button Styles - Using theme-aware styles defined above */

.btn-tally {
  background-color: var(--tally-red);
  border-color: var(--tally-red);
  color: white;
}

.btn-tally:hover {
  background-color: #c41e24;
  border-color: #c41e24;
  color: white;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

/* Modal Centering Utilities */
.modal-center {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4;
}

.modal-content-center {
  @apply bg-white rounded-lg shadow-xl mx-auto;
  max-width: calc(100vw - 2rem);
  max-height: calc(100vh - 2rem);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Loading spinner centering utilities */
.loading-center {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-center-full {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.loading-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

/* Global loading spinner centering */
.animate-spin {
  display: inline-block;
}

/* Ensure loading containers are centered */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
}

/* Center loading text and spinners */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loading-center-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

/* Error States */
.error-boundary {
  padding: 2rem;
  text-align: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  margin: 2rem;
}

.error-boundary h2 {
  color: var(--brand-danger);
  margin-bottom: 1rem;
}

.error-boundary p {
  color: var(--muted);
  margin-bottom: 1.5rem;
}

/* Timeline Styles */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0.75rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e7eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-marker {
  position: absolute;
  left: -2.25rem;
  top: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #e5e7eb;
}

.timeline-content {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full transition-transform duration-300 ease-in-out;
  }

  .sidebar.show {
    @apply translate-x-0;
  }

  .dashboard-stats {
    @apply grid-cols-1;
  }

  .auth-card {
    @apply m-4 p-6;
  }

  /* Mobile-specific overflow fixes */
  .table-responsive {
    @apply overflow-x-auto;
  }

  /* Ensure dropdowns don't overflow on mobile */
  .dropdown-menu {
    max-width: calc(100vw - 2rem);
    left: 0 !important;
    right: auto !important;
  }

  /* Fix button text overflow on mobile */
  .btn {
    @apply text-sm px-3 py-2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Ensure form inputs don't overflow */
  .form-control,
  input,
  select,
  textarea {
    min-width: 0;
    max-width: 100%;
  }

  /* Fix modal positioning on mobile */
  .modal {
    @apply p-4;
  }

  .modal-content {
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
    @apply overflow-y-auto;
  }

  /* Ensure all modals are properly centered */
  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4;
  }

  .modal-container {
    @apply bg-white rounded-lg shadow-xl mx-auto;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
}

/* Additional responsive utilities */
@media (max-width: 640px) {
  /* Force card view on very small screens */
  .force-card-mobile {
    display: block !important;
  }

  .hide-mobile {
    display: none !important;
  }

  /* Smaller padding on mobile */
  .responsive-padding {
    @apply p-2;
  }

  /* Responsive text sizes */
  .responsive-text {
    @apply text-sm;
  }

  .responsive-text-lg {
    @apply text-base;
  }

  .responsive-text-xl {
    @apply text-lg;
  }

  /* Mobile dashboard stats cards optimization */
  .dashboard-stat-card {
    min-height: 120px;
  }

  .dashboard-stat-card .card-body {
    padding: 1rem;
    min-height: 120px;
  }

  /* Mobile text sizing for stats cards */
  .dashboard-stat-card h3 {
    font-size: 1.25rem;
    line-height: 1.2;
    margin-bottom: 0.75rem;
  }

  .dashboard-stat-card .text-xs.sm\\:text-sm {
    font-size: 0.75rem;
  }

  /* Mobile icon sizing */
  .dashboard-stat-card .rounded-lg.p-2.sm\\:p-3 {
    padding: 0.5rem;
  }

  /* Mobile badge styling */
  .dashboard-stat-card .flex.items-center.px-2.py-1.rounded-full {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  /* Mobile button improvements */
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  /* Mobile Quick Actions optimization */
  .grid.grid-cols-1.sm\\:grid-cols-2 {
    gap: 0.75rem;
  }

  /* Mobile table text sizing */
  .table.text-xs.sm\\:text-sm {
    font-size: 0.75rem;
  }

  /* Mobile table cell padding */
  .table td,
  .table th {
    padding: 0.5rem 0.75rem;
  }

  /* Ensure proper spacing on mobile */
  .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* Mobile-specific table adjustments */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-padding {
    @apply p-4;
  }

  .responsive-text {
    @apply text-base;
  }

  .responsive-text-lg {
    @apply text-lg;
  }

  .responsive-text-xl {
    @apply text-xl;
  }

  /* Dashboard stats card improvements for tablet/laptop view */
  .dashboard-stat-card .card-body {
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  /* Ensure proper spacing in dashboard cards */
  .dashboard-stat-card {
    min-height: 140px;
  }

  /* Fix overdue services card layout on tablet/laptop */
  .dashboard-stat-card .flex.flex-col.sm\\:flex-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .dashboard-stat-card .flex.flex-col.sm\\:flex-row .flex-shrink-0 {
    align-self: flex-end;
  }
}

/* Laptop specific adjustments (1025px - 1366px) */
@media (min-width: 1025px) and (max-width: 1366px) {
  .responsive-padding {
    @apply p-5;
  }

  .responsive-text {
    @apply text-base;
  }

  .responsive-text-lg {
    @apply text-lg;
  }

  .responsive-text-xl {
    @apply text-xl;
  }

  /* Dashboard stats cards optimization for laptop screens */
  .dashboard-stat-card {
    min-height: 180px;
    transition: all 0.3s ease;
  }

  .dashboard-stat-card .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 180px;
  }

  /* Better spacing for top section */
  .dashboard-stat-card .flex.items-start.justify-between {
    margin-bottom: 1rem;
  }

  /* Improved icon container sizing */
  .dashboard-stat-card .rounded-lg.p-2.sm\\:p-3 {
    padding: 0.875rem;
  }

  /* Better text sizing for laptop screens */
  .dashboard-stat-card h3 {
    font-size: 1.75rem;
    line-height: 1.2;
    font-weight: 700;
    margin-bottom: 1.25rem;
  }

  /* Optimize title text sizing */
  .dashboard-stat-card .text-xs.sm\\:text-sm {
    font-size: 0.8125rem;
    font-weight: 600;
    letter-spacing: 0.05em;
  }

  /* Improved bottom section layout for laptop */
  .dashboard-stat-card .flex.flex-col.sm\\:flex-row {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    gap: 1rem;
  }

  /* Better badge styling */
  .dashboard-stat-card .flex.items-center.px-2.py-1.rounded-full {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
  }

  /* Ensure proper text alignment in overdue services card */
  .dashboard-stat-card .flex-shrink-0 p {
    text-align: right;
    white-space: nowrap;
    font-size: 0.75rem;
  }

  /* Improve hover effects for laptop */
  .dashboard-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Optimize filter section for laptop screens */
  .grid.grid-cols-2.sm\\:grid-cols-4 {
    gap: 0.75rem;
  }

  /* Reduce form element heights for laptop */
  input[type="text"],
  select {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  /* Optimize label spacing for laptop */
  label {
    margin-bottom: 0.375rem;
  }
}

/* Desktop responsive adjustments (larger than 1366px) */
@media (min-width: 1367px) {
  .responsive-padding {
    @apply p-6;
  }

  .responsive-text {
    @apply text-base;
  }

  .responsive-text-lg {
    @apply text-lg;
  }

  .responsive-text-xl {
    @apply text-xl;
  }
}

/* Utility classes for overflow prevention */
.prevent-overflow {
  min-width: 0;
  overflow: hidden;
}

.text-truncate-responsive {
  @apply truncate;
  max-width: 100%;
}

.flex-responsive {
  @apply flex flex-wrap;
}

.grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Service Analytics specific styles */
.analytics-card {
  transition: all 0.3s ease;
  height: 120px;
  min-height: 120px;
}

.analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.analytics-section-header {
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* Mobile optimizations for analytics */
@media (max-width: 640px) {
  .analytics-card {
    height: 100px;
    min-height: 100px;
    padding: 0.75rem;
  }

  .analytics-card h4 {
    font-size: 0.875rem;
  }

  .analytics-card .metric-value {
    font-size: 1.25rem;
  }

  .analytics-section-header h3 {
    font-size: 1rem;
  }
}

/* Enhanced Service Reports Styles */
.service-reports-enhanced {
  /* Collapsible group styles */
  .group-header {
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
  }

  .group-header:hover {
    background-color: #f9fafb;
    border-left-color: #3b82f6;
  }

  .group-header.expanded {
    border-left-color: #3b82f6;
    background-color: #f8fafc;
  }

  /* Enhanced table row styles */
  .enhanced-table-row {
    transition: all 0.15s ease;
    border-left: 3px solid transparent;
  }

  .enhanced-table-row:hover {
    background-color: #f8fafc;
    border-left-color: #e5e7eb;
    transform: translateX(2px);
  }

  .enhanced-table-row.even {
    background-color: #fafafa;
  }

  .enhanced-table-row.odd {
    background-color: #ffffff;
  }

  /* Status and priority indicators */
  .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  .priority-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
  }

  /* Expanded details styles */
  .expanded-details {
    background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
    border-left: 4px solid #3b82f6;
    animation: slideDown 0.2s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
    }
    to {
      opacity: 1;
      max-height: 500px;
      padding-top: 1rem;
      padding-bottom: 1rem;
    }
  }

  /* Interactive elements */
  .interactive-row {
    cursor: pointer;
    position: relative;
  }

  .interactive-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: transparent;
    transition: background-color 0.2s ease;
  }

  .interactive-row:hover::before {
    background-color: #3b82f6;
  }

  /* Group badge styles */
  .group-badge {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #93c5fd;
  }

  /* Empty state enhancements */
  .enhanced-empty-state {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
  }
}

/* Dashboard stats card specific improvements */
.dashboard-stat-card {
  position: relative;
  overflow: hidden;
}

.dashboard-stat-card .card-body {
  position: relative;
  z-index: 1;
}

/* Ensure consistent spacing and alignment */
.dashboard-stat-card .flex.items-center.min-w-0.flex-1 {
  overflow: hidden;
}

.dashboard-stat-card .min-w-0.flex-1 p:not(.no-truncate) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Allow text wrapping for overdue services card */
.dashboard-stat-card .min-w-0.flex-1 p.no-truncate {
  overflow: visible;
  text-overflow: unset;
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.3;
}

/* Ensure overdue services card has adequate width */
.dashboard-stat-card:has(.no-truncate) {
  min-width: 280px;
}

@media (max-width: 640px) {
  .dashboard-stat-card:has(.no-truncate) {
    min-width: 250px;
  }
}

/* Fix bottom section alignment issues */
.dashboard-stat-card .flex.flex-col.sm\\:flex-row.sm\\:items-center.sm\\:justify-between {
  align-items: flex-start;
  gap: 0.5rem;
}

@media (min-width: 640px) {
  .dashboard-stat-card .flex.flex-col.sm\\:flex-row.sm\\:items-center.sm\\:justify-between {
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    gap: 0.75rem;
  }
}

/* Button responsive improvements */
.btn-responsive {
  @apply px-3 py-2 text-sm;
  min-width: 0;
  white-space: nowrap;
}

@media (min-width: 640px) {
  .btn-responsive {
    @apply px-4 py-2 text-base;
  }
}

/* Table responsive improvements */
.table-container {
  @apply overflow-x-auto;
  max-width: 100%;
}

.table-container table {
  min-width: 600px;
}

@media (max-width: 640px) {
  .table-container table {
    min-width: 500px;
  }
}

/* Header dropdown fixes */
header {
  overflow: visible !important;
}

.header-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
  overflow: visible !important;
}

/* Ensure dropdowns appear above all content */
.dropdown-menu,
.user-menu-dropdown {
  position: absolute;
  z-index: 9999;
  overflow: visible;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
