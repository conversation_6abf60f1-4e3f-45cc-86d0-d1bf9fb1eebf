import models from '../src/models/index.js';

async function checkOverdueServices() {
  try {
    console.log('🔍 Checking for overdue services...');

    // Get all service calls with scheduled dates
    const allServices = await models.ServiceCall.findAll({
      include: [
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'category', 'code']
        },
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'contact_person']
        }
      ],
      order: [['scheduled_date', 'ASC']]
    });

    console.log(`📊 Total service calls: ${allServices.length}`);

    const today = new Date();
    console.log(`📅 Today: ${today.toISOString()}`);

    // Check each service
    allServices.forEach((service, index) => {
      const scheduledDate = service.scheduled_date ? new Date(service.scheduled_date) : null;
      const isOverdue = scheduledDate && scheduledDate < today && service.status?.category === 'open';
      const daysOverdue = scheduledDate ? Math.ceil((today - scheduledDate) / (1000 * 60 * 60 * 24)) : 0;

      console.log(`\n${index + 1}. ${service.call_number || 'No Number'}`);
      console.log(`   Customer: ${service.customer?.company_name || service.customer?.contact_person || 'Unknown'}`);
      console.log(`   Scheduled: ${scheduledDate ? scheduledDate.toISOString() : 'Not scheduled'}`);
      console.log(`   Status: ${service.status?.name || 'No status'} (${service.status?.category || 'No category'})`);
      console.log(`   Is Overdue: ${isOverdue ? `YES (${daysOverdue} days)` : 'NO'}`);
    });

    // Count overdue services
    const overdueServices = allServices.filter(service => {
      const scheduledDate = service.scheduled_date ? new Date(service.scheduled_date) : null;
      return scheduledDate && scheduledDate < today && service.status?.category === 'open';
    });

    console.log(`\n📈 Summary:`);
    console.log(`   Total services: ${allServices.length}`);
    console.log(`   Overdue services: ${overdueServices.length}`);
    console.log(`   Services with scheduled dates: ${allServices.filter(s => s.scheduled_date).length}`);
    console.log(`   Services with open status: ${allServices.filter(s => s.status?.category === 'open').length}`);

    // Check call statuses
    const statuses = await models.CallStatus.findAll({
      attributes: ['id', 'name', 'category', 'code', 'is_active'],
      order: [['name', 'ASC']]
    });

    console.log(`\n📋 Available statuses:`);
    statuses.forEach(status => {
      console.log(`   ${status.name} (${status.category}) - Active: ${status.is_active}`);
    });

  } catch (error) {
    console.error('❌ Error checking overdue services:', error);
  } finally {
    process.exit(0);
  }
}

checkOverdueServices();
