#!/usr/bin/env node

/**
 * Create Sample Email Log
 * This script creates sample email log entries to demonstrate the logging system
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function createSampleEmailLog() {
  console.log('📝 Creating Sample Email Log...\n');

  const logPath = path.join(__dirname, 'logs/email-send-status.log');
  
  // Ensure logs directory exists
  const logDir = path.dirname(logPath);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
    console.log('📁 Created logs directory');
  }

  // Function to create log entry
  function createLogEntry(type, recipient, subject, success, details = {}) {
    const timestamp = new Date().toLocaleString('en-IN', { 
      timeZone: 'Asia/Kolkata',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    const status = success ? '✅ SUCCESS' : '❌ FAILED';
    const serviceNumber = details.serviceNumber || 'N/A';
    const messageId = details.messageId || 'N/A';
    const errorMsg = details.error || '';

    let logLine = `[${timestamp}] ${status} | ${type.toUpperCase()}\n`;
    logLine += `  📧 To: ${recipient}\n`;
    logLine += `  📋 Subject: ${subject}\n`;
    logLine += `  🔢 Service: ${serviceNumber}\n`;
    
    if (success) {
      logLine += `  📨 Message ID: ${messageId}\n`;
      logLine += `  📤 Email Source: ${details.emailSource || 'main_email'}\n`;
    } else {
      logLine += `  ❌ Error: ${errorMsg}\n`;
    }
    
    logLine += `  ${'='.repeat(80)}\n\n`;

    return logLine;
  }

  // Clear existing log
  fs.writeFileSync(logPath, '');

  // Create sample log entries
  const sampleEntries = [
    {
      type: 'service_notification_service_created',
      recipient: '<EMAIL>',
      subject: 'Service Request Created - SRV-001',
      success: true,
      details: {
        serviceNumber: 'SRV-001',
        messageId: '<<EMAIL>>',
        emailSource: 'address_book'
      }
    },
    {
      type: 'service_notification_status_change',
      recipient: '<EMAIL>',
      subject: 'Service Status Updated - SRV-002',
      success: true,
      details: {
        serviceNumber: 'SRV-002',
        messageId: '<<EMAIL>>',
        emailSource: 'main_email'
      }
    },
    {
      type: 'service_completion',
      recipient: '<EMAIL>',
      subject: 'Service Completed - SRV-001',
      success: true,
      details: {
        serviceNumber: 'SRV-001',
        messageId: '<<EMAIL>>',
        emailSource: 'custom_fields'
      }
    },
    {
      type: 'service_notification_service_created',
      recipient: '<EMAIL>',
      subject: 'Service Request Created - SRV-003',
      success: false,
      details: {
        serviceNumber: 'SRV-003',
        error: 'SMTP Error: Invalid recipient email address',
        emailSource: 'main_email'
      }
    },
    {
      type: 'test_email',
      recipient: '<EMAIL>',
      subject: 'TallyCRM Email Test',
      success: true,
      details: {
        serviceNumber: 'TEST-001',
        messageId: '<<EMAIL>>',
        emailSource: 'address_book'
      }
    }
  ];

  // Add some delay between entries to show different timestamps
  sampleEntries.forEach((entry, index) => {
    // Simulate different times
    const date = new Date();
    date.setMinutes(date.getMinutes() - (sampleEntries.length - index) * 5);
    
    const logEntry = createLogEntry(
      entry.type,
      entry.recipient,
      entry.subject,
      entry.success,
      entry.details
    );
    
    fs.appendFileSync(logPath, logEntry);
    console.log(`✅ Added log entry ${index + 1}: ${entry.success ? 'SUCCESS' : 'FAILED'} - ${entry.type}`);
  });

  console.log('\n📋 Sample Email Send Status Log Created!');
  console.log(`📁 Location: ${logPath}`);
  
  // Display the log contents
  console.log('\n📄 Log Contents:');
  console.log('='.repeat(100));
  const logContent = fs.readFileSync(logPath, 'utf8');
  console.log(logContent);
  
  console.log('🎉 Sample email log creation completed!');
  console.log('\n📊 Summary:');
  console.log('✅ 5 sample log entries created');
  console.log('✅ 4 successful email logs');
  console.log('✅ 1 failed email log');
  console.log('✅ Different email types demonstrated');
  console.log('✅ Various email sources shown');
  
  console.log('\n🔍 API Endpoints to Test:');
  console.log('📊 GET /api/v1/service-calls/email-send-status?lines=10');
  console.log('📈 GET /api/v1/service-calls/email-statistics');
  console.log('📋 GET /api/v1/service-calls/email-logs');
  
  console.log('\n📝 Log File Features:');
  console.log('✅ Human-readable format');
  console.log('✅ Indian timezone timestamps');
  console.log('✅ Success/failure status indicators');
  console.log('✅ Service number tracking');
  console.log('✅ Email source identification');
  console.log('✅ Error message logging');
  console.log('✅ Message ID tracking');
}

// Run the script
createSampleEmailLog();
