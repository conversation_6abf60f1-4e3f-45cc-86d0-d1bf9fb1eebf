import React, { useState, useEffect } from 'react';
import { He<PERSON><PERSON> } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  billingService,
  formatCurrency,
  formatDate,
  getInvoiceStatusColor
} from '../../services/subscriptionService';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Spinner } from '../../components/ui/Spinner';

const BillingHistory = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  useEffect(() => {
    fetchInvoices();
  }, [pagination.page]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const response = await billingService.getBillingHistory(pagination.page, pagination.limit);
      setInvoices(response.data.invoices);
      setPagination(prev => ({
        ...prev,
        ...response.data.pagination,
      }));
    } catch (error) {
      console.error('Error fetching billing history:', error);
      toast.error('Failed to load billing history');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadInvoice = async (invoiceId) => {
    try {
      await billingService.downloadInvoice(invoiceId);
      toast.success('Invoice downloaded successfully');
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error('Failed to download invoice');
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (loading && pagination.page === 1) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Billing History - TallyCRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Billing History</h1>
            <p className="text-gray-600">View and download your invoices</p>
          </div>
          <Link to="/billing/dashboard">
            <Button variant="outline">
              <i className="bi bi-arrow-left mr-2"></i>
              Back to Dashboard
            </Button>
          </Link>
        </div>

        {/* Invoices List */}
        <Card>
          <div className="p-6">
            {invoices.length === 0 ? (
              <div className="text-center py-12">
                <i className="bi bi-receipt text-6xl text-gray-400 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Invoices Found</h3>
                <p className="text-gray-600">
                  You don't have any invoices yet. Invoices will appear here after your first billing cycle.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="hidden md:grid md:grid-cols-6 gap-4 pb-3 border-b border-gray-200 text-sm font-medium text-gray-600">
                  <div>Invoice</div>
                  <div>Date</div>
                  <div>Period</div>
                  <div>Amount</div>
                  <div>Status</div>
                  <div>Actions</div>
                </div>

                {/* Invoice Rows */}
                {invoices.map((invoice) => (
                  <div
                    key={invoice.id}
                    className="grid grid-cols-1 md:grid-cols-6 gap-4 py-4 border-b border-gray-100 last:border-b-0"
                  >
                    {/* Invoice Number */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Invoice</div>
                      <div className="font-medium text-gray-900">{invoice.invoice_number}</div>
                    </div>

                    {/* Date */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Date</div>
                      <div className="text-gray-900">{formatDate(invoice.created_at)}</div>
                    </div>

                    {/* Period */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Period</div>
                      <div className="text-gray-900 text-sm">
                        {formatDate(invoice.period_start)} - {formatDate(invoice.period_end)}
                      </div>
                    </div>

                    {/* Amount */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Amount</div>
                      <div className="font-semibold text-gray-900">
                        {formatCurrency(invoice.total, invoice.currency)}
                      </div>
                    </div>

                    {/* Status */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Status</div>
                      <Badge className={getInvoiceStatusColor(invoice.status)}>
                        {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      </Badge>
                    </div>

                    {/* Actions */}
                    <div className="md:col-span-1">
                      <div className="md:hidden text-sm font-medium text-gray-600 mb-1">Actions</div>
                      <div className="flex space-x-2">
                        <Link to={`/billing/invoices/${invoice.id}`}>
                          <Button size="sm" variant="outline">
                            <i className="bi bi-eye mr-1"></i>
                            View
                          </Button>
                        </Link>
                        {invoice.status === 'paid' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownloadInvoice(invoice.id)}
                          >
                            <i className="bi bi-download mr-1"></i>
                            Download
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} invoices
                </div>

                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1 || loading}
                  >
                    <i className="bi bi-chevron-left mr-1"></i>
                    Previous
                  </Button>

                  {/* Page Numbers */}
                  {(() => {
                    const maxVisiblePages = 5;
                    const totalPages = pagination.totalPages;
                    const currentPage = pagination.page;

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    // Adjust start page if we're near the end
                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    const pages = [];
                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <Button
                          key={i}
                          size="sm"
                          variant={i === currentPage ? 'primary' : 'outline'}
                          onClick={() => handlePageChange(i)}
                          disabled={loading}
                        >
                          {i}
                        </Button>
                      );
                    }

                    return pages;
                  })()}

                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.totalPages || loading}
                  >
                    Next
                    <i className="bi bi-chevron-right ml-1"></i>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <div className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600 mb-2">
                {invoices.filter(inv => inv.status === 'paid').length}
              </div>
              <div className="text-sm text-gray-600">Paid Invoices</div>
            </div>
          </Card>

          <Card>
            <div className="p-6 text-center">
              <div className="text-2xl font-bold text-yellow-600 mb-2">
                {invoices.filter(inv => inv.status === 'open').length}
              </div>
              <div className="text-sm text-gray-600">Pending Invoices</div>
            </div>
          </Card>

          <Card>
            <div className="p-6 text-center">
              <div className="text-2xl font-bold text-purple-600 mb-2">
                {formatCurrency(
                  invoices
                    .filter(inv => inv.status === 'paid')
                    .reduce((sum, inv) => sum + parseFloat(inv.total), 0)
                )}
              </div>
              <div className="text-sm text-gray-600">Total Paid</div>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default BillingHistory;
