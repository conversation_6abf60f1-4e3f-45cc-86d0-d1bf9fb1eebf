/**
 * User-friendly error messages for backend responses
 */

export const userFriendlyMessages = {
  // Generic CRUD operations
  fetchFailed: (resource) => `Unable to load ${resource}. Please try again.`,
  createFailed: (resource) => `Unable to create ${resource}. Please check your information and try again.`,
  updateFailed: (resource) => `Unable to update ${resource}. Please check your information and try again.`,
  deleteFailed: (resource) => `Unable to delete ${resource}. Please try again.`,
  notFound: (resource) => `The requested ${resource} could not be found.`,
  
  // Database errors
  databaseError: 'A database error occurred. Please try again in a few moments.',
  connectionError: 'Unable to connect to the database. Please try again later.',
  constraintError: 'This action cannot be completed due to data dependencies.',
  
  // Validation errors
  validationFailed: 'Please check your input and correct any errors.',
  requiredFieldMissing: (field) => `${field} is required.`,
  invalidFormat: (field) => `Please enter a valid ${field}.`,
  duplicateValue: (field) => `This ${field} already exists. Please use a different value.`,
  
  // Authentication errors
  unauthorized: 'You do not have permission to perform this action.',
  sessionExpired: 'Your session has expired. Please log in again.',
  invalidCredentials: 'Invalid email or password. Please try again.',
  
  // File upload errors
  fileUploadFailed: 'File upload failed. Please try again.',
  fileTooLarge: 'The file is too large. Please choose a smaller file.',
  invalidFileType: 'This file type is not supported.',
  
  // Service call specific
  serviceCallLocked: 'This service call cannot be modified because it has already been completed.',
  invalidStatusChange: 'The status change is not allowed.',
  
  // Customer specific
  customerNotFound: 'Customer not found. Please check the customer information.',
  customerExists: 'A customer with this information already exists.',
  
  // Generic fallback
  serverError: 'An unexpected error occurred. Please try again.',
  operationFailed: 'The operation could not be completed. Please try again.'
};

/**
 * Convert technical error to user-friendly message
 * @param {Error} error - The error object
 * @param {string} resource - The resource being operated on (e.g., 'customer', 'service call')
 * @param {string} operation - The operation being performed (e.g., 'create', 'update', 'delete')
 * @returns {Object} User-friendly error response
 */
export const createUserFriendlyError = (error, resource = 'record', operation = 'process') => {
  let userMessage = '';
  let statusCode = 500;
  
  // Handle specific error types
  if (error.name === 'SequelizeValidationError') {
    userMessage = userFriendlyMessages.validationFailed;
    statusCode = 400;
  } else if (error.name === 'SequelizeUniqueConstraintError') {
    const field = error.errors[0]?.path || 'field';
    userMessage = userFriendlyMessages.duplicateValue(field);
    statusCode = 409;
  } else if (error.name === 'SequelizeForeignKeyConstraintError') {
    userMessage = userFriendlyMessages.constraintError;
    statusCode = 400;
  } else if (error.name === 'SequelizeDatabaseError') {
    if (error.original?.code === '22007') {
      userMessage = 'Invalid date format. Please check your date entries.';
    } else if (error.message.includes('does not exist')) {
      userMessage = 'Database configuration error. Please contact support.';
    } else {
      userMessage = userFriendlyMessages.databaseError;
    }
    statusCode = 500;
  } else if (error.message?.toLowerCase().includes('not found')) {
    userMessage = userFriendlyMessages.notFound(resource);
    statusCode = 404;
  } else if (error.message?.toLowerCase().includes('unauthorized')) {
    userMessage = userFriendlyMessages.unauthorized;
    statusCode = 401;
  } else if (error.message?.toLowerCase().includes('validation')) {
    userMessage = userFriendlyMessages.validationFailed;
    statusCode = 400;
  } else {
    // Default based on operation
    switch (operation.toLowerCase()) {
      case 'create':
        userMessage = userFriendlyMessages.createFailed(resource);
        break;
      case 'update':
        userMessage = userFriendlyMessages.updateFailed(resource);
        break;
      case 'delete':
        userMessage = userFriendlyMessages.deleteFailed(resource);
        break;
      case 'fetch':
      case 'get':
        userMessage = userFriendlyMessages.fetchFailed(resource);
        break;
      default:
        userMessage = userFriendlyMessages.operationFailed;
    }
  }
  
  return {
    success: false,
    message: userMessage,
    details: {
      userFriendlyMessage: userMessage,
      originalError: process.env.NODE_ENV === 'development' ? error.message : undefined,
      errorType: error.name || 'UnknownError'
    }
  };
};

/**
 * Enhanced response helper with user-friendly messages
 * @param {Object} res - Express response object
 * @param {Error} error - The error object
 * @param {string} resource - The resource being operated on
 * @param {string} operation - The operation being performed
 * @param {number} statusCode - HTTP status code (optional)
 */
export const sendUserFriendlyError = (res, error, resource = 'record', operation = 'process', statusCode = null) => {
  const errorResponse = createUserFriendlyError(error, resource, operation);
  const status = statusCode || (error.statusCode || 500);
  
  // Log the technical error for debugging
  console.error(`${operation} ${resource} error:`, {
    message: error.message,
    name: error.name,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
  
  return res.status(status).json(errorResponse);
};

/**
 * Success response helper
 * @param {Object} res - Express response object
 * @param {Object} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code
 */
export const sendSuccess = (res, data = null, message = 'Operation completed successfully', statusCode = 200) => {
  const response = {
    success: true,
    message
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  return res.status(statusCode).json(response);
};

/**
 * Validation error response helper
 * @param {Object} res - Express response object
 * @param {Array} errors - Array of validation errors
 * @param {string} message - Main error message
 */
export const sendValidationError = (res, errors, message = 'Please check your input and correct any errors.') => {
  return res.status(400).json({
    success: false,
    message,
    errors,
    details: {
      userFriendlyMessage: message,
      errorType: 'ValidationError'
    }
  });
};

export default {
  userFriendlyMessages,
  createUserFriendlyError,
  sendUserFriendlyError,
  sendSuccess,
  sendValidationError
};
