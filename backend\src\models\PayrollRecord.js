import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const PayrollRecord = sequelize.define('PayrollRecord', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Employee for this payroll record',
    },
    salary_structure_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'salary_structures',
        key: 'id',
      },
      comment: 'Salary structure used for this payroll',
    },
    payroll_number: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Unique payroll number',
    },
    month: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 12,
      },
      comment: 'Payroll month (1-12)',
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Payroll year',
    },
    pay_period_start: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'Start date of pay period',
    },
    pay_period_end: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      comment: 'End date of pay period',
    },
    
    // Attendance Summary
    total_working_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Total working days in the month',
    },
    present_days: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days present',
    },
    absent_days: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days absent',
    },
    leave_days: {
      type: DataTypes.DECIMAL(4, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days on leave',
    },
    holiday_days: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Number of holidays in the month',
    },
    overtime_hours: {
      type: DataTypes.DECIMAL(6, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Total overtime hours',
    },
    late_hours: {
      type: DataTypes.DECIMAL(6, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Total late hours',
    },
    
    // Salary Components
    basic_salary: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Basic salary for the month',
    },
    hra: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'House Rent Allowance',
    },
    transport_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Transport Allowance',
    },
    medical_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Medical Allowance',
    },
    special_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Special Allowance',
    },
    performance_allowance: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Performance Allowance',
    },
    overtime_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Overtime payment',
    },
    other_earnings: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other earnings as JSON object',
    },
    gross_salary: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Total gross salary',
    },
    
    // Deductions
    pf_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee PF deduction',
    },
    esi_employee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employee ESI deduction',
    },
    professional_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Professional Tax',
    },
    income_tax: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Income Tax (TDS)',
    },
    late_deduction: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction for late arrivals',
    },
    absent_deduction: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Deduction for absences',
    },
    loan_deduction: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Loan repayment deduction',
    },
    advance_deduction: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Advance salary deduction',
    },
    other_deductions: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Other deductions as JSON object',
    },
    total_deductions: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Total deductions',
    },
    
    // Final Amount
    net_salary: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      comment: 'Net salary after deductions',
    },
    
    // Employer Contributions
    pf_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer PF contribution',
    },
    esi_employer: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Employer ESI contribution',
    },
    
    // Status and Processing
    status: {
      type: DataTypes.ENUM('draft', 'calculated', 'approved', 'paid', 'cancelled'),
      allowNull: false,
      defaultValue: 'draft',
      comment: 'Payroll processing status',
    },
    calculated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payroll was calculated',
    },
    calculated_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who calculated the payroll',
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When payroll was approved',
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who approved the payroll',
    },
    paid_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When salary was paid',
    },
    payment_method: {
      type: DataTypes.ENUM('bank_transfer', 'cash', 'cheque', 'online'),
      allowNull: true,
      comment: 'Method of salary payment',
    },
    payment_reference: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Payment reference number',
    },
    
    // Additional Information
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional remarks',
    },
    payslip_generated: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether payslip has been generated',
    },
    payslip_sent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether payslip has been sent to employee',
    },
    
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'payroll_records',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['employee_id'],
      },
      {
        fields: ['month', 'year'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['tenant_id', 'employee_id', 'month', 'year'],
        unique: true,
        name: 'unique_employee_month_year',
      },
      {
        fields: ['payroll_number'],
        unique: true,
      },
      {
        fields: ['calculated_at'],
      },
      {
        fields: ['approved_at'],
      },
      {
        fields: ['paid_at'],
      },
    ],
  });

  // Instance methods
  PayrollRecord.prototype.calculateAttendanceRatio = function() {
    if (this.total_working_days === 0) return 0;
    return this.present_days / this.total_working_days;
  };

  PayrollRecord.prototype.calculateEffectiveSalary = function() {
    const attendanceRatio = this.calculateAttendanceRatio();
    return this.basic_salary * attendanceRatio;
  };

  PayrollRecord.prototype.getTotalEarnings = function() {
    let otherEarningsTotal = 0;
    if (this.other_earnings) {
      otherEarningsTotal = Object.values(this.other_earnings).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    }
    
    return this.basic_salary + this.hra + this.transport_allowance + 
           this.medical_allowance + this.special_allowance + 
           this.performance_allowance + this.overtime_amount + otherEarningsTotal;
  };

  PayrollRecord.prototype.getTotalDeductions = function() {
    let otherDeductionsTotal = 0;
    if (this.other_deductions) {
      otherDeductionsTotal = Object.values(this.other_deductions).reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
    }
    
    return this.pf_employee + this.esi_employee + this.professional_tax + 
           this.income_tax + this.late_deduction + this.absent_deduction + 
           this.loan_deduction + this.advance_deduction + otherDeductionsTotal;
  };

  PayrollRecord.prototype.canBeApproved = function() {
    return this.status === 'calculated';
  };

  PayrollRecord.prototype.canBePaid = function() {
    return this.status === 'approved';
  };

  // Class methods
  PayrollRecord.generatePayrollNumber = function(tenantId, month, year) {
    const monthStr = month.toString().padStart(2, '0');
    const yearStr = year.toString().slice(-2);
    const timestamp = Date.now().toString().slice(-6);
    return `PAY${yearStr}${monthStr}${timestamp}`;
  };

  PayrollRecord.getStatusOptions = function() {
    return [
      { value: 'draft', label: 'Draft', color: '#9E9E9E' },
      { value: 'calculated', label: 'Calculated', color: '#2196F3' },
      { value: 'approved', label: 'Approved', color: '#4CAF50' },
      { value: 'paid', label: 'Paid', color: '#8BC34A' },
      { value: 'cancelled', label: 'Cancelled', color: '#F44336' },
    ];
  };

  // Associations
  PayrollRecord.associate = function(models) {
    PayrollRecord.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    PayrollRecord.belongsTo(models.Executive, {
      foreignKey: 'employee_id',
      as: 'employee',
    });

    PayrollRecord.belongsTo(models.SalaryStructure, {
      foreignKey: 'salary_structure_id',
      as: 'salaryStructure',
    });

    PayrollRecord.belongsTo(models.User, {
      foreignKey: 'calculated_by',
      as: 'calculator',
    });

    PayrollRecord.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver',
    });
  };

  return PayrollRecord;
}
