import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('attendance_policies', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Policy name',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Policy description',
    },
    shift_start_time: {
      type: DataTypes.TIME,
      allowNull: false,
      defaultValue: '09:00:00',
      comment: 'Standard shift start time',
    },
    shift_end_time: {
      type: DataTypes.TIME,
      allowNull: false,
      defaultValue: '18:00:00',
      comment: 'Standard shift end time',
    },
    grace_period_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 15,
      comment: 'Grace period for late arrival in minutes',
    },
    minimum_hours_full_day: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 8.0,
      comment: 'Minimum hours required for full day',
    },
    minimum_hours_half_day: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 4.0,
      comment: 'Minimum hours required for half day',
    },
    overtime_threshold_hours: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: false,
      defaultValue: 8.0,
      comment: 'Hours after which overtime calculation starts',
    },
    weekly_off_days: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: '["sunday"]',
      comment: 'Array of weekly off days',
    },
    allow_early_check_in: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Allow check-in before shift start time',
    },
    allow_late_check_out: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Allow check-out after shift end time',
    },
    require_location_verification: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Require GPS location verification for check-in/out',
    },
    allowed_locations: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Array of allowed GPS locations with radius',
    },
    auto_check_out_enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Automatically check out employees at shift end time',
    },
    break_time_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 60,
      comment: 'Standard break time in minutes',
    },
    late_arrival_deduction_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Minutes to deduct for late arrival',
    },
    early_departure_deduction_minutes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Minutes to deduct for early departure',
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is the default policy for new employees',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this policy is active',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('attendance_policies', ['tenant_id']);
  await queryInterface.addIndex('attendance_policies', ['is_default']);
  await queryInterface.addIndex('attendance_policies', ['is_active']);
  await queryInterface.addIndex('attendance_policies', ['tenant_id', 'name'], {
    unique: true,
    name: 'unique_tenant_policy_name',
  });

  console.log('✅ Created attendance_policies table');
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('attendance_policies');
  console.log('✅ Dropped attendance_policies table');
};
