/**
 * Analytics Routes
 * Routes for tenant-specific analytics data
 */

import express from 'express';
import { query } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import { withUsageTracking } from '../middleware/usageTracking.js';
import {
  getTenantAnalytics,
  getCustomerAnalytics,
  getServiceAnalytics,
  getFinancialAnalytics,
  getExecutiveAnalytics,
  getTrendAnalytics
} from '../controllers/tenantAnalyticsController.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

/**
 * @swagger
 * /api/v1/analytics:
 *   get:
 *     summary: Get comprehensive tenant analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom start date (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom end date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Analytics data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     period:
 *                       type: string
 *                     dateRange:
 *                       type: object
 *                     customerMetrics:
 *                       type: object
 *                     serviceMetrics:
 *                       type: object
 *                     financialMetrics:
 *                       type: object
 *                     executiveMetrics:
 *                       type: object
 *                     trendData:
 *                       type: object
 */
router.get('/', [
  requirePermission('analytics.view'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 90d, 1y'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be in YYYY-MM-DD format'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be in YYYY-MM-DD format'),
  validateRequest,
  ...withUsageTracking('analytics', { operation: 'view' })
], getTenantAnalytics);

/**
 * @swagger
 * /api/v1/analytics/customers:
 *   get:
 *     summary: Get customer analytics data
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y]
 *           default: 30d
 *     responses:
 *       200:
 *         description: Customer analytics data
 */
router.get('/customers', [
  requirePermission('analytics.view'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 90d, 1y'),
  validateRequest,
  ...withUsageTracking('analytics', { operation: 'view', type: 'customers' })
], async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', startDate: customStartDate, endDate: customEndDate } = req.query;
    
    // Calculate date range
    const endDate = customEndDate ? new Date(customEndDate) : new Date();
    const startDate = customStartDate ? new Date(customStartDate) : (() => {
      const date = new Date();
      switch (period) {
        case '7d': date.setDate(date.getDate() - 7); break;
        case '30d': date.setDate(date.getDate() - 30); break;
        case '90d': date.setDate(date.getDate() - 90); break;
        case '1y': date.setFullYear(date.getFullYear() - 1); break;
        default: date.setDate(date.getDate() - 30);
      }
      return date;
    })();

    const data = await getCustomerAnalytics(tenantId, startDate, endDate);
    
    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        ...data
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve customer analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/services:
 *   get:
 *     summary: Get service analytics data
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 */
router.get('/services', [
  requirePermission('analytics.view'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 90d, 1y'),
  validateRequest,
  ...withUsageTracking('analytics', { operation: 'view', type: 'services' })
], async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', startDate: customStartDate, endDate: customEndDate } = req.query;
    
    // Calculate date range
    const endDate = customEndDate ? new Date(customEndDate) : new Date();
    const startDate = customStartDate ? new Date(customStartDate) : (() => {
      const date = new Date();
      switch (period) {
        case '7d': date.setDate(date.getDate() - 7); break;
        case '30d': date.setDate(date.getDate() - 30); break;
        case '90d': date.setDate(date.getDate() - 90); break;
        case '1y': date.setFullYear(date.getFullYear() - 1); break;
        default: date.setDate(date.getDate() - 30);
      }
      return date;
    })();

    const data = await getServiceAnalytics(tenantId, startDate, endDate);
    
    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        ...data
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve service analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/financial:
 *   get:
 *     summary: Get financial analytics data
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 */
router.get('/financial', [
  requirePermission('analytics.view'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '90d', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 90d, 1y'),
  validateRequest,
  ...withUsageTracking('analytics', { operation: 'view', type: 'financial' })
], async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', startDate: customStartDate, endDate: customEndDate } = req.query;
    
    // Calculate date range
    const endDate = customEndDate ? new Date(customEndDate) : new Date();
    const startDate = customStartDate ? new Date(customStartDate) : (() => {
      const date = new Date();
      switch (period) {
        case '7d': date.setDate(date.getDate() - 7); break;
        case '30d': date.setDate(date.getDate() - 30); break;
        case '90d': date.setDate(date.getDate() - 90); break;
        case '1y': date.setFullYear(date.getFullYear() - 1); break;
        default: date.setDate(date.getDate() - 30);
      }
      return date;
    })();

    const data = await getFinancialAnalytics(tenantId, startDate, endDate);
    
    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        ...data
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve financial analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @swagger
 * /api/v1/analytics/executives:
 *   get:
 *     summary: Get executive performance analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 */
router.get('/executives', [
  requirePermission('analytics.view'),
  ...withUsageTracking('analytics', { operation: 'view', type: 'executives' })
], async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', startDate: customStartDate, endDate: customEndDate } = req.query;
    
    // Calculate date range
    const endDate = customEndDate ? new Date(customEndDate) : new Date();
    const startDate = customStartDate ? new Date(customStartDate) : (() => {
      const date = new Date();
      switch (period) {
        case '7d': date.setDate(date.getDate() - 7); break;
        case '30d': date.setDate(date.getDate() - 30); break;
        case '90d': date.setDate(date.getDate() - 90); break;
        case '1y': date.setFullYear(date.getFullYear() - 1); break;
        default: date.setDate(date.getDate() - 30);
      }
      return date;
    })();

    const data = await getExecutiveAnalytics(tenantId, startDate, endDate);
    
    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        ...data
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve executive analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

export default router;
