import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  FaT<PERSON><PERSON>, FaFilter, FaDownload, FaEye, FaSearch, FaCalendarAlt,
  FaUser, FaExclamationTriangle, FaCheckCircle, FaClock,
  FaChevronLeft, FaChevronRight, FaSort, FaSortUp, FaSortDown,
  FaChartBar, FaChartPie
} from 'react-icons/fa';
import api from '../../services/api';
import LoadingScreen from '../../components/ui/LoadingScreen';

const ServiceCallReports = () => {
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    statusId: '',
    priority: '',
    assignedTo: '',
    customerId: '',
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await api.get(`/reports/service-calls?${params}`);
      setReportData(response.data.data);
      toast.success('Service call reports loaded');
    } catch (error) {
      console.error('Error fetching service call reports:', error);
      toast.error('Failed to load service call reports');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleApplyFilters = () => {
    fetchReportData();
    setShowFilters(false);
  };

  const handleClearFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      statusId: '',
      priority: '',
      assignedTo: '',
      customerId: '',
    });
    fetchReportData();
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    
    switch (status.category) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'open': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleExport = (format = 'csv') => {
    try {
      if (!reportData?.serviceCalls || reportData.serviceCalls.length === 0) {
        toast.error('No service call data to export');
        return;
      }

      const exportData = reportData.serviceCalls.map(call => ({
        'Call Number': call.call_number || 'N/A',
        'Customer': call.customer?.company_name || call.customer?.display_name || 'N/A',
        'Contact Person': call.contactPerson?.first_name && call.contactPerson?.last_name
          ? `${call.contactPerson.first_name} ${call.contactPerson.last_name}`
          : call.customer?.contact_person || 'N/A',
        'Subject': call.subject || 'N/A',
        'Description': call.description || 'N/A',
        'Call Type': call.call_type || 'N/A',
        'Call Billing Type': call.call_billing_type || 'N/A',
        'Status': call.status?.name || call.status || 'N/A',
        'Priority': call.priority || 'N/A',
        'Assigned Executive': call.assignedExecutive?.first_name && call.assignedExecutive?.last_name
          ? `${call.assignedExecutive.first_name} ${call.assignedExecutive.last_name}`
          : 'Unassigned',
        'Created Date': call.created_at ? new Date(call.created_at).toLocaleDateString() : 'N/A',
        'Scheduled Date': call.scheduled_date ? new Date(call.scheduled_date).toLocaleDateString() : 'N/A',
        'Completed Date': call.completed_at ? new Date(call.completed_at).toLocaleDateString() : 'N/A',
        'Service Charges': call.service_charges || '0',
        'Estimated Hours': call.estimated_hours || '0',
        'Actual Hours': call.actual_hours || '0',
        'Total Time (minutes)': call.total_time_minutes || '0',
        'Customer Location': call.customer?.city && call.customer?.state
          ? `${call.customer.city}, ${call.customer.state}`
          : 'N/A',
        'Tally Serial Number': call.tally_serial_number || 'N/A',
        'Is Under AMC': call.is_under_amc ? 'Yes' : 'No',
        'Is Billable': call.is_billable ? 'Yes' : 'No'
      }));

      if (format === 'csv') {
        // Generate CSV content
        const headers = Object.keys(exportData[0] || {});
        const csvContent = [
          headers.join(','),
          ...exportData.map(row =>
            headers.map(header => {
              const value = row[header] || '';
              // Escape quotes and wrap in quotes if contains comma, quote, or newline
              const stringValue = value.toString();
              if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
                return `"${stringValue.replace(/"/g, '""')}"`;
              }
              return stringValue;
            }).join(',')
          )
        ].join('\n');

        // Download CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `service_calls_report_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast.success(`${exportData.length} service calls exported to CSV successfully`);
      } else {
        toast.error(`${format.toUpperCase()} export not yet implemented`);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export service call data');
    }
  };

  const renderFilters = () => (
    <div className={`bg-gray-50 border-t border-gray-200 p-4 ${showFilters ? 'block' : 'hidden'}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
          <select
            value={filters.priority}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
        <div className="flex items-end space-x-2">
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700"
          >
            Apply Filters
          </button>
          <button
            onClick={handleClearFilters}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );

  const renderSummaryCards = () => {
    if (!reportData?.summary) return null;

    const { summary } = reportData;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <FaTools className="text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Calls</p>
              <p className="text-2xl font-bold text-gray-900">{summary.totalCalls}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg mr-3">
              <FaCheckCircle className="text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Resolution Time</p>
              <p className="text-2xl font-bold text-gray-900">{summary.avgResolutionTime}h</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg mr-3">
              <FaExclamationTriangle className="text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {summary.byPriority?.high || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg mr-3">
              <FaClock className="text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Critical Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {summary.byPriority?.critical || 0}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderStatusChart = () => {
    if (!reportData?.summary?.byStatus) return null;

    const statusData = Object.entries(reportData.summary.byStatus);
    const total = statusData.reduce((sum, [, count]) => sum + count, 0);

    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Status Distribution</h3>
          <FaChartPie className="text-blue-600" />
        </div>
        <div className="space-y-3">
          {statusData.map(([status, count]) => {
            const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
            return (
              <div key={status} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 capitalize">{status}</span>
                <div className="flex items-center">
                  <div className="w-32 bg-gray-200 rounded-full h-2 mr-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">{count}</span>
                  <span className="text-xs text-gray-500 w-12">({percentage}%)</span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Service Call Reports..."
        subtitle="Analyzing service call data"
        variant="page"
      />
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-0">Service Call Reports</h2>
          <p className="text-gray-600">Comprehensive analysis of all service calls</p>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center ${
              showFilters
                ? 'bg-blue-600 text-white border-blue-600'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <FaFilter className="mr-1 sm:mr-2" />
            Filters
          </button>
          <button
            onClick={() => handleExport('csv')}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border bg-green-600 text-white border-green-600 hover:bg-green-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
          >
            <FaDownload className="mr-1 sm:mr-2" />
            Export CSV
          </button>
          <button
            onClick={() => handleExport('excel')}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border bg-blue-600 text-white border-blue-600 hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
          >
            <FaDownload className="mr-1 sm:mr-2" />
            Export Excel
          </button>
        </div>
      </div>

      {/* Filters */}
      {renderFilters()}

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {renderStatusChart()}
        
        {/* Priority Distribution */}
        {reportData?.summary?.byPriority && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Priority Distribution</h3>
              <FaChartBar className="text-orange-600" />
            </div>
            <div className="space-y-3">
              {Object.entries(reportData.summary.byPriority).map(([priority, count]) => (
                <div key={priority} className="flex items-center justify-between">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(priority)}`}>
                    {priority}
                  </span>
                  <span className="text-lg font-bold text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Service Calls Table */}
      {reportData?.serviceCalls && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Service Calls</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Call Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Subject
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned To
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Call Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.serviceCalls.slice(0, 20).map((call) => (
                  <tr key={call.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {call.callNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        <div className="font-medium">{call.customer?.company_name}</div>
                        <div className="text-gray-500">{call.customer?.customer_code}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="max-w-xs truncate" title={call.subject}>
                        {call.subject || 'No subject'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(call.priority)}`}>
                        {call.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(call.status)}`}>
                        {call.status?.name || 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {call.assignedExecutive?.name || 'Unassigned'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(call.callDate)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceCallReports;
