import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Lead = sequelize.define('Lead', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Lead date',
    },
    customer_name: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200],
      },
      comment: 'Potential customer name',
    },
    products_issues_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'products_issues',
        key: 'id',
      },
      comment: 'Reference to products/issues from master data',
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Potential deal amount',
      get() {
        const value = this.getDataValue('amount');
        return value ? parseFloat(value) : null;
      },
    },
    contact_no: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 20],
      },
      comment: 'Contact phone number without country code',
    },
    country_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '+91',
      validate: {
        len: [0, 10],
      },
      comment: 'Country code for contact number',
    },
    status_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'call_statuses',
        key: 'id',
      },
      comment: 'Reference to call status from master data',
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Additional remarks or notes',
    },
    executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Reference to executive from master data',
      validate: {
        isUUID: {
          args: 4,
          msg: 'Please select a valid executive from the list'
        }
      }
    },
    contacted_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Executive who made contact with the lead',
      validate: {
        isUUID: {
          args: 4,
          msg: 'Please select a valid contacted executive from the list'
        }
      }
    },
    follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
      comment: 'Next follow up date',
    },
    ref_name: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100],
      },
      comment: 'Reference person name',
    },
    ref_contact_no: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 20],
      },
      comment: 'Reference contact number without country code',
    },
    ref_country_code: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '+91',
      validate: {
        len: [0, 10],
      },
      comment: 'Country code for reference contact number',
    },
    ref_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Reference amount',
      get() {
        const value = this.getDataValue('ref_amount');
        return value ? parseFloat(value) : null;
      },
    },
    converted_to_customer_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customers',
        key: 'id',
      },
      comment: 'Customer ID if lead was converted',
    },
    conversion_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Date when lead was converted to customer',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    updated_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'leads',
    timestamps: true,
    underscored: true,
    paranoid: true, // Enable soft deletes
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['executive'],
      },
      {
        fields: ['follow_up_date'],
      },
      {
        fields: ['created_by'],
      },
      {
        fields: ['converted_to_customer_id'],
      },
      {
        fields: ['date'],
      },
      {
        fields: ['created_at'],
      },
    ],
  });

  // Instance methods
  Lead.prototype.getFullContactNumber = function() {
    if (!this.contact_no) return null;
    return `${this.country_code || '+91'} ${this.contact_no}`;
  };

  Lead.prototype.getFullRefContactNumber = function() {
    if (!this.ref_contact_no) return null;
    return `${this.ref_country_code || '+91'} ${this.ref_contact_no}`;
  };

  Lead.prototype.isConverted = function() {
    return !!this.converted_to_customer_id;
  };

  Lead.prototype.canBeConverted = function() {
    return !this.isConverted() && this.customer_name && this.contact_no;
  };

  Lead.prototype.getDisplayName = function() {
    return this.customer_name || `Lead #${this.id.slice(-8)}`;
  };

  // Class methods
  Lead.getStatusOptions = function() {
    return [
      'New',
      'Follow Up',
      'Call Not Attended',
      'Interested',
      'Not Interested',
      'Converted',
      'Lost'
    ];
  };

  // Associations
  Lead.associate = function(models) {
    Lead.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Lead.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    Lead.belongsTo(models.User, {
      foreignKey: 'updated_by',
      as: 'updater',
    });

    Lead.belongsTo(models.Customer, {
      foreignKey: 'converted_to_customer_id',
      as: 'convertedCustomer',
    });

    Lead.belongsTo(models.ProductsIssues, {
      foreignKey: 'products_issues_id',
      as: 'productsIssues',
    });

    Lead.belongsTo(models.CallStatus, {
      foreignKey: 'status_id',
      as: 'status',
    });

    Lead.belongsTo(models.Executive, {
      foreignKey: 'executive_id',
      as: 'executive',
    });

    Lead.belongsTo(models.Executive, {
      foreignKey: 'contacted_executive_id',
      as: 'contactedExecutive',
    });

    Lead.hasMany(models.LeadContactHistory, {
      foreignKey: 'lead_id',
      as: 'contactHistory',
    });
  };

  return Lead;
}
