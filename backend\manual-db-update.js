#!/usr/bin/env node

/**
 * Manual Database Update Script
 * 
 * This script manually adds the whatsapp_enabled column and updates records
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { Sequelize, DataTypes } from 'sequelize';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '.env') });

// Create database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USERNAME,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log
  }
);

async function main() {
  console.log('🔧 Manual Database Update for WhatsApp...\n');

  try {
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Check if column exists
    const [results] = await sequelize.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'notification_settings' 
      AND column_name = 'whatsapp_enabled'
    `);

    if (results.length === 0) {
      console.log('📋 Adding whatsapp_enabled column...');
      
      // Add the column
      await sequelize.query(`
        ALTER TABLE notification_settings 
        ADD COLUMN whatsapp_enabled BOOLEAN DEFAULT true
      `);
      
      console.log('✅ Added whatsapp_enabled column');
    } else {
      console.log('✅ whatsapp_enabled column already exists');
    }

    // Update existing records
    console.log('📋 Updating existing records...');
    const [updateResults] = await sequelize.query(`
      UPDATE notification_settings 
      SET whatsapp_enabled = true 
      WHERE whatsapp_enabled IS NULL
    `);
    
    console.log(`✅ Updated ${updateResults.rowCount || 0} records`);

    // Verify the update
    console.log('🔍 Verifying the update...');
    const [verifyResults] = await sequelize.query(`
      SELECT id, tenant_id, email_enabled, whatsapp_enabled 
      FROM notification_settings
    `);
    
    console.log('📋 Current notification settings:');
    verifyResults.forEach(row => {
      console.log(`   ID: ${row.id}, Tenant: ${row.tenant_id}, Email: ${row.email_enabled}, WhatsApp: ${row.whatsapp_enabled}`);
    });

    console.log('\n✅ Manual database update completed!');

  } catch (error) {
    console.error('\n❌ Manual update failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the update script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
