/**
 * Metric Card Component
 * Display key metrics with trend indicators and mini charts
 */

import React from 'react';
import { cn } from '../../utils/helpers';
import { formatNumber, calculatePercentageChange } from './chartUtils';
import { CHART_COLORS } from './chartThemes';

const MetricCard = ({
  title,
  value,
  previousValue = null,
  unit = '',
  format = 'default', // 'default', 'currency', 'percentage', 'compact'
  trend = null, // 'up', 'down', 'neutral' or auto-calculated from previousValue
  trendValue = null, // Custom trend value or auto-calculated
  icon = null,
  color = 'primary', // 'primary', 'success', 'warning', 'danger'
  size = 'medium', // 'small', 'medium', 'large'
  showTrend = true,
  miniChart = null, // Optional mini chart component
  className = '',
  ...props
}) => {
  // Calculate trend if not provided
  const calculatedTrend = trend || (
    previousValue !== null && previousValue !== undefined
      ? value > previousValue ? 'up' : value < previousValue ? 'down' : 'neutral'
      : null
  );

  // Calculate trend value if not provided
  const calculatedTrendValue = trendValue !== null 
    ? trendValue 
    : (previousValue !== null && previousValue !== undefined
        ? calculatePercentageChange(value, previousValue)
        : null
      );

  // Size configurations
  const sizeClasses = {
    small: {
      container: 'p-4',
      title: 'text-xs',
      value: 'text-lg',
      trend: 'text-xs',
      icon: 'w-4 h-4'
    },
    medium: {
      container: 'p-6',
      title: 'text-sm',
      value: 'text-2xl',
      trend: 'text-sm',
      icon: 'w-5 h-5'
    },
    large: {
      container: 'p-8',
      title: 'text-base',
      value: 'text-3xl',
      trend: 'text-base',
      icon: 'w-6 h-6'
    }
  };

  // Color configurations
  const colorClasses = {
    primary: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'text-blue-600',
      value: 'text-blue-900'
    },
    success: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      icon: 'text-green-600',
      value: 'text-green-900'
    },
    warning: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      icon: 'text-yellow-600',
      value: 'text-yellow-900'
    },
    danger: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      icon: 'text-red-600',
      value: 'text-red-900'
    }
  };

  // Trend configurations
  const trendClasses = {
    up: {
      bg: 'bg-green-100',
      text: 'text-green-800',
      icon: '↗'
    },
    down: {
      bg: 'bg-red-100',
      text: 'text-red-800',
      icon: '↘'
    },
    neutral: {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      icon: '→'
    }
  };

  const sizeConfig = sizeClasses[size];
  const colorConfig = colorClasses[color];
  const trendConfig = calculatedTrend ? trendClasses[calculatedTrend] : null;

  // Format the main value
  const formattedValue = formatNumber(value, format);

  return (
    <div 
      className={cn(
        'bg-white rounded-lg border shadow-sm',
        colorConfig.border,
        sizeConfig.container,
        className
      )}
      {...props}
    >
      {/* Header with title and icon */}
      <div className="flex items-center justify-between mb-2">
        <h3 className={cn('font-medium text-gray-600', sizeConfig.title)}>
          {title}
        </h3>
        {icon && (
          <div className={cn('flex-shrink-0', colorConfig.icon, sizeConfig.icon)}>
            {icon}
          </div>
        )}
      </div>

      {/* Main value */}
      <div className={cn('font-bold', colorConfig.value, sizeConfig.value)}>
        {formattedValue}
        {unit && <span className="text-gray-500 ml-1">{unit}</span>}
      </div>

      {/* Trend indicator and mini chart */}
      {(showTrend || miniChart) && (
        <div className="flex items-center justify-between mt-3">
          {/* Trend indicator */}
          {showTrend && trendConfig && calculatedTrendValue !== null && (
            <div className={cn(
              'inline-flex items-center px-2 py-1 rounded-full',
              trendConfig.bg,
              trendConfig.text,
              sizeConfig.trend
            )}>
              <span className="mr-1">{trendConfig.icon}</span>
              <span className="font-medium">
                {Math.abs(calculatedTrendValue).toFixed(1)}%
              </span>
            </div>
          )}

          {/* Mini chart */}
          {miniChart && (
            <div className="flex-1 ml-4 h-8">
              {miniChart}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Preset metric card variants
export const RevenueCard = (props) => (
  <MetricCard
    color="success"
    format="currency"
    icon={
      <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
      </svg>
    }
    {...props}
  />
);

export const CustomerCard = (props) => (
  <MetricCard
    color="primary"
    format="default"
    icon={
      <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>
    }
    {...props}
  />
);

export const ServiceCard = (props) => (
  <MetricCard
    color="warning"
    format="default"
    icon={
      <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    }
    {...props}
  />
);

export default MetricCard;
