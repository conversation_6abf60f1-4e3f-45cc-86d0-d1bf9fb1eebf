import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Payment = sequelize.define('Payment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    invoice_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'invoices',
        key: 'id',
      },
    },
    stripe_payment_intent_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    stripe_charge_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payment_method: {
      type: DataTypes.ENUM(
        'card',
        'bank_transfer',
        'upi',
        'wallet',
        'cash',
        'cheque',
        'other'
      ),
      defaultValue: 'card',
    },
    status: {
      type: DataTypes.ENUM(
        'pending',
        'processing',
        'succeeded',
        'failed',
        'canceled',
        'refunded',
        'partially_refunded'
      ),
      defaultValue: 'pending',
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    amount_refunded: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    failure_reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    failure_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    receipt_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    receipt_url: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    refund_reason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    processed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    failed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    refunded_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'payments',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['invoice_id'],
      },
      {
        fields: ['stripe_payment_intent_id'],
        unique: true,
        where: {
          stripe_payment_intent_id: {
            [sequelize.Sequelize.Op.ne]: null,
          },
        },
      },
      {
        fields: ['status'],
      },
      {
        fields: ['payment_method'],
      },
      {
        fields: ['processed_at'],
      },
    ],
  });

  // Instance methods
  Payment.prototype.isSuccessful = function() {
    return this.status === 'succeeded';
  };

  Payment.prototype.isFailed = function() {
    return this.status === 'failed';
  };

  Payment.prototype.isPending = function() {
    return ['pending', 'processing'].includes(this.status);
  };

  Payment.prototype.canRefund = function() {
    return this.status === 'succeeded' && this.amount_refunded < this.amount;
  };

  Payment.prototype.getRemainingRefundAmount = function() {
    return this.amount - this.amount_refunded;
  };

  Payment.prototype.markAsSucceeded = async function(processedAt = new Date()) {
    await this.update({
      status: 'succeeded',
      processed_at: processedAt,
    });
  };

  Payment.prototype.markAsFailed = async function(reason, code, failedAt = new Date()) {
    await this.update({
      status: 'failed',
      failure_reason: reason,
      failure_code: code,
      failed_at: failedAt,
    });
  };

  Payment.prototype.processRefund = async function(amount, reason) {
    const refundAmount = amount || this.getRemainingRefundAmount();
    const newRefundedAmount = parseFloat(this.amount_refunded) + parseFloat(refundAmount);
    
    const status = newRefundedAmount >= parseFloat(this.amount) ? 'refunded' : 'partially_refunded';
    
    await this.update({
      status,
      amount_refunded: newRefundedAmount,
      refund_reason: reason,
      refunded_at: new Date(),
    });
  };

  // Static methods
  Payment.getTotalByTenant = async function(tenantId, startDate, endDate) {
    const whereClause = {
      tenant_id: tenantId,
      status: 'succeeded',
    };

    if (startDate && endDate) {
      whereClause.processed_at = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate],
      };
    }

    const result = await this.findOne({
      where: whereClause,
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'total_amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_count'],
      ],
    });

    return {
      totalAmount: parseFloat(result.getDataValue('total_amount')) || 0,
      totalCount: parseInt(result.getDataValue('total_count')) || 0,
    };
  };

  // Associations
  Payment.associate = function(models) {
    Payment.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Payment.belongsTo(models.Invoice, {
      foreignKey: 'invoice_id',
      as: 'invoice',
    });
  };

  return Payment;
}
