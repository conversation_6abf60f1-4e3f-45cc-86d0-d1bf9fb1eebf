/**
 * Chart Container Component
 * Provides consistent wrapper for all chart components with loading states, error handling, and responsive behavior
 */

import React from 'react';
import { cn } from '../../utils/helpers';
import { Spinner } from '../ui';

const ChartContainer = ({
  title,
  subtitle,
  children,
  loading = false,
  error = null,
  noData = false,
  className = '',
  headerClassName = '',
  contentClassName = '',
  actions = null,
  size = 'medium', // 'small', 'medium', 'large'
  ...props
}) => {
  // Size configurations
  const sizeClasses = {
    small: 'p-4',
    medium: 'p-6',
    large: 'p-8'
  };

  const headerSizeClasses = {
    small: 'mb-3',
    medium: 'mb-4',
    large: 'mb-6'
  };

  const titleSizeClasses = {
    small: 'text-base',
    medium: 'text-lg',
    large: 'text-xl'
  };

  const subtitleSizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  // Loading state
  if (loading) {
    return (
      <div className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm',
        sizeClasses[size],
        className
      )} {...props}>
        {(title || subtitle) && (
          <div className={cn('border-b border-gray-100', headerSizeClasses[size], headerClassName)}>
            {title && (
              <h3 className={cn('font-semibold text-gray-900', titleSizeClasses[size])}>
                {title}
              </h3>
            )}
            {subtitle && (
              <p className={cn('text-gray-600 mt-1', subtitleSizeClasses[size])}>
                {subtitle}
              </p>
            )}
          </div>
        )}
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Spinner size="lg" className="mx-auto mb-4" />
            <p className="text-gray-500 text-sm">Loading chart data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm',
        sizeClasses[size],
        className
      )} {...props}>
        {(title || subtitle) && (
          <div className={cn('border-b border-gray-100', headerSizeClasses[size], headerClassName)}>
            {title && (
              <h3 className={cn('font-semibold text-gray-900', titleSizeClasses[size])}>
                {title}
              </h3>
            )}
            {subtitle && (
              <p className={cn('text-gray-600 mt-1', subtitleSizeClasses[size])}>
                {subtitle}
              </p>
            )}
          </div>
        )}
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-red-600 text-sm font-medium mb-1">Failed to load chart</p>
            <p className="text-gray-500 text-xs">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // No data state
  if (noData) {
    return (
      <div className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm',
        sizeClasses[size],
        className
      )} {...props}>
        {(title || subtitle) && (
          <div className={cn('border-b border-gray-100', headerSizeClasses[size], headerClassName)}>
            <div className="flex items-center justify-between">
              <div>
                {title && (
                  <h3 className={cn('font-semibold text-gray-900', titleSizeClasses[size])}>
                    {title}
                  </h3>
                )}
                {subtitle && (
                  <p className={cn('text-gray-600 mt-1', subtitleSizeClasses[size])}>
                    {subtitle}
                  </p>
                )}
              </div>
              {actions && <div className="flex items-center space-x-2">{actions}</div>}
            </div>
          </div>
        )}
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-gray-500 text-sm">No data available</p>
            <p className="text-gray-400 text-xs mt-1">Data will appear here when available</p>
          </div>
        </div>
      </div>
    );
  }

  // Normal state with data
  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 shadow-sm',
      sizeClasses[size],
      className
    )} {...props}>
      {(title || subtitle || actions) && (
        <div className={cn('border-b border-gray-100', headerSizeClasses[size], headerClassName)}>
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className={cn('font-semibold text-gray-900', titleSizeClasses[size])}>
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className={cn('text-gray-600 mt-1', subtitleSizeClasses[size])}>
                  {subtitle}
                </p>
              )}
            </div>
            {actions && <div className="flex items-center space-x-2">{actions}</div>}
          </div>
        </div>
      )}
      <div className={cn('chart-content', contentClassName)}>
        {children}
      </div>
    </div>
  );
};

export default ChartContainer;
