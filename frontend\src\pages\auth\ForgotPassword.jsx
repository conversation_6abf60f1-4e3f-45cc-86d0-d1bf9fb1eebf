import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const ForgotPassword = () => {
  return (
    <>
      <Helmet>
        <title>Forgot Password - TallyCRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Reset Password</h2>
          <p className="text-gray-600">We'll help you get back into your account</p>
        </div>

        {/* Coming Soon Card */}
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-500 rounded-full mb-4">
            <i className="bi bi-key text-white text-2xl"></i>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">Password Reset Coming Soon!</h3>
          <p className="text-gray-600 mb-6">
            We're implementing a secure password reset system.
            For now, please contact your administrator to reset your password.
          </p>

          {/* Contact Info */}
          <div className="bg-white rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-gray-900 mb-3">Need immediate help?</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center justify-center">
                <i className="bi bi-envelope mr-2 text-orange-500"></i>
                <span>Contact: <EMAIL></span>
              </div>
              <div className="flex items-center justify-center">
                <i className="bi bi-telephone mr-2 text-orange-500"></i>
                <span>Support: +****************</span>
              </div>
            </div>
          </div>

          {/* Features Preview */}
          <div className="grid grid-cols-1 gap-3 mb-6">
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Secure email verification</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Time-limited reset links</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <i className="bi bi-check-circle text-green-500 mr-2"></i>
              <span>Strong password requirements</span>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Remember your password?</span>
          </div>
        </div>

        {/* Back to Login Link */}
        <div className="text-center">
          <Link
            to="/auth/login"
            className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium no-underline transition-colors duration-200 hover:underline"
          >
            <i className="bi bi-arrow-left mr-2"></i>
            Back to Sign In
          </Link>
        </div>
      </div>
    </>
  );
};

export default ForgotPassword;
