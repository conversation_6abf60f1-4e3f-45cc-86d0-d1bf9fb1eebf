import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

/**
 * Test script to customize and test renewal email templates
 */
async function testEmailTemplates() {
  try {
    console.log('🎨 Starting email template customization and testing...');

    // Get the first active tenant for testing
    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    if (!tenant) {
      console.error('❌ No active tenant found. Please create a tenant first.');
      return;
    }

    console.log(`✅ Using tenant: ${tenant.name} (${tenant.id})`);

    // Test accessing email template management system
    console.log('\n📧 Testing email template access...');
    
    // Import email template controller functions
    const { getTemplateContent, isTemplateEnabled } = await import('./src/controllers/emailTemplateController.js');

    // Test template keys for renewal notifications
    const renewalTemplateKeys = ['renewal_reminder', 'renewal_urgent', 'renewal_overdue'];
    
    console.log('\n🔍 Checking existing renewal email templates...');
    
    for (const templateKey of renewalTemplateKeys) {
      try {
        const isEnabled = await isTemplateEnabled(tenant.id, templateKey);
        const templateContent = await getTemplateContent(tenant.id, templateKey);
        
        console.log(`  📌 ${templateKey}:`);
        console.log(`     - Enabled: ${isEnabled}`);
        console.log(`     - Template exists: ${!!templateContent}`);
        
        if (templateContent) {
          console.log(`     - Subject: ${templateContent.subject.substring(0, 50)}...`);
          console.log(`     - Content length: ${templateContent.content.length} characters`);
        }
      } catch (error) {
        console.log(`     - Error accessing template: ${error.message}`);
      }
    }

    // Test template variable replacement
    console.log('\n🧪 Testing template variable replacement...');
    
    const sampleCustomerData = {
      customer_name: 'ABC Technologies Pvt Ltd',
      company_name: 'ABC Technologies Pvt Ltd',
      renewal_type: 'AMC',
      expiry_date: '25/07/2025',
      days_remaining: 35,
      days_overdue: 0,
      renewal_amount: 25000,
      contact_email: '<EMAIL>',
      service_type: 'AMC',
      is_overdue: false,
      is_urgent: false,
      days_before_expiry: 30,
    };

    // Test variable replacement function
    const { replaceTemplateVariables } = await import('./src/services/NotificationService.js');
    const notificationService = new (await import('./src/services/NotificationService.js')).default();

    console.log('\n📝 Testing variable replacement with sample data:');
    console.log('   Sample Data:', JSON.stringify(sampleCustomerData, null, 2));

    // Test template generation for different scenarios
    const testScenarios = [
      { eventType: 'renewal_reminder', description: 'Standard Renewal Reminder (30+ days)' },
      { eventType: 'renewal_urgent', description: 'Urgent Renewal Reminder (2-3 days)' },
      { eventType: 'renewal_overdue', description: 'Overdue Renewal Notice (past expiry)' }
    ];

    for (const scenario of testScenarios) {
      console.log(`\n🎯 Testing ${scenario.description}:`);
      
      try {
        // Adjust sample data for scenario
        const scenarioData = { ...sampleCustomerData, tenantId: tenant.id };
        
        if (scenario.eventType === 'renewal_urgent') {
          scenarioData.days_remaining = 2;
          scenarioData.is_urgent = true;
          scenarioData.days_before_expiry = 2;
        } else if (scenario.eventType === 'renewal_overdue') {
          scenarioData.days_remaining = 0;
          scenarioData.days_overdue = 5;
          scenarioData.is_overdue = true;
          scenarioData.expiry_date = '15/06/2025';
        }

        const emailContent = await notificationService.generateEmailContent(scenario.eventType, scenarioData);
        
        console.log(`   ✅ Template generation successful`);
        console.log(`   📧 Subject: ${emailContent.subject}`);
        console.log(`   📄 Content preview: ${emailContent.template.substring(0, 200).replace(/\s+/g, ' ')}...`);
        
        // Test variable replacement
        const hasVariables = emailContent.subject.includes('{{') || emailContent.template.includes('{{');
        console.log(`   🔧 Variables properly replaced: ${!hasVariables ? '✅' : '❌'}`);
        
        if (hasVariables) {
          const unreplacedVars = [...emailContent.subject.matchAll(/\{\{([^}]+)\}\}/g), ...emailContent.template.matchAll(/\{\{([^}]+)\}\}/g)];
          console.log(`   ⚠️  Unreplaced variables: ${unreplacedVars.map(match => match[0]).join(', ')}`);
        }

      } catch (error) {
        console.error(`   ❌ Error testing ${scenario.eventType}:`, error.message);
      }
    }

    // Test template selection logic
    console.log('\n🎯 Testing template selection logic...');
    
    const RenewalNotificationService = (await import('./src/services/RenewalNotificationService.js')).default;
    const renewalService = new RenewalNotificationService();
    
    const testCases = [
      { daysRemaining: 30, expected: 'renewal_reminder' },
      { daysRemaining: 7, expected: 'renewal_reminder' },
      { daysRemaining: 3, expected: 'renewal_urgent' },
      { daysRemaining: 2, expected: 'renewal_urgent' },
      { daysRemaining: 0, expected: 'renewal_urgent' },
      { daysRemaining: -5, expected: 'renewal_overdue' },
    ];

    for (const testCase of testCases) {
      const mockNotificationSchedule = {
        expiry_date: new Date(Date.now() + testCase.daysRemaining * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        days_before_expiry: Math.max(0, testCase.daysRemaining)
      };

      const selectedTemplate = renewalService.getTemplateType(mockNotificationSchedule);
      const isCorrect = selectedTemplate === testCase.expected;
      
      console.log(`   Days: ${testCase.daysRemaining.toString().padStart(3)} → Template: ${selectedTemplate.padEnd(16)} ${isCorrect ? '✅' : '❌'}`);
    }

    // Test email styling and formatting
    console.log('\n🎨 Testing email styling and formatting...');
    
    const testTemplate = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
        <div style="background: linear-gradient(135deg, #1d5795 0%, #2c7be5 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center;">
          <h1 style="margin: 0; font-size: 28px; font-weight: 600;">🔔 Renewal Reminder</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your service is expiring soon</p>
        </div>
        <div style="background: white; padding: 40px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <h2 style="color: #1d5795; margin-top: 0; font-size: 24px;">Dear {{customer_name}},</h2>
          <p style="color: #6c757d; line-height: 1.8; font-size: 16px;">
            Your <strong>{{renewal_type}}</strong> service expires on <strong>{{expiry_date}}</strong> 
            ({{days_remaining}} days remaining).
          </p>
        </div>
      </div>
    `;

    const styledEmail = notificationService.replaceTemplateVariables(testTemplate, sampleCustomerData);
    
    console.log('   ✅ Email styling test completed');
    console.log('   📧 Styled email preview:');
    console.log('   ' + styledEmail.substring(0, 300).replace(/\s+/g, ' ') + '...');

    // Validate HTML structure
    const hasValidHTML = styledEmail.includes('<div') && styledEmail.includes('</div>') && 
                        styledEmail.includes('style=') && !styledEmail.includes('{{');
    console.log(`   🏗️  Valid HTML structure: ${hasValidHTML ? '✅' : '❌'}`);

    console.log('\n🎉 Email template testing completed successfully!');
    
    return {
      success: true,
      tenant: tenant,
      templatesChecked: renewalTemplateKeys.length,
      scenariosTested: testScenarios.length,
      templateSelectionTests: testCases.length
    };

  } catch (error) {
    console.error('❌ Error testing email templates:', error);
    throw error;
  }
}

/**
 * Test email template customization
 */
async function testTemplateCustomization() {
  try {
    console.log('\n🔧 Testing email template customization...');

    // Get tenant
    const tenant = await models.Tenant.findOne({
      where: { is_active: true },
      attributes: ['id', 'name']
    });

    // Test getting notification settings
    const notificationSettings = await models.NotificationSettings.findOne({
      where: { tenant_id: tenant.id }
    });

    if (!notificationSettings) {
      console.log('⚠️  No notification settings found, creating default...');
      await models.NotificationSettings.create({
        tenant_id: tenant.id,
        email_enabled: true,
        whatsapp_enabled: false
      });
    }

    console.log('✅ Notification settings verified');

    // Test template availability
    const templateKeys = ['renewal_reminder', 'renewal_urgent', 'renewal_overdue'];
    
    for (const templateKey of templateKeys) {
      console.log(`\n📧 Testing template: ${templateKey}`);
      
      // Check if template exists in notification settings
      const settings = await models.NotificationSettings.findOne({
        where: { tenant_id: tenant.id }
      });

      if (settings && settings.email_templates && settings.email_templates[templateKey]) {
        console.log(`   ✅ Template found in settings`);
        console.log(`   📝 Subject: ${settings.email_templates[templateKey].subject}`);
        console.log(`   🎨 Enabled: ${settings.email_templates[templateKey].enabled}`);
      } else {
        console.log(`   ⚠️  Template not found in settings (will use fallback)`);
      }
    }

    console.log('\n✅ Template customization testing completed');
    
    return {
      success: true,
      templatesChecked: templateKeys.length,
      settingsVerified: true
    };

  } catch (error) {
    console.error('❌ Error testing template customization:', error);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting TallyCRM Email Template Testing\n');

    // Phase 1: Test email templates
    const templateResult = await testEmailTemplates();
    
    // Phase 2: Test template customization
    const customizationResult = await testTemplateCustomization();

    console.log('\n🎉 All email template tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`  ✅ Templates checked: ${templateResult.templatesChecked}`);
    console.log(`  ✅ Scenarios tested: ${templateResult.scenariosTested}`);
    console.log(`  ✅ Template selection tests: ${templateResult.templateSelectionTests}`);
    console.log(`  ✅ Customization tests: ${customizationResult.templatesChecked}`);
    console.log(`  ✅ Tenant: ${templateResult.tenant.name}`);

    process.exit(0);

  } catch (error) {
    console.error('\n❌ Email template testing failed:', error);
    process.exit(1);
  }
}

// Run the tests
main();
