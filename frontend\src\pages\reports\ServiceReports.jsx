import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  FaTools, FaChartLine, FaClock, FaUsers, FaFileAlt, FaChartBar,
  FaExclamationTriangle, FaCheckCircle, FaRupeeSign, FaCalendarAlt,
  FaDownload, FaFilter, FaSearch, FaEye, FaArrowUp, FaArrowDown,
  FaEquals, FaChartPie, FaStar, FaChartArea, FaArrowLeft, FaSpinner,
  FaTicketAlt, FaUserTie, FaBuilding, FaPhone, FaEnvelope,
  FaChevronLeft, FaChevronRight, FaInfoCircle, FaCalendar,
  FaUser, FaIdCard, FaMapMarkerAlt, FaHistory, FaList, FaTh,
  FaChevronDown, FaChevronUp, <PERSON><PERSON><PERSON><PERSON><PERSON>ointer, FaLayerGroup, FaDollarSign
} from 'react-icons/fa';
import api from '../../services/api';
import LoadingScreen from '../../components/ui/LoadingScreen';
import Tooltip from '../../components/ui/Tooltip';
import { useViewPreference, PAGE_NAMES } from '../../utils/viewPreferences';

const ServiceReports = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [servicesLoading, setServicesLoading] = useState(false);
  const [callsLoading, setCallsLoading] = useState(false);
  const [filterLoading, setFilterLoading] = useState(false); // New state for filter changes
  const [hasUserInteracted, setHasUserInteracted] = useState(false); // Track user interactions
  const [serviceData, setServiceData] = useState(null);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    statusId: '',
    priority: '',
    assignedTo: '',
    searchTerm: '',
    // New comprehensive filters
    callBillingType: '',
    isOverdue: '',
    statusCategory: 'closed', // Default to completed services (closed category)
    scheduledDateFrom: '',
    scheduledDateTo: '',
    customerId: '',
    // New date filter options
    dateFilter: '', // today, yesterday, thisMonth, lastMonth, thisYear, lastFinancialYear, thisFinancialYear, mentionPeriod
  });
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState(() => {
    // Initialize from URL parameter or default to 'overview'
    return searchParams.get('tab') || 'overview';
  });
  const [showDatePopup, setShowDatePopup] = useState(false);
  const [customFromDate, setCustomFromDate] = useState('');
  const [customToDate, setCustomToDate] = useState('');

  // View preferences for each tab
  const [overviewViewMode, setOverviewViewMode] = useViewPreference(PAGE_NAMES.SERVICE_REPORTS_OVERVIEW, 'table');
  const [servicesViewMode, setServicesViewMode] = useViewPreference(PAGE_NAMES.SERVICE_REPORTS_SERVICES, 'table');
  const [callsViewMode, setCallsViewMode] = useViewPreference(PAGE_NAMES.SERVICE_REPORTS_CALLS, 'table');

  // Collapsible groups and expanded details state
  const [expandedGroups, setExpandedGroups] = useState({});
  const [expandedDetails, setExpandedDetails] = useState({});
  const [groupBy, setGroupBy] = useState('customer');

  // Filter options state
  const [filterOptions, setFilterOptions] = useState({
    statuses: [],
    executives: [],
    customers: [],
    priorities: [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ],
    callBillingTypes: [
      { value: 'free_call', label: 'Free Call' },
      { value: 'amc_call', label: 'AMC Call' },
      { value: 'per_call', label: 'Per Call' }
    ],
    statusCategories: [
      { value: 'open', label: 'Open' },
      { value: 'in_progress', label: 'In Progress' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' },
      { value: 'cancelled', label: 'Cancelled' }
    ]
  }); // 'customer', 'status', 'executive'

  // Pagination state for Services tab
  const [servicesPagination, setServicesPagination] = useState({
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    totalCount: 0
  });

  // Pagination state for Service Calls tab
  const [callsPagination, setCallsPagination] = useState({
    currentPage: 1,
    pageSize: 20,
    totalPages: 0,
    totalCount: 0
  });

  useEffect(() => {
    // Initial load should apply default filters (statusCategory: 'closed' for completed services)
    fetchServiceData();
    fetchFilterOptions();
  }, []);

  // Effect to handle URL parameter changes and sync activeTab
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  // Effect to fetch appropriate data when activeTab changes
  useEffect(() => {
    if (activeTab === 'services') {
      fetchServicesData(servicesPagination.currentPage);
    } else if (activeTab === 'calls') {
      fetchServiceCallsData(callsPagination.currentPage);
    }
  }, [activeTab]);

  // Effect to handle filter changes for quick filters
  useEffect(() => {
    // Fetch data on initial load (to apply default statusCategory: 'closed') or when user interacts with filters
    if (hasUserInteracted || filters.statusCategory === 'closed') {
      console.log('Filters changed, refetching data...', filters); // Debug log
      setFilterLoading(true); // Show subtle loading indicator
      const timeoutId = setTimeout(async () => {
        try {
          // Use showLoader=false for filter changes to prevent page blinking
          await fetchServiceData(false);
          if (activeTab === 'services') {
            await fetchServicesData(1);
          } else if (activeTab === 'calls') {
            await fetchServiceCallsData(1);
          }
        } finally {
          setFilterLoading(false); // Hide loading indicator
        }
      }, 200); // Further reduced debounce for better responsiveness

      return () => clearTimeout(timeoutId);
    }
  }, [filters.statusId, filters.statusCategory, filters.priority, filters.callBillingType, filters.dateFrom, filters.dateTo, filters.dateFilter, hasUserInteracted]); // Watch filter fields including statusCategory

  // Separate effect for search term with debouncing
  useEffect(() => {
    if (hasUserInteracted && filters.searchTerm !== undefined) { // Only trigger after user interaction
      setFilterLoading(true); // Show subtle loading indicator
      const timeoutId = setTimeout(async () => {
        try {
          // Use showLoader=false for search changes to prevent page blinking
          await fetchServiceData(false);
          if (activeTab === 'services') {
            await fetchServicesData(1);
          } else if (activeTab === 'calls') {
            await fetchServiceCallsData(1);
          }
        } finally {
          setFilterLoading(false); // Hide loading indicator
        }
      }, 300); // Reduced debounce for better responsiveness

      return () => clearTimeout(timeoutId);
    }
  }, [filters.searchTerm, hasUserInteracted]); // Watch search term and user interaction

  // Initialize expanded groups when service calls data changes
  useEffect(() => {
    if (serviceData?.serviceCalls && serviceData.serviceCalls.length > 0) {
      const groups = groupDataBy(serviceData.serviceCalls, groupBy);
      const initialExpanded = {};
      Object.keys(groups).forEach(key => {
        initialExpanded[key] = true; // Expand all groups by default
      });
      setExpandedGroups(initialExpanded);
    }
  }, [serviceData?.serviceCalls, groupBy]);

  // Fetch filter options
  const fetchFilterOptions = async () => {
    try {
      // Fetch statuses from service calls filters endpoint
      const statusResponse = await api.get('/service-calls/filters');
      const statusData = statusResponse.data.data;

      console.log('Filter options response:', statusData); // Debug log

      // Fetch customers with multiple pages if needed
      let allCustomers = [];
      let currentPage = 1;
      let hasMoreCustomers = true;

      while (hasMoreCustomers && currentPage <= 5) { // Limit to 5 pages (500 customers max)
        try {
          const customerResponse = await api.get(`/customers?limit=100&page=${currentPage}`);
          const customerData = customerResponse.data.data;

          if (customerData.customers && customerData.customers.length > 0) {
            allCustomers = [...allCustomers, ...customerData.customers];

            // Check if there are more pages
            const pagination = customerResponse.data.pagination;
            hasMoreCustomers = pagination && currentPage < pagination.totalPages;
            currentPage++;
          } else {
            hasMoreCustomers = false;
          }
        } catch (customerError) {
          console.error(`Error fetching customers page ${currentPage}:`, customerError);
          hasMoreCustomers = false;
        }
      }

      // Fetch executives with multiple pages if needed
      let allExecutives = [];
      currentPage = 1;
      let hasMoreExecutives = true;

      while (hasMoreExecutives && currentPage <= 5) { // Limit to 5 pages (500 executives max)
        try {
          const executiveResponse = await api.get(`/executives?limit=100&page=${currentPage}`);
          const executiveData = executiveResponse.data.data;

          if (executiveData.executives && executiveData.executives.length > 0) {
            allExecutives = [...allExecutives, ...executiveData.executives];

            // Check if there are more pages
            const pagination = executiveResponse.data.pagination;
            hasMoreExecutives = pagination && currentPage < pagination.totalPages;
            currentPage++;
          } else {
            hasMoreExecutives = false;
          }
        } catch (executiveError) {
          console.error(`Error fetching executives page ${currentPage}:`, executiveError);
          hasMoreExecutives = false;
        }
      }

      // Map statuses correctly - ensure we have both id and value for filtering
      const mappedStatuses = (statusData.statuses || []).map(status => ({
        id: status.id,
        value: status.value || status.code, // Use value or code for filtering
        label: status.label || status.name,
        name: status.label || status.name,
        category: status.category,
        color: status.color
      }));

      console.log('Mapped statuses:', mappedStatuses); // Debug log

      setFilterOptions(prev => ({
        ...prev,
        statuses: mappedStatuses,
        executives: allExecutives.map(exec => ({
          value: exec.id,
          label: `${exec.first_name || ''} ${exec.last_name || ''}`.trim() || exec.name || 'Unknown'
        })),
        customers: allCustomers.map(customer => ({
          value: customer.id,
          label: customer.company_name || customer.name || 'Unknown Customer'
        }))
      }));
    } catch (error) {
      console.error('Error fetching filter options:', error);
      // Set empty arrays as fallback
      setFilterOptions(prev => ({
        ...prev,
        statuses: [],
        executives: [],
        customers: []
      }));
    }
  };

  // Fetch data for overview and summary (no pagination needed)
  const fetchServiceData = async (showLoader = true) => {
    try {
      if (showLoader) {
        setLoading(true);
      }
      const params = new URLSearchParams();

      // Add all filter parameters
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== '' && key !== 'searchTerm') {
          params.append(key, value);
        }
      });

      // Add search parameter separately
      if (filters.searchTerm && filters.searchTerm.trim()) {
        params.append('search', filters.searchTerm.trim());
      }

      console.log('Fetching service data with params:', params.toString()); // Debug log

      // Fetch both filtered and unfiltered data for proper summary
      const [filteredResponse, totalResponse] = await Promise.all([
        // Filtered data for current view
        api.get(`/service-calls?${params}&limit=100`),
        // Unfiltered data for total counts
        api.get('/service-calls?limit=100') // Just get count, not actual data
      ]);

      const filteredServiceCalls = filteredResponse.data.data.serviceCalls || [];
      const totalCount = totalResponse.data.data.pagination?.totalItems || 0;
      const filteredCount = filteredResponse.data.data.pagination?.totalItems || filteredServiceCalls.length;

      console.log('Service data response:', {
        filtered: filteredResponse.data.data,
        totalCount,
        filteredCount
      }); // Debug log

      setServiceData({
        services: filteredServiceCalls,
        serviceCalls: filteredServiceCalls,
        summary: {
          totalServices: totalCount, // Total unfiltered count
          totalCalls: filteredCount, // Filtered count (completed/resolved)
          openCalls: totalCount - filteredCount, // Calculate open calls
          ...filteredResponse.data.data.summary
        }
      });

      // Reset pagination when filters change
      setServicesPagination(prev => ({ ...prev, currentPage: 1 }));
      setCallsPagination(prev => ({ ...prev, currentPage: 1 }));

    } catch (error) {
      console.error('Error fetching service data:', error);
      toast.error('Failed to load service reports data');
    } finally {
      if (showLoader) {
        setLoading(false);
      }
    }
  };

  // Fetch paginated data for Services tab
  const fetchServicesData = async (page = 1) => {
    try {
      setServicesLoading(true);
      const params = new URLSearchParams();

      // Add all filter parameters
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== '' && key !== 'searchTerm') {
          params.append(key, value);
        }
      });

      // Add search parameter separately
      if (filters.searchTerm && filters.searchTerm.trim()) {
        params.append('search', filters.searchTerm.trim());
      }

      params.append('page', page.toString());
      params.append('limit', servicesPagination.pageSize.toString());

      console.log('Fetching services data with params:', params.toString()); // Debug log
      console.log('Current filters state:', filters); // Debug log

      const response = await api.get(`/service-calls?${params}`);
      const data = response.data.data;

      setServiceData(prev => ({
        ...prev,
        services: data.serviceCalls || []
      }));

      setServicesPagination(prev => ({
        ...prev,
        currentPage: page,
        totalPages: data.pagination?.totalPages || Math.ceil((data.pagination?.totalItems || 0) / prev.pageSize),
        totalCount: data.pagination?.totalItems || 0
      }));

    } catch (error) {
      console.error('Error fetching services data:', error);
      toast.error('Failed to load services data');
    } finally {
      setServicesLoading(false);
    }
  };

  // Fetch paginated data for Service Calls tab
  const fetchServiceCallsData = async (page = 1) => {
    try {
      setCallsLoading(true);
      const params = new URLSearchParams();

      // Add all filter parameters
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== '' && key !== 'searchTerm') {
          params.append(key, value);
        }
      });

      // Add search parameter separately
      if (filters.searchTerm && filters.searchTerm.trim()) {
        params.append('search', filters.searchTerm.trim());
      }

      params.append('page', page.toString());
      params.append('limit', callsPagination.pageSize.toString());

      console.log('Fetching service calls data with params:', params.toString()); // Debug log

      const response = await api.get(`/service-calls?${params}`);
      const data = response.data.data;

      setServiceData(prev => ({
        ...prev,
        serviceCalls: data.serviceCalls || []
      }));

      setCallsPagination(prev => ({
        ...prev,
        currentPage: page,
        totalPages: data.pagination?.totalPages || Math.ceil((data.pagination?.totalItems || 0) / prev.pageSize),
        totalCount: data.pagination?.totalItems || 0
      }));

    } catch (error) {
      console.error('Error fetching service calls data:', error);
      toast.error('Failed to load service calls data');
    } finally {
      setCallsLoading(false);
    }
  };

  // Date calculation functions
  const getDateRange = (filterType) => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    switch (filterType) {
      case 'today':
        return {
          dateFrom: today.toISOString().split('T')[0],
          dateTo: today.toISOString().split('T')[0]
        };

      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        return {
          dateFrom: yesterday.toISOString().split('T')[0],
          dateTo: yesterday.toISOString().split('T')[0]
        };

      case 'thisMonth':
        const thisMonthStart = new Date(currentYear, currentMonth, 1);
        const thisMonthEnd = new Date(currentYear, currentMonth + 1, 0);
        return {
          dateFrom: thisMonthStart.toISOString().split('T')[0],
          dateTo: thisMonthEnd.toISOString().split('T')[0]
        };

      case 'lastMonth':
        const lastMonthStart = new Date(currentYear, currentMonth - 1, 1);
        const lastMonthEnd = new Date(currentYear, currentMonth, 0);
        return {
          dateFrom: lastMonthStart.toISOString().split('T')[0],
          dateTo: lastMonthEnd.toISOString().split('T')[0]
        };

      case 'thisYear':
        const thisYearStart = new Date(currentYear, 0, 1);
        const thisYearEnd = new Date(currentYear, 11, 31);
        return {
          dateFrom: thisYearStart.toISOString().split('T')[0],
          dateTo: thisYearEnd.toISOString().split('T')[0]
        };

      case 'lastFinancialYear':
        // Financial year in India: April 1 to March 31
        const lastFYStart = new Date(currentYear - 1, 3, 1); // April 1 of previous year
        const lastFYEnd = new Date(currentYear, 2, 31); // March 31 of current year
        return {
          dateFrom: lastFYStart.toISOString().split('T')[0],
          dateTo: lastFYEnd.toISOString().split('T')[0]
        };

      case 'thisFinancialYear':
        // Current financial year
        const thisFYStart = new Date(currentYear, 3, 1); // April 1 of current year
        const thisFYEnd = new Date(currentYear + 1, 2, 31); // March 31 of next year
        return {
          dateFrom: thisFYStart.toISOString().split('T')[0],
          dateTo: thisFYEnd.toISOString().split('T')[0]
        };

      default:
        return { dateFrom: '', dateTo: '' };
    }
  };

  const handleDateFilterChange = (filterType) => {
    // Mark that user has interacted with filters
    setHasUserInteracted(true);

    if (filterType === 'mentionPeriod') {
      setShowDatePopup(true);
      return;
    }

    // Handle "All Dates" option (empty value)
    if (!filterType || filterType === '') {
      console.log('Setting All Dates - clearing date filters'); // Debug log
      setFilters(prev => ({
        ...prev,
        dateFilter: '',
        dateFrom: '',
        dateTo: ''
      }));
      return;
    }

    const dateRange = getDateRange(filterType);
    console.log('Setting date filter:', filterType, 'Range:', dateRange); // Debug log
    setFilters(prev => ({
      ...prev,
      dateFilter: filterType,
      dateFrom: dateRange.dateFrom,
      dateTo: dateRange.dateTo
    }));
  };

  const handleCustomDateRange = (fromDate, toDate) => {
    // Mark that user has interacted with filters
    setHasUserInteracted(true);
    setFilters(prev => ({
      ...prev,
      dateFilter: 'mentionPeriod',
      dateFrom: fromDate,
      dateTo: toDate
    }));
    setShowDatePopup(false);
    setCustomFromDate('');
    setCustomToDate('');
  };

  const handleFilterChange = (key, value) => {
    console.log(`Filter changed: ${key} = ${value}`); // Debug log
    // Mark that user has interacted with filters
    setHasUserInteracted(true);

    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };

      // If user selects "All Status" (statusId = ''), also clear statusCategory to show all services
      if (key === 'statusId' && value === '') {
        newFilters.statusCategory = '';
        console.log('Cleared statusCategory because All Status was selected');
      }

      // If user selects "All Categories" (statusCategory = ''), also clear statusId to avoid conflicts
      if (key === 'statusCategory' && value === '') {
        newFilters.statusId = '';
        console.log('Cleared statusId because All Categories was selected');
      }

      return newFilters;
    });
  };

  const handleApplyFilters = () => {
    // Use showLoader=false for filter application to prevent page blinking
    fetchServiceData(false);
    // Reset pagination and fetch data for current tab
    if (activeTab === 'services') {
      fetchServicesData(1);
    } else if (activeTab === 'calls') {
      fetchServiceCallsData(1);
    }
    setShowFilters(false);
  };

  // Handle tab change and fetch appropriate data
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Update URL parameter to preserve tab state on reload
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', tab);
    setSearchParams(newSearchParams);

    if (tab === 'services') {
      fetchServicesData(servicesPagination.currentPage);
    } else if (tab === 'calls') {
      fetchServiceCallsData(callsPagination.currentPage);
    }
  };

  const handleExport = async (format = 'csv') => {
    try {
      const exportData = activeTab === 'services' ? serviceData?.services : serviceData?.serviceCalls;
      if (!exportData || exportData.length === 0) {
        toast.error('No data to export');
        return;
      }

      // Prepare data for export based on active tab
      let formattedData = [];
      let filename = '';

      if (activeTab === 'services') {
        formattedData = exportData.map(service => ({
          // Basic Service Information
          'Service Number': service.serviceNumber || service.call_number || 'N/A',
          'Customer': service.customer?.company_name || service.customer?.display_name || service.customer_name || 'N/A',
          'Contact Person': service.contactPerson || service.contact_person || 'N/A',
          'Subject': service.subject || 'N/A',
          'Description': service.description || 'N/A',

          // Service Type & Status
          'Service Type': service.type || service.call_type || 'N/A',
          'Call Type': service.callBillingType || service.call_billing_type || 'N/A',
          'Status': service.status?.name || service.status || 'N/A',
          'Priority': service.priority || 'N/A',

          // Assignment & Contact
          'Assigned To': service.assignedTo || service.assigned_executive?.name || 'Unassigned',
          'Phone': service.customer?.phone || service.contact_phone || 'N/A',
          'Email': service.customer?.email || service.contact_email || 'N/A',

          // Dates & Time
          'Created Date': service.createdDate ? new Date(service.createdDate).toLocaleDateString() : 'N/A',
          'Scheduled Date': service.scheduledDate ? new Date(service.scheduledDate).toLocaleDateString() : 'N/A',
          'Completed Date': service.completedDate ? new Date(service.completedDate).toLocaleDateString() : 'N/A',
          'Estimated Hours': service.estimatedHours || service.estimated_hours || '0',
          'Actual Hours': service.actualHours || service.actual_hours || '0',

          // Financial Information
          'Service Amount': service.amount || service.service_charges || '0',
          'Currency': service.currency || 'INR',
          'Charging Type': service.charging_type || 'fixed',
          'Payment Status': service.payment_status || 'Pending',

          // Location & Address
          'Service Address': service.service_address || 'N/A',
          'City': service.customer?.city || 'N/A',
          'State': service.customer?.state || 'N/A',

          // Tally & Technical Details
          'Tally Serial Number': service.tally_serial_number || 'N/A',
          'TSS Status': service.tss_status || 'N/A',
          'Company Name': service.customer?.company_name || 'N/A'
        }));
        filename = 'service_requests_report';
      } else {
        formattedData = exportData.map(call => ({
          // Basic Call Information
          'Call Number': call.callNumber || call.call_number || 'N/A',
          'Customer': call.customer?.company_name || call.customer?.display_name || call.customer_name || 'N/A',
          'Subject': call.subject || 'N/A',
          'Description': call.description || 'N/A',

          // Call Type & Status
          'Type': call.type || call.call_type || 'N/A',
          'Call Type': call.call_billing_type || 'N/A',
          'Status': call.status?.name || call.status || 'N/A',
          'Priority': call.priority || 'N/A',

          // Assignment & Contact
          'Assigned To': call.assignedTo || call.assigned_executive?.name || 'Unassigned',
          'Technician Phone': call.assigned_executive?.phone || 'N/A',
          'Technician Email': call.assigned_executive?.email || 'N/A',

          // Customer Contact
          'Customer Phone': call.customer?.phone || 'N/A',
          'Customer Email': call.customer?.email || 'N/A',
          'Contact Person': call.contact_person || 'N/A',

          // Dates & Time
          'Created Date': call.createdDate ? new Date(call.createdDate).toLocaleDateString() : 'N/A',
          'Scheduled Date': call.scheduledDate ? new Date(call.scheduledDate).toLocaleDateString() : 'N/A',
          'Resolution Date': call.resolutionDate ? new Date(call.resolutionDate).toLocaleDateString() : 'N/A',
          'Response Time (hours)': call.responseTime || '0',
          'Resolution Time (hours)': call.resolutionTime || '0',
          'Estimated Hours': call.estimated_hours || '0',
          'Actual Hours': call.actual_hours || '0',

          // Financial Information
          'Service Amount': call.service_charges || '0',
          'Currency': call.currency || 'INR',
          'Payment Status': call.payment_status || 'Pending',

          // Location & Technical
          'Service Address': call.service_address || 'N/A',
          'City': call.customer?.city || 'N/A',
          'State': call.customer?.state || 'N/A',
          'Tally Serial Number': call.tally_serial_number || 'N/A',
          'TSS Status': call.tss_status || 'N/A',
          'Company Name': call.customer?.company_name || 'N/A',

          // Progress & Status
          'Progress': call.progress || '0%',
          'Hours Spent': call.hours_spent || '0.00h',
          'Amount': call.amount || 'Not set'
        }));
        filename = 'service_calls_report';
      }

      if (format === 'csv') {
        // Generate CSV content
        const headers = Object.keys(formattedData[0] || {});
        const csvContent = [
          headers.join(','),
          ...formattedData.map(row =>
            headers.map(header => {
              const value = row[header] || '';
              // Escape quotes and wrap in quotes if contains comma, quote, or newline
              const stringValue = value.toString();
              if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
                return `"${stringValue.replace(/"/g, '""')}"`;
              }
              return stringValue;
            }).join(',')
          )
        ].join('\n');

        // Download CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        toast.success(`${formattedData.length} records exported to CSV successfully`);
      } else {
        toast.error(`${format.toUpperCase()} export not yet implemented`);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data');
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';

    // Handle both string and object status
    const statusText = typeof status === 'string' ? status : (status.name || status.status || '');

    switch (statusText.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'open': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    if (!priority) return 'bg-gray-100 text-gray-800';

    // Handle both string and object priority
    const priorityText = typeof priority === 'string' ? priority : (priority.name || priority.priority || '');

    switch (priorityText.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString, showTime = false) => {
    if (!dateString) return 'Not Available';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';

      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      };

      if (showTime) {
        options.hour = '2-digit';
        options.minute = '2-digit';
      }

      return date.toLocaleDateString('en-US', options);
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const getDisplayValue = (value, fallback = 'Not Available') => {
    if (value === null || value === undefined || value === '') return fallback;
    return value;
  };

  const getServiceNumber = (service) => {
    return service?.serviceNumber || service?.call_number || service?.callNumber || 'No Service Number';
  };

  const getCustomerName = (service) => {
    return service?.customer?.company_name || service?.customer?.name || 'Unknown Customer';
  };

  const getCallDate = (service) => {
    // Try different date fields based on service status
    const callDate = service?.callDate || service?.call_date;
    const createdAt = service?.createdAt || service?.created_at;
    const updatedAt = service?.updatedAt || service?.updated_at;
    const completedAt = service?.completedAt || service?.completed_at;
    const lastServedDate = service?.lastServedDate || service?.last_served_date;

    // If service is completed, show completion date
    if (service?.status === 'completed' || service?.status === 'Completed') {
      return completedAt || lastServedDate || callDate || createdAt;
    }

    // Otherwise show call date or creation date
    return callDate || createdAt;
  };

  const getExecutiveName = (service) => {
    // Debug logging to understand the data structure
    if (service && Object.keys(service).length > 0) {
      console.log('Service data structure:', {
        id: service.id,
        keys: Object.keys(service),
        assignedExecutive: service.assignedExecutive,
        assigned_executive: service.assigned_executive,
        executive: service.executive,
        assignedTo: service.assignedTo,
        assigned_to: service.assigned_to,
        user: service.user,
        created_by: service.created_by,
        fullService: service
      });
    }

    // Try multiple possible data structure variations
    const executiveName =
      service?.assignedExecutive?.name ||
      service?.assigned_executive?.name ||
      service?.executive?.name ||
      service?.assignedTo?.name ||
      service?.assignedExecutive?.first_name ||
      service?.assigned_executive?.first_name ||
      service?.executive?.first_name ||
      service?.assignedTo?.first_name ||
      service?.assignedExecutive ||
      service?.assigned_executive ||
      service?.executive ||
      service?.assignedTo ||
      service?.assigned_to ||
      service?.user?.name ||
      service?.user?.first_name ||
      service?.created_by?.name ||
      service?.created_by?.first_name;

    // If we have a string, return it
    if (typeof executiveName === 'string' && executiveName.trim()) {
      return executiveName.trim();
    }

    // If we have an object with name properties, try to construct the name
    if (typeof executiveName === 'object' && executiveName) {
      const firstName = executiveName.first_name || executiveName.firstName || '';
      const lastName = executiveName.last_name || executiveName.lastName || '';
      const fullName = `${firstName} ${lastName}`.trim();

      if (fullName) return fullName;
      if (executiveName.name) return executiveName.name;
      if (executiveName.username) return executiveName.username;
      if (executiveName.email) return executiveName.email;
    }

    return 'Unassigned';
  };

  const getServiceStatus = (service) => {
    if (typeof service?.status === 'string') return service.status;
    return service?.status?.name || service?.status?.status || 'Unknown Status';
  };

  // Visual status indicators - removed emoji icons as requested
  const getStatusIcon = (status) => {
    // Emojis removed - return empty string
    return '';
  };

  const getPriorityIcon = (priority) => {
    // Emojis removed - return empty string
    return '';
  };

  // Group data by specified field
  const groupDataBy = (data, field) => {
    const groups = {};
    data.forEach(item => {
      let key;
      switch (field) {
        case 'customer':
          key = getCustomerName(item);
          break;
        case 'status':
          key = getServiceStatus(item);
          break;
        case 'executive':
          key = getExecutiveName(item);
          break;
        default:
          key = 'Other';
      }

      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
    });

    // Sort groups by count (descending)
    return Object.entries(groups)
      .sort(([,a], [,b]) => b.length - a.length)
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {});
  };

  // Toggle group expansion
  const toggleGroup = (groupKey) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }));
  };

  // Toggle detail expansion
  const toggleDetails = (itemId) => {
    setExpandedDetails(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // Enhanced Empty State Component
  const EmptyState = ({ type, title, description, icon: Icon = FaExclamationTriangle }) => (
    <div className="text-center py-12 px-6">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <Icon className="h-12 w-12 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6 max-w-md mx-auto">{description}</p>
      <div className="space-y-2 text-sm text-gray-400">
        <p>• Check your filters and date range</p>
        <p>• Ensure data has been properly synced</p>
        <p>• Try refreshing the page</p>
      </div>
    </div>
  );

  // View Toggle Component
  const ViewToggle = ({ viewMode, setViewMode, className = "" }) => (
    <div className={`flex items-center space-x-2 ${className}`}>
      <button
        onClick={() => setViewMode('table')}
        className={`p-2 rounded-lg transition-colors ${
          viewMode === 'table'
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-400 hover:text-gray-600'
        }`}
        title="Table View"
      >
        <FaList className="h-4 w-4" />
      </button>
      <button
        onClick={() => setViewMode('card')}
        className={`p-2 rounded-lg transition-colors ${
          viewMode === 'card'
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-400 hover:text-gray-600'
        }`}
        title="Card View"
      >
        <FaTh className="h-4 w-4" />
      </button>
    </div>
  );

  // Group Toggle Component
  const GroupToggle = ({ groupBy, setGroupBy, className = "" }) => (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-sm text-gray-500 mr-2">Group by:</span>
      <select
        value={groupBy}
        onChange={(e) => setGroupBy(e.target.value)}
        className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="customer">Customer</option>
        <option value="status">Status</option>
        <option value="executive">Executive</option>
      </select>
    </div>
  );

  // Pagination component
  const renderPagination = (pagination, onPageChange) => {
    if (pagination.totalPages <= 1) return null;

    const { currentPage, totalPages, totalCount } = pagination;
    const startRecord = (currentPage - 1) * pagination.pageSize + 1;
    const endRecord = Math.min(currentPage * pagination.pageSize, totalCount);

    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{startRecord}</span> to{' '}
              <span className="font-medium">{endRecord}</span> of{' '}
              <span className="font-medium">{totalCount}</span> results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaChevronLeft className="h-3 w-3" />
              </button>

              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      pageNum === currentPage
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FaChevronRight className="h-3 w-3" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    );
  };

  const renderSummaryCards = () => {
    if (!serviceData?.summary) return null;

    const summary = serviceData.summary;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Total Services</p>
                <p className="text-3xl font-bold text-white">{summary.totalServices || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaTools size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#28a745' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Completed Services</p>
                <p className="text-3xl font-bold text-white">{summary.totalCalls || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaCheckCircle size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#dc3545' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Open Services</p>
                <p className="text-3xl font-bold text-white">{summary.openCalls || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaExclamationTriangle size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Avg Resolution</p>
                <p className="text-3xl font-bold text-white">{summary.avgResolutionTime || 0}h</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaClock size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 mb-6 ${showFilters ? 'block' : 'hidden'}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Comprehensive Filter Options</h3>

      {/* Date Range Display */}
      {(filters.dateFrom || filters.dateTo) && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3">Selected Date Range</h4>
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            <strong>Selected Period:</strong> {filters.dateFrom} to {filters.dateTo}
          </div>
        </div>
      )}

      {/* Status and Priority Filters */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3">Status & Priority Filters</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              value={filters.statusId}
              onChange={(e) => handleFilterChange('statusId', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              {filterOptions.statuses.map(status => (
                <option key={status.id} value={status.id}>{status.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status Category</label>
            <select
              value={filters.statusCategory}
              onChange={(e) => handleFilterChange('statusCategory', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              {filterOptions.statusCategories.map(category => (
                <option key={category.value} value={category.value}>{category.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Priorities</option>
              {filterOptions.priorities.map(priority => (
                <option key={priority.value} value={priority.value}>{priority.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Overdue Status</label>
            <select
              value={filters.isOverdue}
              onChange={(e) => handleFilterChange('isOverdue', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Services</option>
              <option value="true">Overdue Only</option>
              <option value="false">Not Overdue</option>
            </select>
          </div>
        </div>
      </div>

      {/* Service Type and Assignment Filters */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3">Service Type & Assignment Filters</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Service Type</label>
            <select
              value={filters.callBillingType}
              onChange={(e) => handleFilterChange('callBillingType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Service Types</option>
              {filterOptions.callBillingTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Customer</label>
            <select
              value={filters.customerId}
              onChange={(e) => handleFilterChange('customerId', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Customers</option>
              {filterOptions.customers.map(customer => (
                <option key={customer.value} value={customer.value}>{customer.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Assigned Executive</label>
            <select
              value={filters.assignedTo}
              onChange={(e) => handleFilterChange('assignedTo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Executives</option>
              {filterOptions.executives.map(executive => (
                <option key={executive.value} value={executive.value}>{executive.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Search Filter */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3">Search Filter</h4>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search Term</label>
            <input
              type="text"
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              placeholder="Search by service number, customer name, description..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-4 border-t border-gray-200">
        <button
          onClick={() => {
            setFilters({
              dateFrom: '',
              dateTo: '',
              statusId: '',
              priority: '',
              assignedTo: '',
              searchTerm: '',
              callBillingType: '',
              isOverdue: '',
              statusCategory: '',
              scheduledDateFrom: '',
              scheduledDateTo: '',
              customerId: '',
              dateFilter: ''
            });
          }}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Clear All Filters
        </button>
        <div className="flex gap-2">
          <button
            onClick={() => setShowFilters(false)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
            style={{ backgroundColor: '#15579e' }}
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );

  const renderTabNavigation = () => (
    <div className="bg-white rounded-lg border border-gray-200 mb-6">
      <div className="border-b border-gray-200">
        <div className="flex justify-between items-center px-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => handleTabChange('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <FaChartBar className="inline mr-2" />
              Overview
            </button>
            <button
              onClick={() => handleTabChange('services')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'services'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <FaTools className="inline mr-2" />
              Services ({servicesPagination.totalCount || serviceData?.services?.length || 0})
            </button>
            <button
              onClick={() => handleTabChange('calls')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'calls'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <FaTicketAlt className="inline mr-2" />
              Service Calls ({callsPagination.totalCount || serviceData?.serviceCalls?.length || 0})
            </button>
          </nav>

          {/* Date Filters & Quick Filters & View Toggle */}
          <div className="flex items-center gap-4">
            {/* Date Filter Dropdown - Show for all tabs */}
            <div className="hidden lg:flex items-center gap-1">
              <FaCalendar className="text-gray-400" size={14} />
              <select
                value={filters.dateFilter}
                onChange={(e) => {
                  const selectedFilter = e.target.value;
                  console.log('Date filter changed:', selectedFilter); // Debug log
                  handleDateFilterChange(selectedFilter);
                }}
                className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white min-w-[120px]"
                disabled={filterLoading}
              >
                <option value="">All Dates</option>
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="thisMonth">This Month</option>
                <option value="lastMonth">Last Month</option>
                <option value="thisYear">This Year</option>
                <option value="lastFinancialYear">Last FY</option>
                <option value="thisFinancialYear">This FY</option>
                <option value="mentionPeriod">Custom</option>
              </select>
              {filterLoading && (
                <div className="flex items-center justify-center ml-1">
                  <FaSpinner className="animate-spin text-blue-500 text-xs" />
                </div>
              )}
            </div>

            {/* Quick Filters - Show only for services and calls tabs */}
            {(activeTab === 'services' || activeTab === 'calls') && (
              <div className="hidden lg:flex items-center gap-3">
                {/* Show All Services Button */}
                <button
                  onClick={() => {
                    console.log('Show All Services clicked - clearing all filters');
                    setFilters(prev => ({
                      ...prev,
                      statusId: '',
                      statusCategory: '',
                      dateFrom: '',
                      dateTo: '',
                      dateFilter: '',
                      searchTerm: '',
                      callBillingType: '',
                      priority: '',
                      assignedTo: ''
                    }));
                    setHasUserInteracted(true);
                  }}
                  className="flex items-center gap-1 px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
                  disabled={filterLoading}
                >
                  <FaList size={12} />
                  Show All
                </button>
                {/* Status Filter */}
                <div className="flex items-center gap-1">
                  <FaCheckCircle className="text-gray-400" size={14} />
                  <select
                    value={filters.statusId}
                    onChange={(e) => {
                      const newStatusId = e.target.value;
                      console.log('Status filter changed:', newStatusId); // Debug log
                      handleFilterChange('statusId', newStatusId);
                    }}
                    className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white min-w-[100px]"
                    disabled={filterLoading}
                  >
                    <option value="">All Status</option>
                    {filterOptions.statuses.map(status => (
                      <option key={status.id} value={status.id}>{status.label || status.name}</option>
                    ))}
                  </select>
                </div>

                {/* Service Type Filter */}
                <div className="flex items-center gap-1">
                  <FaTools className="text-gray-400" size={14} />
                  <select
                    value={filters.callBillingType}
                    onChange={(e) => {
                      const newServiceType = e.target.value;
                      console.log('Service type filter changed:', newServiceType); // Debug log
                      handleFilterChange('callBillingType', newServiceType);
                    }}
                    className="text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 bg-white min-w-[100px]"
                    disabled={filterLoading}
                  >
                    <option value="">All Types</option>
                    {filterOptions.callBillingTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>
                {/* Show loading indicator for filter section */}
                {filterLoading && (
                  <div className="flex items-center text-xs text-blue-600">
                    <FaSpinner className="animate-spin mr-1" size={12} />
                    <span>Updating...</span>
                  </div>
                )}
              </div>
            )}

            {/* View Toggle */}
            <div className="hidden sm:block">
              {activeTab === 'services' && (
                <ViewToggle
                  viewMode={servicesViewMode}
                  setViewMode={setServicesViewMode}
                />
              )}
              {activeTab === 'calls' && (
                <ViewToggle
                  viewMode={callsViewMode}
                  setViewMode={setCallsViewMode}
                />
              )}
              {activeTab === 'overview' && (
                <ViewToggle
                  viewMode={overviewViewMode}
                  setViewMode={setOverviewViewMode}
                />
              )}
            </div>
          </div>


        </div>
      </div>
    </div>
  );

  const renderOverviewContent = () => (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Status Distribution */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Status Distribution</h3>
        <div className="space-y-3">
          {serviceData?.summary?.byStatus ? Object.entries(serviceData.summary.byStatus).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status)}`}>
                {status}
              </span>
              <span className="text-lg font-bold text-gray-900">{count}</span>
            </div>
          )) : (
            <p className="text-gray-500">No status data available</p>
          )}
        </div>
      </div>

      {/* Priority Distribution */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Priority Distribution</h3>
        <div className="space-y-3">
          {serviceData?.summary?.byPriority ? Object.entries(serviceData.summary.byPriority).map(([priority, count]) => (
            <div key={priority} className="flex items-center justify-between">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(priority)}`}>
                {priority}
              </span>
              <span className="text-lg font-bold text-gray-900">{count}</span>
            </div>
          )) : (
            <p className="text-gray-500">No priority data available</p>
          )}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 lg:col-span-2">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Service Activity</h3>
          <div className="flex items-center gap-2">
            <Tooltip content="Latest 5 service activities with detailed information">
              <FaInfoCircle className="text-gray-400 cursor-help" />
            </Tooltip>
            <div className="sm:hidden">
              <ViewToggle
                viewMode={overviewViewMode}
                setViewMode={setOverviewViewMode}
              />
            </div>
          </div>
        </div>

        {overviewViewMode === 'table' ? (
          <div className="space-y-4">
            {serviceData?.services?.slice(0, 5).map((service, index) => (
              <div key={service.id || index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center flex-1">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: '#15579e' }}>
                    <FaTools className="text-white" size={18} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Tooltip content={`Service Number: ${getServiceNumber(service)}`}>
                        <p className="text-sm font-medium text-gray-900 truncate cursor-help">
                          {getServiceNumber(service)}
                        </p>
                      </Tooltip>
                      {service.priority && (
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(service.priority)}`}>
                          {typeof service.priority === 'string' ? service.priority : (service.priority?.name || 'Normal')}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <Tooltip content={`Customer: ${getCustomerName(service)}`}>
                        <div className="flex items-center cursor-help">
                          <FaBuilding className="mr-1" size={10} />
                          <span className="truncate max-w-32">{getCustomerName(service)}</span>
                        </div>
                      </Tooltip>
                      <Tooltip content={`Assigned Executive: ${getExecutiveName(service)}`}>
                        <div className="flex items-center cursor-help">
                          <FaUser className="mr-1" size={10} />
                          <span className="truncate max-w-24">{getExecutiveName(service)}</span>
                        </div>
                      </Tooltip>
                      {service.description && (
                        <Tooltip content={`Description: ${service.description}`}>
                          <div className="flex items-center cursor-help">
                            <FaFileAlt className="mr-1" size={10} />
                            <span className="truncate max-w-32">{service.description}</span>
                          </div>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </div>
                <div className="text-right ml-4">
                  <Tooltip content={`Status: ${getServiceStatus(service)}`}>
                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(service.status)} cursor-help`}>
                      {getServiceStatus(service)}
                    </span>
                  </Tooltip>
                  <div className="flex items-center justify-end gap-2 mt-2 text-xs text-gray-500">
                    <Tooltip content={`Service Date: ${formatDate(getCallDate(service), true)}`}>
                      <div className="flex items-center cursor-help">
                        <FaCalendar className="mr-1" size={10} />
                        <span>{formatDate(getCallDate(service))}</span>
                      </div>
                    </Tooltip>
                    <Tooltip content={`Last Updated: ${formatDate(service.updatedAt || service.createdAt, true)}`}>
                      <div className="flex items-center cursor-help">
                        <FaHistory className="mr-1" size={10} />
                        <span>{formatDate(service.updatedAt || service.createdAt)}</span>
                      </div>
                    </Tooltip>
                  </div>
                </div>
              </div>
            )) || (
              <EmptyState
                type="overview"
                title="No Recent Service Activities"
                description="Service activities will appear here once data is available. Recent activities help you track the latest service requests and their progress."
                icon={FaTools}
              />
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {serviceData?.services?.slice(0, 6).map((service, index) => (
              <div key={service.id || index} className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: '#15579e' }}>
                      <FaTools className="text-white" size={16} />
                    </div>
                    <div>
                      <Tooltip content={`Service Number: ${getServiceNumber(service)}`}>
                        <h6 className="font-bold text-gray-900 text-sm cursor-help">{getServiceNumber(service)}</h6>
                      </Tooltip>
                      <Tooltip content={`Customer: ${getCustomerName(service)}`}>
                        <p className="text-xs text-gray-600 cursor-help truncate max-w-24">{getCustomerName(service)}</p>
                      </Tooltip>
                    </div>
                  </div>
                  {service.priority && (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(service.priority)}`}>
                      {typeof service.priority === 'string' ? service.priority : (service.priority?.name || 'Normal')}
                    </span>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Status:</span>
                    <Tooltip content={`Status: ${getServiceStatus(service)}`}>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(service.status)} cursor-help`}>
                        {getServiceStatus(service)}
                      </span>
                    </Tooltip>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-gray-500">Executive:</span>
                      <Tooltip content={`Assigned Executive: ${getExecutiveName(service)}`}>
                        <p className="font-medium cursor-help truncate">{getExecutiveName(service)}</p>
                      </Tooltip>
                    </div>
                    <div>
                      <span className="text-gray-500">Date:</span>
                      <Tooltip content={`Service Date: ${formatDate(getCallDate(service), true)}`}>
                        <p className="font-medium cursor-help">{formatDate(getCallDate(service))}</p>
                      </Tooltip>
                    </div>
                  </div>

                  {service.description && (
                    <div>
                      <span className="text-xs text-gray-500">Description:</span>
                      <Tooltip content={`Description: ${service.description}`}>
                        <p className="text-xs text-gray-700 cursor-help truncate">{service.description}</p>
                      </Tooltip>
                    </div>
                  )}
                </div>

                <div className="flex justify-end mt-3">
                  <button
                    className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                    onClick={() => navigate(`/services/${service.id}`)}
                  >
                    <FaEye className="mr-1" size={10} />
                    View Details
                  </button>
                </div>
              </div>
            )) || (
              <div className="col-span-full">
                <EmptyState
                  type="overview"
                  title="No Recent Service Activities"
                  description="Service activities will appear here once data is available. Recent activities help you track the latest service requests and their progress."
                  icon={FaTools}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  const renderServicesContent = () => {
    const services = serviceData?.services || [];

    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              Services ({servicesPagination.totalCount} total records)
            </h3>
            <div className="flex items-center gap-4">
              <div className="flex items-center text-sm text-gray-500">
                <FaChartBar className="mr-2" />
                Real-time data
              </div>
              <div className="sm:hidden">
                <ViewToggle
                  viewMode={servicesViewMode}
                  setViewMode={setServicesViewMode}
                />
              </div>
            </div>
          </div>
        </div>

        {servicesLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="flex items-center justify-center space-x-3">
              <FaSpinner className="animate-spin text-blue-500 text-2xl" />
              <span className="text-gray-600">Loading services...</span>
            </div>
          </div>
        ) : services.length > 0 ? (
          servicesViewMode === 'table' ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Task 7: Enhanced table headers with all required columns */}
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaIdCard className="mr-1" size={12} />
                      Service ID
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaBuilding className="mr-1" size={12} />
                      Customer Name
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaTools className="mr-1" size={12} />
                      Type
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaCheckCircle className="mr-1" size={12} />
                      Status
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaStar className="mr-1" size={12} />
                      Priority
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaUserTie className="mr-1" size={12} />
                      Assigned To
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaCalendar className="mr-1" size={12} />
                      Created Date
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaCalendar className="mr-1" size={12} />
                      Scheduled Date
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaCalendar className="mr-1" size={12} />
                      Completed Date
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaHistory className="mr-1" size={12} />
                      Hours Spent
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FaDollarSign className="mr-1" size={12} />
                      Amount
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {services.map((service, index) => (
                  <tr key={service.id || index} className="hover:bg-gray-50">
                    {/* Task 7: Enhanced table cells with all required columns */}

                    {/* Service ID */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        <Tooltip content={`Service ID: ${service.id || 'N/A'}`}>
                          <span className="cursor-help">{service.id || 'N/A'}</span>
                        </Tooltip>
                      </div>
                      <div className="text-xs text-gray-500">
                        {getServiceNumber(service)}
                      </div>
                    </td>

                    {/* Customer Name */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <Tooltip content={`Customer: ${getCustomerName(service)}`}>
                          <div className="flex items-center cursor-help">
                            <FaBuilding className="mr-1" size={12} />
                            <span className="truncate max-w-32">{getCustomerName(service)}</span>
                          </div>
                        </Tooltip>
                      </div>
                    </td>

                    {/* Type */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <Tooltip content={`Service Type: ${service.typeOfCall?.name || service.call_billing_type || 'N/A'}`}>
                          <span className="cursor-help">
                            {service.typeOfCall?.name || service.call_billing_type || 'N/A'}
                          </span>
                        </Tooltip>
                      </div>
                    </td>

                    {/* Status */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <Tooltip content={`Status: ${getServiceStatus(service)}`}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(service.status)} cursor-help`}>
                          {getServiceStatus(service)}
                        </span>
                      </Tooltip>
                    </td>

                    {/* Priority */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <Tooltip content={`Priority: ${service.priority || 'N/A'}`}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(service.priority)} cursor-help`}>
                          {service.priority || 'N/A'}
                        </span>
                      </Tooltip>
                    </td>

                    {/* Assigned To */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <Tooltip content={`Assigned to: ${getExecutiveName(service)}`}>
                        <div className="flex items-center text-sm text-gray-900 cursor-help">
                          <FaUserTie className="mr-1" size={12} />
                          <span className="truncate max-w-24">{getExecutiveName(service)}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Created Date */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      <Tooltip content={`Created: ${formatDate(service.createdAt, true)}`}>
                        <div className="flex items-center cursor-help">
                          <FaCalendar className="mr-1" size={12} />
                          <span>{formatDate(service.createdAt)}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Scheduled Date */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      <Tooltip content={`Scheduled: ${formatDate(service.scheduled_date || service.scheduledDate, true)}`}>
                        <div className="flex items-center cursor-help">
                          <FaCalendar className="mr-1" size={12} />
                          <span>{formatDate(service.scheduled_date || service.scheduledDate)}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Completed Date */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                      <Tooltip content={`Completed: ${formatDate(service.completed_date || service.completedDate, true)}`}>
                        <div className="flex items-center cursor-help">
                          <FaCalendar className="mr-1" size={12} />
                          <span>{formatDate(service.completed_date || service.completedDate)}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Hours Spent */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <Tooltip content={`Hours Spent: ${service.hours_spent || service.hoursSpent || 'N/A'}`}>
                        <div className="flex items-center cursor-help">
                          <FaHistory className="mr-1" size={12} />
                          <span>{service.hours_spent || service.hoursSpent || 'N/A'}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Amount */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <Tooltip content={`Amount: ${service.amount ? `₹${service.amount}` : 'N/A'}`}>
                        <div className="flex items-center cursor-help">
                          <FaDollarSign className="mr-1" size={12} />
                          <span>{service.amount ? `₹${service.amount}` : 'N/A'}</span>
                        </div>
                      </Tooltip>
                    </td>

                    {/* Actions */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                        onClick={() => navigate(`/services/${service.id}`)}
                      >
                        <FaEye className="mr-1" size={12} />
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {services.map((service, index) => (
                  <div key={service.id || index} className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: '#15579e' }}>
                          <FaTools className="text-white" size={16} />
                        </div>
                        <div>
                          <Tooltip content={`Service Number: ${getServiceNumber(service)}`}>
                            <h6 className="font-bold text-gray-900 text-sm cursor-help">{getServiceNumber(service)}</h6>
                          </Tooltip>
                          <Tooltip content={`Customer: ${getCustomerName(service)}`}>
                            <p className="text-xs text-gray-600 cursor-help truncate max-w-32">{getCustomerName(service)}</p>
                          </Tooltip>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">Status:</span>
                        <Tooltip content={`Status: ${getServiceStatus(service)} - Assigned to: ${getExecutiveName(service)}`}>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(service.status)} cursor-help`}>
                            {getServiceStatus(service)}
                          </span>
                        </Tooltip>
                      </div>

                      <div className="grid grid-cols-2 gap-3 text-xs">
                        <div>
                          <span className="text-gray-500">Executive:</span>
                          <Tooltip content={`Assigned Executive: ${getExecutiveName(service)}`}>
                            <p className="font-medium cursor-help flex items-center">
                              <FaUserTie className="mr-1" size={10} />
                              <span className="truncate">{getExecutiveName(service)}</span>
                            </p>
                          </Tooltip>
                        </div>
                        <div>
                          <span className="text-gray-500">Service Date:</span>
                          <Tooltip content={`Service Date: ${formatDate(getCallDate(service), true)}`}>
                            <p className="font-medium cursor-help flex items-center">
                              <FaCalendar className="mr-1" size={10} />
                              <span>{formatDate(getCallDate(service))}</span>
                            </p>
                          </Tooltip>
                        </div>
                        {service.customer?.contact_person && (
                          <div>
                            <span className="text-gray-500">Contact:</span>
                            <Tooltip content={`Contact Person: ${service.customer.contact_person}`}>
                              <p className="font-medium cursor-help flex items-center">
                                <FaUser className="mr-1" size={10} />
                                <span className="truncate">{service.customer.contact_person}</span>
                              </p>
                            </Tooltip>
                          </div>
                        )}
                        <div>
                          <span className="text-gray-500">Created:</span>
                          <Tooltip content={`Created: ${formatDate(service.createdAt, true)}`}>
                            <p className="font-medium cursor-help flex items-center">
                              <FaHistory className="mr-1" size={10} />
                              <span>{formatDate(service.createdAt)}</span>
                            </p>
                          </Tooltip>
                        </div>
                      </div>

                      {service.description && (
                        <div>
                          <span className="text-xs text-gray-500">Description:</span>
                          <Tooltip content={service.description ? `Description: ${service.description}` : 'No description provided'}>
                            <p className="text-xs text-gray-700 cursor-help truncate">{getDisplayValue(service.description, 'No description provided')}</p>
                          </Tooltip>
                        </div>
                      )}
                    </div>

                    <div className="flex justify-end mt-4 pt-3 border-t border-gray-200">
                      <button
                        className="text-xs text-blue-600 hover:text-blue-800 flex items-center font-medium"
                        onClick={() => navigate(`/services/${service.id}`)}
                      >
                        <FaEye className="mr-1" size={10} />
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        ) : (
          <EmptyState
            type="services"
            title="No Services Found"
            description={filters.searchTerm ? 'No services match your current search criteria. Try adjusting your filters or search terms.' : 'No service data is currently available. Services will appear here once they are created.'}
            icon={FaTools}
          />
        )}

        {/* Pagination */}
        {renderPagination(servicesPagination, fetchServicesData)}
      </div>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Service Reports..."
        subtitle="Preparing comprehensive service analytics"
        variant="page"
      />
    );
  }

  const renderServiceCallsContent = () => {
    const serviceCalls = serviceData?.serviceCalls || [];

    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                Service Calls ({callsPagination.totalCount} total records)
              </h3>
              <div className="flex items-center text-sm text-gray-500">
                <FaChartBar className="mr-2" />
                Real-time data with enhanced grouping
              </div>
            </div>
            <div className="flex items-center gap-4">
              <GroupToggle
                groupBy={groupBy}
                setGroupBy={setGroupBy}
                className="hidden sm:flex"
              />
              <div className="sm:hidden">
                <ViewToggle
                  viewMode={callsViewMode}
                  setViewMode={setCallsViewMode}
                />
              </div>
            </div>
          </div>

          {/* Mobile grouping toggle */}
          <div className="sm:hidden mt-3 pt-3 border-t border-gray-100">
            <GroupToggle
              groupBy={groupBy}
              setGroupBy={setGroupBy}
            />
          </div>
        </div>

        {callsLoading ? (
          <div className="flex justify-center items-center py-12">
            <FaSpinner className="animate-spin text-blue-500 text-2xl mr-3" />
            <span className="text-gray-600">Loading service calls...</span>
          </div>
        ) : serviceCalls.length > 0 ? (
          <div>
            {callsViewMode === 'table' ? (
              // Enhanced Collapsible List View
            <div className="divide-y divide-gray-100">
              {Object.entries(groupDataBy(serviceCalls, groupBy)).map(([groupKey, groupItems]) => (
                <div key={groupKey} className="bg-white">
                  {/* Group Header */}
                  <div
                    className="px-6 py-4 bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors border-l-4 border-blue-500"
                    onClick={() => toggleGroup(groupKey)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center">
                          {expandedGroups[groupKey] ? (
                            <FaChevronDown className="h-4 w-4 text-gray-500" />
                          ) : (
                            <FaChevronUp className="h-4 w-4 text-gray-500" />
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <FaLayerGroup className="h-4 w-4 text-blue-600" />
                          <span className="font-semibold text-gray-900">{groupKey}</span>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {groupItems.length} call{groupItems.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <FaMousePointer className="h-3 w-3" />
                        <span>Click to {expandedGroups[groupKey] ? 'collapse' : 'expand'}</span>
                      </div>
                    </div>
                  </div>

                  {/* Group Items */}
                  {expandedGroups[groupKey] && (
                    <div className="bg-white">
                      {/* Enhanced Table Header */}
                      <div className="px-6 py-3 bg-gray-25 border-b border-gray-200">
                        <div className="grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <div className="col-span-3">Call Details</div>
                          <div className="col-span-2">Customer</div>
                          <div className="col-span-1">Priority</div>
                          <div className="col-span-2">Status</div>
                          <div className="col-span-2">Assigned To</div>
                          <div className="col-span-2">Date</div>
                        </div>
                      </div>

                      {/* Enhanced Table Rows */}
                      <div className="divide-y divide-gray-100">
                        {groupItems.map((call, index) => (
                          <div key={call.id || index}>
                            {/* Main Row */}
                            <div
                              className={`px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                                index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                              }`}
                              onClick={() => toggleDetails(call.id || `${groupKey}-${index}`)}
                            >
                              <div className="grid grid-cols-12 gap-4 items-center">
                                {/* Call Details */}
                                <div className="col-span-3">
                                  <div className="flex items-center">
                                    <div className="w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: '#15579e' }}>
                                      <FaTicketAlt className="text-white" size={12} />
                                    </div>
                                    <div className="flex-1">
                                      <div className="text-sm font-medium text-gray-900 flex items-center">
                                        {getServiceNumber(call)}
                                        {expandedDetails[call.id || `${groupKey}-${index}`] ? (
                                          <FaChevronUp className="ml-2 h-3 w-3 text-gray-400" />
                                        ) : (
                                          <FaChevronDown className="ml-2 h-3 w-3 text-gray-400" />
                                        )}
                                      </div>
                                      <div className="text-xs text-gray-500 mt-1 break-words">
                                        {getDisplayValue(call.subject, 'No subject')}
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Customer */}
                                <div className="col-span-2">
                                  <div className="text-sm text-gray-900 font-medium">
                                    {getCustomerName(call)}
                                  </div>
                                  {call.customer?.contact_person && (
                                    <div className="text-xs text-gray-500">
                                      {call.customer.contact_person}
                                    </div>
                                  )}
                                </div>

                                {/* Priority */}
                                <div className="col-span-1">
                                  <div className="flex items-center">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(call.priority)}`}>
                                      {call.priority?.name || call.priority || 'Normal'}
                                    </span>
                                  </div>
                                </div>

                                {/* Status */}
                                <div className="col-span-2">
                                  <div className="flex items-center">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(call.status)}`}>
                                      {getServiceStatus(call)}
                                    </span>
                                  </div>
                                </div>

                                {/* Assigned To */}
                                <div className="col-span-2">
                                  <div className="flex items-center">
                                    <FaUserTie className="mr-1 h-3 w-3 text-gray-400" />
                                    <span className="text-sm text-gray-900 truncate">
                                      {getExecutiveName(call)}
                                    </span>
                                  </div>
                                </div>

                                {/* Date */}
                                <div className="col-span-2">
                                  <div className="text-sm text-gray-900">
                                    {formatDate(getCallDate(call))}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    Created {formatDate(call.createdAt)}
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Expanded Details */}
                            {expandedDetails[call.id || `${groupKey}-${index}`] && (
                              <div className="px-6 py-4 bg-blue-50 border-l-4 border-blue-200">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                                  <div>
                                    <span className="font-medium text-gray-700">Full Description:</span>
                                    <p className="text-gray-600 mt-1">
                                      {call.description || call.subject || 'No description available'}
                                    </p>
                                  </div>
                                  {call.customer?.phone && (
                                    <div>
                                      <span className="font-medium text-gray-700">Contact Phone:</span>
                                      <p className="text-gray-600 mt-1 flex items-center">
                                        <FaPhone className="mr-1 h-3 w-3" />
                                        {call.customer.phone}
                                      </p>
                                    </div>
                                  )}
                                  {call.customer?.email && (
                                    <div>
                                      <span className="font-medium text-gray-700">Contact Email:</span>
                                      <p className="text-gray-600 mt-1 flex items-center">
                                        <FaEnvelope className="mr-1 h-3 w-3" />
                                        {call.customer.email}
                                      </p>
                                    </div>
                                  )}
                                  <div>
                                    <span className="font-medium text-gray-700">Last Updated:</span>
                                    <p className="text-gray-600 mt-1">
                                      {formatDate(call.updatedAt || call.createdAt, true)}
                                    </p>
                                  </div>
                                  <div className="md:col-span-2 lg:col-span-1">
                                    <button
                                      className="inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        navigate(`/services/${call.id}`);
                                      }}
                                    >
                                      <FaEye className="mr-2 h-3 w-3" />
                                      View Full Details
                                    </button>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
            ) : (
              // Card View
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {serviceCalls.map((call, index) => (
                    <div key={call.id || index} className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-all duration-200 border border-gray-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: '#15579e' }}>
                            <FaTicketAlt className="text-white" size={16} />
                          </div>
                          <div>
                            <Tooltip content={`Call Number: ${getServiceNumber(call)}`}>
                              <h6 className="font-bold text-gray-900 text-sm cursor-help">{getServiceNumber(call)}</h6>
                            </Tooltip>
                            <Tooltip content={`Customer: ${getCustomerName(call)}`}>
                              <p className="text-xs text-gray-600 cursor-help truncate max-w-32">{getCustomerName(call)}</p>
                            </Tooltip>
                          </div>
                        </div>
                        <Tooltip content={`Priority Level: ${call.priority?.name || call.priority || 'Normal Priority'}`}>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(call.priority)} cursor-help`}>
                            {call.priority?.name || call.priority || 'Normal'}
                          </span>
                        </Tooltip>
                      </div>

                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500">Status:</span>
                          <Tooltip content={`Status: ${getServiceStatus(call)} - Assigned to: ${getExecutiveName(call)}`}>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(call.status)} cursor-help`}>
                              {getServiceStatus(call)}
                            </span>
                          </Tooltip>
                        </div>

                        <div className="grid grid-cols-2 gap-3 text-xs">
                          <div>
                            <span className="text-gray-500">Executive:</span>
                            <Tooltip content={`Assigned Executive: ${getExecutiveName(call)}`}>
                              <p className="font-medium cursor-help flex items-center">
                                <FaUserTie className="mr-1" size={10} />
                                <span className="truncate">{getExecutiveName(call)}</span>
                              </p>
                            </Tooltip>
                          </div>
                          <div>
                            <span className="text-gray-500">Call Date:</span>
                            <Tooltip content={`Call Date: ${formatDate(getCallDate(call), true)}`}>
                              <p className="font-medium cursor-help flex items-center">
                                <FaCalendar className="mr-1" size={10} />
                                <span>{formatDate(getCallDate(call))}</span>
                              </p>
                            </Tooltip>
                          </div>
                          {call.customer?.contact_person && (
                            <div>
                              <span className="text-gray-500">Contact:</span>
                              <Tooltip content={`Contact Person: ${call.customer.contact_person}`}>
                                <p className="font-medium cursor-help flex items-center">
                                  <FaUser className="mr-1" size={10} />
                                  <span className="truncate">{call.customer.contact_person}</span>
                                </p>
                              </Tooltip>
                            </div>
                          )}
                          {call.customer?.phone && (
                            <div>
                              <span className="text-gray-500">Phone:</span>
                              <Tooltip content={`Phone: ${call.customer.phone}`}>
                                <p className="font-medium cursor-help flex items-center">
                                  <FaPhone className="mr-1" size={10} />
                                  <span className="truncate">{call.customer.phone}</span>
                                </p>
                              </Tooltip>
                            </div>
                          )}
                        </div>

                        {call.subject && (
                          <div>
                            <span className="text-xs text-gray-500">Subject:</span>
                            <Tooltip content={call.subject ? `Subject: ${call.subject}` : 'No subject provided'}>
                              <p className="text-xs text-gray-700 cursor-help break-words">{getDisplayValue(call.subject, 'No subject provided')}</p>
                            </Tooltip>
                          </div>
                        )}

                        <div>
                          <span className="text-xs text-gray-500">Created:</span>
                          <Tooltip content={`Created: ${formatDate(call.createdAt, true)}`}>
                            <p className="text-xs text-gray-600 cursor-help flex items-center">
                              <FaHistory className="mr-1" size={10} />
                              <span>{formatDate(call.createdAt)}</span>
                            </p>
                          </Tooltip>
                        </div>
                      </div>

                      <div className="flex justify-end mt-4 pt-3 border-t border-gray-200">
                        <button
                          className="text-xs text-blue-600 hover:text-blue-800 flex items-center font-medium"
                          onClick={() => navigate(`/services/${call.id}`)}
                        >
                          <FaEye className="mr-1" size={10} />
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <EmptyState
            type="serviceCalls"
            title="No Service Calls Found"
            description={filters.searchTerm ? 'No service calls match your current search criteria. Try adjusting your filters, search terms, or grouping options.' : 'No service call data is currently available. Service calls will appear here once they are created and logged in the system.'}
            icon={FaTicketAlt}
          />
        )}

        {/* Pagination */}
        {renderPagination(callsPagination, fetchServiceCallsData)}
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewContent();
      case 'services':
        return renderServicesContent();
      case 'calls':
        return renderServiceCallsContent();
      default:
        return renderOverviewContent();
    }
  };

  // Date Popup Component
  const renderDatePopup = () => {
    if (!showDatePopup) return null;

    const handleApply = () => {
      if (customFromDate && customToDate) {
        handleCustomDateRange(customFromDate, customToDate);
      }
    };

    const handleCancel = () => {
      setShowDatePopup(false);
      setCustomFromDate('');
      setCustomToDate('');
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-96 max-w-90vw">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Select Date Range</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
              <input
                type="date"
                value={customFromDate}
                onChange={(e) => setCustomFromDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
              <input
                type="date"
                value={customToDate}
                onChange={(e) => setCustomToDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApply}
              disabled={!customFromDate || !customToDate}
              className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: '#15579e' }}
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-0">Service Reports</h2>
          <p className="text-gray-600">Comprehensive service analytics and insights</p>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
            style={{
              borderColor: '#15579e',
              color: '#15579e'
            }}
          >
            <FaFilter className="mr-1 sm:mr-2" />
            Filters
          </button>
          <button
            onClick={() => handleExport('csv')}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white flex-1 sm:flex-none justify-center"
            style={{
              backgroundColor: '#15579e',
              borderColor: '#15579e'
            }}
          >
            <FaDownload className="mr-1 sm:mr-2" />
            Export CSV
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Filters */}
      {renderFilters()}

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 text-sm"
            placeholder="Search services, calls, customers..."
            value={filters.searchTerm}
            onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
          />
        </div>
      </div>

      {/* Tab Navigation */}
      {renderTabNavigation()}

      {/* Tab Content */}
      {renderTabContent()}

      {/* Date Popup */}
      {renderDatePopup()}
    </div>
  );
};

export default ServiceReports;
