import { toast } from 'react-hot-toast';

// Storage keys
const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refreshToken',
  USER: 'user',
};

/**
 * Check if an error is an authentication error
 * @param {Error|Object} error - The error object
 * @returns {boolean} - True if it's an auth error
 */
export const isAuthError = (error) => {
  // Check error response
  if (error.response) {
    const { status, data } = error.response;

    // Check status code
    if (status === 401) return true;

    // Check error codes and messages
    if (data && (
      data.code === 'INVALID_TOKEN' ||
      data.code === 'TOKEN_EXPIRED' ||
      data.code === 'AUTH_REQUIRED' ||
      data.message === 'Invalid access token' ||
      data.message === 'Token expired' ||
      data.message === 'Authentication required'
    )) {
      return true;
    }
  }

  // Check error message directly
  if (error.message && (
    error.message.includes('Invalid access token') ||
    error.message.includes('Token expired') ||
    error.message.includes('Authentication required') ||
    error.message.includes('Unauthorized')
  )) {
    return true;
  }

  return false;
};

/**
 * Clear authentication data from localStorage
 */
export const clearAuthData = () => {
  localStorage.removeItem(STORAGE_KEYS.TOKEN);
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);
};

/**
 * Redirect to login page
 * @param {string} message - Optional message to show
 */
export const redirectToLogin = (message = 'Session expired. Please login again.') => {
  // Show toast message
  toast.error(message);

  // Clear auth data
  clearAuthData();

  // Avoid redirect loop if already on login page
  if (!window.location.pathname.includes('/auth/login')) {
    // Add a small delay to ensure toast is shown
    setTimeout(() => {
      window.location.href = '/auth/login';
    }, 1000);
  }
};

/**
 * Handle authentication errors
 * @param {Error|Object} error - The error object
 * @param {string} customMessage - Custom message to show
 * @returns {boolean} - True if it was an auth error and handled
 */
export const handleAuthError = (error, customMessage) => {
  if (isAuthError(error)) {
    const message = customMessage || 'Your session has expired. Please login again.';
    redirectToLogin(message);
    return true;
  }
  return false;
};

/**
 * Higher-order function to wrap API calls with auth error handling
 * @param {Function} apiCall - The API call function
 * @param {string} errorMessage - Custom error message
 * @returns {Function} - Wrapped function
 */
export const withAuthErrorHandling = (apiCall, errorMessage) => {
  return async (...args) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      if (handleAuthError(error, errorMessage)) {
        throw error; // Re-throw to maintain error flow
      }
      throw error; // Re-throw non-auth errors
    }
  };
};

/**
 * React hook for handling auth errors in components
 * @returns {Function} - Error handler function
 */
export const useAuthErrorHandler = () => {
  return (error, customMessage) => {
    return handleAuthError(error, customMessage);
  };
};

export default {
  isAuthError,
  clearAuthData,
  redirectToLogin,
  handleAuthError,
  withAuthErrorHandling,
  useAuthErrorHandler,
};
