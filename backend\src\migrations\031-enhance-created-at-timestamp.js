import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  try {
    console.log('🔧 Enhancing created_at field in service_calls table to ensure proper timestamp support...');
    
    // Check current table structure
    const tableDescription = await queryInterface.describeTable('service_calls');
    
    if (tableDescription.created_at) {
      console.log('✅ created_at field already exists in service_calls table');
      
      // Ensure the field has proper constraints and default value
      await queryInterface.changeColumn('service_calls', 'created_at', {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'Service call creation timestamp with date and time',
      });
      
      console.log('✅ Enhanced created_at field with proper constraints and comment');
    } else {
      // If for some reason the field doesn't exist, create it
      await queryInterface.addColumn('service_calls', 'created_at', {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'Service call creation timestamp with date and time',
      });
      
      console.log('✅ Added created_at field to service_calls table');
    }
    
    // Also ensure updated_at field has proper constraints
    if (tableDescription.updated_at) {
      await queryInterface.changeColumn('service_calls', 'updated_at', {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: 'Service call last update timestamp',
      });
      
      console.log('✅ Enhanced updated_at field with proper constraints and comment');
    }
    
    console.log('🎉 Successfully enhanced timestamp fields in service_calls table');
    
  } catch (error) {
    console.error('❌ Error enhancing timestamp fields:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  try {
    console.log('⏪ Reverting timestamp field enhancements...');
    
    // Revert created_at field to basic definition
    await queryInterface.changeColumn('service_calls', 'created_at', {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    });
    
    // Revert updated_at field to basic definition
    await queryInterface.changeColumn('service_calls', 'updated_at', {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    });
    
    console.log('✅ Reverted timestamp field enhancements');
    
  } catch (error) {
    console.error('❌ Error reverting timestamp field enhancements:', error);
    throw error;
  }
};
