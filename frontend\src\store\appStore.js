import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UI_CONFIG } from '../utils/constants';

const useAppStore = create(
  persist(
    (set, get) => ({
      // UI State
      theme: UI_CONFIG.DEFAULT_THEME,
      language: UI_CONFIG.DEFAULT_LANGUAGE,
      sidebarCollapsed: false,
      sidebarVisible: false,

      // Loading States
      isLoading: false,
      loadingMessage: '',

      // Notifications
      notifications: [],
      unreadCount: 0,

      // Page State
      currentPage: '',
      pageTitle: '',
      breadcrumbs: [],

      // Master Data Counts
      masterCounts: {
        'customers': 0,
        'license-editions': 0,
        'designations': 0,
        'tally-products': 0,
        'staff-roles': 0,
        'executives': 0,
        'industries': 0,
        'areas': 0,
        'nature-of-issues': 0,
        'additional-services': 0,
        'call-statuses': 0,
      },

      // Actions
      setTheme: (theme) => {
        set({ theme });
        document.documentElement.setAttribute('data-theme', theme);
      },

      setLanguage: (language) => {
        set({ language });
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));
      },

      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed });
      },

      toggleSidebarMobile: () => {
        set((state) => ({ sidebarVisible: !state.sidebarVisible }));
      },

      setSidebarVisible: (visible) => {
        set({ sidebarVisible: visible });
      },

      setLoading: (isLoading, message = '') => {
        set({ isLoading, loadingMessage: message });
      },

      addNotification: (notification) => {
        const newNotification = {
          id: Date.now(),
          timestamp: new Date(),
          read: false,
          ...notification,
        };

        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1,
        }));
      },

      markNotificationAsRead: (id) => {
        set((state) => ({
          notifications: state.notifications.map((notification) =>
            (notification.id === id
              ? { ...notification, read: true }
              : notification)
          ),
          unreadCount: Math.max(0, state.unreadCount - 1),
        }));
      },

      markAllNotificationsAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map((notification) => ({
            ...notification,
            read: true,
          })),
          unreadCount: 0,
        }));
      },

      removeNotification: (id) => {
        set((state) => {
          const notification = state.notifications.find((n) => n.id === id);
          const wasUnread = notification && !notification.read;

          return {
            notifications: state.notifications.filter((n) => n.id !== id),
            unreadCount: wasUnread
              ? Math.max(0, state.unreadCount - 1)
              : state.unreadCount,
          };
        });
      },

      clearAllNotifications: () => {
        set({ notifications: [], unreadCount: 0 });
      },

      // Master Data Actions
      updateMasterCounts: (counts) => {
        console.log('🔍 Updating master counts in store:', counts);
        set((state) => ({
          masterCounts: { ...state.masterCounts, ...counts }
        }));
      },

      // Force refresh master counts
      refreshMasterCounts: async () => {
        try {
          // This will be called by components that need to refresh counts
          console.log('🔄 Refreshing master counts...');
        } catch (error) {
          console.error('Error refreshing master counts:', error);
        }
      },

      setCurrentPage: (page, title = '', breadcrumbs = []) => {
        set({
          currentPage: page,
          pageTitle: title,
          breadcrumbs,
        });
      },

      // Initialize app state
      initialize: () => {
        const { theme } = get();
        document.documentElement.setAttribute('data-theme', theme);

        // Set initial page state
        const path = window.location.pathname;
        set({ currentPage: path });
      },
    }),
    {
      name: 'app-storage',
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
    }
  )
);

export default useAppStore;
