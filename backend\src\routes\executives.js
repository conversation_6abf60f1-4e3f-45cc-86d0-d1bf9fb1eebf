import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getExecutives,
  getExecutiveById,
  createExecutive,
  updateExecutive,
  deleteExecutive,
  getExecutiveStats,
} from '../controllers/executiveController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @swagger
 * components:
 *   schemas:
 *     Executive:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           example: "123e4567-e89b-12d3-a456-************"
 *         name:
 *           type: string
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           example: "+91-9876543210"
 *         designation:
 *           type: string
 *           example: "Sales Executive"
 *         area_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         is_active:
 *           type: boolean
 *           example: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     ExecutiveCreateRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - phone
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           example: "John Doe"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           example: "+91-9876543210"
 *         designation:
 *           type: string
 *           maxLength: 100
 *           example: "Sales Executive"
 *         area_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         is_active:
 *           type: boolean
 *           default: true
 *           example: true
 *     ExecutiveUpdateRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 100
 *           example: "John Doe Updated"
 *         email:
 *           type: string
 *           format: email
 *           example: "<EMAIL>"
 *         phone:
 *           type: string
 *           example: "+91-9876543210"
 *         designation:
 *           type: string
 *           maxLength: 100
 *           example: "Senior Sales Executive"
 *         area_id:
 *           type: string
 *           format: uuid
 *           nullable: true
 *         is_active:
 *           type: boolean
 *           example: true
 */

/**
 * @swagger
 * /executives:
 *   get:
 *     summary: Get all executives
 *     description: Retrieve a paginated list of executives with optional filtering and sorting
 *     tags: [Executives]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of executives per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search term for name, email, or designation
 *       - in: query
 *         name: areaId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by area ID
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [name, email, designation, created_at, updated_at]
 *           default: name
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC, asc, desc]
 *           default: ASC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Executives retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         executives:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/Executive'
 *             example:
 *               success: true
 *               message: "Executives retrieved successfully"
 *               data:
 *                 executives:
 *                   - id: "123e4567-e89b-12d3-a456-************"
 *                     name: "John Doe"
 *                     email: "<EMAIL>"
 *                     phone: "+91-9876543210"
 *                     designation: "Sales Executive"
 *                     is_active: true
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 total: 25
 *                 totalPages: 3
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/', [
  requirePermission('executives.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must be less than 100 characters'),
  query('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'first_name', 'last_name', 'employee_code'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
  validate,
], getExecutives);

/**
 * @route   GET /api/executives/stats
 * @desc    Get executive statistics
 * @access  Private (requires executives.read permission)
 */
router.get('/stats', [
  requirePermission('executives.read'),
], getExecutiveStats);

/**
 * @swagger
 * /executives/{id}:
 *   get:
 *     summary: Get executive by ID
 *     description: Retrieve detailed information about a specific executive including assigned customers and performance metrics
 *     tags: [Executives]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Executive ID
 *         example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       200:
 *         description: Executive retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         executive:
 *                           allOf:
 *                             - $ref: '#/components/schemas/Executive'
 *                             - type: object
 *                               properties:
 *                                 area:
 *                                   type: object
 *                                   properties:
 *                                     id:
 *                                       type: string
 *                                       format: uuid
 *                                     name:
 *                                       type: string
 *                                       example: "North Zone"
 *                                 statistics:
 *                                   type: object
 *                                   properties:
 *                                     total_customers:
 *                                       type: integer
 *                                       example: 25
 *                                     active_customers:
 *                                       type: integer
 *                                       example: 22
 *                                     total_service_calls:
 *                                       type: integer
 *                                       example: 45
 *                                     this_month_calls:
 *                                       type: integer
 *                                       example: 8
 *             example:
 *               success: true
 *               message: "Executive retrieved successfully"
 *               data:
 *                 executive:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   name: "John Doe"
 *                   email: "<EMAIL>"
 *                   phone: "+91-9876543210"
 *                   designation: "Sales Executive"
 *                   is_active: true
 *                   area:
 *                     id: "456e7890-e89b-12d3-a456-************"
 *                     name: "North Zone"
 *                   statistics:
 *                     total_customers: 25
 *                     active_customers: 22
 *                     total_service_calls: 45
 *                     this_month_calls: 8
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       404:
 *         description: Executive not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Executive not found"
 *               errors: {}
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/:id', [
  requirePermission('executives.read'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  validate,
], getExecutiveById);

/**
 * @swagger
 * /executives:
 *   post:
 *     summary: Create new executive
 *     description: Create a new executive with contact information and area assignment
 *     tags: [Executives]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ExecutiveCreateRequest'
 *           example:
 *             name: "John Doe"
 *             email: "<EMAIL>"
 *             phone: "+91-9876543210"
 *             designation: "Sales Executive"
 *             area_id: "456e7890-e89b-12d3-a456-************"
 *             is_active: true
 *     responses:
 *       201:
 *         description: Executive created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         executive:
 *                           $ref: '#/components/schemas/Executive'
 *             example:
 *               success: true
 *               message: "Executive created successfully"
 *               data:
 *                 executive:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   name: "John Doe"
 *                   email: "<EMAIL>"
 *                   phone: "+91-9876543210"
 *                   designation: "Sales Executive"
 *                   is_active: true
 *                   created_at: "2024-01-15T10:30:00Z"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       409:
 *         description: Executive already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Executive with this email already exists"
 *               errors:
 *                 email: "<EMAIL> is already registered"
 *       422:
 *         $ref: '#/components/responses/422'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.post('/', [
  requirePermission('executives.create'),
  body('first_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('employee_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Employee code must be between 2 and 20 characters'),
  body('email')
    .notEmpty()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email is required and must be valid'),
  body('phone')
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('alternate_phone')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Use a simple phone validation that accepts various formats
      const phoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
      if (!phoneRegex.test(value)) {
        throw new Error('Please provide a valid alternate phone number');
      }
      return true;
    }),
  body('designation_id')
    .notEmpty()
    .isUUID()
    .withMessage('Designation is required and must be a valid UUID'),
  body('staff_role_id')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Basic UUID validation
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(value)) {
        throw new Error('Staff role ID must be a valid UUID');
      }
      return true;
    }),
  body('department')
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('date_of_joining')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Check if it's a valid ISO8601 date
      const date = new Date(value);
      if (isNaN(date.getTime()) || !value.match(/^\d{4}-\d{2}-\d{2}$/)) {
        throw new Error('Date of joining must be a valid date (YYYY-MM-DD)');
      }
      return true;
    }),
  body('date_of_birth')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Check if it's a valid ISO8601 date
      const date = new Date(value);
      if (isNaN(date.getTime()) || !value.match(/^\d{4}-\d{2}-\d{2}$/)) {
        throw new Error('Date of birth must be a valid date (YYYY-MM-DD)');
      }
      return true;
    }),
  body('address')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Address is required and must be between 1 and 500 characters'),
  body('city')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('City is required and must be between 1 and 100 characters'),
  body('state')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('State is required and must be between 1 and 100 characters'),
  body('postal_code')
    .notEmpty()
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Postal code is required and must be between 1 and 20 characters'),
  body('salary')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Salary is required and must be a valid decimal number'),
  body('commission_rate')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .custom((value) => {
      const num = parseFloat(value);
      if (num < 0 || num > 100) {
        throw new Error('Commission rate must be between 0 and 100');
      }
      return true;
    })
    .withMessage('Commission rate is required and must be a valid percentage (0-100)'),
  body('target_amount')
    .notEmpty()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Target amount is required and must be a valid decimal number'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('areas_covered')
    .optional()
    .isArray()
    .withMessage('Areas covered must be an array'),
  validate,
], createExecutive);

/**
 * @route   PUT /api/executives/:id
 * @desc    Update executive
 * @access  Private (requires executives.update permission)
 */
router.put('/:id', [
  requirePermission('executives.update'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  body('first_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('last_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('employee_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Employee code must be between 2 and 20 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('alternate_phone')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Use a simple phone validation that accepts various formats
      const phoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
      if (!phoneRegex.test(value)) {
        throw new Error('Please provide a valid alternate phone number');
      }
      return true;
    }),
  body('designation_id')
    .optional({ nullable: true, checkFalsy: true })
    .isUUID()
    .withMessage('Designation ID must be a valid UUID'),
  body('staff_role_id')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Basic UUID validation
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(value)) {
        throw new Error('Staff role ID must be a valid UUID');
      }
      return true;
    }),
  body('department')
    .optional()
    .isIn(['sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'])
    .withMessage('Invalid department'),
  body('date_of_joining')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Check if it's a valid ISO8601 date
      const date = new Date(value);
      if (isNaN(date.getTime()) || !value.match(/^\d{4}-\d{2}-\d{2}$/)) {
        throw new Error('Date of joining must be a valid date (YYYY-MM-DD)');
      }
      return true;
    }),
  body('date_of_birth')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value || value === '' || value === null || value === undefined) {
        return true; // Allow empty values
      }
      // Check if it's a valid ISO8601 date
      const date = new Date(value);
      if (isNaN(date.getTime()) || !value.match(/^\d{4}-\d{2}-\d{2}$/)) {
        throw new Error('Date of birth must be a valid date (YYYY-MM-DD)');
      }
      return true;
    }),
  body('salary')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Salary must be a valid decimal number'),
  body('commission_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Commission rate must be a valid decimal number'),
  body('target_amount')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Target amount must be a valid decimal number'),
  body('skills')
    .optional()
    .isArray()
    .withMessage('Skills must be an array'),
  body('areas_covered')
    .optional()
    .isArray()
    .withMessage('Areas covered must be an array'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  validate,
], updateExecutive);

/**
 * @route   DELETE /api/executives/:id
 * @desc    Delete executive
 * @access  Private (requires executives.delete permission)
 */
router.delete('/:id', [
  requirePermission('executives.delete'),
  param('id')
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  validate,
], deleteExecutive);

export default router;
