import { useState, useCallback, useRef } from 'react';
import { apiService } from '../services/api';

/**
 * Hook for server-side customer search
 * Provides debounced search functionality for customer selection components
 */
export const useCustomerSearch = () => {
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(null);
  const timeoutRef = useRef(null);

  // Debounced search function
  const searchCustomers = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const response = await apiService.get('/customers', {
        params: {
          search: searchTerm,
          limit: 100, // Maximum allowed by backend validation (was 1000, causing 400 error)
          page: 1,
          sortBy: 'company_name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data?.customers) {
        // Transform customer data for SearchableSelect with comprehensive details
        const customers = response.data.data.customers.map(customer => ({
          id: customer.id,
          company_name: customer.company_name || customer.display_name || 'Unknown Company',
          display_name: customer.display_name || customer.company_name || 'Unknown Company',
          contact_person: customer.contact_person || 'N/A',
          phone: customer.phone || 'N/A',
          customer_code: customer.customer_code || 'N/A',
          tally_serial_number: customer.tally_serial_number || 'N/A',
          email: customer.email || 'N/A',
          city: customer.city || 'N/A',
          state: customer.state || 'N/A',
          address: customer.address || 'N/A',
          gst_number: customer.gst_number || 'N/A',
          // TSS and AMC status
          tss_status: customer.custom_fields?.tss_status || 'N/A',
          amc_status: customer.custom_fields?.amc_status || 'N/A',
          // Additional contact details
          md_contact_person: customer.md_contact_person || 'N/A',
          admin_email: customer.admin_email || 'N/A',
          // Include original customer data for form population
          originalData: customer,
          // Custom fields for additional information
          custom_fields: customer.custom_fields || {}
        }));

        setSearchResults(customers);

        // Log search results for debugging
        console.log(`🔍 Customer search completed: Found ${customers.length} customers for "${searchTerm}"`);
      } else {
        setSearchResults([]);
        console.log(`🔍 Customer search completed: No customers found for "${searchTerm}"`);
      }
    } catch (error) {
      console.error('Customer search error:', error);

      // Enhanced error handling with specific error messages
      let errorMessage = 'Failed to search customers';
      if (error.response?.status === 404) {
        errorMessage = 'Customer search service not available';
      } else if (error.response?.status === 500) {
        errorMessage = 'Server error while searching customers';
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error - please check your connection';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setSearchError(errorMessage);
      setSearchResults([]);

      console.log(`🔍 Customer search failed for "${searchTerm}": ${errorMessage}`);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Debounced search with timeout
  const debouncedSearch = useCallback((searchTerm) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      searchCustomers(searchTerm);
    }, 150); // 150ms debounce - fast but prevents infinite loops
  }, [searchCustomers]);

  // Reset search state
  const resetSearch = useCallback(() => {
    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setSearchResults(null);
    setIsSearching(false);
    setSearchError(null);
  }, []);

  return {
    searchResults,
    isSearching,
    searchError,
    searchCustomers: debouncedSearch, // Use debounced search to prevent infinite loops (100ms delay)
    resetSearch
  };
};

export default useCustomerSearch;
