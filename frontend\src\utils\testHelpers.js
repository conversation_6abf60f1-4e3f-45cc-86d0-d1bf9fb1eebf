/**
 * Test Helper Functions
 * Utility functions to help with testing and debugging
 */

/**
 * Test pagination functionality
 * @param {Object} paginationData - Pagination object from API response
 * @param {Array} dataArray - Array of data items
 * @returns {Object} Test results
 */
export const testPagination = (paginationData, dataArray) => {
  const results = {
    passed: true,
    errors: [],
    warnings: [],
    info: {}
  };

  // Check if pagination data exists
  if (!paginationData) {
    results.passed = false;
    results.errors.push('Pagination data is missing');
    return results;
  }

  // Validate pagination structure
  const requiredFields = ['currentPage', 'totalPages', 'totalItems', 'itemsPerPage'];
  requiredFields.forEach(field => {
    if (paginationData[field] === undefined || paginationData[field] === null) {
      results.passed = false;
      results.errors.push(`Missing pagination field: ${field}`);
    }
  });

  // Check data consistency
  const expectedItemsOnPage = Math.min(
    paginationData.itemsPerPage,
    paginationData.totalItems - ((paginationData.currentPage - 1) * paginationData.itemsPerPage)
  );

  if (dataArray.length !== expectedItemsOnPage) {
    results.warnings.push(
      `Data array length (${dataArray.length}) doesn't match expected items on page (${expectedItemsOnPage})`
    );
  }

  // Calculate expected total pages
  const expectedTotalPages = Math.ceil(paginationData.totalItems / paginationData.itemsPerPage);
  if (paginationData.totalPages !== expectedTotalPages) {
    results.passed = false;
    results.errors.push(
      `Total pages mismatch: got ${paginationData.totalPages}, expected ${expectedTotalPages}`
    );
  }

  // Store info for debugging
  results.info = {
    currentPage: paginationData.currentPage,
    totalPages: paginationData.totalPages,
    totalItems: paginationData.totalItems,
    itemsPerPage: paginationData.itemsPerPage,
    dataArrayLength: dataArray.length,
    expectedItemsOnPage
  };

  return results;
};

/**
 * Test search functionality
 * @param {string} searchTerm - The search term used
 * @param {Array} results - Search results array
 * @param {Array} searchFields - Fields that should be searched
 * @returns {Object} Test results
 */
export const testSearchFunctionality = (searchTerm, results, searchFields = []) => {
  const testResults = {
    passed: true,
    errors: [],
    warnings: [],
    info: {}
  };

  if (!searchTerm || searchTerm.length < 2) {
    testResults.warnings.push('Search term is too short for meaningful testing');
    return testResults;
  }

  // Check if results contain the search term
  const searchTermLower = searchTerm.toLowerCase();
  const matchingResults = results.filter(item => {
    return searchFields.some(field => {
      const fieldValue = item[field];
      return fieldValue && fieldValue.toString().toLowerCase().includes(searchTermLower);
    });
  });

  if (matchingResults.length === 0 && results.length > 0) {
    testResults.warnings.push(
      `No results contain search term "${searchTerm}" in specified fields: ${searchFields.join(', ')}`
    );
  }

  testResults.info = {
    searchTerm,
    totalResults: results.length,
    matchingResults: matchingResults.length,
    searchFields,
    matchPercentage: results.length > 0 ? (matchingResults.length / results.length * 100).toFixed(2) : 0
  };

  return testResults;
};

/**
 * Test loading state management
 * @param {boolean} isLoading - Current loading state
 * @param {HTMLElement} loadingElement - Loading element to check
 * @returns {Object} Test results
 */
export const testLoadingState = (isLoading, loadingElement) => {
  const results = {
    passed: true,
    errors: [],
    warnings: [],
    info: {}
  };

  if (isLoading && !loadingElement) {
    results.passed = false;
    results.errors.push('Loading state is true but no loading element found');
  }

  if (!isLoading && loadingElement) {
    results.warnings.push('Loading element exists but loading state is false');
  }

  if (loadingElement) {
    const computedStyle = window.getComputedStyle(loadingElement);
    const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
    
    if (isLoading && !isVisible) {
      results.passed = false;
      results.errors.push('Loading element should be visible when loading state is true');
    }

    // Check if loading element is centered
    const parent = loadingElement.parentElement;
    if (parent) {
      const parentStyle = window.getComputedStyle(parent);
      const isCentered = parentStyle.display === 'flex' && 
                        (parentStyle.justifyContent === 'center' || parentStyle.alignItems === 'center');
      
      if (!isCentered) {
        results.warnings.push('Loading element may not be properly centered');
      }
    }
  }

  results.info = {
    isLoading,
    hasLoadingElement: !!loadingElement,
    elementVisible: loadingElement ? window.getComputedStyle(loadingElement).display !== 'none' : false
  };

  return results;
};

/**
 * Test responsive design
 * @param {number} windowWidth - Current window width
 * @returns {Object} Test results with breakpoint info
 */
export const testResponsiveDesign = (windowWidth = window.innerWidth) => {
  const breakpoints = {
    mobile: 768,
    tablet: 1024,
    desktop: 1440
  };

  const currentBreakpoint = windowWidth < breakpoints.mobile ? 'mobile' :
                           windowWidth < breakpoints.tablet ? 'tablet' :
                           windowWidth < breakpoints.desktop ? 'desktop' : 'large-desktop';

  return {
    windowWidth,
    currentBreakpoint,
    isMobile: windowWidth < breakpoints.mobile,
    isTablet: windowWidth >= breakpoints.mobile && windowWidth < breakpoints.tablet,
    isDesktop: windowWidth >= breakpoints.tablet,
    breakpoints
  };
};

/**
 * Test form validation
 * @param {Object} formData - Form data object
 * @param {Object} validationRules - Validation rules
 * @returns {Object} Validation test results
 */
export const testFormValidation = (formData, validationRules) => {
  const results = {
    passed: true,
    errors: [],
    warnings: [],
    validatedFields: {}
  };

  Object.keys(validationRules).forEach(fieldName => {
    const value = formData[fieldName];
    const rules = validationRules[fieldName];
    const fieldResults = {
      passed: true,
      errors: []
    };

    // Required field check
    if (rules.required && (!value || value.toString().trim() === '')) {
      fieldResults.passed = false;
      fieldResults.errors.push(`${fieldName} is required`);
    }

    // Min length check
    if (rules.minLength && value && value.toString().length < rules.minLength) {
      fieldResults.passed = false;
      fieldResults.errors.push(`${fieldName} must be at least ${rules.minLength} characters`);
    }

    // Max length check
    if (rules.maxLength && value && value.toString().length > rules.maxLength) {
      fieldResults.passed = false;
      fieldResults.errors.push(`${fieldName} must be no more than ${rules.maxLength} characters`);
    }

    // Email validation
    if (rules.email && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        fieldResults.passed = false;
        fieldResults.errors.push(`${fieldName} must be a valid email address`);
      }
    }

    // Phone validation
    if (rules.phone && value) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
        fieldResults.passed = false;
        fieldResults.errors.push(`${fieldName} must be a valid phone number`);
      }
    }

    results.validatedFields[fieldName] = fieldResults;
    if (!fieldResults.passed) {
      results.passed = false;
      results.errors.push(...fieldResults.errors);
    }
  });

  return results;
};

/**
 * Performance testing helper
 * @param {Function} testFunction - Function to test
 * @param {number} iterations - Number of iterations to run
 * @returns {Object} Performance test results
 */
export const testPerformance = async (testFunction, iterations = 10) => {
  const results = {
    iterations,
    times: [],
    averageTime: 0,
    minTime: Infinity,
    maxTime: 0,
    totalTime: 0
  };

  for (let i = 0; i < iterations; i++) {
    const startTime = performance.now();
    await testFunction();
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    
    results.times.push(executionTime);
    results.totalTime += executionTime;
    results.minTime = Math.min(results.minTime, executionTime);
    results.maxTime = Math.max(results.maxTime, executionTime);
  }

  results.averageTime = results.totalTime / iterations;

  return results;
};

/**
 * Console logger for test results
 * @param {string} testName - Name of the test
 * @param {Object} results - Test results object
 */
export const logTestResults = (testName, results) => {
  console.group(`🧪 Test: ${testName}`);
  
  if (results.passed) {
    console.log('✅ PASSED');
  } else {
    console.log('❌ FAILED');
  }

  if (results.errors && results.errors.length > 0) {
    console.group('❌ Errors:');
    results.errors.forEach(error => console.error(error));
    console.groupEnd();
  }

  if (results.warnings && results.warnings.length > 0) {
    console.group('⚠️ Warnings:');
    results.warnings.forEach(warning => console.warn(warning));
    console.groupEnd();
  }

  if (results.info) {
    console.group('ℹ️ Info:');
    console.table(results.info);
    console.groupEnd();
  }

  console.groupEnd();
};

/**
 * Run all tests for a component
 * @param {Object} testSuite - Object containing test functions
 * @returns {Object} Overall test results
 */
export const runTestSuite = async (testSuite) => {
  const overallResults = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    results: {}
  };

  for (const [testName, testFunction] of Object.entries(testSuite)) {
    overallResults.totalTests++;
    
    try {
      const result = await testFunction();
      overallResults.results[testName] = result;
      
      if (result.passed) {
        overallResults.passedTests++;
      } else {
        overallResults.failedTests++;
      }
      
      logTestResults(testName, result);
    } catch (error) {
      overallResults.failedTests++;
      overallResults.results[testName] = {
        passed: false,
        errors: [error.message],
        warnings: [],
        info: {}
      };
      
      logTestResults(testName, overallResults.results[testName]);
    }
  }

  console.group('📊 Test Suite Summary');
  console.log(`Total Tests: ${overallResults.totalTests}`);
  console.log(`Passed: ${overallResults.passedTests}`);
  console.log(`Failed: ${overallResults.failedTests}`);
  console.log(`Success Rate: ${((overallResults.passedTests / overallResults.totalTests) * 100).toFixed(2)}%`);
  console.groupEnd();

  return overallResults;
};

export default {
  testPagination,
  testSearchFunctionality,
  testLoadingState,
  testResponsiveDesign,
  testFormValidation,
  testPerformance,
  logTestResults,
  runTestSuite
};
