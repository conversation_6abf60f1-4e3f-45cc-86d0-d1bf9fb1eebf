/**
 * Frontend Validation Utilities
 * Comprehensive client-side validation for better user experience
 */

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation regex (Indian format)
const PHONE_REGEX = /^[+]?91[-\s]?[6-9]\d{9}$/;

// International phone validation regex (supports country codes)
const INTERNATIONAL_PHONE_REGEX = /^[+]\d{1,4}[\s-]?\d{4,15}$/;

// PAN validation regex
const PAN_REGEX = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

// GST validation regex
const GST_REGEX = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

// Customer serial number regex
const CUSTOMER_SERIAL_REGEX = /^[A-Z0-9]+$/;

// Service number regex (flexible pattern)
const SERVICE_NUMBER_REGEX = /^[A-Z]{2,4}-\d{3,6}[A-Z]?$/;

/**
 * Validation Rules
 */
export const validationRules = {
  // Required field validation
  required: (value, fieldName = 'Field') => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return { isValid: false, message: `${fieldName} is required` };
    }
    return { isValid: true };
  },

  // Email validation
  email: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    if (!value || !EMAIL_REGEX.test(value.trim())) {
      return { isValid: false, message: 'Please enter a valid email address' };
    }
    return { isValid: true };
  },

  // Phone validation (supports international formats)
  phone: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }

    const cleanPhone = value ? value.trim() : '';
    if (!cleanPhone) {
      return { isValid: false, message: 'Please enter a valid phone number' };
    }

    // Check for international format first (with country code)
    if (INTERNATIONAL_PHONE_REGEX.test(cleanPhone.replace(/[\s-]/g, ''))) {
      return { isValid: true };
    }

    // Fallback to Indian format validation (for backward compatibility)
    const digitsOnly = cleanPhone.replace(/\D/g, '');
    if (digitsOnly.length === 10 && /^[6-9]\d{9}$/.test(digitsOnly)) {
      return { isValid: true };
    }

    return {
      isValid: false,
      message: 'Please enter a valid phone number with country code'
    };
  },

  // Text length validation
  length: (value, min = 0, max = Infinity, fieldName = 'Field') => {
    const length = value ? value.trim().length : 0;
    if (length < min) {
      return { isValid: false, message: `${fieldName} must be at least ${min} characters` };
    }
    if (length > max) {
      return { isValid: false, message: `${fieldName} must not exceed ${max} characters` };
    }
    return { isValid: true };
  },

  // Customer serial number validation
  customerSerial: (value, isRequired = true) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    if (!value || !CUSTOMER_SERIAL_REGEX.test(value.trim())) {
      return { isValid: false, message: 'Customer serial number must contain only uppercase letters and numbers' };
    }
    if (value.trim().length < 2 || value.trim().length > 20) {
      return { isValid: false, message: 'Customer serial number must be between 2 and 20 characters' };
    }
    return { isValid: true };
  },

  // Service number validation
  serviceNumber: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    if (!value || !SERVICE_NUMBER_REGEX.test(value.trim())) {
      return { isValid: false, message: 'Service number must follow format like SER-001, SE-003S' };
    }
    return { isValid: true };
  },

  // GST number validation
  gst: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    if (!value || value.trim().length !== 15) {
      return { isValid: false, message: 'GST number must be exactly 15 characters' };
    }
    if (!GST_REGEX.test(value.trim())) {
      return { isValid: false, message: 'Please enter a valid GST number' };
    }
    return { isValid: true };
  },

  // PAN number validation
  pan: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    if (!value || !PAN_REGEX.test(value.trim())) {
      return { isValid: false, message: 'Please enter a valid PAN number (e.g., **********)' };
    }
    return { isValid: true };
  },

  // Number validation
  number: (value, min = -Infinity, max = Infinity, fieldName = 'Number') => {
    if (!value || value === '') {
      return { isValid: true }; // Allow empty for optional fields
    }
    const num = parseFloat(value);
    if (isNaN(num)) {
      return { isValid: false, message: `${fieldName} must be a valid number` };
    }
    if (num < min) {
      return { isValid: false, message: `${fieldName} must be at least ${min}` };
    }
    if (num > max) {
      return { isValid: false, message: `${fieldName} must not exceed ${max}` };
    }
    return { isValid: true };
  },

  // Date validation
  date: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return { isValid: false, message: 'Please enter a valid date' };
    }
    return { isValid: true };
  },

  // URL validation
  url: (value, isRequired = false) => {
    if (!isRequired && (!value || value.trim() === '')) {
      return { isValid: true };
    }
    try {
      new URL(value);
      return { isValid: true };
    } catch {
      return { isValid: false, message: 'Please enter a valid URL' };
    }
  }
};

/**
 * Form Validation Functions
 */

// Validate service call form
export const validateServiceCallForm = (formData) => {
  const errors = {};

  // Customer Serial Number is required
  const customerSerialValidation = validationRules.customerSerial(formData.customerSerialNumber, true);
  if (!customerSerialValidation.isValid) {
    errors.customerSerialNumber = customerSerialValidation.message;
  }

  // Subject is optional but has max length
  if (formData.subject) {
    const subjectValidation = validationRules.length(formData.subject, 0, 200, 'Subject');
    if (!subjectValidation.isValid) {
      errors.subject = subjectValidation.message;
    }
  }

  // Description is optional but has max length
  if (formData.description) {
    const descriptionValidation = validationRules.length(formData.description, 0, 2000, 'Description');
    if (!descriptionValidation.isValid) {
      errors.description = descriptionValidation.message;
    }
  }

  // Service number validation if provided
  if (formData.serviceNumber) {
    const serviceNumberValidation = validationRules.serviceNumber(formData.serviceNumber);
    if (!serviceNumberValidation.isValid) {
      errors.serviceNumber = serviceNumberValidation.message;
    }
  }

  // Estimated hours validation if provided
  if (formData.estimatedHours) {
    const hoursValidation = validationRules.number(formData.estimatedHours, 0, 24, 'Estimated hours');
    if (!hoursValidation.isValid) {
      errors.estimatedHours = hoursValidation.message;
    }
  }

  // Scheduled date validation if provided
  if (formData.scheduledDate) {
    const dateValidation = validationRules.date(formData.scheduledDate);
    if (!dateValidation.isValid) {
      errors.scheduledDate = dateValidation.message;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Validate customer form
export const validateCustomerForm = (formData) => {
  const errors = {};

  // Company name is required
  const companyNameValidation = validationRules.required(formData.companyName, 'Company name');
  if (!companyNameValidation.isValid) {
    errors.companyName = companyNameValidation.message;
  } else {
    const lengthValidation = validationRules.length(formData.companyName, 2, 200, 'Company name');
    if (!lengthValidation.isValid) {
      errors.companyName = lengthValidation.message;
    }
  }

  // Customer serial number is required
  const customerSerialValidation = validationRules.customerSerial(formData.tallySerialNo, true);
  if (!customerSerialValidation.isValid) {
    errors.tallySerialNo = customerSerialValidation.message;
  }

  // Email validation
  if (formData.adminEmail) {
    const emailValidation = validationRules.email(formData.adminEmail, true);
    if (!emailValidation.isValid) {
      errors.adminEmail = emailValidation.message;
    }
  }

  // Phone validation
  if (formData.mdPhoneNo) {
    const phoneValidation = validationRules.phone(formData.mdPhoneNo, true);
    if (!phoneValidation.isValid) {
      errors.mdPhoneNo = phoneValidation.message;
    }
  }

  // GST validation
  if (formData.gstNo) {
    const gstValidation = validationRules.gst(formData.gstNo);
    if (!gstValidation.isValid) {
      errors.gstNo = gstValidation.message;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Real-time field validation
 */
export const validateField = (fieldName, value, validationConfig = {}) => {
  const { type, required = false, min, max, ...options } = validationConfig;

  switch (type) {
    case 'email':
      return validationRules.email(value, required);
    case 'phone':
      return validationRules.phone(value, required);
    case 'customerSerial':
      return validationRules.customerSerial(value, required);
    case 'serviceNumber':
      return validationRules.serviceNumber(value, required);
    case 'gst':
      return validationRules.gst(value, required);
    case 'pan':
      return validationRules.pan(value, required);
    case 'number':
      return validationRules.number(value, min, max, fieldName);
    case 'date':
      return validationRules.date(value, required);
    case 'url':
      return validationRules.url(value, required);
    case 'text':
      if (required) {
        const requiredValidation = validationRules.required(value, fieldName);
        if (!requiredValidation.isValid) return requiredValidation;
      }
      return validationRules.length(value, min || 0, max || Infinity, fieldName);
    default:
      if (required) {
        return validationRules.required(value, fieldName);
      }
      return { isValid: true };
  }
};

/**
 * Utility functions
 */

// Check if phone number has actual digits (for conditional validation)
export const hasPhoneDigits = (phoneValue) => {
  if (!phoneValue || phoneValue.trim() === '') return false;
  const phoneDigits = phoneValue.replace(/[^\d]/g, '');
  return phoneDigits.length > 0;
};

// Format input values
export const formatInput = {
  customerSerial: (value) => (value ? value.toUpperCase().replace(/[^A-Z0-9]/g, '') : ''),
  gst: (value) => (value ? value.toUpperCase().replace(/\s/g, '') : ''),
  pan: (value) => (value ? value.toUpperCase().replace(/\s/g, '') : ''),
  phone: (value) => (value ? value.replace(/\D/g, '') : ''),
  email: (value) => (value ? value.toLowerCase().trim() : ''),
  text: (value) => (value ? value.trim() : '')
};

// Check if form has errors
export const hasErrors = (errors) => {
  return Object.keys(errors).some(key => errors[key]);
};

// Get first error message
export const getFirstError = (errors) => {
  const firstErrorKey = Object.keys(errors).find(key => errors[key]);
  return firstErrorKey ? errors[firstErrorKey] : null;
};
