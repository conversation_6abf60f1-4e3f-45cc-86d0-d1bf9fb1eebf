import { io } from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.timerCallbacks = new Map();
  }

  /**
   * Initialize socket connection
   */
  connect() {
    // If already connected, return existing socket
    if (this.socket && this.isConnected) {
      console.log('🔌 Socket already connected:', this.socket.id);
      return this.socket;
    }

    // If already trying to connect, return existing socket
    if (this.isConnecting) {
      console.log('🔌 Socket connection already in progress...');
      return this.socket;
    }

    // If socket exists but not connected, try to reconnect
    if (this.socket && !this.isConnected) {
      console.log('🔌 Socket exists but not connected, attempting to reconnect...');
      this.isConnecting = true;
      this.socket.connect();
      return this.socket;
    }

    const token = localStorage.getItem('token');
    // Extract base URL from API URL (remove /api/v1 suffix)
    const apiUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1';
    const serverUrl = apiUrl.replace('/api/v1', '');

    console.log('🔌 Creating new WebSocket connection to:', serverUrl);
    console.log('🔑 Using auth token:', token ? 'Present' : 'Missing');

    this.isConnecting = true;

    this.socket = io(serverUrl, {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: false, // Don't force new connection
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5
    });

    this.setupEventListeners();
    return this.socket;
  }

  /**
   * Setup socket event listeners
   */
  setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Socket connected:', this.socket.id);
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
      this.isConnected = false;
      this.isConnecting = false;

      // Auto-reconnect for certain disconnect reasons
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }

      this.handleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('🔴 Socket connection error:', error);
      this.isConnected = false;
      this.isConnecting = false;
      this.handleReconnect();
    });

    // Timer-specific events
    this.socket.on('timer_update', (data) => {
      console.log('⏱️ Timer update received:', data);
      this.handleTimerUpdate(data);
    });

    this.socket.on('timer_started', (data) => {
      console.log('▶️ Timer started:', data);
      this.handleTimerUpdate(data);
    });

    this.socket.on('timer_paused', (data) => {
      console.log('⏸️ Timer paused:', data);
      this.handleTimerUpdate(data);
    });

    this.socket.on('timer_stopped', (data) => {
      console.log('⏹️ Timer stopped:', data);
      this.handleTimerUpdate(data);
    });

    this.socket.on('service_status_changed', (data) => {
      console.log('🔄 Service status changed:', data);
      this.handleTimerUpdate(data);
    });
  }

  /**
   * Handle reconnection attempts
   */
  handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

    setTimeout(() => {
      if (!this.isConnected && this.socket) {
        // Try to reconnect existing socket first
        this.socket.connect();
      } else if (!this.isConnected && !this.socket) {
        // Create new connection if socket doesn't exist
        this.connect();
      }
    }, delay);
  }

  /**
   * Handle timer updates from server
   */
  handleTimerUpdate(data) {
    const { serviceCallId } = data;
    
    if (this.timerCallbacks.has(serviceCallId)) {
      const callback = this.timerCallbacks.get(serviceCallId);
      callback(data);
    }
  }

  /**
   * Subscribe to timer updates for a specific service call
   */
  subscribeToTimer(serviceCallId, callback) {
    if (!this.socket || !this.isConnected) {
      console.warn('⚠️ Socket not connected, cannot subscribe to timer');
      return;
    }

    console.log(`📡 Subscribing to timer updates for service call: ${serviceCallId}`);
    
    // Store callback for this service call
    this.timerCallbacks.set(serviceCallId, callback);
    
    // Join the service call room
    this.socket.emit('join_service_room', { serviceCallId });
  }

  /**
   * Unsubscribe from timer updates for a specific service call
   */
  unsubscribeFromTimer(serviceCallId) {
    if (!this.socket) return;

    console.log(`📡 Unsubscribing from timer updates for service call: ${serviceCallId}`);
    
    // Remove callback
    this.timerCallbacks.delete(serviceCallId);
    
    // Leave the service call room
    this.socket.emit('leave_service_room', { serviceCallId });
  }

  /**
   * Request timer status for a service call
   */
  requestTimerStatus(serviceCallId) {
    if (!this.socket || !this.isConnected) {
      console.warn('⚠️ Socket not connected, cannot request timer status');
      return;
    }

    this.socket.emit('get_timer_status', { serviceCallId });
  }

  /**
   * Disconnect socket
   */
  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting socket');
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.timerCallbacks.clear();
    }
  }

  /**
   * Check if socket is connected
   */
  isSocketConnected() {
    return this.socket && this.isConnected;
  }

  /**
   * Get socket instance
   */
  getSocket() {
    return this.socket;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
