import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  FaFilter, FaDownload, FaEye, FaSearch, FaCalendarAlt,
  FaUser, FaExclamationTriangle, FaCheckCircle, FaClock,
  FaChevronLeft, FaChevronRight, FaSort, FaSortUp, FaSortDown,
  FaInfoCircle, FaTicketAlt, FaUserTie, FaHistory, FaIdCard, FaBuilding
} from 'react-icons/fa';
import api from '../../services/api';
import LoadingScreen from '../ui/LoadingScreen';
import Tooltip from '../ui/Tooltip';

const ServiceRequestReport = ({ onBack }) => {
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    customerId: '',
    priority: '',
    statusId: '',
    assignedTo: '',
    page: 1,
    limit: 25,
  });
  const [sortConfig, setSortConfig] = useState({ key: 'createdAt', direction: 'desc' });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, [filters.page, filters.limit]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await api.get(`/reports/service-request?${params}`);
      setReportData(response.data.data);
      toast.success('Service request report loaded');
    } catch (error) {
      console.error('Error fetching service request report:', error);
      toast.error('Failed to load service request report');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handleApplyFilters = () => {
    fetchReportData();
    setShowFilters(false);
  };

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const getSortIcon = (key) => {
    if (sortConfig.key !== key) return <FaSort className="text-gray-400" />;
    return sortConfig.direction === 'asc' ? 
      <FaSortUp className="text-primary-600" /> : 
      <FaSortDown className="text-primary-600" />;
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';
    
    switch (status.category) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'open': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString, showTime = false) => {
    if (!dateString) return 'Not Available';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid Date';

      const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      };

      if (showTime) {
        options.hour = '2-digit';
        options.minute = '2-digit';
      }

      return date.toLocaleDateString('en-US', options);
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const getDisplayValue = (value, fallback = 'Not Available') => {
    if (value === null || value === undefined || value === '') return fallback;
    return value;
  };

  const getCallNumber = (serviceCall) => {
    return serviceCall?.callNumber || serviceCall?.call_number || 'No Call Number';
  };

  const getCustomerName = (serviceCall) => {
    return serviceCall?.customer?.company_name || serviceCall?.customer?.name || 'Unknown Customer';
  };

  const getExecutiveName = (serviceCall) => {
    return serviceCall?.assignedExecutive?.name ||
           serviceCall?.assigned_executive?.name ||
           serviceCall?.executive?.name ||
           serviceCall?.assignedTo?.name ||
           'Unassigned';
  };

  const renderFilters = () => (
    <div className={`bg-gray-50 border-t border-gray-200 p-4 ${showFilters ? 'block' : 'hidden'}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Date From</label>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Date To</label>
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
          <select
            value={filters.priority}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
        <div className="flex items-end space-x-2">
          <button
            onClick={handleApplyFilters}
            className="px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700"
          >
            Apply Filters
          </button>
          <button
            onClick={() => {
              setFilters(prev => ({
                ...prev,
                dateFrom: '',
                dateTo: '',
                customerId: '',
                priority: '',
                statusId: '',
                assignedTo: '',
                page: 1,
              }));
              fetchReportData();
            }}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  );

  const renderSummaryCards = () => {
    if (!reportData?.summary) return null;

    const { summary } = reportData;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <FaEye className="text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{summary.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg mr-3">
              <FaCheckCircle className="text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Resolution Time</p>
              <p className="text-2xl font-bold text-gray-900">{summary.avgResolutionTime}h</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg mr-3">
              <FaExclamationTriangle className="text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {summary.byPriority?.high || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg mr-3">
              <FaClock className="text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Critical Priority</p>
              <p className="text-2xl font-bold text-gray-900">
                {summary.byPriority?.critical || 0}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTable = () => {
    if (!reportData?.serviceCalls) return null;

    const sortedData = [...reportData.serviceCalls].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      
      if (sortConfig.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return (
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('callNumber')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Call Number</span>
                    {getSortIcon('callNumber')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('customer')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Customer</span>
                    {getSortIcon('customer')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subject
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('priority')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Priority</span>
                    {getSortIcon('priority')}
                  </div>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned To
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('agingDays')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Aging</span>
                    {getSortIcon('agingDays')}
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Created</span>
                    {getSortIcon('createdAt')}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedData.map((serviceCall) => (
                <tr key={serviceCall.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <Tooltip content={`Call Number: ${getCallNumber(serviceCall)}`}>
                      <div className="flex items-center cursor-help">
                        <FaTicketAlt className="mr-2 text-gray-400" size={12} />
                        {getCallNumber(serviceCall)}
                      </div>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <Tooltip content={`Customer: ${getCustomerName(serviceCall)}`}>
                        <div className="flex items-center font-medium cursor-help">
                          <FaBuilding className="mr-1" size={10} />
                          <span className="truncate max-w-32">{getCustomerName(serviceCall)}</span>
                        </div>
                      </Tooltip>
                      <Tooltip content={`Customer Code: ${getDisplayValue(serviceCall.customer?.customer_code, 'No customer code')}`}>
                        <div className="flex items-center text-gray-500 cursor-help">
                          <FaIdCard className="mr-1" size={10} />
                          <span className="truncate max-w-28">{getDisplayValue(serviceCall.customer?.customer_code, 'No code')}</span>
                        </div>
                      </Tooltip>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    <Tooltip content={serviceCall.subject ? `Subject: ${serviceCall.subject}` : 'No subject provided'}>
                      <div className="max-w-xs truncate cursor-help">
                        {getDisplayValue(serviceCall.subject, 'No subject provided')}
                      </div>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Tooltip content={`Priority Level: ${serviceCall.priority || 'Normal Priority'}`}>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(serviceCall.priority)} cursor-help`}>
                        {serviceCall.priority || 'Normal'}
                      </span>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Tooltip content={`Status: ${serviceCall.status?.name || 'Unknown Status'}`}>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(serviceCall.status)} cursor-help`}>
                        {serviceCall.status?.name || 'Unknown Status'}
                      </span>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <Tooltip content={`Assigned Executive: ${getExecutiveName(serviceCall)}`}>
                      <div className="flex items-center cursor-help">
                        <FaUserTie className="mr-1" size={12} />
                        <span className="truncate max-w-24">{getExecutiveName(serviceCall)}</span>
                      </div>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <Tooltip content={`Aging: ${serviceCall.agingDays || 0} days since creation`}>
                      <div className="flex items-center cursor-help">
                        <FaClock className="mr-1" size={12} />
                        <span className={`font-medium ${serviceCall.agingDays > 7 ? 'text-red-600' : serviceCall.agingDays > 3 ? 'text-yellow-600' : 'text-green-600'}`}>
                          {serviceCall.agingDays || 0} days
                        </span>
                      </div>
                    </Tooltip>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <Tooltip content={`Created: ${formatDate(serviceCall.createdAt, true)}`}>
                      <div className="flex items-center cursor-help">
                        <FaHistory className="mr-1" size={12} />
                        <span>{formatDate(serviceCall.createdAt)}</span>
                      </div>
                    </Tooltip>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Service Request Report..."
        subtitle="Analyzing service request data"
        variant="modal"
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
          >
            <FaChevronLeft />
          </button>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Service Request Report</h3>
            <p className="text-gray-600">Comprehensive list of all service requests with filters</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded border ${
              showFilters ? 'bg-primary-600 text-white border-primary-600' : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <FaFilter className="mr-2" />
            Filters
          </button>
          <button
            onClick={() => toast.success('Export functionality will be implemented')}
            className="inline-flex items-center px-4 py-2 text-sm font-medium rounded border bg-green-600 text-white border-green-600 hover:bg-green-700"
          >
            <FaDownload className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Filters */}
      {renderFilters()}

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Table */}
      {renderTable()}

      {/* Pagination */}
      {reportData?.pagination && (
        <div className="flex items-center justify-between bg-white px-6 py-3 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-700">
            Showing {((reportData.pagination.page - 1) * reportData.pagination.limit) + 1} to{' '}
            {Math.min(reportData.pagination.page * reportData.pagination.limit, reportData.pagination.total)} of{' '}
            {reportData.pagination.total} results
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
              disabled={filters.page <= 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              <FaChevronLeft />
            </button>
            <span className="px-3 py-1 text-sm">
              Page {filters.page} of {reportData.pagination.pages}
            </span>
            <button
              onClick={() => handleFilterChange('page', Math.min(reportData.pagination.pages, filters.page + 1))}
              disabled={filters.page >= reportData.pagination.pages}
              className="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              <FaChevronRight />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceRequestReport;
