import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  const defaultStatuses = [
    {
      id: '11111111-1111-1111-1111-111111111111',
      name: 'New',
      code: 'NEW',
      description: 'New lead that needs initial contact',
      category: 'new',
      color: '#28a745',
      is_final: false,
      requires_approval: false,
      auto_follow_up_days: 1,
      is_active: true,
      is_default: true,
      sort_order: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '22222222-2222-2222-2222-222222222222',
      name: 'Follow Up',
      code: 'FOLLOW_UP',
      description: 'Lead requires follow up contact',
      category: 'active',
      color: '#ffc107',
      is_final: false,
      requires_approval: false,
      auto_follow_up_days: 3,
      is_active: true,
      is_default: true,
      sort_order: 2,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '33333333-3333-3333-3333-333333333333',
      name: 'Call Not Attended',
      code: 'CALL_NOT_ATTENDED',
      description: 'Lead did not answer the call',
      category: 'active',
      color: '#fd7e14',
      is_final: false,
      requires_approval: false,
      auto_follow_up_days: 2,
      is_active: true,
      is_default: true,
      sort_order: 3,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '44444444-4444-4444-4444-444444444444',
      name: 'Interested',
      code: 'INTERESTED',
      description: 'Lead has shown interest in products/services',
      category: 'interested',
      color: '#17a2b8',
      is_final: false,
      requires_approval: false,
      auto_follow_up_days: 7,
      is_active: true,
      is_default: true,
      sort_order: 4,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '55555555-5555-5555-5555-555555555555',
      name: 'Not Interested',
      code: 'NOT_INTERESTED',
      description: 'Lead is not interested in products/services',
      category: 'not_interested',
      color: '#6c757d',
      is_final: true,
      requires_approval: false,
      auto_follow_up_days: null,
      is_active: true,
      is_default: true,
      sort_order: 5,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '66666666-6666-6666-6666-666666666666',
      name: 'Converted',
      code: 'CONVERTED',
      description: 'Lead has been converted to customer',
      category: 'converted',
      color: '#007bff',
      is_final: true,
      requires_approval: false,
      auto_follow_up_days: null,
      is_active: true,
      is_default: true,
      sort_order: 6,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: '77777777-7777-7777-7777-777777777777',
      name: 'Lost',
      code: 'LOST',
      description: 'Lead is lost and no longer viable',
      category: 'lost',
      color: '#dc3545',
      is_final: true,
      requires_approval: false,
      auto_follow_up_days: null,
      is_active: true,
      is_default: true,
      sort_order: 7,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  // Insert default lead statuses
  await queryInterface.bulkInsert('lead_statuses', defaultStatuses);

  console.log('✅ Populated default lead statuses');
};

export const down = async (queryInterface) => {
  // Remove default lead statuses
  await queryInterface.bulkDelete('lead_statuses', {
    is_default: true,
  });

  console.log('✅ Removed default lead statuses');
};
