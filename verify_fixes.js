/**
 * Verification script for data calculation fixes
 * Run this script to verify that the dashboard and customer reports fixes are working correctly
 */

const API_BASE_URL = 'http://localhost:8082/api/v1';

// Mock authentication token - replace with actual token for testing
const AUTH_TOKEN = 'your_auth_token_here';

/**
 * Make authenticated API request
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    ...options.headers
  };

  try {
    const response = await fetch(url, { ...options, headers });
    const data = await response.json();
    return { success: response.ok, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Test Dashboard Service Calls Limit Fix
 */
async function testDashboardServiceCallsLimit() {
  console.log('\n🔍 Testing Dashboard Service Calls Limit Fix...');
  
  // Test with old limit (should now fetch more records)
  const serviceCallsResponse = await apiRequest('/service-calls?limit=1000&sort=created_at&order=desc');
  
  if (serviceCallsResponse.success) {
    const serviceCallsCount = serviceCallsResponse.data?.data?.serviceCalls?.length || 0;
    console.log(`✅ Service calls fetched: ${serviceCallsCount} records`);
    
    if (serviceCallsCount > 10) {
      console.log('✅ PASS: Dashboard now fetches more than 10 service calls for calculations');
    } else {
      console.log('⚠️  WARNING: Still fetching limited records, but this might be due to actual data count');
    }
  } else {
    console.log('❌ FAIL: Could not fetch service calls data');
    console.log('Error:', serviceCallsResponse.error || serviceCallsResponse.data);
  }
}

/**
 * Test Customer Reports Summary Calculation Fix
 */
async function testCustomerReportsSummary() {
  console.log('\n🔍 Testing Customer Reports Summary Calculation Fix...');
  
  // Test customer reports with pagination
  const page1Response = await apiRequest('/reports/customers?page=1&limit=50');
  const page2Response = await apiRequest('/reports/customers?page=2&limit=50');
  
  if (page1Response.success && page2Response.success) {
    const page1Summary = page1Response.data?.data?.summary;
    const page2Summary = page2Response.data?.data?.summary;
    
    console.log('Page 1 Summary:', {
      totalCustomers: page1Summary?.totalCustomers,
      activeCustomers: page1Summary?.activeCustomers,
      prospects: page1Summary?.prospects
    });
    
    console.log('Page 2 Summary:', {
      totalCustomers: page2Summary?.totalCustomers,
      activeCustomers: page2Summary?.activeCustomers,
      prospects: page2Summary?.prospects
    });
    
    // Check if summary totals are consistent across pages
    if (page1Summary?.totalCustomers === page2Summary?.totalCustomers &&
        page1Summary?.activeCustomers === page2Summary?.activeCustomers &&
        page1Summary?.prospects === page2Summary?.prospects) {
      console.log('✅ PASS: Summary totals are consistent across pagination');
      
      // Check if totals are greater than page limit (indicating real database totals)
      if (page1Summary?.totalCustomers > 50) {
        console.log('✅ PASS: Total customers count exceeds pagination limit (using database totals)');
      } else {
        console.log('⚠️  WARNING: Total customers count is low, but fix is implemented correctly');
      }
    } else {
      console.log('❌ FAIL: Summary totals are inconsistent across pages');
    }
  } else {
    console.log('❌ FAIL: Could not fetch customer reports data');
    console.log('Page 1 Error:', page1Response.error || page1Response.data);
    console.log('Page 2 Error:', page2Response.error || page2Response.data);
  }
}

/**
 * Test Dashboard Overview API
 */
async function testDashboardOverview() {
  console.log('\n🔍 Testing Dashboard Overview API...');
  
  const dashboardResponse = await apiRequest('/dashboard/overview');
  
  if (dashboardResponse.success) {
    const summary = dashboardResponse.data?.data?.summary;
    console.log('Dashboard Summary:', {
      totalCustomers: summary?.totalCustomers,
      totalServiceCalls: summary?.totalServiceCalls,
      openServiceCalls: summary?.openServiceCalls,
      activeCustomers: summary?.activeCustomers
    });
    console.log('✅ PASS: Dashboard overview API is working');
  } else {
    console.log('❌ FAIL: Dashboard overview API failed');
    console.log('Error:', dashboardResponse.error || dashboardResponse.data);
  }
}

/**
 * Main verification function
 */
async function runVerification() {
  console.log('🚀 Starting Data Calculation Fixes Verification...');
  console.log('================================================');
  
  // Note about authentication
  if (AUTH_TOKEN === 'your_auth_token_here') {
    console.log('⚠️  NOTE: Please update AUTH_TOKEN in this script with a valid token for full testing');
    console.log('⚠️  Some tests may fail due to authentication requirements');
  }
  
  await testDashboardServiceCallsLimit();
  await testCustomerReportsSummary();
  await testDashboardOverview();
  
  console.log('\n================================================');
  console.log('✅ Verification completed!');
  console.log('\n📋 Manual Testing Required:');
  console.log('1. Open browser and navigate to Dashboard');
  console.log('2. Check Service status overview, Priority breakdown, and Team workload charts');
  console.log('3. Navigate to Reports > Customer Reports');
  console.log('4. Verify summary cards show correct totals regardless of pagination');
}

// Run verification if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  runVerification().catch(console.error);
} else {
  // Browser environment
  console.log('Run runVerification() in browser console to test');
}

// Export for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runVerification, testDashboardServiceCallsLimit, testCustomerReportsSummary, testDashboardOverview };
}
