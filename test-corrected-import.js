// Test the corrected customer import functionality
import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const API_BASE = 'http://localhost:3001/api/v1';

async function testCorrectedImport() {
  try {
    console.log('🧪 Testing Corrected Customer Import Functionality...');
    
    // Step 1: Authenticate
    console.log('\n1. Authenticating...');
    const authResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    if (!authResponse.ok) {
      throw new Error(`Authentication failed: ${authResponse.status}`);
    }
    
    const authData = await authResponse.json();
    const token = authData.token;
    console.log('✅ Authentication successful');
    
    // Step 2: Create a corrected test CSV file
    console.log('\n2. Creating corrected test CSV file...');
    
    const correctedCsvData = [
      // Headers - only mandatory fields marked with *
      [
        'Company Name*', 'Customer Code*', 'Email*', 'Phone*', 'Tally Serial Number*',
        'Display Name', 'Customer Type', 'Business Type',
        'Address Line 1', 'City', 'State', 'Country', 'PIN Code',
        'GST Number', 'PAN Number',
        'Admin Email', 'MD Contact Person', 'MD Phone Number', 'MD Email',
        'Office Contact Person', 'Office Mobile Number', 'Office Email',
        'Auditor Name', 'Auditor Number', 'Auditor Email',
        'Tax Consultant Name', 'Tax Consultant Number', 'Tax Consultant Email',
        'IT Name', 'IT Number', 'IT Email',
        'Area', 'Number of Tally Users', 'Executive Name', 'Status',
        'Profile Status', 'Customer Status'
      ].join(','),
      
      // Sample data row 1 - all mandatory fields filled
      [
        'Test Company 1', 'TEST001', '<EMAIL>', '+91-9876543210', 'TSN001',
        'Test Co 1', 'customer', 'private_limited',
        '123 Test Street', 'Mumbai', 'Maharashtra', 'India', '400001',
        '27**********1Z5', '**********',
        '<EMAIL>', 'John Doe', '+91-9876543211', '<EMAIL>',
        'Jane Smith', '+91-9876543212', '<EMAIL>',
        'CA Rajesh', '+91-9876543213', '<EMAIL>',
        'Priya Sharma', '+91-9876543214', '<EMAIL>',
        'Amit IT', '+91-9876543215', '<EMAIL>',
        'Mumbai Central', '5', 'Sales Exec 1', 'ACTIVE',
        'FOLLOW UP', 'ACTIVE'
      ].map(field => `"${field}"`).join(','),
      
      // Sample data row 2 - minimal mandatory fields only
      [
        'Test Company 2', 'TEST002', '<EMAIL>', '+91-9876543220', 'TSN002',
        '', '', '',
        '', '', '', '', '',
        '', '',
        '', '', '', '',
        '', '', '',
        '', '', '',
        '', '', '',
        '', '', '',
        '', '', '', '',
        '', ''
      ].map(field => `"${field}"`).join(','),
      
      // Sample data row 3 - invalid data to test validation
      [
        '', 'TEST003', 'invalid-email', '', 'TSN003',
        '', '', '',
        '', '', '', '', '',
        'INVALID_GST', 'INVALID_PAN',
        '', '', '', '',
        '', '', '',
        '', '', '',
        '', '', '',
        '', '', '',
        '', '', '', '',
        '', ''
      ].map(field => `"${field}"`).join(',')
    ];
    
    const csvContent = correctedCsvData.join('\n');
    const csvPath = path.join(process.cwd(), 'corrected_customer_import.csv');
    fs.writeFileSync(csvPath, csvContent);
    console.log('✅ Corrected CSV file created');
    
    // Step 3: Test preview with corrected structure
    console.log('\n3. Testing preview with corrected structure...');
    
    const form = new FormData();
    form.append('file', fs.createReadStream(csvPath));
    
    const previewResponse = await fetch(`${API_BASE}/customers/import/preview`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...form.getHeaders()
      },
      body: form
    });
    
    if (!previewResponse.ok) {
      const errorText = await previewResponse.text();
      throw new Error(`Preview failed: ${previewResponse.status} - ${errorText}`);
    }
    
    const previewData = await previewResponse.json();
    console.log('✅ Preview successful');
    
    // Step 4: Validate corrected structure
    console.log('\n4. Validating corrected structure...');
    
    const data = previewData.data;
    
    console.log('📊 Import Statistics:');
    console.log(`   Total rows: ${data.totalRows}`);
    console.log(`   Valid rows: ${data.validRows}`);
    console.log(`   Error rows: ${data.errorRows}`);
    console.log(`   Duplicate rows: ${data.duplicateRows}`);
    
    // Check mandatory fields
    const requiredFields = data.validationRules?.required_fields || [];
    console.log(`✅ Required fields (${requiredFields.length}):`, requiredFields);
    
    // Check column mapping for core fields
    const coreFields = ['Company Name*', 'Customer Code*', 'Email*', 'Phone*', 'Tally Serial Number*'];
    console.log('\n📋 Core field mappings:');
    coreFields.forEach(field => {
      const mapping = data.columnMapping?.[field];
      if (mapping) {
        console.log(`   ${field} → ${mapping.dbField} (required: ${mapping.required})`);
      }
    });
    
    // Check custom fields mapping
    const customFields = ['Admin Email', 'MD Contact Person', 'Area'];
    console.log('\n📋 Custom field mappings:');
    customFields.forEach(field => {
      const mapping = data.columnMapping?.[field];
      if (mapping) {
        console.log(`   ${field} → ${mapping.dbField} (required: ${mapping.required})`);
      }
    });
    
    // Validate errors
    if (data.errors && data.errors.length > 0) {
      console.log('\n⚠️  Validation errors:');
      data.errors.slice(0, 5).forEach(error => {
        console.log(`   Row ${error.row}: ${error.message}`);
      });
    }
    
    // Step 5: Test import execution if we have valid rows
    if (data.validRows > 0) {
      console.log('\n5. Testing import execution...');
      
      const importForm = new FormData();
      importForm.append('file', fs.createReadStream(csvPath));
      
      const importResponse = await fetch(`${API_BASE}/customers/import/execute`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          ...importForm.getHeaders()
        },
        body: importForm
      });
      
      if (importResponse.ok) {
        const importData = await importResponse.json();
        console.log('✅ Import execution successful');
        console.log(`   Imported: ${importData.data.successCount} customers`);
        console.log(`   Errors: ${importData.data.errorCount}`);
        
        if (importData.data.successDetails && importData.data.successDetails.length > 0) {
          console.log('   Sample imported customer:', importData.data.successDetails[0]);
        }
      } else {
        const errorText = await importResponse.text();
        console.log('⚠️  Import execution failed:', errorText);
      }
    }
    
    // Cleanup
    if (fs.existsSync(csvPath)) {
      fs.unlinkSync(csvPath);
      console.log('🧹 Cleanup completed');
    }
    
    console.log('\n🎉 Corrected Import Test Summary:');
    console.log('  ✅ Only 5 mandatory fields required');
    console.log('  ✅ Proper custom_fields structure');
    console.log('  ✅ Correct validation rules');
    console.log('  ✅ Matches actual customer creation flow');
    
  } catch (error) {
    console.error('❌ Corrected import test failed:', error.message);
  }
}

// Run the test
testCorrectedImport();
