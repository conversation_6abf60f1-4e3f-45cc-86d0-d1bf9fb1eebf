export const up = async (queryInterface, Sequelize) => {
  try {
    console.log('🔄 Checking for existing call_billing_type data to update...');

    // Check if there are any records with the old enum values
    const [oldRecords] = await queryInterface.sequelize.query(`
      SELECT COUNT(*) as count
      FROM service_calls
      WHERE call_billing_type IS NOT NULL
    `, { type: queryInterface.sequelize.QueryTypes.SELECT });

    if (parseInt(oldRecords.count) === 0) {
      console.log('ℹ️ No existing call_billing_type data found - column was recreated');
      return;
    }

    // Update existing data to use new enum values (with underscores)
    const updateMappings = [
      { old: 'free call', new: 'free_call' },
      { old: 'amc call', new: 'amc_call' },
      { old: 'per call', new: 'per_call' }
    ];

    for (const mapping of updateMappings) {
      try {
        const result = await queryInterface.sequelize.query(`
          UPDATE service_calls
          SET call_billing_type = '${mapping.new}',
              updated_at = NOW()
          WHERE call_billing_type = '${mapping.old}'
        `);

        console.log(`✅ Updated ${result[1]} records from '${mapping.old}' to '${mapping.new}'`);
      } catch (error) {
        console.log(`ℹ️ No records found with value '${mapping.old}' or already updated`);
      }
    }

    // Check final distribution
    const distribution = await queryInterface.sequelize.query(`
      SELECT 
        call_billing_type,
        COUNT(*) as count
      FROM service_calls 
      WHERE call_billing_type IS NOT NULL
      GROUP BY call_billing_type
      ORDER BY call_billing_type
    `, { type: queryInterface.sequelize.QueryTypes.SELECT });

    console.log('📊 Final call_billing_type distribution:');
    distribution.forEach(row => {
      console.log(`  ${row.call_billing_type}: ${row.count}`);
    });

  } catch (error) {
    console.error('❌ Error updating call_billing_type data:', error);
    throw error;
  }
};

export const down = async (queryInterface, Sequelize) => {
  try {
    console.log('🔄 Reverting call_billing_type data to old enum values...');

    // Revert data back to old enum values (with spaces)
    const revertMappings = [
      { old: 'free_call', new: 'free call' },
      { old: 'amc_call', new: 'amc call' },
      { old: 'per_call', new: 'per call' }
    ];

    for (const mapping of revertMappings) {
      const result = await queryInterface.sequelize.query(`
        UPDATE service_calls 
        SET call_billing_type = '${mapping.new}',
            updated_at = NOW()
        WHERE call_billing_type = '${mapping.old}'
      `);
      
      console.log(`✅ Reverted ${result[1]} records from '${mapping.old}' to '${mapping.new}'`);
    }

  } catch (error) {
    console.error('❌ Error reverting call_billing_type data:', error);
    throw error;
  }
};
