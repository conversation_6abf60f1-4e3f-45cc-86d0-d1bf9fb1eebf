/**
 * Test Customer Status Update Functionality
 * This script tests the customer status update issue where setting inactive status
 * should properly update both is_active and customer_type fields
 */

import models from './src/models/index.js';
import { logger } from './src/utils/logger.js';

async function testCustomerStatusUpdate() {
  try {
    console.log('🧪 Testing Customer Status Update Functionality...\n');

    // Find or create a test tenant
    let testTenant = await models.Tenant.findOne({
      where: { name: 'Test Tenant' }
    });

    if (!testTenant) {
      testTenant = await models.Tenant.create({
        name: 'Test Tenant',
        slug: 'test-tenant',
        subdomain: 'test-tenant',
        status: 'active'
      });
      console.log('✅ Created test tenant');
    }

    // Create a test customer
    const testCustomer = await models.Customer.create({
      tenant_id: testTenant.id,
      customer_code: 'TEST001',
      company_name: 'TEST COMPANY',
      customer_type: 'customer', // Initially active
      is_active: true,
      email: '<EMAIL>',
      phone: '1234567890',
      tally_serial_number: 'TSN001',
      custom_fields: {
        customer_status: 'ACTIVE'
      }
    });

    console.log('✅ Created test customer:', {
      id: testCustomer.id,
      customer_type: testCustomer.customer_type,
      is_active: testCustomer.is_active,
      customer_status: testCustomer.custom_fields?.customer_status
    });

    // Test 1: Update customer to inactive status
    console.log('\n🔄 Test 1: Updating customer to inactive status...');
    
    const updateData = {
      customer_type: 'inactive', // This should be set when customerStatus is 'INACTIVE'
      is_active: false,
      custom_fields: {
        ...testCustomer.custom_fields,
        customer_status: 'INACTIVE'
      }
    };

    await testCustomer.update(updateData);
    await testCustomer.reload();

    console.log('✅ Updated customer status:', {
      customer_type: testCustomer.customer_type,
      is_active: testCustomer.is_active,
      customer_status: testCustomer.custom_fields?.customer_status
    });

    // Verify the update
    const isCorrect = testCustomer.customer_type === 'inactive' && 
                     testCustomer.is_active === false &&
                     testCustomer.custom_fields?.customer_status === 'INACTIVE';

    if (isCorrect) {
      console.log('✅ Test 1 PASSED: Customer status updated correctly');
    } else {
      console.log('❌ Test 1 FAILED: Customer status not updated correctly');
    }

    // Test 2: Update customer back to active status
    console.log('\n🔄 Test 2: Updating customer back to active status...');
    
    const reactivateData = {
      customer_type: 'customer',
      is_active: true,
      custom_fields: {
        ...testCustomer.custom_fields,
        customer_status: 'ACTIVE'
      }
    };

    await testCustomer.update(reactivateData);
    await testCustomer.reload();

    console.log('✅ Reactivated customer status:', {
      customer_type: testCustomer.customer_type,
      is_active: testCustomer.is_active,
      customer_status: testCustomer.custom_fields?.customer_status
    });

    // Verify the reactivation
    const isReactivated = testCustomer.customer_type === 'customer' && 
                         testCustomer.is_active === true &&
                         testCustomer.custom_fields?.customer_status === 'ACTIVE';

    if (isReactivated) {
      console.log('✅ Test 2 PASSED: Customer reactivated correctly');
    } else {
      console.log('❌ Test 2 FAILED: Customer reactivation failed');
    }

    // Test 3: Test filtering by customer_type
    console.log('\n🔄 Test 3: Testing customer filtering by type...');
    
    // Create another inactive customer
    const inactiveCustomer = await models.Customer.create({
      tenant_id: testTenant.id,
      customer_code: 'TEST002',
      company_name: 'INACTIVE TEST COMPANY',
      customer_type: 'inactive',
      is_active: false,
      email: '<EMAIL>',
      phone: '9876543210',
      tally_serial_number: 'TSN002',
      custom_fields: {
        customer_status: 'INACTIVE'
      }
    });

    // Query active customers
    const activeCustomers = await models.Customer.findAll({
      where: {
        tenant_id: testTenant.id,
        customer_type: 'customer'
      }
    });

    // Query inactive customers
    const inactiveCustomers = await models.Customer.findAll({
      where: {
        tenant_id: testTenant.id,
        customer_type: 'inactive'
      }
    });

    console.log(`✅ Found ${activeCustomers.length} active customers`);
    console.log(`✅ Found ${inactiveCustomers.length} inactive customers`);

    if (activeCustomers.length >= 1 && inactiveCustomers.length >= 1) {
      console.log('✅ Test 3 PASSED: Customer filtering works correctly');
    } else {
      console.log('❌ Test 3 FAILED: Customer filtering not working');
    }

    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await testCustomer.destroy();
    await inactiveCustomer.destroy();
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Customer Status Update Tests Completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    logger.error('Customer status test error:', error);
  } finally {
    // Close database connection
    await models.sequelize.close();
  }
}

// Run the test
testCustomerStatusUpdate();
