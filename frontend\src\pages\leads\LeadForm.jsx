import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { leadAPI, masterDataAPI } from '../../services/api';
import SearchableSelect from '../../components/ui/SearchableSelect';
import MobileInput from '../../components/ui/MobileInput';
import {
  FaSave,
  FaTimes,
  FaUser,
  FaBuilding,
  FaPhone,
  FaEnvelope,
  FaCalendar,
  FaRupeeSign,
  FaUserTie,
  FaClipboardList,
  FaStickyNote
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';

const LeadForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEdit);
  const [formData, setFormData] = useState({
    customer_name: '',
    products_issues_id: '',
    amount: '',
    contact_no: '',
    country_code: '+91',
    status_id: '',
    remarks: '',
    executive_id: '',
    contacted_executive_id: '',
    follow_up_date: '',
    ref_name: '',
    ref_contact_no: '',
    ref_country_code: '+91',
    ref_amount: ''
  });

  const [errors, setErrors] = useState({});

  // Master data states
  const [leadStatuses, setLeadStatuses] = useState([]);
  const [executives, setExecutives] = useState([]);
  const [productsIssues, setProductsIssues] = useState([]);
  const [loadingStatuses, setLoadingStatuses] = useState(false);
  const [loadingExecutives, setLoadingExecutives] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);

  // Country codes for phone inputs
  const countryCodeOptions = [
    { value: '+91', label: '🇮🇳 +91 (India)' },
    { value: '+1', label: '🇺🇸 +1 (USA)' },
    { value: '+44', label: '🇬🇧 +44 (UK)' },
    { value: '+971', label: '🇦🇪 +971 (UAE)' },
    { value: '+65', label: '🇸🇬 +65 (Singapore)' },
    { value: '+60', label: '🇲🇾 +60 (Malaysia)' },
    { value: '+86', label: '🇨🇳 +86 (China)' },
    { value: '+81', label: '🇯🇵 +81 (Japan)' },
    { value: '+82', label: '🇰🇷 +82 (South Korea)' },
    { value: '+61', label: '🇦🇺 +61 (Australia)' }
  ];

  // Default lead status options (fallback)
  const defaultStatusOptions = [
    { id: 'new', name: 'New' },
    { id: 'follow_up', name: 'Follow Up' },
    { id: 'call_not_attended', name: 'Call Not Attended' },
    { id: 'interested', name: 'Interested' },
    { id: 'not_interested', name: 'Not Interested' },
    { id: 'converted', name: 'Converted' },
    { id: 'lost', name: 'Lost' }
  ];

  // Load call statuses (used for lead statuses)
  const loadLeadStatuses = async () => {
    try {
      setLoadingStatuses(true);
      // Load from call statuses master data
      const response = await masterDataAPI.getCallStatuses({
        is_active: true,
        limit: 100,
        sortBy: 'sort_order',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data) {
        // Handle different possible response structures
        const statusesData = response.data.data.callstatus ||
                           response.data.data.callStatuses ||
                           response.data.data.call_statuses ||
                           response.data.data;
        const statuses = Array.isArray(statusesData) ? statusesData : [];

        // Ensure all status objects have proper string values
        const safeStatuses = statuses.map(status => ({
          id: typeof status.id === 'object' ? JSON.stringify(status.id) : String(status.id || ''),
          name: typeof status.name === 'object' ? JSON.stringify(status.name) : String(status.name || 'Unknown Status'),
          code: typeof status.code === 'object' ? JSON.stringify(status.code) : String(status.code || ''),
          color: typeof status.color === 'object' ? JSON.stringify(status.color) : String(status.color || '#6c757d'),
          category: typeof status.category === 'object' ? JSON.stringify(status.category) : String(status.category || 'open'),
          description: typeof status.description === 'object' ? JSON.stringify(status.description) : String(status.description || ''),
          // Keep other fields as-is but ensure they're not objects
          ...Object.keys(status).reduce((acc, key) => {
            if (!['id', 'name', 'code', 'color', 'category', 'description'].includes(key)) {
              acc[key] = typeof status[key] === 'object' ? JSON.stringify(status[key]) : status[key];
            }
            return acc;
          }, {})
        }));


        setLeadStatuses(safeStatuses);

        // Set default status to "Follow Up" if not already set
        if (!formData.status_id && statuses.length > 0) {
          const followUpStatus = statuses.find(
            status => status.name.toLowerCase().includes('follow up') ||
                     status.name.toLowerCase().includes('followup')
          );
          if (followUpStatus) {
            setFormData(prev => ({ ...prev, status_id: followUpStatus.id }));
          }
        }
      } else {
        // Fallback to default options
        setLeadStatuses(defaultStatusOptions);
      }
    } catch (error) {
      console.error('Error loading call statuses:', error);
      // Use default options as fallback
      setLeadStatuses(defaultStatusOptions);
    } finally {
      setLoadingStatuses(false);
    }
  };

  // Load executives
  const loadExecutives = async () => {
    try {
      setLoadingExecutives(true);
      const response = await masterDataAPI.getExecutives({
        is_active: true,
        limit: 100,
        sortBy: 'first_name',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data) {
        // Handle different possible response structures
        const executivesData = response.data.data.executives || response.data.data;
        const safeExecutivesData = Array.isArray(executivesData) ? executivesData : [];

        // Ensure all executive objects have proper string values
        const cleanExecutives = safeExecutivesData.map(exec => ({
          ...exec,
          id: typeof exec.id === 'object' ? JSON.stringify(exec.id) : String(exec.id || ''),
          first_name: typeof exec.first_name === 'object' ? JSON.stringify(exec.first_name) : String(exec.first_name || ''),
          last_name: typeof exec.last_name === 'object' ? JSON.stringify(exec.last_name) : String(exec.last_name || ''),
          email: typeof exec.email === 'object' ? JSON.stringify(exec.email) : String(exec.email || ''),
        }));


        setExecutives(cleanExecutives);
      }
    } catch (error) {
      console.error('Error loading executives:', error);
      toast.error('Failed to load executives');
    } finally {
      setLoadingExecutives(false);
    }
  };

  // Load products/issues
  const loadProductsIssues = async () => {
    try {
      setLoadingProducts(true);
      const response = await masterDataAPI.getProductsIssues({
        is_active: true,
        limit: 100,
        sortBy: 'name',
        sortOrder: 'ASC'
      });

      if (response.data?.success && response.data?.data) {
        // Handle different possible response structures
        const productsData = response.data.data.productsIssues ||
                           response.data.data.products_issues ||
                           response.data.data;
        const safeProductsData = Array.isArray(productsData) ? productsData : [];

        // Ensure all product objects have proper string values
        const cleanProducts = safeProductsData.map(product => ({
          ...product,
          id: typeof product.id === 'object' ? JSON.stringify(product.id) : String(product.id || ''),
          name: typeof product.name === 'object' ? JSON.stringify(product.name) : String(product.name || ''),
          description: typeof product.description === 'object' ? JSON.stringify(product.description) : String(product.description || ''),
        }));


        setProductsIssues(cleanProducts);
      }
    } catch (error) {
      console.error('Error loading products/issues:', error);
      toast.error('Failed to load products/issues');
    } finally {
      setLoadingProducts(false);
    }
  };

  // Load existing lead data for editing
  const loadLeadData = async () => {
    try {
      setInitialLoading(true);
      const response = await leadAPI.getById(id);
      
      if (response.data?.success) {
        const lead = response.data.data.lead;
        setFormData({
          customer_name: lead.customer_name || '',
          products_issues_id: lead.products_issues_id || '',
          amount: lead.amount || '',
          contact_no: lead.contact_no || '',
          country_code: lead.country_code || '+91',
          status_id: lead.status_id || '',
          remarks: lead.remarks || '',
          executive_id: lead.executive_id || '',
          contacted_executive_id: lead.contacted_executive_id || '',
          follow_up_date: lead.follow_up_date || '',
          ref_name: lead.ref_name || '',
          ref_contact_no: lead.ref_contact_no || '',
          ref_country_code: lead.ref_country_code || '+91',
          ref_amount: lead.ref_amount || ''
        });
      } else {
        toast.error('Failed to load lead data');
        navigate('/leads');
      }
    } catch (error) {
      console.error('Error loading lead:', error);
      toast.error('Failed to load lead data');
      navigate('/leads');
    } finally {
      setInitialLoading(false);
    }
  };

  // Load master data on component mount
  useEffect(() => {
    loadLeadStatuses();
    loadExecutives();
    loadProductsIssues();

    if (isEdit) {
      loadLeadData();
    }
  }, [isEdit, id]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSelectChange = (name, selectedOption) => {
    setFormData(prev => ({
      ...prev,
      [name]: selectedOption ? selectedOption.value || selectedOption.name : ''
    }));

    // Clear error when user makes selection
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Mandatory field validations
    if (!formData.customer_name || formData.customer_name.trim() === '') {
      newErrors.customer_name = 'Customer name is required';
    }

    if (!formData.contact_no || formData.contact_no.trim() === '') {
      newErrors.contact_no = 'Contact number is required';
    } else {
      // Validate phone number format
      const phoneDigits = formData.contact_no.replace(/\D/g, '');
      if (phoneDigits.length < 7 || phoneDigits.length > 15) {
        newErrors.contact_no = 'Contact number must be between 7 and 15 digits';
      }
    }

    // Use API validation for other fields
    const validation = leadAPI.validateForm(formData, ['customer_name', 'contact_no']);

    if (!validation.isValid) {
      Object.assign(newErrors, validation.errors);
    }

    // Additional custom validations
    if (formData.amount && (isNaN(formData.amount) || parseFloat(formData.amount) < 0)) {
      newErrors.amount = 'Amount must be a positive number';
    }

    if (formData.ref_amount && (isNaN(formData.ref_amount) || parseFloat(formData.ref_amount) < 0)) {
      newErrors.ref_amount = 'Reference amount must be a positive number';
    }

    if (formData.follow_up_date && new Date(formData.follow_up_date) < new Date().setHours(0, 0, 0, 0)) {
      newErrors.follow_up_date = 'Follow up date cannot be in the past';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      setLoading(true);
      
      const submitData = { ...formData };
      
      // Convert empty strings to null for optional fields
      Object.keys(submitData).forEach(key => {
        if (submitData[key] === '') {
          submitData[key] = null;
        }
      });

      // Ensure executive fields are properly handled
      if (!submitData.executive_id || submitData.executive_id === '') {
        submitData.executive_id = null;
      }
      if (!submitData.contacted_executive_id || submitData.contacted_executive_id === '') {
        submitData.contacted_executive_id = null;
      }

      let response;
      if (isEdit) {
        response = await leadAPI.update(id, submitData);
      } else {
        response = await leadAPI.create(submitData);
      }

      if (response.data?.success) {
        toast.success(`Lead ${isEdit ? 'updated' : 'created'} successfully`);
        navigate('/leads');
      } else {
        toast.error(response.data?.message || `Failed to ${isEdit ? 'update' : 'create'} lead`);
      }
    } catch (error) {
      console.error('Error submitting lead:', error);
      
      // Handle validation errors from backend
      if (error.response?.data?.errors) {
        const backendErrors = error.response.data.errors;

        // Convert technical error messages to user-friendly ones
        const userFriendlyErrors = {};
        Object.keys(backendErrors).forEach(field => {
          let message = backendErrors[field];

          // Convert technical messages to user-friendly ones
          if (field === 'executive_id' && message.includes('UUID')) {
            message = 'Please select a valid executive from the list or leave it empty';
          } else if (field === 'contacted_executive_id' && message.includes('UUID')) {
            message = 'Please select a valid contacted executive from the list or leave it empty';
          } else if (field === 'status_id' && message.includes('UUID')) {
            message = 'Please select a valid status from the list';
          } else if (field === 'products_issues_id' && message.includes('UUID')) {
            message = 'Please select a valid product/service from the list';
          }

          userFriendlyErrors[field] = message;
        });

        setErrors(userFriendlyErrors);
        toast.error('Please fix the validation errors and try again');
      } else {
        const errorMessage = error.response?.data?.message || `Failed to ${isEdit ? 'update' : 'create'} lead`;
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {isEdit ? 'Edit Lead' : 'Add New Lead'}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {isEdit ? 'Update lead information' : 'Create a new sales lead'}
          </p>
        </div>
        <button
          onClick={() => navigate('/leads')}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
        >
          <FaTimes className="mr-2 h-4 w-4" />
          Cancel
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaUser className="mr-2 h-5 w-5 text-theme-600" />
              Basic Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Name
                </label>
                <input
                  type="text"
                  name="customer_name"
                  value={formData.customer_name}
                  onChange={handleInputChange}
                  placeholder="Enter customer name"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                    errors.customer_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.customer_name && <p className="mt-1 text-sm text-red-600">{errors.customer_name}</p>}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Products/Services
                </label>
                <SearchableSelect
                  options={productsIssues}
                  value={formData.products_issues_id}
                  onChange={(value) => {
                    setFormData(prev => ({ ...prev, products_issues_id: value }));
                    if (errors.products_issues_id) {
                      setErrors(prev => ({ ...prev, products_issues_id: '' }));
                    }
                  }}
                  placeholder="Type to search products or services..."
                  searchFields={['name', 'description']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingProducts}
                  className={errors.products_issues_id ? 'border-red-500' : ''}
                  minSearchLength={1}
                  maxResults={20}
                />
                {errors.products_issues_id && <p className="mt-1 text-sm text-red-600">{errors.products_issues_id}</p>}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaPhone className="mr-2 h-5 w-5 text-theme-600" />
              Contact Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Number
                </label>
                <div className="flex space-x-2">
                  <select
                    name="country_code"
                    value={formData.country_code}
                    onChange={handleInputChange}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent"
                  >
                    {countryCodeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <input
                    type="text"
                    name="contact_no"
                    value={formData.contact_no}
                    onChange={handleInputChange}
                    placeholder="Enter phone number"
                    className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                      errors.contact_no ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                </div>
                {errors.contact_no && <p className="mt-1 text-sm text-red-600">{errors.contact_no}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Amount
                </label>
                <div className="relative">
                  <FaRupeeSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    placeholder="Enter potential deal amount"
                    min="0"
                    step="0.01"
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                      errors.amount ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                </div>
                {errors.amount && <p className="mt-1 text-sm text-red-600">{errors.amount}</p>}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Lead Management */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaClipboardList className="mr-2 h-5 w-5 text-theme-600" />
              Lead Management
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status_id"
                  value={formData.status_id || ''}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                    errors.status_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={loadingStatuses}
                >
                  <option value="">Select lead status</option>
                  {leadStatuses.map(status => {
                    // Safely render status properties
                    const statusId = typeof status.id === 'object' ? JSON.stringify(status.id) : String(status.id || '');
                    const statusName = typeof status.name === 'object' ? JSON.stringify(status.name) : String(status.name || 'Unknown Status');

                    return (
                      <option key={statusId} value={statusId}>
                        {statusName}
                      </option>
                    );
                  })}
                </select>
                {loadingStatuses && <p className="mt-1 text-sm text-gray-500">Loading statuses...</p>}
                {errors.status_id && <p className="mt-1 text-sm text-red-600">{errors.status_id}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assigned Executive (Optional)
                </label>
                <SearchableSelect
                  options={executives.map(exec => {
                    const firstName = typeof exec.first_name === 'string' ? exec.first_name : '';
                    const lastName = typeof exec.last_name === 'string' ? exec.last_name : '';
                    const email = typeof exec.email === 'string' ? exec.email : '';

                    return {
                      ...exec,
                      name: `${firstName} ${lastName}`.trim() || 'Unknown Executive',
                      searchText: `${firstName} ${lastName} ${email}`.trim()
                    };
                  })}
                  value={formData.executive_id}
                  onChange={(value) => {
                    setFormData(prev => ({ ...prev, executive_id: value }));
                    if (errors.executive_id) {
                      setErrors(prev => ({ ...prev, executive_id: '' }));
                    }
                  }}
                  placeholder="Type to search executives or leave empty..."
                  searchFields={['name', 'first_name', 'last_name', 'email', 'searchText']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingExecutives}
                  className={errors.executive_id ? 'border-red-500' : ''}
                  minSearchLength={1}
                  maxResults={20}
                  allowClear={true}
                />
                {errors.executive_id && <p className="mt-1 text-sm text-red-600">{errors.executive_id}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contacted Executive (Optional)
                </label>
                <SearchableSelect
                  options={executives.map(exec => {
                    const firstName = typeof exec.first_name === 'string' ? exec.first_name : '';
                    const lastName = typeof exec.last_name === 'string' ? exec.last_name : '';
                    const email = typeof exec.email === 'string' ? exec.email : '';

                    return {
                      ...exec,
                      name: `${firstName} ${lastName}`.trim() || 'Unknown Executive',
                      searchText: `${firstName} ${lastName} ${email}`.trim()
                    };
                  })}
                  value={formData.contacted_executive_id}
                  onChange={(value) => {
                    setFormData(prev => ({ ...prev, contacted_executive_id: value }));
                    if (errors.contacted_executive_id) {
                      setErrors(prev => ({ ...prev, contacted_executive_id: '' }));
                    }
                  }}
                  placeholder="Type to search contacted executive or leave empty..."
                  searchFields={['name', 'first_name', 'last_name', 'email', 'searchText']}
                  displayField="name"
                  valueField="id"
                  disabled={loadingExecutives}
                  className={errors.contacted_executive_id ? 'border-red-500' : ''}
                  minSearchLength={1}
                  maxResults={20}
                  allowClear={true}
                />
                {errors.contacted_executive_id && <p className="mt-1 text-sm text-red-600">{errors.contacted_executive_id}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Follow Up Date
                </label>
                <input
                  type="date"
                  name="follow_up_date"
                  value={formData.follow_up_date}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                    errors.follow_up_date ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.follow_up_date && <p className="mt-1 text-sm text-red-600">{errors.follow_up_date}</p>}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Remarks
                </label>
                <textarea
                  name="remarks"
                  value={formData.remarks}
                  onChange={handleInputChange}
                  placeholder="Add any additional notes or remarks"
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                    errors.remarks ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.remarks && <p className="mt-1 text-sm text-red-600">{errors.remarks}</p>}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Reference Information */}
        <Card>
          <CardBody className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaUserTie className="mr-2 h-5 w-5 text-theme-600" />
              Reference Information (Optional)
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reference Name
                </label>
                <input
                  type="text"
                  name="ref_name"
                  value={formData.ref_name}
                  onChange={handleInputChange}
                  placeholder="Enter reference person name"
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                    errors.ref_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.ref_name && <p className="mt-1 text-sm text-red-600">{errors.ref_name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reference Contact
                </label>
                <div className="flex space-x-2">
                  <select
                    name="ref_country_code"
                    value={formData.ref_country_code}
                    onChange={handleInputChange}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent"
                  >
                    {countryCodeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                  <input
                    type="text"
                    name="ref_contact_no"
                    value={formData.ref_contact_no}
                    onChange={handleInputChange}
                    placeholder="Enter reference phone number"
                    className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                      errors.ref_contact_no ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                </div>
                {errors.ref_contact_no && <p className="mt-1 text-sm text-red-600">{errors.ref_contact_no}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reference Amount
                </label>
                <div className="relative">
                  <FaRupeeSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="number"
                    name="ref_amount"
                    value={formData.ref_amount}
                    onChange={handleInputChange}
                    placeholder="Enter reference amount"
                    min="0"
                    step="0.01"
                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-theme-500 focus:border-transparent ${
                      errors.ref_amount ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                </div>
                {errors.ref_amount && <p className="mt-1 text-sm text-red-600">{errors.ref_amount}</p>}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6">
          <button
            type="button"
            onClick={() => navigate('/leads')}
            className="px-6 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
            style={{ backgroundColor: '#1d5795' }}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {isEdit ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                <FaSave className="mr-2 h-4 w-4" />
                {isEdit ? 'Update Lead' : 'Create Lead'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default LeadForm;
