import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';
import moment from 'moment';

/**
 * Comprehensive Renewal Notification Service
 * Handles scheduling, sending, and tracking of renewal notifications
 */
class RenewalNotificationService {
  constructor() {
    this.notificationService = null;
  }

  /**
   * Initialize the service with notification service dependency
   */
  async initialize() {
    if (!this.notificationService) {
      const NotificationServiceModule = await import('./NotificationService.js');
      const NotificationService = NotificationServiceModule.default;
      this.notificationService = new NotificationService();
    }
  }

  /**
   * Schedule renewal notifications for all active customers
   * This method should be called when renewal settings change or new customers are added
   */
  async scheduleAllRenewalNotifications(tenantId = null) {
    try {
      await this.initialize();
      logger.info('🔄 Starting renewal notification scheduling process...');

      const tenants = tenantId 
        ? [{ id: tenantId }] 
        : await models.Tenant.findAll({ where: { is_active: true }, attributes: ['id'] });

      let totalScheduled = 0;

      for (const tenant of tenants) {
        const scheduled = await this.scheduleRenewalNotificationsForTenant(tenant.id);
        totalScheduled += scheduled;
      }

      logger.info(`✅ Renewal notification scheduling completed. Total scheduled: ${totalScheduled}`);
      return totalScheduled;
    } catch (error) {
      logger.error('❌ Error in scheduleAllRenewalNotifications:', error);
      throw error;
    }
  }

  /**
   * Schedule renewal notifications for a specific tenant
   */
  async scheduleRenewalNotificationsForTenant(tenantId) {
    try {
      logger.info(`📋 Scheduling renewal notifications for tenant: ${tenantId}`);

      // Get active renewal notification settings for this tenant
      const renewalSettings = await models.RenewalNotificationSettings.findAll({
        where: {
          tenant_id: tenantId,
          is_active: true,
        },
      });

      if (renewalSettings.length === 0) {
        logger.info(`⚠️ No active renewal settings found for tenant: ${tenantId}`);
        return 0;
      }

      let totalScheduled = 0;

      // Process each renewal setting
      for (const setting of renewalSettings) {
        const scheduled = await this.scheduleNotificationsForSetting(tenantId, setting);
        totalScheduled += scheduled;
      }

      return totalScheduled;
    } catch (error) {
      logger.error(`❌ Error scheduling notifications for tenant ${tenantId}:`, error);
      throw error;
    }
  }

  /**
   * Schedule notifications for a specific renewal setting
   */
  async scheduleNotificationsForSetting(tenantId, setting) {
    try {
      logger.info(`📅 Processing setting: ${setting.field_name} for tenant: ${tenantId}`);

      const renewalData = await this.getRenewalDataForSetting(tenantId, setting);
      let scheduledCount = 0;

      for (const renewal of renewalData) {
        const scheduled = await this.scheduleNotificationsForRenewal(renewal, setting);
        scheduledCount += scheduled;
      }

      logger.info(`✅ Scheduled ${scheduledCount} notifications for ${setting.field_name}`);
      return scheduledCount;
    } catch (error) {
      logger.error(`❌ Error processing setting ${setting.field_name}:`, error);
      throw error;
    }
  }

  /**
   * Get renewal data based on the setting field name
   */
  async getRenewalDataForSetting(tenantId, setting) {
    const today = new Date();
    const futureDate = moment().add(Math.max(...setting.reminder_days), 'days').toDate();

    switch (setting.field_name) {
      case 'amc_expiry_date':
      case 'amc_renewal_date':
        return await models.CustomerAMC.findAll({
          where: {
            is_active: true,
            status: ['active', 'renewed'],
            [Op.or]: [
              { end_date: { [Op.between]: [today, futureDate] } },
              { renewal_date: { [Op.between]: [today, futureDate] } },
            ],
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'email', 'phone'],
              where: {
                is_active: true,
                tenant_id: tenantId
              },
            },
          ],
        });

      case 'tss_expiry_date':
      case 'tss_renewal_date':
        return await models.CustomerTSS.findAll({
          where: {
            is_active: true,
            status: ['active'],
            [Op.or]: [
              { expiry_date: { [Op.between]: [today, futureDate] } },
              { renewal_date: { [Op.between]: [today, futureDate] } },
            ],
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'email', 'phone'],
              where: {
                is_active: true,
                tenant_id: tenantId
              },
            },
          ],
        });

      default:
        logger.warn(`⚠️ Unknown field name: ${setting.field_name}`);
        return [];
    }
  }

  /**
   * Schedule notifications for a specific renewal record
   */
  async scheduleNotificationsForRenewal(renewal, setting) {
    try {
      const expiryDate = this.getExpiryDateFromRenewal(renewal, setting);
      if (!expiryDate) {
        logger.warn(`⚠️ No expiry date found for renewal ${renewal.id}`);
        return 0;
      }

      const renewalType = this.getRenewalTypeFromSetting(setting);
      let scheduledCount = 0;

      // Schedule notifications for each reminder day
      for (const days of setting.reminder_days) {
        const notifyDate = moment(expiryDate).subtract(days, 'days').format('YYYY-MM-DD');
        
        // Only schedule future notifications
        if (moment(notifyDate).isSameOrAfter(moment(), 'day')) {
          const scheduled = await this.createNotificationSchedule({
            tenant_id: renewal.tenant_id,
            customer_id: renewal.customer_id,
            renewal_type: renewalType,
            renewal_record_id: renewal.id,
            expiry_date: expiryDate,
            notify_at: notifyDate,
            days_before_expiry: days,
            notification_type: 'reminder',
          });

          if (scheduled) scheduledCount++;
        }
      }

      return scheduledCount;
    } catch (error) {
      logger.error(`❌ Error scheduling notifications for renewal ${renewal.id}:`, error);
      return 0;
    }
  }

  /**
   * Create a notification schedule entry (with duplicate prevention)
   */
  async createNotificationSchedule(scheduleData) {
    try {
      const [schedule, created] = await models.NotificationSchedule.findOrCreate({
        where: {
          customer_id: scheduleData.customer_id,
          renewal_type: scheduleData.renewal_type,
          renewal_record_id: scheduleData.renewal_record_id,
          notify_at: scheduleData.notify_at,
        },
        defaults: scheduleData,
      });

      if (created) {
        logger.debug(`📅 Scheduled notification for ${scheduleData.notify_at}`);
        return true;
      } else {
        logger.debug(`⏭️ Notification already scheduled for ${scheduleData.notify_at}`);
        return false;
      }
    } catch (error) {
      logger.error('❌ Error creating notification schedule:', error);
      return false;
    }
  }

  /**
   * Process pending notifications (called by cron job)
   */
  async processPendingNotifications(date = null) {
    try {
      await this.initialize();
      const targetDate = date || moment().format('YYYY-MM-DD');
      
      logger.info(`🔄 Processing pending notifications for date: ${targetDate}`);

      const pendingNotifications = await models.NotificationSchedule.getPendingNotifications(targetDate);
      
      if (pendingNotifications.length === 0) {
        logger.info('✅ No pending notifications found');
        return { processed: 0, successful: 0, failed: 0 };
      }

      logger.info(`📧 Found ${pendingNotifications.length} pending notifications`);

      let successful = 0;
      let failed = 0;

      for (const notification of pendingNotifications) {
        try {
          const result = await this.sendRenewalNotification(notification);
          if (result.success) {
            successful++;
            await notification.markAsSent({ email: result.email });
          } else {
            failed++;
            await notification.markAsFailed(result.error || 'Unknown error');
          }
        } catch (error) {
          failed++;
          logger.error(`❌ Error sending notification ${notification.id}:`, error);
          await notification.markAsFailed(error.message);
        }
      }

      logger.info(`✅ Processed ${pendingNotifications.length} notifications: ${successful} successful, ${failed} failed`);
      
      return {
        processed: pendingNotifications.length,
        successful,
        failed,
      };
    } catch (error) {
      logger.error('❌ Error processing pending notifications:', error);
      throw error;
    }
  }

  /**
   * Send a renewal notification
   */
  async sendRenewalNotification(notificationSchedule) {
    try {
      const renewalData = await this.getRenewalDataById(
        notificationSchedule.renewal_type,
        notificationSchedule.renewal_record_id
      );

      if (!renewalData) {
        throw new Error(`Renewal record not found: ${notificationSchedule.renewal_record_id}`);
      }

      const customer = notificationSchedule.customer;
      if (!customer || !customer.email) {
        throw new Error('Customer email not available');
      }

      // Prepare email data
      const emailData = this.prepareRenewalEmailData(renewalData, notificationSchedule);

      // Determine template based on days remaining and notification type
      const templateType = this.getTemplateType(notificationSchedule);

      // Send email using notification service
      const result = await this.notificationService.sendEmailNotification(
        customer,
        templateType,
        emailData
      );

      return {
        success: result.success,
        email: result,
        error: result.success ? null : result.message,
      };
    } catch (error) {
      logger.error('❌ Error sending renewal notification:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Helper methods
   */
  getExpiryDateFromRenewal(renewal, setting) {
    switch (setting.field_name) {
      case 'amc_expiry_date':
        return renewal.end_date;
      case 'amc_renewal_date':
        return renewal.renewal_date;
      case 'tss_expiry_date':
        return renewal.expiry_date;
      case 'tss_renewal_date':
        return renewal.renewal_date;
      default:
        return null;
    }
  }

  getRenewalTypeFromSetting(setting) {
    if (setting.field_name.includes('amc')) return 'amc';
    if (setting.field_name.includes('tss')) return 'tss';
    return 'license';
  }

  async getRenewalDataById(renewalType, recordId) {
    switch (renewalType) {
      case 'amc':
        return await models.CustomerAMC.findByPk(recordId, {
          include: [{ model: models.Customer, as: 'customer' }],
        });
      case 'tss':
        return await models.CustomerTSS.findByPk(recordId, {
          include: [{ model: models.Customer, as: 'customer' }],
        });
      default:
        return null;
    }
  }

  getTemplateType(notificationSchedule) {
    const daysRemaining = moment(notificationSchedule.expiry_date).diff(moment(), 'days');

    // If overdue
    if (daysRemaining < 0) {
      return 'renewal_overdue';
    }

    // If urgent (2-3 days)
    if (daysRemaining <= 3) {
      return 'renewal_urgent';
    }

    // Default reminder template
    return 'renewal_reminder';
  }

  prepareRenewalEmailData(renewalData, notificationSchedule) {
    const daysRemaining = moment(notificationSchedule.expiry_date).diff(moment(), 'days');
    const isOverdue = daysRemaining < 0;

    return {
      customer_name: renewalData.customer?.company_name || 'Valued Customer',
      company_name: renewalData.customer?.company_name || '',
      renewal_type: notificationSchedule.renewal_type.toUpperCase(),
      expiry_date: moment(notificationSchedule.expiry_date).format('DD/MM/YYYY'),
      days_remaining: Math.max(0, daysRemaining),
      days_overdue: isOverdue ? Math.abs(daysRemaining) : 0,
      renewal_amount: renewalData.contract_value || renewalData.amount || 0,
      contact_person: renewalData.customer?.company_name || '',
      contact_email: process.env.SMTP_USER || '<EMAIL>',
      service_type: notificationSchedule.renewal_type.toUpperCase(),
      days_before_expiry: notificationSchedule.days_before_expiry,
      is_overdue: isOverdue,
      is_urgent: daysRemaining <= 3 && daysRemaining >= 0,
    };
  }

  /**
   * Clean up old notification schedules
   */
  async cleanupOldSchedules(daysOld = 90) {
    try {
      const cutoffDate = moment().subtract(daysOld, 'days').toDate();
      
      const result = await models.NotificationSchedule.destroy({
        where: {
          status: ['sent', 'cancelled'],
          updated_at: {
            [Op.lt]: cutoffDate,
          },
        },
      });

      logger.info(`🧹 Cleaned up ${result} old notification schedules`);
      return result;
    } catch (error) {
      logger.error('❌ Error cleaning up old schedules:', error);
      throw error;
    }
  }
}

export default RenewalNotificationService;
