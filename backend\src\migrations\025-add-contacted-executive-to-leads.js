import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  try {
    // Add contacted_executive_id column to leads table
    await queryInterface.addColumn('leads', 'contacted_executive_id', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Executive who made contact with the lead',
    });

    console.log('✅ Added contacted_executive_id column to leads table');
  } catch (error) {
    console.error('❌ Error adding contacted_executive_id column:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  try {
    // Remove contacted_executive_id column from leads table
    await queryInterface.removeColumn('leads', 'contacted_executive_id');
    
    console.log('✅ Removed contacted_executive_id column from leads table');
  } catch (error) {
    console.error('❌ Error removing contacted_executive_id column:', error);
    throw error;
  }
};
