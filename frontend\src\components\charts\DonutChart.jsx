/**
 * Donut Chart Component
 * Responsive donut chart using Recharts with center content support
 */

import React from 'react';
import <PERSON><PERSON><PERSON> from './PieChart';
import { formatNumber } from './chartUtils';

const DonutChart = ({
  data = [],
  dataKey = 'value',
  nameKey = 'name',
  height = 300,
  showLegend = true,
  showTooltip = true,
  showLabels = false,
  showCenter = true,
  centerContent = null,
  colorScheme = 'primary',
  innerRadius = 60,
  outerRadius = null,
  theme = 'default',
  className = '',
  ...props
}) => {
  // Calculate total and primary metric for center display
  const total = data.reduce((sum, item) => sum + (Number(item[dataKey]) || 0), 0);
  const primaryItem = data.length > 0 ? data[0] : null;

  // Default center content
  const defaultCenterContent = showCenter && (
    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">
          {formatNumber(total)}
        </div>
        <div className="text-sm text-gray-500 mt-1">
          Total
        </div>
      </div>
    </div>
  );

  return (
    <div className={`relative ${className}`} {...props}>
      <PieChart
        data={data}
        dataKey={dataKey}
        nameKey={nameKey}
        height={height}
        showLegend={showLegend}
        showTooltip={showTooltip}
        showLabels={showLabels}
        colorScheme={colorScheme}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        theme={theme}
      />
      {centerContent || defaultCenterContent}
    </div>
  );
};

export default DonutChart;
