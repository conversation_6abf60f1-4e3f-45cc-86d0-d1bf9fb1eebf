import React, { useState, useEffect, useRef } from 'react';
import { FaMap, FaTimes, FaMapMarkerAlt, FaSearch } from 'react-icons/fa';

const MapLocationPicker = ({
  isOpen,
  onClose,
  onLocationSelect,
  initialLocation = '',
  initialLat = '',
  initialLng = ''
}) => {
  const [selectedLocation, setSelectedLocation] = useState({
    address: initialLocation,
    lat: initialLat,
    lng: initialLng
  });
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markerRef = useRef(null);
  const geocoderRef = useRef(null);

  // Mock Google Maps Integration (no API key required)
  useEffect(() => {
    if (isOpen) {
      // Always use mock mode for development
      setIsLoading(false);
    }
  }, [isOpen]);

  const loadGoogleMapsAPI = () => {
    if (window.google && window.google.maps) {
      initializeMap();
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.defer = true;
    script.onload = initializeMap;
    script.onerror = () => {
      console.error('Failed to load Google Maps API - check if VITE_GOOGLE_MAPS_API_KEY is set');
      // Fallback to demo mode
      setIsLoading(false);
    };
    document.head.appendChild(script);
  };

  const initializeMap = () => {
    if (!mapRef.current || !window.google) return;

    const defaultCenter = {
      lat: initialLat ? parseFloat(initialLat) : 28.6139,
      lng: initialLng ? parseFloat(initialLng) : 77.2090
    };

    mapInstanceRef.current = new window.google.maps.Map(mapRef.current, {
      center: defaultCenter,
      zoom: 13,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
    });

    geocoderRef.current = new window.google.maps.Geocoder();

    // Add click listener to map
    mapInstanceRef.current.addListener('click', handleMapClick);

    // Add initial marker if coordinates exist
    if (initialLat && initialLng) {
      addMarker(defaultCenter);
    }
  };

  const handleMapClick = (event) => {
    const lat = event.latLng.lat();
    const lng = event.latLng.lng();

    addMarker({ lat, lng });
    reverseGeocode(lat, lng);
  };

  const addMarker = (position) => {
    if (markerRef.current) {
      markerRef.current.setMap(null);
    }

    markerRef.current = new window.google.maps.Marker({
      position,
      map: mapInstanceRef.current,
      draggable: true,
      title: 'Selected Location'
    });

    markerRef.current.addListener('dragend', (event) => {
      const lat = event.latLng.lat();
      const lng = event.latLng.lng();
      reverseGeocode(lat, lng);
    });

    setSelectedLocation(prev => ({
      ...prev,
      lat: position.lat.toFixed(6),
      lng: position.lng.toFixed(6)
    }));
  };

  const reverseGeocode = (lat, lng) => {
    if (!geocoderRef.current) return;

    geocoderRef.current.geocode(
      { location: { lat, lng } },
      (results, status) => {
        if (status === 'OK' && results[0]) {
          setSelectedLocation(prev => ({
            ...prev,
            address: results[0].formatted_address,
            lat: lat.toFixed(6),
            lng: lng.toFixed(6)
          }));
        } else {
          setSelectedLocation(prev => ({
            ...prev,
            address: `Location at ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
            lat: lat.toFixed(6),
            lng: lng.toFixed(6)
          }));
        }
      }
    );
  };

  const handleAddressSearch = async () => {
    if (!searchQuery.trim() || !geocoderRef.current) return;

    setIsLoading(true);

    geocoderRef.current.geocode(
      { address: searchQuery },
      (results, status) => {
        setIsLoading(false);

        if (status === 'OK' && results[0]) {
          const { location } = results[0].geometry;
          const lat = location.lat();
          const lng = location.lng();

          mapInstanceRef.current.setCenter({ lat, lng });
          mapInstanceRef.current.setZoom(15);

          addMarker({ lat, lng });

          setSelectedLocation({
            address: results[0].formatted_address,
            lat: lat.toFixed(6),
            lng: lng.toFixed(6)
          });
        } else {
          alert('Location not found. Please try a different search term.');
        }
      }
    );
  };

  // Fallback for when Google Maps is not available
  const handleLocationClick = (e) => {
    if (window.google && window.google.maps) return; // Use Google Maps if available

    // Simulate map click coordinates (fallback)
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Convert to approximate lat/lng (this is just for demo)
    const lat = (28.6139 + (y / rect.height - 0.5) * 0.1).toFixed(6);
    const lng = (77.2090 + (x / rect.width - 0.5) * 0.1).toFixed(6);

    setSelectedLocation({
      address: `Location at ${lat}, ${lng}`,
      lat,
      lng
    });
  };

  const handleConfirmLocation = () => {
    if (selectedLocation.lat && selectedLocation.lng) {
      onLocationSelect({
        address: selectedLocation.address,
        latitude: selectedLocation.lat,
        longitude: selectedLocation.lng
      });
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-white flex items-center justify-center z-modal p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[95vh] flex flex-col mx-auto">
        {/* Header */}
        <div className="px-6 py-4 flex justify-between items-center border-b border-gray-200 flex-shrink-0">
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <FaMap className="mr-3" />
            Select Location
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 flex-1 overflow-y-auto">
          {/* Search Bar */}
          <div className="mb-6">
            <label className="block text-sm font-bold text-gray-700 mb-2">
              Search Location
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                className="flex-1 px-4 py-3 border-2 border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:border-blue-300 transition-all duration-200"
                placeholder="Enter address or location name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddressSearch()}
              />
              <button
                onClick={handleAddressSearch}
                disabled={isLoading || !searchQuery.trim()}
                className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 transition-all duration-200 flex items-center"
              >
                <FaSearch className="mr-2" />
                {isLoading ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>

          {/* Map Area */}
          <div className="mb-6">
            <label className="block text-sm font-bold text-gray-700 mb-2">
              Demo Map - Click to select location
            </label>
            {/* Mock demo map */}
            <div
              className="w-full h-80 bg-green-100 border-2 border-blue-200 rounded-xl cursor-crosshair relative overflow-hidden"
              onClick={handleLocationClick}
            >
              {/* Mock map background */}
              <div className="absolute inset-0 bg-green-200 opacity-50"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-gray-600">
                  <FaMap className="h-16 w-16 mx-auto mb-4 text-blue-500" />
                  <p className="text-lg font-medium">Click anywhere to select location</p>
                  <p className="text-sm">Demo map - Add Google Maps API key for full functionality</p>
                </div>
              </div>

              {/* Selected location marker */}
              {selectedLocation.lat && selectedLocation.lng && (
                <div
                  className="absolute transform -translate-x-1/2 -translate-y-full"
                  style={{
                    left: `${50 + (parseFloat(selectedLocation.lng) - 77.2090) * 1000}%`,
                    top: `${50 - (parseFloat(selectedLocation.lat) - 28.6139) * 1000}%`
                  }}
                >
                  <FaMapMarkerAlt className="h-8 w-8 text-red-500 drop-shadow-lg" />
                </div>
              )}
            </div>
          </div>

          {/* Selected Location Info */}
          {selectedLocation.lat && selectedLocation.lng && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
              <h4 className="font-bold text-blue-700 mb-2">Selected Location:</h4>
              <p className="text-sm text-gray-700 mb-2">{selectedLocation.address}</p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Latitude:</span>
                  <span className="ml-2 text-gray-900">{selectedLocation.lat}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Longitude:</span>
                  <span className="ml-2 text-gray-900">{selectedLocation.lng}</span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6 flex-shrink-0">
            <button
              onClick={onClose}
              className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all duration-200"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirmLocation}
              disabled={!selectedLocation.lat || !selectedLocation.lng}
              className="px-6 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              Confirm Location
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapLocationPicker;
