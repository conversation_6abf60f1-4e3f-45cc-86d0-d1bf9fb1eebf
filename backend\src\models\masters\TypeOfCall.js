import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const TypeOfCall = sequelize.define('TypeOfCall', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('amc', 'tss', 'support', 'maintenance', 'training', 'other'),
      allowNull: false,
      defaultValue: 'support',
    },
    service_type: {
      type: DataTypes.ENUM('onsite', 'online', 'both'),
      allowNull: false,
      defaultValue: 'both',
      comment: 'Which service types this call type applies to',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this type of call is billable',
    },
    default_duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Default duration in minutes',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this call type requires approval',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'type_of_calls',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['service_type'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  TypeOfCall.getDefaultTypes = function() {
    return [
      {
        name: 'AMC Visit',
        code: 'AMC_VISIT',
        description: 'Annual Maintenance Contract visit',
        category: 'amc',
        service_type: 'onsite',
        is_billable: false,
        default_duration: 120,
        requires_approval: false,
        sort_order: 1,
      },
      {
        name: 'TSS Visit',
        code: 'TSS_VISIT',
        description: 'Tally Software Services visit',
        category: 'tss',
        service_type: 'onsite',
        is_billable: false,
        default_duration: 90,
        requires_approval: false,
        sort_order: 2,
      },
      {
        name: 'Computer Repair',
        code: 'COMPUTER_REPAIR',
        description: 'Computer hardware/software repair',
        category: 'support',
        service_type: 'onsite',
        is_billable: true,
        default_duration: 180,
        requires_approval: false,
        sort_order: 3,
      },
      {
        name: 'Online Support',
        code: 'ONLINE_SUPPORT',
        description: 'Remote online support session',
        category: 'support',
        service_type: 'online',
        is_billable: true,
        default_duration: 60,
        requires_approval: false,
        sort_order: 4,
      },
      {
        name: 'Training Session',
        code: 'TRAINING',
        description: 'Software training session',
        category: 'training',
        service_type: 'both',
        is_billable: true,
        default_duration: 240,
        requires_approval: true,
        sort_order: 5,
      },
      {
        name: 'Installation',
        code: 'INSTALLATION',
        description: 'Software installation and setup',
        category: 'support',
        service_type: 'onsite',
        is_billable: true,
        default_duration: 120,
        requires_approval: false,
        sort_order: 6,
      },
    ];
  };

  // Associations
  TypeOfCall.associate = function(models) {
    TypeOfCall.hasMany(models.ServiceCall, {
      foreignKey: 'type_of_call_id',
      as: 'serviceCalls',
    });
  };

  return TypeOfCall;
}
