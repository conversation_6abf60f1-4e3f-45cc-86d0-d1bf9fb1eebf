/* TallyCRM Tailwind CSS Base Styles */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom responsive utilities */
@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }

  /* Prevent horizontal overflow */
  .overflow-x-hidden {
    overflow-x: hidden;
  }

  /* Mobile touch optimization */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improved mobile button sizing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile form controls */
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
  }

  /* Custom breakpoint utilities */
  @screen xs {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
    .xs\:w-auto {
      width: auto;
    }
    .xs\:flex-row {
      flex-direction: row;
    }
    .xs\:text-sm {
      font-size: 0.875rem;
    }
    .xs\:p-3 {
      padding: 0.75rem;
    }
  }

  /* Mobile-specific table improvements */
  @media (max-width: 768px) {
    .mobile-table-scroll {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .mobile-card-shadow {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  }
}

/* Custom base styles */
@layer base {
  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    @apply bg-gray-50 text-gray-900 antialiased;
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold leading-tight;
  }

  h1 { @apply text-3xl; }
  h2 { @apply text-2xl; }
  h3 { @apply text-xl; }
  h4 { @apply text-lg; }
  h5 { @apply text-base; }
  h6 { @apply text-sm; }

  /* Links */
  a {
    @apply no-underline transition-colors duration-150;
    color: var(--primary-color, #1d5795);
  }

  a:hover {
    @apply underline;
    color: var(--primary-dark, #6b21a8);
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-200;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
}

/* Custom component styles */
@layer components {
  /* Link styles */
  .link-primary {
    color: var(--primary-color, #1d5795);
  }

  .link-primary:hover {
    color: var(--primary-dark, #6b21a8);
  }

  /* Sidebar styles with dynamic text colors */
  .sidebar {
    @apply fixed top-0 left-0 h-screen transition-all duration-300 overflow-y-auto z-50;
    background: var(--sidebar-background, #1d5795);
    color: var(--primary-text, #ffffff);
    width: 220px;
  }

  .sidebar.collapsed {
    width: 64px;
  }

  .sidebar .nav-link {
    @apply flex items-center px-3 py-2.5 transition-all duration-200 relative text-sm;
    color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.8);
  }

  .sidebar .nav-link:hover {
    color: var(--primary-text, #ffffff);
    background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.1);
  }

  .sidebar .nav-link.active {
    color: var(--primary-text, #ffffff);
    background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
    border-right: 2px solid var(--primary-text, #ffffff);
  }

  .sidebar .nav-link i {
    @apply text-base mr-2.5 min-w-[1.25rem];
  }

  .sidebar.collapsed .nav-link span {
    @apply hidden;
  }

  .sidebar.collapsed .nav-link {
    @apply justify-center px-2;
  }

  .sidebar.collapsed .nav-link i {
    @apply mr-0;
  }

  /* Masters dropdown styles */
  .masters-dropdown .nav-link {
    @apply relative;
  }

  .masters-submenu {
    @apply ml-4;
    background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.05);
    border-left: 2px solid rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
  }

  .nav-sub-link {
    @apply flex items-center justify-between px-4 py-2 transition-all duration-200 text-sm;
    color: rgba(var(--primary-text-rgb, 255, 255, 255), 0.7);
  }

  .nav-sub-link:hover {
    color: var(--primary-text, #ffffff);
    background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.05);
  }

  .nav-sub-link.active {
    @apply border-r-2 font-semibold;
    color: var(--primary-text, #ffffff);
    border-color: var(--primary-text, #ffffff);
    background: var(--primary-color, #1d5795);
  }

  .sub-item-label {
    @apply flex-1;
  }

  .sub-item-count {
    @apply text-xs px-2 py-1 rounded-full min-w-[1.5rem] text-center;
    background: rgba(var(--primary-text-rgb, 255, 255, 255), 0.2);
    color: var(--primary-text, #ffffff);
  }

  /* Mobile sidebar */
  @media (max-width: 768px) {
    .sidebar {
      @apply transform -translate-x-full;
    }

    .sidebar.show {
      @apply translate-x-0;
    }
  }

}
