import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LeaveBalance = sequelize.define('LeaveBalance', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    employee_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'executives',
        key: 'id',
      },
      comment: 'Employee for whom balance is maintained',
    },
    leave_type_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'leave_types',
        key: 'id',
      },
      comment: 'Type of leave',
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Calendar year for this balance',
    },
    allocated_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Total days allocated for the year',
    },
    used_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days already used/taken',
    },
    pending_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days in pending leave requests',
    },
    carry_forward_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days carried forward from previous year',
    },
    carry_forward_used: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Carry forward days already used',
    },
    carry_forward_expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When carry forward days expire',
    },
    encashed_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days encashed for money',
    },
    lapsed_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Days that lapsed without use',
    },
    adjustment_days: {
      type: DataTypes.DECIMAL(5, 1),
      allowNull: false,
      defaultValue: 0.0,
      comment: 'Manual adjustments (can be positive or negative)',
    },
    adjustment_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for manual adjustment',
    },
    adjusted_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'User who made the adjustment',
    },
    adjusted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When adjustment was made',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this balance record is active',
    },
    last_calculated_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When balance was last calculated',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'leave_balances',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['employee_id'],
      },
      {
        fields: ['leave_type_id'],
      },
      {
        fields: ['year'],
      },
      {
        fields: ['tenant_id', 'employee_id', 'leave_type_id', 'year'],
        unique: true,
        name: 'unique_employee_leave_year',
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['last_calculated_at'],
      },
    ],
  });

  // Virtual fields
  LeaveBalance.prototype.getRemainingDays = function() {
    return this.allocated_days + this.carry_forward_days + this.adjustment_days - this.used_days - this.carry_forward_used;
  };

  LeaveBalance.prototype.getAvailableDays = function() {
    return this.getRemainingDays() - this.pending_days;
  };

  LeaveBalance.prototype.getTotalDays = function() {
    return this.allocated_days + this.carry_forward_days + this.adjustment_days;
  };

  LeaveBalance.prototype.getUsagePercentage = function() {
    const total = this.getTotalDays();
    if (total === 0) return 0;
    return ((this.used_days + this.carry_forward_used) / total) * 100;
  };

  // Instance methods
  LeaveBalance.prototype.canTakeLeave = function(days) {
    return this.getAvailableDays() >= days;
  };

  LeaveBalance.prototype.addUsedDays = function(days) {
    // First use carry forward days, then current year allocation
    const carryForwardAvailable = this.carry_forward_days - this.carry_forward_used;
    
    if (days <= carryForwardAvailable) {
      this.carry_forward_used += days;
    } else {
      this.carry_forward_used = this.carry_forward_days;
      this.used_days += (days - carryForwardAvailable);
    }
    
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.addPendingDays = function(days) {
    this.pending_days += days;
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.removePendingDays = function(days) {
    this.pending_days = Math.max(0, this.pending_days - days);
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.makeAdjustment = function(days, reason, userId) {
    this.adjustment_days += days;
    this.adjustment_reason = reason;
    this.adjusted_by = userId;
    this.adjusted_at = new Date();
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.processCarryForward = function(carryForwardDays, expiryDate) {
    this.carry_forward_days = carryForwardDays;
    this.carry_forward_expires_at = expiryDate;
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.processEncashment = function(days) {
    if (this.getAvailableDays() >= days) {
      this.encashed_days += days;
      this.used_days += days; // Treat encashed days as used
      this.last_calculated_at = new Date();
      return true;
    }
    return false;
  };

  LeaveBalance.prototype.processLapse = function(days) {
    this.lapsed_days += days;
    this.last_calculated_at = new Date();
  };

  LeaveBalance.prototype.isCarryForwardExpired = function() {
    if (!this.carry_forward_expires_at) return false;
    return new Date() > new Date(this.carry_forward_expires_at);
  };

  // Class methods
  LeaveBalance.createYearlyBalance = function(employeeId, leaveTypeId, year, allocatedDays, tenantId) {
    return {
      tenant_id: tenantId,
      employee_id: employeeId,
      leave_type_id: leaveTypeId,
      year: year,
      allocated_days: allocatedDays,
      used_days: 0.0,
      pending_days: 0.0,
      carry_forward_days: 0.0,
      carry_forward_used: 0.0,
      encashed_days: 0.0,
      lapsed_days: 0.0,
      adjustment_days: 0.0,
      is_active: true,
      last_calculated_at: new Date(),
    };
  };

  LeaveBalance.calculateProRatedAllocation = function(joinDate, annualQuota, year) {
    const yearStart = new Date(year, 0, 1);
    const yearEnd = new Date(year, 11, 31);
    const employeeJoinDate = new Date(joinDate);
    
    // If joined before year start, give full allocation
    if (employeeJoinDate <= yearStart) {
      return annualQuota;
    }
    
    // If joined after year end, give zero allocation
    if (employeeJoinDate > yearEnd) {
      return 0;
    }
    
    // Calculate pro-rated allocation
    const totalDaysInYear = (yearEnd - yearStart) / (1000 * 60 * 60 * 24) + 1;
    const remainingDaysInYear = (yearEnd - employeeJoinDate) / (1000 * 60 * 60 * 24) + 1;
    
    return Math.round((annualQuota * remainingDaysInYear / totalDaysInYear) * 10) / 10;
  };

  // Associations
  LeaveBalance.associate = function(models) {
    LeaveBalance.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    LeaveBalance.belongsTo(models.Executive, {
      foreignKey: 'employee_id',
      as: 'employee',
    });

    LeaveBalance.belongsTo(models.LeaveType, {
      foreignKey: 'leave_type_id',
      as: 'leaveType',
    });

    LeaveBalance.belongsTo(models.User, {
      foreignKey: 'adjusted_by',
      as: 'adjuster',
    });
  };

  return LeaveBalance;
}
