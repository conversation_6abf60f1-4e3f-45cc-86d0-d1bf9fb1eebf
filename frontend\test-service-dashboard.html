<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>🧪 Service Dashboard Data Flow Test</h1>
    
    <div class="test-section">
        <h2>API Connection Test</h2>
        <button onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Service Stats API Test</h2>
        <button onclick="testServiceStats()">Test Service Stats</button>
        <div id="stats-test-results"></div>
        
        <div id="stats-display" class="stats-grid" style="display: none;">
            <div class="stat-card">
                <div class="stat-value" id="total-calls">-</div>
                <div class="stat-label">Total Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="recent-calls">-</div>
                <div class="stat-label">Recent Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="free-calls">-</div>
                <div class="stat-label">Free Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="amc-calls">-</div>
                <div class="stat-label">AMC Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="paid-calls">-</div>
                <div class="stat-label">Per Calls</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="scheduled-calls">-</div>
                <div class="stat-label">Scheduled Calls</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Error Handling Test</h2>
        <button onclick="testErrorHandling()">Test Error Scenarios</button>
        <div id="error-test-results"></div>
    </div>

    <div class="test-section">
        <h2>Data Validation Test</h2>
        <button onclick="testDataValidation()">Validate Data Structure</button>
        <div id="validation-results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/api/v1';
        
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testAPIConnection() {
            clearResults('api-test-results');
            showResult('api-test-results', '<div class="loading"></div> Testing API connection...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    showResult('api-test-results', '✅ API connection successful', 'success');
                } else {
                    showResult('api-test-results', `❌ API connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('api-test-results', `❌ API connection error: ${error.message}`, 'error');
            }
        }

        async function testServiceStats() {
            clearResults('stats-test-results');
            showResult('stats-test-results', '<div class="loading"></div> Testing service stats API...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/service-calls/stats`);
                
                if (!response.ok) {
                    showResult('stats-test-results', `❌ Stats API failed: ${response.status}`, 'error');
                    return;
                }

                const data = await response.json();
                
                if (data.success) {
                    showResult('stats-test-results', '✅ Service stats API successful', 'success');
                    displayStats(data.data);
                } else {
                    showResult('stats-test-results', `⚠️ API returned success: false - ${data.message}`, 'warning');
                    if (data.data) {
                        showResult('stats-test-results', '📊 Displaying fallback data', 'info');
                        displayStats(data.data);
                    }
                }
            } catch (error) {
                showResult('stats-test-results', `❌ Stats API error: ${error.message}`, 'error');
            }
        }

        function displayStats(statsData) {
            document.getElementById('stats-display').style.display = 'grid';
            
            document.getElementById('total-calls').textContent = statsData.totalCalls || 0;
            document.getElementById('recent-calls').textContent = statsData.recentCalls || 0;
            document.getElementById('free-calls').textContent = statsData.callsByType?.freeCalls || 0;
            document.getElementById('amc-calls').textContent = statsData.callsByType?.amcCalls || 0;
            document.getElementById('paid-calls').textContent = statsData.callsByType?.paidCalls || 0;
            
            // Find scheduled calls from callsByStatus
            const scheduledStatus = statsData.callsByStatus?.find(s => 
                s.status?.toLowerCase() === 'scheduled'
            );
            document.getElementById('scheduled-calls').textContent = scheduledStatus?.count || 0;
        }

        async function testErrorHandling() {
            clearResults('error-test-results');
            showResult('error-test-results', '<div class="loading"></div> Testing error scenarios...', 'info');
            
            // Test invalid endpoint
            try {
                const response = await fetch(`${API_BASE_URL}/service-calls/invalid-endpoint`);
                showResult('error-test-results', `✅ Invalid endpoint handled correctly: ${response.status}`, 'success');
            } catch (error) {
                showResult('error-test-results', `❌ Error handling test failed: ${error.message}`, 'error');
            }
        }

        async function testDataValidation() {
            clearResults('validation-results');
            showResult('validation-results', '<div class="loading"></div> Validating data structure...', 'info');
            
            try {
                const response = await fetch(`${API_BASE_URL}/service-calls/stats`);
                const data = await response.json();
                
                const validations = [
                    { field: 'success', type: 'boolean', value: data.success },
                    { field: 'data.totalCalls', type: 'number', value: data.data?.totalCalls },
                    { field: 'data.callsByType.freeCalls', type: 'number', value: data.data?.callsByType?.freeCalls },
                    { field: 'data.callsByType.amcCalls', type: 'number', value: data.data?.callsByType?.amcCalls },
                    { field: 'data.callsByType.paidCalls', type: 'number', value: data.data?.callsByType?.paidCalls },
                    { field: 'data.callsByStatus', type: 'array', value: data.data?.callsByStatus },
                ];

                let passed = 0;
                validations.forEach(validation => {
                    const actualType = Array.isArray(validation.value) ? 'array' : typeof validation.value;
                    const isValid = actualType === validation.type;
                    
                    showResult('validation-results', 
                        `${isValid ? '✅' : '❌'} ${validation.field}: ${actualType} (expected: ${validation.type})`,
                        isValid ? 'success' : 'error'
                    );
                    
                    if (isValid) passed++;
                });

                showResult('validation-results', 
                    `📊 Validation Summary: ${passed}/${validations.length} passed`,
                    passed === validations.length ? 'success' : 'warning'
                );

            } catch (error) {
                showResult('validation-results', `❌ Validation test failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
