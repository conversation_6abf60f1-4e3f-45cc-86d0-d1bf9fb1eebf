import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  try {
    // Add total_time_minutes column if it doesn't exist
    const tableDescription = await queryInterface.describeTable('service_calls');

    if (!tableDescription.total_time_minutes) {
      await queryInterface.addColumn('service_calls', 'total_time_minutes', {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
        comment: 'Total time spent on the call in minutes',
      });

      console.log('✅ Added total_time_minutes column to service_calls');
    }

    // Add total_time_seconds column if it doesn't exist
    if (!tableDescription.total_time_seconds) {
      await queryInterface.addColumn('service_calls', 'total_time_seconds', {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
        comment: 'Total time spent on the call in seconds (more precise)',
      });

      console.log('✅ Added total_time_seconds column to service_calls');
    }

    // Add index for total_time_minutes for performance
    try {
      await queryInterface.addIndex('service_calls', ['total_time_minutes'], {
        name: 'service_calls_total_time_minutes'
      });
      console.log('✅ Added index for total_time_minutes');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    // Add index for total_time_seconds for performance
    try {
      await queryInterface.addIndex('service_calls', ['total_time_seconds'], {
        name: 'service_calls_total_time_seconds'
      });
      console.log('✅ Added index for total_time_seconds');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

    // Add index for time_tracking_history for JSONB queries
    try {
      await queryInterface.addIndex('service_calls', ['time_tracking_history'], {
        name: 'service_calls_time_tracking_history',
        using: 'gin'
      });
      console.log('✅ Added GIN index for time_tracking_history');
    } catch (error) {
      if (error.original?.code !== '42P07') { // Index already exists
        throw error;
      }
    }

  } catch (error) {
    console.error('❌ Error in time tracking migration:', error);
    throw error;
  }
};

export const down = async (queryInterface) => {
  try {
    // Remove indexes
    try {
      await queryInterface.removeIndex('service_calls', 'service_calls_total_time_minutes');
    } catch (error) {
      // Index might not exist
    }

    try {
      await queryInterface.removeIndex('service_calls', 'service_calls_total_time_seconds');
    } catch (error) {
      // Index might not exist
    }

    try {
      await queryInterface.removeIndex('service_calls', 'service_calls_time_tracking_history');
    } catch (error) {
      // Index might not exist
    }

    // Remove total_time_minutes column
    try {
      const tableDescription = await queryInterface.describeTable('service_calls');
      if (tableDescription.total_time_minutes) {
        await queryInterface.removeColumn('service_calls', 'total_time_minutes');
      }
    } catch (error) {
      // Column might not exist
    }

    // Remove total_time_seconds column
    try {
      const tableDescription = await queryInterface.describeTable('service_calls');
      if (tableDescription.total_time_seconds) {
        await queryInterface.removeColumn('service_calls', 'total_time_seconds');
      }
    } catch (error) {
      // Column might not exist
    }
  } catch (error) {
    console.error('❌ Error in time tracking migration rollback:', error);
    throw error;
  }
};
