import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import useAppStore from '../../store/appStore';
import {
  FaPlus,
  FaEdit,
  FaEye,
  FaTrash,
  FaSearch,
  FaFilter,
  FaDownload,
  FaUpload,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaUsers,
  FaCheckCircle,
  FaClock,
  FaRupeeSign,
  FaTh,
  FaList,
  FaEllipsisV,
  FaCalendar
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';
import { Card, CardBody } from '../../components/ui/Card';
import DateRangeFilter from '../../components/DateRangeFilter';
import StandardFilterLayout, { FilterDropdown } from '../../components/common/StandardFilterLayout';
import { useViewPreference, PAGE_NAMES } from '../../utils/viewPreferences';

const CustomerList = () => {
  const navigate = useNavigate();
  const { updateMasterCounts } = useAppStore();
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false); // Task 9: Separate loading state for search
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({
    totalItems: 0,
    totalPages: 0,
    currentPage: 1,
    itemsPerPage: 10
  });
  const [showMoreFilters, setShowMoreFilters] = useState(false);
  const [moreFilters, setMoreFilters] = useState({
    amcStatus: 'all',
    amcExpiryDate: '',
    tssStatus: 'all',
    tssExpiryDate: '',
    licenseEdition: 'all',
    productType: 'all',
    tdlAddons: 'all',
    tdlExpiryDate: '',
    autoBackup: 'all',
    autoBackupExpiryDate: '',
    cloudUser: 'all',
    cloudUserExpiryDate: '',
    mobileApp: 'all',
    mobileAppExpiryDate: '',
    whatsappGroup: 'all',
    whatsappExpiryDate: ''
  });

  // Master data for dropdowns
  const [licenseEditions, setLicenseEditions] = useState([]);
  const [tallyProducts, setTallyProducts] = useState([]);
  const [customersPerPage] = useState(10);
  const [viewMode, setViewMode] = useViewPreference(PAGE_NAMES.CUSTOMERS, 'table');
  const [currentThemeColor, setCurrentThemeColor] = useState('#2f69b3');
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [dateRange, setDateRange] = useState(null);

  // Customer statistics state
  const [customerStats, setCustomerStats] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    inactiveCustomers: 0,
    totalRevenue: 0,
    loading: true,
    error: null
  });

  // Get current theme color
  useEffect(() => {
    const savedTheme = localStorage.getItem('primaryColor') || '#2f69b3';
    setCurrentThemeColor(savedTheme);
  }, []);

  // Force card view on mobile devices
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) { // sm breakpoint
        setViewMode('card');
      }
    };

    // Check on mount
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch master data and customer stats on component mount
  useEffect(() => {
    fetchMasterData();
    fetchCustomerStats();
  }, []);

  // Fetch customers data from API - Combined useEffect with proper dependencies
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchCustomersData();
    }, searchTerm ? 500 : 0); // Debounce only for search, immediate for other changes

    return () => clearTimeout(timeoutId);
  }, [searchTerm, filterStatus, currentPage, moreFilters, dateRange]); // Added moreFilters and dateRange dependency

  // Toggle dropdown for actions
  const toggleDropdown = (customerId) => {
    setDropdownOpen(prev => ({
      ...prev,
      [customerId]: !prev[customerId]
    }));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen({});
    };
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Handle date range change
  const handleDateRangeChange = (range) => {
    setDateRange(range);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Fetch master data for filters
  const fetchMasterData = async () => {
    try {
      const [licenseEditionsRes, tallyProductsRes] = await Promise.all([
        apiService.get('/master-data/license-editions').catch(() => ({ data: { data: { licenseedition: [] } } })),
        apiService.get('/master-data/tally-products').catch(() => ({ data: { data: { tallyproduct: [] } } }))
      ]);

      setLicenseEditions(licenseEditionsRes.data?.data?.licenseedition || []);
      setTallyProducts(tallyProductsRes.data?.data?.tallyproduct || []);
    } catch (error) {
      console.error('Error fetching master data:', error);
    }
  };

  // Fetch customer statistics from API
  const fetchCustomerStats = async () => {
    try {
      setCustomerStats(prev => ({ ...prev, loading: true, error: null }));

      const response = await apiService.get('/customers/stats');

      if (response.data?.success) {
        const data = response.data.data;
        setCustomerStats({
          totalCustomers: data.totalCustomers || 0,
          activeCustomers: data.activeCustomers || 0,
          inactiveCustomers: data.inactiveCustomers || 0,
          totalRevenue: data.totalRevenue || 0,
          loading: false,
          error: null
        });
        console.log('✅ Customer stats loaded:', data);
        console.log('📊 Stats breakdown:', {
          totalCustomers: data.totalCustomers,
          activeCustomers: data.activeCustomers,
          inactiveCustomers: data.inactiveCustomers,
          totalRevenue: data.totalRevenue
        });
      } else {
        throw new Error('Failed to fetch customer statistics');
      }
    } catch (error) {
      console.error('Error fetching customer stats:', error);
      setCustomerStats(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
    }
  };

  const fetchCustomersData = async () => {
    try {
      // Task 9: Use different loading states for initial load vs search
      if (searchTerm) {
        setSearchLoading(true);
      } else {
        setLoading(true);
      }


      const response = await apiService.get('/customers', {
        params: {
          page: currentPage,
          limit: customersPerPage,
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && {
            customerType: filterStatus === 'active' ? 'customer' : filterStatus
          }),
          // Add new filter parameters
          ...(moreFilters.amcStatus !== 'all' && { amcStatus: moreFilters.amcStatus }),
          ...(moreFilters.amcExpiryDate && { amcExpiryDate: moreFilters.amcExpiryDate }),
          ...(moreFilters.tssStatus !== 'all' && { tssStatus: moreFilters.tssStatus }),
          ...(moreFilters.tssExpiryDate && { tssExpiryDate: moreFilters.tssExpiryDate }),
          ...(moreFilters.licenseEdition !== 'all' && { licenseEdition: moreFilters.licenseEdition }),
          ...(moreFilters.productType !== 'all' && { productType: moreFilters.productType }),
          ...(moreFilters.tdlAddons !== 'all' && { tdlAddons: moreFilters.tdlAddons }),
          ...(moreFilters.tdlExpiryDate && { tdlExpiryDate: moreFilters.tdlExpiryDate }),
          ...(moreFilters.autoBackup !== 'all' && { autoBackup: moreFilters.autoBackup }),
          ...(moreFilters.autoBackupExpiryDate && { autoBackupExpiryDate: moreFilters.autoBackupExpiryDate }),
          ...(moreFilters.cloudUser !== 'all' && { cloudUser: moreFilters.cloudUser }),
          ...(moreFilters.cloudUserExpiryDate && { cloudUserExpiryDate: moreFilters.cloudUserExpiryDate }),
          ...(moreFilters.mobileApp !== 'all' && { mobileApp: moreFilters.mobileApp }),
          ...(moreFilters.mobileAppExpiryDate && { mobileAppExpiryDate: moreFilters.mobileAppExpiryDate }),
          ...(moreFilters.whatsappGroup !== 'all' && { whatsappGroup: moreFilters.whatsappGroup }),
          ...(moreFilters.whatsappExpiryDate && { whatsappExpiryDate: moreFilters.whatsappExpiryDate }),
          // Add date range parameters
          ...(dateRange && {
            startDate: dateRange.startDate.toISOString().split('T')[0],
            endDate: dateRange.endDate.toISOString().split('T')[0]
          }),
        }
      });

      if (response.data?.success) {
        const customersData = response.data.data.customers || [];
        // Transform API data to match frontend expectations
        const transformedCustomers = customersData.map(customer => {
          const customFields = customer.custom_fields || {};
          return {
            id: customer.id,
            name: customer.company_name || customer.display_name || 'Unknown Company',
            contactPerson: customer.contact_person || customFields.md_contact_person || 'N/A',
            email: customer.email || customFields.admin_email || 'N/A',
            phone: customer.phone || customFields.md_phone_no || customFields.office_mobile_no || 'N/A',
            city: customer.city || 'N/A',
            state: customer.state || 'N/A',
            status: customer.customer_type === 'customer' ? 'active' :
                   customer.customer_type === 'inactive' ? 'inactive' : 'active', // Map to active/inactive
            customerType: customer.customer_type || 'prospect', // Keep customer type separate
            isActive: customer.is_active, // Keep is_active for reference
            tallyVersion: 'Prime', // This would come from customer's Tally license info
            tallySerialNumber: customer.tally_serial_number || customer.customer_code || customFields.tally_serial_number || `AUTO-${customer.id.slice(-8).toUpperCase()}`,
            amcStatus: customFields.amc_status || 'NO',
            tssStatus: customFields.tss_status || 'NO',
            lastContact: customer.last_contact_date || customer.updatedAt || 'N/A',
            totalServices: 0, // This would come from service calls count
            pendingAmount: customer.pending_amount || customFields.current_amc_amount || 0,
            customFields // Include custom fields for filtering
          };
        });

        setCustomers(transformedCustomers);

        // Update pagination state from server response
        const serverPagination = response.data?.data?.pagination;
        console.log('🔍 Customer Pagination Debug:', {
          serverPagination,
          customersReceived: transformedCustomers.length,
          apiResponse: response.data
        });

        if (serverPagination) {
          setPagination({
            totalItems: serverPagination.totalItems || 0,
            totalPages: serverPagination.totalPages || 0,
            currentPage: serverPagination.currentPage || 1,
            itemsPerPage: serverPagination.itemsPerPage || 10
          });
        } else {
          // Fallback if no pagination data from server
          console.warn('⚠️ No pagination data from server, using fallback');
          setPagination({
            totalItems: transformedCustomers.length,
            totalPages: Math.ceil(transformedCustomers.length / customersPerPage),
            currentPage: 1,
            itemsPerPage: customersPerPage
          });
        }

        // Update customer count in the store using totalItems from pagination for accurate count
        const totalCustomerCount = serverPagination?.totalItems || transformedCustomers.length;
        console.log('🔍 Customer Count Update:', {
          totalItems: serverPagination?.totalItems,
          arrayLength: transformedCustomers.length,
          finalCount: totalCustomerCount
        });
        updateMasterCounts({ customers: totalCustomerCount });
      } else {
        console.error('Failed to fetch customers data:', response.data?.message);
        setCustomers([]);
        updateMasterCounts({ customers: 0 });
      }
    } catch (error) {
      console.error('Error fetching customers data:', error);
      toast.error('Failed to load customers data');
      setCustomers([]);
      updateMasterCounts({ customers: 0 });
    } finally {
      setLoading(false);
      setSearchLoading(false); // Task 9: Always reset search loading
    }
  };

  // Filter customers based on status and more filters (search is handled by backend)
  // Note: Status filtering is now handled by backend via customerType parameter
  const filteredCustomers = customers.filter(customer => {
    // Status filtering is handled by backend, so we don't need to filter here
    // But keep this for any additional client-side filtering if needed
    const matchesStatus = true; // Backend handles status filtering

    // Apply more filters
    const matchesAMC = moreFilters.amcStatus === 'all' || customer.amcStatus === moreFilters.amcStatus;
    const matchesTSS = moreFilters.tssStatus === 'all' || customer.tssStatus === moreFilters.tssStatus;

    // License Edition filter
    const matchesLicenseEdition = moreFilters.licenseEdition === 'all' ||
      customer.licenseEdition?.id === moreFilters.licenseEdition;

    // Product Type filter
    const matchesProductType = moreFilters.productType === 'all' ||
      customer.product?.id === moreFilters.productType;

    // For boolean filters, we need to check the custom fields
    const customFields = customer.customFields || {};
    const matchesTDL = moreFilters.tdlAddons === 'all' ||
      (moreFilters.tdlAddons === 'true' ? customFields.tdl_addons === true : customFields.tdl_addons !== true);
    const matchesAutoBackup = moreFilters.autoBackup === 'all' ||
      (moreFilters.autoBackup === 'true' ? customFields.auto_backup === true : customFields.auto_backup !== true);
    const matchesCloudUser = moreFilters.cloudUser === 'all' ||
      (moreFilters.cloudUser === 'true' ? customFields.cloud_user === true : customFields.cloud_user !== true);
    const matchesMobileApp = moreFilters.mobileApp === 'all' ||
      (moreFilters.mobileApp === 'true' ? customFields.mobile_app === true : customFields.mobile_app !== true);
    const matchesWhatsApp = moreFilters.whatsappGroup === 'all' ||
      (moreFilters.whatsappGroup === 'true' ? customFields.whatsapp_group === true : customFields.whatsapp_group !== true);

    return matchesStatus && matchesAMC && matchesTSS && matchesLicenseEdition && matchesProductType &&
           matchesTDL && matchesAutoBackup && matchesCloudUser && matchesMobileApp && matchesWhatsApp;
  });

  // Note: Customer stats are now fetched from API instead of calculated locally

  // Server-side pagination - use customers directly (no client-side slicing needed)
  const currentCustomers = filteredCustomers;
  const totalPages = pagination.totalPages;

  const handleDelete = async (customerId) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        const response = await apiService.delete(`/customers/${customerId}`);
        if (response.data?.success) {
          setCustomers(customers.filter(customer => customer.id !== customerId));
          toast.success('Customer deleted successfully');
        } else {
          // Use the improved error message from the API response
          const errorMessage = response.data?.message || 'Failed to delete customer';
          toast.error(errorMessage);
        }
      } catch (error) {
        console.error('Error deleting customer:', error);

        // Import and use the ErrorHandler for better error parsing
        const { ErrorHandler } = await import('../../utils/errorUtils.js');
        ErrorHandler.showError(error, 'Failed to delete customer');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'active': { bg: 'bg-green-100', text: 'text-green-800', icon: '✅' },
      'inactive': { bg: 'bg-gray-100', text: 'text-gray-800', icon: '❌' }
    };

    const config = statusConfig[status] || statusConfig.inactive;
    const displayText = status.charAt(0).toUpperCase() + status.slice(1);

    return (
      <span className={`inline-flex items-center px-3 py-1.5 text-xs font-bold rounded-full shadow-sm ${config.bg} ${config.text} border border-opacity-20`}>
        <span className="mr-1">{config.icon}</span>
        {displayText}
      </span>
    );
  };

  // Card View Component
  const CustomerCard = ({ customer }) => (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-lg hover:border-blue-300 transition-all duration-300 w-72 cursor-pointer group">
      <div className="p-4">
        {/* Header with Company Info */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-sm flex-shrink-0">
              <span className="text-white font-bold text-lg">{customer.name?.charAt(0) || 'C'}</span>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-gray-900 text-sm leading-tight mb-1 truncate">{customer.name}</h3>
              <p className="text-gray-600 text-xs font-medium">{customer.contactPerson}</p>
              <div className="flex items-center mt-1">
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                  {customer.tallySerialNumber || 'No S.NO'}
                </span>
              </div>
            </div>
          </div>
          <div className="relative">
            <button
              className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors opacity-0 group-hover:opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                toggleDropdown(customer.id);
              }}
            >
              <FaEllipsisV className="h-4 w-4" />
            </button>
            {dropdownOpen[customer.id] && (
              <div className="absolute right-0 mt-1 w-36 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
                <div className="py-1">
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 flex items-center transition-colors"
                    onClick={() => navigate(`/customers/${customer.id}`)}
                  >
                    <FaEye className="mr-2 h-3 w-3" />
                    View Details
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 flex items-center transition-colors"
                    onClick={() => navigate(`/customers/${customer.id}/edit`)}
                  >
                    <FaEdit className="mr-2 h-3 w-3" />
                    Edit
                  </button>
                  <hr className="my-1" />
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors"
                    onClick={() => handleDelete(customer.id)}
                  >
                    <FaTrash className="mr-2 h-3 w-3" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm">
            <div className="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <FaPhone className="text-green-600 h-3 w-3" />
            </div>
            <span className="text-gray-700 font-medium truncate">{customer.phone || 'No phone'}</span>
          </div>
          <div className="flex items-center text-sm">
            <div className="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
              <FaMapMarkerAlt className="text-orange-600 h-3 w-3" />
            </div>
            <span className="text-gray-700 truncate">{customer.city}, {customer.state}</span>
          </div>
        </div>

        {/* Status Section */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Services</span>
            {getStatusBadge(customer.status)}
          </div>
          <div className="flex space-x-2">
            <div className={`flex-1 text-center py-2 px-3 rounded-lg border-2 transition-all ${
              customer.amcStatus === 'YES'
                ? 'bg-green-50 border-green-200 text-green-700'
                : 'bg-gray-50 border-gray-200 text-gray-500'
            }`}>
              <div className="text-xs font-bold">AMC</div>
              <div className="text-xs">{customer.amcStatus === 'YES' ? 'Active' : 'Inactive'}</div>
            </div>
            <div className={`flex-1 text-center py-2 px-3 rounded-lg border-2 transition-all ${
              customer.tssStatus === 'YES'
                ? 'bg-blue-50 border-blue-200 text-blue-700'
                : 'bg-gray-50 border-gray-200 text-gray-500'
            }`}>
              <div className="text-xs font-bold">TSS</div>
              <div className="text-xs">{customer.tssStatus === 'YES' ? 'Active' : 'Inactive'}</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <button
            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 flex items-center justify-center shadow-sm hover:shadow-md"
            onClick={() => navigate(`/customers/${customer.id}`)}
          >
            <FaEye className="mr-2 h-3 w-3" />
            View Details
          </button>
          <button
            className="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2.5 px-4 rounded-lg border border-gray-300 hover:border-gray-400 transition-all duration-200 flex items-center justify-center"
            onClick={() => navigate(`/customers/${customer.id}/edit`)}
          >
            <FaEdit className="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>
  );

  const handleExport = async () => {
    try {
      toast.loading('Preparing export...');

      // Build export parameters based on current filters
      const exportParams = {
        format: 'csv',
        ...(searchTerm && { search: searchTerm }),
        ...(filterStatus !== 'all' && {
          customerType: filterStatus === 'active' ? 'customer' : filterStatus
        }),
        // Add current filter parameters
        ...(moreFilters.amcStatus !== 'all' && { amcStatus: moreFilters.amcStatus }),
        ...(moreFilters.amcExpiryDate && { amcExpiryDate: moreFilters.amcExpiryDate }),
        ...(moreFilters.tssStatus !== 'all' && { tssStatus: moreFilters.tssStatus }),
        ...(moreFilters.tssExpiryDate && { tssExpiryDate: moreFilters.tssExpiryDate }),
        ...(moreFilters.licenseEdition !== 'all' && { licenseEdition: moreFilters.licenseEdition }),
        ...(moreFilters.productType !== 'all' && { productType: moreFilters.productType }),
        ...(moreFilters.tdlAddons !== 'all' && { tdlAddons: moreFilters.tdlAddons }),
        ...(moreFilters.tdlExpiryDate && { tdlExpiryDate: moreFilters.tdlExpiryDate }),
        ...(moreFilters.autoBackup !== 'all' && { autoBackup: moreFilters.autoBackup }),
        ...(moreFilters.autoBackupExpiryDate && { autoBackupExpiryDate: moreFilters.autoBackupExpiryDate }),
        ...(moreFilters.cloudUser !== 'all' && { cloudUser: moreFilters.cloudUser }),
        ...(moreFilters.cloudUserExpiryDate && { cloudUserExpiryDate: moreFilters.cloudUserExpiryDate }),
        ...(moreFilters.mobileApp !== 'all' && { mobileApp: moreFilters.mobileApp }),
        ...(moreFilters.mobileAppExpiryDate && { mobileAppExpiryDate: moreFilters.mobileAppExpiryDate }),
        ...(moreFilters.whatsappGroup !== 'all' && { whatsappGroup: moreFilters.whatsappGroup }),
        ...(moreFilters.whatsappExpiryDate && { whatsappExpiryDate: moreFilters.whatsappExpiryDate }),
      };

      // Make API call to export endpoint
      const response = await apiService.get('/customers/export', {
        params: exportParams,
        responseType: 'blob', // Important for file download
      });

      // Create download link
      const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `customers_export_${timestamp}.csv`;
      link.setAttribute('download', filename);

      // Trigger download
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

      toast.success(`Exported customers successfully to ${filename}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export customers. Please try again.');
    }
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customers..."
        subtitle="Please wait while we fetch your customer data"
        variant="page"
      />
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        <div className="w-full">
        {/* Colorful Header */}
        <div className="mb-8">
          <div className="header-gradient rounded-2xl shadow-xl p-6 text-white">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
              <div>
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2 flex items-center">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center mr-4" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                    <FaUsers className="text-xl" style={{ color: 'var(--primary-text, #ffffff)' }} />
                  </div>
                  Customer Management
                </h2>
                <p className="text-sm sm:text-base" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Manage your customer database efficiently</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  className="inline-flex items-center justify-center px-6 py-3 border-2 border-opacity-30 text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                  onClick={handleExport}
                  disabled={filteredCustomers.length === 0}
                >
                  <FaDownload className="mr-2" />
                  Export ({filteredCustomers.length})
                </button>
                <Link
                  to="/customers/import"
                  className="inline-flex items-center justify-center px-6 py-3 border-2 border-opacity-30 text-sm font-medium rounded-xl bg-opacity-10 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm"
                  style={{
                    borderColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.3)',
                    color: 'var(--primary-text, #ffffff)',
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)',
                    '--tw-ring-color': 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.5)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.1)'}
                >
                  <FaUpload className="mr-2" />
                  Import
                </Link>
                <Link
                  to="/customers/add"
                  className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 shadow-lg"
                  style={{
                    backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.95)',
                    color: 'var(--primary-color, #1d5795)',
                    backdropFilter: 'blur(10px)'
                  }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.85)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.95)'}
                >
                  <FaPlus className="mr-2" />
                  Add Customer
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Error message for customer stats */}
        {customerStats.error && (
          <div className="mb-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
            <strong>Stats Warning:</strong> Unable to load customer statistics. {customerStats.error}
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="stats-card-primary rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0">
                    {customerStats.loading ? '...' : customerStats.totalCustomers.toLocaleString()}
                  </h4>
                  <p className="mb-0 text-sm" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}>Total Customers</p>
                </div>
                <div className="rounded-lg p-3" style={{ backgroundColor: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.2)' }}>
                  <FaUsers className="h-6 w-6" style={{ color: 'var(--primary-text, #ffffff)' }} />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +12%
                </div>
                <span className="text-white/70 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">
                    {customerStats.loading ? '...' : customerStats.activeCustomers.toLocaleString()}
                  </h4>
                  <p className="text-gray-600 mb-0 text-sm">Active Customers</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaCheckCircle className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +8%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">
                    {customerStats.loading ? '...' : customerStats.inactiveCustomers.toLocaleString()}
                  </h4>
                  <p className="text-gray-600 mb-0 text-sm">Inactive Customers</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaClock className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-down mr-1"></i>
                  -3%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
            <div className="p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-xl sm:text-2xl font-bold mb-0 text-gray-900">
                    {customerStats.loading ? '₹...' : `₹${customerStats.totalRevenue.toLocaleString()}`}
                  </h4>
                  <p className="text-gray-600 mb-0 text-sm">Total Revenue</p>
                </div>
                <div className="white-stats-icon-bg rounded-lg p-3">
                  <FaRupeeSign className="h-6 w-6 white-stats-icon-text" />
                </div>
              </div>
              <div className="mt-3 flex items-center">
                <div className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                  <i className="bi bi-arrow-up mr-1"></i>
                  +15%
                </div>
                <span className="text-gray-500 text-xs ml-2">vs last month</span>
              </div>
            </div>
          </div>
        </div>

        {/* Standardized Filters */}
        <StandardFilterLayout
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search by name, email, phone..."
          leftActions={
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowMoreFilters(!showMoreFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-all duration-200 ${
                  showMoreFilters
                    ? 'border-blue-600 text-blue-600 bg-blue-50'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <FaFilter className="mr-2 h-3 w-3" />
                More Filters
              </button>
            </div>
          }
          rightActions={
            <div className="flex items-center gap-3">
              <button
                onClick={() => setViewMode(viewMode === 'table' ? 'card' : 'table')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200"
              >
                {viewMode === 'table' ? <FaTh className="h-3 w-3" /> : <FaList className="h-3 w-3" />}
                <span className="ml-2 hidden sm:inline">
                  {viewMode === 'table' ? 'Card View' : 'Table View'}
                </span>
              </button>
            </div>
          }
        >
          {/* Status Filter */}
          <FilterDropdown
            value={filterStatus}
            onChange={setFilterStatus}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' }
            ]}
            placeholder="Filter by Status"
            icon={FaCheckCircle}
          />

          {/* Date Range Filter */}
          <div className="flex items-center gap-1">
            <FaCalendar className="text-gray-400" size={14} />
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              label="Creation Date"
            />
          </div>
        </StandardFilterLayout>

        {/* More Filters Dropdown - Positioned relative to container */}
          {showMoreFilters && (
            <div className="relative mt-4">
              <div className="bg-gray-50 rounded-xl border border-gray-200 p-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {/* License Edition Filter */}
                  <div>
                    <label className="block text-xs font-bold text-gray-700 mb-1">License Edition</label>
                    <select
                      className="w-full px-2 py-1.5 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={moreFilters.licenseEdition}
                      onChange={(e) => setMoreFilters(prev => ({ ...prev, licenseEdition: e.target.value }))}
                    >
                      <option value="all">All</option>
                      {licenseEditions.map(edition => (
                        <option key={edition.id} value={edition.id}>{edition.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* Product Type Filter */}
                  <div>
                    <label className="block text-xs font-bold text-gray-700 mb-1">Product Type</label>
                    <select
                      className="w-full px-2 py-1.5 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={moreFilters.productType}
                      onChange={(e) => setMoreFilters(prev => ({ ...prev, productType: e.target.value }))}
                    >
                      <option value="all">All</option>
                      {tallyProducts.map(product => (
                        <option key={product.id} value={product.id}>{product.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* AMC Status Filter */}
                  <div>
                    <label className="block text-xs font-bold text-gray-700 mb-1">AMC Status</label>
                    <select
                      className="w-full px-2 py-1.5 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={moreFilters.amcStatus}
                      onChange={(e) => setMoreFilters(prev => ({ ...prev, amcStatus: e.target.value }))}
                    >
                      <option value="all">All</option>
                      <option value="YES">Yes</option>
                      <option value="NO">No</option>
                    </select>
                  </div>

                  {/* TSS Status Filter */}
                  <div>
                    <label className="block text-xs font-bold text-gray-700 mb-1">TSS Status</label>
                    <select
                      className="w-full px-2 py-1.5 border border-gray-300 rounded text-xs focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={moreFilters.tssStatus}
                      onChange={(e) => setMoreFilters(prev => ({ ...prev, tssStatus: e.target.value }))}
                    >
                      <option value="all">All</option>
                      <option value="YES">Yes</option>
                      <option value="NO">No</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-between mt-4 pt-3 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setMoreFilters({
                        amcStatus: 'all',
                        amcExpiryDate: '',
                        tssStatus: 'all',
                        tssExpiryDate: '',
                        licenseEdition: 'all',
                        productType: 'all',
                        tdlAddons: 'all',
                        tdlExpiryDate: '',
                        autoBackup: 'all',
                        autoBackupExpiryDate: '',
                        cloudUser: 'all',
                        cloudUserExpiryDate: '',
                        mobileApp: 'all',
                        mobileAppExpiryDate: '',
                        whatsappGroup: 'all',
                        whatsappExpiryDate: ''
                      });
                    }}
                    className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    onClick={() => setShowMoreFilters(false)}
                    className="px-4 py-2 text-sm text-white rounded-lg hover:opacity-90 transition-colors"
                    style={{ backgroundColor: 'var(--primary-color, #1d5795)' }}
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>



        {/* Conditional View Rendering */}
        {/* Table View - Hidden on mobile */}
        {viewMode === 'table' && (
          <div className="hidden sm:block">
            <ResponsiveTable
              columns={[
                {
                  header: 'Customer Name',
                  key: 'name',
                  width: '30%',
                  priority: 'high',
                  render: (customer) => (
                    <div className="min-w-0">
                      <div className="text-sm font-bold text-gray-900 truncate" title={customer.name}>{customer.name}</div>
                      <div className="text-xs text-gray-500 truncate" title={customer.contactPerson}>{customer.contactPerson}</div>
                    </div>
                  )
                },
                {
                  header: 'Tally S.NO',
                  key: 'tallySerialNo',
                  width: '18%',
                  priority: 'high',
                  render: (customer) => (
                    <span className="text-xs font-mono text-gray-900 truncate" title={customer.tallySerialNumber || 'N/A'}>
                      {customer.tallySerialNumber || 'N/A'}
                    </span>
                  )
                },
                {
                  header: 'AMC',
                  key: 'amc',
                  width: '10%',
                  priority: 'high',
                  render: (customer) => (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      customer.amcStatus === 'YES'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                    >
                      {customer.amcStatus === 'YES' ? 'YES' : 'NO'}
                    </span>
                  )
                },
                {
                  header: 'TSS',
                  key: 'tss',
                  width: '10%',
                  priority: 'high',
                  render: (customer) => (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      customer.tssStatus === 'YES'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                    >
                      {customer.tssStatus === 'YES' ? 'YES' : 'NO'}
                    </span>
                  )
                },
                {
                  header: 'Status',
                  key: 'status',
                  width: '12%',
                  priority: 'high',
                  render: (customer) => getStatusBadge(customer.status)
                },
                {
                  header: 'Contact No',
                  key: 'contact',
                  width: '20%',
                  priority: 'high',
                  render: (customer) => (
                    <div className="flex items-center">
                      <FaPhone className="text-gray-400 mr-1 h-3 w-3 flex-shrink-0" />
                      <span className="text-xs text-gray-900 truncate" title={customer.phone || 'N/A'}>
                        {customer.phone || 'N/A'}
                      </span>
                    </div>
                  )
                },
                {
                  header: 'Actions',
                  key: 'actions',
                  width: '7%',
                  priority: 'high',
                  truncate: false,
                  render: (customer) => (
                    <div className="flex justify-center space-x-1">
                      <button
                        className="p-1 rounded hover:bg-gray-50 transition-colors"
                        style={{ color: 'var(--primary-color)' }}
                        onClick={() => navigate(`/customers/${customer.id}`)}
                        title="View Details"
                      >
                        <FaEye className="h-3 w-3" />
                      </button>
                      <button
                        className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                        onClick={() => navigate(`/customers/${customer.id}/edit`)}
                        title="Edit"
                      >
                        <FaEdit className="h-3 w-3" />
                      </button>
                      <button
                        className="text-danger-600 hover:text-danger-900 p-1 rounded hover:bg-danger-50"
                        onClick={() => handleDelete(customer.id)}
                        title="Delete"
                      >
                        <FaTrash className="h-3 w-3" />
                      </button>
                    </div>
                  )
                }
              ]}
              data={currentCustomers}
              loading={loading}
              laptopOptimized={true}
              compactMode={true}
              showTooltips={true}
              emptyMessage="No customers found. Start by adding your first customer."
            />
          </div>
        )}

        {/* Mobile Card View - Always show on mobile */}
        <div className="sm:hidden">
          {currentCustomers.length === 0 ? (
            <div className="bg-white rounded-2xl shadow-xl p-12 text-center border border-gray-100">
              <div className="w-24 h-24 mx-auto mb-6 theme-icon-bg rounded-full flex items-center justify-center">
                <FaUsers className="h-12 w-12 theme-icon-text" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">No Customers Found</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm || filterStatus !== 'all' || dateRange || Object.entries(moreFilters).some(([key, value]) =>
                  key.includes('ExpiryDate') ? value !== '' : value !== 'all'
                )
                  ? 'No customers match your current filters. Try adjusting your search criteria.'
                  : 'Get started by adding your first customer.'}
              </p>
              <Link
                to="/customers/add"
                className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-theme-600 hover:bg-theme-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-500 transition-all duration-200"
              >
                <FaPlus className="mr-2 h-4 w-4" />
                Add Customer
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentCustomers.map((customer) => (
                <CustomerCard key={customer.id} customer={customer} />
              ))}
            </div>
          )}
        </div>

        {/* Desktop Card View - Conditional based on viewMode */}
        {viewMode === 'card' && (
          <div className="hidden sm:block">
            {currentCustomers.length === 0 ? (
              <div className="bg-white rounded-2xl shadow-xl p-12 text-center border border-gray-100">
                <div className="w-24 h-24 mx-auto mb-6 theme-icon-bg rounded-full flex items-center justify-center">
                  <FaUsers className="h-12 w-12 theme-icon-text" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">No Customers Found</h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || filterStatus !== 'all' || dateRange || Object.entries(moreFilters).some(([key, value]) =>
                    key.includes('ExpiryDate') ? value !== '' : value !== 'all'
                  )
                    ? 'No customers match your current filters. Try adjusting your search criteria.'
                    : 'Get started by adding your first customer.'}
                </p>
                <Link
                  to="/customers/add"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-theme-600 hover:bg-theme-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-theme-500 transition-all duration-200"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Add Customer
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 3xl:grid-cols-6 gap-6">
                {currentCustomers.map((customer) => (
                  <CustomerCard key={customer.id} customer={customer} />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Debug Pagination Info - Remove in production */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4 text-sm">
          <strong>Debug Pagination Info:</strong> Total Items: {pagination.totalItems}, Total Pages: {totalPages}, Current Page: {currentPage}, Items Per Page: {pagination.itemsPerPage}
        </div>

        {/* Pagination */}
        {pagination.totalItems > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 px-4 py-3 mt-4">
            <div className="flex flex-col sm:flex-row items-center justify-between space-y-3 sm:space-y-0">
              <div className="text-sm text-gray-700 text-center sm:text-left">
                {totalPages > 1 ? (
                  <>
                    Showing page <span className="font-medium">{currentPage}</span> of{' '}
                    <span className="font-medium">{totalPages}</span>
                    {' '}({pagination.totalItems} total customers)
                  </>
                ) : (
                  <>Showing all {pagination.totalItems} customers</>
                )}
              </div>
              {totalPages > 1 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <i className="bi bi-chevron-left mr-1"></i>
                    Previous
                  </button>

                {/* Page numbers - show limited on mobile */}
                <div className="hidden sm:flex items-center space-x-1">
                  {[...Array(Math.min(totalPages, 5))].map((_, index) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = index + 1;
                    } else if (currentPage <= 3) {
                      pageNum = index + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + index;
                    } else {
                      pageNum = currentPage - 2 + index;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`relative inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md ${
                          currentPage === pageNum
                            ? 'btn-primary border-transparent'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                {/* Mobile page indicator */}
                <div className="sm:hidden px-3 py-2 text-sm text-gray-500">
                  {currentPage} / {totalPages}
                </div>

                  <button
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-blue hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                    <i className="bi bi-chevron-right ml-1"></i>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CustomerList;
