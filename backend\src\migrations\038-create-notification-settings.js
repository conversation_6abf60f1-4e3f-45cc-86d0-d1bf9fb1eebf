import { DataTypes, Sequelize } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
  await queryInterface.createTable('notification_settings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    // Service status notification triggers
    service_created: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Send notification when service is created',
    },
    service_started: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Send notification when service is started/in progress',
    },
    service_completed: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Send notification when service is completed',
    },
    service_cancelled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Send notification when service is cancelled',
    },
    service_on_hold: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Send notification when service is put on hold',
    },
    service_pending: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Send notification when service is pending',
    },
    service_follow_up: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Send notification when service requires follow up',
    },
    service_onsite: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Send notification when service is onsite',
    },
    service_on_process: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Send notification when service is on process',
    },
    // Email settings
    email_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Enable email notifications',
    },
    // Future: SMS, Push notifications, etc.
    sms_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Enable SMS notifications (future feature)',
    },
    push_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Enable push notifications (future feature)',
    },
    // Notification templates
    email_templates: {
      type: DataTypes.JSON,
      defaultValue: {},
      comment: 'Custom email templates for different notification types',
    },
    // Additional settings
    notification_delay_minutes: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Delay in minutes before sending notification',
    },
    business_hours_only: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Send notifications only during business hours',
    },
    business_hours_start: {
      type: DataTypes.TIME,
      defaultValue: '09:00:00',
      comment: 'Business hours start time',
    },
    business_hours_end: {
      type: DataTypes.TIME,
      defaultValue: '18:00:00',
      comment: 'Business hours end time',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add unique constraint for tenant_id
  await queryInterface.addIndex('notification_settings', {
    fields: ['tenant_id'],
    unique: true,
    name: 'notification_settings_tenant_id_unique',
  });

  // Table comment is not supported in this way, skipping
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('notification_settings');
};
