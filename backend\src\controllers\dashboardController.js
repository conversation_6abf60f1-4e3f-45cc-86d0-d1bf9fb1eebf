import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get dashboard overview statistics
 */
export const getDashboardOverview = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d', dateFrom, dateTo } = req.query;

    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);

    // Calculate date range based on filters
    let filterStartDate, filterEndDate;

    if (dateFrom && dateTo) {
      // Use custom date range
      filterStartDate = new Date(dateFrom);
      filterEndDate = new Date(dateTo);
      filterEndDate.setHours(23, 59, 59, 999); // End of day
    } else {
      // Use period-based filtering
      filterEndDate = new Date();
      switch (period) {
        case '7d':
          filterStartDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '3m':
          filterStartDate = new Date();
          filterStartDate.setMonth(filterStartDate.getMonth() - 3);
          break;
        case '1y':
          filterStartDate = new Date();
          filterStartDate.setFullYear(filterStartDate.getFullYear() - 1);
          break;
        case '30d':
        default:
          filterStartDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          break;
      }
    }

    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    logger.info(`Dashboard overview requested for tenant: ${tenantId}, period: ${period}, dateRange: ${filterStartDate?.toISOString()} - ${filterEndDate?.toISOString()}`);

    // Helper function to safely execute queries with fallback
    const safeCount = async (queryFn, fallbackValue = 0, description = '') => {
      try {
        const result = await queryFn();
        logger.debug(`${description}: ${result}`);
        return result;
      } catch (error) {
        logger.warn(`Failed to get ${description}, using fallback:`, error.message);
        return fallbackValue;
      }
    };

    // Get basic counts with error handling
    const [
      totalCustomers,
      activeCustomers,
      totalServiceCalls,
      openServiceCalls,
      overdueServiceCalls,
      totalSales,
      totalExecutives,
      activeExecutives,
    ] = await Promise.all([
      safeCount(() => models.Customer.count({
        where: { tenant_id: tenantId },
      }), 0, 'total customers'),

      safeCount(() => models.Customer.count({
        where: { tenant_id: tenantId, is_active: true },
      }), 0, 'active customers'),

      safeCount(() => models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.between]: [filterStartDate, filterEndDate]
          }
        },
      }), 0, 'total service calls'),

      safeCount(async () => {
        // Try with join first, fallback to simple count
        try {
          return await models.ServiceCall.count({
            where: {
              tenant_id: tenantId,
              created_at: {
                [Op.between]: [filterStartDate, filterEndDate]
              },
              '$status.category$': 'open',
            },
            include: [
              {
                model: models.CallStatus,
                as: 'status',
                attributes: [],
              },
            ],
          });
        } catch (joinError) {
          logger.warn('Failed to count open service calls with join, trying simple count:', joinError.message);
          // Fallback to simple count without join
          return await models.ServiceCall.count({
            where: {
              tenant_id: tenantId,
              created_at: {
                [Op.between]: [filterStartDate, filterEndDate]
              }
            },
          });
        }
      }, 0, 'open service calls'),

      safeCount(async () => {
        // Try with join first, fallback to simple count
        try {
          return await models.ServiceCall.count({
            where: {
              tenant_id: tenantId,
              scheduled_date: {
                [Op.lt]: new Date(), // Scheduled date is in the past
              },
              '$status.category$': 'open', // Still open/pending
            },
            include: [
              {
                model: models.CallStatus,
                as: 'status',
                attributes: [],
              },
            ],
          });
        } catch (joinError) {
          logger.warn('Failed to count overdue service calls with join, trying simple count:', joinError.message);
          // Fallback to simple count of overdue calls
          return await models.ServiceCall.count({
            where: {
              tenant_id: tenantId,
              scheduled_date: {
                [Op.lt]: new Date(),
              },
            },
          });
        }
      }, 0, 'overdue service calls'),

      safeCount(() => models.Sale.count({
        where: { tenant_id: tenantId },
      }), 0, 'total sales'),

      safeCount(() => models.Executive.count({
        where: { tenant_id: tenantId },
      }), 0, 'total executives'),

      safeCount(() => models.Executive.count({
        where: { tenant_id: tenantId, is_active: true },
      }), 0, 'active executives'),
    ]);

    // Get recent activity counts with error handling
    const [
      newCustomersThisMonth,
      serviceCallsThisMonth,
      salesThisMonth,
      serviceCallsLast30Days,
    ] = await Promise.all([
      safeCount(() => models.Customer.count({
        where: {
          tenant_id: tenantId,
          created_at: { [Op.gte]: startOfMonth },
        },
      }), 0, 'new customers this month'),

      safeCount(() => models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: { [Op.gte]: startOfMonth },
        },
      }), 0, 'service calls this month'),

      safeCount(() => models.Sale.count({
        where: {
          tenant_id: tenantId,
          sale_date: { [Op.gte]: startOfMonth },
        },
      }), 0, 'sales this month'),

      safeCount(() => models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: { [Op.gte]: last30Days },
        },
      }), 0, 'service calls last 30 days'),
    ]);

    // Get revenue statistics with error handling
    const [salesRevenue, amcRevenue] = await Promise.all([
      safeCount(async () => {
        const result = await models.Sale.findAll({
          where: {
            tenant_id: tenantId,
            sale_date: { [Op.gte]: startOfYear },
          },
          attributes: [
            [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total'],
            [models.sequelize.fn('SUM', models.sequelize.col('paid_amount')), 'paid'],
          ],
          raw: true,
        });
        return result;
      }, [{ total: 0, paid: 0 }], 'sales revenue'),

      safeCount(async () => {
        try {
          const result = await models.CustomerAMC.findAll({
            where: {
              '$customer.tenant_id$': tenantId,
              start_date: { [Op.gte]: startOfYear },
            },
            include: [
              {
                model: models.Customer,
                as: 'customer',
                attributes: [],
              },
            ],
            attributes: [
              [models.sequelize.fn('SUM', models.sequelize.col('contract_value')), 'total'],
            ],
            raw: true,
          });
          return result;
        } catch (joinError) {
          logger.warn('Failed to get AMC revenue with join, trying simple query:', joinError.message);
          // Fallback to simple AMC query
          const result = await models.CustomerAMC.findAll({
            where: {
              start_date: { [Op.gte]: startOfYear },
            },
            attributes: [
              [models.sequelize.fn('SUM', models.sequelize.col('contract_value')), 'total'],
            ],
            raw: true,
          });
          return result;
        }
      }, [{ total: 0 }], 'AMC revenue'),
    ]);

    // Get service call priority distribution with error handling
    const serviceCallsByPriority = await safeCount(async () => {
      return await models.ServiceCall.findAll({
        where: { tenant_id: tenantId },
        attributes: [
          'priority',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['priority'],
        raw: true,
      });
    }, [], 'service calls by priority');

    // Get customer type distribution with error handling
    const customersByType = await safeCount(async () => {
      return await models.Customer.findAll({
        where: { tenant_id: tenantId },
        attributes: [
          'customer_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['customer_type'],
        raw: true,
      });
    }, [], 'customers by type');

    // Get recent service calls with error handling
    const recentServiceCalls = await safeCount(async () => {
      try {
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            created_at: {
              [Op.between]: [filterStartDate, filterEndDate]
            }
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'customer_code'],
            },
            {
              model: models.CallStatus,
              as: 'status',
              attributes: ['id', 'name', 'color', 'category'],
            },
            {
              model: models.Executive,
              as: 'assignedExecutive',
              attributes: ['id', 'first_name', 'last_name'],
            },
          ],
          order: [['created_at', 'DESC']],
          limit: 10,
        });
      } catch (joinError) {
        logger.warn('Failed to get recent service calls with joins, trying simple query:', joinError.message);
        // Fallback to simple query without joins
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            created_at: {
              [Op.between]: [filterStartDate, filterEndDate]
            }
          },
          order: [['created_at', 'DESC']],
          limit: 10,
        });
      }
    }, [], 'recent service calls');

    // Get upcoming follow-ups with error handling
    const upcomingFollowUps = await safeCount(async () => {
      try {
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            follow_up_required: true,
            follow_up_date: {
              [Op.gte]: today,
              [Op.lte]: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next 7 days
            },
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'customer_code'],
            },
            {
              model: models.Executive,
              as: 'assignedExecutive',
              attributes: ['id', 'first_name', 'last_name'],
            },
          ],
          order: [['follow_up_date', 'ASC']],
          limit: 10,
        });
      } catch (joinError) {
        logger.warn('Failed to get upcoming follow-ups with joins, trying simple query:', joinError.message);
        // Fallback to simple query
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            follow_up_required: true,
            follow_up_date: {
              [Op.gte]: today,
              [Op.lte]: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
            },
          },
          order: [['follow_up_date', 'ASC']],
          limit: 10,
        });
      }
    }, [], 'upcoming follow-ups');

    // Get expiring AMCs with error handling
    const expiringAMCs = await safeCount(async () => {
      try {
        return await models.CustomerAMC.findAll({
          where: {
            '$customer.tenant_id$': tenantId,
            status: 'active',
            end_date: {
              [Op.gte]: today,
              [Op.lte]: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Next 30 days
            },
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'customer_code'],
            },
          ],
          order: [['end_date', 'ASC']],
          limit: 10,
        });
      } catch (joinError) {
        logger.warn('Failed to get expiring AMCs with joins, trying simple query:', joinError.message);
        // Fallback to simple query
        return await models.CustomerAMC.findAll({
          where: {
            status: 'active',
            end_date: {
              [Op.gte]: today,
              [Op.lte]: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            },
          },
          order: [['end_date', 'ASC']],
          limit: 10,
        });
      }
    }, [], 'expiring AMCs');

    // Get overdue service calls for alerts with error handling
    const overdueServiceCallsList = await safeCount(async () => {
      try {
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            scheduled_date: {
              [Op.lt]: today, // Scheduled date is in the past
            },
            '$status.category$': 'open', // Still open/pending
          },
          include: [
            {
              model: models.Customer,
              as: 'customer',
              attributes: ['id', 'company_name', 'name'],
            },
            {
              model: models.CallStatus,
              as: 'status',
              attributes: ['id', 'name', 'category'],
            },
            {
              model: models.Executive,
              as: 'assignedExecutive',
              attributes: ['id', 'first_name', 'last_name', 'name'],
            },
          ],
          order: [['scheduled_date', 'ASC']],
          limit: 10,
        });
      } catch (joinError) {
        logger.warn('Failed to get overdue service calls with joins, trying simple query:', joinError.message);
        // Fallback to simple query
        return await models.ServiceCall.findAll({
          where: {
            tenant_id: tenantId,
            scheduled_date: {
              [Op.lt]: today,
            },
          },
          order: [['scheduled_date', 'ASC']],
          limit: 10,
        });
      }
    }, [], 'overdue service calls');

    // Calculate growth percentages (compared to previous month) with error handling
    const previousMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const endOfPreviousMonth = new Date(today.getFullYear(), today.getMonth(), 0);

    const [
      customersLastMonth,
      serviceCallsLastMonth,
      salesLastMonth,
    ] = await Promise.all([
      safeCount(() => models.Customer.count({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }), 0, 'customers last month'),

      safeCount(() => models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }), 0, 'service calls last month'),

      safeCount(() => models.Sale.count({
        where: {
          tenant_id: tenantId,
          sale_date: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }), 0, 'sales last month'),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    const overview = {
      summary: {
        totalCustomers,
        activeCustomers,
        totalServiceCalls,
        openServiceCalls,
        overdueServiceCalls,
        totalSales,
        totalExecutives,
        activeExecutives,
      },
      thisMonth: {
        newCustomers: newCustomersThisMonth,
        serviceCalls: serviceCallsThisMonth,
        sales: salesThisMonth,
      },
      growth: {
        customers: calculateGrowth(newCustomersThisMonth, customersLastMonth),
        serviceCalls: calculateGrowth(serviceCallsThisMonth, serviceCallsLastMonth),
        sales: calculateGrowth(salesThisMonth, salesLastMonth),
      },
      revenue: {
        salesTotal: parseFloat(salesRevenue[0]?.total || 0),
        salesPaid: parseFloat(salesRevenue[0]?.paid || 0),
        amcTotal: parseFloat(amcRevenue[0]?.total || 0),
      },
      distributions: {
        serviceCallsByPriority: Array.isArray(serviceCallsByPriority) ? serviceCallsByPriority.reduce((acc, item) => {
          if (item && item.priority && item.count) {
            acc[item.priority] = parseInt(item.count);
          }
          return acc;
        }, {}) : {},
        customersByType: Array.isArray(customersByType) ? customersByType.reduce((acc, item) => {
          if (item && item.customer_type && item.count) {
            acc[item.customer_type] = parseInt(item.count);
          }
          return acc;
        }, {}) : {},
      },
      recentActivity: {
        serviceCallsLast30Days,
        recentServiceCalls: Array.isArray(recentServiceCalls) ? recentServiceCalls.map(call => ({
          id: call.id,
          callNumber: call.call_number || 'N/A',
          subject: call.subject || 'No subject',
          priority: call.priority || 'medium',
          customer: call.customer || { id: null, company_name: 'Unknown', customer_code: 'N/A' },
          status: call.status || { id: null, name: 'Unknown', color: '#6c757d', category: 'open' },
          assignedExecutive: call.assignedExecutive || { id: null, first_name: 'Unassigned', last_name: '' },
          createdAt: call.created_at,
        })) : [],
      },
      alerts: {
        upcomingFollowUps: Array.isArray(upcomingFollowUps) ? upcomingFollowUps.map(call => ({
          id: call.id,
          callNumber: call.call_number || 'N/A',
          subject: call.subject || 'No subject',
          followUpDate: call.follow_up_date,
          customer: call.customer || { id: null, company_name: 'Unknown', customer_code: 'N/A' },
          assignedExecutive: call.assignedExecutive || { id: null, first_name: 'Unassigned', last_name: '' },
        })) : [],
        expiringAMCs: Array.isArray(expiringAMCs) ? expiringAMCs.map(amc => ({
          id: amc.id,
          amcNumber: amc.amc_number || 'N/A',
          endDate: amc.end_date,
          contractValue: amc.contract_value || 0,
          customer: amc.customer || { id: null, company_name: 'Unknown', customer_code: 'N/A' },
          daysRemaining: amc.end_date ? Math.ceil((new Date(amc.end_date) - today) / (1000 * 60 * 60 * 24)) : 0,
        })) : [],
        overdueServiceCalls: Array.isArray(overdueServiceCallsList) ? overdueServiceCallsList.map(call => ({
          id: call.id,
          callNumber: call.call_number || 'N/A',
          subject: call.subject || 'No subject',
          scheduledDate: call.scheduled_date,
          customer: call.customer || { id: null, company_name: 'Unknown', name: 'Unknown' },
          assignedExecutive: call.assignedExecutive || { id: null, first_name: 'Unassigned', last_name: '', name: 'Unassigned' },
          status: call.status || { id: null, name: 'Unknown', category: 'open' },
          daysOverdue: call.scheduled_date ? Math.ceil((today - new Date(call.scheduled_date)) / (1000 * 60 * 60 * 24)) : 0,
        })) : [],
      },
    };

    logger.info(`Dashboard overview successfully generated for tenant: ${tenantId}`);

    res.json({
      success: true,
      data: overview,
    });

  } catch (error) {
    logger.error('Get dashboard overview error:', {
      error: error.message,
      stack: error.stack,
      tenantId: req.user?.tenant?.id,
      sql: error.sql || 'No SQL query',
    });

    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview',
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack,
        sql: error.sql
      } : undefined,
    });
  }
};

/**
 * Get dashboard charts data
 */
export const getDashboardCharts = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d' } = req.query;

    let startDate;
    let groupBy;
    let dateFormat;

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
        break;
      case '3m':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        groupBy = 'week';
        dateFormat = 'YYYY-WW';
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        groupBy = 'month';
        dateFormat = 'YYYY-MM';
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
    }

    // Service calls trend
    const serviceCallsTrend = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        call_date: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date')), 'ASC']],
      raw: true,
    });

    // Sales trend
    const salesTrend = await models.Sale.findAll({
      where: {
        tenant_id: tenantId,
        sale_date: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'amount'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date')), 'ASC']],
      raw: true,
    });

    // Customer acquisition trend
    const customersTrend = await models.Customer.findAll({
      where: {
        tenant_id: tenantId,
        created_at: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at')), 'ASC']],
      raw: true,
    });

    res.json({
      success: true,
      data: {
        period,
        serviceCallsTrend: serviceCallsTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
        })),
        salesTrend: salesTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
          amount: parseFloat(item.amount || 0),
        })),
        customersTrend: customersTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
        })),
      },
    });

  } catch (error) {
    logger.error('Get dashboard charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard charts data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
