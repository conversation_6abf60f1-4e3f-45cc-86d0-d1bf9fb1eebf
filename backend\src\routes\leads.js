import express from 'express';
import { body, query, param } from 'express-validator';
import { validateRequest } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import { validateTenantExists } from '../middleware/tenantValidation.js';
import { trackApiUsage } from '../middleware/usageTracking.js';
import {
  getLeads,
  getLeadById,
  createLead,
  updateLead,
  deleteLead,
  getLeadStats,
  getLeadContactHistory,
  addLeadContactHistory,
  convertLeadToCustomer,
} from '../controllers/leadController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(validateTenantExists);
router.use(requireTenantAccess);
router.use(trackApiUsage());

/**
 * @swagger
 * /leads:
 *   get:
 *     summary: Get all leads
 *     description: Retrieve a paginated list of leads with optional filtering and sorting
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of leads per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Search term for customer name, products, contact, etc.
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           maxLength: 50
 *         description: Filter by lead status
 *       - in: query
 *         name: executive
 *         schema:
 *           type: string
 *           maxLength: 100
 *         description: Filter by executive name
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter leads from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter leads to this date
 *       - in: query
 *         name: followUpFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by follow up date from
 *       - in: query
 *         name: followUpTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by follow up date to
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, date, follow_up_date, customer_name]
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC, asc, desc]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Leads retrieved successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
router.get('/', [
  requirePermission('leads.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must be less than 100 characters'),
  query('status')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Status must be less than 50 characters'),
  query('executive')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Executive name must be less than 100 characters'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('followUpFrom')
    .optional()
    .isISO8601()
    .withMessage('Follow up from must be a valid date'),
  query('followUpTo')
    .optional()
    .isISO8601()
    .withMessage('Follow up to must be a valid date'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'date', 'follow_up_date', 'customer_name'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Sort order must be ASC, DESC, asc, or desc'),
  validateRequest,
], getLeads);

/**
 * @swagger
 * /leads/stats:
 *   get:
 *     summary: Get lead statistics
 *     description: Retrieve comprehensive statistics about leads including counts, conversion rates, and breakdowns
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lead statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
router.get('/stats', [
  requirePermission('leads.read'),
  validateRequest,
], getLeadStats);

/**
 * @swagger
 * /leads/{id}:
 *   get:
 *     summary: Get lead by ID
 *     description: Retrieve a specific lead by its ID
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Lead ID
 *     responses:
 *       200:
 *         description: Lead retrieved successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Lead not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', [
  requirePermission('leads.read'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  validateRequest,
], getLeadById);

/**
 * @swagger
 * /leads:
 *   post:
 *     summary: Create new lead
 *     description: Create a new lead with all optional fields
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Lead date
 *               customer_name:
 *                 type: string
 *                 maxLength: 200
 *                 description: Potential customer name
 *               products:
 *                 type: string
 *                 description: Products of interest
 *               amount:
 *                 type: number
 *                 minimum: 0
 *                 description: Potential deal amount
 *               contact_no:
 *                 type: string
 *                 maxLength: 20
 *                 description: Contact phone number
 *               country_code:
 *                 type: string
 *                 maxLength: 10
 *                 default: "+91"
 *                 description: Country code for contact number
 *               status:
 *                 type: string
 *                 maxLength: 50
 *                 description: Lead status
 *               remarks:
 *                 type: string
 *                 description: Additional remarks
 *               executive:
 *                 type: string
 *                 maxLength: 100
 *                 description: Assigned executive
 *               follow_up_date:
 *                 type: string
 *                 format: date
 *                 description: Next follow up date
 *               ref_name:
 *                 type: string
 *                 maxLength: 100
 *                 description: Reference person name
 *               ref_contact_no:
 *                 type: string
 *                 maxLength: 20
 *                 description: Reference contact number
 *               ref_country_code:
 *                 type: string
 *                 maxLength: 10
 *                 default: "+91"
 *                 description: Country code for reference contact
 *               ref_amount:
 *                 type: number
 *                 minimum: 0
 *                 description: Reference amount
 *     responses:
 *       201:
 *         description: Lead created successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
router.post('/', [
  requirePermission('leads.create'),
  body('date')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Date must be a valid date'),
  body('customer_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 200 })
    .withMessage('Customer name must be less than 200 characters'),
  body('products')
    .optional({ nullable: true, checkFalsy: true })
    .trim(),
  body('amount')
    .optional({ nullable: true, checkFalsy: true })
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('contact_no')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 20 })
    .withMessage('Contact number must be less than 20 characters'),
  body('country_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 10 })
    .withMessage('Country code must be less than 10 characters'),
  body('status')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 50 })
    .withMessage('Status must be less than 50 characters'),
  body('remarks')
    .optional({ nullable: true, checkFalsy: true })
    .trim(),
  body('executive')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('Executive name must be less than 100 characters'),
  body('follow_up_date')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Follow up date must be a valid date'),
  body('ref_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('Reference name must be less than 100 characters'),
  body('ref_contact_no')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 20 })
    .withMessage('Reference contact number must be less than 20 characters'),
  body('ref_country_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 10 })
    .withMessage('Reference country code must be less than 10 characters'),
  body('ref_amount')
    .optional({ nullable: true, checkFalsy: true })
    .isFloat({ min: 0 })
    .withMessage('Reference amount must be a positive number'),
  validateRequest,
], createLead);

/**
 * @swagger
 * /leads/{id}:
 *   put:
 *     summary: Update lead
 *     description: Update an existing lead
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Lead ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               customer_name:
 *                 type: string
 *                 maxLength: 200
 *               products:
 *                 type: string
 *               amount:
 *                 type: number
 *                 minimum: 0
 *               contact_no:
 *                 type: string
 *                 maxLength: 20
 *               country_code:
 *                 type: string
 *                 maxLength: 10
 *               status:
 *                 type: string
 *                 maxLength: 50
 *               remarks:
 *                 type: string
 *               executive:
 *                 type: string
 *                 maxLength: 100
 *               follow_up_date:
 *                 type: string
 *                 format: date
 *               ref_name:
 *                 type: string
 *                 maxLength: 100
 *               ref_contact_no:
 *                 type: string
 *                 maxLength: 20
 *               ref_country_code:
 *                 type: string
 *                 maxLength: 10
 *               ref_amount:
 *                 type: number
 *                 minimum: 0
 *     responses:
 *       200:
 *         description: Lead updated successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Lead not found
 *       500:
 *         description: Internal server error
 */
router.put('/:id', [
  requirePermission('leads.update'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  body('date')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Date must be a valid date'),
  body('customer_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 200 })
    .withMessage('Customer name must be less than 200 characters'),
  body('products')
    .optional({ nullable: true, checkFalsy: true })
    .trim(),
  body('amount')
    .optional({ nullable: true, checkFalsy: true })
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('contact_no')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 20 })
    .withMessage('Contact number must be less than 20 characters'),
  body('country_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 10 })
    .withMessage('Country code must be less than 10 characters'),
  body('status')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 50 })
    .withMessage('Status must be less than 50 characters'),
  body('remarks')
    .optional({ nullable: true, checkFalsy: true })
    .trim(),
  body('executive')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('Executive name must be less than 100 characters'),
  body('follow_up_date')
    .optional({ nullable: true, checkFalsy: true })
    .isISO8601()
    .withMessage('Follow up date must be a valid date'),
  body('ref_name')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 100 })
    .withMessage('Reference name must be less than 100 characters'),
  body('ref_contact_no')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 20 })
    .withMessage('Reference contact number must be less than 20 characters'),
  body('ref_country_code')
    .optional({ nullable: true, checkFalsy: true })
    .trim()
    .isLength({ max: 10 })
    .withMessage('Reference country code must be less than 10 characters'),
  body('ref_amount')
    .optional({ nullable: true, checkFalsy: true })
    .isFloat({ min: 0 })
    .withMessage('Reference amount must be a positive number'),
  validateRequest,
], updateLead);

/**
 * @swagger
 * /leads/{id}:
 *   delete:
 *     summary: Delete lead
 *     description: Delete a lead (soft delete)
 *     tags: [Leads]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Lead ID
 *     responses:
 *       200:
 *         description: Lead deleted successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Lead not found
 *       500:
 *         description: Internal server error
 */
router.delete('/:id', [
  requirePermission('leads.delete'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  validateRequest,
], deleteLead);

// Lead Contact History Routes
router.get('/:id/contact-history', [
  requirePermission('leads.read'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  validateRequest,
], getLeadContactHistory);

router.post('/:id/contact-history', [
  requirePermission('leads.update'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  body('contact_type')
    .notEmpty()
    .withMessage('Contact type is required')
    .isIn(['phone', 'email', 'meeting', 'whatsapp', 'other'])
    .withMessage('Invalid contact type'),
  body('contact_date')
    .optional({ checkFalsy: true })
    .isISO8601()
    .withMessage('Contact date must be a valid date'),
  body('duration_minutes')
    .optional({ checkFalsy: true })
    .isInt({ min: 0 })
    .withMessage('Duration must be a positive integer'),
  body('outcome')
    .optional({ checkFalsy: true })
    .isIn(['interested', 'not_interested', 'callback_requested', 'meeting_scheduled', 'converted', 'no_response', 'other'])
    .withMessage('Invalid outcome'),
  body('notes')
    .optional({ checkFalsy: true })
    .trim(),
  body('next_follow_up')
    .optional({ checkFalsy: true })
    .isISO8601()
    .withMessage('Next follow up must be a valid date'),
  body('executive_id')
    .optional({ checkFalsy: true })
    .isUUID()
    .withMessage('Executive ID must be a valid UUID'),
  validateRequest,
], addLeadContactHistory);

// Lead Conversion Route
router.post('/:id/convert', [
  requirePermission('leads.update'),
  param('id')
    .notEmpty()
    .withMessage('Lead ID is required')
    .isUUID()
    .withMessage('Lead ID must be a valid UUID'),
  body('tallySerialNumber')
    .notEmpty()
    .withMessage('Tally Serial Number is required')
    .trim()
    .customSanitizer(value => {
      // Convert to uppercase
      return value ? value.toUpperCase() : value;
    })
    .isLength({ min: 1, max: 50 })
    .withMessage('Tally Serial Number must be between 1 and 50 characters'),
  validateRequest,
], convertLeadToCustomer);

export default router;
