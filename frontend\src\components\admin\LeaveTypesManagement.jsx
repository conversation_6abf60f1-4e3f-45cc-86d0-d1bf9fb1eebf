import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON>, Badge } from '../ui';
import { Plus, Edit, Trash2, Calendar, Clock } from 'lucide-react';
import { leaveAPI } from '../../services/api';
import { toast } from 'react-hot-toast';

const LeaveTypesManagement = () => {
  const [loading, setLoading] = useState(true);
  const [leaveTypes, setLeaveTypes] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingType, setEditingType] = useState(null);

  useEffect(() => {
    fetchLeaveTypes();
  }, []);

  const fetchLeaveTypes = async () => {
    try {
      setLoading(true);
      const response = await leaveAPI.getTypes();
      setLeaveTypes(response.data.leaveTypes);
    } catch (error) {
      console.error('Error fetching leave types:', error);
      toast.error('Failed to fetch leave types');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveType = async (typeData) => {
    try {
      // Mock API call - replace with actual implementation
      console.log('Saving leave type:', typeData);
      toast.success('Leave type saved successfully!');
      setShowForm(false);
      setEditingType(null);
      fetchLeaveTypes();
    } catch (error) {
      console.error('Error saving leave type:', error);
      toast.error('Failed to save leave type');
    }
  };

  const handleDeleteType = async (typeId) => {
    if (!confirm('Are you sure you want to delete this leave type?')) {
      return;
    }

    try {
      // Mock API call - replace with actual implementation
      console.log('Deleting leave type:', typeId);
      toast.success('Leave type deleted successfully!');
      fetchLeaveTypes();
    } catch (error) {
      console.error('Error deleting leave type:', error);
      toast.error('Failed to delete leave type');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leave Types Management</h1>
          <p className="text-gray-600">Configure leave types, quotas, and policies</p>
        </div>
        <Button
          onClick={() => setShowForm(true)}
          className="flex items-center"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Leave Type
        </Button>
      </div>

      {/* Leave Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {leaveTypes.map((type) => (
          <Card key={type.id} className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: type.color_code || '#3B82F6' }}
                ></div>
                <div>
                  <h3 className="font-semibold">{type.name}</h3>
                  <Badge className="bg-gray-100 text-gray-800">{type.code}</Badge>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  onClick={() => {
                    setEditingType(type);
                    setShowForm(true);
                  }}
                  variant="outline"
                  size="sm"
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  onClick={() => handleDeleteType(type.id)}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Annual Quota:</span>
                <span className="font-medium">{type.annual_quota} days</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Paid Leave:</span>
                <Badge className={type.is_paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                  {type.is_paid ? 'Yes' : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Requires Approval:</span>
                <Badge className={type.requires_approval ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}>
                  {type.requires_approval ? 'Yes' : 'Auto-approved'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Advance Notice:</span>
                <span className="font-medium">{type.advance_notice_days} days</span>
              </div>
              
              {type.max_consecutive_days && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Max Consecutive:</span>
                  <span className="font-medium">{type.max_consecutive_days} days</span>
                </div>
              )}
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Half Day Allowed:</span>
                <Badge className={type.allow_half_day ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}>
                  {type.allow_half_day ? 'Yes' : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Carry Forward:</span>
                <Badge className={type.carry_forward_allowed ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}>
                  {type.carry_forward_allowed ? `${type.max_carry_forward} days` : 'No'}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <Badge className={type.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                  {type.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>

            {type.description && (
              <div className="mt-4 pt-4 border-t">
                <p className="text-sm text-gray-600">{type.description}</p>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {leaveTypes.length === 0 && (
        <Card className="p-12 text-center">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No leave types</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first leave type.
          </p>
          <div className="mt-6">
            <Button onClick={() => setShowForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              New Leave Type
            </Button>
          </div>
        </Card>
      )}

      {/* Form Modal */}
      {showForm && (
        <Modal
          isOpen={showForm}
          onClose={() => {
            setShowForm(false);
            setEditingType(null);
          }}
          title={editingType ? 'Edit Leave Type' : 'New Leave Type'}
          size="lg"
        >
          <LeaveTypeForm
            leaveType={editingType}
            onSave={handleSaveType}
            onCancel={() => {
              setShowForm(false);
              setEditingType(null);
            }}
          />
        </Modal>
      )}
    </div>
  );
};

// Leave Type Form Component
const LeaveTypeForm = ({ leaveType, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: leaveType?.name || '',
    code: leaveType?.code || '',
    description: leaveType?.description || '',
    annual_quota: leaveType?.annual_quota || 0,
    is_paid: leaveType?.is_paid !== undefined ? leaveType.is_paid : true,
    requires_approval: leaveType?.requires_approval !== undefined ? leaveType.requires_approval : true,
    advance_notice_days: leaveType?.advance_notice_days || 1,
    max_consecutive_days: leaveType?.max_consecutive_days || '',
    min_days_per_request: leaveType?.min_days_per_request || 0.5,
    max_days_per_request: leaveType?.max_days_per_request || '',
    carry_forward_allowed: leaveType?.carry_forward_allowed || false,
    max_carry_forward: leaveType?.max_carry_forward || 0,
    carry_forward_expiry_months: leaveType?.carry_forward_expiry_months || 12,
    encashment_allowed: leaveType?.encashment_allowed || false,
    max_encashment_days: leaveType?.max_encashment_days || 0,
    applicable_after_months: leaveType?.applicable_after_months || 0,
    gender_specific: leaveType?.gender_specific || 'all',
    requires_document: leaveType?.requires_document || false,
    document_required_after_days: leaveType?.document_required_after_days || '',
    allow_half_day: leaveType?.allow_half_day !== undefined ? leaveType.allow_half_day : true,
    allow_negative_balance: leaveType?.allow_negative_balance || false,
    max_negative_balance: leaveType?.max_negative_balance || 0,
    color_code: leaveType?.color_code || '#3B82F6',
    sort_order: leaveType?.sort_order || 0,
    is_active: leaveType?.is_active !== undefined ? leaveType.is_active : true
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Leave Type Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Code *
          </label>
          <input
            type="text"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            maxLength="5"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
          className="w-full border border-gray-300 rounded-md px-3 py-2"
        />
      </div>

      {/* Quota and Limits */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Annual Quota (days) *
          </label>
          <input
            type="number"
            step="0.5"
            value={formData.annual_quota}
            onChange={(e) => setFormData({ ...formData, annual_quota: parseFloat(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="0"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Advance Notice (days)
          </label>
          <input
            type="number"
            value={formData.advance_notice_days}
            onChange={(e) => setFormData({ ...formData, advance_notice_days: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="0"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Max Consecutive Days
          </label>
          <input
            type="number"
            value={formData.max_consecutive_days}
            onChange={(e) => setFormData({ ...formData, max_consecutive_days: e.target.value ? parseInt(e.target.value) : '' })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="1"
          />
        </div>
      </div>

      {/* Policies */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-gray-900">Leave Policies</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.is_paid}
              onChange={(e) => setFormData({ ...formData, is_paid: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Paid leave</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.requires_approval}
              onChange={(e) => setFormData({ ...formData, requires_approval: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Requires approval</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.allow_half_day}
              onChange={(e) => setFormData({ ...formData, allow_half_day: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Allow half day</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.carry_forward_allowed}
              onChange={(e) => setFormData({ ...formData, carry_forward_allowed: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Allow carry forward</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.encashment_allowed}
              onChange={(e) => setFormData({ ...formData, encashment_allowed: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Allow encashment</span>
          </label>

          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.requires_document}
              onChange={(e) => setFormData({ ...formData, requires_document: e.target.checked })}
              className="mr-2"
            />
            <span className="text-sm text-gray-700">Requires documents</span>
          </label>
        </div>
      </div>

      {/* Carry Forward Settings */}
      {formData.carry_forward_allowed && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Carry Forward Days
            </label>
            <input
              type="number"
              value={formData.max_carry_forward}
              onChange={(e) => setFormData({ ...formData, max_carry_forward: parseInt(e.target.value) })}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Carry Forward Expiry (months)
            </label>
            <input
              type="number"
              value={formData.carry_forward_expiry_months}
              onChange={(e) => setFormData({ ...formData, carry_forward_expiry_months: parseInt(e.target.value) })}
              className="w-full border border-gray-300 rounded-md px-3 py-2"
              min="1"
              max="24"
            />
          </div>
        </div>
      )}

      {/* Additional Settings */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Gender Specific
          </label>
          <select
            value={formData.gender_specific}
            onChange={(e) => setFormData({ ...formData, gender_specific: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="all">All</option>
            <option value="male">Male Only</option>
            <option value="female">Female Only</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Color Code
          </label>
          <input
            type="color"
            value={formData.color_code}
            onChange={(e) => setFormData({ ...formData, color_code: e.target.value })}
            className="w-full border border-gray-300 rounded-md px-3 py-2 h-10"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Sort Order
          </label>
          <input
            type="number"
            value={formData.sort_order}
            onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            min="0"
          />
        </div>
      </div>

      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="mr-2"
          />
          <span className="text-sm font-medium text-gray-700">Active</span>
        </label>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Save Leave Type
        </Button>
      </div>
    </form>
  );
};

export default LeaveTypesManagement;
