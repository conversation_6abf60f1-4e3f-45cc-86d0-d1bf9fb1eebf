import React, { useState, useEffect } from 'react';
import { FaTimes, FaSpinner } from 'react-icons/fa';
import toast from 'react-hot-toast';
import { apiService } from '../../services/api';

const CallStatusForm = ({ item, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    category: 'open',
    color: '#6c757d',
    is_final: false,
    requires_approval: false,
    auto_close_after_days: '',
    is_billable: true,
    is_active: true,
    sort_order: '',
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name || '',
        code: item.code || '',
        description: item.description || '',
        category: item.category || 'open',
        color: item.color || '#6c757d',
        is_final: item.is_final || false,
        requires_approval: item.requires_approval || false,
        auto_close_after_days: item.auto_close_after_days || '',
        is_billable: item.is_billable !== undefined ? item.is_billable : true,
        is_active: item.is_active !== undefined ? item.is_active : true,
        sort_order: item.sort_order || '',
      });
    }
  }, [item]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    let processedValue = type === 'checkbox' ? checked : value;

    // Special handling for code field - convert to uppercase and validate
    if (name === 'code') {
      processedValue = value.toUpperCase();

      // Real-time validation for code field
      if (processedValue && !/^[A-Z0-9_]*$/.test(processedValue)) {
        // Remove invalid characters
        processedValue = processedValue.replace(/[^A-Z0-9_]/g, '');
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: '',
      }));
    }

    // Real-time validation for code field
    if (name === 'code' && processedValue) {
      const codeErrors = {};
      if (processedValue.length < 2) {
        codeErrors.code = 'Code must be at least 2 characters';
      } else if (!/^[A-Z0-9_]+$/.test(processedValue)) {
        codeErrors.code = 'Code must contain only uppercase letters, numbers, and underscores';
      }

      setErrors(prev => ({
        ...prev,
        ...codeErrors
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Code is required';
    } else if (formData.code.trim().length < 2) {
      newErrors.code = 'Code must be at least 2 characters';
    } else if (formData.code.trim().length > 20) {
      newErrors.code = 'Code must be less than 20 characters';
    } else if (!/^[A-Z0-9_]+$/.test(formData.code.trim())) {
      newErrors.code = 'Code must contain only uppercase letters, numbers, and underscores';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.color && !/^#[0-9A-Fa-f]{6}$/.test(formData.color)) {
      newErrors.color = 'Color must be a valid hex color code';
    }

    if (formData.auto_close_after_days && formData.auto_close_after_days.toString().trim() !== '' && (isNaN(formData.auto_close_after_days) || parseInt(formData.auto_close_after_days) < 1)) {
      newErrors.auto_close_after_days = 'Auto close days must be a positive number';
    }

    if (formData.sort_order && (isNaN(formData.sort_order) || parseInt(formData.sort_order) < 0)) {
      newErrors.sort_order = 'Sort order must be a non-negative number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const submitData = {
        ...formData,
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        description: formData.description.trim() || null,
        auto_close_after_days: formData.auto_close_after_days && formData.auto_close_after_days.toString().trim() !== '' ? parseInt(formData.auto_close_after_days) : null,
        sort_order: formData.sort_order && formData.sort_order.toString().trim() !== '' ? parseInt(formData.sort_order) : 0,
      };

      let response;
      if (item) {
        response = await apiService.put(`/master-data/call-statuses/${item.id}`, submitData);
      } else {
        response = await apiService.post('/master-data/call-statuses', submitData);
      }

      if (response.data?.success) {
        toast.success(item ? 'Call status updated successfully' : 'Call status created successfully');
        onSuccess();
      } else {
        throw new Error(response.data?.message || 'Failed to save call status');
      }
    } catch (error) {
      console.error('Error saving call status:', error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to save call status');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      category: 'open',
      color: '#6c757d',
      is_final: false,
      requires_approval: false,
      auto_close_after_days: '',
      is_billable: true,
      is_active: true,
      sort_order: '',
    });
    setErrors({});
    onCancel();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {item ? 'Edit Call Status' : 'Add New Call Status'}
          </h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Enter status name"
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Code *
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.code ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="EXAMPLE_CODE_123"
                maxLength={20}
              />
              {errors.code && <p className="text-red-500 text-xs mt-1">{errors.code}</p>}
              <p className="text-xs text-gray-500 mt-1">Only uppercase letters, numbers, and underscores allowed (2-20 characters)</p>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description <span className="text-gray-500 text-xs">(Optional)</span>
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter status description (optional)"
            />
          </div>

          {/* Category and Color */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.category ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
              >
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
                <option value="cancelled">Cancelled</option>
              </select>
              {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Color <span className="text-gray-500 text-xs">(Optional)</span>
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  name="color"
                  value={formData.color}
                  onChange={handleInputChange}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  name="color"
                  value={formData.color}
                  onChange={handleInputChange}
                  className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.color ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="#6c757d (default gray)"
                />
              </div>
              {errors.color && <p className="text-red-500 text-xs mt-1">{errors.color}</p>}
              <p className="text-xs text-gray-500 mt-1">Choose a color for the status indicator</p>
            </div>
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Auto Close After (Days) <span className="text-gray-500 text-xs">(Optional)</span>
              </label>
              <input
                type="number"
                name="auto_close_after_days"
                value={formData.auto_close_after_days}
                onChange={handleInputChange}
                min="1"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.auto_close_after_days ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="Leave empty for no auto-close"
              />
              {errors.auto_close_after_days && <p className="text-red-500 text-xs mt-1">{errors.auto_close_after_days}</p>}
              <p className="text-xs text-gray-500 mt-1">Leave empty to disable auto-close functionality</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order <span className="text-gray-500 text-xs">(Optional)</span>
              </label>
              <input
                type="number"
                name="sort_order"
                value={formData.sort_order}
                onChange={handleInputChange}
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.sort_order ? 'border-red-300 bg-red-50' : 'border-gray-300'
                }`}
                placeholder="0"
              />
              {errors.sort_order && <p className="text-red-500 text-xs mt-1">{errors.sort_order}</p>}
              <p className="text-xs text-gray-500 mt-1">Leave empty to use default order (0)</p>
            </div>
          </div>

          {/* Checkboxes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_final"
                  checked={formData.is_final}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Final Status</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="requires_approval"
                  checked={formData.requires_approval}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Requires Approval</span>
              </label>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_billable"
                  checked={formData.is_billable}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Billable</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={handleCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading && <FaSpinner className="w-4 h-4 mr-2 animate-spin" />}
              {item ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CallStatusForm;
