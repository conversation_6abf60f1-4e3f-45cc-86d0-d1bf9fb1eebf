# Renewal Notifications User Guide

## Overview

The Renewal Notification System in TallyCRM automatically sends email reminders to customers when their services (AMC, TSS, licenses) are approaching expiry. This guide explains how to configure and manage the system.

## Getting Started

### Accessing Renewal Notifications

1. Log in to your TallyCRM admin panel
2. Navigate to **Settings** > **Renewal Notifications**
3. You'll see the renewal notification dashboard

### Default Configuration

When you first access the system, default settings are automatically created:

- **AMC Renewals**: Reminders at 30, 15, 7, and 2 days before expiry
- **TSS Renewals**: Reminders at 30, 15, 7, and 2 days before expiry
- **Email Channel**: Enabled by default
- **All Settings**: Active by default

## Managing Renewal Settings

### Viewing Current Settings

The settings page displays all configured renewal types with:
- **Service Type**: AMC, TSS, License, etc.
- **Reminder Days**: When notifications are sent
- **Channels**: Email, SMS, WhatsApp (email only currently active)
- **Status**: Active/Inactive
- **Last Modified**: When settings were last updated

### Creating New Settings

To add a new renewal notification setting:

1. Click **"Add New Setting"**
2. Select the **Service Type** from dropdown:
   - AMC Expiry Date
   - AMC Renewal Date
   - TSS Expiry Date
   - TSS Renewal Date
3. Configure **Reminder Days**:
   - Enter days before expiry (e.g., 30, 15, 7, 2)
   - Separate multiple values with commas
   - Must be positive numbers
4. Select **Notification Channels**:
   - Email (currently supported)
   - SMS (coming soon)
   - WhatsApp (coming soon)
5. Set **Status** to Active/Inactive
6. Click **"Save"**

### Editing Existing Settings

To modify renewal settings:

1. Find the setting you want to edit
2. Click the **"Edit"** button
3. Modify the reminder days or channels
4. Click **"Update"**

**Note**: Changes trigger automatic regeneration of notification schedules.

### Deleting Settings

To remove a renewal setting:

1. Click the **"Delete"** button next to the setting
2. Confirm the deletion
3. All scheduled notifications for this setting will be cancelled

## Understanding Email Templates

### Template Types

The system uses different email templates based on urgency:

#### 1. **Standard Reminder** (7+ days before expiry)
- Professional, informative tone
- Includes service details and renewal benefits
- Clear call-to-action for renewal

#### 2. **Urgent Reminder** (2-3 days before expiry)
- High-priority design with red accents
- Emphasizes immediate action required
- Lists consequences of not renewing

#### 3. **Overdue Notice** (after expiry)
- Service restoration focus
- Explains current restrictions
- Immediate renewal benefits

### Template Variables

All templates automatically include:
- Customer company name
- Service type (AMC, TSS, etc.)
- Expiry date
- Days remaining/overdue
- Renewal amount (if available)
- Contact information

### Customizing Templates

Templates can be customized through the **Email Templates** section:

1. Go to **Settings** > **Email Templates**
2. Select the template to modify:
   - `renewal_reminder` (Standard)
   - `renewal_urgent` (Urgent)
   - `renewal_overdue` (Overdue)
3. Edit the subject line and content
4. Use template variables like `{{customer_name}}`, `{{expiry_date}}`
5. Save changes

## Monitoring and Statistics

### Notification Statistics

The dashboard shows key metrics:

- **Pending Notifications**: Scheduled for today
- **Upcoming Renewals**: Services expiring within 7 days
- **Sent Notifications**: Successfully delivered
- **Failed Notifications**: Delivery failures requiring attention

### Notification History

View detailed notification history:

1. Go to **Renewal Notifications** > **History**
2. Filter by:
   - **Status**: Scheduled, Sent, Failed, Cancelled
   - **Service Type**: AMC, TSS, License
   - **Customer**: Specific customer
   - **Date Range**: Custom date range
3. Click on any notification for detailed information

### Understanding Notification Status

- **Scheduled**: Notification is queued for sending
- **Sent**: Successfully delivered to customer
- **Failed**: Delivery failed (will retry automatically)
- **Cancelled**: Notification was cancelled (usually due to setting changes)

## Manual Operations

### Triggering Notifications Manually

To send notifications immediately:

1. Go to **Renewal Notifications** > **Manual Operations**
2. Click **"Trigger Notifications"**
3. Optionally specify a date (defaults to today)
4. Click **"Execute"**

This processes all pending notifications for the specified date.

### Regenerating Schedules

To recreate notification schedules:

1. Go to **Manual Operations**
2. Click **"Regenerate Schedules"**
3. Click **"Execute"**

This rescans all customer renewals and creates new notification schedules based on current settings.

### Checking System Status

Monitor automated jobs:

1. Go to **System Status**
2. View job status:
   - **Daily Notifications**: Runs at 9:00 AM
   - **Weekly Schedule Generation**: Runs Sundays at 6:00 AM
   - **Failed Retry**: Runs every 2 hours
   - **Monthly Cleanup**: Runs 1st of month at 2:00 AM
   - **Health Check**: Runs daily at 12:00 PM

## Best Practices

### Setting Up Reminder Intervals

**Recommended intervals:**
- **High-value services**: 30, 15, 7, 2 days
- **Standard services**: 15, 7, 2 days
- **Low-value services**: 7, 2 days

**Tips:**
- Don't set too many reminders (customers may ignore them)
- Include at least one urgent reminder (2-3 days)
- Consider your sales team's follow-up capacity

### Managing Customer Experience

**Do:**
- Keep reminder content professional and helpful
- Include clear renewal benefits
- Provide easy contact methods
- Test email templates before deployment

**Don't:**
- Send too many reminders
- Use overly aggressive language
- Forget to update contact information
- Ignore failed notification reports

### Monitoring and Maintenance

**Daily:**
- Check failed notifications
- Review urgent renewals requiring immediate attention

**Weekly:**
- Review notification statistics
- Update customer contact information as needed
- Check system status

**Monthly:**
- Analyze renewal conversion rates
- Update email templates if needed
- Review and adjust reminder intervals

## Troubleshooting

### Common Issues

#### Notifications Not Sending
**Possible Causes:**
- Email configuration issues
- Customer email addresses missing/invalid
- Notification settings disabled

**Solutions:**
1. Check email configuration in system settings
2. Verify customer email addresses are valid
3. Ensure notification settings are active
4. Review failed notification logs

#### Duplicate Notifications
**Cause:** Usually due to manual schedule regeneration

**Solution:** The system prevents duplicates automatically, but if you notice duplicates:
1. Check notification history for the customer
2. Contact system administrator if duplicates persist

#### Missing Notifications
**Possible Causes:**
- Renewal dates not set in customer records
- Notification settings don't cover the time period
- Customer records inactive

**Solutions:**
1. Verify renewal/expiry dates are set correctly
2. Check notification settings include appropriate intervals
3. Ensure customer records are active

#### Template Variables Not Working
**Cause:** Incorrect variable syntax or missing data

**Solution:**
1. Use correct syntax: `{{variable_name}}`
2. Ensure customer data includes required fields
3. Test templates with sample data

### Getting Help

If you encounter issues:

1. **Check System Status**: Review job status and error logs
2. **Review Documentation**: Consult this guide and system documentation
3. **Contact Support**: Reach out to your system administrator
4. **Check Logs**: Review notification history for error details

## Advanced Features

### API Integration

For developers, the system provides REST APIs for:
- Managing notification settings
- Triggering manual operations
- Retrieving notification history
- Monitoring system status

### Bulk Operations

For large-scale changes:
- Use manual schedule regeneration after bulk customer updates
- Consider temporarily disabling notifications during data migration
- Monitor system performance during bulk operations

### Reporting and Analytics

Export notification data for analysis:
- Notification success rates
- Customer response patterns
- Renewal conversion tracking
- System performance metrics

## Security and Privacy

### Data Protection
- All customer data is encrypted in transit and at rest
- Notification history is retained according to data retention policies
- Access to notification settings requires appropriate permissions

### Email Security
- All emails are sent through secure SMTP connections
- Email content is sanitized to prevent security issues
- Bounce and delivery tracking maintains customer privacy

## Support and Updates

### System Updates
- Notification system updates are deployed automatically
- New features and improvements are announced in release notes
- Backward compatibility is maintained for existing configurations

### Training and Support
- Additional training materials available in the help section
- Video tutorials for common operations
- Support team available for complex configurations

---

**Need Help?**
Contact your system administrator or support team for assistance with renewal notification configuration and troubleshooting.
