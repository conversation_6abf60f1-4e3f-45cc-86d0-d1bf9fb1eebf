import { NotificationSettings } from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Get email templates for the current tenant
 */
export const getEmailTemplates = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    // Get notification settings for the tenant
    let settings = await NotificationSettings.findOne({
      where: { tenant_id: tenantId }
    });

    // If no settings exist, create default ones
    if (!settings) {
      const defaultTemplates = {
        welcome_email: {
          name: 'Welcome Email',
          subject: 'Welcome to PremInfoTech - {{customer_name}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: linear-gradient(135deg, #2f69b3 0%, #1e4a73 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to PremInfoTech!</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your Trusted Tally Support Partner</p>
              </div>
              <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
                <p style="color: #666; line-height: 1.6; font-size: 16px;">
                  Welcome to PremInfoTech! We're excited to have you as our valued customer.
                </p>
                <p style="color: #666; line-height: 1.6; font-size: 16px;">
                  Our team is committed to providing you with the best Tally support and services. 
                  If you have any questions or need assistance, please don't hesitate to contact us.
                </p>
                <div style="text-align: center; margin: 30px 0;">
                  <p style="color: #2f69b3; font-weight: bold; font-size: 18px;">Thank you for choosing PremInfoTech!</p>
                </div>
              </div>
            </div>
          `,
          enabled: true
        },
        service_created: {
          name: 'Service Created',
          subject: 'Service Request Created - {{service_number}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: #2f69b3; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">Service Request Created</h1>
              </div>
              <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
                <p style="color: #666; line-height: 1.6;">
                  Your service request has been successfully created and assigned to our team.
                </p>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                  <h3 style="color: #333; margin-top: 0;">Service Details:</h3>
                  <p><strong>Service Number:</strong> {{service_number}}</p>
                  <p><strong>Type of Call:</strong> {{type_of_call}}</p>
                  <p><strong>Created Date:</strong> {{created_date}}</p>
                  <p><strong>Assigned Executive:</strong> {{executive_name}}</p>
                </div>
                <p style="color: #666; line-height: 1.6;">
                  We will keep you updated on the progress of your service request.
                </p>
              </div>
            </div>
          `,
          enabled: true
        },
        service_completed: {
          name: 'Service Completed',
          subject: 'Service Completed - {{service_number}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: #28a745; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">✅ Service Completed</h1>
              </div>
              <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
                <p style="color: #666; line-height: 1.6;">
                  Great news! Your service request has been successfully completed.
                </p>
                <div style="background: #d4edda; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;">
                  <h3 style="color: #155724; margin-top: 0;">Service Summary:</h3>
                  <p><strong>Service Number:</strong> {{service_number}}</p>
                  <p><strong>Completed Date:</strong> {{completed_date}}</p>
                  <p><strong>Executive:</strong> {{executive_name}}</p>
                </div>
                <p style="color: #666; line-height: 1.6;">
                  Thank you for choosing PremInfoTech. We hope our service met your expectations.
                </p>
              </div>
            </div>
          `,
          enabled: true
        },
        expiry_notification: {
          name: 'Expiry Notification',
          subject: 'Service Expiry Reminder - {{service_type}}',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: #ffc107; color: #212529; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">⚠️ Service Expiry Reminder</h1>
              </div>
              <div style="background: white; padding: 30px; border-radius: 0 0 8px 8px; border: 1px solid #ddd;">
                <h2 style="color: #333; margin-top: 0;">Dear {{customer_name}},</h2>
                <p style="color: #666; line-height: 1.6;">
                  This is a reminder that your {{service_type}} service is expiring soon.
                </p>
                <div style="background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border: 1px solid #ffeaa7;">
                  <h3 style="color: #856404; margin-top: 0;">Expiry Details:</h3>
                  <p><strong>Service Type:</strong> {{service_type}}</p>
                  <p><strong>Expiry Date:</strong> {{expiry_date}}</p>
                  <p><strong>Days Remaining:</strong> {{days_remaining}}</p>
                </div>
                <p style="color: #666; line-height: 1.6;">
                  Please contact us to renew your service and avoid any interruption.
                </p>
              </div>
            </div>
          `,
          enabled: true
        },
        renewal_reminder: {
          name: 'Renewal Reminder',
          subject: '🔔 {{renewal_type}} Renewal Reminder - {{days_remaining}} Days Left',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
              <div style="background: linear-gradient(135deg, #1d5795 0%, #2c7be5 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 28px; font-weight: 600;">🔔 Renewal Reminder</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your service is expiring soon</p>
              </div>
              <div style="background: white; padding: 40px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h2 style="color: #1d5795; margin-top: 0; font-size: 24px;">Dear {{customer_name}},</h2>
                <p style="color: #6c757d; line-height: 1.8; font-size: 16px; margin-bottom: 30px;">
                  We hope this message finds you well. This is a friendly reminder that your <strong>{{renewal_type}}</strong> service is approaching its renewal date.
                </p>

                <div style="background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); padding: 25px; border-radius: 8px; margin: 30px 0; border-left: 4px solid #ffc107;">
                  <h3 style="color: #856404; margin-top: 0; font-size: 20px; display: flex; align-items: center;">
                    <span style="margin-right: 10px;">📅</span> Renewal Details
                  </h3>
                  <div style="display: grid; gap: 12px;">
                    <p style="margin: 0; color: #856404;"><strong>Service Type:</strong> {{renewal_type}}</p>
                    <p style="margin: 0; color: #856404;"><strong>Expiry Date:</strong> {{expiry_date}}</p>
                    <p style="margin: 0; color: #856404;"><strong>Days Remaining:</strong> <span style="font-size: 18px; font-weight: bold;">{{days_remaining}} days</span></p>
                    {{#if renewal_amount}}<p style="margin: 0; color: #856404;"><strong>Renewal Amount:</strong> ₹{{renewal_amount}}</p>{{/if}}
                  </div>
                </div>

                <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #2196f3;">
                  <h4 style="color: #1976d2; margin-top: 0; font-size: 16px;">💡 Why Renew Early?</h4>
                  <ul style="color: #1976d2; margin: 10px 0; padding-left: 20px;">
                    <li>Avoid service interruption</li>
                    <li>Continue receiving priority support</li>
                    <li>Maintain access to latest updates</li>
                    <li>Ensure business continuity</li>
                  </ul>
                </div>

                <div style="text-align: center; margin: 35px 0;">
                  <a href="mailto:{{contact_email}}" style="background: linear-gradient(135deg, #1d5795 0%, #2c7be5 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: 600; font-size: 16px; display: inline-block; box-shadow: 0 4px 15px rgba(29, 87, 149, 0.3);">
                    📞 Contact Us to Renew
                  </a>
                </div>

                <p style="color: #6c757d; line-height: 1.6; font-size: 14px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                  <strong>Need assistance?</strong> Our support team is here to help you with the renewal process.
                  Please don't hesitate to reach out if you have any questions.
                </p>

                <div style="text-align: center; margin-top: 25px;">
                  <p style="color: #6c757d; font-size: 14px; margin: 0;">
                    Best regards,<br>
                    <strong style="color: #1d5795;">Prem Infotech Support Team</strong>
                  </p>
                </div>
              </div>
            </div>
          `,
          enabled: true
        },
        renewal_urgent: {
          name: 'Urgent Renewal Reminder (2-3 Days)',
          subject: '🚨 URGENT: {{renewal_type}} Expires in {{days_remaining}} Days!',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
              <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 28px; font-weight: 600;">🚨 URGENT RENEWAL NOTICE</h1>
                <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">Action Required Immediately</p>
              </div>
              <div style="background: white; padding: 40px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h2 style="color: #dc3545; margin-top: 0; font-size: 24px;">Dear {{customer_name}},</h2>
                <p style="color: #6c757d; line-height: 1.8; font-size: 16px; margin-bottom: 30px;">
                  <strong style="color: #dc3545;">URGENT:</strong> Your {{renewal_type}} service will expire in just <strong style="color: #dc3545; font-size: 18px;">{{days_remaining}} days</strong>.
                  Immediate action is required to avoid service interruption.
                </p>

                <div style="background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); padding: 25px; border-radius: 8px; margin: 30px 0; border-left: 4px solid #dc3545;">
                  <h3 style="color: #721c24; margin-top: 0; font-size: 20px; display: flex; align-items: center;">
                    <span style="margin-right: 10px;">⏰</span> Critical Timeline
                  </h3>
                  <div style="display: grid; gap: 12px;">
                    <p style="margin: 0; color: #721c24;"><strong>Service:</strong> {{renewal_type}}</p>
                    <p style="margin: 0; color: #721c24;"><strong>Expires On:</strong> {{expiry_date}}</p>
                    <p style="margin: 0; color: #721c24;"><strong>Time Left:</strong> <span style="font-size: 20px; font-weight: bold;">{{days_remaining}} DAYS</span></p>
                  </div>
                </div>

                <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ffc107;">
                  <h4 style="color: #856404; margin-top: 0; font-size: 16px;">⚠️ What Happens if You Don't Renew?</h4>
                  <ul style="color: #856404; margin: 10px 0; padding-left: 20px;">
                    <li><strong>Service will be suspended immediately</strong></li>
                    <li>Loss of technical support access</li>
                    <li>No software updates or patches</li>
                    <li>Potential data security risks</li>
                    <li>Business operations may be affected</li>
                  </ul>
                </div>

                <div style="text-align: center; margin: 35px 0; padding: 20px; background: #e3f2fd; border-radius: 8px;">
                  <h3 style="color: #1976d2; margin-top: 0;">📞 RENEW NOW - Don't Wait!</h3>
                  <a href="mailto:{{contact_email}}" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 18px 35px; text-decoration: none; border-radius: 25px; font-weight: 600; font-size: 18px; display: inline-block; box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4); margin: 10px;">
                    🚨 URGENT RENEWAL
                  </a>
                  <p style="color: #1976d2; margin: 15px 0 0 0; font-size: 14px;">
                    Call us immediately: <strong>+91-XXXX-XXXX</strong>
                  </p>
                </div>

                <p style="color: #6c757d; line-height: 1.6; font-size: 14px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                  This is an automated urgent reminder. Please contact us immediately to avoid service disruption.
                </p>
              </div>
            </div>
          `,
          enabled: true
        },
        renewal_overdue: {
          name: 'Overdue Renewal Notice',
          subject: '❌ {{renewal_type}} Service Expired - Immediate Action Required',
          content: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
              <div style="background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 28px; font-weight: 600;">❌ SERVICE EXPIRED</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Renewal Required for Service Restoration</p>
              </div>
              <div style="background: white; padding: 40px; border-radius: 0 0 12px 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                <h2 style="color: #6f42c1; margin-top: 0; font-size: 24px;">Dear {{customer_name}},</h2>
                <p style="color: #6c757d; line-height: 1.8; font-size: 16px; margin-bottom: 30px;">
                  We regret to inform you that your <strong>{{renewal_type}}</strong> service has expired as of <strong>{{expiry_date}}</strong>.
                  To restore your service and avoid further disruption, please renew immediately.
                </p>

                <div style="background: linear-gradient(135deg, #e2d9f3 0%, #d1c4e9 100%); padding: 25px; border-radius: 8px; margin: 30px 0; border-left: 4px solid #6f42c1;">
                  <h3 style="color: #4a148c; margin-top: 0; font-size: 20px; display: flex; align-items: center;">
                    <span style="margin-right: 10px;">📋</span> Service Status
                  </h3>
                  <div style="display: grid; gap: 12px;">
                    <p style="margin: 0; color: #4a148c;"><strong>Service:</strong> {{renewal_type}}</p>
                    <p style="margin: 0; color: #4a148c;"><strong>Expired On:</strong> {{expiry_date}}</p>
                    <p style="margin: 0; color: #4a148c;"><strong>Status:</strong> <span style="color: #d32f2f; font-weight: bold;">EXPIRED</span></p>
                    <p style="margin: 0; color: #4a148c;"><strong>Days Overdue:</strong> {{days_overdue}}</p>
                  </div>
                </div>

                <div style="background: #ffebee; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #f44336;">
                  <h4 style="color: #c62828; margin-top: 0; font-size: 16px;">🔒 Current Service Restrictions</h4>
                  <ul style="color: #c62828; margin: 10px 0; padding-left: 20px;">
                    <li>Technical support is currently suspended</li>
                    <li>Software updates are not available</li>
                    <li>New feature access is restricted</li>
                    <li>Service level agreements are void</li>
                  </ul>
                </div>

                <div style="text-align: center; margin: 35px 0; padding: 25px; background: #f3e5f5; border-radius: 8px;">
                  <h3 style="color: #6f42c1; margin-top: 0;">🔄 Restore Your Service Now</h3>
                  <a href="mailto:{{contact_email}}" style="background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%); color: white; padding: 18px 35px; text-decoration: none; border-radius: 25px; font-weight: 600; font-size: 18px; display: inline-block; box-shadow: 0 4px 15px rgba(111, 66, 193, 0.4); margin: 10px;">
                    💳 RENEW & RESTORE
                  </a>
                  <p style="color: #6f42c1; margin: 15px 0 0 0; font-size: 14px;">
                    Quick renewal available - Contact us now!
                  </p>
                </div>

                <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #4caf50;">
                  <h4 style="color: #2e7d32; margin-top: 0; font-size: 16px;">✅ Benefits of Immediate Renewal</h4>
                  <ul style="color: #2e7d32; margin: 10px 0; padding-left: 20px;">
                    <li>Instant service restoration</li>
                    <li>Full technical support access</li>
                    <li>Latest software updates</li>
                    <li>Priority customer service</li>
                    <li>Continued business protection</li>
                  </ul>
                </div>

                <p style="color: #6c757d; line-height: 1.6; font-size: 14px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                  <strong>Important:</strong> Extended delays in renewal may result in additional restoration fees.
                  Please contact us immediately to discuss your renewal options.
                </p>
              </div>
            </div>
          `,
          enabled: true
        }
      };

      settings = await NotificationSettings.create({
        tenant_id: tenantId,
        email_templates: defaultTemplates
      });
    }

    res.json({
      success: true,
      data: settings.email_templates || {}
    });
  } catch (error) {
    logger.error('Error fetching email templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email templates',
      error: error.message
    });
  }
};

/**
 * Update a specific email template
 */
export const updateEmailTemplate = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { templateKey } = req.params;
    const templateData = req.body;

    // Validate template key
    const validTemplateKeys = [
      'welcome_email',
      'service_created',
      'service_completed',
      'expiry_notification',
      'renewal_reminder',
      'renewal_urgent',
      'renewal_overdue',
      'attendance_reminder',
      'late_arrival_alert',
      'absence_alert',
      'leave_request_notification',
      'leave_approved',
      'leave_rejected'
    ];
    if (!validTemplateKeys.includes(templateKey)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid template key'
      });
    }

    // Validate template data
    if (!templateData.name || !templateData.subject || !templateData.content) {
      return res.status(400).json({
        success: false,
        message: 'Template name, subject, and content are required'
      });
    }

    // Get or create notification settings
    let settings = await NotificationSettings.findOne({
      where: { tenant_id: tenantId }
    });

    if (!settings) {
      settings = await NotificationSettings.create({
        tenant_id: tenantId,
        email_templates: {}
      });
    }

    // Update the specific template
    const currentTemplates = settings.email_templates || {};
    currentTemplates[templateKey] = {
      name: templateData.name,
      subject: templateData.subject,
      content: templateData.content,
      enabled: templateData.enabled !== undefined ? templateData.enabled : true
    };

    // Save updated templates
    await settings.update({
      email_templates: currentTemplates
    });

    logger.info(`Email template updated: ${templateKey} for tenant: ${tenantId}`);

    res.json({
      success: true,
      message: 'Email template updated successfully',
      data: currentTemplates[templateKey]
    });
  } catch (error) {
    logger.error('Error updating email template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update email template',
      error: error.message
    });
  }
};

/**
 * Get a specific email template
 */
export const getEmailTemplate = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { templateKey } = req.params;

    const settings = await NotificationSettings.findOne({
      where: { tenant_id: tenantId }
    });

    if (!settings || !settings.email_templates || !settings.email_templates[templateKey]) {
      return res.status(404).json({
        success: false,
        message: 'Email template not found'
      });
    }

    res.json({
      success: true,
      data: settings.email_templates[templateKey]
    });
  } catch (error) {
    logger.error('Error fetching email template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email template',
      error: error.message
    });
  }
};

/**
 * Check if a specific email template is enabled
 */
export const isTemplateEnabled = async (tenantId, templateKey) => {
  try {
    const settings = await NotificationSettings.findOne({
      where: { tenant_id: tenantId }
    });

    if (!settings || !settings.email_templates || !settings.email_templates[templateKey]) {
      return true; // Default to enabled if template not found
    }

    return settings.email_templates[templateKey].enabled !== false;
  } catch (error) {
    logger.error('Error checking template status:', error);
    return true; // Default to enabled on error
  }
};

/**
 * Get email template content for notifications
 */
export const getTemplateContent = async (tenantId, templateKey) => {
  try {
    const settings = await NotificationSettings.findOne({
      where: { tenant_id: tenantId }
    });

    if (!settings || !settings.email_templates || !settings.email_templates[templateKey]) {
      return null;
    }

    const template = settings.email_templates[templateKey];
    if (template.enabled === false) {
      return null; // Template is disabled
    }

    return {
      subject: template.subject,
      content: template.content
    };
  } catch (error) {
    logger.error('Error getting template content:', error);
    return null;
  }
};
