import React, { useState, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../../services/api';
import {
  FaUpload,
  FaDownload,
  FaFileExcel,
  FaCheck,
  FaTimes,
  FaExclamationTriangle,
  FaArrowLeft,
  FaEye,
  FaSpinner,
  FaTable,
  FaCheckCircle
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerImport = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);
  
  const [step, setStep] = useState(1); // 1: Upload, 2: Preview, 3: Import, 4: Results
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [importResults, setImportResults] = useState(null);
  const [validationErrors, setValidationErrors] = useState([]);
  const [duplicateRows, setDuplicateRows] = useState([]);
  const [selectedDuplicates, setSelectedDuplicates] = useState({});
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);

  // Corrected template data structure matching actual customer model
  const templateColumns = [
    // Core Mandatory Fields (Database Level)
    'Company Name*',
    'Customer Code*',
    'Email*',
    'Phone*',
    'Tally Serial Number*',

    // Optional Basic Fields
    'Display Name',
    'Customer Type',
    'Business Type',

    // Address Information (Optional)
    'Address Line 1',
    'City',
    'State',
    'Country',
    'PIN Code',

    // Business Details (Optional)
    'GST Number',
    'PAN Number',

    // Contact Information (Optional - stored in custom_fields)
    'Admin Email',
    'MD Contact Person',
    'MD Phone Number',
    'MD Email',
    'Office Contact Person',
    'Office Mobile Number',
    'Office Email',

    // Professional Contacts (Optional - stored in custom_fields)
    'Auditor Name',
    'Auditor Number',
    'Auditor Email',
    'Tax Consultant Name',
    'Tax Consultant Number',
    'Tax Consultant Email',

    // IT Contact (Optional - stored in custom_fields)
    'IT Name',
    'IT Number',
    'IT Email',

    // Business Information (Optional - stored in custom_fields)
    'Area',
    'Number of Tally Users',
    'Executive Name',
    'Status',
    'Profile Status',
    'Customer Status'
  ];

  const handleFileSelect = (event) => {
    const selectedFile = event.target.files[0];
    console.log('=== FILE SELECTION DEBUG ===');
    console.log('File selected:', selectedFile?.name);
    console.log('File object:', selectedFile);
    console.log('Current file state before:', file);

    if (selectedFile) {
      console.log('File details:', {
        name: selectedFile.name,
        size: selectedFile.size,
        type: selectedFile.type
      });

      // Validate file type - be more permissive with CSV files
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv', // .csv
        'application/csv', // .csv (alternative)
        'text/comma-separated-values', // .csv (alternative)
        'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm
        '' // Empty MIME type (some browsers don't set MIME type for CSV)
      ];

      // Also check file extension as fallback
      const fileName = selectedFile.name.toLowerCase();
      const hasValidExtension = fileName.endsWith('.csv') || fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.xlsm');

      if (!allowedTypes.includes(selectedFile.type) && !hasValidExtension) {
        console.error('Invalid file type:', selectedFile.type, 'for file:', fileName);
        toast.error('Please select a valid Excel (.xlsx, .xls) or CSV file');
        return;
      }

      // Validate file size (max 10MB)
      if (selectedFile.size > 10 * 1024 * 1024) {
        console.error('File too large:', selectedFile.size);
        toast.error('File size must be less than 10MB');
        return;
      }

      console.log('Setting file state...');
      setFile(selectedFile);
      console.log('File state set, should trigger re-render');
      toast.success('File selected successfully');
      console.log('=== FILE SELECTION DEBUG END ===');
    } else {
      console.log('No file selected');
      console.log('=== FILE SELECTION DEBUG END ===');
    }
  };

  const downloadTemplate = async () => {
    try {
      setLoading(true);
      
      // Create sample data matching the corrected customer structure
      const sampleData = [
        {
          // Core Mandatory Fields
          'Company Name*': 'ABC Technologies Pvt Ltd',
          'Customer Code*': 'ABC001',
          'Email*': '<EMAIL>',
          'Phone*': '+91-9876543210',
          'Tally Serial Number*': 'TSN001',

          // Optional Basic Fields
          'Display Name': 'ABC Tech',
          'Customer Type': 'customer',
          'Business Type': 'private_limited',

          // Address Information
          'Address Line 1': '123 Business Park, Sector 5',
          'City': 'Mumbai',
          'State': 'Maharashtra',
          'Country': 'India',
          'PIN Code': '400001',

          // Business Details
          'GST Number': '27**********1Z5',
          'PAN Number': '**********',

          // Contact Information (stored in custom_fields)
          'Admin Email': '<EMAIL>',
          'MD Contact Person': 'John Doe',
          'MD Phone Number': '+91-9876543211',
          'MD Email': '<EMAIL>',
          'Office Contact Person': 'Jane Smith',
          'Office Mobile Number': '+91-9876543212',
          'Office Email': '<EMAIL>',

          // Professional Contacts (stored in custom_fields)
          'Auditor Name': 'CA Rajesh Kumar',
          'Auditor Number': '+91-9876543213',
          'Auditor Email': '<EMAIL>',
          'Tax Consultant Name': 'Priya Sharma',
          'Tax Consultant Number': '+91-9876543214',
          'Tax Consultant Email': '<EMAIL>',

          // IT Contact (stored in custom_fields)
          'IT Name': 'Amit Patel',
          'IT Number': '+91-9876543215',
          'IT Email': '<EMAIL>',

          // Business Information (stored in custom_fields)
          'Area': 'Mumbai Central',
          'Number of Tally Users': '5',
          'Executive Name': 'Sales Executive 1',
          'Status': 'ACTIVE',
          'Profile Status': 'FOLLOW UP',
          'Customer Status': 'ACTIVE'
        },
        {
          // Core Mandatory Fields
          'Company Name*': 'XYZ Enterprises',
          'Customer Code*': 'XYZ001',
          'Email*': '<EMAIL>',
          'Phone*': '+91-9876543220',
          'Tally Serial Number*': 'TSN002',

          // Optional Basic Fields
          'Display Name': 'XYZ Ent',
          'Customer Type': 'customer',
          'Business Type': 'partnership',

          // Address Information
          'Address Line 1': '456 Industrial Area',
          'City': 'Delhi',
          'State': 'Delhi',
          'Country': 'India',
          'PIN Code': '110001',

          // Business Details
          'GST Number': '07**********1H2',
          'PAN Number': '**********',

          // Contact Information (stored in custom_fields)
          'Admin Email': '<EMAIL>',
          'MD Contact Person': 'Suresh Gupta',
          'MD Phone Number': '+91-9876543221',
          'MD Email': '<EMAIL>',
          'Office Contact Person': 'Meera Singh',
          'Office Mobile Number': '+91-9876543222',
          'Office Email': '<EMAIL>',

          // Professional Contacts (stored in custom_fields)
          'Auditor Name': 'CA Deepak Jain',
          'Auditor Number': '+91-9876543223',
          'Auditor Email': '<EMAIL>',
          'Tax Consultant Name': 'Ravi Agarwal',
          'Tax Consultant Number': '+91-9876543224',
          'Tax Consultant Email': '<EMAIL>',

          // IT Contact (stored in custom_fields) - empty example
          'IT Name': '',
          'IT Number': '',
          'IT Email': '',

          // Business Information (stored in custom_fields)
          'Area': 'Delhi North',
          'Number of Tally Users': '3',
          'Executive Name': 'Sales Executive 2',
          'Status': 'ACTIVE',
          'Profile Status': 'FOLLOW UP',
          'Customer Status': 'ACTIVE'
        }
      ];

      // Convert to CSV
      const headers = Object.keys(sampleData[0]);
      const csvContent = [
        headers.join(','),
        ...sampleData.map(row => 
          headers.map(header => 
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'customer_import_template.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success('Template downloaded successfully');
    } catch (error) {
      console.error('Download template error:', error);
      toast.error('Failed to download template');
    } finally {
      setLoading(false);
    }
  };

  const previewFile = async () => {
    if (!file) {
      toast.error('Please select a file first');
      return;
    }

    // Check authentication
    const token = localStorage.getItem('tallycrm_token');
    if (!token) {
      toast.error('Please login first');
      return;
    }

    try {
      setLoading(true);

      const formData = new FormData();
      formData.append('file', file);

      const response = await apiService.post('/customers/import/preview', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setPreviewData(response.data.data);
        setValidationErrors(response.data.data.errors || []);
        setDuplicateRows(response.data.data.duplicates || []);

        // Initialize selected duplicates state
        const initialSelected = {};
        (response.data.data.duplicates || []).forEach((duplicate, index) => {
          initialSelected[index] = false; // Default to not selected
        });
        setSelectedDuplicates(initialSelected);

        setStep(2);
        const duplicateCount = response.data.data.duplicates?.length || 0;
        toast.success(`Preview loaded: ${response.data.data.validRows} valid rows, ${response.data.data.errorRows} errors, ${duplicateCount} duplicates`);
      } else {
        console.error('API returned error:', response.data);
        toast.error(response.data.message || 'Failed to preview file');
      }
    } catch (error) {
      console.error('Preview file error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to preview file';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicateSelection = (index, checked) => {
    setSelectedDuplicates(prev => ({
      ...prev,
      [index]: checked
    }));
  };

  const handleSelectAllDuplicates = (checked) => {
    const newSelected = {};
    duplicateRows.forEach((_, index) => {
      newSelected[index] = checked;
    });
    setSelectedDuplicates(newSelected);
  };

  const importData = async (forceImportSelected = false) => {
    if (!previewData) {
      toast.error('No preview data available');
      return;
    }

    // Check error handling preference
    const errorHandling = document.querySelector('input[name="errorHandling"]:checked')?.value || 'skip';
    const forceImportErrors = errorHandling === 'force';

    // Check if there are duplicates and user hasn't made a decision
    if (duplicateRows.length > 0 && !forceImportSelected) {
      setShowDuplicateModal(true);
      return;
    }

    // Show confirmation if user wants to force import errors
    if (forceImportErrors && validationErrors.length > 0) {
      const confirmed = window.confirm(
        `⚠️ You are about to import ${validationErrors.length} rows with validation errors.\n\n` +
        `This may cause data quality issues. Are you sure you want to proceed?`
      );
      if (!confirmed) {
        return;
      }
    }

    try {
      setLoading(true);
      setStep(3);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('skipErrors', forceImportErrors ? 'false' : 'true');
      formData.append('forceImportErrors', forceImportErrors ? 'true' : 'false');

      // Add duplicate handling options
      if (forceImportSelected) {
        const selectedIndexes = Object.keys(selectedDuplicates)
          .filter(key => selectedDuplicates[key])
          .map(key => parseInt(key));
        formData.append('forceImportDuplicates', JSON.stringify(selectedIndexes));
      }

      const response = await apiService.post('/customers/import/execute', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.data.success) {
        setImportResults(response.data.data);
        setStep(4);

        // Show meaningful success message
        const successCount = response.data.data.successCount;
        const errorCount = response.data.data.errorCount;

        if (successCount > 0) {
          if (errorCount > 0) {
            toast.success(`Import completed! ${successCount} customers imported successfully, ${errorCount} failed.`);
          } else {
            toast.success(`🎉 All ${successCount} customers imported successfully!`);
          }
        } else {
          toast.error(`Import failed! No customers were imported. Please check your data and try again.`);
        }
      } else {
        toast.error(response.data.message || 'Import failed');
        setStep(2); // Go back to preview
      }
    } catch (error) {
      console.error('Import data error:', error);
      toast.error(error.response?.data?.message || 'Import failed');
      setStep(2); // Go back to preview
    } finally {
      setLoading(false);
      setShowDuplicateModal(false);
    }
  };

  const resetImport = () => {
    setStep(1);
    setFile(null);
    setPreviewData(null);
    setImportResults(null);
    setValidationErrors([]);
    setDuplicateRows([]);
    setSelectedDuplicates({});
    setShowDuplicateModal(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  if (loading && step === 3) {
    return (
      <LoadingScreen
        title="Importing Customers..."
        subtitle="Please wait while we process your data"
        variant="page"
      />
    );
  }



  // Debug current state
  console.log('=== RENDER DEBUG ===');
  console.log('Current step:', step);
  console.log('Current file:', file);
  console.log('File name:', file?.name);
  console.log('Loading state:', loading);
  console.log('=== RENDER DEBUG END ===');

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center min-w-0 flex-1">
          <button
            onClick={() => navigate('/customers')}
            className="mr-3 sm:mr-4 p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors flex-shrink-0"
          >
            <FaArrowLeft size={20} />
          </button>
          <div className="min-w-0 flex-1">
            <h2 className="mb-0 text-lg sm:text-xl lg:text-2xl">Import Customers</h2>
            <p className="text-gray-600 text-sm sm:text-base hidden sm:block">Upload Excel or CSV file to import customer data</p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        {/* Desktop Progress Steps */}
        <div className="hidden md:flex items-center justify-center">
          {[
            { step: 1, title: 'Upload File', icon: FaUpload },
            { step: 2, title: 'Preview Data', icon: FaEye },
            { step: 3, title: 'Import', icon: FaSpinner },
            { step: 4, title: 'Results', icon: FaCheck }
          ].map((item, index) => (
            <div key={item.step} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                step >= item.step
                  ? 'bg-primary-600 border-primary-600 text-white'
                  : 'border-gray-300 text-gray-400'
              }`}>
                <item.icon size={16} />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step >= item.step ? 'text-primary-600' : 'text-gray-400'
              }`}>
                {item.title}
              </span>
              {index < 3 && (
                <div className={`mx-4 h-0.5 w-16 ${
                  step > item.step ? 'bg-primary-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Mobile Progress Steps */}
        <div className="md:hidden">
          <div className="flex items-center justify-center mb-2">
            <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
              step >= 1 ? 'bg-primary-600 border-primary-600 text-white' : 'border-gray-300 text-gray-400'
            }`}>
              {step === 1 && <FaUpload size={18} />}
              {step === 2 && <FaEye size={18} />}
              {step === 3 && <FaSpinner size={18} />}
              {step === 4 && <FaCheck size={18} />}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm font-medium text-primary-600">
              Step {step} of 4: {
                step === 1 ? 'Upload File' :
                step === 2 ? 'Preview Data' :
                step === 3 ? 'Import' : 'Results'
              }
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(step / 4) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Step 1: Upload File */}
      {step === 1 && (
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Upload Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Customer Data</h3>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors">
                <FaFileExcel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-900">
                    {file ? file.name : 'Choose a file to upload'}
                  </p>
                  <p className="text-sm text-gray-500">
                    Excel (.xlsx, .xls) or CSV files up to 10MB
                  </p>
                </div>

                <div className="mt-6 space-y-3">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="inline-flex items-center px-4 py-2 border border-primary-600 text-primary-600 bg-white hover:bg-primary-50 rounded-md font-medium transition-colors"
                  >
                    <FaUpload className="mr-2" />
                    Select File
                  </button>

                  {file && (
                    <div className="mt-4">
                      <div className="text-sm text-green-600 mb-2 p-2 bg-green-50 rounded">
                        ✅ File selected: {file.name} ({(file.size / 1024).toFixed(2)} KB)
                      </div>
                      <button
                        onClick={previewFile}
                        disabled={loading}
                        className="block w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 font-medium transition-colors shadow-md"
                        style={{ backgroundColor: '#15579e' }}
                      >
                        {loading ? 'Processing...' : 'Preview Data'}
                      </button>
                    </div>
                  )}

                  {!file && (
                    <div className="text-sm text-gray-500 mt-2">
                      No file selected yet. Please choose a file to continue.
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Template Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Download Template</h3>

              <div className="space-y-4">
                <p className="text-gray-600">
                  Download our Excel template to ensure your data is formatted correctly for import.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Required Fields (marked with *):</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Company Name*</li>
                      <li>• Customer Code*</li>
                      <li>• Tally Serial Number*</li>
                      <li>• Admin Email*</li>
                      <li>• MD Contact Person*</li>
                      <li>• MD Phone Number*</li>
                      <li>• MD Email*</li>
                      <li>• Office Contact Person*</li>
                    </ul>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Office Mobile Number*</li>
                      <li>• Office Email*</li>
                      <li>• Auditor Name*</li>
                      <li>• Auditor Number*</li>
                      <li>• Auditor Email*</li>
                      <li>• Tax Consultant Name*</li>
                      <li>• Tax Consultant Number*</li>
                      <li>• Tax Consultant Email*</li>
                    </ul>
                  </div>
                  <div className="mt-2">
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Area*, Number of Tally Users*, Executive Name*, Status*</li>
                    </ul>
                  </div>
                </div>

                <button
                  onClick={downloadTemplate}
                  disabled={loading}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 font-medium transition-colors flex items-center justify-center"
                >
                  <FaDownload className="mr-2" />
                  {loading ? 'Generating...' : 'Download Template'}
                </button>
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h4 className="font-medium text-yellow-900 mb-3">Import Instructions:</h4>
            <ul className="text-sm text-yellow-800 space-y-2">
              <li>1. Download the template and fill in your customer data</li>
              <li>2. Ensure required fields (marked with *) are filled</li>
              <li>3. Use the exact column headers from the template</li>
              <li>4. Save your file as Excel (.xlsx) or CSV format</li>
              <li>5. Upload the file and preview before importing</li>
            </ul>
          </div>
        </div>
      )}

      {/* Step 2: Preview Data */}
      {step === 2 && previewData && (
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Import Preview</h3>
                <p className="text-gray-600">Review your data before importing</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 sm:space-x-0">
                <button
                  onClick={() => setStep(1)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={resetImport}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Start Over
                </button>
                <button
                  onClick={() => setStep(3)}
                  disabled={previewData.validRows === 0}
                  className="px-6 py-2 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium"
                  style={{ backgroundColor: '#15579e' }}
                >
                  Next: Confirm Import
                </button>
              </div>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-blue-600">{previewData.totalRows}</div>
                <div className="text-xs sm:text-sm text-blue-800">Total Rows</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-green-600">{previewData.validRows}</div>
                <div className="text-xs sm:text-sm text-green-800">Valid Rows</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-red-600">{previewData.errorRows}</div>
                <div className="text-xs sm:text-sm text-red-800">Error Rows</div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-yellow-600">{previewData.duplicateRows || 0}</div>
                <div className="text-xs sm:text-sm text-yellow-800">Duplicates</div>
              </div>
            </div>
          </div>



          {/* Data Preview Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">

            {/* Validation Errors with Force Import Option */}
            {validationErrors.length > 0 && (
              <div className="mb-6">
                <h4 className="font-medium text-red-900 mb-3 flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  ⚠️ Data Validation Issues ({validationErrors.length} errors found)
                </h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="mb-4">
                    <p className="text-sm text-red-800 mb-3 font-medium">
                      The following rows have validation errors. You can either fix the data or force import with invalid data:
                    </p>
                  </div>

                  <div className="max-h-60 overflow-y-auto mb-4">
                    {validationErrors.map((error, index) => (
                      <div key={index} className="text-sm text-red-800 mb-2 p-2 bg-white rounded border border-red-300">
                        <strong>Row {error.row}:</strong> {error.message}
                        {error.field && <span className="text-red-600"> (Field: {error.field})</span>}
                      </div>
                    ))}
                  </div>

                  <div className="bg-yellow-50 border border-yellow-300 rounded-lg p-3">
                    <div className="flex items-center mb-2">
                      <FaExclamationTriangle className="text-yellow-600 mr-2" />
                      <span className="font-medium text-yellow-800">What would you like to do?</span>
                    </div>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="errorHandling"
                          value="skip"
                          defaultChecked
                          className="mr-2"
                        />
                        <span className="text-sm text-yellow-900">Skip rows with errors (recommended)</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="errorHandling"
                          value="force"
                          className="mr-2"
                        />
                        <span className="text-sm text-yellow-900">Force import all rows (including invalid data)</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Duplicate Handling */}
            {duplicateRows.length > 0 && (
              <div className="mb-6">
                <h4 className="font-medium text-yellow-900 mb-3 flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  Duplicate Records Found ({duplicateRows.length})
                </h4>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="mb-4">
                    <p className="text-sm text-yellow-800 mb-3">
                      The following records already exist in the database. Select which ones you want to force import:
                    </p>
                    <div className="flex items-center mb-3">
                      <input
                        type="checkbox"
                        id="selectAllDuplicates"
                        checked={Object.values(selectedDuplicates).every(Boolean)}
                        onChange={(e) => handleSelectAllDuplicates(e.target.checked)}
                        className="mr-2"
                      />
                      <label htmlFor="selectAllDuplicates" className="text-sm font-medium text-yellow-900">
                        Select All Duplicates
                      </label>
                    </div>
                  </div>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {duplicateRows.map((duplicate, index) => (
                      <div key={index} className="flex items-center p-3 bg-white rounded border border-yellow-300">
                        <input
                          type="checkbox"
                          id={`duplicate-${index}`}
                          checked={selectedDuplicates[index] || false}
                          onChange={(e) => handleDuplicateSelection(index, e.target.checked)}
                          className="mr-3"
                        />
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            <strong>Row {duplicate.row}:</strong> {duplicate.companyName || 'Unknown Company'}
                          </div>
                          <div className="text-xs text-gray-600">
                            {duplicate.reason} - {duplicate.field}: {duplicate.value}
                          </div>
                        </div>
                        <div className="text-xs text-yellow-700 bg-yellow-100 px-2 py-1 rounded">
                          Force Import?
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <h4 className="font-medium text-gray-900 mb-4 flex items-center">
              <FaEye className="mr-2 text-blue-600" />
              Data Preview (First 10 rows)
            </h4>

            {/* Dynamic Data Preview Table */}
            <div className="overflow-x-auto -mx-6 px-6 sm:mx-0 sm:px-0">
              <div className="inline-block min-w-full align-middle">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10 min-w-20">
                        Status
                      </th>
                      {previewData.columns?.map((column, index) => (
                        <th key={index} className="px-2 sm:px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-24 sm:min-w-32">
                          <div className="flex flex-col">
                            <span className="truncate" title={column}>{column}</span>
                            {previewData.columnMapping?.[column]?.required && (
                              <span className="text-red-500 text-xs">Required</span>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {previewData.preview?.slice(0, 10).map((row, index) => (
                      <tr key={index} className={row.hasErrors ? 'bg-red-50' : row.isDuplicate ? 'bg-yellow-50' : 'hover:bg-gray-50'}>
                        <td className="px-2 sm:px-4 py-3 whitespace-nowrap sticky left-0 bg-inherit z-10 min-w-20">
                          <div className="flex items-center space-x-1 sm:space-x-2">
                            {row.hasErrors ? (
                              <FaTimes className="text-red-500 text-sm" title="Has validation errors" />
                            ) : row.isDuplicate ? (
                              <FaExclamationTriangle className="text-yellow-500 text-sm" title="Duplicate record" />
                            ) : (
                              <FaCheck className="text-green-500 text-sm" title="Valid record" />
                            )}
                            <span className="text-xs text-gray-500 hidden sm:inline">#{index + 1}</span>
                          </div>
                        </td>
                        {previewData.columns?.map((column, colIndex) => {
                          const value = row.data[column];
                          const mapping = previewData.columnMapping?.[column];
                          const hasError = row.errors?.some(error => error.includes(column));

                          return (
                            <td key={colIndex} className={`px-2 sm:px-4 py-3 text-sm min-w-24 sm:min-w-32 ${hasError ? 'bg-red-100' : ''}`}>
                              <div className="max-w-32 sm:max-w-48">
                                <div className={`truncate ${hasError ? 'text-red-900' : 'text-gray-900'}`} title={value || '-'}>
                                  {value || '-'}
                                </div>
                                {mapping?.transformation && mapping.transformation !== 'none' && value && (
                                  <div className="text-xs text-blue-600 mt-1 hidden sm:block">
                                    Will be: {mapping.transformation === 'uppercase' ? value.toUpperCase() :
                                             mapping.transformation === 'lowercase' ? value.toLowerCase() : value}
                                  </div>
                                )}
                                {hasError && (
                                  <div className="text-xs text-red-600 mt-1">
                                    {row.errors?.find(error => error.includes(column))}
                                  </div>
                                )}
                              </div>
                            </td>
                          );
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {previewData.preview?.length > 10 && (
                <div className="text-center py-4 text-sm text-gray-500">
                  Showing first 10 rows of {previewData.totalRows} total rows
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Confirm Import */}
      {step === 3 && previewData && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-6">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 mb-4">
                <FaCheckCircle className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Confirm Import</h3>
              <p className="text-gray-600">Review the summary and confirm to proceed with the import</p>
            </div>

            {/* Import Summary */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-blue-600">{previewData.totalRows}</div>
                <div className="text-xs sm:text-sm text-blue-800">Total Rows</div>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-green-600">{previewData.validRows}</div>
                <div className="text-xs sm:text-sm text-green-800">Will be Imported</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-red-600">{previewData.errorRows}</div>
                <div className="text-xs sm:text-sm text-red-800">Will be Skipped</div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-xl sm:text-2xl font-bold text-yellow-600">{previewData.duplicateRows || 0}</div>
                <div className="text-xs sm:text-sm text-yellow-800">Duplicates Found</div>
              </div>
            </div>

            {/* Import Details */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="font-medium text-gray-900 mb-3">What will happen:</h4>
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-center">
                  <FaCheck className="text-green-500 mr-2" />
                  {previewData.validRows} valid records will be imported into the database
                </li>
                <li className="flex items-center">
                  <FaTimes className="text-red-500 mr-2" />
                  {previewData.errorRows} records with errors will be skipped
                </li>
                {previewData.duplicateRows > 0 && (
                  <li className="flex items-center">
                    <FaExclamationTriangle className="text-yellow-500 mr-2" />
                    {previewData.duplicateRows} duplicate records will be handled based on your selection
                  </li>
                )}
                <li className="flex items-center">
                  <FaCheckCircle className="text-blue-500 mr-2" />
                  All imported records will be assigned to your tenant
                </li>
              </ul>
            </div>

            {/* Duplicate Handling Options */}
            {duplicateRows.length > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-yellow-900 mb-3 flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  Duplicate Records ({duplicateRows.length})
                </h4>
                <p className="text-sm text-yellow-800 mb-3">
                  Choose how to handle duplicate records:
                </p>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="duplicateHandling"
                      value="skip"
                      defaultChecked
                      className="mr-2"
                    />
                    <span className="text-sm text-yellow-900">Skip all duplicate records (recommended)</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="duplicateHandling"
                      value="force"
                      className="mr-2"
                    />
                    <span className="text-sm text-yellow-900">Force import selected duplicates</span>
                  </label>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:space-x-0">
              <button
                onClick={() => setStep(2)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Back to Preview
              </button>
              <button
                onClick={resetImport}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Start Over
              </button>
              <button
                onClick={() => importData()}
                disabled={previewData.validRows === 0}
                className="px-8 py-2 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium"
                style={{ backgroundColor: '#15579e' }}
              >
                Confirm Import ({previewData.validRows} records)
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 4: Import Results */}
      {step === 4 && importResults && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-6">
              {importResults.successCount > 0 ? (
                <>
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                    <FaCheck className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    🎉 Import Completed Successfully!
                  </h3>
                  <p className="text-gray-600">
                    {importResults.successCount} customer{importResults.successCount > 1 ? 's' : ''} imported successfully
                    {importResults.errorCount > 0 && `, ${importResults.errorCount} failed`}.
                  </p>
                </>
              ) : (
                <>
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                    <FaTimes className="h-8 w-8 text-red-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    ❌ Import Failed
                  </h3>
                  <p className="text-gray-600">
                    No customers were imported. Please check the errors below and try again.
                  </p>
                </>
              )}
            </div>

            {/* Results Summary */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                <div className="text-2xl sm:text-3xl font-bold text-green-600">{importResults.successCount}</div>
                <div className="text-xs sm:text-sm text-green-800">Successfully Imported</div>
              </div>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                <div className="text-2xl sm:text-3xl font-bold text-red-600">{importResults.errorCount}</div>
                <div className="text-xs sm:text-sm text-red-800">Failed to Import</div>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <div className="text-2xl sm:text-3xl font-bold text-yellow-600">{importResults.skippedCount || 0}</div>
                <div className="text-xs sm:text-sm text-yellow-800">Skipped (Duplicates)</div>
              </div>
            </div>

            {/* Error Details */}
            {importResults.errors && importResults.errors.length > 0 && (
              <div className="mb-6">
                <h4 className="font-medium text-red-900 mb-3">Import Errors:</h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  {importResults.errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-800 mb-2">
                      <strong>Row {error.row}:</strong> {error.message}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Success Details */}
            {importResults.successDetails && importResults.successDetails.length > 0 && (
              <div className="mb-6">
                <h4 className="font-medium text-green-900 mb-3">Successfully Imported Customers:</h4>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                  {importResults.successDetails.slice(0, 10).map((customer, index) => (
                    <div key={index} className="text-sm text-green-800 mb-1">
                      {customer.company_name} ({customer.customer_code})
                    </div>
                  ))}
                  {importResults.successDetails.length > 10 && (
                    <div className="text-sm text-green-600 mt-2">
                      ... and {importResults.successDetails.length - 10} more customers
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:space-x-0">
              {importResults.successCount > 0 ? (
                <>
                  <button
                    onClick={resetImport}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Import More Data
                  </button>
                  <button
                    onClick={() => navigate('/customers')}
                    className="px-6 py-2 text-white rounded-md hover:bg-blue-700 transition-colors"
                    style={{ backgroundColor: '#15579e' }}
                  >
                    View Customers
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => setStep(2)}
                    className="px-6 py-2 text-white rounded-md hover:bg-blue-700 transition-colors"
                    style={{ backgroundColor: '#15579e' }}
                  >
                    Back to Preview
                  </button>
                  <button
                    onClick={resetImport}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Start Over
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Duplicate Confirmation Modal */}
      {showDuplicateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Duplicate Records Found</h3>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                {duplicateRows.length} duplicate records were found. What would you like to do?
              </p>

              <div className="text-sm text-gray-700">
                <p><strong>Selected for force import:</strong> {Object.values(selectedDuplicates).filter(Boolean).length}</p>
                <p><strong>Will be skipped:</strong> {duplicateRows.length - Object.values(selectedDuplicates).filter(Boolean).length}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-end gap-3">
              <button
                onClick={() => setShowDuplicateModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => importData(false)}
                className="px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors"
              >
                Skip All Duplicates
              </button>
              <button
                onClick={() => importData(true)}
                className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
                style={{ backgroundColor: '#15579e' }}
              >
                Import Selected ({Object.values(selectedDuplicates).filter(Boolean).length})
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerImport;
