# 🕒 Attendance Management System

## Overview

A comprehensive attendance management system integrated into the TallyCRM platform, providing real-time attendance tracking, leave management, payroll integration, and analytics for Tally resellers and their teams.

## 🚀 Key Features

### Core Attendance Management
- **GPS-based Check-in/Check-out**: Accurate location tracking with configurable office boundaries
- **Real-time Dashboard**: Live attendance status and working hours tracking
- **Automatic Status Detection**: Late arrivals, early departures, and overtime calculation
- **Manual Entry Support**: Admin/manager capability for attendance corrections
- **Offline Capability**: Mobile app works offline with automatic sync

### Leave Management
- **Flexible Leave Types**: Configurable leave categories with custom rules
- **Leave Balance Tracking**: Real-time balance calculation with carry-forward support
- **Approval Workflow**: Multi-level approval process with notifications
- **Leave Calendar**: Team-wide leave visibility and planning
- **Half-day Support**: Granular leave tracking for partial days

### Payroll Integration
- **Attendance-based Calculation**: Automatic salary computation based on attendance
- **Overtime Processing**: Configurable overtime rules and calculations
- **Deduction Management**: Late arrival and absence deductions
- **Payslip Generation**: Detailed payslips with attendance summary
- **Bulk Processing**: Monthly payroll processing for all employees

### Analytics & Reporting
- **Real-time Analytics**: Attendance trends and patterns
- **Department-wise Reports**: Team performance insights
- **Leave Utilization**: Leave pattern analysis and forecasting
- **Export Capabilities**: PDF and CSV export for all reports
- **Custom Dashboards**: Role-based analytics views

### Mobile-First Design
- **Responsive Interface**: Optimized for all device sizes
- **Progressive Web App**: App-like experience on mobile browsers
- **Touch-friendly UI**: Large buttons and intuitive navigation
- **Offline Support**: Core functionality available without internet

## 🛠️ Technical Implementation

### New Database Tables
- `attendance_records` - Daily attendance tracking
- `attendance_policies` - Configurable attendance rules
- `attendance_settings` - Tenant-specific settings
- `leave_types` - Leave category definitions
- `leave_requests` - Leave applications and approvals
- `leave_balances` - Employee leave balance tracking
- `salary_structures` - Employee salary components
- `payroll_records` - Monthly payroll data

### API Endpoints

#### Attendance Management
```
POST   /api/v1/attendance/check-in          # Record check-in
POST   /api/v1/attendance/check-out         # Record check-out
GET    /api/v1/attendance/today             # Get today's status
GET    /api/v1/attendance/records           # Get attendance history
POST   /api/v1/attendance/manual-entry      # Manual attendance entry
GET    /api/v1/attendance/team              # Team attendance (managers)
```

#### Leave Management
```
GET    /api/v1/leaves/types                 # Get leave types
POST   /api/v1/leaves/request               # Submit leave request
GET    /api/v1/leaves/requests              # Get leave requests
PUT    /api/v1/leaves/requests/:id/approve  # Approve leave
PUT    /api/v1/leaves/requests/:id/reject   # Reject leave
GET    /api/v1/leaves/balance               # Get leave balance
GET    /api/v1/leaves/calendar              # Leave calendar
```

#### Payroll Management
```
GET    /api/v1/payroll/salary-structure     # Get salary structure
POST   /api/v1/payroll/process              # Process payroll
GET    /api/v1/payroll/records              # Get payroll records
PUT    /api/v1/payroll/records/:id/approve  # Approve payroll
```

#### Reports & Analytics
```
GET    /api/reports/attendance-analytics    # Attendance analytics
GET    /api/reports/leave-analytics         # Leave analytics
```

### Frontend Components

#### Attendance Components
- `AttendanceDashboard.jsx` - Main attendance dashboard
- `MobileAttendance.jsx` - Mobile-optimized interface
- `AttendanceReports.jsx` - Analytics and reporting
- `ResponsiveAttendanceCard.jsx` - Reusable attendance widget

#### Leave Management
- `LeaveManagement.jsx` - Leave request management
- `LeaveRequestForm.jsx` - Leave application form
- `LeaveCalendar.jsx` - Team leave calendar

#### Payroll Components
- `PayrollManagement.jsx` - Payroll processing interface

#### Admin Configuration
- `AttendanceConfiguration.jsx` - System settings
- `LeaveTypesManagement.jsx` - Leave type configuration

## 👥 User Roles & Permissions

### Employee
- ✅ Check-in/check-out
- ✅ View own attendance records
- ✅ Submit leave requests
- ✅ View own payslips
- ❌ View team data
- ❌ Approve requests

### Team Lead
- ✅ All employee permissions
- ✅ View team attendance
- ✅ Basic team reports
- ❌ Approve leave requests
- ❌ System configuration

### Manager
- ✅ All team lead permissions
- ✅ Approve/reject leave requests
- ✅ Manual attendance entries
- ✅ Team analytics
- ❌ System-wide configuration

### HR
- ✅ All manager permissions
- ✅ Configure leave types
- ✅ Process payroll
- ✅ System-wide reports
- ✅ Attendance policies

### Admin
- ✅ All permissions
- ✅ System configuration
- ✅ User management
- ✅ Advanced analytics

## 🔧 Configuration

### Environment Variables
```env
# GPS Tracking
GPS_TRACKING_ENABLED=true
OFFICE_LATITUDE=12.9716
OFFICE_LONGITUDE=77.5946
OFFICE_RADIUS=100

# Notifications
ENABLE_EMAIL_NOTIFICATIONS=true
DAILY_REMINDER_TIME=08:45
SEND_LATE_ARRIVAL_ALERTS=true

# Policies
DEFAULT_SHIFT_START=09:00
DEFAULT_SHIFT_END=18:00
GRACE_PERIOD_MINUTES=15
OVERTIME_THRESHOLD_HOURS=8
```

### Attendance Policies
Configure through the admin interface:
- Shift timings
- Grace periods
- Overtime rules
- Location restrictions
- Notification settings

## 📱 Mobile Features

### Progressive Web App (PWA)
- App-like experience on mobile devices
- Offline functionality for core features
- Push notifications for reminders
- Home screen installation

### Mobile-Specific Features
- Large touch-friendly buttons
- GPS-based automatic check-in
- Battery and connectivity status
- Optimized for one-handed use

## 🔒 Security Features

- **Location Verification**: GPS-based office boundary checking
- **Role-based Access**: Granular permission system
- **Data Encryption**: Sensitive data protection
- **Audit Trail**: Complete activity logging
- **Session Management**: Secure token handling

## 📊 Analytics & Insights

### Attendance Analytics
- Daily/weekly/monthly trends
- Department-wise performance
- Late arrival patterns
- Overtime analysis
- Absence tracking

### Leave Analytics
- Leave utilization rates
- Seasonal patterns
- Team coverage analysis
- Balance forecasting

### Payroll Insights
- Attendance impact on salary
- Overtime costs
- Deduction analysis
- Department-wise costs

## 🚀 Getting Started

### For Employees
1. Log into the TallyCRM system
2. Navigate to "Attendance" section
3. Allow location permissions
4. Use "Check In" button to start your day
5. Submit leave requests through "Leave Management"

### For Managers
1. Access team attendance through "Team" section
2. Review and approve leave requests
3. Generate team reports
4. Create manual attendance entries if needed

### For HR/Admin
1. Configure attendance policies in "Settings"
2. Set up leave types and quotas
3. Process monthly payroll
4. Generate system-wide reports

## 📚 Documentation

- [User Guide](docs/USER_GUIDE.md) - Complete user documentation
- [API Documentation](docs/ATTENDANCE_API_DOCUMENTATION.md) - API reference
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Production setup

## 🧪 Testing

### Test Coverage
- Unit tests for all components
- Integration tests for API endpoints
- End-to-end tests for user workflows
- Mobile responsiveness testing

### Running Tests
```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test

# Integration tests
npm run test:integration
```

## 🔄 Integration with Existing CRM

The attendance system seamlessly integrates with the existing TallyCRM features:

- **Employee Management**: Uses existing user/executive models
- **Notifications**: Extends existing notification system
- **Reports**: Integrates with existing reporting framework
- **Permissions**: Uses existing role-based access control
- **Multi-tenancy**: Supports existing tenant architecture

## 🎯 Future Enhancements

### Planned Features
- Biometric integration
- Facial recognition check-in
- Advanced ML-based analytics
- Mobile app (React Native)
- Integration with external HR systems

### Roadmap
- **Phase 1**: Core attendance tracking ✅
- **Phase 2**: Leave management ✅
- **Phase 3**: Payroll integration ✅
- **Phase 4**: Advanced analytics ✅
- **Phase 5**: Mobile app (Planned)
- **Phase 6**: AI/ML features (Future)

## 🆘 Support

- **Documentation**: Check the docs folder
- **Issues**: Report via GitHub Issues
- **Email**: <EMAIL>
- **Training**: Available for team onboarding

---

**Integrated Attendance Management for TallyCRM** 🕒✨
