import { useState, useCallback, useRef } from 'react';
import { apiService } from '../services/api';

/**
 * Hook for server-side executive search
 * Provides debounced search functionality for executive selection components
 */
export const useExecutiveSearch = () => {
  const [searchResults, setSearchResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(null);

  // Timeout ref for proper debouncing
  const timeoutRef = useRef(null);

  // Debounced search function
  const searchExecutives = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const response = await apiService.get('/executives', {
        params: {
          search: searchTerm,
          limit: 20, // Limit results for performance
          page: 1,
          sortBy: 'first_name',
          sortOrder: 'ASC'
        }
      });

      if (response.data?.success && response.data?.data?.executives) {
        // Transform executive data for SearchableSelect
        const executives = response.data.data.executives.map(executive => ({
          id: executive.id,
          first_name: executive.first_name || '',
          last_name: executive.last_name || '',
          name: executive.name || `${executive.first_name || ''} ${executive.last_name || ''}`.trim(),
          email: executive.email || 'N/A',
          employee_code: executive.employee_code || 'N/A',
          phone: executive.phone || 'N/A',
          department: executive.department || 'N/A',
          // Include original executive data
          originalData: executive
        }));

        setSearchResults(executives);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Executive search error:', error);
      setSearchError(error.message || 'Failed to search executives');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Properly debounced search with timeout management
  const debouncedSearch = useCallback((searchTerm) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      searchExecutives(searchTerm);
    }, 150); // 150ms debounce - fast but prevents infinite loops
  }, [searchExecutives]);

  // Reset search state
  const resetSearch = useCallback(() => {
    setSearchResults(null);
    setIsSearching(false);
    setSearchError(null);
  }, []);

  return {
    searchResults,
    isSearching,
    searchError,
    searchExecutives: debouncedSearch,
    resetSearch
  };
};

export default useExecutiveSearch;
