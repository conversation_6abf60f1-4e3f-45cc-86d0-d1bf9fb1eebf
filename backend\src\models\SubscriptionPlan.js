import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const SubscriptionPlan = sequelize.define('SubscriptionPlan', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    price_monthly: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    price_yearly: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    stripe_price_id_monthly: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stripe_price_id_yearly: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stripe_product_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    trial_days: {
      type: DataTypes.INTEGER,
      defaultValue: 14,
    },
    max_users: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
    },
    max_customers: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
    },
    max_service_calls: {
      type: DataTypes.INTEGER,
      defaultValue: 50,
    },
    max_storage_gb: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
    },
    features: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'JSON object containing feature flags and limits',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_popular: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'subscription_plans',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['slug'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Instance methods
  SubscriptionPlan.prototype.getPrice = function(interval = 'monthly') {
    return interval === 'yearly' ? this.price_yearly : this.price_monthly;
  };

  SubscriptionPlan.prototype.getStripePrice = function(interval = 'monthly') {
    return interval === 'yearly' ? this.stripe_price_id_yearly : this.stripe_price_id_monthly;
  };

  SubscriptionPlan.prototype.hasFeature = function(featureName) {
    return this.features && this.features[featureName] === true;
  };

  SubscriptionPlan.prototype.getFeatureLimit = function(featureName) {
    if (!this.features || !this.features[featureName]) {
      return null;
    }
    return this.features[featureName];
  };

  SubscriptionPlan.prototype.canUpgradeTo = function(targetPlan) {
    return this.sort_order < targetPlan.sort_order;
  };

  SubscriptionPlan.prototype.canDowngradeTo = function(targetPlan) {
    return this.sort_order > targetPlan.sort_order;
  };

  // Static methods
  SubscriptionPlan.getDefaultPlan = async function() {
    return await this.findOne({
      where: { slug: 'basic', is_active: true },
    });
  };

  SubscriptionPlan.getActivePlans = async function() {
    return await this.findAll({
      where: { is_active: true },
      order: [['sort_order', 'ASC']],
    });
  };

  // Associations
  SubscriptionPlan.associate = function(models) {
    SubscriptionPlan.hasMany(models.Subscription, {
      foreignKey: 'plan_id',
      as: 'subscriptions',
    });

    SubscriptionPlan.hasMany(models.Tenant, {
      foreignKey: 'subscription_plan',
      sourceKey: 'slug',
      as: 'tenants',
    });
  };

  return SubscriptionPlan;
}
