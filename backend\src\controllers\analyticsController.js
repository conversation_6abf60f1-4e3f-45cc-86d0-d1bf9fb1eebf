import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get SaaS analytics dashboard data
 */
export const getSaasAnalytics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Get tenant metrics
    const tenantMetrics = await getTenantMetrics(startDate, endDate);
    
    // Get revenue metrics
    const revenueMetrics = await getRevenueMetrics(startDate, endDate);
    
    // Get usage metrics
    const usageMetrics = await getUsageMetrics(startDate, endDate);
    
    // Get subscription metrics
    const subscriptionMetrics = await getSubscriptionMetrics(startDate, endDate);

    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        tenantMetrics,
        revenueMetrics,
        usageMetrics,
        subscriptionMetrics,
      },
    });
  } catch (error) {
    logger.error('Get SaaS analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get tenant-specific analytics
 */
export const getTenantAnalytics = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d' } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Get customer metrics
    const customerMetrics = await getCustomerMetrics(tenantId, startDate, endDate);
    
    // Get service call metrics
    const serviceMetrics = await getServiceMetrics(tenantId, startDate, endDate);
    
    // Get sales metrics
    const salesMetrics = await getSalesMetrics(tenantId, startDate, endDate);
    
    // Get usage trends
    const usageTrends = await getUsageTrends(tenantId, startDate, endDate);

    res.json({
      success: true,
      data: {
        period,
        dateRange: { startDate, endDate },
        customerMetrics,
        serviceMetrics,
        salesMetrics,
        usageTrends,
      },
    });
  } catch (error) {
    logger.error('Get tenant analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve tenant analytics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Helper functions

async function getTenantMetrics(startDate, endDate) {
  const totalTenants = await models.Tenant.count();
  
  const newTenants = await models.Tenant.count({
    where: {
      created_at: { [Op.between]: [startDate, endDate] },
    },
  });

  const activeTenants = await models.Tenant.count({
    where: {
      subscription_status: ['active', 'trial'],
    },
  });

  const tenantsByStatus = await models.Tenant.findAll({
    attributes: [
      'subscription_status',
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
    ],
    group: ['subscription_status'],
  });

  const tenantsByPlan = await models.Tenant.findAll({
    attributes: [
      'subscription_plan',
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
    ],
    group: ['subscription_plan'],
  });

  return {
    totalTenants,
    newTenants,
    activeTenants,
    churnRate: totalTenants > 0 ? ((totalTenants - activeTenants) / totalTenants * 100).toFixed(2) : 0,
    tenantsByStatus: tenantsByStatus.reduce((acc, item) => {
      acc[item.subscription_status] = parseInt(item.getDataValue('count'));
      return acc;
    }, {}),
    tenantsByPlan: tenantsByPlan.reduce((acc, item) => {
      acc[item.subscription_plan] = parseInt(item.getDataValue('count'));
      return acc;
    }, {}),
  };
}

async function getRevenueMetrics(startDate, endDate) {
  const totalRevenue = await models.Payment.findOne({
    where: {
      status: 'succeeded',
      processed_at: { [Op.between]: [startDate, endDate] },
    },
    attributes: [
      [models.sequelize.fn('SUM', models.sequelize.col('amount')), 'total'],
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      [models.sequelize.fn('AVG', models.sequelize.col('amount')), 'average'],
    ],
  });

  const monthlyRecurringRevenue = await models.Subscription.findOne({
    where: {
      status: ['active', 'trial'],
      interval: 'month',
    },
    attributes: [
      [models.sequelize.fn('SUM', models.sequelize.col('amount')), 'mrr'],
    ],
  });

  const annualRecurringRevenue = await models.Subscription.findOne({
    where: {
      status: ['active', 'trial'],
      interval: 'year',
    },
    attributes: [
      [models.sequelize.fn('SUM', models.sequelize.col('amount')), 'arr'],
    ],
  });

  return {
    totalRevenue: parseFloat(totalRevenue?.getDataValue('total')) || 0,
    totalPayments: parseInt(totalRevenue?.getDataValue('count')) || 0,
    averagePayment: parseFloat(totalRevenue?.getDataValue('average')) || 0,
    mrr: parseFloat(monthlyRecurringRevenue?.getDataValue('mrr')) || 0,
    arr: parseFloat(annualRecurringRevenue?.getDataValue('arr')) || 0,
  };
}

async function getUsageMetrics(startDate, endDate) {
  const usageByMetric = await models.UsageRecord.findAll({
    where: {
      timestamp: { [Op.between]: [startDate, endDate] },
    },
    attributes: [
      'metric_name',
      [models.sequelize.fn('AVG', models.sequelize.col('quantity')), 'average'],
      [models.sequelize.fn('MAX', models.sequelize.col('quantity')), 'maximum'],
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'records'],
    ],
    group: ['metric_name'],
  });

  return usageByMetric.reduce((acc, item) => {
    acc[item.metric_name] = {
      average: parseFloat(item.getDataValue('average')),
      maximum: parseInt(item.getDataValue('maximum')),
      records: parseInt(item.getDataValue('records')),
    };
    return acc;
  }, {});
}

async function getSubscriptionMetrics(startDate, endDate) {
  const newSubscriptions = await models.Subscription.count({
    where: {
      created_at: { [Op.between]: [startDate, endDate] },
    },
  });

  const canceledSubscriptions = await models.Subscription.count({
    where: {
      canceled_at: { [Op.between]: [startDate, endDate] },
    },
  });

  const subscriptionsByPlan = await models.Subscription.findAll({
    include: [
      {
        model: models.SubscriptionPlan,
        as: 'plan',
        attributes: ['name'],
      },
    ],
    attributes: [
      [models.sequelize.fn('COUNT', models.sequelize.col('Subscription.id')), 'count'],
    ],
    group: ['plan.name'],
  });

  return {
    newSubscriptions,
    canceledSubscriptions,
    netGrowth: newSubscriptions - canceledSubscriptions,
    subscriptionsByPlan: subscriptionsByPlan.reduce((acc, item) => {
      const planName = item.plan?.name || 'Unknown';
      acc[planName] = parseInt(item.getDataValue('count'));
      return acc;
    }, {}),
  };
}

async function getCustomerMetrics(tenantId, startDate, endDate) {
  const totalCustomers = await models.Customer.count({
    where: { tenant_id: tenantId },
  });

  const newCustomers = await models.Customer.count({
    where: {
      tenant_id: tenantId,
      created_at: { [Op.between]: [startDate, endDate] },
    },
  });

  const customersByType = await models.Customer.findAll({
    where: { tenant_id: tenantId },
    attributes: [
      'customer_type',
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
    ],
    group: ['customer_type'],
  });

  return {
    totalCustomers,
    newCustomers,
    customersByType: customersByType.reduce((acc, item) => {
      acc[item.customer_type] = parseInt(item.getDataValue('count'));
      return acc;
    }, {}),
  };
}

async function getServiceMetrics(tenantId, startDate, endDate) {
  const totalServiceCalls = await models.ServiceCall.count({
    where: { tenant_id: tenantId },
  });

  const newServiceCalls = await models.ServiceCall.count({
    where: {
      tenant_id: tenantId,
      created_at: { [Op.between]: [startDate, endDate] },
    },
  });

  const serviceCallsByStatus = await models.ServiceCall.findAll({
    where: { tenant_id: tenantId },
    attributes: [
      'status',
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
    ],
    group: ['status'],
  });

  return {
    totalServiceCalls,
    newServiceCalls,
    serviceCallsByStatus: serviceCallsByStatus.reduce((acc, item) => {
      acc[item.status] = parseInt(item.getDataValue('count'));
      return acc;
    }, {}),
  };
}

async function getSalesMetrics(tenantId, startDate, endDate) {
  const totalSales = await models.Sale.findOne({
    where: { tenant_id: tenantId },
    attributes: [
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total'],
    ],
  });

  const newSales = await models.Sale.findOne({
    where: {
      tenant_id: tenantId,
      created_at: { [Op.between]: [startDate, endDate] },
    },
    attributes: [
      [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total'],
    ],
  });

  return {
    totalSalesCount: parseInt(totalSales?.getDataValue('count')) || 0,
    totalSalesAmount: parseFloat(totalSales?.getDataValue('total')) || 0,
    newSalesCount: parseInt(newSales?.getDataValue('count')) || 0,
    newSalesAmount: parseFloat(newSales?.getDataValue('total')) || 0,
  };
}

async function getUsageTrends(tenantId, startDate, endDate) {
  const usageTrends = await models.UsageRecord.findAll({
    where: {
      tenant_id: tenantId,
      timestamp: { [Op.between]: [startDate, endDate] },
    },
    attributes: [
      'metric_name',
      [models.sequelize.fn('DATE', models.sequelize.col('timestamp')), 'date'],
      [models.sequelize.fn('MAX', models.sequelize.col('quantity')), 'value'],
    ],
    group: ['metric_name', models.sequelize.fn('DATE', models.sequelize.col('timestamp'))],
    order: [['date', 'ASC']],
  });

  return usageTrends.reduce((acc, item) => {
    const metric = item.metric_name;
    if (!acc[metric]) acc[metric] = [];
    acc[metric].push({
      date: item.getDataValue('date'),
      value: parseInt(item.getDataValue('value')),
    });
    return acc;
  }, {});
}
