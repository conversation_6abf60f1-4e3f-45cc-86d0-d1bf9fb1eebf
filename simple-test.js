console.log('Testing corrected import...');

// Create a simple CSV for testing
const fs = require('fs');

const csvContent = `"Company Name*","Customer Code*","Email*","Phone*","Tally Serial Number*","Display Name"
"Test Company","TEST001","<EMAIL>","+91-9876543210","TSN001","Test Co"
"Another Company","TEST002","<EMAIL>","+91-9876543211","TSN002","Another Co"`;

fs.writeFileSync('simple-test.csv', csvContent);
console.log('Simple CSV created');

// Test the API
const fetch = require('node-fetch');
const FormData = require('form-data');

async function testAPI() {
  try {
    // Login
    const authResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    const authData = await authResponse.json();
    console.log('Auth response:', authData.success ? 'Success' : 'Failed');
    
    if (!authData.success) {
      console.log('Auth failed, stopping test');
      return;
    }
    
    // Test preview
    const form = new FormData();
    form.append('file', fs.createReadStream('simple-test.csv'));
    
    const previewResponse = await fetch('http://localhost:3001/api/v1/customers/import/preview', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        ...form.getHeaders()
      },
      body: form
    });
    
    const previewData = await previewResponse.json();
    console.log('Preview response:', previewData.success ? 'Success' : 'Failed');
    
    if (previewData.success) {
      console.log('Required fields:', previewData.data.validationRules?.required_fields);
      console.log('Total rows:', previewData.data.totalRows);
      console.log('Valid rows:', previewData.data.validRows);
      console.log('Error rows:', previewData.data.errorRows);
    } else {
      console.log('Preview error:', previewData.message);
    }
    
    // Cleanup
    fs.unlinkSync('simple-test.csv');
    
  } catch (error) {
    console.error('Test error:', error.message);
  }
}

testAPI();
