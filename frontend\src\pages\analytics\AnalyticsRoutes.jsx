/**
 * Analytics Routes
 * Routing configuration for analytics pages
 */

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import CustomerAnalytics from './CustomerAnalytics';
import ServiceAnalytics from './ServiceAnalytics';
import FinancialAnalytics from './FinancialAnalytics';

const AnalyticsRoutes = () => {
  return (
    <Routes>
      <Route index element={<Navigate to="customers" replace />} />
      <Route path="customers" element={<CustomerAnalytics />} />
      <Route path="services" element={<ServiceAnalytics />} />
      <Route path="financial" element={<FinancialAnalytics />} />
    </Routes>
  );
};

export default AnalyticsRoutes;
