#!/usr/bin/env node

/**
 * Master Data Seeder
 * Seeds all master tables with default data
 */

import models from '../models/index.js';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

const seedMasterData = async () => {
  try {
    logger.info('🌱 Seeding master data...');

    // 1. License Editions
    const licenseEditions = [
      {
        id: uuidv4(),
        name: 'Tally.ERP 9 Silver',
        code: 'SILVER',
        description: 'Single User License',
        version: '6.6.3',
        price: 18000.00,
        annual_maintenance_charge: 4500.00,
        max_companies: 1,
        features: ['Basic Accounting', 'Inventory Management', 'Statutory Compliance'],
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Tally.ERP 9 Gold',
        code: 'GOLD',
        description: 'Multi User License',
        version: '6.6.3',
        price: 54000.00,
        annual_maintenance_charge: 13500.00,
        max_companies: 999,
        features: ['All Silver Features', 'Multi-User Access', 'Advanced Security'],
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'TallyPrime Silver',
        code: 'PRIME_SILVER',
        description: 'Single User TallyPrime',
        version: '3.0',
        price: 18000.00,
        annual_maintenance_charge: 4500.00,
        max_companies: 1,
        features: ['Modern Interface', 'Enhanced Reporting', 'Cloud Connectivity'],
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'TallyPrime Gold',
        code: 'PRIME_GOLD',
        description: 'Multi User TallyPrime',
        version: '3.0',
        price: 54000.00,
        annual_maintenance_charge: 13500.00,
        max_companies: 999,
        features: ['All Prime Silver Features', 'Multi-User Access', 'Advanced Analytics'],
        is_active: true
      }
    ];

    for (const edition of licenseEditions) {
      await models.LicenseEdition.findOrCreate({
        where: { code: edition.code },
        defaults: edition
      });
    }

    // 2. Tally Products
    const tallyProducts = [
      {
        id: uuidv4(),
        name: 'Tally.ERP 9',
        code: 'ERP9',
        description: 'Complete Business Management Software',
        category: 'software',
        version: '6.6.3',
        price: 18000.00,
        cost_price: 15000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'TallyPrime',
        code: 'PRIME',
        description: 'Next Generation Business Software',
        category: 'software',
        version: '3.0',
        price: 18000.00,
        cost_price: 15000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Tally Developer 9',
        code: 'DEV9',
        description: 'Development Platform for Tally',
        category: 'addon',
        version: '6.6.3',
        price: 25000.00,
        cost_price: 20000.00,
        hsn_code: '998361',
        gst_rate: 18.00,
        unit: 'Nos',
        is_service: false,
        is_active: true
      }
    ];

    for (const product of tallyProducts) {
      await models.TallyProduct.findOrCreate({
        where: { code: product.code },
        defaults: product
      });
    }

    // 3. Industries
    const industries = [
      { id: uuidv4(), name: 'Manufacturing', code: 'MFG', description: 'Manufacturing and Production' },
      { id: uuidv4(), name: 'Trading', code: 'TRD', description: 'Trading and Distribution' },
      { id: uuidv4(), name: 'Services', code: 'SVC', description: 'Service Industry' },
      { id: uuidv4(), name: 'IT/Software', code: 'IT', description: 'Information Technology' },
      { id: uuidv4(), name: 'Healthcare', code: 'HC', description: 'Healthcare and Medical' },
      { id: uuidv4(), name: 'Education', code: 'EDU', description: 'Educational Institutions' },
      { id: uuidv4(), name: 'Real Estate', code: 'RE', description: 'Real Estate and Construction' },
      { id: uuidv4(), name: 'Retail', code: 'RTL', description: 'Retail and Consumer Goods' },
      { id: uuidv4(), name: 'Hospitality', code: 'HSP', description: 'Hotels and Restaurants' },
      { id: uuidv4(), name: 'Transportation', code: 'TRP', description: 'Transport and Logistics' }
    ];

    for (const industry of industries) {
      await models.Industry.findOrCreate({
        where: { code: industry.code },
        defaults: industry
      });
    }

    // 4. Areas (Major Indian Cities)
    const areas = [
      { id: uuidv4(), name: 'Mumbai', code: 'MUM', description: 'Mumbai Metropolitan Area', city: 'Mumbai', state: 'Maharashtra', country: 'India' },
      { id: uuidv4(), name: 'Delhi', code: 'DEL', description: 'Delhi NCR', city: 'Delhi', state: 'Delhi', country: 'India' },
      { id: uuidv4(), name: 'Bangalore', code: 'BLR', description: 'Bangalore Urban', city: 'Bangalore', state: 'Karnataka', country: 'India' },
      { id: uuidv4(), name: 'Chennai', code: 'CHN', description: 'Chennai Metropolitan', city: 'Chennai', state: 'Tamil Nadu', country: 'India' },
      { id: uuidv4(), name: 'Hyderabad', code: 'HYD', description: 'Hyderabad City', city: 'Hyderabad', state: 'Telangana', country: 'India' },
      { id: uuidv4(), name: 'Pune', code: 'PUN', description: 'Pune Metropolitan', city: 'Pune', state: 'Maharashtra', country: 'India' },
      { id: uuidv4(), name: 'Kolkata', code: 'KOL', description: 'Kolkata Metropolitan', city: 'Kolkata', state: 'West Bengal', country: 'India' },
      { id: uuidv4(), name: 'Ahmedabad', code: 'AMD', description: 'Ahmedabad City', city: 'Ahmedabad', state: 'Gujarat', country: 'India' }
    ];

    for (const area of areas) {
      await models.Area.findOrCreate({
        where: { code: area.code },
        defaults: area
      });
    }

    // 5. Designations
    const designations = [
      { id: uuidv4(), name: 'Managing Director', code: 'MD', description: 'Managing Director', is_mandatory: true },
      { id: uuidv4(), name: 'Director', code: 'DIR', description: 'Director', is_mandatory: false },
      { id: uuidv4(), name: 'General Manager', code: 'GM', description: 'General Manager', is_mandatory: false },
      { id: uuidv4(), name: 'Manager', code: 'MGR', description: 'Manager', is_mandatory: false },
      { id: uuidv4(), name: 'Accountant', code: 'ACC', description: 'Accountant', is_mandatory: true },
      { id: uuidv4(), name: 'Accounts Executive', code: 'AE', description: 'Accounts Executive', is_mandatory: false },
      { id: uuidv4(), name: 'IT Head', code: 'ITH', description: 'IT Head', is_mandatory: false },
      { id: uuidv4(), name: 'Purchase Manager', code: 'PM', description: 'Purchase Manager', is_mandatory: false },
      { id: uuidv4(), name: 'Sales Manager', code: 'SM', description: 'Sales Manager', is_mandatory: false }
    ];

    for (const designation of designations) {
      await models.Designation.findOrCreate({
        where: { code: designation.code },
        defaults: designation
      });
    }

    // 6. Staff Roles
    const staffRoles = [
      { id: uuidv4(), name: 'Sales Executive', code: 'SE', description: 'Sales Executive' },
      { id: uuidv4(), name: 'Senior Sales Executive', code: 'SSE', description: 'Senior Sales Executive' },
      { id: uuidv4(), name: 'Sales Manager', code: 'SM', description: 'Sales Manager' },
      { id: uuidv4(), name: 'Technical Support', code: 'TS', description: 'Technical Support Executive' },
      { id: uuidv4(), name: 'Implementation Consultant', code: 'IC', description: 'Implementation Consultant' },
      { id: uuidv4(), name: 'Training Specialist', code: 'TR', description: 'Training Specialist' }
    ];

    for (const role of staffRoles) {
      await models.StaffRole.findOrCreate({
        where: { code: role.code },
        defaults: role
      });
    }

    // 7. Nature of Issues
    const natureOfIssues = [
      { id: uuidv4(), name: 'Installation Issue', code: 'INST', description: 'Software Installation Problems', category: 'installation', severity: 'medium' },
      { id: uuidv4(), name: 'Data Import/Export', code: 'DATA', description: 'Data Import/Export Issues', category: 'data', severity: 'high' },
      { id: uuidv4(), name: 'Performance Issue', code: 'PERF', description: 'Software Performance Problems', category: 'technical', severity: 'medium' },
      { id: uuidv4(), name: 'Training Required', code: 'TRAIN', description: 'User Training Required', category: 'training', severity: 'low' },
      { id: uuidv4(), name: 'Customization Request', code: 'CUST', description: 'Software Customization', category: 'functional', severity: 'medium' },
      { id: uuidv4(), name: 'Bug Report', code: 'BUG', description: 'Software Bug Report', category: 'technical', severity: 'high' },
      { id: uuidv4(), name: 'Feature Request', code: 'FEAT', description: 'New Feature Request', category: 'functional', severity: 'low' }
    ];

    for (const issue of natureOfIssues) {
      await models.NatureOfIssue.findOrCreate({
        where: { code: issue.code },
        defaults: issue
      });
    }

    // 8. Call Statuses
    const callStatuses = [
      { id: uuidv4(), name: 'Open', code: 'OPEN', description: 'Call is open and pending', color: '#fbbf24' },
      { id: uuidv4(), name: 'In Progress', code: 'PROGRESS', description: 'Call is being worked on', color: '#3b82f6' },
      { id: uuidv4(), name: 'Resolved', code: 'RESOLVED', description: 'Call has been resolved', color: '#10b981' },
      { id: uuidv4(), name: 'Closed', code: 'CLOSED', description: 'Call is closed', color: '#6b7280' },
      { id: uuidv4(), name: 'Cancelled', code: 'CANCELLED', description: 'Call was cancelled', color: '#ef4444' },
      { id: uuidv4(), name: 'On Hold', code: 'HOLD', description: 'Call is on hold', color: '#f59e0b' }
    ];

    for (const status of callStatuses) {
      await models.CallStatus.findOrCreate({
        where: { code: status.code },
        defaults: status
      });
    }

    // 9. Additional Services
    const additionalServices = [
      { id: uuidv4(), name: 'Data Migration', code: 'DM', description: 'Data Migration Service', price: 5000.00 },
      { id: uuidv4(), name: 'Custom Reports', code: 'CR', description: 'Custom Report Development', price: 3000.00 },
      { id: uuidv4(), name: 'Advanced Training', code: 'AT', description: 'Advanced User Training', price: 2000.00 },
      { id: uuidv4(), name: 'Remote Support', code: 'RS', description: 'Remote Technical Support', price: 1500.00 },
      { id: uuidv4(), name: 'On-site Support', code: 'OS', description: 'On-site Technical Support', price: 3500.00 },
      { id: uuidv4(), name: 'TDL Development', code: 'TDL', description: 'TDL Customization', price: 8000.00 }
    ];

    for (const service of additionalServices) {
      await models.AdditionalService.findOrCreate({
        where: { code: service.code },
        defaults: service
      });
    }

    // 10. Type of Calls
    const typeOfCalls = [
      {
        id: uuidv4(),
        name: 'AMC Visit',
        code: 'AMC_VISIT',
        description: 'Annual Maintenance Contract visit',
        category: 'amc',
        service_type: 'onsite',
        is_billable: false,
        default_duration: 120,
        requires_approval: false,
        sort_order: 1,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'TSS Visit',
        code: 'TSS_VISIT',
        description: 'Tally Software Services visit',
        category: 'tss',
        service_type: 'onsite',
        is_billable: false,
        default_duration: 90,
        requires_approval: false,
        sort_order: 2,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Computer Repair',
        code: 'COMPUTER_REPAIR',
        description: 'Computer hardware/software repair',
        category: 'support',
        service_type: 'onsite',
        is_billable: true,
        default_duration: 180,
        requires_approval: false,
        sort_order: 3,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Software Installation',
        code: 'SOFTWARE_INSTALL',
        description: 'Tally software installation and setup',
        category: 'support',
        service_type: 'onsite',
        is_billable: true,
        default_duration: 120,
        requires_approval: false,
        sort_order: 4,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Data Migration',
        code: 'DATA_MIGRATION',
        description: 'Data migration and transfer services',
        category: 'support',
        service_type: 'onsite',
        is_billable: true,
        default_duration: 240,
        requires_approval: true,
        sort_order: 5,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'Training Session',
        code: 'TRAINING',
        description: 'Tally software training session',
        category: 'training',
        service_type: 'both',
        is_billable: true,
        default_duration: 180,
        requires_approval: false,
        sort_order: 6,
        is_active: true
      },
      {
        id: uuidv4(),
        name: 'General Support',
        code: 'GENERAL_SUPPORT',
        description: 'General technical support and assistance',
        category: 'support',
        service_type: 'both',
        is_billable: true,
        default_duration: 60,
        requires_approval: false,
        sort_order: 7,
        is_active: true
      }
    ];

    // Check if TypeOfCall model exists and table has data
    try {
      const existingTypeOfCallsCount = await models.TypeOfCall.count();
      if (existingTypeOfCallsCount === 0) {
        // Only seed if table is empty
        for (const typeOfCall of typeOfCalls) {
          // Remove the hardcoded ID to let the database generate it
          const { id, ...typeOfCallData } = typeOfCall;
          await models.TypeOfCall.findOrCreate({
            where: { code: typeOfCall.code },
            defaults: typeOfCallData
          });
        }
        logger.info('✅ Type of calls seeded successfully');
      } else {
        logger.info('ℹ️  Type of calls already exist, skipping seed');
      }
    } catch (error) {
      logger.info('ℹ️  TypeOfCall model not available or table does not exist, skipping seed');
      logger.info('Error details:', error.message);
    }

    logger.info('✅ Master data seeded successfully');

  } catch (error) {
    logger.error('❌ Error seeding master data:', error);
    throw error;
  }
};

export default seedMasterData;
