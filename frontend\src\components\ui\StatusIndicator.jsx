import React from 'react';
import { cn } from '../../utils/helpers';

/**
 * Enhanced Status Indicator Component
 * Provides consistent status visualization across the TallyCRM application
 * Supports multiple status types with color-coded badges and icons
 */
const StatusIndicator = ({
  status,
  type = 'service', // 'service', 'priority', 'general'
  size = 'md',
  showIcon = true,
  showText = true,
  className = '',
  ...props
}) => {
  // Normalize status to lowercase for consistent matching
  const normalizedStatus = status?.toLowerCase()?.trim() || '';

  // Service call status configurations
  const serviceStatusConfig = {
    'completed': {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200',
      icon: '✅',
      label: 'Completed'
    },
    'cancelled': {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      icon: '❌',
      label: 'Cancelled'
    },
    'canceled': {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      icon: '❌',
      label: 'Cancelled'
    },
    'pending': {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200',
      icon: '⏳',
      label: 'Pending'
    },
    'open': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔵',
      label: 'Open'
    },
    'in_progress': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔄',
      label: 'In Progress'
    },
    'in progress': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔄',
      label: 'In Progress'
    },
    'on_process': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔄',
      label: 'On Process'
    },
    'progress': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔄',
      label: 'In Progress'
    },
    'working': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      border: 'border-blue-200',
      icon: '🔄',
      label: 'Working'
    },
    'on_hold': {
      bg: 'bg-orange-100',
      text: 'text-orange-800',
      border: 'border-orange-200',
      icon: '⏸️',
      label: 'On Hold'
    },
    'hold': {
      bg: 'bg-orange-100',
      text: 'text-orange-800',
      border: 'border-orange-200',
      icon: '⏸️',
      label: 'On Hold'
    },
    'follow_up': {
      bg: 'bg-purple-100',
      text: 'text-purple-800',
      border: 'border-purple-200',
      icon: '📞',
      label: 'Follow Up'
    },
    'onsite': {
      bg: 'bg-indigo-100',
      text: 'text-indigo-800',
      border: 'border-indigo-200',
      icon: '🏢',
      label: 'Onsite'
    }
  };

  // Priority status configurations
  const priorityStatusConfig = {
    'high': {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      icon: '🔴',
      label: 'High'
    },
    'urgent': {
      bg: 'bg-red-100',
      text: 'text-red-800',
      border: 'border-red-200',
      icon: '🔴',
      label: 'Urgent'
    },
    'medium': {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      border: 'border-yellow-200',
      icon: '🟡',
      label: 'Medium'
    },
    'low': {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200',
      icon: '🟢',
      label: 'Low'
    }
  };

  // General status configurations
  const generalStatusConfig = {
    'active': {
      bg: 'bg-green-100',
      text: 'text-green-800',
      border: 'border-green-200',
      icon: '✅',
      label: 'Active'
    },
    'inactive': {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200',
      icon: '❌',
      label: 'Inactive'
    }
  };

  // Select appropriate configuration based on type
  let config;
  switch (type) {
    case 'priority':
      config = priorityStatusConfig[normalizedStatus];
      break;
    case 'general':
      config = generalStatusConfig[normalizedStatus];
      break;
    case 'service':
    default:
      config = serviceStatusConfig[normalizedStatus];
      break;
  }

  // Fallback configuration for unknown statuses
  if (!config) {
    config = {
      bg: 'bg-gray-100',
      text: 'text-gray-800',
      border: 'border-gray-200',
      icon: '⚪',
      label: status || 'Unknown'
    };
  }

  // Size configurations
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  const iconSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const baseClasses = 'inline-flex items-center font-medium rounded-full border transition-all duration-200 hover:shadow-sm';

  return (
    <span
      className={cn(
        baseClasses,
        config.bg,
        config.text,
        config.border,
        sizeClasses[size],
        className
      )}
      title={config.label}
      {...props}
    >
      {showIcon && (
        <span className={cn('mr-1', iconSizeClasses[size])}>
          {config.icon}
        </span>
      )}
      {showText && (
        <span className="whitespace-nowrap">
          {config.label}
        </span>
      )}
    </span>
  );
};

/**
 * Service Status Badge - Specialized component for service call statuses
 */
export const ServiceStatusBadge = ({ status, ...props }) => (
  <StatusIndicator status={status} type="service" {...props} />
);

/**
 * Priority Badge - Specialized component for priority indicators
 */
export const PriorityBadge = ({ priority, ...props }) => (
  <StatusIndicator status={priority} type="priority" {...props} />
);

/**
 * General Status Badge - For active/inactive and other general statuses
 */
export const GeneralStatusBadge = ({ status, ...props }) => (
  <StatusIndicator status={status} type="general" {...props} />
);

export default StatusIndicator;
