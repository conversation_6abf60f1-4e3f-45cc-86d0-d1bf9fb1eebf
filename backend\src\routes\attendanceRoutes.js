import express from 'express';
import {
  checkIn,
  checkOut,
  getAttendanceRecords,
  getTodayAttendance,
  createManualEntry,
  updateAttendanceRecord,
  getAttendanceSummary
} from '../controllers/attendanceController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validateTenantExists } from '../middleware/tenantValidation.js';
import { requirePermission } from '../middleware/auth.js';
import { body, query, param, validationResult } from 'express-validator';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }
  next();
};

// Check-in validation
const checkInValidation = [
  body('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  body('location').optional().isObject().withMessage('Location must be an object'),
  body('location.latitude').optional().isFloat({ min: -90, max: 90 }).withMessage('Invalid latitude'),
  body('location.longitude').optional().isFloat({ min: -180, max: 180 }).withMessage('Invalid longitude'),
  body('location.address').optional().isString().withMessage('Address must be a string'),
  body('notes').optional().isString().withMessage('Notes must be a string')
];

// Check-out validation
const checkOutValidation = [
  body('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  body('location').optional().isObject().withMessage('Location must be an object'),
  body('location.latitude').optional().isFloat({ min: -90, max: 90 }).withMessage('Invalid latitude'),
  body('location.longitude').optional().isFloat({ min: -180, max: 180 }).withMessage('Invalid longitude'),
  body('location.address').optional().isString().withMessage('Address must be a string'),
  body('notes').optional().isString().withMessage('Notes must be a string')
];

// Manual entry validation
const manualEntryValidation = [
  body('employee_id').isUUID().withMessage('Valid employee ID is required'),
  body('date').isISO8601().withMessage('Valid date is required'),
  body('check_in_time').optional().isISO8601().withMessage('Invalid check-in time'),
  body('check_out_time').optional().isISO8601().withMessage('Invalid check-out time'),
  body('status').isIn(['present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'])
    .withMessage('Invalid status'),
  body('total_hours').optional().isFloat({ min: 0, max: 24 }).withMessage('Invalid total hours'),
  body('overtime_hours').optional().isFloat({ min: 0, max: 24 }).withMessage('Invalid overtime hours'),
  body('notes').optional().isString().withMessage('Notes must be a string'),
  body('is_holiday').optional().isBoolean().withMessage('is_holiday must be boolean')
];

// Update record validation
const updateRecordValidation = [
  param('id').isUUID().withMessage('Valid record ID is required'),
  body('check_in_time').optional().isISO8601().withMessage('Invalid check-in time'),
  body('check_out_time').optional().isISO8601().withMessage('Invalid check-out time'),
  body('status').optional().isIn(['present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'])
    .withMessage('Invalid status'),
  body('total_hours').optional().isFloat({ min: 0, max: 24 }).withMessage('Invalid total hours'),
  body('overtime_hours').optional().isFloat({ min: 0, max: 24 }).withMessage('Invalid overtime hours'),
  body('notes').optional().isString().withMessage('Notes must be a string'),
  body('is_holiday').optional().isBoolean().withMessage('is_holiday must be boolean')
];

// Query validation for attendance records
const attendanceQueryValidation = [
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  query('start_date').optional().isISO8601().withMessage('Invalid start date'),
  query('end_date').optional().isISO8601().withMessage('Invalid end date'),
  query('status').optional().isIn(['present', 'absent', 'late', 'half_day', 'work_from_home', 'on_leave'])
    .withMessage('Invalid status'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('sort_by').optional().isIn(['date', 'status', 'total_hours', 'created_at'])
    .withMessage('Invalid sort field'),
  query('sort_order').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC')
];

// Summary query validation
const summaryQueryValidation = [
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  query('start_date').optional().isISO8601().withMessage('Invalid start date'),
  query('end_date').optional().isISO8601().withMessage('Invalid end date'),
  query('group_by').optional().isIn(['employee', 'department', 'date'])
    .withMessage('Invalid group_by value')
];

// Apply middleware to all routes
router.use(authenticateToken);
router.use(validateTenantExists);

/**
 * @route POST /api/attendance/check-in
 * @desc Employee check-in
 * @access Private (Employee)
 */
router.post('/check-in',
  requirePermission('attendance.create'),
  checkInValidation,
  handleValidationErrors,
  checkIn
);

/**
 * @route POST /api/attendance/check-out
 * @desc Employee check-out
 * @access Private (Employee)
 */
router.post('/check-out',
  requirePermission('attendance.create'),
  checkOutValidation,
  handleValidationErrors,
  checkOut
);

/**
 * @route GET /api/attendance/today
 * @desc Get today's attendance status
 * @access Private (Employee)
 */
router.get('/today',
  requirePermission('attendance.read'),
  getTodayAttendance
);

/**
 * @route GET /api/attendance/records
 * @desc Get attendance records
 * @access Private (Employee can view own, Manager can view team, Admin can view all)
 */
router.get('/records',
  requirePermission('attendance.read'),
  attendanceQueryValidation,
  handleValidationErrors,
  getAttendanceRecords
);

/**
 * @route POST /api/attendance/manual-entry
 * @desc Create manual attendance entry
 * @access Private (Admin/HR/Manager only)
 */
router.post('/manual-entry',
  requirePermission('attendance.create'),
  manualEntryValidation,
  handleValidationErrors,
  createManualEntry
);

/**
 * @route PUT /api/attendance/records/:id
 * @desc Update attendance record
 * @access Private (Admin/HR/Manager or record owner)
 */
router.put('/records/:id',
  requirePermission('attendance.update'),
  updateRecordValidation,
  handleValidationErrors,
  updateAttendanceRecord
);

/**
 * @route GET /api/attendance/summary
 * @desc Get attendance summary
 * @access Private (Manager can view team, Admin can view all)
 */
router.get('/summary',
  requirePermission('attendance.read'),
  summaryQueryValidation,
  handleValidationErrors,
  getAttendanceSummary
);

export default router;
