import express from 'express';
import {
  getSalaryStructure,
  createSalaryStructure,
  processPayroll,
  getPayrollRecords,
  approvePayroll,
  markPayrollPaid,
  getPayrollReports
} from '../controllers/payrollController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validateTenantExists } from '../middleware/tenantValidation.js';
import { requirePermission } from '../middleware/auth.js';
import { body, query, param, validationResult } from 'express-validator';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation errors',
      errors: errors.array()
    });
  }
  next();
};

// Salary structure validation
const salaryStructureValidation = [
  body('employee_id').isUUID().withMessage('Valid employee ID is required'),
  body('structure_name').isString().isLength({ min: 2, max: 100 })
    .withMessage('Structure name must be between 2 and 100 characters'),
  body('basic_salary').isFloat({ min: 0 }).withMessage('Basic salary must be a positive number'),
  body('hra').optional().isFloat({ min: 0 }).withMessage('HRA must be a positive number'),
  body('transport_allowance').optional().isFloat({ min: 0 }).withMessage('Transport allowance must be positive'),
  body('medical_allowance').optional().isFloat({ min: 0 }).withMessage('Medical allowance must be positive'),
  body('special_allowance').optional().isFloat({ min: 0 }).withMessage('Special allowance must be positive'),
  body('performance_allowance').optional().isFloat({ min: 0 }).withMessage('Performance allowance must be positive'),
  body('pf_employee').optional().isFloat({ min: 0 }).withMessage('PF employee must be positive'),
  body('pf_employer').optional().isFloat({ min: 0 }).withMessage('PF employer must be positive'),
  body('esi_employee').optional().isFloat({ min: 0 }).withMessage('ESI employee must be positive'),
  body('esi_employer').optional().isFloat({ min: 0 }).withMessage('ESI employer must be positive'),
  body('professional_tax').optional().isFloat({ min: 0 }).withMessage('Professional tax must be positive'),
  body('income_tax').optional().isFloat({ min: 0 }).withMessage('Income tax must be positive'),
  body('overtime_rate_per_hour').optional().isFloat({ min: 0 }).withMessage('Overtime rate must be positive'),
  body('late_deduction_per_hour').optional().isFloat({ min: 0 }).withMessage('Late deduction must be positive'),
  body('absent_deduction_per_day').optional().isFloat({ min: 0 }).withMessage('Absent deduction must be positive'),
  body('pf_calculation_method').optional().isIn(['percentage', 'fixed'])
    .withMessage('PF calculation method must be percentage or fixed'),
  body('pf_percentage').optional().isFloat({ min: 0, max: 100 }).withMessage('PF percentage must be 0-100'),
  body('esi_calculation_method').optional().isIn(['percentage', 'fixed'])
    .withMessage('ESI calculation method must be percentage or fixed'),
  body('esi_percentage').optional().isFloat({ min: 0, max: 100 }).withMessage('ESI percentage must be 0-100'),
  body('effective_from').isISO8601().withMessage('Valid effective from date is required'),
  body('effective_to').optional().isISO8601().withMessage('Invalid effective to date'),
  body('is_active').optional().isBoolean().withMessage('is_active must be boolean'),
  body('is_default').optional().isBoolean().withMessage('is_default must be boolean')
];

// Payroll processing validation
const payrollProcessValidation = [
  body('month').isInt({ min: 1, max: 12 }).withMessage('Month must be between 1 and 12'),
  body('year').isInt({ min: 2020, max: 2030 }).withMessage('Invalid year'),
  body('employee_ids').optional().isArray().withMessage('Employee IDs must be an array'),
  body('employee_ids.*').optional().isUUID().withMessage('Each employee ID must be valid UUID')
];

// Payment marking validation
const paymentValidation = [
  param('id').isUUID().withMessage('Valid payroll ID is required'),
  body('payment_method').isIn(['bank_transfer', 'cash', 'cheque', 'online'])
    .withMessage('Invalid payment method'),
  body('payment_reference').optional().isString().withMessage('Payment reference must be string')
];

// Query validation for payroll records
const payrollQueryValidation = [
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  query('month').optional().isInt({ min: 1, max: 12 }).withMessage('Invalid month'),
  query('year').optional().isInt({ min: 2020, max: 2030 }).withMessage('Invalid year'),
  query('status').optional().isIn(['draft', 'calculated', 'approved', 'paid', 'cancelled'])
    .withMessage('Invalid status'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('sort_by').optional().isIn(['created_at', 'month', 'year', 'net_salary', 'status'])
    .withMessage('Invalid sort field'),
  query('sort_order').optional().isIn(['ASC', 'DESC']).withMessage('Sort order must be ASC or DESC')
];

// Reports query validation
const reportsQueryValidation = [
  query('month').optional().isInt({ min: 1, max: 12 }).withMessage('Invalid month'),
  query('year').optional().isInt({ min: 2020, max: 2030 }).withMessage('Invalid year'),
  query('department').optional().isString().withMessage('Department must be string'),
  query('report_type').optional().isIn(['summary', 'detailed']).withMessage('Invalid report type')
];

// Apply middleware to all routes
router.use(authenticateToken);
router.use(validateTenantExists);

/**
 * @route GET /api/payroll/salary-structure
 * @desc Get salary structure for an employee
 * @access Private (Employee can view own, HR/Admin can view any)
 */
router.get('/salary-structure',
  requirePermission('payroll.read'),
  query('employee_id').optional().isUUID().withMessage('Invalid employee ID'),
  handleValidationErrors,
  getSalaryStructure
);

/**
 * @route POST /api/payroll/salary-structure
 * @desc Create or update salary structure
 * @access Private (HR/Admin only)
 */
router.post('/salary-structure',
  requirePermission('payroll.create'),
  salaryStructureValidation,
  handleValidationErrors,
  createSalaryStructure
);

/**
 * @route POST /api/payroll/process
 * @desc Process monthly payroll
 * @access Private (HR/Admin only)
 */
router.post('/process',
  requirePermission('payroll.create'),
  payrollProcessValidation,
  handleValidationErrors,
  processPayroll
);

/**
 * @route GET /api/payroll/records
 * @desc Get payroll records
 * @access Private (Employee can view own, HR/Admin can view all)
 */
router.get('/records',
  requirePermission('payroll.read'),
  payrollQueryValidation,
  handleValidationErrors,
  getPayrollRecords
);

/**
 * @route PUT /api/payroll/records/:id/approve
 * @desc Approve payroll record
 * @access Private (HR/Admin only)
 */
router.put('/records/:id/approve',
  requirePermission('payroll.update'),
  param('id').isUUID().withMessage('Valid payroll ID is required'),
  handleValidationErrors,
  approvePayroll
);

/**
 * @route PUT /api/payroll/records/:id/mark-paid
 * @desc Mark payroll as paid
 * @access Private (HR/Admin only)
 */
router.put('/records/:id/mark-paid',
  requirePermission('payroll.update'),
  paymentValidation,
  handleValidationErrors,
  markPayrollPaid
);

/**
 * @route GET /api/payroll/reports
 * @desc Get payroll reports and analytics
 * @access Private (HR/Admin/Manager only)
 */
router.get('/reports',
  requirePermission('payroll.read'),
  reportsQueryValidation,
  handleValidationErrors,
  getPayrollReports
);

export default router;
