import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const NotificationTemplate = sequelize.define('NotificationTemplate', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Template name for identification',
    },
    type: {
      type: DataTypes.ENUM('new_lead', 'new_customer', 'service_call_created', 'service_call_completed', 'renewal_reminder', 'custom'),
      allowNull: false,
      comment: 'Type of notification template',
    },
    channel: {
      type: DataTypes.ENUM('sms', 'email', 'whatsapp'),
      allowNull: false,
      comment: 'Notification channel',
    },
    subject: {
      type: DataTypes.STRING(200),
      allowNull: true,
      comment: 'Subject line for email notifications',
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Template content with placeholders',
    },
    variables: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Available variables for this template',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this template is active',
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this is the default template for this type',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'notification_templates',
    timestamps: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['channel'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['is_default'],
      },
      {
        fields: ['created_by'],
      },
      {
        unique: true,
        fields: ['tenant_id', 'type', 'channel', 'is_default'],
        name: 'unique_default_template_per_type_channel',
        where: {
          is_default: true
        }
      },
    ],
  });

  // Associations
  NotificationTemplate.associate = function(models) {
    NotificationTemplate.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    NotificationTemplate.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });
  };

  // Instance methods
  NotificationTemplate.prototype.renderContent = function(variables = {}) {
    let content = this.content;
    
    // Replace placeholders with actual values
    Object.keys(variables).forEach(key => {
      const placeholder = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      content = content.replace(placeholder, variables[key] || '');
    });
    
    return content;
  };

  NotificationTemplate.prototype.renderSubject = function(variables = {}) {
    if (!this.subject) return null;
    
    let subject = this.subject;
    
    // Replace placeholders with actual values
    Object.keys(variables).forEach(key => {
      const placeholder = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      subject = subject.replace(placeholder, variables[key] || '');
    });
    
    return subject;
  };

  // Static methods
  NotificationTemplate.getDefaultTemplate = async function(tenantId, type, channel) {
    return await this.findOne({
      where: {
        tenant_id: tenantId,
        type,
        channel,
        is_default: true,
        is_active: true,
      },
    });
  };

  NotificationTemplate.getAvailableVariables = function(type) {
    const variableMap = {
      new_lead: [
        'customer_name', 'contact_no', 'amount', 'products_services', 
        'executive_name', 'created_date', 'company_name'
      ],
      new_customer: [
        'customer_name', 'company_name', 'tally_serial_no', 'phone', 
        'email', 'created_date', 'executive_name'
      ],
      service_call_created: [
        'customer_name', 'company_name', 'service_no', 'call_type', 
        'issue_description', 'created_date', 'executive_name'
      ],
      service_call_completed: [
        'customer_name', 'company_name', 'service_no', 'call_type', 
        'resolution', 'completed_date', 'executive_name'
      ],
      renewal_reminder: [
        'customer_name', 'company_name', 'renewal_type', 'expiry_date', 
        'days_remaining', 'renewal_amount', 'contact_person'
      ],
      custom: []
    };

    return variableMap[type] || [];
  };

  return NotificationTemplate;
}
