import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Invoice = sequelize.define('Invoice', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    subscription_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'subscriptions',
        key: 'id',
      },
    },
    stripe_invoice_id: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    invoice_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM(
        'draft',
        'open',
        'paid',
        'void',
        'uncollectible'
      ),
      defaultValue: 'draft',
    },
    amount_due: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    amount_paid: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    amount_remaining: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    subtotal: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    tax_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0,
    },
    total: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'INR',
    },
    period_start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    period_end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    due_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    paid_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    voided_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    attempt_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    next_payment_attempt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    billing_reason: {
      type: DataTypes.ENUM(
        'subscription_cycle',
        'subscription_create',
        'subscription_update',
        'subscription_threshold',
        'upcoming',
        'manual'
      ),
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    line_items: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'invoices',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['subscription_id'],
      },
      {
        fields: ['stripe_invoice_id'],
        unique: true,
        where: {
          stripe_invoice_id: {
            [sequelize.Sequelize.Op.ne]: null,
          },
        },
      },
      {
        fields: ['invoice_number'],
        unique: true,
      },
      {
        fields: ['status'],
      },
      {
        fields: ['due_date'],
      },
    ],
  });

  // Instance methods
  Invoice.prototype.isPaid = function() {
    return this.status === 'paid';
  };

  Invoice.prototype.isOverdue = function() {
    return this.status === 'open' && 
           this.due_date && 
           new Date() > new Date(this.due_date);
  };

  Invoice.prototype.canVoid = function() {
    return ['draft', 'open'].includes(this.status);
  };

  Invoice.prototype.markAsPaid = async function(paidAt = new Date()) {
    await this.update({
      status: 'paid',
      paid_at: paidAt,
      amount_paid: this.amount_due,
      amount_remaining: 0,
    });
  };

  Invoice.prototype.markAsVoid = async function() {
    await this.update({
      status: 'void',
      voided_at: new Date(),
    });
  };

  // Static methods
  Invoice.generateInvoiceNumber = async function() {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const lastInvoice = await this.findOne({
      where: {
        invoice_number: {
          [sequelize.Sequelize.Op.like]: `INV-${year}${month}-%`,
        },
      },
      order: [['created_at', 'DESC']],
    });

    let sequence = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.invoice_number.split('-')[2]);
      sequence = lastSequence + 1;
    }

    return `INV-${year}${month}-${String(sequence).padStart(4, '0')}`;
  };

  // Associations
  Invoice.associate = function(models) {
    Invoice.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Invoice.belongsTo(models.Subscription, {
      foreignKey: 'subscription_id',
      as: 'subscription',
    });

    Invoice.hasMany(models.Payment, {
      foreignKey: 'invoice_id',
      as: 'payments',
    });
  };

  return Invoice;
}
