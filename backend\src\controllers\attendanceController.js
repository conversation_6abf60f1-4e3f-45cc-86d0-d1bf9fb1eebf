import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Employee check-in
 */
export const checkIn = async (req, res) => {
  try {
    const { employee_id, location, notes } = req.body;
    const tenantId = req.user.tenant.id;
    const today = new Date().toISOString().split('T')[0];

    // Check if employee exists and is active
    const employee = await models.Executive.findOne({
      where: { 
        id: employee_id || req.user.executive_id,
        tenant_id: tenantId,
        is_active: true,
        attendance_tracking_enabled: true
      },
      include: [{
        model: models.AttendancePolicy,
        as: 'attendancePolicy'
      }]
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found or attendance tracking disabled'
      });
    }

    // Check if already checked in today
    const existingRecord = await models.AttendanceRecord.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employee.id,
        date: today
      }
    });

    if (existingRecord && existingRecord.check_in_time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked in today',
        data: { existingRecord }
      });
    }

    const checkInTime = new Date();
    
    // Determine status based on policy
    let status = 'present';
    const policy = employee.attendancePolicy;
    
    if (policy) {
      const shiftStart = new Date(`${today}T${policy.shift_start_time}`);
      const graceEnd = new Date(shiftStart.getTime() + policy.grace_period_minutes * 60000);
      
      if (checkInTime > graceEnd) {
        status = 'late';
      }
    }

    // Create or update attendance record
    const attendanceData = {
      tenant_id: tenantId,
      employee_id: employee.id,
      date: today,
      check_in_time: checkInTime,
      status: status,
      location_check_in: location,
      notes: notes,
      is_manual_entry: false
    };

    let attendanceRecord;
    if (existingRecord) {
      attendanceRecord = await existingRecord.update(attendanceData);
    } else {
      attendanceRecord = await models.AttendanceRecord.create(attendanceData);
    }

    // Log the check-in
    logger.info('Employee checked in:', {
      employeeId: employee.id,
      employeeName: employee.getFullName(),
      checkInTime: checkInTime,
      status: status,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Check-in successful',
      data: {
        attendanceRecord,
        employee: {
          id: employee.id,
          name: employee.getFullName(),
          employee_code: employee.employee_code
        }
      }
    });

  } catch (error) {
    logger.error('Check-in error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process check-in',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Employee check-out
 */
export const checkOut = async (req, res) => {
  try {
    const { employee_id, location, notes } = req.body;
    const tenantId = req.user.tenant.id;
    const today = new Date().toISOString().split('T')[0];

    // Find today's attendance record
    const attendanceRecord = await models.AttendanceRecord.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employee_id || req.user.executive_id,
        date: today
      },
      include: [{
        model: models.Executive,
        as: 'employee',
        include: [{
          model: models.AttendancePolicy,
          as: 'attendancePolicy'
        }]
      }]
    });

    if (!attendanceRecord) {
      return res.status(404).json({
        success: false,
        message: 'No check-in record found for today'
      });
    }

    if (attendanceRecord.check_out_time) {
      return res.status(400).json({
        success: false,
        message: 'Already checked out today',
        data: { attendanceRecord }
      });
    }

    const checkOutTime = new Date();
    
    // Calculate total hours
    const totalHours = attendanceRecord.calculateTotalHours();
    
    // Calculate overtime if applicable
    let overtimeHours = 0;
    const policy = attendanceRecord.employee.attendancePolicy;
    if (policy && totalHours > policy.overtime_threshold_hours) {
      overtimeHours = totalHours - policy.overtime_threshold_hours;
    }

    // Update attendance record
    await attendanceRecord.update({
      check_out_time: checkOutTime,
      location_check_out: location,
      total_hours: totalHours,
      overtime_hours: overtimeHours,
      notes: notes ? `${attendanceRecord.notes || ''}\n${notes}`.trim() : attendanceRecord.notes
    });

    // Log the check-out
    logger.info('Employee checked out:', {
      employeeId: attendanceRecord.employee_id,
      employeeName: attendanceRecord.employee.getFullName(),
      checkOutTime: checkOutTime,
      totalHours: totalHours,
      overtimeHours: overtimeHours,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Check-out successful',
      data: {
        attendanceRecord,
        totalHours: totalHours,
        overtimeHours: overtimeHours
      }
    });

  } catch (error) {
    logger.error('Check-out error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process check-out',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get attendance records
 */
export const getAttendanceRecords = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      start_date,
      end_date,
      status,
      page = 1,
      limit = 50,
      sort_by = 'date',
      sort_order = 'DESC'
    } = req.query;

    const whereClause = { tenant_id: tenantId };
    
    if (employee_id) {
      whereClause.employee_id = employee_id;
    }
    
    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date]
      };
    } else if (start_date) {
      whereClause.date = {
        [Op.gte]: start_date
      };
    } else if (end_date) {
      whereClause.date = {
        [Op.lte]: end_date
      };
    }
    
    if (status) {
      whereClause.status = status;
    }

    const offset = (page - 1) * limit;

    const { count, rows: attendanceRecords } = await models.AttendanceRecord.findAndCountAll({
      where: whereClause,
      include: [{
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
      }, {
        model: models.User,
        as: 'approver',
        attributes: ['id', 'first_name', 'last_name']
      }],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        attendanceRecords,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Get attendance records error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance records',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get today's attendance status
 */
export const getTodayAttendance = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { employee_id } = req.query;
    const today = new Date().toISOString().split('T')[0];

    const attendanceRecord = await models.AttendanceRecord.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employee_id || req.user.executive_id,
        date: today
      },
      include: [{
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department'],
        include: [{
          model: models.AttendancePolicy,
          as: 'attendancePolicy'
        }]
      }]
    });

    if (!attendanceRecord) {
      return res.json({
        success: true,
        data: {
          hasCheckedIn: false,
          hasCheckedOut: false,
          attendanceRecord: null
        }
      });
    }

    res.json({
      success: true,
      data: {
        hasCheckedIn: !!attendanceRecord.check_in_time,
        hasCheckedOut: !!attendanceRecord.check_out_time,
        attendanceRecord,
        currentHours: attendanceRecord.check_in_time && !attendanceRecord.check_out_time 
          ? ((new Date() - new Date(attendanceRecord.check_in_time)) / (1000 * 60 * 60)).toFixed(2)
          : null
      }
    });

  } catch (error) {
    logger.error('Get today attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch today\'s attendance',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Manual attendance entry (Admin/Manager only)
 */
export const createManualEntry = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      date,
      check_in_time,
      check_out_time,
      status,
      total_hours,
      overtime_hours,
      notes,
      is_holiday
    } = req.body;

    // Check if user has permission to create manual entries
    // This should be checked by middleware, but adding extra validation
    const userRoles = await req.user.getRoles();
    const canCreateManual = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    );

    if (!canCreateManual) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to create manual attendance entries'
      });
    }

    // Check if employee exists
    const employee = await models.Executive.findOne({
      where: {
        id: employee_id,
        tenant_id: tenantId,
        is_active: true
      }
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Check if attendance record already exists for this date
    const existingRecord = await models.AttendanceRecord.findOne({
      where: {
        tenant_id: tenantId,
        employee_id: employee_id,
        date: date
      }
    });

    if (existingRecord) {
      return res.status(400).json({
        success: false,
        message: 'Attendance record already exists for this date'
      });
    }

    // Create manual attendance record
    const attendanceRecord = await models.AttendanceRecord.create({
      tenant_id: tenantId,
      employee_id: employee_id,
      date: date,
      check_in_time: check_in_time,
      check_out_time: check_out_time,
      status: status,
      total_hours: total_hours || 0,
      overtime_hours: overtime_hours || 0,
      notes: notes,
      is_holiday: is_holiday || false,
      is_manual_entry: true,
      approved_by: req.user.id
    });

    // Log the manual entry
    logger.info('Manual attendance entry created:', {
      employeeId: employee_id,
      employeeName: employee.getFullName(),
      date: date,
      createdBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Manual attendance entry created successfully',
      data: { attendanceRecord }
    });

  } catch (error) {
    logger.error('Create manual entry error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create manual attendance entry',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Update attendance record
 */
export const updateAttendanceRecord = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { id } = req.params;
    const updateData = req.body;

    const attendanceRecord = await models.AttendanceRecord.findOne({
      where: {
        id: id,
        tenant_id: tenantId
      },
      include: [{
        model: models.Executive,
        as: 'employee'
      }]
    });

    if (!attendanceRecord) {
      return res.status(404).json({
        success: false,
        message: 'Attendance record not found'
      });
    }

    // Check permissions - only allow updates by admin/hr/manager or the employee themselves
    const userRoles = await req.user.getRoles();
    const canUpdate = userRoles.some(role =>
      ['admin', 'hr', 'manager'].includes(role.slug)
    ) || attendanceRecord.employee_id === req.user.executive_id;

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update this attendance record'
      });
    }

    // Update the record
    await attendanceRecord.update({
      ...updateData,
      approved_by: req.user.id
    });

    // Log the update
    logger.info('Attendance record updated:', {
      recordId: id,
      employeeId: attendanceRecord.employee_id,
      updatedBy: req.user.id,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Attendance record updated successfully',
      data: { attendanceRecord }
    });

  } catch (error) {
    logger.error('Update attendance record error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update attendance record',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get attendance summary for a period
 */
export const getAttendanceSummary = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const {
      employee_id,
      start_date,
      end_date,
      group_by = 'employee' // 'employee', 'department', 'date'
    } = req.query;

    const whereClause = { tenant_id: tenantId };

    if (employee_id) {
      whereClause.employee_id = employee_id;
    }

    if (start_date && end_date) {
      whereClause.date = {
        [Op.between]: [start_date, end_date]
      };
    }

    const attendanceRecords = await models.AttendanceRecord.findAll({
      where: whereClause,
      include: [{
        model: models.Executive,
        as: 'employee',
        attributes: ['id', 'employee_code', 'first_name', 'last_name', 'department']
      }],
      order: [['date', 'ASC']]
    });

    // Calculate summary based on group_by parameter
    let summary = {};

    if (group_by === 'employee') {
      summary = attendanceRecords.reduce((acc, record) => {
        const empId = record.employee_id;
        if (!acc[empId]) {
          acc[empId] = {
            employee: record.employee,
            totalDays: 0,
            presentDays: 0,
            absentDays: 0,
            lateDays: 0,
            halfDays: 0,
            workFromHomeDays: 0,
            totalHours: 0,
            overtimeHours: 0
          };
        }

        acc[empId].totalDays++;
        acc[empId].totalHours += parseFloat(record.total_hours || 0);
        acc[empId].overtimeHours += parseFloat(record.overtime_hours || 0);

        switch (record.status) {
          case 'present':
            acc[empId].presentDays++;
            break;
          case 'absent':
            acc[empId].absentDays++;
            break;
          case 'late':
            acc[empId].lateDays++;
            acc[empId].presentDays++; // Late is still present
            break;
          case 'half_day':
            acc[empId].halfDays++;
            break;
          case 'work_from_home':
            acc[empId].workFromHomeDays++;
            acc[empId].presentDays++; // WFH is considered present
            break;
        }

        return acc;
      }, {});
    }

    res.json({
      success: true,
      data: {
        summary: Object.values(summary),
        period: { start_date, end_date },
        totalRecords: attendanceRecords.length
      }
    });

  } catch (error) {
    logger.error('Get attendance summary error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance summary',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
