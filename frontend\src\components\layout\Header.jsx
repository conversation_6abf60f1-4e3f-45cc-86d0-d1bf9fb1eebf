import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { cn } from '../../utils/helpers';
import preminfoLogo from '../../assets/preminfotechlogo.svg';

const Header = ({ onToggleSidebar, onToggleSidebarMobile, sidebarCollapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const { user, logout } = useAuth();
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const userMenuRef = useRef(null);
  const notificationsRef = useRef(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Function to get dynamic page title based on current route
  const getPageTitle = () => {
    const { pathname } = location;
    const tab = searchParams.get('tab');

    // Dashboard
    if (pathname === '/dashboard') {
      return 'Dashboard';
    }

    // Customers
    if (pathname.startsWith('/customers')) {
      if (pathname.includes('/add')) return 'Add Customer';
      if (pathname.includes('/edit')) return 'Edit Customer';
      if (pathname.includes('/details')) return 'Customer Details';
      return 'Customers';
    }

    // Services
    if (pathname.startsWith('/services')) {
      if (pathname.includes('/add')) return 'Add Service Call';
      if (pathname.includes('/edit')) return 'Edit Service Call';
      if (pathname.includes('/details')) return 'Service Call Details';
      return 'Service Calls';
    }

    // Sales
    if (pathname.startsWith('/sales')) {
      if (pathname.includes('/add')) return 'Add Sale';
      if (pathname.includes('/edit')) return 'Edit Sale';
      if (pathname.includes('/details')) return 'Sale Details';
      return 'Sales';
    }

    // Masters with tab-based titles
    if (pathname.startsWith('/masters')) {
      const masterTitles = {
        'license-editions': 'License Editions',
        'designations': 'Designations',
        'tally-products': 'Tally Products',
        'staff-roles': 'Staff Roles',
        'executives': 'Executives',
        'industries': 'Industries',
        'areas': 'Areas',
        'nature-of-issues': 'Nature of Issues',
        'additional-services': 'Additional Services',
        'call-statuses': 'Call Statuses'
      };
      return masterTitles[tab] || 'Master Data';
    }

    // Reports
    if (pathname.startsWith('/reports')) {
      return 'Reports';
    }

    // Settings
    if (pathname.startsWith('/settings')) {
      return 'Settings';
    }

    // Profile
    if (pathname.startsWith('/profile')) {
      return 'Profile';
    }

    // Billing
    if (pathname.startsWith('/billing')) {
      if (pathname.includes('/plans')) return 'Subscription Plans';
      if (pathname.includes('/dashboard')) return 'Billing Dashboard';
      if (pathname.includes('/history')) return 'Billing History';
      if (pathname.includes('/success')) return 'Payment Success';
      return 'Billing';
    }

    // Default fallback
    return 'Dashboard';
  };

  const handleProfileClick = () => {
    console.log('🔄 Profile button clicked, navigating to /profile');
    navigate('/profile');
  };

  const handleSettingsClick = () => {
    setUserMenuOpen(false);
    navigate('/settings');
  };

  const handleLogoutClick = async () => {
    setUserMenuOpen(false);
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 px-2 sm:px-4 h-16 overflow-x-hidden relative">
      <div className="flex items-center w-full h-full min-w-0">
        {/* Mobile Menu Toggle */}
        <button
          className="inline-flex items-center justify-center p-2 mr-2 sm:mr-3 text-gray-600 hover:text-gray-900 md:hidden focus:outline-none focus-primary rounded-lg"
          onClick={onToggleSidebarMobile}
        >
          <i className="bi bi-list text-xl"></i>
        </button>

        {/* Desktop Menu Toggle - Task 4: Fixed confusing navigation icon */}
        <button
          className="hidden md:inline-flex items-center justify-center p-2 mr-3 text-gray-600 hover:text-gray-900 focus:outline-none focus-primary rounded-lg"
          onClick={onToggleSidebar}
          title={sidebarCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar'}
        >
          <i className={cn('bi text-lg', sidebarCollapsed ? 'bi-layout-sidebar-inset' : 'bi-layout-sidebar-inset-reverse')}></i>
        </button>

        {/* Logo - Only visible when sidebar is collapsed */}
        {sidebarCollapsed && (
          <div className="hidden md:flex items-center mr-4">
            <img
              src={preminfoLogo}
              alt="Preminfo Tech Logo"
              className="h-8 object-contain"
            />
          </div>
        )}

        {/* Page Title */}
        <h6 className="text-base sm:text-lg font-semibold text-gray-900 flex-1 min-w-0 truncate">{getPageTitle()}</h6>

        {/* Right Side Items */}
        <div className="flex items-center ml-auto min-w-0">
          {/* Notifications */}
          <div className="relative mr-2 sm:mr-3" ref={notificationsRef}>
            <button
              className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-lg"
              onClick={() => setNotificationsOpen(!notificationsOpen)}
            >
              <i className="bi bi-bell text-lg"></i>
              <span className="absolute -top-0.5 -right-0.5 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full min-w-[20px] h-5">
                3
              </span>
            </button>

            {notificationsOpen && (
              <div className="absolute right-0 mt-2 w-72 sm:w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-[9999] max-w-[calc(100vw-2rem)]">
                <div className="py-1">
                  <div className="px-4 py-2 text-sm font-semibold text-gray-900 border-b border-gray-200">
                    Notifications
                  </div>
                  <div className="px-4 py-3 hover:bg-gray-50">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <i className="bi bi-info-circle text-blue-500"></i>
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <h6 className="text-sm font-medium text-gray-900 mb-1 truncate">New customer registered</h6>
                        <p className="text-sm text-gray-600 mb-1 line-clamp-2">John Doe has registered as a new customer</p>
                        <small className="text-xs text-gray-500">2 minutes ago</small>
                      </div>
                    </div>
                  </div>
                  <div className="border-t border-gray-200"></div>
                  <div className="px-4 py-2 text-center">
                    <small className="text-sm text-gray-600">View all notifications</small>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* User Profile */}
          <div className="relative min-w-0 z-50" ref={userMenuRef}>
            <button
              className="flex items-center p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-lg min-w-0"
              onClick={() => setUserMenuOpen(!userMenuOpen)}
            >
              <div className="flex items-center min-w-0">
                <div className="mr-2 hidden sm:block text-right min-w-0 max-w-[120px]">
                  <div className="font-semibold text-gray-900 text-sm truncate">
                    {user ? `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email : 'User'}
                  </div>
                  <small className="text-gray-500 truncate block">
                    {user?.roles?.[0]?.name || 'User'}
                  </small>
                </div>
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0">
                  <i className="bi bi-person-fill text-white text-sm sm:text-base"></i>
                </div>
              </div>
            </button>

            {userMenuOpen && (
              <div className="user-menu-dropdown absolute right-0 mt-2 w-48 bg-white rounded-md shadow-xl ring-1 ring-black ring-opacity-5 z-[9999] overflow-visible">
                <div className="py-1">
                  {/* Profile button temporarily commented out due to 431 header size error */}
                  {/*
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleProfileClick}
                  >
                    <i className="bi bi-person mr-2"></i>
                    Profile
                  </button>
                  */}
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleSettingsClick}
                  >
                    <i className="bi bi-gear mr-2"></i>
                    Settings
                  </button>
                  <div className="border-t border-gray-100"></div>
                  <button
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    onClick={handleLogoutClick}
                  >
                    <i className="bi bi-box-arrow-right mr-2"></i>
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
