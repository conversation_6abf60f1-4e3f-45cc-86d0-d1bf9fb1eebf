# TallyCRM Deployment Guide

## Same-Origin Deployment (Recommended)

When deploying both frontend and backend on the same server/domain, you can disable CORS completely for better performance and security.

### Production Environment Variables

#### Backend (.env)
```env
# Disable CORS for same-origin deployment
ENABLE_CORS=false

# Disable rate limiting for internal requests
ENABLE_RATE_LIMITING=false

# Production settings
NODE_ENV=production
PORT=8000
APP_URL=https://yourdomain.com
```

#### Frontend (.env)
```env
# For same-origin deployment, don't set VITE_API_BASE_URL
# The system will automatically use relative paths

# Production settings
NODE_ENV=production
```

### Nginx Configuration Example

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    # Serve frontend static files
    location / {
        root /var/www/tallycrm/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # Proxy API requests to backend
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Cross-Origin Deployment

If you need to deploy frontend and backend on different domains:

#### Backend (.env)
```env
# Enable CORS for cross-origin deployment
ENABLE_CORS=true
CORS_ORIGINS=https://frontend.yourdomain.com,https://yourdomain.com

# Enable rate limiting for external requests
ENABLE_RATE_LIMITING=true
```

#### Frontend (.env)
```env
# Explicitly set backend URL
VITE_API_BASE_URL=https://api.yourdomain.com/api/v1
```

## Development Setup

The system automatically detects development environment and configures appropriate URLs:

- Frontend on port 3003 → Backend on port 8000
- Frontend on port 3000 → Backend on port 3001
- Frontend on port 5173 → Backend on port 8000

## Environment Variables Reference

### Backend
- `ENABLE_CORS`: true/false - Enable/disable CORS
- `ENABLE_RATE_LIMITING`: true/false - Enable/disable rate limiting
- `CORS_ORIGINS`: Comma-separated list of allowed origins
- `RATE_LIMIT_WINDOW_MS`: Rate limit window in milliseconds
- `RATE_LIMIT_MAX_REQUESTS`: Maximum requests per window

### Frontend
- `VITE_API_BASE_URL`: Backend API URL (optional, auto-detected if not set)
- `VITE_API_TIMEOUT`: API request timeout in milliseconds

## Benefits of Same-Origin Deployment

1. **No CORS Issues**: Eliminates all cross-origin request complications
2. **Better Security**: Reduces attack surface
3. **Improved Performance**: No preflight requests
4. **Simplified Configuration**: No need to specify URLs
5. **Single SSL Certificate**: One certificate covers both frontend and backend
