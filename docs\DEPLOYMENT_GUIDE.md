# Attendance Management System - Deployment Guide

## Overview

This guide covers the deployment of the Attendance Management System for the Prem Infotech application. The system includes backend APIs, frontend components, database migrations, and mobile-responsive interfaces.

## Prerequisites

### System Requirements

**Server Requirements:**
- Node.js 18+ 
- PostgreSQL 13+
- Redis 6+ (for caching and sessions)
- <PERSON>in<PERSON> (for reverse proxy)
- SSL certificate
- Minimum 4GB RAM, 2 CPU cores
- 50GB storage space

**Development Tools:**
- Git
- Docker & Docker Compose (optional)
- PM2 (for process management)

### Environment Setup

1. **Clone Repository:**
```bash
git clone https://github.com/preminfotech/attendance-system.git
cd attendance-system
```

2. **Install Dependencies:**
```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
```

## Database Setup

### PostgreSQL Configuration

1. **Create Database:**
```sql
CREATE DATABASE prem_infotech_attendance;
CREATE USER attendance_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE prem_infotech_attendance TO attendance_user;
```

2. **Run Migrations:**
```bash
cd backend
npm run migrate
```

3. **Seed Initial Data:**
```bash
npm run seed
```

### Database Migrations

The system includes the following new tables:
- `attendance_records`
- `attendance_policies`
- `attendance_settings`
- `leave_types`
- `leave_requests`
- `leave_balances`
- `salary_structures`
- `payroll_records`

## Backend Deployment

### Environment Configuration

Create `.env` file in the backend directory:

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=prem_infotech_attendance
DB_USER=attendance_user
DB_PASSWORD=secure_password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# JWT
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# File Upload
UPLOAD_PATH=/var/uploads
MAX_FILE_SIZE=10MB

# Attendance Settings
GPS_TRACKING_ENABLED=true
OFFICE_LATITUDE=12.9716
OFFICE_LONGITUDE=77.5946
OFFICE_RADIUS=100

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
ENABLE_PUSH_NOTIFICATIONS=true

# API Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Environment
NODE_ENV=production
PORT=3001
```

### Production Build

```bash
cd backend
npm run build
```

### Process Management with PM2

1. **Install PM2:**
```bash
npm install -g pm2
```

2. **Create PM2 Configuration:**
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'attendance-api',
    script: './dist/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

3. **Start Application:**
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Frontend Deployment

### Environment Configuration

Create `.env.production` file in the frontend directory:

```env
REACT_APP_API_URL=https://api.preminfotech.com/api/v1
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=1.0.0
REACT_APP_ENABLE_GPS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_SENTRY_DSN=your_sentry_dsn
```

### Production Build

```bash
cd frontend
npm run build
```

### Static File Serving

The build files should be served by Nginx:

```nginx
# /etc/nginx/sites-available/attendance
server {
    listen 80;
    server_name attendance.preminfotech.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name attendance.preminfotech.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    root /var/www/attendance/build;
    index index.html;

    # Frontend routes
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Docker Deployment

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: prem_infotech_attendance
      POSTGRES_USER: attendance_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    command: redis-server --requirepass redis_password
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    ports:
      - "3001:3001"
    volumes:
      - ./uploads:/var/uploads

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  postgres_data:
```

### Docker Build

```bash
# Build and start services
docker-compose up -d

# Run migrations
docker-compose exec backend npm run migrate

# View logs
docker-compose logs -f
```

## SSL Configuration

### Let's Encrypt Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d attendance.preminfotech.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### Application Monitoring

1. **PM2 Monitoring:**
```bash
pm2 monit
pm2 logs
```

2. **Log Rotation:**
```bash
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### Health Checks

Create health check endpoints:

```bash
# API Health Check
curl https://api.preminfotech.com/api/v1/health

# Database Health Check
curl https://api.preminfotech.com/api/v1/health/db
```

### Error Tracking

Configure Sentry for error tracking:

```javascript
// In your application
import * as Sentry from "@sentry/node";

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV
});
```

## Backup Strategy

### Database Backup

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/attendance"
DB_NAME="prem_infotech_attendance"

# Create backup
pg_dump $DB_NAME > $BACKUP_DIR/attendance_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/attendance_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
```

### Automated Backups

```bash
# Add to crontab
0 2 * * * /path/to/backup.sh
```

## Security Considerations

### Firewall Configuration

```bash
# UFW Configuration
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### Application Security

1. **Rate Limiting**: Implemented in the application
2. **CORS Configuration**: Properly configured for production
3. **Input Validation**: All inputs are validated
4. **SQL Injection Prevention**: Using parameterized queries
5. **XSS Protection**: Content Security Policy headers

### Environment Security

```bash
# Secure file permissions
chmod 600 .env
chown app:app .env

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

## Performance Optimization

### Database Optimization

```sql
-- Create indexes for better performance
CREATE INDEX idx_attendance_employee_date ON attendance_records(employee_id, date);
CREATE INDEX idx_leave_requests_status ON leave_requests(status);
CREATE INDEX idx_payroll_records_month_year ON payroll_records(month, year);
```

### Caching Strategy

- Redis for session storage
- API response caching
- Static asset caching via Nginx

### CDN Configuration

Configure CloudFlare or similar CDN for:
- Static asset delivery
- DDoS protection
- SSL termination

## Troubleshooting

### Common Issues

1. **Database Connection Issues:**
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

2. **Application Not Starting:**
```bash
# Check PM2 logs
pm2 logs attendance-api

# Check system resources
htop
df -h
```

3. **High Memory Usage:**
```bash
# Restart application
pm2 restart attendance-api

# Check for memory leaks
pm2 monit
```

## Maintenance

### Regular Maintenance Tasks

1. **Weekly:**
   - Check application logs
   - Monitor disk space
   - Review error rates

2. **Monthly:**
   - Update dependencies
   - Review security patches
   - Optimize database

3. **Quarterly:**
   - Full system backup test
   - Performance review
   - Security audit

### Update Procedure

```bash
# 1. Backup current version
git tag v1.0.0

# 2. Pull latest changes
git pull origin main

# 3. Install dependencies
npm install

# 4. Run migrations
npm run migrate

# 5. Build application
npm run build

# 6. Restart services
pm2 restart all

# 7. Verify deployment
curl https://api.preminfotech.com/api/v1/health
```

## Support and Documentation

- **Technical Documentation**: `/docs`
- **API Documentation**: `/docs/api`
- **User Guide**: `/docs/user-guide`
- **Support Email**: <EMAIL>
- **Emergency Contact**: +91-XXXXXXXXXX

For additional support or custom deployment requirements, contact the development team.
