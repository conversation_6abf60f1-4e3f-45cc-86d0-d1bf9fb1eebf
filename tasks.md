task1: remove this input Map Location(Optional)
Enter address or click map to select

Specify location for service delivery from create new service call form please

task2:
what are all the places have a search drop down in that place if i enter some input and select the data that data not showing in that input but fuctionality wise all are good but the data after selection not showing in that input eg: create new service call form customer name input liek this analy each and every input in the codebase



task3:
in the dashbord common search input searching only the customers not services like if i enter the govindaraji that's showing only the customer list or service list at a time what if user needs govindaraji customer and with there services, in that time the customer and services also should listing out here so you should show both and some time the user needs govindaraji customer, servides, executives also but here current scenariio is showing only one at a time i need all are


task4:
if the service 