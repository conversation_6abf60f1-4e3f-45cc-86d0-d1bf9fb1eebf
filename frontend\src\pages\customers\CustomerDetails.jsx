import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Helmet } from 'react-helmet-async';
import { apiService, customerAPI } from '../../services/api';
import { handleAuthError } from '../../utils/authErrorHandler';
import { Card, CardHeader, CardBody, Button, Badge, Spinner, Alert } from '../../components/ui';
import LoadingScreen from '../../components/ui/LoadingScreen';

const CustomerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(null);
  const [rawCustomerData, setRawCustomerData] = useState(null);
  const [customerStats, setCustomerStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [statsLoading, setStatsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [showLogDetails, setShowLogDetails] = useState(false);


  // Transform raw customer data to display format
  const transformCustomerData = (customerData) => {
    if (!customerData) return null;

    const customFields = customerData.custom_fields || {};

    return {
      id: customerData.id,
      name: customerData.company_name || customerData.display_name || 'N/A',
      contactPerson: customFields.address_book?.[0]?.contact_person || customerData.contact_person || 'N/A',
      designation: customFields.address_book?.[0]?.type || customerData.designation || 'N/A',
      email: customerData.email || customFields.address_book?.[0]?.email || 'N/A',
      phone: customerData.phone || customFields.address_book?.[0]?.mobile_numbers?.[0] || 'N/A',
      alternatePhone: customFields.address_book?.[0]?.phone || customerData.alternate_phone || 'N/A',
      address: customerData.address_line_1 || customerData.address || customFields.map_location || 'N/A',
      city: customerData.city || 'N/A',
      state: customerData.state || 'N/A',
      pincode: customerData.postal_code || customerData.pincode || 'N/A',
      country: customerData.country || 'India',
      businessType: customerData.business_type || 'N/A',
      industry: customerData.industry?.name || customerData.industry || 'N/A',
      gstNumber: customerData.gst_number || 'N/A',
      panNumber: customerData.pan_number || 'N/A',
      annualTurnover: customerData.annual_turnover ? `${customerData.annual_turnover} Lakhs` : 'N/A',
      employeeCount: customerData.employee_count || 'N/A',
      status: customerData.is_active ? 'active' : 'inactive',
      productType: customFields.product_name || 'N/A', // Product Type instead of Tally Version
      tallySerialNumber: customerData.tally_serial_number || customerData.customer_code || customFields.tally_serial_number || `AUTO-${customerData.id.slice(-8).toUpperCase()}`,
      licenseType: customFields.license_edition_id || customerData.license_type || 'N/A',
      installationDate: customerData.installation_date ? new Date(customerData.installation_date).toLocaleDateString() : 'N/A',
      registrationDate: customerData.created_at ? new Date(customerData.created_at).toLocaleDateString() : 'N/A',
      lastContact: customerData.updated_at ? new Date(customerData.updated_at).toLocaleDateString() : 'N/A',
      notes: customerData.notes || 'No notes available',

      // Additional fields from custom_fields
      profileStatus: customFields.profile_status || 'N/A',
      customerStatus: customerData.is_active ? 'active' : 'inactive',
      followUpExecutive: customFields.follow_up_executive_name || 'N/A',
      licenseEdition: customFields.license_edition_name || 'N/A',
      location: customFields.location_name || 'N/A',
      mapLocation: customFields.map_location || customerData.address_line_1 || 'N/A',
      latitude: customerData.latitude || 'N/A',
      longitude: customerData.longitude || 'N/A',
      remarks: customerData.notes || 'No remarks available',

      // Task 1: New contact information fields
      adminEmail: customFields.admin_email || 'N/A',
      mdContactPerson: customFields.md_contact_person || 'N/A',
      mdPhoneNo: customFields.md_phone_no || 'N/A',
      mdEmail: customFields.md_email || 'N/A',
      officeContactPerson: customFields.office_contact_person || 'N/A',
      officeMobileNo: customFields.office_mobile_no || 'N/A',
      officeEmail: customFields.office_email || 'N/A',
      auditorName: customFields.auditor_name || 'N/A',
      auditorNo: customFields.auditor_no || 'N/A',
      auditorEmail: customFields.auditor_email || 'N/A',
      taxConsultantName: customFields.tax_consultant_name || 'N/A',
      taxConsultantNo: customFields.tax_consultant_no || 'N/A',
      taxConsultantEmail: customFields.tax_consultant_email || 'N/A',
      itName: customFields.it_name || 'N/A',
      itNo: customFields.it_no || 'N/A',
      itEmail: customFields.it_email || 'N/A',
      area: customFields.area || 'N/A',
      pinCode: customFields.pin_code || 'N/A',
      stateCountry: customFields.state_country || 'N/A',
      noOfTallyUsers: customFields.no_of_tally_users || 'N/A',
      logDetails: customFields.log_details || 'N/A',
      executiveName: customFields.executive_name || 'N/A',
      customStatus: customFields.status || 'N/A',

      // Address Book
      addressBook: customFields.address_book || [],

      // TSS Status
      tssStatus: customFields.tss_status || 'NO',
      tssExpiryDate: customFields.tss_expiry_date || 'N/A',

      // AMC Status
      amcStatus: customFields.amc_status || 'NO',
      amcFromDate: customFields.amc_from_date || 'N/A',
      amcToDate: customFields.amc_to_date || 'N/A',
      renewalDate: customFields.renewal_date || 'N/A',
      noOfVisits: customFields.no_of_visits || 'N/A',
      currentAmcAmount: customFields.current_amc_amount || 'N/A',
      lastYearAmcAmount: customFields.last_year_amc_amount || 'N/A',


      // Additional Features with expiry dates
      tdlAddons: customFields.tdl_addons || false,
      tdlAddonsExpiryDate: customFields.tdl_addons_expiry_date || 'N/A',
      whatsappTelegramGroup: customFields.whatsapp_telegram_group || false,
      whatsappTelegramGroupExpiryDate: customFields.whatsapp_telegram_group_expiry_date || 'N/A',
      autoBackup: customFields.auto_backup || false,
      autoBackupExpiryDate: customFields.auto_backup_expiry_date || 'N/A',
      cloudUser: customFields.cloud_user || false,
      cloudUserExpiryDate: customFields.cloud_user_expiry_date || 'N/A',
      mobileApp: customFields.mobile_app || false,
      mobileAppExpiryDate: customFields.mobile_app_expiry_date || 'N/A',

      // Mock data for services and payments until we implement those APIs
      services: [],
      payments: [],

      // Real statistics (will be populated from API)
      stats: customerStats ? {
        totalServices: customerStats.summary?.totalServices || 0,
        completedServices: customerStats.summary?.completedServices || 0,
        pendingServices: customerStats.summary?.pendingServices || 0,
        inProgressServices: customerStats.summary?.inProgressServices || 0,
        totalRevenue: customerStats.summary?.totalRevenue || 0,
        pendingAmount: customerStats.summary?.pendingAmount || 0,
        lastServiceDate: customerStats.summary?.lastServiceDate || 'N/A'
      } : {
        totalServices: 0,
        completedServices: 0,
        pendingServices: 0,
        inProgressServices: 0,
        totalRevenue: 0,
        pendingAmount: 0,
        lastServiceDate: 'N/A'
      }
    };
  };

  // Fetch customer data from API (consolidated to reduce duplicate requests)
  useEffect(() => {
    if (id && !rawCustomerData) { // Only fetch if we don't already have customer data
      fetchCustomerData();
    }
  }, [id]); // Removed customer from dependencies to prevent infinite loops

  // Transform customer data when raw data or stats change
  useEffect(() => {
    if (rawCustomerData) {
      const transformed = transformCustomerData(rawCustomerData);
      setCustomer(transformed);
    }
  }, [rawCustomerData, customerStats]);


  // Consolidated fetch function to reduce duplicate requests
  const fetchCustomerData = async () => {
    try {
      setLoading(true);
      setStatsLoading(true);

      // Try to fetch both customer data and statistics in parallel
      const [customerResponse, statsResponse] = await Promise.allSettled([
        apiService.get(`/customers/${id}`),
        customerAPI.getStatistics(id)
      ]);

      // Handle customer data
      if (customerResponse.status === 'fulfilled' &&
          customerResponse.value.data?.success &&
          customerResponse.value.data?.data?.customer) {
        const customerData = customerResponse.value.data.data.customer;
        setRawCustomerData(customerData);
      } else {
        toast.error('Customer not found');
        navigate('/customers');
        return;
      }

      // Handle statistics data
      if (statsResponse.status === 'fulfilled' &&
          statsResponse.value.data?.success &&
          statsResponse.value.data?.data) {
        setCustomerStats(statsResponse.value.data.data);
        console.log('Customer statistics loaded:', statsResponse.value.data.data);
      } else {
        console.warn('No statistics data available for customer');
        // Don't show error toast for missing stats, just use defaults
      }

    } catch (error) {
      console.error('Error fetching customer data:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');

      // Handle authentication errors
      if (ErrorHandler.handleAuthError(error)) {
        return; // Auth error handled, component will unmount
      }

      ErrorHandler.showError(error, 'Unable to load customer information');
      navigate('/customers');
    } finally {
      setLoading(false);
      setStatsLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      try {
        const response = await apiService.delete(`/customers/${id}`);
        if (response.data?.success) {
          toast.success('Customer deleted successfully');
          navigate('/customers');
        } else {
          // Use the improved error message from the API response
          const errorMessage = response.data?.message || 'Failed to delete customer';
          toast.error(errorMessage);
        }
      } catch (error) {
        console.error('Error deleting customer:', error);

        // Import and use the ErrorHandler for better error parsing
        const { ErrorHandler } = await import('../../utils/errorUtils.js');
        ErrorHandler.showError(error, 'Failed to delete customer');
      }
    }
  };

  const getStatusBadge = (status) => {
    const normalizedStatus = status?.toLowerCase();
    return (
      <Badge
        style={{ backgroundColor: '#1d5795', color: 'white' }}
        className="border-0"
      >
        {status?.toUpperCase() || 'UNKNOWN'}
      </Badge>
    );
  };

  const getServiceStatusBadge = (status) => {
    return (
      <Badge
        style={{ backgroundColor: '#1d5795', color: 'white' }}
        className="border-0"
      >
        {status.toUpperCase()}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status) => {
    return (
      <Badge
        style={{ backgroundColor: '#1d5795', color: 'white' }}
        className="border-0"
      >
        {status.toUpperCase()}
      </Badge>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Customer Details..."
        subtitle="Fetching customer information and history"
        variant="page"
      />
    );
  }

  if (!customer) {
    return (
      <div className="w-full">
        <Alert variant="danger">
          Customer not found.
        </Alert>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{customer.name} - Customer Details - TallyCRM</title>
      </Helmet>

      <div className="w-full">
        {/* Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{customer.name}</h1>
              <p className="text-gray-600 mb-3">{customer.contactPerson} • {customer.designation}</p>
              <div className="flex flex-wrap items-center gap-3">
                {getStatusBadge(customer.customerStatus)}
                <Badge style={{ backgroundColor: '#1d5795', color: 'white' }} className="border-0">{customer.productType}</Badge>
                <span className="text-sm text-gray-600 flex items-center">
                  <i className="bi bi-calendar mr-1"></i>
                  Customer since {customer.registrationDate}
                </span>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={() => navigate(`/customers/${customer.id}/edit`)}
                className="flex items-center"
              >
                <i className="bi bi-pencil mr-2"></i>
                Edit
              </Button>
              <Button
                variant="danger"
                onClick={handleDelete}
                className="flex items-center"
              >
                <i className="bi bi-trash mr-2"></i>
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="stats-card-primary border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  {statsLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <>
                      <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.totalServices}</h3>
                      <p className="text-white text-sm">Total Services</p>
                    </>
                  )}
                </div>
                <i className="bi bi-tools text-2xl" style={{ color: 'rgba(var(--primary-text-rgb, 255, 255, 255), 0.8)' }}></i>
              </div>
            </CardBody>
          </Card>

          <Card style={{ backgroundColor: '#1d5795' }} className="text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  {statsLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <>
                      <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.totalRevenue.toLocaleString()}</h3>
                      <p className="text-white text-sm">Total Revenue</p>
                    </>
                  )}
                </div>
                <i className="bi bi-currency-rupee text-2xl text-white"></i>
              </div>
            </CardBody>
          </Card>

          <Card style={{ backgroundColor: '#1d5795' }} className="text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  {statsLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <>
                      <h3 className="text-2xl font-bold text-white mb-1">₹{customer.stats.pendingAmount.toLocaleString()}</h3>
                      <p className="text-white text-sm">Pending Amount</p>
                    </>
                  )}
                </div>
                <i className="bi bi-file-earmark-text text-2xl text-white"></i>
              </div>
            </CardBody>
          </Card>

          <Card style={{ backgroundColor: '#1d5795' }} className="text-white border-0 shadow-lg">
            <CardBody className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  {statsLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-2"></div>
                      <span className="text-sm">Loading...</span>
                    </div>
                  ) : (
                    <>
                      <h3 className="text-2xl font-bold text-white mb-1">{customer.stats.pendingServices}</h3>
                      <p className="text-white text-sm">Pending Services</p>
                    </>
                  )}
                </div>
                <i className="bi bi-graph-up text-2xl text-white"></i>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'services'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('services')}
              >
                Services ({customer.services.length})
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'payments'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('payments')}
              >
                Payments ({customer.payments.length})
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Information Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Tally Information */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-calculator mr-2"></i>
                    Tally Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Tally Serial Number:</label>
                      <p className="text-gray-900 font-mono">{customer.tallySerialNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">License Edition:</label>
                      <p className="text-gray-900">{customer.licenseEdition}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Product Type:</label>
                      <p className="text-gray-900">{customer.productType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Installation Date:</label>
                      <p className="text-gray-900">{customer.installationDate}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Profile Information */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-person-badge mr-2"></i>
                    Profile Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Profile Status:</label>
                      <p className="text-gray-900">{customer.profileStatus}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Customer Status:</label>
                      <div className="mt-1">
                        {getStatusBadge(customer.customerStatus)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Follow-up Executive:</label>
                      <p className="text-gray-900">{customer.followUpExecutive}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Location:</label>
                      <p className="text-gray-900">{customer.location}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Company Information Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Company Information */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-building mr-2"></i>
                    Company Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Business Type:</label>
                      <p className="text-gray-900">{customer.businessType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Industry:</label>
                      <p className="text-gray-900">{customer.industry}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">GST Number:</label>
                      <p className="text-gray-900">{customer.gstNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">PAN Number:</label>
                      <p className="text-gray-900">{customer.panNumber}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Annual Turnover:</label>
                      <p className="text-gray-900">{customer.annualTurnover}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Employee Count:</label>
                      <p className="text-gray-900">{customer.employeeCount}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Map Location */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-geo-alt mr-2"></i>
                    Location Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Map Location:</label>
                      <p className="text-gray-900">{customer.mapLocation}</p>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Latitude:</label>
                        <p className="text-gray-900 font-mono">{customer.latitude}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Longitude:</label>
                        <p className="text-gray-900 font-mono">{customer.longitude}</p>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Full Address:</label>
                      <p className="text-gray-900">
                        {customer.address}<br />
                        {customer.city}, {customer.state} - {customer.pincode}<br />
                        {customer.country}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Contact Address Book */}
            <Card>
              <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                <h5 className="text-lg font-semibold text-white flex items-center">
                  <i className="bi bi-person-lines-fill mr-2"></i>
                  Contact Address Book
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                {customer.addressBook && customer.addressBook.length > 0 ? (
                  <div className="space-y-4">
                    {customer.addressBook.map((contact, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">Type:</label>
                            <p className="text-gray-900 font-semibold">{contact.type || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Contact Person:</label>
                            <p className="text-gray-900">{contact.contact_person || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Phone:</label>
                            <p className="text-gray-900">{contact.phone || 'N/A'}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Email:</label>
                            <p className="text-gray-900">{contact.email || 'N/A'}</p>
                          </div>
                          {contact.mobile_numbers && contact.mobile_numbers.length > 0 && (
                            <div className="md:col-span-2 lg:col-span-4">
                              <label className="text-sm font-medium text-gray-700">Mobile Numbers:</label>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {contact.mobile_numbers.map((mobile, mobileIndex) => (
                                  <span key={mobileIndex} style={{ backgroundColor: '#1d5795' }} className="text-white px-2 py-1 rounded text-sm">
                                    {mobile}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No contact information available.</p>
                )}
              </CardBody>
            </Card>

            {/* Task 1: New Contact Information Sections */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Professional Contacts */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-person-workspace mr-2"></i>
                    Professional Contacts
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Admin Email:</label>
                        <p className="text-gray-900">{customer.adminEmail}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">MD Contact Person:</label>
                        <p className="text-gray-900">{customer.mdContactPerson}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">MD Phone No:</label>
                        <p className="text-gray-900">{customer.mdPhoneNo}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">MD Email:</label>
                        <p className="text-gray-900">{customer.mdEmail}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Office Contact Person:</label>
                        <p className="text-gray-900">{customer.officeContactPerson}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Office Mobile No:</label>
                        <p className="text-gray-900">{customer.officeMobileNo}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Office Email:</label>
                        <p className="text-gray-900">{customer.officeEmail}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Executive Name:</label>
                        <p className="text-gray-900">{customer.executiveName}</p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>

              {/* Consultant Information */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-person-badge mr-2"></i>
                    Consultant Information
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Auditor Name:</label>
                        <p className="text-gray-900">{customer.auditorName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Auditor No:</label>
                        <p className="text-gray-900">{customer.auditorNo}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Auditor Email:</label>
                        <p className="text-gray-900">{customer.auditorEmail}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Tax Consultant Name:</label>
                        <p className="text-gray-900">{customer.taxConsultantName}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Tax Consultant No:</label>
                        <p className="text-gray-900">{customer.taxConsultantNo}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Tax Consultant Email:</label>
                        <p className="text-gray-900">{customer.taxConsultantEmail}</p>
                      </div>
                      {customer.itName !== 'N/A' && (
                        <>
                          <div>
                            <label className="text-sm font-medium text-gray-700">IT Name:</label>
                            <p className="text-gray-900">{customer.itName}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">IT No:</label>
                            <p className="text-gray-900">{customer.itNo}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">IT Email:</label>
                            <p className="text-gray-900">{customer.itEmail}</p>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Additional Information */}
            <Card>
              <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                <h5 className="text-lg font-semibold text-white flex items-center">
                  <i className="bi bi-info-circle mr-2"></i>
                  Additional Information
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Area:</label>
                    <p className="text-gray-900">{customer.area}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">PIN Code:</label>
                    <p className="text-gray-900">{customer.pinCode}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">State Country:</label>
                    <p className="text-gray-900">{customer.stateCountry}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">No. of Tally Users:</label>
                    <p className="text-gray-900">{customer.noOfTallyUsers}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Status:</label>
                    <p className="text-gray-900">{customer.customStatus}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Log Details:</label>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-900">Available</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowLogDetails(true)}
                        className="text-xs"
                      >
                        View Log Details
                      </Button>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* TSS and AMC Status Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* TSS Status */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-shield-check mr-2"></i>
                    TSS Status
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">TSS Status:</label>
                      <div className="mt-1">
                        <Badge style={{ backgroundColor: '#1d5795', color: 'white' }} className="border-0">
                          {customer.tssStatus === 'YES' ? '✅ Active' : '❌ Inactive'}
                        </Badge>
                      </div>
                    </div>
                    {customer.tssStatus === 'YES' && (
                      <>
                        <div>
                          <label className="text-sm font-medium text-gray-700">TSS Expiry Date:</label>
                          <p className="text-gray-900">{customer.tssExpiryDate}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">Admin Email:</label>
                          <p className="text-gray-900">{customer.adminEmail}</p>
                        </div>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>

              {/* AMC Status */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-calendar-check mr-2"></i>
                    AMC Status
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">AMC Status:</label>
                      <div className="mt-1">
                        <Badge style={{ backgroundColor: '#1d5795', color: 'white' }} className="border-0">
                          {customer.amcStatus === 'YES' ? '✅ Active' : '❌ Inactive'}
                        </Badge>
                      </div>
                    </div>
                    {customer.amcStatus === 'YES' && (
                      <>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">From Date:</label>
                            <p className="text-gray-900">{customer.amcFromDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">To Date:</label>
                            <p className="text-gray-900">{customer.amcToDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Renewal Date:</label>
                            <p className="text-gray-900">{customer.renewalDate}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">No of Visits:</label>
                            <p className="text-gray-900">{customer.noOfVisits}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Current AMC Amount:</label>
                            <p className="text-gray-900">₹{customer.currentAmcAmount}</p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">Last Year AMC Amount:</label>
                            <p className="text-gray-900">₹{customer.lastYearAmcAmount}</p>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Additional Features */}
            <div className="grid grid-cols-1 gap-6">
              {/* Additional Features */}
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-star mr-2"></i>
                    Additional Features
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  {(() => {
                    // Helper function to check if date is expired
                    const isExpired = (dateString) => {
                      if (!dateString || dateString === 'N/A') return false;
                      const expiryDate = new Date(dateString);
                      const today = new Date();
                      return expiryDate < today;
                    };

                    // Helper function to render feature item
                    const renderFeature = (name, isActive, expiryDate) => {
                      if (!isActive) return null; // Don't show inactive features

                      const expired = isExpired(expiryDate);

                      return (
                        <div key={name} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Badge style={{ backgroundColor: '#1d5795', color: 'white' }} className="mr-2 border-0">
                              ✅
                            </Badge>
                            <span className="text-gray-900">{name}</span>
                          </div>
                          {expiryDate && expiryDate !== 'N/A' && (
                            <span className={`text-sm ${expired ? 'text-black font-semibold' : 'text-gray-600'}`}>
                              {expired ? '🔴 Expired: ' : 'Expires: '}{expiryDate}
                            </span>
                          )}
                        </div>
                      );
                    };

                    // Collect all active features
                    const activeFeatures = [
                      customer.tdlAddons && renderFeature('TDL & Addons', customer.tdlAddons, customer.tdlAddonsExpiryDate),
                      customer.whatsappTelegramGroup && renderFeature('WhatsApp/Telegram Group', customer.whatsappTelegramGroup, customer.whatsappTelegramGroupExpiryDate),
                      customer.autoBackup && renderFeature('Auto Backup', customer.autoBackup, customer.autoBackupExpiryDate),
                      customer.cloudUser && renderFeature('Cloud User', customer.cloudUser, customer.cloudUserExpiryDate),
                      customer.mobileApp && renderFeature('Mobile App', customer.mobileApp, customer.mobileAppExpiryDate),
                    ].filter(Boolean); // Remove null values

                    return activeFeatures.length > 0 ? (
                      <div className="space-y-3">
                        {activeFeatures}
                      </div>
                    ) : (
                      <p className="text-gray-500">No additional features activated.</p>
                    );
                  })()}
                </CardBody>
              </Card>
            </div>

            {/* Remarks */}
            <Card>
              <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                <h5 className="text-lg font-semibold text-white flex items-center">
                  <i className="bi bi-chat-text mr-2"></i>
                  Remarks
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <p className="text-gray-900 whitespace-pre-wrap">{customer.remarks}</p>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Services Tab */}
        {activeTab === 'services' && (
          <div className="space-y-6">
            {/* Service Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Total Services</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : customer.stats.totalServices}
                      </p>
                    </div>
                    <i className="bi bi-tools text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Completed</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : customer.stats.completedServices}
                      </p>
                    </div>
                    <i className="bi bi-check-circle text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Pending</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : customer.stats.pendingServices}
                      </p>
                    </div>
                    <i className="bi bi-clock text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>In Progress</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : customer.stats.inProgressServices}
                      </p>
                    </div>
                    <i className="bi bi-arrow-repeat text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Service Status Breakdown */}
            {customerStats?.services && (
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-bar-chart mr-2"></i>
                    Service Status Breakdown
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.completed}</p>
                      <p className="text-sm text-gray-600">Completed</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.pending}</p>
                      <p className="text-sm text-gray-600">Pending</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.in_progress}</p>
                      <p className="text-sm text-gray-600">In Progress</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.cancelled}</p>
                      <p className="text-sm text-gray-600">Cancelled</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.on_hold}</p>
                      <p className="text-sm text-gray-600">On Hold</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-black">{customerStats.services.statusBreakdown.others}</p>
                      <p className="text-sm text-gray-600">Others</p>
                    </div>
                  </div>

                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Last Service Date</p>
                        <p className="font-semibold">{customerStats.services.lastServiceDate}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Hours Spent</p>
                        <p className="font-semibold">{customerStats.services.totalHoursSpent}h</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Time (Minutes)</p>
                        <p className="font-semibold">{customerStats.services.totalTimeMinutes} min</p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Service History Table Placeholder */}
            <Card>
              <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                <h5 className="text-lg font-semibold text-white flex items-center">
                  <i className="bi bi-list-ul mr-2"></i>
                  Recent Service Calls
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="text-center py-8">
                  <i className="bi bi-tools text-4xl text-gray-300 mb-4"></i>
                  <p className="text-gray-500 mb-2">Service call history will be displayed here</p>
                  <p className="text-sm text-gray-400">Feature coming soon</p>
                </div>
              </CardBody>
            </Card>
          </div>
        )}

        {/* Payments Tab */}
        {activeTab === 'payments' && (
          <div className="space-y-6">
            {/* Payment Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Total Revenue</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : `₹${customer.stats.totalRevenue.toLocaleString()}`}
                      </p>
                    </div>
                    <i className="bi bi-currency-rupee text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Total Paid</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : `₹${(customerStats?.payments?.totalPaid || 0).toLocaleString()}`}
                      </p>
                    </div>
                    <i className="bi bi-check-circle text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Pending Amount</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : `₹${customer.stats.pendingAmount.toLocaleString()}`}
                      </p>
                    </div>
                    <i className="bi bi-exclamation-triangle text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>

              <Card className="border-gray-200" style={{ backgroundColor: 'rgba(47, 105, 179, 0.1)', borderColor: '#2f69b3' }}>
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium" style={{ color: '#2f69b3' }}>Avg Service Value</p>
                      <p className="text-2xl font-bold text-black">
                        {statsLoading ? '...' : `₹${(customerStats?.payments?.averageServiceValue || 0).toLocaleString()}`}
                      </p>
                    </div>
                    <i className="bi bi-graph-up text-2xl" style={{ color: '#2f69b3' }}></i>
                  </div>
                </CardBody>
              </Card>
            </div>

            {/* Payment Summary */}
            {customerStats?.payments && (
              <Card>
                <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                  <h5 className="text-lg font-semibold text-white flex items-center">
                    <i className="bi bi-credit-card mr-2"></i>
                    Payment Summary
                  </h5>
                </CardHeader>
                <CardBody className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="text-center p-4 bg-white border rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">Total Revenue</p>
                      <p className="text-xl font-bold text-black">₹{customerStats.payments.totalRevenue.toLocaleString()}</p>
                    </div>
                    <div className="text-center p-4 bg-white border rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">Amount Paid</p>
                      <p className="text-xl font-bold text-black">₹{customerStats.payments.totalPaid.toLocaleString()}</p>
                    </div>
                    <div className="text-center p-4 bg-white border rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">Outstanding</p>
                      <p className="text-xl font-bold text-black">₹{customerStats.payments.pendingAmount.toLocaleString()}</p>
                    </div>
                    <div className="text-center p-4 bg-white border rounded-lg">
                      <p className="text-sm text-gray-600 mb-1">Average Value</p>
                      <p className="text-xl font-bold text-black">₹{customerStats.payments.averageServiceValue.toLocaleString()}</p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Payment History Table Placeholder */}
            <Card>
              <CardHeader style={{ backgroundColor: '#1d5795' }} className="border-b">
                <h5 className="text-lg font-semibold text-white flex items-center">
                  <i className="bi bi-receipt mr-2"></i>
                  Payment History
                </h5>
              </CardHeader>
              <CardBody className="p-6">
                <div className="text-center py-8">
                  <i className="bi bi-credit-card text-4xl text-gray-300 mb-4"></i>
                  <p className="text-gray-500 mb-2">Payment history will be displayed here</p>
                  <p className="text-sm text-gray-400">Feature coming soon</p>
                </div>
              </CardBody>
            </Card>
          </div>
        )}
      </div>

      {/* Log Details Modal */}
      {showLogDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-96 overflow-hidden">
            <div className="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Log Details</h3>
              <button
                onClick={() => setShowLogDetails(false)}
                className="text-gray-400 hover:text-gray-600 text-xl font-bold"
              >
                ×
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-80">
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                  {customer.logDetails}
                </pre>
              </div>
            </div>
            <div className="bg-gray-50 px-6 py-3 flex justify-end">
              <button
                onClick={() => setShowLogDetails(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CustomerDetails;
