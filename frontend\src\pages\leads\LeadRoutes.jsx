import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import LeadList from './LeadList';
import LeadForm from './LeadForm';
import LeadDetails from './LeadDetails';

const LeadRoutes = () => {
  return (
    <Routes>
      <Route index element={<LeadList />} />
      <Route path="add" element={<LeadForm />} />
      <Route path=":id" element={<LeadDetails />} />
      <Route path=":id/edit" element={<LeadForm />} />
      <Route path="*" element={<Navigate to="/leads" replace />} />
    </Routes>
  );
};

export default LeadRoutes;
