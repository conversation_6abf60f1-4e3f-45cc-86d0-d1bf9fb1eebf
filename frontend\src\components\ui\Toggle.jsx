import React from 'react';
import { cn } from '../../utils/helpers';

const Toggle = React.forwardRef(({
  checked = false,
  onChange,
  disabled = false,
  size = 'md',
  label,
  description,
  className,
  id,
  ...props
}, ref) => {
  const sizes = {
    sm: {
      switch: 'w-8 h-4',
      slider: 'h-3 w-3 left-0.5 bottom-0.5',
      translate: 'translate-x-4'
    },
    md: {
      switch: 'w-11 h-6',
      slider: 'h-4 w-4 left-1 bottom-1',
      translate: 'translate-x-5'
    },
    lg: {
      switch: 'w-14 h-8',
      slider: 'h-6 w-6 left-1 bottom-1',
      translate: 'translate-x-6'
    }
  };

  const currentSize = sizes[size];

  return (
    <div className={cn('flex items-center', className)}>
      <div className="relative">
        <input
          ref={ref}
          type="checkbox"
          id={id}
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          className="sr-only"
          {...props}
        />
        <div
          className={cn(
            'relative inline-flex items-center cursor-pointer rounded-full transition-colors duration-300 ease-in-out',
            currentSize.switch,
            checked
              ? 'bg-primary-dynamic'
              : 'bg-gray-300',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          style={checked ? { backgroundColor: 'var(--primary-color)' } : {}}
          onClick={() => !disabled && onChange && onChange({ target: { checked: !checked } })}
        >
          <span
            className={cn(
              'inline-block bg-white rounded-full shadow-lg transform transition-transform duration-300 ease-in-out',
              currentSize.slider,
              checked ? currentSize.translate : 'translate-x-0'
            )}
          />
        </div>
      </div>

      {(label || description) && (
        <div className="ml-3">
          {label && (
            <label
              htmlFor={id}
              className={cn(
                'text-sm font-medium cursor-pointer',
                disabled ? 'text-gray-400' : 'text-gray-900'
              )}
            >
              {label}
            </label>
          )}
          {description && (
            <p className={cn(
              'text-xs',
              disabled ? 'text-gray-300' : 'text-gray-500'
            )}
            >
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
});

Toggle.displayName = 'Toggle';

export { Toggle };
export default Toggle;
