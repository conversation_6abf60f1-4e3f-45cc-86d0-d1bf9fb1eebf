# Attendance Management API Documentation

## Overview

The Attendance Management System provides comprehensive APIs for tracking employee attendance, managing leave requests, processing payroll, and generating reports. This documentation covers all available endpoints, request/response formats, and authentication requirements.

## Base URL

```
Production: https://api.preminfotech.com/api/v1
Development: http://localhost:3001/api/v1
```

## Authentication

All API endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer <your-jwt-token>
```

## Attendance Management

### Check In

**POST** `/attendance/check-in`

Records employee check-in with optional GPS location.

**Request Body:**
```json
{
  "location": {
    "latitude": 12.9716,
    "longitude": 77.5946,
    "accuracy": 10,
    "timestamp": "2024-01-01T09:00:00Z"
  },
  "notes": "Regular check-in",
  "device_info": {
    "userAgent": "Mozilla/5.0...",
    "platform": "Win32"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checked in successfully",
  "data": {
    "attendanceRecord": {
      "id": "uuid",
      "employee_id": "uuid",
      "date": "2024-01-01",
      "check_in_time": "2024-01-01T09:00:00Z",
      "status": "present",
      "location": {
        "latitude": 12.9716,
        "longitude": 77.5946
      }
    }
  }
}
```

### Check Out

**POST** `/attendance/check-out`

Records employee check-out and calculates total hours.

**Request Body:**
```json
{
  "location": {
    "latitude": 12.9716,
    "longitude": 77.5946,
    "accuracy": 10
  },
  "notes": "End of day"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checked out successfully",
  "data": {
    "attendanceRecord": {
      "id": "uuid",
      "check_out_time": "2024-01-01T18:00:00Z",
      "total_hours": 8.0,
      "overtime_hours": 0.0,
      "status": "present"
    }
  }
}
```

### Get Today's Attendance

**GET** `/attendance/today`

Retrieves current day's attendance status for the authenticated user.

**Response:**
```json
{
  "success": true,
  "data": {
    "hasCheckedIn": true,
    "hasCheckedOut": false,
    "attendanceRecord": {
      "id": "uuid",
      "date": "2024-01-01",
      "check_in_time": "2024-01-01T09:00:00Z",
      "check_out_time": null,
      "status": "present",
      "total_hours": 0.0
    },
    "currentHours": 4.5
  }
}
```

### Get Attendance Records

**GET** `/attendance/records`

Retrieves attendance records with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Records per page (default: 20)
- `start_date` (string): Start date filter (YYYY-MM-DD)
- `end_date` (string): End date filter (YYYY-MM-DD)
- `status` (string): Status filter (present, absent, late, half_day)
- `employee_id` (string): Employee ID filter (admin/manager only)

**Response:**
```json
{
  "success": true,
  "data": {
    "attendanceRecords": [
      {
        "id": "uuid",
        "date": "2024-01-01",
        "check_in_time": "2024-01-01T09:00:00Z",
        "check_out_time": "2024-01-01T18:00:00Z",
        "total_hours": 8.0,
        "status": "present",
        "employee": {
          "first_name": "John",
          "last_name": "Doe"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### Manual Attendance Entry

**POST** `/attendance/manual-entry`

Creates manual attendance entry (requires manager/admin permissions).

**Request Body:**
```json
{
  "employee_id": "uuid",
  "date": "2024-01-01",
  "check_in_time": "2024-01-01T09:00:00Z",
  "check_out_time": "2024-01-01T18:00:00Z",
  "status": "present",
  "reason": "System was down",
  "approved_by": "uuid"
}
```

## Leave Management

### Get Leave Types

**GET** `/leaves/types`

Retrieves available leave types for the tenant.

**Response:**
```json
{
  "success": true,
  "data": {
    "leaveTypes": [
      {
        "id": "uuid",
        "name": "Annual Leave",
        "code": "AL",
        "annual_quota": 20,
        "is_paid": true,
        "requires_approval": true,
        "allow_half_day": true,
        "color_code": "#3B82F6"
      }
    ]
  }
}
```

### Submit Leave Request

**POST** `/leaves/request`

Submits a new leave request.

**Request Body:**
```json
{
  "leave_type_id": "uuid",
  "start_date": "2024-12-25",
  "end_date": "2024-12-27",
  "total_days": 3,
  "is_half_day": false,
  "reason": "Christmas vacation",
  "emergency_contact": "+1234567890",
  "priority": "normal"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Leave request submitted successfully",
  "data": {
    "leaveRequest": {
      "id": "uuid",
      "request_number": "LR-2024-001",
      "status": "pending",
      "start_date": "2024-12-25",
      "end_date": "2024-12-27",
      "total_days": 3
    }
  }
}
```

### Get Leave Requests

**GET** `/leaves/requests`

Retrieves leave requests with filtering options.

**Query Parameters:**
- `status` (string): Filter by status (pending, approved, rejected)
- `employee_id` (string): Filter by employee (admin/manager only)
- `year` (number): Filter by year
- `page`, `limit`: Pagination

### Approve/Reject Leave

**PUT** `/leaves/requests/:id/approve`
**PUT** `/leaves/requests/:id/reject`

Approves or rejects a leave request (requires manager/HR permissions).

**Request Body:**
```json
{
  "manager_comments": "Approved for Christmas vacation",
  "rejection_reason": "Insufficient notice period" // For reject only
}
```

### Get Leave Balance

**GET** `/leaves/balance`

Retrieves leave balance for the authenticated user or specified employee.

**Query Parameters:**
- `year` (number): Year for balance calculation
- `employee_id` (string): Employee ID (admin/manager only)

**Response:**
```json
{
  "success": true,
  "data": {
    "leaveBalances": [
      {
        "id": "uuid",
        "leaveType": {
          "name": "Annual Leave",
          "code": "AL"
        },
        "allocated_days": 20,
        "used_days": 5,
        "remaining_days": 15,
        "carry_forward_days": 2
      }
    ]
  }
}
```

## Payroll Management

### Get Salary Structure

**GET** `/payroll/salary-structure`

Retrieves salary structure for an employee.

**Query Parameters:**
- `employee_id` (string): Employee ID

**Response:**
```json
{
  "success": true,
  "data": {
    "salaryStructure": {
      "basic_salary": 50000,
      "hra": 15000,
      "transport_allowance": 2000,
      "medical_allowance": 1500,
      "special_allowance": 5000,
      "pf_employee_percentage": 12,
      "esi_employee_percentage": 0.75
    }
  }
}
```

### Process Payroll

**POST** `/payroll/process`

Processes payroll for specified month and employees.

**Request Body:**
```json
{
  "month": 1,
  "year": 2024,
  "employee_ids": ["uuid1", "uuid2"] // Optional, processes all if not specified
}
```

### Get Payroll Records

**GET** `/payroll/records`

Retrieves payroll records with filtering.

**Query Parameters:**
- `month`, `year`: Filter by month/year
- `status`: Filter by status (draft, calculated, approved, paid)
- `employee_id`: Filter by employee

## Reports and Analytics

### Attendance Analytics

**GET** `/reports/attendance-analytics`

Generates attendance analytics and reports.

**Query Parameters:**
- `analytics_type`: Type of analytics (overview, trends, department)
- `start_date`, `end_date`: Date range
- `department`: Department filter
- `employee_id`: Employee filter

**Response:**
```json
{
  "success": true,
  "data": {
    "total_records": 1000,
    "attendance_percentage": 95.5,
    "average_hours_per_day": 8.2,
    "late_arrivals": 25,
    "status_breakdown": {
      "present": 900,
      "late": 50,
      "absent": 30,
      "half_day": 20
    },
    "daily_trends": [
      {
        "date": "2024-01-01",
        "attendance_rate": 95.0,
        "average_hours": 8.1
      }
    ]
  }
}
```

### Leave Analytics

**GET** `/reports/leave-analytics`

Generates leave analytics and utilization reports.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "code": "ERROR_CODE"
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `DUPLICATE_ENTRY`: Duplicate record
- `BUSINESS_RULE_VIOLATION`: Business logic violation

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- 100 requests per minute for regular endpoints
- 10 requests per minute for report generation
- 5 requests per minute for payroll processing

## Webhooks

The system supports webhooks for real-time notifications:

### Available Events
- `attendance.checked_in`
- `attendance.checked_out`
- `leave.requested`
- `leave.approved`
- `leave.rejected`
- `payroll.processed`

### Webhook Payload
```json
{
  "event": "attendance.checked_in",
  "timestamp": "2024-01-01T09:00:00Z",
  "tenant_id": "uuid",
  "data": {
    "employee_id": "uuid",
    "attendance_record": { /* attendance record object */ }
  }
}
```

## SDK and Libraries

Official SDKs are available for:
- JavaScript/Node.js
- Python
- PHP
- Java

## Support

For API support and questions:
- Email: <EMAIL>
- Documentation: https://docs.preminfotech.com/attendance
- Status Page: https://status.preminfotech.com
