import React, { useState, useEffect, useRef } from 'react';
import { FaSearch, FaChevronDown, FaTimes } from 'react-icons/fa';

const SearchableSelect = ({
  options = [],
  value,
  onChange,
  placeholder = 'Search...',
  searchFields = ['name'],
  displayField = 'name',
  valueField = 'id',
  className = '',
  error = false,
  disabled = false,
  minSearchLength = 2,
  maxResults = 10,
  renderOption = null,
  renderSelected = null,
  allowClear = true,
  noResultsText = 'No results found',
  searchingText = 'Type to search...',
  showCreateOption = false,
  onCreateNew = null,
  createNewText = 'Create new',
  // New props for server-side search
  onSearch = null, // Function to call for server-side search
  isSearching = false, // Loading state for server-side search
  searchResults = null, // Results from server-side search (overrides local filtering)
  onSearchReset = null, // Function to reset search results
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredOptions, setFilteredOptions] = useState([]);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [selectedOption, setSelectedOption] = useState(null);

  const containerRef = useRef(null);
  const inputRef = useRef(null);
  const listRef = useRef(null);

  // Ensure options is always an array
  const safeOptions = Array.isArray(options) ? options : [];

  // Filter options based on search term (client-side or server-side)
  // Track last searched term to prevent duplicate API calls
  const lastSearchedTermRef = useRef('');

  // Update selected option when value or options change
  useEffect(() => {
    if (!value) {
      setSelectedOption(null);
      return;
    }

    // First try to find in current options array
    let foundOption = safeOptions.find(option => option[valueField] === value);

    // If not found in current options but we have a value, keep the previous selectedOption
    // This handles the case where search results don't include the selected item
    if (!foundOption && selectedOption && selectedOption[valueField] === value) {
      // Keep the existing selectedOption
      return;
    }

    // If we found a new option or need to clear, update it
    setSelectedOption(foundOption || null);
  }, [value, safeOptions, valueField, selectedOption]);

  useEffect(() => {
    if (!searchTerm || searchTerm.length < minSearchLength) {
      setFilteredOptions([]);
      lastSearchedTermRef.current = '';
      return;
    }

    // If server-side search is enabled, trigger search only if term changed
    if (onSearch) {
      // Only trigger search if the search term actually changed
      if (searchTerm !== lastSearchedTermRef.current) {
        console.log(`🔍 SearchableSelect: Triggering search for "${searchTerm}" (was "${lastSearchedTermRef.current}")`);
        lastSearchedTermRef.current = searchTerm;
        onSearch(searchTerm);
      } else {
        console.log(`🔍 SearchableSelect: Skipping duplicate search for "${searchTerm}"`);
      }
      return;
    }

    // Fallback to client-side filtering
    const filtered = safeOptions.filter(option => {
      return searchFields.some(field => {
        const fieldValue = option[field];
        if (!fieldValue) return false;
        const searchableValue = safeRenderField(fieldValue);
        return searchableValue.toLowerCase().includes(searchTerm.toLowerCase());
      });
    }).slice(0, maxResults);

    setFilteredOptions(filtered);
    setHighlightedIndex(-1);
  }, [searchTerm, safeOptions, searchFields, minSearchLength, maxResults, onSearch]);

  // Handle search results updates (separate from search triggering)
  useEffect(() => {
    if (onSearch && searchResults !== null && searchTerm && searchTerm.length >= minSearchLength) {
      console.log(`🔍 SearchableSelect: Updating filtered options with ${Array.isArray(searchResults) ? searchResults.length : 0} results for "${searchTerm}"`);
      setFilteredOptions(Array.isArray(searchResults) ? searchResults.slice(0, maxResults) : []);
      setHighlightedIndex(-1);
    }
  }, [searchResults, maxResults, onSearch, searchTerm, minSearchLength]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
        // Reset server-side search results when clicking outside
        if (onSearchReset) {
          onSearchReset();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onSearchReset]);

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === 'ArrowDown') {
        setIsOpen(true);
        e.preventDefault();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          (prev < filteredOptions.length - 1 ? prev + 1 : prev)
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => (prev > 0 ? prev - 1 : -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredOptions[highlightedIndex]) {
          handleSelect(filteredOptions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        // Reset server-side search results when pressing Escape
        if (onSearchReset) {
          onSearchReset();
        }
        inputRef.current?.blur();
        break;
    }
  };

  // Handle option selection
  const handleSelect = (option) => {
    if (option && typeof onChange === 'function') {
      setSelectedOption(option); // Set the selected option immediately
      onChange(option[valueField]);
    }
    setIsOpen(false);
    setSearchTerm('');
    inputRef.current?.blur();
  };

  // Handle clear selection
  const handleClear = (e) => {
    e.stopPropagation();
    setSelectedOption(null); // Clear the selected option immediately
    if (typeof onChange === 'function') {
      onChange(null);
    }
    setSearchTerm('');
    // Reset server-side search results when clearing
    if (onSearchReset) {
      onSearchReset();
    }
    inputRef.current?.focus();
  };

  // Handle input focus
  const handleFocus = () => {
    setIsOpen(true);
    if (selectedOption && !searchTerm) {
      setSearchTerm('');
    }
  };

  // Handle input change
  const handleInputChange = (e) => {
    setSearchTerm(e.target.value);
    if (!isOpen) {
      setIsOpen(true);
    }
  };

  // Helper function to safely render field values
  const safeRenderField = (value) => {
    if (value === null || value === undefined) return '';
    if (typeof value === 'object') {
      // If it's an object, try to extract a meaningful string
      return value.name || value.label || value.title || JSON.stringify(value);
    }
    return String(value);
  };

  // Default option renderer
  const defaultRenderOption = (option, isHighlighted) => {
    if (!option) return null;

    const displayValue = safeRenderField(option[displayField]);
    const descriptionValue = safeRenderField(option.description);
    const categoryValue = safeRenderField(option.category);

    return (
      <div className={`px-4 py-3 cursor-pointer transition-colors duration-150 ${
        isHighlighted
          ? 'bg-indigo-50 text-indigo-900'
          : 'text-gray-900 hover:bg-gray-50'
      }`}
      >
        <div className="font-medium">{displayValue || 'Unknown'}</div>
        {descriptionValue && (
          <div className="text-sm text-gray-500 mt-1">{descriptionValue}</div>
        )}
        {categoryValue && (
          <div className="text-xs text-indigo-600 mt-1 font-medium">
            {categoryValue}
          </div>
        )}
      </div>
    );
  };

  // Default selected renderer
  const defaultRenderSelected = (option) => {
    if (!option) return null;
    const displayValue = safeRenderField(option[displayField]);
    return (
      <span className="block truncate">{displayValue || 'Unknown'}</span>
    );
  };

  const baseInputClasses = `
    block w-full px-4 py-3 pr-10 border-2 rounded-xl shadow-sm
    focus:outline-none focus:ring-2 transition-all duration-200 sm:text-sm
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white cursor-text'}
  `;

  const inputClasses = error
    ? `${baseInputClasses} border-red-300 focus:ring-red-500 focus:border-red-500 bg-red-50`
    : `${baseInputClasses} border-indigo-200 focus:ring-indigo-500 focus:border-indigo-500 hover:border-indigo-300`;

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          className={inputClasses}
          placeholder={placeholder}
          value={searchTerm || (selectedOption && !isOpen ? safeRenderField(selectedOption[displayField]) || '' : '')}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          autoComplete="off"
          style={{ color: selectedOption && !isOpen ? '#374151' : undefined }} // Normal text color for selected values
          {...props}
        />

        {/* Right side icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
          {selectedOption && allowClear && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="text-gray-400 hover:text-gray-600 transition-colors duration-150"
            >
              <FaTimes className="h-4 w-4" />
            </button>
          )}

          <div className="text-gray-400">
            {isOpen ? (
              <FaChevronDown className="h-4 w-4 transform rotate-180 transition-transform duration-150" />
            ) : (
              <FaSearch className="h-4 w-4" />
            )}
          </div>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-full bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-auto">
          {searchTerm.length < minSearchLength ? (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              {searchingText}
            </div>
          ) : isSearching ? (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              <div className="flex items-center justify-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-blue-600"></div>
                <span>Searching...</span>
              </div>
            </div>
          ) : filteredOptions.length === 0 ? (
            <div>
              <div className="px-4 py-3 text-sm text-gray-500 text-center">
                {noResultsText}
              </div>
              {showCreateOption && onCreateNew && searchTerm.length >= minSearchLength && (
                <div
                  onClick={() => {
                    onCreateNew(searchTerm);
                    setIsOpen(false);
                  }}
                  className="px-4 py-3 text-sm text-blue-600 hover:bg-blue-50 cursor-pointer border-t border-gray-100 flex items-center space-x-2"
                >
                  <span>+</span>
                  <span>{createNewText}: "{searchTerm}"</span>
                </div>
              )}
            </div>
          ) : (
            <div ref={listRef}>
              {filteredOptions.map((option, index) => {
                const keyValue = option[valueField];
                const safeKey = typeof keyValue === 'object' ? JSON.stringify(keyValue) : String(keyValue || index);

                return (
                  <div
                    key={safeKey}
                    onClick={() => handleSelect(option)}
                    className="border-b border-gray-100 last:border-b-0"
                  >
                    {(renderOption || defaultRenderOption)(option, index === highlightedIndex)}
                  </div>
                );
              })}
              {showCreateOption && onCreateNew && (
                <div
                  onClick={() => {
                    onCreateNew(searchTerm);
                    setIsOpen(false);
                  }}
                  className="px-4 py-3 text-sm text-blue-600 hover:bg-blue-50 cursor-pointer border-t border-gray-100 flex items-center space-x-2"
                >
                  <span>+</span>
                  <span>{createNewText}: "{searchTerm}"</span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchableSelect;
