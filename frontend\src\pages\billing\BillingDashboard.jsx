import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  subscriptionService,
  billingService,
  formatCurrency,
  formatDate,
  getSubscriptionStatusColor,
  calculateUsagePercentage,
  getUsageColor,
  getUsageBarColor
} from '../../services/subscriptionService';
import { Card } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Spinner } from '../../components/ui/Spinner';

const BillingDashboard = () => {
  const [subscription, setSubscription] = useState(null);
  const [usage, setUsage] = useState(null);
  const [billingSummary, setBillingSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [canceling, setCanceling] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [subscriptionResponse, usageResponse, billingResponse] = await Promise.all([
        subscriptionService.getCurrentSubscription(),
        subscriptionService.getUsage(),
        billingService.getBillingSummary(),
      ]);

      setSubscription(subscriptionResponse.data.subscription);
      setUsage(usageResponse.data);
      setBillingSummary(billingResponse.data);
    } catch (error) {
      console.error('Error fetching billing data:', error);
      toast.error('Failed to load billing information');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!window.confirm('Are you sure you want to cancel your subscription? It will remain active until the end of your current billing period.')) {
      return;
    }

    try {
      setCanceling(true);
      await subscriptionService.cancelSubscription(true);
      toast.success('Subscription canceled successfully');
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setCanceling(false);
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      await subscriptionService.reactivateSubscription();
      toast.success('Subscription reactivated successfully');
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast.error('Failed to reactivate subscription');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <i className="bi bi-credit-card text-6xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
          <p className="text-gray-600 mb-6">
            You don't have an active subscription. Choose a plan to get started.
          </p>
          <Link to="/billing/plans">
            <Button className="btn-theme-primary">
              View Plans
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const daysUntilExpiry = subscription.daysUntilExpiry ? subscription.daysUntilExpiry() : 0;

  return (
    <>
      <Helmet>
        <title>Billing Dashboard - TallyCRM</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Billing Dashboard</h1>
            <p className="text-gray-600">Manage your subscription and billing</p>
          </div>
          <div className="flex space-x-3">
            <Link to="/billing/plans">
              <Button variant="outline">
                <i className="bi bi-arrow-up-circle mr-2"></i>
                Upgrade Plan
              </Button>
            </Link>
            <Link to="/billing/history">
              <Button variant="outline">
                <i className="bi bi-receipt mr-2"></i>
                Billing History
              </Button>
            </Link>
          </div>
        </div>

        {/* Subscription Status */}
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Current Subscription</h2>
              <Badge className={getSubscriptionStatusColor(subscription.status)}>
                {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-900">{subscription.plan.name}</h3>
                <p className="text-gray-600">{subscription.plan.description}</p>
                <div className="mt-2">
                  <span className="text-xl font-semibold text-purple-600">
                    {formatCurrency(subscription.amount)}
                  </span>
                  <span className="text-gray-600 ml-1">
                    /{subscription.interval}
                  </span>
                </div>
              </div>

              <div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Current Period</span>
                    <span className="font-medium">
                      {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Next Billing</span>
                    <span className="font-medium">
                      {formatDate(subscription.current_period_end)}
                    </span>
                  </div>
                  {daysUntilExpiry > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Days Remaining</span>
                      <span className={`font-medium ${daysUntilExpiry <= 7 ? 'text-red-600' : 'text-green-600'}`}>
                        {daysUntilExpiry} days
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex flex-col space-y-2">
                {subscription.cancel_at_period_end ? (
                  <>
                    <div className="text-sm text-red-600 mb-2">
                      <i className="bi bi-exclamation-triangle mr-1"></i>
                      Subscription will cancel on {formatDate(subscription.current_period_end)}
                    </div>
                    <Button
                      onClick={handleReactivateSubscription}
                      variant="outline"
                      size="sm"
                      className="border-green-500 text-green-600 hover:bg-green-50"
                    >
                      Reactivate Subscription
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={handleCancelSubscription}
                    disabled={canceling}
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-600 hover:bg-red-50"
                  >
                    {canceling ? (
                      <>
                        <Spinner size="sm" className="mr-2" />
                        Canceling...
                      </>
                    ) : (
                      'Cancel Subscription'
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Usage Overview */}
        {usage && (
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Usage Overview</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Users */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600">Users</span>
                    <span className="text-sm text-gray-900">
                      {usage.currentUsage.users?.current || 0} / {usage.limits.users}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getUsageBarColor(
                        calculateUsagePercentage(usage.currentUsage.users?.current || 0, usage.limits.users)
                      )}`}
                      style={{
                        width: `${calculateUsagePercentage(usage.currentUsage.users?.current || 0, usage.limits.users)}%`,
                      }}
                    >
                    </div>
                  </div>
                  <div className={`text-xs mt-1 ${getUsageColor(
                    calculateUsagePercentage(usage.currentUsage.users?.current || 0, usage.limits.users)
                  )}`}
                  >
                    {calculateUsagePercentage(usage.currentUsage.users?.current || 0, usage.limits.users).toFixed(0)}% used
                  </div>
                </div>

                {/* Customers */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600">Customers</span>
                    <span className="text-sm text-gray-900">
                      {usage.currentUsage.customers?.current || 0} / {usage.limits.customers}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getUsageBarColor(
                        calculateUsagePercentage(usage.currentUsage.customers?.current || 0, usage.limits.customers)
                      )}`}
                      style={{
                        width: `${calculateUsagePercentage(usage.currentUsage.customers?.current || 0, usage.limits.customers)}%`,
                      }}
                    >
                    </div>
                  </div>
                  <div className={`text-xs mt-1 ${getUsageColor(
                    calculateUsagePercentage(usage.currentUsage.customers?.current || 0, usage.limits.customers)
                  )}`}
                  >
                    {calculateUsagePercentage(usage.currentUsage.customers?.current || 0, usage.limits.customers).toFixed(0)}% used
                  </div>
                </div>

                {/* Service Calls */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600">Service Calls</span>
                    <span className="text-sm text-gray-900">
                      {usage.currentUsage.service_calls?.current || 0} / {usage.limits.service_calls}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getUsageBarColor(
                        calculateUsagePercentage(usage.currentUsage.service_calls?.current || 0, usage.limits.service_calls)
                      )}`}
                      style={{
                        width: `${calculateUsagePercentage(usage.currentUsage.service_calls?.current || 0, usage.limits.service_calls)}%`,
                      }}
                    >
                    </div>
                  </div>
                  <div className={`text-xs mt-1 ${getUsageColor(
                    calculateUsagePercentage(usage.currentUsage.service_calls?.current || 0, usage.limits.service_calls)
                  )}`}
                  >
                    {calculateUsagePercentage(usage.currentUsage.service_calls?.current || 0, usage.limits.service_calls).toFixed(0)}% used
                  </div>
                </div>

                {/* Storage */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-600">Storage</span>
                    <span className="text-sm text-gray-900">
                      {usage.currentUsage.storage_gb?.current || 0}GB / {usage.limits.storage_gb}GB
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getUsageBarColor(
                        calculateUsagePercentage(usage.currentUsage.storage_gb?.current || 0, usage.limits.storage_gb)
                      )}`}
                      style={{
                        width: `${calculateUsagePercentage(usage.currentUsage.storage_gb?.current || 0, usage.limits.storage_gb)}%`,
                      }}
                    >
                    </div>
                  </div>
                  <div className={`text-xs mt-1 ${getUsageColor(
                    calculateUsagePercentage(usage.currentUsage.storage_gb?.current || 0, usage.limits.storage_gb)
                  )}`}
                  >
                    {calculateUsagePercentage(usage.currentUsage.storage_gb?.current || 0, usage.limits.storage_gb).toFixed(0)}% used
                  </div>
                </div>
              </div>

              {/* Usage Warnings */}
              {usage.limitCheck?.hasViolations && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center">
                    <i className="bi bi-exclamation-triangle text-red-600 mr-2"></i>
                    <span className="text-red-800 font-medium">Usage Limit Exceeded</span>
                  </div>
                  <p className="text-red-700 text-sm mt-1">
                    You have exceeded your plan limits. Consider upgrading to avoid service interruption.
                  </p>
                  <Link to="/billing/plans" className="inline-block mt-2">
                    <Button size="sm" className="bg-red-600 hover:bg-red-700">
                      Upgrade Now
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* Billing Summary */}
        {billingSummary && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <i className="bi bi-currency-rupee text-green-600 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Paid</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(billingSummary.paymentSummary.totalAmount)}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <i className="bi bi-receipt text-blue-600 text-xl"></i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Invoices</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {billingSummary.paymentSummary.totalPayments}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-lg ${
                    billingSummary.overdueCount > 0 ? 'bg-red-100' : 'bg-gray-100'
                  }`}
                  >
                    <i className={`bi bi-exclamation-triangle text-xl ${
                      billingSummary.overdueCount > 0 ? 'text-red-600' : 'text-gray-600'
                    }`}
                    >
                    </i>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Overdue</p>
                    <p className={`text-2xl font-bold ${
                      billingSummary.overdueCount > 0 ? 'text-red-600' : 'text-gray-900'
                    }`}
                    >
                      {billingSummary.overdueCount}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </>
  );
};

export default BillingDashboard;
