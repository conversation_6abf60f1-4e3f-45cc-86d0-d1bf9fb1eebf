import express from 'express';
import { query } from 'express-validator';
import { validateRequest as validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getDashboardOverview,
  getDashboardCharts,
} from '../controllers/dashboardController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @swagger
 * /dashboard/overview:
 *   get:
 *     summary: Get dashboard overview statistics
 *     description: Retrieve comprehensive dashboard statistics including summary counts, monthly activity, growth percentages, revenue statistics, recent activity, and alerts
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard overview retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         summary:
 *                           type: object
 *                           properties:
 *                             totalCustomers:
 *                               type: integer
 *                               example: 150
 *                             totalServiceCalls:
 *                               type: integer
 *                               example: 75
 *                             activeServiceCalls:
 *                               type: integer
 *                               example: 25
 *                             completedServiceCalls:
 *                               type: integer
 *                               example: 50
 *                             totalRevenue:
 *                               type: number
 *                               example: 125000.50
 *                             monthlyRevenue:
 *                               type: number
 *                               example: 15000.00
 *                         monthlyActivity:
 *                           type: object
 *                           properties:
 *                             newCustomers:
 *                               type: integer
 *                               example: 8
 *                             newServiceCalls:
 *                               type: integer
 *                               example: 12
 *                             completedCalls:
 *                               type: integer
 *                               example: 15
 *                         growth:
 *                           type: object
 *                           properties:
 *                             customers:
 *                               type: number
 *                               example: 5.6
 *                               description: "Growth percentage"
 *                             serviceCalls:
 *                               type: number
 *                               example: 12.3
 *                             revenue:
 *                               type: number
 *                               example: 8.7
 *                         recentActivity:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                                 example: "service_call"
 *                               title:
 *                                 type: string
 *                                 example: "New service call created"
 *                               description:
 *                                 type: string
 *                                 example: "Service call SER-001 created for ABC Enterprises"
 *                               timestamp:
 *                                 type: string
 *                                 format: date-time
 *                         alerts:
 *                           type: object
 *                           properties:
 *                             overdueServiceCalls:
 *                               type: integer
 *                               example: 3
 *                             followUpsToday:
 *                               type: integer
 *                               example: 5
 *                             expiringAmcs:
 *                               type: integer
 *                               example: 2
 *             example:
 *               success: true
 *               message: "Dashboard overview retrieved successfully"
 *               data:
 *                 summary:
 *                   totalCustomers: 150
 *                   totalServiceCalls: 75
 *                   activeServiceCalls: 25
 *                   completedServiceCalls: 50
 *                   totalRevenue: 125000.50
 *                   monthlyRevenue: 15000.00
 *                 monthlyActivity:
 *                   newCustomers: 8
 *                   newServiceCalls: 12
 *                   completedCalls: 15
 *                 growth:
 *                   customers: 5.6
 *                   serviceCalls: 12.3
 *                   revenue: 8.7
 *                 alerts:
 *                   overdueServiceCalls: 3
 *                   followUpsToday: 5
 *                   expiringAmcs: 2
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/overview', [
  requirePermission('dashboard.read'),
], getDashboardOverview);

/**
 * @swagger
 * /dashboard/charts:
 *   get:
 *     summary: Get dashboard charts data
 *     description: Retrieve trend data for dashboard charts including service calls, sales, and customer acquisition trends
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 3m, 1y]
 *           default: 30d
 *         description: Time period for chart data
 *         example: "30d"
 *     responses:
 *       200:
 *         description: Dashboard charts data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         serviceCallsTrend:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-01-15"
 *                               count:
 *                                 type: integer
 *                                 example: 5
 *                               completed:
 *                                 type: integer
 *                                 example: 3
 *                         salesTrend:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-01-15"
 *                               amount:
 *                                 type: number
 *                                 example: 2500.00
 *                               count:
 *                                 type: integer
 *                                 example: 2
 *                         customerAcquisitionTrend:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-01-15"
 *                               newCustomers:
 *                                 type: integer
 *                                 example: 2
 *                               prospects:
 *                                 type: integer
 *                                 example: 1
 *                         period:
 *                           type: string
 *                           example: "30d"
 *             example:
 *               success: true
 *               message: "Dashboard charts data retrieved successfully"
 *               data:
 *                 serviceCallsTrend:
 *                   - date: "2024-01-15"
 *                     count: 5
 *                     completed: 3
 *                   - date: "2024-01-16"
 *                     count: 7
 *                     completed: 4
 *                 salesTrend:
 *                   - date: "2024-01-15"
 *                     amount: 2500.00
 *                     count: 2
 *                   - date: "2024-01-16"
 *                     amount: 3200.00
 *                     count: 3
 *                 customerAcquisitionTrend:
 *                   - date: "2024-01-15"
 *                     newCustomers: 2
 *                     prospects: 1
 *                   - date: "2024-01-16"
 *                     newCustomers: 1
 *                     prospects: 2
 *                 period: "30d"
 *       400:
 *         $ref: '#/components/responses/400'
 *       401:
 *         $ref: '#/components/responses/401'
 *       403:
 *         $ref: '#/components/responses/403'
 *       500:
 *         $ref: '#/components/responses/500'
 */
router.get('/charts', [
  requirePermission('dashboard.read'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '3m', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 3m, 1y'),
  validate,
], getDashboardCharts);

export default router;
