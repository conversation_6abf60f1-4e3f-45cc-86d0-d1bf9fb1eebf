import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Tenant = sequelize.define('Tenant', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        is: /^[a-zA-Z0-9_-]+$/,
        len: [2, 50],
      },
    },
    domain: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    logo: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    gst_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    pan_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    subscription_plan: {
      type: DataTypes.ENUM('basic', 'standard', 'premium', 'enterprise'),
      defaultValue: 'basic',
    },
    subscription_status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended', 'trial'),
      defaultValue: 'trial',
    },
    subscription_expires_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    max_users: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
    },
    max_customers: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    timezone: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Asia/Kolkata',
      comment: 'Tenant timezone',
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'INR',
      comment: 'Tenant default currency',
    },
    date_format: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'DD/MM/YYYY',
      comment: 'Tenant date format preference',
    },
    time_format: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '24',
      comment: 'Tenant time format preference (12/24)',
    },
    logo_url: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'URL to tenant logo',
    },
    settings: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'tenants',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['slug'],
        unique: true,
      },
      {
        fields: ['subscription_status'],
      },
      {
        fields: ['is_active'],
      },
    ],
  });

  // Instance methods
  Tenant.prototype.isSubscriptionActive = function() {
    return this.subscription_status === 'active' &&
           (!this.subscription_expires_at || this.subscription_expires_at > new Date());
  };

  Tenant.prototype.canAddUsers = function(currentUserCount) {
    return currentUserCount < this.max_users;
  };

  Tenant.prototype.canAddCustomers = function(currentCustomerCount) {
    return currentCustomerCount < this.max_customers;
  };

  // Associations
  Tenant.associate = function(models) {
    Tenant.hasMany(models.User, {
      foreignKey: 'tenant_id',
      as: 'users',
    });

    Tenant.hasMany(models.Customer, {
      foreignKey: 'tenant_id',
      as: 'customers',
    });

    Tenant.hasMany(models.ServiceCall, {
      foreignKey: 'tenant_id',
      as: 'serviceCalls',
    });

    Tenant.hasMany(models.Sale, {
      foreignKey: 'tenant_id',
      as: 'sales',
    });

    // Master data associations
    Tenant.hasMany(models.Executive, {
      foreignKey: 'tenant_id',
      as: 'executives',
    });
  };

  return Tenant;
}
