import React from 'react';
import { cn } from '../../utils/helpers';
import preminfoLogo from '../../assets/preminfotechlogo.svg';

const LoadingScreen = ({
  title = 'Loading Dashboard...',
  subtitle = 'Fetching your business insights',
  className = '',
  variant = 'dashboard' // dashboard, page, modal
}) => {
  const variants = {
    dashboard: {
      container: 'min-h-screen bg-gray-50',
      spinner: 'h-20 w-20',
      title: 'text-xl font-bold',
      subtitle: 'text-base'
    },
    page: {
      container: 'min-h-[60vh] bg-gray-50',
      spinner: 'h-16 w-16',
      title: 'text-lg font-semibold',
      subtitle: 'text-sm'
    },
    modal: {
      container: 'min-h-[200px] bg-white',
      spinner: 'h-12 w-12',
      title: 'text-base font-medium',
      subtitle: 'text-sm'
    }
  };

  const config = variants[variant];

  return (
    <div className={cn(config.container, 'flex justify-center items-center', className)}>
      <div className="text-center">
        <div className="relative mb-8">
          {/* Single spinner - main theme color */}
          <div
            className={cn('animate-spin rounded-full border-4 border-t-transparent', config.spinner)}
            style={{
              borderColor: 'var(--primary-color)',
              borderTopColor: 'transparent'
            }}
          >
          </div>

          {/* Center logo removed as requested */}
        </div>

        <div className="space-y-3">
          <h3
            className={cn(config.title)}
            style={{ color: 'var(--primary-color)' }}
          >
            {title}
          </h3>
          <p
            className={cn(config.subtitle)}
            style={{ color: 'rgba(var(--primary-rgb), 0.8)' }}
          >
            {subtitle}
          </p>

          {/* Loading progress dots */}
          <div className="flex justify-center space-x-2 mt-4">
            <div
              className="w-2 h-2 rounded-full animate-bounce"
              style={{ backgroundColor: 'rgba(var(--primary-rgb), 0.6)' }}
            >
            </div>
            <div
              className="w-2 h-2 rounded-full animate-bounce"
              style={{
                backgroundColor: 'rgba(var(--primary-rgb), 0.8)',
                animationDelay: '0.1s'
              }}
            >
            </div>
            <div
              className="w-2 h-2 rounded-full animate-bounce"
              style={{
                backgroundColor: 'var(--primary-color)',
                animationDelay: '0.2s'
              }}
            >
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
