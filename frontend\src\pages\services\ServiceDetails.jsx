import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { apiService } from '../../services/api';
import useTimer from '../../hooks/useTimer';
import TimerHistoryModal from '../../components/TimerHistoryModal';
import {
  FaEdit,
  FaTrash,
  FaUser,
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaCheckCircle,
  FaTimesCircle,
  FaPlayCircle,
  FaPauseCircle,
  FaHistory
} from 'react-icons/fa';

const ServiceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');
  const [showTimerHistory, setShowTimerHistory] = useState(false);
  const [timerHistoryData, setTimerHistoryData] = useState(null);
  const [timerHistoryLoading, setTimerHistoryLoading] = useState(false);
  const [amcDetails, setAmcDetails] = useState(null);

  // Use the timer hook for backend-managed timer functionality
  const {
    timerData,
    isTimerRunning,
    isTimerPaused,
    displayTime,
    formattedTime,
    statusInfo,
    refreshTimer
  } = useTimer(id);

  // Helper function to format time duration with seconds precision
  const formatTimeDuration = (totalSeconds) => {
    if (!totalSeconds || totalSeconds === 0) return '0 seconds';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''} ${seconds} second${seconds !== 1 ? 's' : ''}`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ${seconds} second${seconds !== 1 ? 's' : ''}`;
    }
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;

  };

  // Helper function to format time for display in hours
  const formatHoursDisplay = (totalSeconds, totalMinutes) => {
    if (totalSeconds && totalSeconds > 0) {
      return (totalSeconds / 3600).toFixed(2);
    } else if (totalMinutes && totalMinutes > 0) {
      return (totalMinutes / 60).toFixed(2);
    }
    return '0.00';
  };

  // Helper function to format time in HH:MM:SS format
  const formatTimeDisplay = (totalSeconds) => {
    if (!totalSeconds || totalSeconds === 0) return '00:00:00';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  };

  // Get display time from backend timer hook
  const getDisplayTime = () => {
    return displayTime || 0;
  };

  // Real-time timer component using backend timer data
  const RealTimeTimer = () => {
    // Use backend timer state instead of frontend calculations
    if (!isTimerRunning && !isTimerPaused) return null;

    // Render for "In Progress" status
    if (isTimerRunning) {
      return (
        <div className="rounded-lg p-3 mb-4" style={{ backgroundColor: 'var(--theme-content-bg)', borderColor: 'var(--primary-color)', borderWidth: '1px', borderStyle: 'solid' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full animate-pulse mr-2" style={{ backgroundColor: 'var(--primary-color)' }}></div>
              <span className="text-sm font-medium" style={{ color: 'var(--primary-color)' }}>Service In Progress</span>
            </div>
            <div className="text-right">
              <div className="text-lg font-mono font-bold" style={{ color: 'var(--primary-color)' }}>
                {formattedTime}
              </div>
              <div className="text-xs" style={{ color: 'var(--primary-color)' }}>Live Timer (Backend)</div>
            </div>
          </div>
        </div>
      );
    }

    // Render for "On Hold" status
    if (isTimerPaused) {
      return (
        <div className="bg-white border border-black rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-black rounded-full mr-2"></div>
              <span className="text-sm font-medium text-black">Service On Hold</span>
            </div>
            <div className="text-right">
              <div className="text-lg font-mono font-bold text-black">
                {formattedTime}
              </div>
              <div className="text-xs text-black">Paused Time (Backend)</div>
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  // Fetch service data from API
  useEffect(() => {
    fetchServiceDetails();
  }, [id]);

  // Fetch timer history when Progress tab is active
  useEffect(() => {
    if (activeTab === 'progress' && service?.id) {
      fetchTimerHistory();
    }
  }, [activeTab, service?.id]);

  // Fetch AMC details when service is loaded and call type is AMC
  useEffect(() => {
    if (service?.customerId && (service?.callType === 'amc_call' || service?.category === 'AMC Call')) {
      fetchAMCDetails(service.customerId);
    }
  }, [service?.customerId, service?.callType, service?.category]);

  const fetchServiceDetails = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/service-calls/${id}`);

      if (response.data?.success) {
        const serviceData = response.data.data.serviceCall;

        // Debug logging for amount fields
        console.log('🔍 Service Data Amount Fields Debug:', {
          serviceId: serviceData.id,
          service_charges: serviceData.service_charges,
          amount: serviceData.amount,
          total_amount: serviceData.total_amount,
          hourly_rate: serviceData.hourly_rate,
          hourly_rate_amount: serviceData.hourly_rate_amount,
          travel_charges: serviceData.travel_charges,
          charging_type: serviceData.charging_type,
          allAmountFields: Object.keys(serviceData).filter(key =>
            key.includes('amount') || key.includes('charge') || key.includes('rate')
          ).reduce((obj, key) => {
            obj[key] = serviceData[key];
            return obj;
          }, {})
        });

        // Enhanced data mapping to include all service call fields
        setService({
          id: serviceData.id,
          serviceNumber: serviceData.call_number || serviceData.service_number || `SRV-${serviceData.id}`,
          customer: serviceData.customer?.company_name || serviceData.customer_name || 'Unknown Customer',
          customerId: serviceData.customer_id,

          // Contact Information
          contactPerson: serviceData.contactPerson ?
            `${serviceData.contactPerson.first_name || ''} ${serviceData.contactPerson.last_name || ''}`.trim() :
            serviceData.contact_person || '',
          contactPhone: serviceData.contactPerson?.phone || serviceData.contact_number || serviceData.contact_phone || '',
          contactEmail: serviceData.contactPerson?.email || serviceData.contact_email || '',

          // Service Details
          type: serviceData.call_type || serviceData.service_type || 'General',
          callType: serviceData.call_billing_type || 'free call',
          category: serviceData.typeOfCall?.name || serviceData.category || 'Support',
          description: serviceData.description || serviceData.customer_reported_issue || '',
          subject: serviceData.subject || '',
          priority: serviceData.priority || 'medium',
          status: serviceData.status?.name || serviceData.status || 'pending',
          statusObject: serviceData.status, // Keep the full status object for timer logic

          // Assignment
          assignedTo: serviceData.assignedExecutive ?
            `${serviceData.assignedExecutive.first_name || ''} ${serviceData.assignedExecutive.last_name || ''}`.trim() :
            serviceData.assigned_to || 'Unassigned',
          technicianId: serviceData.assigned_to,
          technicianPhone: serviceData.assignedExecutive?.phone || serviceData.technician_phone || '',
          technicianEmail: serviceData.assignedExecutive?.email || serviceData.technician_email || '',

          // Scheduling & Timing
          createdDate: serviceData.created_at ? serviceData.created_at.split('T')[0] : '',
          createdDateTime: serviceData.created_at || '', // Full timestamp for date and time display
          callDate: serviceData.call_date ? serviceData.call_date.split('T')[0] : '',
          scheduledDate: serviceData.scheduled_date ? serviceData.scheduled_date.split('T')[0] : '',
          bookingDate: serviceData.booking_date ? serviceData.booking_date.split('T')[0] : '',
          scheduledTime: serviceData.scheduled_time || '',
          callStartTime: serviceData.call_start_time || '',
          callEndTime: serviceData.call_end_time || '',
          startedAt: serviceData.started_at, // Full timestamp for timer calculations
          startedDate: serviceData.started_at ? serviceData.started_at.split('T')[0] : null,
          completedDate: serviceData.completed_at ? serviceData.completed_at.split('T')[0] : null,
          closedDate: serviceData.closed_at ? serviceData.closed_at.split('T')[0] : null,
          estimatedHours: serviceData.estimated_hours || 0,
          actualHours: serviceData.actual_hours || serviceData.total_time_minutes ? (serviceData.total_time_minutes / 60).toFixed(2) : 0,
          totalTimeMinutes: serviceData.total_time_minutes || 0,
          totalTimeSeconds: serviceData.total_time_seconds || (serviceData.total_time_minutes ? serviceData.total_time_minutes * 60 : 0),

          // Location & Service Details
          serviceLocation: serviceData.service_location || 'customer-site',
          address: serviceData.address || '',
          city: serviceData.city || '',
          state: serviceData.state || '',
          locationCoordinates: serviceData.location_coordinates || {},
          googleMapsLink: serviceData.google_maps_link || '',

          // Tally & Technical Details
          tallySerialNumber: serviceData.tally_serial_number || '',
          tallyVersion: serviceData.tally_version || '',
          companyName: serviceData.company_name || '',
          designation: serviceData.designation || '',
          tssStatus: serviceData.tss_status || '',
          tssExpiry: serviceData.tss_expiry ? serviceData.tss_expiry.split('T')[0] : '',

          // Financial - Enhanced mapping with multiple fallbacks and debugging
          amount: parseFloat(serviceData.service_charges) || parseFloat(serviceData.total_amount) || parseFloat(serviceData.amount) || 0,
          currency: serviceData.currency || 'INR',
          paymentStatus: serviceData.payment_status || 'pending',
          chargingType: serviceData.charging_type || 'fixed',
          hourlyRate: parseFloat(serviceData.hourly_rate) || parseFloat(serviceData.hourly_rate_amount) || 0,
          travelCharges: parseFloat(serviceData.travel_charges) || 0,

          // Issues & Solutions
          customerReportedIssue: serviceData.customer_reported_issue || '',
          actualIssueFound: serviceData.actual_issue_found || '',
          solutionProvided: serviceData.solution_provided || '',
          executiveRemarks: serviceData.executive_remarks || '',
          customerFeedbackType: serviceData.customer_feedback_type || '',
          customerFeedbackComments: serviceData.customer_feedback_comments || '',

          // Additional Information
          requirements: serviceData.requirements || serviceData.customer_reported_issue || '',
          notes: serviceData.notes || serviceData.internal_notes || '',
          mobileNumber2: serviceData.mobile_number_2 || '',
          isEndTimeFixed: serviceData.is_end_time_fixed || false,

          // Progress tracking
          progress: serviceData.progress || [],
          timeTrackingHistory: serviceData.time_tracking_history || [],
          timeTrackingSummary: serviceData.time_tracking_summary || {},

          // Files/attachments
          attachments: serviceData.attachments || [],

          // Custom fields
          customFields: serviceData.custom_fields || {},
          tags: serviceData.tags || []
        });
      } else {
        toast.error('Service call not found');
        navigate('/services');
      }
    } catch (error) {
      console.error('Error fetching service details:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');

      // Check if it's an auth error first
      if (!ErrorHandler.handleAuthError(error)) {
        ErrorHandler.showError(error, 'Unable to load service details');
      }

      navigate('/services');
    } finally {
      setLoading(false);
    }
  };

  const fetchTimerHistory = async () => {
    try {
      setTimerHistoryLoading(true);
      const response = await apiService.get(`/service-calls/${id}/timer-history`);
      if (response.data?.success) {
        setTimerHistoryData(response.data.data);
      } else {
        console.error('Failed to fetch timer history:', response.data?.message);
      }
    } catch (error) {
      console.error('Error fetching timer history:', error);
    } finally {
      setTimerHistoryLoading(false);
    }
  };

  const fetchAMCDetails = async (customerId) => {
    try {
      const response = await apiService.get(`/customers/${customerId}`);
      if (response.data?.success && response.data?.data?.customer) {
        const customer = response.data.data.customer;
        const customFields = customer.custom_fields || {};

        // Check if customer has active AMC from custom_fields
        const hasActiveAMC = customFields.amc_status === 'active' || customFields.amc_status === 'YES';

        if (hasActiveAMC && customFields.amc_to_date) {
          // Calculate next visit date based on number of visits per year
          const noOfVisits = customFields.no_of_visits || 4; // Default to 4 visits per year
          const monthsBetweenVisits = Math.floor(12 / noOfVisits);
          const nextVisitDate = new Date();
          nextVisitDate.setMonth(nextVisitDate.getMonth() + monthsBetweenVisits);

          setAmcDetails({
            expiryDate: customFields.amc_to_date,
            renewalDate: customFields.renewal_date,
            nextVisit: nextVisitDate.toISOString().split('T')[0],
            fromDate: customFields.amc_from_date,
            noOfVisits: customFields.no_of_visits,
            currentAmount: customFields.current_amc_amount,
            lastYearAmount: customFields.last_year_amc_amount
          });
        } else {
          setAmcDetails(null);
        }
      }
    } catch (error) {
      console.error('Error fetching customer AMC details:', error);
      setAmcDetails(null);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this service request? This action cannot be undone.')) {
      try {
        const response = await apiService.delete(`/service-calls/${id}`);
        if (response.data?.success) {
          toast.success('Service request deleted successfully');
          navigate('/services');
        } else {
          toast.error(response.data?.message || 'Failed to delete service request');
        }
      } catch (error) {
        console.error('Error deleting service:', error);
        toast.error('Failed to delete service request');
      }
    }
  };

  const handleStatusUpdate = async (newStatus) => {
    try {
      console.log('🔄 Starting status update:', { currentStatus: service.status, newStatus });

      // Create a mapping of frontend status names to backend status codes
      const statusMapping = {
        'in-progress': 'ON_PROCESS',
        'completed': 'COMPLETED',
        'scheduled': 'ONSITE',
        'pending': 'PENDING',
        'cancelled': 'CANCELLED',
        'on-hold': 'ON_HOLD'
      };

      const statusCode = statusMapping[newStatus] || newStatus.toUpperCase();
      console.log('📋 Status mapping:', { newStatus, statusCode });

      // Try to get all call statuses directly from master data first
      const statusResponse = await apiService.get('/master-data/call-statuses');
      if (!statusResponse.data?.success) {
        throw new Error('Failed to fetch call statuses from master data');
      }

      const allStatuses = statusResponse.data.data;
      console.log('📊 Available statuses:', allStatuses.map(s => ({ id: s.id, name: s.name, code: s.code })));

      // Find status by code first, then by name
      let foundStatus = allStatuses.find(status => status.code === statusCode);

      // If not found by code, try to find by name
      if (!foundStatus) {
        foundStatus = allStatuses.find(status =>
          status.name.toLowerCase() === newStatus.replace('-', ' ').toLowerCase()
        );
      }

      // Special handling for "in-progress" - ensure we get ON_PROCESS, not PROGRESS
      if (newStatus === 'in-progress' && (!foundStatus || foundStatus.code === 'PROGRESS')) {
        const onProcessStatus = allStatuses.find(status => status.code === 'ON_PROCESS');
        if (onProcessStatus) {
          foundStatus = onProcessStatus;
          console.log('🔄 Using ON_PROCESS status instead of PROGRESS for better compatibility');
        }
      }

      if (!foundStatus) {
        console.error('❌ Status not found:', { statusCode, newStatus, availableStatuses: allStatuses.map(s => s.code) });
        throw new Error(`Status "${statusCode}" not found in database`);
      }

      console.log('✅ Found target status:', foundStatus);

      // Update the service call status via API using the UUID
      const updateData = {
        status_id: foundStatus.id // Use snake_case for backend compatibility
      };

      console.log('🚀 Making API call to update status (edit form):', updateData);
      const response = await apiService.put(`/service-calls/${id}`, updateData);

      if (response.data?.success) {
        console.log('✅ Status update successful (edit form):', response.data);

        // Update local state with the new status object
        setService(prev => ({
          ...prev,
          status: {
            id: foundStatus.id,
            name: foundStatus.name,
            code: foundStatus.code,
            color: foundStatus.color,
            category: foundStatus.category
          }
        }));

        toast.success(`Service status updated to ${foundStatus.name}`);

        // ENHANCED: Add delay before refresh to ensure backend timer processing is complete
        setTimeout(() => {
          console.log('🔄 Refreshing service data and timer (edit form)...');
          fetchServiceDetails();
          refreshTimer(); // Refresh backend timer data
        }, 500); // 500ms delay to ensure backend timer processing is complete
      } else {
        console.error('❌ API response indicates failure:', response.data);
        const errorMessage = response.data?.details?.userFriendlyMessage ||
                            response.data?.message ||
                            'Failed to update status';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Error updating service status:', error);

      // Import and use the new error handler
      const { ErrorHandler } = await import('../../utils/errorUtils.js');
      ErrorHandler.showError(error, 'Failed to update service status');
    }
  };

  const getStatusBadge = (status) => {
    // Handle both string and object status, with null/undefined safety
    let statusValue, statusColor;

    if (typeof status === 'object' && status !== null) {
      statusValue = status.name || status.status || 'unknown';
      statusColor = status.color;
    } else {
      statusValue = status || 'unknown';
    }

    const statusString = String(statusValue).toLowerCase();

    // Use theme colors, black, or white colors only
    const badgeStyle = {};
    let badgeClass = 'badge px-2 py-1 rounded text-xs font-medium';

    if (statusColor) {
      // For custom colors, use theme color or black/white
      const isLightColor = statusColor === '#ffffff' || statusColor === 'white';
      if (isLightColor) {
        badgeClass += ' text-black border border-black';
        badgeStyle.backgroundColor = 'white';
      } else {
        badgeClass += ' text-white';
        badgeStyle.backgroundColor = 'var(--primary-color)';
      }
    } else {
      // Fallback to theme color, black, white color scheme
      if (statusString === 'completed') {
        badgeClass += ' text-white';
        badgeStyle.backgroundColor = 'var(--primary-color)';
      } else if (statusString === 'in progress' || statusString === 'in-progress') {
        badgeClass += ' text-white';
        badgeStyle.backgroundColor = 'var(--primary-light)';
      } else if (statusString === 'on hold' || statusString === 'on-hold') {
        badgeClass += ' bg-black text-white';
      } else if (statusString === 'onsite' || statusString === 'scheduled') {
        badgeClass += ' text-white';
        badgeStyle.backgroundColor = 'var(--primary-dark)';
      } else if (statusString === 'pending') {
        badgeClass += ' bg-white text-black border border-black';
      } else {
        badgeClass += ' bg-black text-white';
      }
    }

    const displayText = statusString.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    return <span className={badgeClass} style={badgeStyle}>{displayText}</span>;
  };

  const getPriorityBadge = (priority) => {
    // Handle null/undefined priority with safe string conversion
    const priorityString = String(priority || 'medium').toLowerCase();
    let badgeClass = 'badge px-2 py-1 rounded text-xs font-medium text-white';
    const badgeStyle = {};

    if (priorityString === 'high') {
      badgeClass += ' bg-black';
    } else if (priorityString === 'medium') {
      badgeStyle.backgroundColor = 'var(--primary-color)';
    } else {
      badgeStyle.backgroundColor = 'var(--primary-light)';
    }

    const displayText = priorityString.charAt(0).toUpperCase() + priorityString.slice(1);
    return <span className={badgeClass} style={badgeStyle}>{displayText}</span>;
  };

  const getPaymentStatusBadge = (status) => {
    // Handle null/undefined status with safe string conversion
    const statusString = String(status || 'pending').toLowerCase();
    let badgeClass = 'badge px-2 py-1 rounded text-xs font-medium';
    const badgeStyle = {};

    if (statusString === 'paid') {
      badgeClass += ' text-white';
      badgeStyle.backgroundColor = 'var(--primary-color)';
    } else if (statusString === 'partial') {
      badgeClass += ' text-white';
      badgeStyle.backgroundColor = 'var(--primary-light)';
    } else if (statusString === 'overdue') {
      badgeClass += ' bg-black text-white';
    } else {
      badgeClass += ' bg-white text-black border border-black';
    }

    const displayText = statusString.charAt(0).toUpperCase() + statusString.slice(1);
    return <span className={badgeClass} style={badgeStyle}>{displayText}</span>;
  };

  const formatCallTypeDisplay = (callType) => {
    const displayMapping = {
      'free_call': 'Free Call',
      'amc_call': 'AMC Call',
      'per_call': 'Per Call',
      // Legacy support for old values
      'free call': 'Free Call',
      'amc call': 'AMC Call',
      'per call': 'Per Call'
    };
    return displayMapping[callType] || 'Free Call';
  };

  const getCallTypeBadge = (callType) => {
    const displayText = formatCallTypeDisplay(callType);
    let badgeClass = 'badge px-2 py-1 rounded text-xs font-medium text-white';
    const badgeStyle = {};
    let emoji = '';

    if (callType === 'free_call' || callType === 'free call') {
      badgeStyle.backgroundColor = 'var(--primary-color)';
      emoji = '';
    } else if (callType === 'amc_call' || callType === 'amc call') {
      badgeStyle.backgroundColor = 'var(--primary-light)';
      emoji = '';
    } else if (callType === 'per_call' || callType === 'per call') {
      badgeClass += ' bg-black';
      emoji = '';
    } else {
      badgeStyle.backgroundColor = 'var(--primary-color)';
      emoji = '';
    }

    return (
      <span className={badgeClass} style={badgeStyle}>
        {emoji} {displayText}
      </span>
    );
  };

  const formatCreatedDateTime = (dateTimeString) => {
    if (!dateTimeString) return 'Not available';

    try {
      const date = new Date(dateTimeString);

      // Format date as: Dec 11, 2024 at 8:53 PM
      const dateOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };

      const timeOptions = {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      };

      const formattedDate = date.toLocaleDateString('en-US', dateOptions);
      const formattedTime = date.toLocaleTimeString('en-US', timeOptions);

      return `${formattedDate} at ${formattedTime}`;
    } catch (error) {
      console.error('Error formatting date time:', error);
      return 'Invalid date';
    }
  };

  // Timer history helper functions
  const getEventIcon = (eventType) => {
    switch (eventType) {
      case 'timer_started':
        return <FaPlayCircle className="text-green-600" />;
      case 'timer_resumed':
        return <FaPlayCircle className="text-blue-600" />;
      case 'timer_paused':
        return <FaPauseCircle className="text-yellow-600" />;
      case 'timer_completed':
        return <FaCheckCircle className="text-green-600" />;
      case 'timer_stopped':
      case 'timer_cancelled':
        return <FaTimesCircle className="text-red-600" />;
      default:
        return <FaClock className="text-gray-600" />;
    }
  };

  const getEventColor = (eventType) => {
    switch (eventType) {
      case 'timer_started':
        return 'success';
      case 'timer_resumed':
        return 'info';
      case 'timer_paused':
        return 'warning';
      case 'timer_completed':
        return 'success';
      case 'timer_stopped':
      case 'timer_cancelled':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const formatEventType = (eventType) => {
    return eventType.replace('timer_', '').replace('_', ' ').toUpperCase();
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center" style={{ height: '400px' }}>
        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600" role="status">
          <span className="sr-only">Loading...</span>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="w-full px-4">
        <div className="p-4 rounded-md border bg-white border-black text-black" role="alert">
          Service request not found.
        </div>
      </div>
    );
  }

  return (
    <div className="w-full px-2 sm:px-4">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold mb-2 truncate">{service.serviceNumber}</h2>
              <p className="text-black mb-3 text-sm sm:text-base">{service.customer} • {service.type}</p>
              <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                {getStatusBadge(service.status)}
                {getPriorityBadge(service.priority)}
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white" style={{ backgroundColor: 'var(--primary-color)' }}>{service.category}</span>
                <small className="text-black flex text-base items-center">
                  <FaCalendar className="mr-1" />
                  <span className="hidden sm:inline">Created &nbsp;</span>
                  <span className="font-medium">{formatCreatedDateTime(service.createdDateTime)}</span>
                </small>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full lg:w-auto">
              {/* Status Action Buttons */}
              {(service.status?.name?.toLowerCase() === 'pending' || service.status?.code === 'PENDING') && (
                <button
                  className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                  style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                  onClick={() => handleStatusUpdate('in-progress')}
                >
                  <FaPlayCircle className="mr-1" />
                  <span className="hidden xs:inline">Start Service</span>
                  <span className="xs:hidden">Start</span>
                </button>
              )}
              {(service.status?.name?.toLowerCase() === 'in progress' || service.status?.code === 'ON_PROCESS') && (
                <>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-black text-white border-black hover:bg-gray-800 focus:ring-black w-full sm:w-auto"
                    onClick={() => handleStatusUpdate('on-hold')}
                  >
                    <FaPauseCircle className="mr-1" />
                    Hold
                  </button>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                    style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    <FaCheckCircle className="mr-1" />
                    Complete
                  </button>
                </>
              )}
              {(service.status?.name?.toLowerCase() === 'on hold' || service.status?.code === 'ON_HOLD') && (
                <>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                    style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                    onClick={() => handleStatusUpdate('in-progress')}
                  >
                    <FaPlayCircle className="mr-1" />
                    Resume
                  </button>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                    style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    <FaCheckCircle className="mr-1" />
                    Complete
                  </button>
                </>
              )}
              {(service.status?.name?.toLowerCase() === 'open' || service.status?.code === 'OPEN') && (
                <>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                    style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                    onClick={() => handleStatusUpdate('in-progress')}
                  >
                    <FaPlayCircle className="mr-1" />
                    Resume
                  </button>
                  <button
                    className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto"
                    style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    <FaCheckCircle className="mr-1" />
                    Complete
                  </button>
                </>
              )}

              <button
                className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 w-full sm:w-auto"
                style={{ borderColor: 'var(--primary-color)', color: 'var(--primary-color)' }}
                onClick={() => setShowTimerHistory(true)}
              >
                <FaHistory className="mr-1" />
                <span className="hidden sm:inline">Timer History</span>
                <span className="sm:hidden">History</span>
              </button>
              <Link
                to={`/services/${service.id}/edit`}
                className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 w-full sm:w-auto"
                style={{ borderColor: 'var(--primary-color)', color: 'var(--primary-color)' }}
              >
                <FaEdit className="mr-1" />
                Edit
              </Link>
              <button
                className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border-black text-black hover:bg-gray-50 focus:ring-black w-full sm:w-auto"
                onClick={handleDelete}
              >
                <FaTrash className="mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
        <div className="col-span-1">
          <div className="rounded-lg shadow-sm border border-gray-200 text-white" style={{ backgroundColor: 'var(--primary-color)' }}>
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-0 text-sm sm:text-base lg:text-lg font-bold truncate">
                    {formatHoursDisplay(getDisplayTime(), service.totalTimeMinutes)}h
                  </h4>
                  <p className="mb-0 text-xs sm:text-sm opacity-90">Hours Spent</p>
                  {getDisplayTime() > 0 && (
                    <p className="text-xs opacity-75 mb-0 hidden sm:block">
                      {formatTimeDuration(getDisplayTime())}
                    </p>
                  )}
                </div>
                <FaClock size={16} className="sm:w-6 sm:h-6 flex-shrink-0 ml-2" />
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="rounded-lg shadow-sm border border-gray-200 text-white" style={{ backgroundColor: 'var(--primary-color)' }}>
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-0 text-sm sm:text-base lg:text-lg font-bold truncate">{service.estimatedHours}h</h4>
                  <p className="mb-0 text-xs sm:text-sm opacity-90">Estimated</p>
                </div>
                <FaCalendar size={16} className="sm:w-6 sm:h-6 flex-shrink-0 ml-2" />
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="rounded-lg shadow-sm border border-gray-200 text-white" style={{ backgroundColor: 'var(--primary-color)' }}>
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-0 text-sm sm:text-base lg:text-lg font-bold truncate">
                    {service.amount > 0 ? `₹${service.amount.toLocaleString()}` : 'Not Set'}
                  </h4>
                  <p className="mb-0 text-xs sm:text-sm opacity-90">Amount</p>
                </div>
                <FaRupeeSign size={16} className="sm:w-6 sm:h-6 flex-shrink-0 ml-2" />
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-1">
          <div className="rounded-lg shadow-sm border border-gray-200 bg-black text-white">
            <div className="p-3 sm:p-4">
              <div className="flex justify-between items-center">
                <div className="min-w-0 flex-1">
                  <h4 className="mb-0 text-sm sm:text-base lg:text-lg font-bold truncate">
                    {service.estimatedHours > 0
                      ? Math.round((service.actualHours / service.estimatedHours) * 100)
                      : service.actualHours > 0 ? 100 : 0}%
                  </h4>
                  <p className="mb-0 text-xs sm:text-sm opacity-90">Progress</p>
                </div>
                <FaTools size={16} className="sm:w-6 sm:h-6 flex-shrink-0 ml-2" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-4 sm:mb-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <ul className="flex flex-wrap border-b border-gray-200">
            <li className="flex-1 min-w-0">
              <button
                className={`w-full px-3 sm:px-4 py-3 text-xs sm:text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'details'
                    ? 'border-transparent text-black hover:text-black hover:border-gray-300'
                    : 'border-transparent text-black hover:text-black hover:border-gray-300'
                }`}
                style={activeTab === 'details' ? { borderBottomColor: 'var(--primary-color)', color: 'var(--primary-color)', backgroundColor: 'var(--theme-content-bg)' } : {}}
                onClick={() => setActiveTab('details')}
              >
                <span className="hidden sm:inline">Service </span>Details
              </button>
            </li>
            <li className="flex-1 min-w-0">
              <button
                className={`w-full px-3 sm:px-4 py-3 text-xs sm:text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'progress'
                    ? 'border-transparent text-black hover:text-black hover:border-gray-300'
                    : 'border-transparent text-black hover:text-black hover:border-gray-300'
                }`}
                style={activeTab === 'progress' ? { borderBottomColor: 'var(--primary-color)', color: 'var(--primary-color)', backgroundColor: 'var(--theme-content-bg)' } : {}}
                onClick={() => setActiveTab('progress')}
              >
                Progress ({service.progress.length})
              </button>
            </li>
            <li className="flex-1 min-w-0">
              <button
                className={`w-full px-3 sm:px-4 py-3 text-xs sm:text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'attachments'
                    ? 'border-transparent text-black hover:text-black hover:border-gray-300'
                    : 'border-transparent text-black hover:text-black hover:border-gray-300'
                }`}
                style={activeTab === 'attachments' ? { borderBottomColor: 'var(--primary-color)', color: 'var(--primary-color)', backgroundColor: 'var(--theme-content-bg)' } : {}}
                onClick={() => setActiveTab('attachments')}
              >
                Files ({service.attachments.length})
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          <div className="p-4">
            {/* Real-time timer using backend data */}
            <RealTimeTimer />

            {activeTab === 'details' && (
              <div className="space-y-4 sm:space-y-6">
                {/* Service Information */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                      <FaTools className="mr-2 text-sm sm:text-base" />
                      Service Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <strong className="text-sm text-black">Service Type:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">{service.type}</p>
                        </div>
                        <div>
                          <strong className="text-sm text-black">Call Type:</strong>
                          <div className="mt-1">
                            {getCallTypeBadge(service.callType)}
                          </div>
                        </div>
                        <div>
                          <strong className="text-sm text-black">Service Number:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">{service.serviceNumber}</p>
                        </div>
                        <div>
                          <strong className="text-sm text-black">Status:</strong>
                          <div className="mt-1">
                            {getStatusBadge(service.status)}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <strong className="text-sm text-black">Type of Call:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">{service.category}</p>
                        </div>
                        <div>
                          <strong className="text-sm text-black">Priority:</strong>
                          <p className="mb-0 text-sm sm:text-base capitalize text-black">{service.priority}</p>
                        </div>
                        <div>
                          <strong className="text-sm text-black">Created Date & Time:</strong>
                          <p className="mb-0 text-sm sm:text-base font-medium text-blue-600">
                            {formatCreatedDateTime(service.createdDateTime)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* AMC Details - Show only when call type is AMC Call */}
                    {(service.callType === 'amc_call' || service.category === 'AMC Call') && amcDetails && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <h5 className="text-sm font-semibold text-blue-700 mb-3 flex items-center">
                            <FaCheckCircle className="mr-2" />
                            AMC Contract Details
                          </h5>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <strong className="text-xs text-blue-600">AMC Expiry Date:</strong>
                              <p className="mb-0 text-sm text-gray-800 bg-white px-3 py-2 rounded border mt-1">
                                {amcDetails.expiryDate ? new Date(amcDetails.expiryDate).toLocaleDateString() : 'N/A'}
                              </p>
                            </div>
                            <div>
                              <strong className="text-xs text-blue-600">Renewal Date:</strong>
                              <p className="mb-0 text-sm text-gray-800 bg-white px-3 py-2 rounded border mt-1">
                                {amcDetails.renewalDate ? new Date(amcDetails.renewalDate).toLocaleDateString() : 'N/A'}
                              </p>
                            </div>
                            <div>
                              <strong className="text-xs text-blue-600">Next Visit Date:</strong>
                              <p className="mb-0 text-sm text-gray-800 bg-white px-3 py-2 rounded border mt-1">
                                {amcDetails.nextVisit ? new Date(amcDetails.nextVisit).toLocaleDateString() : 'N/A'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Additional fields */}
                    {(service.subject || service.description || service.callDate || service.scheduledDate || service.bookingDate || service.scheduledTime || service.callStartTime || service.callEndTime || service.estimatedHours || service.customerReportedIssue || service.requirements || service.notes) && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="space-y-3">
                          {service.subject && (
                            <div>
                              <strong className="text-sm text-black">Subject:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.subject}</p>
                            </div>
                          )}
                          {service.description && (
                            <div>
                              <strong className="text-sm text-black">Description:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.description}</p>
                            </div>
                          )}

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <div>
                              <strong className="text-sm text-black">Call Date:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.callDate ? new Date(service.callDate).toLocaleDateString() : 'N/A'}</p>
                            </div>
                            {service.scheduledDate && (
                              <div>
                                <strong className="text-sm text-black">Scheduled Date:</strong>
                                <p className="mb-0 text-sm sm:text-base text-black">{new Date(service.scheduledDate).toLocaleDateString()}</p>
                              </div>
                            )}
                            {service.bookingDate && (
                              <div>
                                <strong className="text-sm text-black">Booking Date:</strong>
                                <p className="mb-0 text-sm sm:text-base text-black">{new Date(service.bookingDate).toLocaleDateString()}</p>
                              </div>
                            )}
                            {service.scheduledTime && (
                              <div>
                                <strong className="text-sm text-black">Scheduled Time:</strong>
                                <p className="mb-0 text-sm sm:text-base text-black">{service.scheduledTime}</p>
                              </div>
                            )}
                            {service.callStartTime && (
                              <div>
                                <strong className="text-sm text-black">Call Start Time:</strong>
                                <p className="mb-0 text-sm sm:text-base text-black">{service.callStartTime}</p>
                              </div>
                            )}
                            {service.callEndTime && (
                              <div>
                                <strong className="text-sm text-black">Call End Time:</strong>
                                <p className="mb-0 text-sm sm:text-base text-black">{service.callEndTime}</p>
                              </div>
                            )}
                            <div>
                              <strong className="text-sm text-black">Estimated Hours:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.estimatedHours}h</p>
                            </div>
                            <div>
                              <strong className="text-sm text-black">Actual Hours:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">
                                {formatHoursDisplay(getDisplayTime(), service.totalTimeMinutes)}h
                                {getDisplayTime() > 0 && (
                                  <span className="text-xs text-black block">
                                    ({formatTimeDuration(getDisplayTime())})
                                  </span>
                                )}
                              </p>
                            </div>
                          </div>

                          {service.customerReportedIssue && (
                            <div>
                              <strong className="text-sm text-black">Customer Reported Issue:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.customerReportedIssue}</p>
                            </div>
                          )}
                          {service.requirements && (
                            <div>
                              <strong className="text-sm text-black">Requirements:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.requirements}</p>
                            </div>
                          )}
                          {service.notes && (
                            <div>
                              <strong className="text-sm text-black">Notes:</strong>
                              <p className="mb-0 text-sm sm:text-base text-black">{service.notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Customer & Contact Information */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                      <FaUser className="mr-2 text-sm sm:text-base" />
                      Customer & Contact
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="space-y-3">
                      <div>
                        <strong className="text-sm text-black">Customer:</strong>
                        <p className="mb-0 text-sm sm:text-base">
                          <Link to={`/customers/${service.customerId}`} className="no-underline" style={{ color: 'var(--primary-color)' }}>
                            {service.customer}
                          </Link>
                        </p>
                      </div>

                      <div>
                        <strong className="text-sm text-black">Contact Person:</strong>
                        <p className="mb-0 text-sm sm:text-base text-black">{service.contactPerson}</p>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <FaPhone className="text-black mr-2 text-sm" />
                          <strong className="text-sm text-black">Phone:</strong>
                        </div>
                        <p className="mb-0 ml-6 text-sm sm:text-base">
                          <a href={`tel:${service.contactPhone}`} style={{ color: 'var(--primary-color)' }}>
                            {service.contactPhone}
                          </a>
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <FaEnvelope className="text-black mr-2 text-sm" />
                          <strong className="text-sm text-black">Email:</strong>
                        </div>
                        <p className="mb-0 ml-6 text-sm sm:text-base">
                          <a href={`mailto:${service.contactEmail}`} style={{ color: 'var(--primary-color)' }}>
                            {service.contactEmail}
                          </a>
                        </p>
                      </div>

                      {service.serviceLocation === 'customer-site' && (
                        <div>
                          <div className="flex items-center mb-1">
                            <FaMapMarkerAlt className="text-black mr-2 text-sm" />
                            <strong className="text-sm text-black">Service Address:</strong>
                          </div>
                          <p className="mb-0 ml-6 text-sm sm:text-base text-black">
                            {service.address}<br />
                            {service.city}, {service.state}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Technician Information */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                      <FaUser className="mr-2 text-sm sm:text-base" />
                      Assigned Executive
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="space-y-3">
                      <div>
                        <strong className="text-sm text-black">Technician:</strong>
                        <p className="mb-0 text-sm sm:text-base text-black">{service.assignedTo}</p>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <FaPhone className="text-black mr-2 text-sm" />
                          <strong className="text-sm text-black">Phone:</strong>
                        </div>
                        <p className="mb-0 ml-6 text-sm sm:text-base">
                          <a href={`tel:${service.technicianPhone}`} style={{ color: 'var(--primary-color)' }}>
                            {service.technicianPhone}
                          </a>
                        </p>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <FaEnvelope className="text-black mr-2 text-sm" />
                          <strong className="text-sm text-black">Email:</strong>
                        </div>
                        <p className="mb-0 ml-6 text-sm sm:text-base">
                          <a href={`mailto:${service.technicianEmail}`} style={{ color: 'var(--primary-color)' }}>
                            {service.technicianEmail}
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tally & Technical Details */}
                {(service.tallySerialNumber || service.tallyVersion || service.companyName || service.designation || service.tssStatus || service.tssExpiry || service.mobileNumber2) && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="px-4 py-3 border-b border-gray-200 bg-white">
                      <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                        <FaTools className="mr-2 text-sm sm:text-base" />
                        Tally & Technical Details
                      </h5>
                    </div>
                    <div className="p-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {service.tallySerialNumber && (
                          <div>
                            <strong className="text-sm text-black">Tally Serial Number:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.tallySerialNumber}</p>
                          </div>
                        )}
                        {service.tallyVersion && (
                          <div>
                            <strong className="text-sm text-black">Tally Version:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.tallyVersion}</p>
                          </div>
                        )}
                        {service.designation && (
                          <div>
                            <strong className="text-sm text-black">Designation:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.designation}</p>
                          </div>
                        )}
                        {service.tssStatus && (
                          <div>
                            <strong className="text-sm text-black">TSS Status:</strong>
                            <p className="mb-0">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                service.tssStatus === 'active' ? 'text-white' : 'bg-black text-white'
                              }`} style={service.tssStatus === 'active' ? { backgroundColor: 'var(--primary-color)' } : {}}
                              >
                                {service.tssStatus}
                              </span>
                            </p>
                          </div>
                        )}
                        {service.tssExpiry && (
                          <div>
                            <strong className="text-sm text-black">TSS Expiry:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{new Date(service.tssExpiry).toLocaleDateString()}</p>
                          </div>
                        )}
                        {service.mobileNumber2 && (
                          <div>
                            <strong className="text-sm text-black">Secondary Mobile:</strong>
                            <p className="mb-0 text-sm sm:text-base">
                              <a href={`tel:${service.mobileNumber2}`} style={{ color: 'var(--primary-color)' }}>
                                {service.mobileNumber2}
                              </a>
                            </p>
                          </div>
                        )}
                      </div>
                      {service.companyName && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <strong className="text-sm text-black">Company Name:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">{service.companyName}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Financial Information */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                      <FaRupeeSign className="mr-2 text-sm sm:text-base" />
                      Financial Information
                    </h5>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <div>
                        <strong className="text-sm text-black">Service Amount:</strong>
                        <p className="mb-0 text-sm sm:text-base text-black">
                          {service.amount > 0 ? `₹${service.amount.toLocaleString()}` :
                          <span className="text-black italic">Not set</span>}
                        </p>
                      </div>
                      <div>
                        <strong className="text-sm text-black">Currency:</strong>
                        <p className="mb-0 text-sm sm:text-base text-black">{service.currency}</p>
                      </div>
                      <div>
                        <strong className="text-sm text-black">Charging Type:</strong>
                        <p className="mb-0 text-sm sm:text-base capitalize text-black">{service.chargingType}</p>
                      </div>
                      {service.hourlyRate > 0 && (
                        <div>
                          <strong className="text-sm text-black">Hourly Rate:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">₹{service.hourlyRate.toLocaleString()}</p>
                        </div>
                      )}
                      {service.travelCharges > 0 && (
                        <div>
                          <strong className="text-sm text-black">Travel Charges:</strong>
                          <p className="mb-0 text-sm sm:text-base text-black">₹{service.travelCharges.toLocaleString()}</p>
                        </div>
                      )}
                    </div>
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <strong className="text-sm text-black">Payment Status:</strong>
                      <div className="mt-1">
                        {getPaymentStatusBadge(service.paymentStatus)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'progress' && (
              <div className="space-y-4 sm:space-y-6">
                {/* Timer History Section */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                      <FaClock className="mr-2 text-sm sm:text-base" />
                      Timer History & Summary
                    </h5>
                  </div>
                  <div className="p-4">
                    {timerHistoryLoading ? (
                      <div className="text-center py-4">
                        <div className="animate-spin rounded-full border-2 border-gray-300 border-t-current text-primary-600 mx-auto" style={{ width: '24px', height: '24px' }}>
                          <span className="sr-only">Loading...</span>
                        </div>
                        <p className="mt-2 text-sm text-gray-600">Loading timer history...</p>
                      </div>
                    ) : timerHistoryData ? (
                      <div className="space-y-6">
                        {/* Timer Summary */}
                        {timerHistoryData.timer_summary && (
                          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <h6 className="text-sm font-semibold text-black mb-3 flex items-center">
                              <FaClock className="mr-2" />
                              Timer Summary
                            </h6>
                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                              <div className="text-center">
                                <div className="text-lg font-bold text-black mb-1" style={{ color: 'var(--primary-color)' }}>
                                  {timerHistoryData.timer_summary.total_time_formatted || '00:00:00'}
                                </div>
                                <small className="text-xs text-gray-600">Total Time</small>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-bold text-black mb-1">
                                  {timerHistoryData.timer_summary.current_status || 'Unknown'}
                                </div>
                                <small className="text-xs text-gray-600">Current Status</small>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-bold text-black mb-1" style={{ color: 'var(--primary-color)' }}>
                                  {timerHistoryData.statistics?.total_sessions || 0}
                                </div>
                                <small className="text-xs text-gray-600">Total Sessions</small>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-bold text-black mb-1" style={{ color: 'var(--primary-color)' }}>
                                  {timerHistoryData.statistics?.pause_events || 0}
                                </div>
                                <small className="text-xs text-gray-600">Pause/Resume Cycles</small>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Event Statistics */}
                        {timerHistoryData.statistics && (
                          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                            <h6 className="text-sm font-semibold text-black mb-3">Event Statistics</h6>
                            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                              <div className="text-center">
                                <span className="inline-block px-3 py-1 rounded-full text-xs font-medium text-white" style={{ backgroundColor: 'var(--primary-color)' }}>
                                  {timerHistoryData.statistics.start_events} Starts
                                </span>
                              </div>
                              <div className="text-center">
                                <span className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-500 text-white">
                                  {timerHistoryData.statistics.pause_events} Pauses
                                </span>
                              </div>
                              <div className="text-center">
                                <span className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                                  {timerHistoryData.statistics.resume_events} Resumes
                                </span>
                              </div>
                              <div className="text-center">
                                <span className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-500 text-white">
                                  {timerHistoryData.statistics.stop_events} Stops
                                </span>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Timer Events Timeline */}
                        <div>
                          <h6 className="text-sm font-semibold text-black mb-3">Timer Events Timeline</h6>
                          {timerHistoryData.timer_history && timerHistoryData.timer_history.length > 0 ? (
                            <div className="space-y-3">
                              {timerHistoryData.timer_history.map((event, index) => (
                                <div key={event.id || index} className="border-l-4 pl-4 py-2" style={{ borderLeftColor: getEventColor(event.event_type) === 'success' ? 'var(--primary-color)' : getEventColor(event.event_type) === 'warning' ? '#fbbf24' : getEventColor(event.event_type) === 'info' ? '#3b82f6' : '#ef4444' }}>
                                  <div className="bg-white rounded-lg p-3 border border-gray-200">
                                    <div className="flex justify-between items-start">
                                      <div className="flex items-center flex-1">
                                        <div className="mr-3">
                                          {getEventIcon(event.event_type)}
                                        </div>
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2 mb-1">
                                            <span className="inline-block px-2 py-1 rounded text-xs font-medium text-white" style={{ backgroundColor: getEventColor(event.event_type) === 'success' ? 'var(--primary-color)' : getEventColor(event.event_type) === 'warning' ? '#f59e0b' : getEventColor(event.event_type) === 'info' ? '#3b82f6' : '#ef4444' }}>
                                              {formatEventType(event.event_type)}
                                            </span>
                                            <span className="text-sm text-gray-600">
                                              {event.status_from} → {event.status_to}
                                            </span>
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            {formatTimestamp(event.timestamp)}
                                          </div>
                                          {event.event_description && (
                                            <div className="text-xs text-gray-600 mt-1">
                                              {event.event_description}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      <div className="text-right ml-4">
                                        {event.duration_formatted && event.duration_formatted !== '00:00:00' && (
                                          <div className="text-sm font-medium text-black">
                                            {event.duration_formatted}
                                          </div>
                                        )}
                                        {event.cumulative_time_formatted && (
                                          <div className="text-xs text-gray-500">
                                            Total: {event.cumulative_time_formatted}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="text-center text-gray-500 py-8">
                              <FaClock size={48} className="mx-auto mb-3 opacity-50" />
                              <p>No timer history available</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        <FaClock size={48} className="mx-auto mb-3 opacity-50" />
                        <p>Timer history not available</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Service Progress Timeline */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <div className="px-4 py-3 border-b border-gray-200 bg-white">
                    <h5 className="text-base sm:text-lg font-semibold text-black mb-0">Service Progress Timeline</h5>
                  </div>
                  <div className="p-4">
                    <div className="timeline">
                      {service.progress.map((item) => (
                        <div key={item.id} className="timeline-item">
                          <div className="timeline-marker" style={{ backgroundColor: 'var(--primary-color)' }}></div>
                          <div className="timeline-content">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
                              <div className="flex-1 min-w-0">
                                <h6 className="mb-1 text-sm sm:text-base font-medium text-black">{item.action}</h6>
                                <p className="mb-1 text-sm text-black">{item.description}</p>
                                <small className="text-xs text-black">by {item.user}</small>
                              </div>
                              <small className="text-xs text-black sm:text-right flex-shrink-0">
                                {new Date(item.timestamp).toLocaleString()}
                              </small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Issues & Solutions */}
                {(service.actualIssueFound || service.solutionProvided || service.executiveRemarks || service.customerFeedbackComments) && (
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div className="px-4 py-3 border-b border-gray-200 bg-white">
                      <h5 className="text-base sm:text-lg font-semibold text-black mb-0 flex items-center">
                        <FaTools className="mr-2 text-sm sm:text-base" />
                        Issues & Solutions
                      </h5>
                    </div>
                    <div className="p-4">
                      <div className="space-y-3">
                        {service.actualIssueFound && (
                          <div>
                            <strong className="text-sm text-black">Actual Issue Found:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.actualIssueFound}</p>
                          </div>
                        )}
                        {service.solutionProvided && (
                          <div>
                            <strong className="text-sm text-black">Solution Provided:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.solutionProvided}</p>
                          </div>
                        )}
                        {service.executiveRemarks && (
                          <div>
                            <strong className="text-sm text-black">Executive Remarks:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.executiveRemarks}</p>
                          </div>
                        )}
                        {service.customerFeedbackType && (
                          <div>
                            <strong className="text-sm text-black">Customer Feedback Type:</strong>
                            <p className="mb-0 text-sm sm:text-base capitalize text-black">{service.customerFeedbackType}</p>
                          </div>
                        )}
                        {service.customerFeedbackComments && (
                          <div>
                            <strong className="text-sm text-black">Customer Feedback:</strong>
                            <p className="mb-0 text-sm sm:text-base text-black">{service.customerFeedbackComments}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'attachments' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="px-4 py-3 border-b border-gray-200 bg-white flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                  <h5 className="text-base sm:text-lg font-semibold text-black mb-0">Service Attachments</h5>
                  <button className="inline-flex items-center justify-center px-3 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white w-full sm:w-auto" style={{ backgroundColor: 'var(--primary-color)', borderColor: 'var(--primary-color)' }}>
                    Upload File
                  </button>
                </div>
                <div className="p-4">
                  {/* Mobile Card View */}
                  <div className="block sm:hidden space-y-3">
                    {service.attachments.map(file => (
                      <div key={file.id} className="bg-white rounded-lg p-3 border border-gray-200">
                        <div className="flex justify-between items-start mb-2">
                          <h6 className="font-medium text-sm text-black truncate flex-1 mr-2">{file.name}</h6>
                          <span className="text-xs text-black flex-shrink-0">{file.size}</span>
                        </div>
                        <div className="text-xs text-black mb-3">
                          <div>Uploaded by: {file.uploadedBy}</div>
                          <div>Date: {new Date(file.uploadedDate).toLocaleDateString()}</div>
                        </div>
                        <div className="flex gap-2">
                          <button className="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2" style={{ borderColor: 'var(--primary-color)', color: 'var(--primary-color)' }}>
                            Download
                          </button>
                          <button className="flex-1 inline-flex items-center justify-center px-3 py-2 text-xs font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border-black text-black hover:bg-gray-50 focus:ring-black">
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Desktop Table View */}
                  <div className="hidden sm:block overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-white">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">File Name</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Size</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Uploaded By</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Upload Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-black uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {service.attachments.map(file => (
                          <tr key={file.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-black">{file.name}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{file.size}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{file.uploadedBy}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-black">{new Date(file.uploadedDate).toLocaleDateString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="inline-flex rounded-md shadow-sm gap-1" role="group">
                                <button className="inline-flex items-center px-3 py-1 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2" style={{ borderColor: 'var(--primary-color)', color: 'var(--primary-color)' }}>
                                  Download
                                </button>
                                <button className="inline-flex items-center px-3 py-1 text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 border-black text-black hover:bg-gray-50 focus:ring-black">
                                  Delete
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Timer History Modal */}
      <TimerHistoryModal
        isOpen={showTimerHistory}
        onClose={() => setShowTimerHistory(false)}
        serviceCallId={service.id}
        serviceCallNumber={service.serviceNumber}
      />
    </div>
  );
};

export default ServiceDetails;
