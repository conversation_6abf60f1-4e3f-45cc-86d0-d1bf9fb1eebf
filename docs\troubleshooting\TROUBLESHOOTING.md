# Troubleshooting Guide - Enhanced Service Form

## Issue: 500 Internal Server Error on Service Calls API

### Quick Fix Steps

1. **Check Database Connection:**
   ```bash
   # Test the debug endpoint
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        http://localhost:8080/api/v1/service-calls/debug
   ```

2. **Run the Migration:**
   ```bash
   # Option A: Use the migration runner script
   node run-migration.js
   
   # Option B: Use the backend migration command
   cd backend
   npm run migrate
   ```

3. **Restart Backend Server:**
   ```bash
   cd backend
   npm run dev
   ```

### Detailed Troubleshooting

#### 1. Database Migration Issues

**Problem:** Migration hasn't been run or failed
**Solution:**
```bash
# Check if migration files exist
ls backend/src/migrations/024-*

# Run migration manually
cd backend
npm run migrate

# If migration fails, check database connection
psql -h localhost -U postgres -d tallycrm -c "\dt"
```

**Expected Tables After Migration:**
- `type_of_calls` (new table)
- `service_calls` (with new columns)

#### 2. Model Association Issues

**Problem:** TypeOfCall model not found
**Solution:**
```javascript
// Check if models are loaded correctly
console.log('Available models:', Object.keys(models));
console.log('TypeOfCall exists:', !!models.TypeOfCall);
```

#### 3. Database Schema Issues

**Problem:** New columns don't exist in service_calls table
**Check:**
```sql
-- Connect to database
psql -h localhost -U postgres -d tallycrm

-- Check service_calls table structure
\d service_calls

-- Look for new columns
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'service_calls' 
AND column_name IN ('type_of_call_id', 'contact_number', 'executive_remarks');
```

#### 4. API Route Issues

**Problem:** Wrong API endpoint
**Check:**
- Frontend is calling: `http://localhost:8080/api/v1/service-calls`
- Backend expects: `/api/service-calls` (without v1)

**Fix:** Update frontend API base URL or backend routes

#### 5. Authentication Issues

**Problem:** Missing or invalid JWT token
**Check:**
```bash
# Test with valid token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     http://localhost:8080/api/service-calls/debug
```

### Common Error Messages and Solutions

#### Error: "column does not exist"
```
ERROR: column "type_of_call_id" does not exist
```
**Solution:** Run the migration to add new columns

#### Error: "relation type_of_calls does not exist"
```
ERROR: relation "type_of_calls" does not exist
```
**Solution:** Run the migration to create the table

#### Error: "Cannot read property 'tenant' of undefined"
```
TypeError: Cannot read property 'tenant' of undefined
```
**Solution:** Check authentication middleware and JWT token

### Manual Database Setup (If Migration Fails)

If the migration script fails, you can run the SQL manually:

```sql
-- 1. Create type_of_calls table
CREATE TABLE type_of_calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(50) DEFAULT 'support',
    service_type VARCHAR(50) DEFAULT 'both',
    is_billable BOOLEAN DEFAULT true,
    default_duration INTEGER,
    requires_approval BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. Add new columns to service_calls
ALTER TABLE service_calls ADD COLUMN type_of_call_id UUID REFERENCES type_of_calls(id);
ALTER TABLE service_calls ADD COLUMN contact_number VARCHAR(255);
ALTER TABLE service_calls ADD COLUMN tally_serial_number VARCHAR(255);
ALTER TABLE service_calls ADD COLUMN company_name VARCHAR(255);
ALTER TABLE service_calls ADD COLUMN designation VARCHAR(255);
ALTER TABLE service_calls ADD COLUMN tally_version VARCHAR(255);
ALTER TABLE service_calls ADD COLUMN executive_remarks TEXT;
ALTER TABLE service_calls ADD COLUMN customer_feedback_type VARCHAR(50);
ALTER TABLE service_calls ADD COLUMN customer_feedback_comments TEXT;

-- 3. Insert default data
INSERT INTO type_of_calls (id, name, code, description, category, service_type, sort_order) VALUES
('550e8400-e29b-41d4-a716-************', 'AMC Visit', 'AMC_VISIT', 'Annual Maintenance Contract visit', 'amc', 'onsite', 1),
('550e8400-e29b-41d4-a716-************', 'TSS Visit', 'TSS_VISIT', 'Tally Software Services visit', 'tss', 'onsite', 2),
('550e8400-e29b-41d4-a716-************', 'Computer Repair', 'COMPUTER_REPAIR', 'Computer hardware/software repair', 'support', 'onsite', 3);
```

### Testing After Fix

1. **Test Debug Endpoint:**
   ```bash
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        http://localhost:8080/api/service-calls/debug
   ```

2. **Test Service Calls List:**
   ```bash
   curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
        http://localhost:8080/api/service-calls?page=1&limit=10
   ```

3. **Test Enhanced Form:**
   - Navigate to: `http://localhost:3000/services/add`
   - Check browser console for errors
   - Verify form loads with all sections

### Environment Variables Check

Ensure these are set in your `.env` file:

```env
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm
DB_USER=postgres
DB_PASSWORD=your_password

# Server
PORT=8080
NODE_ENV=development

# JWT
JWT_SECRET=your_jwt_secret
```

### Contact Support

If issues persist:

1. **Check Backend Logs:** Look for detailed error messages in the server console
2. **Check Frontend Console:** Look for network errors in browser dev tools
3. **Database Logs:** Check PostgreSQL logs for connection issues
4. **Share Error Details:** Include the full error message and stack trace

### Quick Recovery Commands

```bash
# Reset and restart everything
cd backend
npm run migrate:reset  # If this command exists
npm run migrate
npm run dev

# In another terminal
cd frontend
npm run dev
```

This should resolve the 500 error and get your enhanced service form working!
