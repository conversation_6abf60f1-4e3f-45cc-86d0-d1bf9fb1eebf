#!/usr/bin/env node

/**
 * Complete Data Seeding Script
 * Seeds all default data for TallyCRM
 */

import { logger } from './src/utils/logger.js';

// Import all seeders
import seedAdminUser from './src/seeders/000-seed-admin-user.js';
import seedSubscriptionPlans from './src/seeders/002-seed-subscription-plans.js';
import seedMasterData from './src/seeders/003-seed-master-data.js';
import seedSampleData from './src/seeders/004-seed-sample-data.js';

const seedAll = async () => {
  try {
    logger.info('🚀 Starting complete data seeding...');

    // 0. Seed admin user first
    logger.info('📋 Step 0: Seeding admin user...');
    await seedAdminUser();

    // 1. Seed subscription plans
    logger.info('📋 Step 1: Seeding subscription plans...');
    await seedSubscriptionPlans();

    // 2. Seed master data
    logger.info('📋 Step 2: Seeding master data...');
    await seedMasterData();

    // 3. Seed sample data (optional - only if demo tenant exists)
    try {
      logger.info('📋 Step 3: Seeding sample data...');
      await seedSampleData();
    } catch (error) {
      logger.warn('⚠️ Sample data seeding skipped:', error.message);
      logger.debug('Sample data error details:', error);
    }

    logger.info('🎉 Complete data seeding finished successfully!');
    logger.info('📊 What was seeded:');
    logger.info('   ✅ Admin User (<EMAIL> / Admin@123)');
    logger.info('   ✅ 4 Subscription Plans');
    logger.info('   ✅ 4 License Editions (Silver, Gold, Prime Silver, Prime Gold)');
    logger.info('   ✅ 3 Tally Products (ERP 9, TallyPrime, Developer 9)');
    logger.info('   ✅ 10 Industries (Manufacturing, Trading, Services, etc.)');
    logger.info('   ✅ 8 Areas (Mumbai, Delhi, Bangalore, etc.)');
    logger.info('   ✅ 9 Designations (MD, Director, Manager, etc.)');
    logger.info('   ✅ 6 Staff Roles (Sales Executive, Technical Support, etc.)');
    logger.info('   ✅ 7 Nature of Issues (Installation, Data, Performance, etc.)');
    logger.info('   ✅ 6 Call Statuses (Open, In Progress, Resolved, etc.)');
    logger.info('   ✅ 6 Additional Services (Data Migration, Custom Reports, etc.)');
    logger.info('   ✅ Sample customers and contacts (if demo tenant exists)');

    process.exit(0);
  } catch (error) {
    logger.error('❌ Data seeding failed:', error);
    process.exit(1);
  }
};

seedAll();
