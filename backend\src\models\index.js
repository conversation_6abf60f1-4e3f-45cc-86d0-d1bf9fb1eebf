import { Sequelize, DataTypes } from 'sequelize';
import { sequelize } from '../utils/database.js';
import { logger } from '../utils/logger.js';

// Import model definitions
import UserModel from './User.js';
import TenantModel from './Tenant.js';
import RoleModel from './Role.js';
import PermissionModel from './Permission.js';
import RolePermissionModel from './RolePermission.js';
import UserRoleModel from './UserRole.js';

// Master Data Models
import LicenseEditionModel from './masters/LicenseEdition.js';
import DesignationModel from './masters/Designation.js';
import TallyProductModel from './masters/TallyProduct.js';
import StaffRoleModel from './masters/StaffRole.js';
import ExecutiveModel from './masters/Executive.js';
import IndustryModel from './masters/Industry.js';
import AreaModel from './masters/Area.js';
import NatureOfIssueModel from './masters/NatureOfIssue.js';
import AdditionalServiceModel from './masters/AdditionalService.js';
import CallStatusModel from './masters/CallStatus.js';
import TypeOfCallModel from './masters/TypeOfCall.js';
import OnlineCallTypeModel from './masters/OnlineCallType.js';
import LeadStatusModel from './masters/LeadStatus.js';
import ProductsIssuesModel from './ProductsIssues.js';

// Business Models
import CustomerModel from './Customer.js';
import CustomerContactModel from './CustomerContact.js';
import CustomerTSSModel from './CustomerTSS.js';
import CustomerAMCModel from './CustomerAMC.js';
import ServiceCallModel from './ServiceCall.js';
import ServiceCallItemModel from './ServiceCallItem.js';
import SaleModel from './Sale.js';
import SaleItemModel from './SaleItem.js';
import ReferralModel from './Referral.js';
import LeadModel from './Lead.js';
import LeadContactHistoryModel from './LeadContactHistory.js';
import NotificationTemplateModel from './NotificationTemplate.js';
import NotificationScheduleModel from './NotificationSchedule.js';
import RenewalNotificationSettingsModel from './RenewalNotificationSettings.js';

// Attendance Management Models
import AttendanceRecordModel from './AttendanceRecord.js';
import AttendancePolicyModel from './AttendancePolicy.js';
import AttendanceSettingsModel from './AttendanceSettings.js';
import LeaveTypeModel from './LeaveType.js';
import LeaveRequestModel from './LeaveRequest.js';
import LeaveBalanceModel from './LeaveBalance.js';
import SalaryStructureModel from './SalaryStructure.js';
import PayrollRecordModel from './PayrollRecord.js';

// SaaS Models
import SubscriptionPlanModel from './SubscriptionPlan.js';
import SubscriptionModel from './Subscription.js';
import InvoiceModel from './Invoice.js';
import PaymentModel from './Payment.js';
import UsageRecordModel from './UsageRecord.js';
import NotificationSettingsModel from './NotificationSettings.js';

// Initialize models
const models = {
  // Core models
  User: UserModel(sequelize, DataTypes),
  Tenant: TenantModel(sequelize, DataTypes),
  Role: RoleModel(sequelize, DataTypes),
  Permission: PermissionModel(sequelize, DataTypes),
  RolePermission: RolePermissionModel(sequelize, DataTypes),
  UserRole: UserRoleModel(sequelize, DataTypes),

  // Master data models
  LicenseEdition: LicenseEditionModel(sequelize, DataTypes),
  Designation: DesignationModel(sequelize, DataTypes),
  TallyProduct: TallyProductModel(sequelize, DataTypes),
  StaffRole: StaffRoleModel(sequelize, DataTypes),
  Executive: ExecutiveModel(sequelize, DataTypes),
  Industry: IndustryModel(sequelize, DataTypes),
  Area: AreaModel(sequelize, DataTypes),
  NatureOfIssue: NatureOfIssueModel(sequelize, DataTypes),
  AdditionalService: AdditionalServiceModel(sequelize, DataTypes),
  CallStatus: CallStatusModel(sequelize, DataTypes),
  TypeOfCall: TypeOfCallModel(sequelize, DataTypes),
  OnlineCallType: OnlineCallTypeModel(sequelize, DataTypes),
  LeadStatus: LeadStatusModel(sequelize, DataTypes),
  ProductsIssues: ProductsIssuesModel(sequelize, DataTypes),

  // Business models
  Customer: CustomerModel(sequelize, DataTypes),
  CustomerContact: CustomerContactModel(sequelize, DataTypes),
  CustomerTSS: CustomerTSSModel(sequelize, DataTypes),
  CustomerAMC: CustomerAMCModel(sequelize, DataTypes),
  ServiceCall: ServiceCallModel(sequelize, DataTypes),
  ServiceCallItem: ServiceCallItemModel(sequelize, DataTypes),
  Sale: SaleModel(sequelize, DataTypes),
  SaleItem: SaleItemModel(sequelize, DataTypes),
  Referral: ReferralModel(sequelize, DataTypes),
  Lead: LeadModel(sequelize, DataTypes),
  LeadContactHistory: LeadContactHistoryModel(sequelize, DataTypes),
  NotificationTemplate: NotificationTemplateModel(sequelize, DataTypes),
  NotificationSchedule: NotificationScheduleModel(sequelize, DataTypes),
  RenewalNotificationSettings: RenewalNotificationSettingsModel(sequelize, DataTypes),

  // Attendance Management models
  AttendanceRecord: AttendanceRecordModel(sequelize, DataTypes),
  AttendancePolicy: AttendancePolicyModel(sequelize, DataTypes),
  AttendanceSettings: AttendanceSettingsModel(sequelize, DataTypes),
  LeaveType: LeaveTypeModel(sequelize, DataTypes),
  LeaveRequest: LeaveRequestModel(sequelize, DataTypes),
  LeaveBalance: LeaveBalanceModel(sequelize, DataTypes),
  SalaryStructure: SalaryStructureModel(sequelize, DataTypes),
  PayrollRecord: PayrollRecordModel(sequelize, DataTypes),

  // SaaS models
  SubscriptionPlan: SubscriptionPlanModel(sequelize, DataTypes),
  Subscription: SubscriptionModel(sequelize, DataTypes),
  Invoice: InvoiceModel(sequelize, DataTypes),
  Payment: PaymentModel(sequelize, DataTypes),
  UsageRecord: UsageRecordModel(sequelize, DataTypes),
  NotificationSettings: NotificationSettingsModel(sequelize, DataTypes),
};

// Define associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Add sequelize instance and Sequelize constructor to models object
models.sequelize = sequelize;
models.Sequelize = Sequelize;

// Note: We use migrations instead of sync for database schema management
// Sync is disabled to prevent conflicts with migration system
logger.info('📋 Database models loaded (using migrations for schema management)');

export default models;
export { sequelize, Sequelize };

// Export individual models for direct import
export const {
  User,
  Tenant,
  Role,
  Permission,
  RolePermission,
  UserRole,
  LicenseEdition,
  Designation,
  TallyProduct,
  StaffRole,
  Executive,
  Industry,
  Area,
  NatureOfIssue,
  AdditionalService,
  CallStatus,
  TypeOfCall,
  OnlineCallType,
  LeadStatus,
  ProductsIssues,
  Customer,
  CustomerContact,
  CustomerTSS,
  CustomerAMC,
  ServiceCall,
  ServiceCallItem,
  Sale,
  SaleItem,
  Referral,
  Lead,
  LeadContactHistory,
  NotificationTemplate,
  NotificationSchedule,
  RenewalNotificationSettings,
  AttendanceRecord,
  AttendancePolicy,
  AttendanceSettings,
  LeaveType,
  LeaveRequest,
  LeaveBalance,
  SalaryStructure,
  PayrollRecord,
  SubscriptionPlan,
  Subscription,
  Invoice,
  Payment,
  UsageRecord,
  NotificationSettings
} = models;
