import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all leads with pagination and filters
 */
export const getLeads = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      executive,
      dateFrom,
      dateTo,
      followUpFrom,
      followUpTo,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply search filter
    if (search) {
      where[Op.or] = [
        { customer_name: { [Op.iLike]: `%${search}%` } },
        { contact_no: { [Op.iLike]: `%${search}%` } },
        { remarks: { [Op.iLike]: `%${search}%` } },
        { ref_name: { [Op.iLike]: `%${search}%` } },
        { ref_contact_no: { [Op.iLike]: `%${search}%` } },
        // Search in associated tables
        { '$productsIssues.name$': { [Op.iLike]: `%${search}%` } },
        { '$status.name$': { [Op.iLike]: `%${search}%` } },
        { '$executive.first_name$': { [Op.iLike]: `%${search}%` } },
        { '$executive.last_name$': { [Op.iLike]: `%${search}%` } },
      ];
    }

    // Apply filters
    if (status) {
      where.status_id = status; // Now using status_id
    }

    if (executive) {
      where.executive_id = executive; // Now using executive_id
    }

    if (dateFrom || dateTo) {
      where.date = {};
      if (dateFrom) where.date[Op.gte] = dateFrom;
      if (dateTo) where.date[Op.lte] = dateTo;
    }

    if (followUpFrom || followUpTo) {
      where.follow_up_date = {};
      if (followUpFrom) where.follow_up_date[Op.gte] = followUpFrom;
      if (followUpTo) where.follow_up_date[Op.lte] = followUpTo;
    }

    const { count, rows: leads } = await models.Lead.findAndCountAll({
      where,
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
        {
          model: models.User,
          as: 'updater',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
        {
          model: models.Customer,
          as: 'convertedCustomer',
          attributes: ['id', 'company_name', 'customer_code'],
          required: false,
        },
        {
          model: models.ProductsIssues,
          as: 'productsIssues',
          attributes: ['id', 'name', 'description'],
          required: false,
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'color'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'contactedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        leads,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get leads error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leads',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get lead by ID
 */
export const getLeadById = async (req, res) => {
  try {
    const { id } = req.params;

    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
        {
          model: models.User,
          as: 'updater',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
        {
          model: models.Customer,
          as: 'convertedCustomer',
          attributes: ['id', 'company_name', 'customer_code'],
          required: false,
        },
        {
          model: models.ProductsIssues,
          as: 'productsIssues',
          attributes: ['id', 'name', 'description'],
          required: false,
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'color'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'contactedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
    });

    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    res.json({
      success: true,
      data: { lead },
    });

  } catch (error) {
    logger.error('Get lead by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch lead',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new lead
 */
export const createLead = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    // Set default status to "Follow Up" if not provided
    let statusId = req.body.status_id;
    if (!statusId) {
      const followUpStatus = await models.CallStatus.findOne({
        where: {
          [Op.or]: [
            { name: { [Op.iLike]: 'follow up' } },
            { name: { [Op.iLike]: 'followup' } }
          ]
        },
        transaction
      });
      statusId = followUpStatus?.id;
    }

    const leadData = {
      ...req.body,
      status_id: statusId,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
      // Auto-set created timestamp
      created_at: new Date(),
    };

    // Validate mandatory fields
    if (!leadData.customer_name || leadData.customer_name.trim() === '') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Customer name is required',
        errors: {
          customer_name: 'Customer name is required'
        }
      });
    }

    if (!leadData.contact_no || leadData.contact_no.trim() === '') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Contact number is required',
        errors: {
          contact_no: 'Contact number is required'
        }
      });
    }

    // Validate phone number format if provided
    if (leadData.contact_no && leadData.contact_no.trim()) {
      // Remove any non-digit characters except + for validation
      const cleanPhone = leadData.contact_no.replace(/[^\d+\-\s\(\)]/g, '');
      if (cleanPhone.length < 7 || cleanPhone.length > 20) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid phone number format',
          errors: {
            contact_no: 'Phone number must be between 7 and 20 characters'
          }
        });
      }
    }

    // Validate reference phone number format if provided
    if (leadData.ref_contact_no && leadData.ref_contact_no.trim()) {
      const cleanRefPhone = leadData.ref_contact_no.replace(/[^\d+\-\s\(\)]/g, '');
      if (cleanRefPhone.length < 7 || cleanRefPhone.length > 20) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid reference phone number format',
          errors: {
            ref_contact_no: 'Reference phone number must be between 7 and 20 characters'
          }
        });
      }
    }

    // Validate amount format if provided
    if (leadData.amount && (isNaN(leadData.amount) || leadData.amount < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Invalid amount format',
        errors: {
          amount: 'Amount must be a positive number'
        }
      });
    }

    // Validate reference amount format if provided
    if (leadData.ref_amount && (isNaN(leadData.ref_amount) || leadData.ref_amount < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Invalid reference amount format',
        errors: {
          ref_amount: 'Reference amount must be a positive number'
        }
      });
    }

    // Create the lead
    const lead = await models.Lead.create(leadData, { transaction });

    await transaction.commit();

    // Fetch the created lead with associations
    const createdLead = await models.Lead.findByPk(lead.id, {
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
        {
          model: models.ProductsIssues,
          as: 'productsIssues',
          attributes: ['id', 'name', 'description'],
          required: false,
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'color'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'contactedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
    });

    logger.info('Lead created successfully:', {
      id: lead.id,
      customer_name: lead.customer_name,
      createdBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: 'Lead created successfully',
      data: { lead: createdLead },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Create lead error:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const errors = {};
      error.errors.forEach(err => {
        // Convert technical error messages to user-friendly ones
        let userFriendlyMessage = err.message;

        if (err.path === 'executive_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid executive from the list or leave it empty';
        } else if (err.path === 'contacted_executive_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid contacted executive from the list or leave it empty';
        } else if (err.path === 'status_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid status from the list';
        } else if (err.path === 'products_issues_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid product/service from the list';
        }

        errors[err.path] = userFriendlyMessage;
      });

      return res.status(400).json({
        success: false,
        message: 'Please fix the validation errors and try again',
        errors,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create lead',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update lead
 */
export const updateLead = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updated_by: req.user.id,
    };

    // Find the lead
    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!lead) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    // Validate mandatory fields
    if (updateData.customer_name !== undefined && (!updateData.customer_name || updateData.customer_name.trim() === '')) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Customer name is required',
        errors: {
          customer_name: 'Customer name is required'
        }
      });
    }

    if (updateData.contact_no !== undefined && (!updateData.contact_no || updateData.contact_no.trim() === '')) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Contact number is required',
        errors: {
          contact_no: 'Contact number is required'
        }
      });
    }

    // Validate phone number format if provided
    if (updateData.contact_no && updateData.contact_no.trim()) {
      const cleanPhone = updateData.contact_no.replace(/[^\d+\-\s\(\)]/g, '');
      if (cleanPhone.length < 7 || cleanPhone.length > 20) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid phone number format',
          errors: {
            contact_no: 'Phone number must be between 7 and 20 characters'
          }
        });
      }
    }

    // Validate reference phone number format if provided
    if (updateData.ref_contact_no && updateData.ref_contact_no.trim()) {
      const cleanRefPhone = updateData.ref_contact_no.replace(/[^\d+\-\s\(\)]/g, '');
      if (cleanRefPhone.length < 7 || cleanRefPhone.length > 20) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: 'Invalid reference phone number format',
          errors: {
            ref_contact_no: 'Reference phone number must be between 7 and 20 characters'
          }
        });
      }
    }

    // Validate amount format if provided
    if (updateData.amount && (isNaN(updateData.amount) || updateData.amount < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Invalid amount format',
        errors: {
          amount: 'Amount must be a positive number'
        }
      });
    }

    // Validate reference amount format if provided
    if (updateData.ref_amount && (isNaN(updateData.ref_amount) || updateData.ref_amount < 0)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Invalid reference amount format',
        errors: {
          ref_amount: 'Reference amount must be a positive number'
        }
      });
    }

    // Update the lead
    await lead.update(updateData, { transaction });

    await transaction.commit();

    // Fetch the updated lead with associations
    const updatedLead = await models.Lead.findByPk(lead.id, {
      include: [
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
        {
          model: models.User,
          as: 'updater',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
        {
          model: models.Customer,
          as: 'convertedCustomer',
          attributes: ['id', 'company_name', 'customer_code'],
          required: false,
        },
        {
          model: models.ProductsIssues,
          as: 'productsIssues',
          attributes: ['id', 'name', 'description'],
          required: false,
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'color'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'contactedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
    });

    logger.info('Lead updated successfully:', {
      id: lead.id,
      customer_name: lead.customer_name,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Lead updated successfully',
      data: { lead: updatedLead },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Update lead error:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const errors = {};
      error.errors.forEach(err => {
        // Convert technical error messages to user-friendly ones
        let userFriendlyMessage = err.message;

        if (err.path === 'executive_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid executive from the list or leave it empty';
        } else if (err.path === 'contacted_executive_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid contacted executive from the list or leave it empty';
        } else if (err.path === 'status_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid status from the list';
        } else if (err.path === 'products_issues_id' && err.message.includes('UUID')) {
          userFriendlyMessage = 'Please select a valid product/service from the list';
        }

        errors[err.path] = userFriendlyMessage;
      });

      return res.status(400).json({
        success: false,
        message: 'Please fix the validation errors and try again',
        errors,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update lead',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete lead
 */
export const deleteLead = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;

    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!lead) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    // Check if lead is converted - converted leads should not be deleted
    if (lead.converted_to_customer_id) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Cannot delete this lead because it has been converted to a customer. Please manage the customer record instead of deleting the original lead.',
        errors: {
          general: 'This lead has been converted to a customer and cannot be deleted. Converted leads are preserved for audit purposes.'
        },
        details: {
          converted_to_customer_id: lead.converted_to_customer_id,
          conversion_date: lead.conversion_date
        },
        actionRequired: 'Converted leads cannot be deleted to maintain data integrity and audit trail'
      });
    }

    // Soft delete the lead
    await lead.destroy({ transaction });

    await transaction.commit();

    logger.info('Lead deleted successfully:', {
      id: lead.id,
      customer_name: lead.customer_name,
      deletedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Lead deleted successfully',
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Delete lead error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete lead',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get lead statistics
 */
export const getLeadStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    // Get total leads count
    const totalLeads = await models.Lead.count({
      where: { tenant_id: tenantId },
    });

    // Get leads by status
    const leadsByStatus = await models.Lead.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'status',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
      raw: true,
    });

    // Get recent leads (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentLeads = await models.Lead.count({
      where: {
        tenant_id: tenantId,
        created_at: { [Op.gte]: thirtyDaysAgo },
      },
    });

    // Get converted leads count
    const convertedLeads = await models.Lead.count({
      where: {
        tenant_id: tenantId,
        converted_to_customer_id: { [Op.ne]: null },
      },
    });

    // Get leads requiring follow up (today and overdue)
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    const followUpLeads = await models.Lead.count({
      where: {
        tenant_id: tenantId,
        follow_up_date: { [Op.lte]: today },
        converted_to_customer_id: null, // Only non-converted leads
      },
    });

    // Calculate conversion rate
    const conversionRate = totalLeads > 0 ? ((convertedLeads / totalLeads) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        totalLeads,
        recentLeads,
        convertedLeads,
        followUpLeads,
        conversionRate: parseFloat(conversionRate),
        leadsByStatus: leadsByStatus.reduce((acc, item) => {
          acc[item.status || 'Unknown'] = parseInt(item.count);
          return acc;
        }, {}),
      },
    });

  } catch (error) {
    logger.error('Get lead stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch lead statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get lead contact history
 */
export const getLeadContactHistory = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // Verify lead exists and belongs to tenant
    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    const { count, rows: contactHistory } = await models.LeadContactHistory.findAndCountAll({
      where: {
        lead_id: id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
      order: [['contact_date', 'DESC']],
      limit: parseInt(limit),
      offset,
    });

    res.json({
      success: true,
      data: {
        contactHistory,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get lead contact history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contact history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Add contact history entry for a lead
 */
export const addLeadContactHistory = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const contactData = {
      ...req.body,
      lead_id: id,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // Verify lead exists and belongs to tenant
    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (!lead) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    // Validate required fields
    if (!contactData.contact_type) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Contact type is required',
        errors: {
          contact_type: 'Contact type is required'
        }
      });
    }

    // Create contact history entry
    const contactHistory = await models.LeadContactHistory.create(contactData, { transaction });

    // Update lead's follow-up date if provided
    if (contactData.next_follow_up) {
      await lead.update({
        follow_up_date: contactData.next_follow_up,
        updated_by: req.user.id,
      }, { transaction });
    }

    await transaction.commit();

    // Fetch the created contact history with associations
    const createdContactHistory = await models.LeadContactHistory.findByPk(contactHistory.id, {
      include: [
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
          required: false,
        },
      ],
    });

    logger.info('Lead contact history added successfully:', {
      id: contactHistory.id,
      lead_id: id,
      contact_type: contactData.contact_type,
      createdBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      message: 'Contact history added successfully',
      data: { contactHistory: createdContactHistory },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Add lead contact history error:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const errors = {};
      error.errors.forEach(err => {
        errors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
    }

    // Handle foreign key constraint errors
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const fieldName = error.fields?.[0] || 'unknown';
      const errors = {};

      if (fieldName === 'executive_id') {
        errors.executive_id = 'Please select a valid executive from the list';
      } else if (fieldName === 'lead_id') {
        errors.general = 'Lead not found or invalid';
      } else if (fieldName === 'created_by') {
        errors.general = 'User authentication error';
      } else {
        errors.general = 'Invalid reference data provided';
      }

      return res.status(400).json({
        success: false,
        message: 'Invalid data provided',
        errors,
      });
    }

    // Handle unique constraint errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      const errors = {};
      error.errors.forEach(err => {
        errors[err.path] = `This ${err.path} already exists`;
      });

      return res.status(400).json({
        success: false,
        message: 'Duplicate data found',
        errors,
      });
    }

    // Handle database connection errors
    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      return res.status(503).json({
        success: false,
        message: 'Database connection error. Please try again later.',
        errors: {
          general: 'Service temporarily unavailable'
        }
      });
    }

    // Handle other database errors
    if (error.name && error.name.startsWith('Sequelize')) {
      return res.status(400).json({
        success: false,
        message: 'Database error occurred',
        errors: {
          general: process.env.NODE_ENV === 'development' ? error.message : 'A database error occurred'
        }
      });
    }

    // Generic server error
    res.status(500).json({
      success: false,
      message: 'Failed to add contact history',
      errors: {
        general: process.env.NODE_ENV === 'development' ? error.message : 'An unexpected error occurred'
      }
    });
  }
};

/**
 * Convert lead to customer
 */
export const convertLeadToCustomer = async (req, res) => {
  const transaction = await models.sequelize.transaction();

  try {
    const { id } = req.params;
    const { tallySerialNumber, ...additionalData } = req.body;

    // Verify lead exists and belongs to tenant
    const lead = await models.Lead.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.ProductsIssues,
          as: 'productsIssues',
          attributes: ['id', 'name', 'description'],
          required: false,
        },
        {
          model: models.Executive,
          as: 'executive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
          required: false,
        },
      ],
      transaction,
    });

    if (!lead) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Lead not found',
      });
    }

    // Check if lead is already converted
    if (lead.converted_to_customer_id) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Lead has already been converted to a customer',
        errors: {
          general: 'This lead has already been converted to a customer'
        }
      });
    }

    // Validate and normalize tally serial number
    if (!tallySerialNumber || tallySerialNumber.trim() === '') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Tally Serial Number is required for conversion',
        errors: {
          tallySerialNumber: 'Tally Serial Number is required'
        }
      });
    }

    // Convert tally serial number to uppercase (additional safety check)
    const normalizedTallySerialNumber = tallySerialNumber.trim().toUpperCase();

    // Check for duplicate customers based on name, email, and phone
    const duplicateChecks = [];

    // Check company name (from lead.customer_name)
    if (lead.customer_name && lead.customer_name.trim()) {
      const existingByName = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          company_name: lead.customer_name.trim(),
        },
        attributes: ['id', 'company_name', 'customer_code'],
        transaction,
      });

      if (existingByName) {
        duplicateChecks.push({
          field: 'company_name',
          value: lead.customer_name.trim(),
          existing: existingByName
        });
      }
    }

    // Check email (from form data)
    if (additionalData.email && additionalData.email.trim()) {
      const existingByEmail = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          email: additionalData.email.trim(),
        },
        attributes: ['id', 'company_name', 'customer_code', 'email'],
        transaction,
      });

      if (existingByEmail) {
        duplicateChecks.push({
          field: 'email',
          value: additionalData.email.trim(),
          existing: existingByEmail
        });
      }
    }

    // Check phone number (from lead.contact_no)
    if (lead.contact_no && lead.contact_no.trim()) {
      const existingByPhone = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          phone: lead.contact_no.trim(),
        },
        attributes: ['id', 'company_name', 'customer_code', 'phone'],
        transaction,
      });

      if (existingByPhone) {
        duplicateChecks.push({
          field: 'phone',
          value: lead.contact_no.trim(),
          existing: existingByPhone
        });
      }
    }

    // If duplicates found and force flag is not set, return warning
    if (duplicateChecks.length > 0 && !req.body.force) {
      await transaction.rollback();
      return res.status(409).json({
        success: false,
        message: 'Duplicate customer data found',
        duplicates: duplicateChecks,
        requiresConfirmation: true
      });
    }

    // Check if Tally Serial Number already exists
    const existingCustomer = await models.Customer.findOne({
      where: {
        tally_serial_number: normalizedTallySerialNumber,
        tenant_id: req.user.tenant.id,
      },
      transaction,
    });

    if (existingCustomer) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'A customer with this Tally Serial Number already exists',
        errors: {
          tallySerialNumber: 'This Tally Serial Number is already in use'
        }
      });
    }

    // Generate unique customer code - find next available code
    let customerCode;
    let codeExists = true;
    let codeNumber = 1;

    // Find the next available customer code
    while (codeExists) {
      customerCode = `CUST${String(codeNumber).padStart(4, '0')}`;

      const existingCustomer = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          customer_code: customerCode
        },
        transaction,
      });

      if (!existingCustomer) {
        codeExists = false;
      } else {
        codeNumber++;
      }

      // Safety check to prevent infinite loop
      if (codeNumber > 9999) {
        throw new Error('Maximum customer code limit reached');
      }
    }

    logger.info('Generated customer code for lead conversion:', {
      leadId: id,
      generatedCode: customerCode,
      codeNumber,
      tenantId: req.user.tenant.id
    });

    // Prepare customer data from lead
    const customerData = {
      tenant_id: req.user.tenant.id,
      customer_code: customerCode,
      company_name: lead.customer_name,
      tally_serial_number: normalizedTallySerialNumber,
      phone: lead.contact_no,
      notes: lead.remarks,
      created_by: req.user.id,
      // Map additional fields if provided
      ...additionalData,
    };

    // Create customer
    const customer = await models.Customer.create(customerData, { transaction });

    // Update lead with conversion information
    await lead.update({
      converted_to_customer_id: customer.id,
      conversion_date: new Date(),
      updated_by: req.user.id,
    }, { transaction });

    // Add conversion contact history entry
    await models.LeadContactHistory.create({
      lead_id: id,
      tenant_id: req.user.tenant.id,
      contact_type: 'other',
      contact_date: new Date(),
      outcome: 'converted',
      notes: `Lead converted to customer. Tally Serial Number: ${normalizedTallySerialNumber}`,
      created_by: req.user.id,
    }, { transaction });

    await transaction.commit();

    logger.info('Lead converted to customer successfully:', {
      leadId: id,
      customerId: customer.id,
      tallySerialNumber: normalizedTallySerialNumber,
      convertedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Lead converted to customer successfully',
      data: {
        customer: {
          id: customer.id,
          customer_code: customer.customer_code,
          company_name: customer.company_name,
          tally_serial_number: customer.tally_serial_number,
        },
        lead: {
          id: lead.id,
          converted_to_customer_id: customer.id,
          conversion_date: lead.conversion_date,
        },
      },
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Convert lead to customer error:', error);

    // Handle validation errors
    if (error.name === 'SequelizeValidationError') {
      const errors = {};
      error.errors.forEach(err => {
        errors[err.path] = err.message;
      });

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
    }

    // Handle unique constraint violations
    if (error.name === 'SequelizeUniqueConstraintError') {
      const errors = {};
      error.errors.forEach(err => {
        if (err.path === 'customer_code') {
          errors[err.path] = 'Customer code already exists. Please try again.';
        } else if (err.path === 'tally_serial_number') {
          errors[err.path] = 'Tally Serial Number already exists for another customer.';
        } else {
          errors[err.path] = `${err.path} must be unique`;
        }
      });

      return res.status(400).json({
        success: false,
        message: 'Duplicate data found',
        errors,
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to convert lead to customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
