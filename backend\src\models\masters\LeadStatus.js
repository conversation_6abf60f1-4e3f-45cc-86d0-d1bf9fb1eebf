import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LeadStatus = sequelize.define('LeadStatus', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('new', 'active', 'interested', 'not_interested', 'converted', 'lost'),
      allowNull: false,
      defaultValue: 'new',
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#6c757d',
      comment: 'Color code for UI display',
    },
    is_final: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this is a final status (no further changes allowed)',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether changing to this status requires approval',
    },
    auto_follow_up_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Auto-schedule follow up after specified days',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_default: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this is a default status that cannot be edited or deleted',
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: 'Display order in lists',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'lead_statuses',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  LeadStatus.getDefaultStatuses = function() {
    return [
      {
        name: 'New',
        code: 'NEW',
        description: 'New lead that needs initial contact',
        category: 'new',
        color: '#28a745',
        is_final: false,
        requires_approval: false,
        auto_follow_up_days: 1,
        is_default: true,
        sort_order: 1,
      },
      {
        name: 'Follow Up',
        code: 'FOLLOW_UP',
        description: 'Lead requires follow up contact',
        category: 'active',
        color: '#ffc107',
        is_final: false,
        requires_approval: false,
        auto_follow_up_days: 3,
        is_default: true,
        sort_order: 2,
      },
      {
        name: 'Call Not Attended',
        code: 'CALL_NOT_ATTENDED',
        description: 'Lead did not answer the call',
        category: 'active',
        color: '#fd7e14',
        is_final: false,
        requires_approval: false,
        auto_follow_up_days: 2,
        is_default: true,
        sort_order: 3,
      },
      {
        name: 'Interested',
        code: 'INTERESTED',
        description: 'Lead has shown interest in products/services',
        category: 'interested',
        color: '#17a2b8',
        is_final: false,
        requires_approval: false,
        auto_follow_up_days: 7,
        is_default: true,
        sort_order: 4,
      },
      {
        name: 'Not Interested',
        code: 'NOT_INTERESTED',
        description: 'Lead is not interested in products/services',
        category: 'not_interested',
        color: '#6c757d',
        is_final: true,
        requires_approval: false,
        auto_follow_up_days: null,
        is_default: true,
        sort_order: 5,
      },
      {
        name: 'Converted',
        code: 'CONVERTED',
        description: 'Lead has been converted to customer',
        category: 'converted',
        color: '#007bff',
        is_final: true,
        requires_approval: false,
        auto_follow_up_days: null,
        is_default: true,
        sort_order: 6,
      },
      {
        name: 'Lost',
        code: 'LOST',
        description: 'Lead is lost and no longer viable',
        category: 'lost',
        color: '#dc3545',
        is_final: true,
        requires_approval: false,
        auto_follow_up_days: null,
        is_default: true,
        sort_order: 7,
      },
    ];
  };

  // Instance methods
  LeadStatus.prototype.canTransitionTo = function(targetStatus) {
    // Final statuses cannot transition to other statuses
    if (this.is_final) {
      return false;
    }

    // All non-final statuses can transition to any status
    return true;
  };

  LeadStatus.prototype.isActive = function() {
    return ['new', 'active', 'interested'].includes(this.category);
  };

  LeadStatus.prototype.isFinal = function() {
    return this.is_final;
  };

  LeadStatus.prototype.requiresFollowUp = function() {
    return this.auto_follow_up_days !== null && this.auto_follow_up_days > 0;
  };

  return LeadStatus;
}
