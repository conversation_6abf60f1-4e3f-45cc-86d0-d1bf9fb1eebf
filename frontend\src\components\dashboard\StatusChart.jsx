import React from 'react';
import { cn } from '../../utils/helpers';

/**
 * Status Distribution Chart Component
 * Displays visual charts for status and priority distribution in dashboard
 */
const StatusChart = ({
  data = [],
  type = 'status', // 'status' or 'priority'
  size = 'md',
  showLabels = true,
  showPercentages = true,
  className = '',
  ...props
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={cn('text-center py-4', className)}>
        <div className="text-gray-400 text-sm">No data available</div>
      </div>
    );
  }

  // Default fallback color configuration
  const defaultColor = { bg: 'bg-gray-500', light: 'bg-gray-100', text: 'text-gray-800' };

  // Color schemes for different chart types
  const statusColors = {
    'Completed': { bg: 'bg-green-500', light: 'bg-green-100', text: 'text-green-800' },
    'In Progress': { bg: 'bg-blue-500', light: 'bg-blue-100', text: 'text-blue-800' },
    'Pending': { bg: 'bg-yellow-500', light: 'bg-yellow-100', text: 'text-yellow-800' },
    'On Hold': { bg: 'bg-orange-500', light: 'bg-orange-100', text: 'text-orange-800' },
    'Cancelled': { bg: 'bg-red-500', light: 'bg-red-100', text: 'text-red-800' },
    'Open': { bg: 'bg-blue-400', light: 'bg-blue-100', text: 'text-blue-800' },
    'Closed': { bg: 'bg-gray-600', light: 'bg-gray-100', text: 'text-gray-800' },
    'Other': { bg: 'bg-gray-500', light: 'bg-gray-100', text: 'text-gray-800' }
  };

  const priorityColors = {
    'High': { bg: 'bg-red-500', light: 'bg-red-100', text: 'text-red-800' },
    'Urgent': { bg: 'bg-red-600', light: 'bg-red-100', text: 'text-red-800' },
    'Medium': { bg: 'bg-yellow-500', light: 'bg-yellow-100', text: 'text-yellow-800' },
    'Normal': { bg: 'bg-blue-500', light: 'bg-blue-100', text: 'text-blue-800' },
    'Low': { bg: 'bg-green-500', light: 'bg-green-100', text: 'text-green-800' }
  };

  const colors = type === 'priority' ? priorityColors : statusColors;

  // Size configurations
  const sizeConfig = {
    sm: {
      container: 'space-y-2',
      bar: 'h-2',
      text: 'text-xs',
      spacing: 'space-y-1'
    },
    md: {
      container: 'space-y-3',
      bar: 'h-3',
      text: 'text-sm',
      spacing: 'space-y-2'
    },
    lg: {
      container: 'space-y-4',
      bar: 'h-4',
      text: 'text-base',
      spacing: 'space-y-3'
    }
  };

  const config = sizeConfig[size];
  const total = data.reduce((sum, item) => sum + (item.count || 0), 0);

  return (
    <div className={cn('w-full', config.container, className)} {...props}>
      {/* Chart Title */}
      {showLabels && (
        <div className="flex items-center justify-between mb-2">
          <h6 className={cn('font-medium text-gray-700', config.text)}>
            {type === 'priority' ? 'Priority distribution' : 'Status distribution'}
          </h6>
          <span className={cn('text-gray-500', config.text)}>
            {total} total
          </span>
        </div>
      )}

      {/* Progress Bars */}
      <div className={config.spacing}>
        {data.map((item, index) => {
          const key = item.status || item.priority || 'Other';
          const count = item.count || 0;
          const percentage = item.percentage || (total > 0 ? Math.round((count / total) * 100) : 0);
          const colorConfig = colors[key] || colors['Other'] || defaultColor;

          return (
            <div key={index} className="space-y-1">
              {/* Label and Count */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={cn('w-3 h-3 rounded-full', colorConfig?.bg || 'bg-gray-500')} />
                  <span className={cn('font-medium text-gray-700', config.text)}>
                    {key || 'Unknown'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={cn('text-gray-600', config.text)}>
                    {count}
                  </span>
                  {showPercentages && (
                    <span className={cn('text-gray-500', config.text)}>
                      ({percentage}%)
                    </span>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              <div className={cn('w-full bg-gray-200 rounded-full overflow-hidden', config.bar)}>
                <div
                  className={cn('h-full transition-all duration-500 ease-out', colorConfig?.bg || 'bg-gray-500')}
                  style={{ width: `${percentage}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Stats */}
      {data.length > 1 && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className={cn('font-semibold text-gray-900', config.text)}>
                {data.reduce((sum, item) => sum + (item.count || 0), 0)}
              </div>
              <div className={cn('text-gray-500', config.text)}>
                Total items
              </div>
            </div>
            <div className="text-center">
              <div className={cn('font-semibold text-gray-900', config.text)}>
                {data.length}
              </div>
              <div className={cn('text-gray-500', config.text)}>
                Categories
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Donut Chart Component for compact status visualization
 */
export const StatusDonutChart = ({
  data = [],
  size = 80,
  strokeWidth = 8,
  className = '',
  showLegend = true,
  ...props
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={cn('flex items-center justify-center', className)} style={{ width: size, height: size }}>
        <div className="text-gray-400 text-xs">No data</div>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + (item.count || 0), 0);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  let cumulativePercentage = 0;

  const statusColors = [
    '#10b981', // green - completed
    '#3b82f6', // blue - in progress
    '#f59e0b', // yellow - pending
    '#f97316', // orange - on hold
    '#ef4444', // red - cancelled
    '#6b7280'  // gray - other
  ];

  return (
    <div className={cn('flex items-center space-x-4', className)} {...props}>
      {/* Donut Chart */}
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="transform -rotate-90">
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="#f3f4f6"
            strokeWidth={strokeWidth}
          />
          
          {/* Data segments */}
          {data.map((item, index) => {
            const percentage = total > 0 ? (item.count / total) * 100 : 0;
            const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
            const strokeDashoffset = -((cumulativePercentage / 100) * circumference);
            
            cumulativePercentage += percentage;
            
            return (
              <circle
                key={index}
                cx={size / 2}
                cy={size / 2}
                r={radius}
                fill="none"
                stroke={statusColors[index % statusColors.length]}
                strokeWidth={strokeWidth}
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                className="transition-all duration-500"
              />
            );
          })}
        </svg>
        
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">{total}</div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
        </div>
      </div>

      {/* Legend */}
      {showLegend && (
        <div className="space-y-1">
          {data.map((item, index) => {
            const key = item.status || item.priority;
            const count = item.count || 0;
            const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
            
            return (
              <div key={index} className="flex items-center space-x-2 text-sm">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: statusColors[index % statusColors.length] }}
                />
                <span className="text-gray-700">{key}</span>
                <span className="text-gray-500">({count})</span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default StatusChart;
