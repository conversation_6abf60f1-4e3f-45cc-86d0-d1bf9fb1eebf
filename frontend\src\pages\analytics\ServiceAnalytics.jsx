/**
 * Service Analytics Page
 * Comprehensive analytics for service performance including call trends, resolution metrics, and executive performance
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody, Spinner, Alert, Button } from '../../components/ui';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>hart, 
  <PERSON>hart, 
  Donut<PERSON>hart, 
  AreaChart,
  ComposedChart,
  MetricCard,
  ServiceCard,
  ChartContainer
} from '../../components/charts';
import { apiService } from '../../services/api';

const ServiceAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('30d');

  useEffect(() => {
    fetchServiceAnalytics();
  }, [period]);

  const fetchServiceAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch service analytics data
      const response = await apiService.get(`/analytics/services?period=${period}`);
      
      if (response.data.success) {
        setAnalyticsData(response.data.data);
      } else {
        throw new Error('Failed to fetch service analytics data');
      }
    } catch (err) {
      console.error('Service analytics fetch error:', err);
      setError(err.message || 'Failed to load service analytics data');
      
      // Set mock data for development
      setAnalyticsData(getMockServiceAnalytics());
    } finally {
      setLoading(false);
    }
  };

  // Mock data for development/fallback
  const getMockServiceAnalytics = () => ({
    totalServiceCalls: 156,
    serviceCallsInPeriod: 34,
    openServiceCalls: 18,
    completedServiceCalls: 128,
    avgResolutionTimeHours: 18.5,
    serviceCallsByStatus: [
      { name: 'Open', value: 18, status: 'open' },
      { name: 'In Progress', value: 12, status: 'in-progress' },
      { name: 'Completed', value: 128, status: 'completed' },
      { name: 'Cancelled', value: 8, status: 'cancelled' }
    ],
    serviceCallsByPriority: [
      { name: 'High', value: 25, priority: 'high' },
      { name: 'Medium', value: 89, priority: 'medium' },
      { name: 'Low', value: 42, priority: 'low' }
    ],
    serviceCallsTrend: [
      { date: '2024-01-01', value: 8, completed: 6, open: 2 },
      { date: '2024-01-02', value: 12, completed: 9, open: 3 },
      { date: '2024-01-03', value: 6, completed: 5, open: 1 },
      { date: '2024-01-04', value: 15, completed: 11, open: 4 },
      { date: '2024-01-05', value: 9, completed: 7, open: 2 },
      { date: '2024-01-06', value: 11, completed: 8, open: 3 },
      { date: '2024-01-07', value: 7, completed: 6, open: 1 }
    ],
    executivePerformance: [
      { name: 'John Doe', total: 45, completed: 38, completionRate: 84 },
      { name: 'Jane Smith', total: 32, completed: 29, completionRate: 91 },
      { name: 'Mike Johnson', total: 38, completed: 31, completionRate: 82 },
      { name: 'Sarah Wilson', total: 28, completed: 25, completionRate: 89 }
    ],
    resolutionTimeByPriority: [
      { name: 'High', avgHours: 8.5 },
      { name: 'Medium', avgHours: 18.2 },
      { name: 'Low', avgHours: 32.1 }
    ],
    serviceTypeDistribution: [
      { name: 'Installation', value: 45 },
      { name: 'Maintenance', value: 62 },
      { name: 'Support', value: 38 },
      { name: 'Training', value: 11 }
    ],
    billingTypeDistribution: [
      { name: 'Per Call', value: 68, type: 'per_call' },
      { name: 'AMC Call', value: 52, type: 'amc_call' },
      { name: 'Free Call', value: 36, type: 'free_call' }
    ]
  });

  const periodOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Alert variant="error" className="mb-6">
          <strong>Service Analytics Error:</strong> {error}
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Service Analytics</h1>
            <p className="text-gray-600">Performance insights for service calls, resolution times, and team efficiency</p>
          </div>
          
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Period:</label>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {periodOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Button
              onClick={fetchServiceAnalytics}
              variant="outline"
              size="sm"
            >
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <ServiceCard
          title="Total Service Calls"
          value={analyticsData.totalServiceCalls}
          previousValue={analyticsData.totalServiceCalls - analyticsData.serviceCallsInPeriod}
          size="medium"
        />
        
        <ServiceCard
          title="Open Calls"
          value={analyticsData.openServiceCalls}
          color="warning"
          size="medium"
        />
        
        <ServiceCard
          title="Completed Calls"
          value={analyticsData.completedServiceCalls}
          color="success"
          size="medium"
        />
        
        <MetricCard
          title="Avg Resolution Time"
          value={analyticsData.avgResolutionTimeHours}
          unit="hours"
          format="decimal"
          color="primary"
          size="medium"
          icon={
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Service Calls Trend */}
        <ChartContainer
          title="Service Calls Trend"
          subtitle={`Service call activity over the last ${period}`}
        >
          <ComposedChart
            data={analyticsData.serviceCallsTrend}
            elements={[
              { type: 'bar', dataKey: 'value', name: 'Total Calls', color: '#1d5795' },
              { type: 'line', dataKey: 'completed', name: 'Completed', color: '#10b981', yAxisId: 'right' },
              { type: 'line', dataKey: 'open', name: 'Open', color: '#f59e0b', yAxisId: 'right' }
            ]}
            height={300}
            formatters={{ 
              value: (val) => `${val} calls`,
              completed: (val) => `${val} completed`,
              open: (val) => `${val} open`
            }}
          />
        </ChartContainer>

        {/* Executive Performance */}
        <ChartContainer
          title="Executive Performance"
          subtitle="Service calls handled and completion rates"
        >
          <ComposedChart
            data={analyticsData.executivePerformance}
            elements={[
              { type: 'bar', dataKey: 'total', name: 'Total Calls', color: '#6b7280' },
              { type: 'bar', dataKey: 'completed', name: 'Completed', color: '#10b981' },
              { type: 'line', dataKey: 'completionRate', name: 'Completion %', color: '#2563eb', yAxisId: 'right' }
            ]}
            height={300}
            formatters={{ 
              total: (val) => `${val} calls`,
              completed: (val) => `${val} completed`,
              completionRate: (val) => `${val}%`
            }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Service Calls by Status */}
        <ChartContainer
          title="Service Call Status"
          subtitle="Distribution by current status"
        >
          <DonutChart
            data={analyticsData.serviceCallsByStatus}
            height={280}
            colorScheme="status"
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {analyticsData.totalServiceCalls}
                </div>
                <div className="text-sm text-gray-500">Total Calls</div>
              </div>
            }
          />
        </ChartContainer>

        {/* Service Calls by Priority */}
        <ChartContainer
          title="Priority Distribution"
          subtitle="Service calls by priority level"
        >
          <PieChart
            data={analyticsData.serviceCallsByPriority}
            height={280}
            colorScheme="priority"
            showLabels={true}
            showPercentages={true}
          />
        </ChartContainer>

        {/* Service Type Distribution */}
        <ChartContainer
          title="Service Types"
          subtitle="Distribution by service category"
        >
          <BarChart
            data={analyticsData.serviceTypeDistribution}
            bars={[{ dataKey: 'value', name: 'Calls' }]}
            height={280}
            orientation="vertical"
            formatters={{ value: (val) => `${val} calls` }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 3 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Billing Type Distribution */}
        <ChartContainer
          title="Billing Types"
          subtitle="Revenue distribution by billing type"
        >
          <DonutChart
            data={analyticsData.billingTypeDistribution}
            height={280}
            centerContent={
              <div className="text-center">
                <div className="text-xl font-bold text-gray-900">
                  {analyticsData.billingTypeDistribution?.reduce((sum, item) => sum + item.value, 0) || 0}
                </div>
                <div className="text-sm text-gray-500">Total Calls</div>
              </div>
            }
          />
        </ChartContainer>
        {/* Resolution Time by Priority */}
        <ChartContainer
          title="Resolution Time by Priority"
          subtitle="Average resolution time for different priority levels"
        >
          <BarChart
            data={analyticsData.resolutionTimeByPriority}
            bars={[{ dataKey: 'avgHours', name: 'Avg Hours' }]}
            height={300}
            orientation="horizontal"
            colorScheme="priority"
            formatters={{ avgHours: (val) => `${val} hours` }}
          />
        </ChartContainer>

        {/* Service Performance Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Summary</h3>
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Key Metrics</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Completion Rate</span>
                  <span className="font-medium text-green-600">
                    {((analyticsData.completedServiceCalls / analyticsData.totalServiceCalls) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Open Call Rate</span>
                  <span className="font-medium text-orange-600">
                    {((analyticsData.openServiceCalls / analyticsData.totalServiceCalls) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Calls This Period</span>
                  <span className="font-medium text-blue-600">
                    {analyticsData.serviceCallsInPeriod}
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Top Performer</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {analyticsData.executivePerformance && analyticsData.executivePerformance.length > 0
                    ? analyticsData.executivePerformance.reduce((top, exec) =>
                        exec.completionRate > top.completionRate ? exec : top
                      ).name
                    : 'No data'
                  }
                </span>
                <span className="font-medium text-green-600">
                  {analyticsData.executivePerformance && analyticsData.executivePerformance.length > 0
                    ? `${analyticsData.executivePerformance.reduce((top, exec) =>
                        exec.completionRate > top.completionRate ? exec : top
                      ).completionRate}%`
                    : '0%'
                  }
                </span>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Most Common Service</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {analyticsData.serviceTypeDistribution && analyticsData.serviceTypeDistribution.length > 0
                    ? analyticsData.serviceTypeDistribution.reduce((top, type) =>
                        type.value > top.value ? type : top
                      ).name
                    : 'No data'
                  }
                </span>
                <span className="font-medium text-gray-900">
                  {analyticsData.serviceTypeDistribution && analyticsData.serviceTypeDistribution.length > 0
                    ? `${analyticsData.serviceTypeDistribution.reduce((top, type) =>
                        type.value > top.value ? type : top
                      ).value} calls`
                    : '0 calls'
                  }
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ServiceAnalytics;
