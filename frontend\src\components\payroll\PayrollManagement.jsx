import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Spinner, <PERSON>, Badge } from '../ui';
import { DollarSign, Calculator, Download, CheckCircle, Clock, AlertCircle, Users } from 'lucide-react';
import { payrollAPI } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';

const PayrollManagement = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [payrollRecords, setPayrollRecords] = useState([]);
  const [showProcessForm, setShowProcessForm] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [filters, setFilters] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    status: 'all'
  });

  useEffect(() => {
    fetchPayrollRecords();
  }, [filters]);

  const fetchPayrollRecords = async () => {
    try {
      setLoading(true);
      const response = await payrollAPI.getRecords({
        month: filters.month,
        year: filters.year,
        status: filters.status !== 'all' ? filters.status : undefined,
        page: 1,
        limit: 50
      });
      setPayrollRecords(response.data.payrollRecords);
    } catch (error) {
      console.error('Error fetching payroll records:', error);
      toast.error('Failed to fetch payroll records');
    } finally {
      setLoading(false);
    }
  };

  const handleProcessPayroll = async (processData) => {
    try {
      const response = await payrollAPI.processPayroll(processData);
      toast.success(`Payroll processed for ${response.data.processed.length} employees`);
      setShowProcessForm(false);
      fetchPayrollRecords();
    } catch (error) {
      console.error('Error processing payroll:', error);
      toast.error(error.response?.data?.message || 'Failed to process payroll');
    }
  };

  const handleApprovePayroll = async (recordId) => {
    if (!confirm('Are you sure you want to approve this payroll record?')) {
      return;
    }

    try {
      await payrollAPI.approvePayroll(recordId);
      toast.success('Payroll approved successfully!');
      fetchPayrollRecords();
    } catch (error) {
      console.error('Error approving payroll:', error);
      toast.error('Failed to approve payroll');
    }
  };

  const handleMarkPaid = async (recordId, paymentData) => {
    try {
      await payrollAPI.markPaid(recordId, paymentData);
      toast.success('Payroll marked as paid successfully!');
      fetchPayrollRecords();
    } catch (error) {
      console.error('Error marking payroll as paid:', error);
      toast.error('Failed to mark payroll as paid');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      calculated: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      paid: 'bg-purple-100 text-purple-800',
      cancelled: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'calculated':
        return <Calculator className="h-4 w-4 text-blue-600" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'paid':
        return <DollarSign className="h-4 w-4 text-purple-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  const getTotalSalary = () => {
    return payrollRecords.reduce((total, record) => total + parseFloat(record.net_salary), 0);
  };

  const getStatusCounts = () => {
    return payrollRecords.reduce((counts, record) => {
      counts[record.status] = (counts[record.status] || 0) + 1;
      return counts;
    }, {});
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payroll Management</h1>
          <p className="text-gray-600">Process and manage employee payroll</p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowProcessForm(true)}
            className="flex items-center"
          >
            <Calculator className="mr-2 h-4 w-4" />
            Process Payroll
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Employees</p>
              <p className="text-2xl font-bold text-blue-600">{payrollRecords.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Salary</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(getTotalSalary())}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-purple-600">{statusCounts.approved || 0}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{statusCounts.calculated || 0}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Month
            </label>
            <select
              value={filters.month}
              onChange={(e) => setFilters({ ...filters, month: parseInt(e.target.value) })}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              {Array.from({ length: 12 }, (_, i) => (
                <option key={i + 1} value={i + 1}>
                  {new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'long' })}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Year
            </label>
            <select
              value={filters.year}
              onChange={(e) => setFilters({ ...filters, year: parseInt(e.target.value) })}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value={new Date().getFullYear()}>{new Date().getFullYear()}</option>
              <option value={new Date().getFullYear() - 1}>{new Date().getFullYear() - 1}</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="calculated">Calculated</option>
              <option value="approved">Approved</option>
              <option value="paid">Paid</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Payroll Records */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Payroll Records</h3>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center"
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>

        {payrollRecords.length === 0 ? (
          <div className="text-center py-8">
            <Calculator className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payroll records</h3>
            <p className="mt-1 text-sm text-gray-500">
              Process payroll for the selected month to see records here.
            </p>
            <div className="mt-6">
              <Button onClick={() => setShowProcessForm(true)}>
                <Calculator className="mr-2 h-4 w-4" />
                Process Payroll
              </Button>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payroll Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gross Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deductions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Net Salary
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payrollRecords.map((record) => (
                  <tr key={record.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {record.employee.first_name} {record.employee.last_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {record.employee.employee_code}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {record.payroll_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(record.gross_salary)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(record.total_deductions)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(record.net_salary)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(record.status)}
                        <Badge className={getStatusColor(record.status)}>
                          {record.status.toUpperCase()}
                        </Badge>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => setSelectedRecord(record)}
                          variant="outline"
                          size="sm"
                        >
                          View
                        </Button>
                        
                        {record.status === 'calculated' && (
                          <Button
                            onClick={() => handleApprovePayroll(record.id)}
                            size="sm"
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Approve
                          </Button>
                        )}
                        
                        {record.status === 'approved' && (
                          <Button
                            onClick={() => handleMarkPaid(record.id, { 
                              payment_method: 'bank_transfer',
                              payment_reference: `PAY-${record.payroll_number}`
                            })}
                            size="sm"
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            Mark Paid
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Process Payroll Modal */}
      {showProcessForm && (
        <Modal
          isOpen={showProcessForm}
          onClose={() => setShowProcessForm(false)}
          title="Process Payroll"
          size="md"
        >
          <ProcessPayrollForm
            onSubmit={handleProcessPayroll}
            onCancel={() => setShowProcessForm(false)}
          />
        </Modal>
      )}

      {/* Payroll Detail Modal */}
      {selectedRecord && (
        <Modal
          isOpen={!!selectedRecord}
          onClose={() => setSelectedRecord(null)}
          title="Payroll Details"
          size="lg"
        >
          <PayrollDetailView
            record={selectedRecord}
            onClose={() => setSelectedRecord(null)}
          />
        </Modal>
      )}
    </div>
  );
};

// Process Payroll Form Component
const ProcessPayrollForm = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    employee_ids: []
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onSubmit(formData);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Month *
          </label>
          <select
            value={formData.month}
            onChange={(e) => setFormData({ ...formData, month: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            required
          >
            {Array.from({ length: 12 }, (_, i) => (
              <option key={i + 1} value={i + 1}>
                {new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'long' })}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Year *
          </label>
          <select
            value={formData.year}
            onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            required
          >
            <option value={new Date().getFullYear()}>{new Date().getFullYear()}</option>
            <option value={new Date().getFullYear() - 1}>{new Date().getFullYear() - 1}</option>
          </select>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex items-center">
          <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
          <span className="text-sm text-yellow-800">
            This will process payroll for all active employees for the selected month.
            Make sure attendance data is complete before processing.
          </span>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Spinner size="sm" className="mr-2" />
              Processing...
            </>
          ) : (
            <>
              <Calculator className="mr-2 h-4 w-4" />
              Process Payroll
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

// Payroll Detail View Component
const PayrollDetailView = ({ record, onClose }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Employee Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Employee Information</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Name:</span>
            <span className="ml-2 font-medium">
              {record.employee.first_name} {record.employee.last_name}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Employee Code:</span>
            <span className="ml-2 font-medium">{record.employee.employee_code}</span>
          </div>
          <div>
            <span className="text-gray-600">Department:</span>
            <span className="ml-2 font-medium">{record.employee.department}</span>
          </div>
          <div>
            <span className="text-gray-600">Payroll Number:</span>
            <span className="ml-2 font-medium">{record.payroll_number}</span>
          </div>
        </div>
      </div>

      {/* Attendance Summary */}
      <div>
        <h4 className="font-medium text-gray-900 mb-3">Attendance Summary</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{record.total_working_days}</div>
            <div className="text-blue-800">Working Days</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{record.present_days}</div>
            <div className="text-green-800">Present Days</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{record.absent_days}</div>
            <div className="text-red-800">Absent Days</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{record.overtime_hours}</div>
            <div className="text-purple-800">Overtime Hours</div>
          </div>
        </div>
      </div>

      {/* Salary Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Earnings */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Earnings</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Basic Salary:</span>
              <span className="font-medium">{formatCurrency(record.basic_salary)}</span>
            </div>
            <div className="flex justify-between">
              <span>HRA:</span>
              <span className="font-medium">{formatCurrency(record.hra)}</span>
            </div>
            <div className="flex justify-between">
              <span>Transport Allowance:</span>
              <span className="font-medium">{formatCurrency(record.transport_allowance)}</span>
            </div>
            <div className="flex justify-between">
              <span>Medical Allowance:</span>
              <span className="font-medium">{formatCurrency(record.medical_allowance)}</span>
            </div>
            <div className="flex justify-between">
              <span>Special Allowance:</span>
              <span className="font-medium">{formatCurrency(record.special_allowance)}</span>
            </div>
            <div className="flex justify-between">
              <span>Overtime Amount:</span>
              <span className="font-medium">{formatCurrency(record.overtime_amount)}</span>
            </div>
            <div className="flex justify-between font-semibold text-green-600 pt-2 border-t">
              <span>Gross Salary:</span>
              <span>{formatCurrency(record.gross_salary)}</span>
            </div>
          </div>
        </div>

        {/* Deductions */}
        <div>
          <h4 className="font-medium text-gray-900 mb-3">Deductions</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>PF (Employee):</span>
              <span className="font-medium">{formatCurrency(record.pf_employee)}</span>
            </div>
            <div className="flex justify-between">
              <span>ESI (Employee):</span>
              <span className="font-medium">{formatCurrency(record.esi_employee)}</span>
            </div>
            <div className="flex justify-between">
              <span>Professional Tax:</span>
              <span className="font-medium">{formatCurrency(record.professional_tax)}</span>
            </div>
            <div className="flex justify-between">
              <span>Income Tax:</span>
              <span className="font-medium">{formatCurrency(record.income_tax)}</span>
            </div>
            <div className="flex justify-between">
              <span>Late Deduction:</span>
              <span className="font-medium">{formatCurrency(record.late_deduction)}</span>
            </div>
            <div className="flex justify-between">
              <span>Absent Deduction:</span>
              <span className="font-medium">{formatCurrency(record.absent_deduction)}</span>
            </div>
            <div className="flex justify-between font-semibold text-red-600 pt-2 border-t">
              <span>Total Deductions:</span>
              <span>{formatCurrency(record.total_deductions)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Net Salary */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-green-800">Net Salary:</span>
          <span className="text-2xl font-bold text-green-600">
            {formatCurrency(record.net_salary)}
          </span>
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={onClose} variant="outline">
          Close
        </Button>
      </div>
    </div>
  );
};

export default PayrollManagement;
