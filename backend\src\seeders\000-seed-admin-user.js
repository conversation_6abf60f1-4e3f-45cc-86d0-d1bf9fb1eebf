#!/usr/bin/env node

/**
 * Admin User Seeder
 * Creates the default admin user for TallyCRM production
 */

import models from '../models/index.js';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Admin@123',
  firstName: 'Admin',
  lastName: 'User',
  phone: '+91-9999999999'
};

const seedAdminUser = async () => {
  try {
    logger.info('🚀 Seeding admin user...');

    // Create or find default tenant
    const [tenant] = await models.Tenant.findOrCreate({
      where: { slug: 'default' },
      defaults: {
        id: uuidv4(),
        name: 'TallyCRM Default',
        slug: 'default',
        subscription_status: 'active',
        subscription_plan: 'enterprise',
        max_users: 1000,
        max_customers: 10000,
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        settings: {
          theme: 'purple',
          language: 'en',
          is_default_tenant: true
        }
      }
    });

    logger.info('✅ Default tenant created/found:', tenant.name);

    // Check if admin user already exists
    const existingAdmin = await models.User.findOne({
      where: { email: ADMIN_CREDENTIALS.email }
    });

    if (existingAdmin) {
      logger.info('ℹ️  Admin user already exists, skipping creation');
      logger.info('📧 Email:', ADMIN_CREDENTIALS.email);
      logger.info('🔑 Password:', ADMIN_CREDENTIALS.password);

      // Check if executive record exists
      const existingExecutive = await models.Executive.findOne({
        where: { user_id: existingAdmin.id }
      });

      if (!existingExecutive) {
        logger.info('ℹ️  Creating missing executive record for existing admin...');
        // Create the missing executive record (will be handled below)
      } else {
        logger.info('✅ Admin user and executive record already exist');
        return;
      }
    }

    let adminUser = existingAdmin;

    // Create admin user if it doesn't exist
    if (!existingAdmin) {
      // Create admin user - password will be hashed automatically by the model hook
      adminUser = await models.User.create({
        id: uuidv4(),
        tenant_id: tenant.id,
        email: ADMIN_CREDENTIALS.email,
        password: ADMIN_CREDENTIALS.password, // Pass plain text - model will hash it
        first_name: ADMIN_CREDENTIALS.firstName,
        last_name: ADMIN_CREDENTIALS.lastName,
        phone: ADMIN_CREDENTIALS.phone,
        is_active: true,
        is_verified: true,
        preferences: {
          theme: 'light',
          language: 'en',
          notifications: {
            email: true,
            browser: true,
            sms: false,
          },
        },
      });

      logger.info('✅ Admin user created successfully!');
    }

    // Find or create admin role
    const [adminRole] = await models.Role.findOrCreate({
      where: { slug: 'admin' },
      defaults: {
        name: 'Administrator',
        slug: 'admin',
        description: 'Full system administrator access',
        is_system: true,
        level: 1
      }
    });

    // Assign admin role to user if not already assigned
    const existingUserRole = await models.UserRole.findOne({
      where: {
        user_id: adminUser.id,
        role_id: adminRole.id
      }
    });

    if (!existingUserRole) {
      await models.UserRole.create({
        user_id: adminUser.id,
        role_id: adminRole.id,
        assigned_at: new Date(),
        is_active: true,
      });
      logger.info('✅ Admin role assigned to user');
    } else {
      logger.info('ℹ️  Admin role already assigned to user');
    }

    // Find or create a default designation for admin
    const [adminDesignation] = await models.Designation.findOrCreate({
      where: { code: 'ADMIN' },
      defaults: {
        name: 'Administrator',
        code: 'ADMIN',
        description: 'System Administrator',
        level: 1,
        department: 'Administration',
        is_mandatory: false,
        is_active: true,
        sort_order: 0,
      }
    });

    // Create executive record for admin if it doesn't exist
    const existingExecutive = await models.Executive.findOne({
      where: { user_id: adminUser.id }
    });

    if (!existingExecutive) {
      await models.Executive.create({
        tenant_id: tenant.id,
        user_id: adminUser.id,
        employee_code: 'ADMIN001',
        first_name: ADMIN_CREDENTIALS.firstName,
        last_name: ADMIN_CREDENTIALS.lastName,
        email: ADMIN_CREDENTIALS.email,
        phone: ADMIN_CREDENTIALS.phone,
        designation_id: adminDesignation.id,
        department: 'management',
        address: 'TallyCRM Head Office',
        city: 'Mumbai',
        state: 'Maharashtra',
        postal_code: '400001',
        date_of_joining: new Date(),
        salary: 100000.00,
        commission_rate: 0.00,
        target_amount: 0.00,
        is_active: true,
        skills: ['System Administration', 'CRM Management', 'User Management'],
        areas_covered: ['All'],
      });
      logger.info('✅ Admin executive record created');
    } else {
      logger.info('ℹ️  Admin executive record already exists');
    }

    logger.info('✅ Admin user setup completed successfully!');
    logger.info('📧 Email:', ADMIN_CREDENTIALS.email);
    logger.info('🔑 Password:', ADMIN_CREDENTIALS.password);
    logger.info('🏢 Tenant:', tenant.name);

  } catch (error) {
    logger.error('❌ Error seeding admin user:', error);
    throw error;
  }
};

export default seedAdminUser;

// Allow direct execution
if (import.meta.url === `file://${process.argv[1]}`) {
  seedAdminUser()
    .then(() => {
      logger.info('🎉 Admin user seeding completed!');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('❌ Admin user seeding failed:', error);
      process.exit(1);
    });
}
