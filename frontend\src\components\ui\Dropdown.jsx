import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown } from 'react-icons/fa';

const Dropdown = ({
  trigger,
  children,
  className = '',
  align = 'right',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleItemClick = () => {
    setIsOpen(false);
  };

  const alignmentClasses = {
    left: 'left-0',
    right: 'right-0',
    center: 'left-1/2 transform -translate-x-1/2'
  };

  return (
    <div className={`relative inline-block text-left ${className}`} ref={dropdownRef}>
      <div onClick={handleToggle}>
        {trigger || (
          <button
            type="button"
            className={`inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
              disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={disabled}
          >
            Actions
            <FaChevronDown className="w-3 h-3 ml-2" />
          </button>
        )}
      </div>

      {isOpen && (
        <div
          className={`absolute mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 ${alignmentClasses[align]}`}
          style={{ zIndex: 10000 }}
        >
          <div className="py-1" onClick={handleItemClick}>
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

const DropdownItem = ({
  children,
  onClick,
  className = '',
  disabled = false,
  icon = null
}) => {
  return (
    <button
      className={`w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900 ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
      onClick={onClick}
      disabled={disabled}
    >
      {icon && <span className="mr-3">{icon}</span>}
      {children}
    </button>
  );
};

const DropdownDivider = () => {
  return <hr className="my-1 border-gray-200" />;
};

export default Dropdown;
export { DropdownItem, DropdownDivider };
