import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Badge, Spinner } from '../ui';
import { Clock, MapPin, Calendar, TrendingUp, Users, AlertCircle } from 'lucide-react';
import { attendanceAPI } from '../../services/api';
import { useAuth } from '../../hooks/useAuth';
import { toast } from 'react-hot-toast';

const AttendanceDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [todayAttendance, setTodayAttendance] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationLoading, setLocationLoading] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);
  const [checkingOut, setCheckingOut] = useState(false);

  useEffect(() => {
    fetchTodayAttendance();
  }, []);

  const fetchTodayAttendance = async () => {
    try {
      setLoading(true);
      const response = await attendanceAPI.getTodayAttendance();
      setTodayAttendance(response.data);
    } catch (error) {
      console.error('Error fetching today attendance:', error);
      toast.error('Failed to fetch attendance data');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported'));
        return;
      }

      setLocationLoading(true);
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          };
          setCurrentLocation(location);
          setLocationLoading(false);
          resolve(location);
        },
        (error) => {
          setLocationLoading(false);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  };

  const handleCheckIn = async () => {
    try {
      setCheckingIn(true);
      
      // Get current location
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
        // Continue without location if user allows
        if (!confirm('Location access failed. Continue check-in without location?')) {
          return;
        }
      }

      const checkInData = {
        location: location,
        notes: ''
      };

      await attendanceAPI.checkIn(checkInData);
      toast.success('Checked in successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-in error:', error);
      toast.error(error.response?.data?.message || 'Failed to check in');
    } finally {
      setCheckingIn(false);
    }
  };

  const handleCheckOut = async () => {
    try {
      setCheckingOut(true);
      
      // Get current location
      let location = null;
      try {
        location = await getCurrentLocation();
      } catch (locationError) {
        console.warn('Could not get location:', locationError);
      }

      const checkOutData = {
        location: location,
        notes: ''
      };

      await attendanceAPI.checkOut(checkOutData);
      toast.success('Checked out successfully!');
      fetchTodayAttendance();
    } catch (error) {
      console.error('Check-out error:', error);
      toast.error(error.response?.data?.message || 'Failed to check out');
    } finally {
      setCheckingOut(false);
    }
  };

  const formatTime = (timeString) => {
    if (!timeString) return '--:--';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      present: 'bg-green-100 text-green-800',
      late: 'bg-yellow-100 text-yellow-800',
      absent: 'bg-red-100 text-red-800',
      half_day: 'bg-blue-100 text-blue-800',
      work_from_home: 'bg-purple-100 text-purple-800',
      on_leave: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getCurrentHours = () => {
    if (!todayAttendance?.attendanceRecord?.check_in_time) return '0.00';
    
    const checkIn = new Date(todayAttendance.attendanceRecord.check_in_time);
    const now = todayAttendance.attendanceRecord.check_out_time 
      ? new Date(todayAttendance.attendanceRecord.check_out_time)
      : new Date();
    
    const diffMs = now - checkIn;
    const diffHours = diffMs / (1000 * 60 * 60);
    return Math.max(0, diffHours).toFixed(2);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  const { hasCheckedIn, hasCheckedOut, attendanceRecord, currentHours } = todayAttendance || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Attendance Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.first_name}!</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Today</p>
          <p className="text-lg font-semibold">{new Date().toLocaleDateString()}</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Check-in/out Card */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Quick Actions</h3>
            <Clock className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {!hasCheckedIn ? (
              <Button
                onClick={handleCheckIn}
                disabled={checkingIn || locationLoading}
                className="w-full bg-green-600 hover:bg-green-700"
                size="lg"
              >
                {checkingIn ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Checking In...
                  </>
                ) : (
                  <>
                    <Clock className="mr-2 h-4 w-4" />
                    Check In
                  </>
                )}
              </Button>
            ) : !hasCheckedOut ? (
              <Button
                onClick={handleCheckOut}
                disabled={checkingOut || locationLoading}
                className="w-full bg-red-600 hover:bg-red-700"
                size="lg"
              >
                {checkingOut ? (
                  <>
                    <Spinner size="sm" className="mr-2" />
                    Checking Out...
                  </>
                ) : (
                  <>
                    <Clock className="mr-2 h-4 w-4" />
                    Check Out
                  </>
                )}
              </Button>
            ) : (
              <div className="text-center py-4">
                <div className="text-green-600 font-semibold">
                  ✓ Day Complete
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  You have completed your attendance for today
                </p>
              </div>
            )}

            {locationLoading && (
              <div className="flex items-center justify-center text-sm text-gray-500">
                <MapPin className="mr-1 h-4 w-4" />
                Getting location...
              </div>
            )}
          </div>
        </Card>

        {/* Today's Status */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Today's Status</h3>
            <Calendar className="h-5 w-5 text-gray-400" />
          </div>
          
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Status:</span>
              {attendanceRecord ? (
                <Badge className={getStatusColor(attendanceRecord.status)}>
                  {attendanceRecord.status.replace('_', ' ').toUpperCase()}
                </Badge>
              ) : (
                <Badge className="bg-gray-100 text-gray-800">NOT MARKED</Badge>
              )}
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Check In:</span>
              <span className="font-medium">
                {formatTime(attendanceRecord?.check_in_time)}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Check Out:</span>
              <span className="font-medium">
                {formatTime(attendanceRecord?.check_out_time)}
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Hours Worked:</span>
              <span className="font-medium text-blue-600">
                {getCurrentHours()} hrs
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Weekly Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">This Week Overview</h3>
          <TrendingUp className="h-5 w-5 text-gray-400" />
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">5</div>
            <div className="text-sm text-gray-600">Present Days</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">1</div>
            <div className="text-sm text-gray-600">Late Arrivals</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">42.5</div>
            <div className="text-sm text-gray-600">Total Hours</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">2.5</div>
            <div className="text-sm text-gray-600">Overtime</div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AttendanceDashboard;
