import { DataTypes } from 'sequelize';

export const up = async (queryInterface, Sequelize) => {
    await queryInterface.createTable('notification_schedules', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tenant_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      customer_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'customers',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      renewal_type: {
        type: DataTypes.ENUM('amc', 'tss', 'license', 'maintenance', 'support'),
        allowNull: false,
        comment: 'Type of renewal (AMC, TSS, License, etc.)',
      },
      renewal_record_id: {
        type: DataTypes.UUID,
        allowNull: false,
        comment: 'ID of the specific renewal record (CustomerAMC.id, CustomerTSS.id, etc.)',
      },
      expiry_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: 'The actual expiry date of the service',
      },
      notify_at: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        comment: 'Date when the notification should be sent',
      },
      days_before_expiry: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Number of days before expiry this notification is scheduled',
      },
      notification_type: {
        type: DataTypes.ENUM('reminder', 'overdue'),
        allowNull: false,
        defaultValue: 'reminder',
        comment: 'Type of notification (reminder or overdue)',
      },
      status: {
        type: DataTypes.ENUM('scheduled', 'sent', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'scheduled',
        comment: 'Status of the notification',
      },
      sent_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Timestamp when notification was sent',
      },
      email_sent: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Whether email notification was sent',
      },
      sms_sent: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Whether SMS notification was sent',
      },
      whatsapp_sent: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Whether WhatsApp notification was sent',
      },
      email_message_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Email message ID for tracking',
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Error message if notification failed',
      },
      retry_count: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: 'Number of retry attempts',
      },
      max_retries: {
        type: DataTypes.INTEGER,
        defaultValue: 3,
        comment: 'Maximum number of retry attempts',
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('notification_schedules', ['tenant_id']);
    await queryInterface.addIndex('notification_schedules', ['customer_id']);
    await queryInterface.addIndex('notification_schedules', ['renewal_type']);
    await queryInterface.addIndex('notification_schedules', ['renewal_record_id']);
    await queryInterface.addIndex('notification_schedules', ['expiry_date']);
    await queryInterface.addIndex('notification_schedules', ['notify_at']);
    await queryInterface.addIndex('notification_schedules', ['status']);
    await queryInterface.addIndex('notification_schedules', ['notification_type']);

    // Add unique constraint to prevent duplicate notifications
    await queryInterface.addIndex('notification_schedules', {
      fields: ['customer_id', 'renewal_type', 'renewal_record_id', 'notify_at'],
      unique: true,
      name: 'unique_notification_schedule',
    });

    // Add composite index for efficient querying of pending notifications
    await queryInterface.addIndex('notification_schedules', {
      fields: ['notify_at', 'status'],
      name: 'idx_pending_notifications',
      where: {
        status: 'scheduled',
      },
    });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('notification_schedules');
};
