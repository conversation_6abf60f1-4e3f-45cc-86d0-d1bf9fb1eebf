// Test the improved customer import functionality
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');

const API_BASE = 'http://localhost:3001/api/v1';

async function testImprovedImport() {
  try {
    console.log('🧪 Testing Improved Customer Import...');
    
    // Step 1: Authenticate
    const authResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin@123'
      })
    });
    
    const authData = await authResponse.json();
    if (!authData.success) {
      throw new Error('Authentication failed');
    }
    console.log('✅ Authentication successful');
    
    // Step 2: Create test CSV with validation errors
    console.log('\n2. Creating test CSV with validation errors...');
    
    const csvContent = `"Company Name*","Customer Code*","Email*","Phone*","Tally Serial Number*","GST Number","PAN Number"
"Valid Company","VALID001","<EMAIL>","+91-9876543210","TSN001","27**********1Z5","**********"
"Invalid Company","INVALID001","invalid-email","","TSN002","INVALID_GST","INVALID_PAN"
"Another Valid","VALID002","<EMAIL>","+91-9876543211","TSN003","",""`; 
    
    fs.writeFileSync('test-validation.csv', csvContent);
    console.log('✅ Test CSV created with validation errors');
    
    // Step 3: Test preview
    console.log('\n3. Testing preview...');
    
    const form = new FormData();
    form.append('file', fs.createReadStream('test-validation.csv'));
    
    const previewResponse = await fetch(`${API_BASE}/customers/import/preview`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        ...form.getHeaders()
      },
      body: form
    });
    
    const previewData = await previewResponse.json();
    if (!previewData.success) {
      throw new Error('Preview failed: ' + previewData.message);
    }
    
    console.log('✅ Preview successful');
    console.log(`   Total rows: ${previewData.data.totalRows}`);
    console.log(`   Valid rows: ${previewData.data.validRows}`);
    console.log(`   Error rows: ${previewData.data.errorRows}`);
    console.log(`   Required fields: ${previewData.data.validationRules.required_fields.length}`);
    
    // Check validation errors
    if (previewData.data.errors && previewData.data.errors.length > 0) {
      console.log('\n📋 Validation errors found:');
      previewData.data.errors.forEach(error => {
        console.log(`   Row ${error.row}: ${error.message}`);
      });
    }
    
    // Step 4: Test import with skip errors (default)
    console.log('\n4. Testing import with skip errors...');
    
    const importForm1 = new FormData();
    importForm1.append('file', fs.createReadStream('test-validation.csv'));
    importForm1.append('skipErrors', 'true');
    importForm1.append('forceImportErrors', 'false');
    
    const importResponse1 = await fetch(`${API_BASE}/customers/import/execute`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        ...importForm1.getHeaders()
      },
      body: importForm1
    });
    
    const importData1 = await importResponse1.json();
    console.log('📊 Skip errors result:');
    console.log(`   Success: ${importData1.success}`);
    if (importData1.success) {
      console.log(`   Imported: ${importData1.data.successCount} customers`);
      console.log(`   Failed: ${importData1.data.errorCount} customers`);
      console.log(`   Skipped: ${importData1.data.skippedCount} customers`);
    } else {
      console.log(`   Error: ${importData1.message}`);
    }
    
    // Step 5: Test import with force errors
    console.log('\n5. Testing import with force errors...');
    
    const importForm2 = new FormData();
    importForm2.append('file', fs.createReadStream('test-validation.csv'));
    importForm2.append('skipErrors', 'false');
    importForm2.append('forceImportErrors', 'true');
    
    const importResponse2 = await fetch(`${API_BASE}/customers/import/execute`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authData.token}`,
        ...importForm2.getHeaders()
      },
      body: importForm2
    });
    
    const importData2 = await importResponse2.json();
    console.log('📊 Force import result:');
    console.log(`   Success: ${importData2.success}`);
    if (importData2.success) {
      console.log(`   Imported: ${importData2.data.successCount} customers`);
      console.log(`   Failed: ${importData2.data.errorCount} customers`);
      console.log(`   Skipped: ${importData2.data.skippedCount} customers`);
    } else {
      console.log(`   Error: ${importData2.message}`);
    }
    
    // Cleanup
    fs.unlinkSync('test-validation.csv');
    console.log('\n🧹 Cleanup completed');
    
    console.log('\n🎉 Improved Import Test Summary:');
    console.log('  ✅ Removed confusing column mapping');
    console.log('  ✅ Clear validation error display');
    console.log('  ✅ Force import option working');
    console.log('  ✅ Better success/failure messages');
    console.log('  ✅ Proper user experience flow');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testImprovedImport();
