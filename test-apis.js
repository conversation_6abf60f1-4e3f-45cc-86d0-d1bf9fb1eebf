/**
 * Test script for the APIs created in this session
 * Run with: node test-apis.js
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8080/api/v1';
let authToken = '';

// Test configuration
const testConfig = {
  email: '<EMAIL>',
  password: 'Admin@123',
  leadId: '', // Will be populated during tests
  customerId: '', // Will be populated during tests
};

// Helper function to make authenticated requests
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
    ...options.headers,
  };

  const response = await fetch(url, {
    ...options,
    headers,
  });

  const data = await response.json();
  return { response, data };
}

// Test authentication
async function testAuth() {
  console.log('\n🔐 Testing Authentication...');

  try {
    const { response, data } = await apiRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: testConfig.email,
        password: testConfig.password,
      }),
    });

    console.log(`   Response Status: ${response.status}`);
    console.log(`   Response Data:`, JSON.stringify(data, null, 2));

    if (response.ok && (data.status === "success" || data.success) && data.token) {
      authToken = data.token;
      console.log('✅ Authentication successful');
      console.log(`   Token received: ${authToken.substring(0, 20)}...`);
      console.log(`   User: ${data.user.first_name} ${data.user.last_name} (${data.user.email})`);
      return true;
    } else {
      console.log('❌ Authentication failed:', data.message || 'Unknown error');
      console.log('   Full response:', JSON.stringify(data, null, 2));
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication error:', error.message);
    console.log('   Stack:', error.stack);
    return false;
  }
}

// Test Lead Contact History APIs
async function testLeadContactHistory() {
  console.log('\n📞 Testing Lead Contact History APIs...');
  
  try {
    // First, get a lead to test with
    const { data: leadsData } = await apiRequest('/leads?limit=1');
    if (!leadsData.success || leadsData.data.leads.length === 0) {
      console.log('❌ No leads found to test with');
      return;
    }
    
    const leadId = leadsData.data.leads[0].id;
    testConfig.leadId = leadId;
    console.log(`📋 Using lead ID: ${leadId}`);

    // Test getting contact history (should be empty initially)
    const { data: historyData } = await apiRequest(`/leads/${leadId}/contact-history`);
    if (historyData.success) {
      console.log('✅ Get contact history successful');
      console.log(`   Found ${historyData.data.contactHistory.length} contact entries`);
    } else {
      console.log('❌ Get contact history failed:', historyData.message);
    }

    // Test adding contact history
    const contactData = {
      contact_type: 'phone',
      contact_date: new Date().toISOString(),
      duration_minutes: 15,
      outcome: 'interested',
      notes: 'Test contact from API test script',
      next_follow_up: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
    };

    const { data: addContactData } = await apiRequest(`/leads/${leadId}/contact-history`, {
      method: 'POST',
      body: JSON.stringify(contactData),
    });

    if (addContactData.success) {
      console.log('✅ Add contact history successful');
      console.log(`   Contact ID: ${addContactData.data.contactHistory.id}`);
    } else {
      console.log('❌ Add contact history failed:', addContactData.message);
    }

  } catch (error) {
    console.log('❌ Lead contact history test error:', error.message);
  }
}

// Test Lead Conversion API
async function testLeadConversion() {
  console.log('\n🔄 Testing Lead Conversion API...');

  try {
    if (!testConfig.leadId) {
      console.log('❌ No lead ID available for conversion test');
      return;
    }

    const conversionData = {
      tallySerialNumber: `TEST-${Date.now()}`,
      email: '<EMAIL>',
      address: 'Test Address',
      city: 'Test City',
      state: 'Test State',
      pincode: '123456',
    };

    const { response, data: conversionResult } = await apiRequest(`/leads/${testConfig.leadId}/convert`, {
      method: 'POST',
      body: JSON.stringify(conversionData),
    });

    console.log(`   Response Status: ${response.status}`);

    if (conversionResult.success) {
      console.log('✅ Lead conversion successful');
      console.log(`   Customer ID: ${conversionResult.data.customer.id}`);
      console.log(`   Customer Code: ${conversionResult.data.customer.customer_code}`);
      console.log(`   Tally Serial: ${conversionResult.data.customer.tally_serial_number}`);
      testConfig.customerId = conversionResult.data.customer.id;
    } else {
      console.log('❌ Lead conversion failed:', conversionResult.message);
      if (conversionResult.errors) {
        console.log('   Errors:', JSON.stringify(conversionResult.errors, null, 2));
      }
      if (conversionResult.error) {
        console.log('   Error Details:', conversionResult.error);
      }
    }

  } catch (error) {
    console.log('❌ Lead conversion test error:', error.message);
  }
}

// Test Notification Template APIs
async function testNotificationTemplates() {
  console.log('\n📧 Testing Notification Template APIs...');
  
  try {
    // Test getting template variables
    const { data: variablesData } = await apiRequest('/notifications/template-variables/new_lead');
    if (variablesData.success) {
      console.log('✅ Get template variables successful');
      console.log(`   Available variables: ${variablesData.data.variables.join(', ')}`);
    } else {
      console.log('❌ Get template variables failed:', variablesData.message);
    }

    // Test getting notification templates
    const { data: templatesData } = await apiRequest('/notifications/templates');
    if (templatesData.success) {
      console.log('✅ Get notification templates successful');
      console.log(`   Found ${templatesData.data.templates.length} templates`);
    } else {
      console.log('❌ Get notification templates failed:', templatesData.message);
    }

    // Test creating a notification template
    const templateData = {
      name: 'Test Template',
      type: 'new_lead',
      channel: 'email',
      subject: 'New Lead: {{customer_name}}',
      content: 'Hello! A new lead has been created for {{customer_name}}. Contact: {{contact_no}}. Amount: {{amount}}.',
      is_active: true,
      is_default: false,
    };

    const { data: createTemplateData } = await apiRequest('/notifications/templates', {
      method: 'POST',
      body: JSON.stringify(templateData),
    });

    if (createTemplateData.success) {
      console.log('✅ Create notification template successful');
      console.log(`   Template ID: ${createTemplateData.data.template.id}`);
    } else {
      console.log('❌ Create notification template failed:', createTemplateData.message);
      if (createTemplateData.errors) {
        console.log('   Errors:', createTemplateData.errors);
      }
    }

  } catch (error) {
    console.log('❌ Notification template test error:', error.message);
  }
}

// Test Notification Settings APIs
async function testNotificationSettings() {
  console.log('\n⚙️ Testing Notification Settings APIs...');
  
  try {
    // Test getting notification settings
    const { data: settingsData } = await apiRequest('/notifications/settings');
    if (settingsData.success) {
      console.log('✅ Get notification settings successful');
    } else {
      console.log('❌ Get notification settings failed:', settingsData.message);
    }

    // Test getting event types
    const { data: eventTypesData } = await apiRequest('/notifications/event-types');
    if (eventTypesData.success) {
      console.log('✅ Get notification event types successful');
      console.log(`   Found ${eventTypesData.data.length} event types`);
    } else {
      console.log('❌ Get notification event types failed:', eventTypesData.message);
    }

  } catch (error) {
    console.log('❌ Notification settings test error:', error.message);
  }
}

// Test all Lead APIs
async function testAllLeadAPIs() {
  console.log('\n📋 Testing All Lead APIs...');

  try {
    // Test getting leads
    const { data: leadsData } = await apiRequest('/leads?limit=5');
    if (leadsData.success) {
      console.log('✅ Get leads successful');
      console.log(`   Found ${leadsData.data.leads.length} leads`);

      if (leadsData.data.leads.length > 0) {
        testConfig.leadId = leadsData.data.leads[0].id;
        console.log(`   Using lead ID: ${testConfig.leadId}`);

        // Test getting single lead
        const { data: singleLeadData } = await apiRequest(`/leads/${testConfig.leadId}`);
        if (singleLeadData.success) {
          console.log('✅ Get single lead successful');
        } else {
          console.log('❌ Get single lead failed:', singleLeadData.message);
        }
      }
    } else {
      console.log('❌ Get leads failed:', leadsData.message);
    }

    // Test lead stats
    const { data: statsData } = await apiRequest('/leads/stats');
    if (statsData.success) {
      console.log('✅ Get lead stats successful');
      console.log(`   Total leads: ${statsData.data.totalLeads || 'N/A'}`);
    } else {
      console.log('❌ Get lead stats failed:', statsData.message);
    }

  } catch (error) {
    console.log('❌ Lead APIs test error:', error.message);
  }
}

// Test Customer APIs (to verify conversion works)
async function testCustomerAPIs() {
  console.log('\n👥 Testing Customer APIs...');

  try {
    // Test getting customers
    const { data: customersData } = await apiRequest('/customers?limit=5');
    if (customersData.success) {
      console.log('✅ Get customers successful');
      console.log(`   Found ${customersData.data.customers.length} customers`);
    } else {
      console.log('❌ Get customers failed:', customersData.message);
    }

    // If we have a converted customer, test getting it
    if (testConfig.customerId) {
      const { data: customerData } = await apiRequest(`/customers/${testConfig.customerId}`);
      if (customerData.success) {
        console.log('✅ Get converted customer successful');
        console.log(`   Customer: ${customerData.data.customer.company_name}`);
      } else {
        console.log('❌ Get converted customer failed:', customerData.message);
      }
    }

  } catch (error) {
    console.log('❌ Customer APIs test error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Comprehensive API Tests for TallyCRM Session Updates');
  console.log('='.repeat(70));

  // Test authentication first
  const authSuccess = await testAuth();
  if (!authSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Run all tests in sequence
  await testAllLeadAPIs();
  await testLeadContactHistory();
  await testLeadConversion();
  await testCustomerAPIs();
  await testNotificationTemplates();
  await testNotificationSettings();

  console.log('\n✨ All API Tests Completed!');
  console.log('='.repeat(70));

  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`   🔑 Authentication: ${authSuccess ? '✅ Passed' : '❌ Failed'}`);
  console.log(`   📋 Lead ID used: ${testConfig.leadId || 'None'}`);
  console.log(`   👥 Customer ID created: ${testConfig.customerId || 'None'}`);
  console.log('\n🎯 Key Features Tested:');
  console.log('   • Lead CRUD operations');
  console.log('   • Lead contact history tracking');
  console.log('   • Lead to customer conversion');
  console.log('   • Notification template management');
  console.log('   • Notification settings');
}

// Run the tests
runTests().catch(console.error);
