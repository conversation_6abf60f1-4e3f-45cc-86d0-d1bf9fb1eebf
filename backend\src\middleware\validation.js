import { validationResult } from 'express-validator';
import { AppError } from './errorHandler.js';
import models from '../models/index.js';
import { logger } from '../utils/logger.js';

/**
 * Middleware to validate request data using express-validator
 */
export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value,
    }));

    // Create field-specific error object for frontend
    const fieldErrors = {};
    errorMessages.forEach(error => {
      fieldErrors[error.field] = error.message;
    });

    const message = `Validation failed: ${errorMessages.map(e => e.message).join(', ')}`;

    // Return structured error response for frontend
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: fieldErrors,
      details: errorMessages
    });
  }

  next();
};

/**
 * Middleware to map frontend field names to backend field names
 * Converts camelCase to snake_case for service call creation
 */
export const mapServiceCallFields = (req, res, next) => {
  try {
    const fieldMapping = {
      // Basic service call fields
      'serviceNumber': 'call_number',
      'customerId': 'customer_id',
      'customerName': 'company_name',
      'tallySerialNumber': 'tally_serial_number',
      'contactNumber': 'contact_number',
      'executiveId': 'assigned_to',
      'serviceType': 'call_type',
      'companyName': 'company_name',
      'typeOfCallId': 'type_of_call_id',
      'callBillingType': 'call_billing_type',
      'onsiteExecutiveId': 'assigned_to', // For onsite calls
      'serviceLocation': 'service_location',
      'locationCoordinates': 'location_coordinates',
      'googleMapsLink': 'google_maps_link',
      'tallyVersion': 'tally_version',
      'designation': 'designation',
      'tssStatus': 'tss_status',
      'tssExpiry': 'tss_expiry',
      'callStartTime': 'started_at',
      'callEndTime': 'call_end_time',
      'isEndTimeFixed': 'is_end_time_fixed',
      'statusId': 'status_id',
      'chargingType': 'charging_type',
      'serviceCharges': 'service_charges',
      'hourlyRate': 'hourly_rate_amount',
      'executiveRemarks': 'executive_remarks',
      'customerFeedbackType': 'customer_feedback_type',
      'customerFeedbackComments': 'customer_feedback_comments',
      'bookingDate': 'booking_date',
      'onlineTypeOfCallId': 'type_of_call_id',
      'contactPerson': 'contact_person_id', // This might need special handling
      'onlineExecutiveId': 'assigned_to',
      'onlineStatusId': 'status_id',
      'remarks': 'internal_notes',
      'startTime': 'started_at',
      'endTime': 'completed_at',
      'mobileNumber2': 'mobile_number_2',
      'onlineExecutiveRemarks': 'executive_remarks',
      'emailAddress': 'email_address', // Custom field
      'auditorName': 'auditor_name', // Custom field
      'auditorContact': 'auditor_contact', // Custom field
      'autoBackupModule': 'auto_backup_module', // Custom field
      'whatsappGroup': 'whatsapp_group', // Custom field
      'tallyCloud': 'tally_cloud', // Custom field
      'tallyOnMobile': 'tally_on_mobile', // Custom field
      'tallyOnWhatsapp': 'tally_on_whatsapp', // Custom field
      'tallyCapital': 'tally_capital', // Custom field
      'bankName': 'bank_name', // Custom field
      'cloudServiceStatus': 'cloud_service_status', // Custom field
      'cloudServiceExpiry': 'cloud_service_expiry', // Custom field
      'autoBackupExpiryDate': 'auto_backup_expiry_date', // Custom field
      'whatsappGroupExpiryDate': 'whatsapp_group_expiry_date', // Custom field
      'mobileAppExpiryDate': 'mobile_app_expiry_date', // Custom field
      'tallyOnWhatsappExpiryDate': 'tally_on_whatsapp_expiry_date', // Custom field
      'totalHours': 'actual_hours',
      'totalAmount': 'total_amount'
    };

    // Create new mapped body
    const mappedBody = {};
    const customFields = {};

    // Map known fields
    for (const [frontendField, backendField] of Object.entries(fieldMapping)) {
      if (req.body.hasOwnProperty(frontendField)) {
        const value = req.body[frontendField];

        // Skip empty values for UUID fields
        if (value === '' && (backendField.includes('_id') || backendField === 'assigned_to')) {
          continue; // Skip empty UUID fields entirely
        }

        // Handle special cases
        if (frontendField === 'serviceType') {
          // Map serviceType to call_type
          mappedBody.call_type = value;
        } else if (frontendField === 'contactPerson' && typeof value === 'string') {
          // If contactPerson is a string, store it in custom fields
          customFields.contact_person_name = value;
        } else if (backendField.includes('_expiry_date') || backendField.includes('_expiry')) {
          // Handle date fields
          if (value && value !== '') {
            const dateValue = new Date(value);
            if (!isNaN(dateValue.getTime())) {
              mappedBody[backendField] = dateValue;
            }
            // If invalid date, skip the field (don't add it)
          }
        } else if (backendField.includes('_status') || backendField.includes('_module') ||
                   backendField.includes('_group') || backendField.includes('_cloud') ||
                   backendField.includes('_mobile') || backendField.includes('_whatsapp') ||
                   backendField.includes('_capital') || backendField.includes('_name') ||
                   backendField.includes('_contact') || backendField.includes('email_address')) {
          // Store these in custom_fields
          customFields[backendField] = value;
        } else {
          // For all other fields, add them if they have valid values
          if (value !== '' || !backendField.includes('_id')) {
            mappedBody[backendField] = value;
          }
        }
      }
    }

    // Copy unmapped fields that might already be in correct format
    for (const [key, value] of Object.entries(req.body)) {
      if (!fieldMapping.hasOwnProperty(key) && !mappedBody.hasOwnProperty(key)) {
        // Check if it's already in snake_case format
        if (key.includes('_') || ['subject', 'description', 'priority', 'call_date', 'scheduled_date'].includes(key)) {
          mappedBody[key] = value;
        }
      }
    }

    // Add custom fields if any
    if (Object.keys(customFields).length > 0) {
      mappedBody.custom_fields = {
        ...mappedBody.custom_fields,
        ...customFields
      };
    }

    // Handle special field conversions with proper date validation
    if (mappedBody.started_at && typeof mappedBody.started_at === 'string') {
      const startDate = new Date(mappedBody.started_at);
      if (!isNaN(startDate.getTime())) {
        mappedBody.started_at = startDate;
      } else {
        delete mappedBody.started_at; // Remove invalid date
      }
    }

    if (mappedBody.call_end_time && typeof mappedBody.call_end_time === 'string') {
      const endDate = new Date(mappedBody.call_end_time);
      if (!isNaN(endDate.getTime())) {
        mappedBody.call_end_time = endDate;
      } else {
        delete mappedBody.call_end_time; // Remove invalid date
      }
    }

    if (mappedBody.scheduled_date && typeof mappedBody.scheduled_date === 'string') {
      const schedDate = new Date(mappedBody.scheduled_date);
      if (!isNaN(schedDate.getTime())) {
        mappedBody.scheduled_date = schedDate;
      } else {
        delete mappedBody.scheduled_date; // Remove invalid date
      }
    }

    if (mappedBody.booking_date && typeof mappedBody.booking_date === 'string') {
      if (mappedBody.booking_date === '') {
        delete mappedBody.booking_date; // Remove empty booking date
      } else {
        const bookingDate = new Date(mappedBody.booking_date);
        if (!isNaN(bookingDate.getTime())) {
          mappedBody.booking_date = bookingDate;
        } else {
          delete mappedBody.booking_date; // Remove invalid date
        }
      }
    }

    // Convert string numbers to proper types
    if (mappedBody.service_charges && typeof mappedBody.service_charges === 'string') {
      mappedBody.service_charges = parseFloat(mappedBody.service_charges);
    }

    if (mappedBody.hourly_rate_amount && typeof mappedBody.hourly_rate_amount === 'string') {
      mappedBody.hourly_rate_amount = parseFloat(mappedBody.hourly_rate_amount);
    }

    if (mappedBody.actual_hours && typeof mappedBody.actual_hours === 'string') {
      mappedBody.actual_hours = parseFloat(mappedBody.actual_hours);
    }

    if (mappedBody.total_amount && typeof mappedBody.total_amount === 'string') {
      mappedBody.total_amount = parseFloat(mappedBody.total_amount);
    }

    // Final cleanup: Remove any remaining problematic fields
    console.log('🔍 Final cleanup of mapped body...');

    // Remove empty UUID fields
    const uuidFields = Object.keys(mappedBody).filter(key =>
      key.includes('_id') || key === 'assigned_to'
    );
    uuidFields.forEach(field => {
      if (mappedBody[field] === '' || mappedBody[field] === null) {
        console.log(`Removing empty UUID field: ${field}`);
        delete mappedBody[field];
      }
    });

    // Remove invalid date fields
    const dateFields = ['started_at', 'call_end_time', 'scheduled_date', 'booking_date', 'call_date'];
    dateFields.forEach(field => {
      if (mappedBody[field] !== undefined) {
        console.log(`${field}:`, mappedBody[field], typeof mappedBody[field]);
        if (typeof mappedBody[field] === 'string') {
          if (mappedBody[field] === '' || mappedBody[field].includes('Invalid')) {
            console.log(`Removing invalid date field: ${field}`);
            delete mappedBody[field];
          } else {
            // Try to parse the date
            const dateValue = new Date(mappedBody[field]);
            if (isNaN(dateValue.getTime())) {
              console.log(`Removing unparseable date field: ${field}`);
              delete mappedBody[field];
            }
          }
        }
      }
    });

    // Replace request body with mapped version
    req.body = mappedBody;

    logger.info('Service call fields mapped:', {
      originalFieldCount: Object.keys(req.body).length,
      mappedFieldCount: Object.keys(mappedBody).length,
      customFieldCount: Object.keys(customFields).length
    });

    next();
  } catch (error) {
    logger.error('Error mapping service call fields:', error);
    return next(new AppError('Failed to process service call data', 500));
  }
};

/**
 * Middleware to convert customer serial number to customer ID
 * This allows service calls to accept customer_serial_number instead of customer_id
 */
export const resolveCustomerFromSerial = async (req, res, next) => {
  try {
    // Only process if customer_serial_number is provided instead of customer_id
    if (req.body.customer_serial_number && !req.body.customer_id) {
      const serialNumber = req.body.customer_serial_number.trim().toUpperCase();

      // Find customer by serial number (customer_code)
      const customer = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          customer_code: serialNumber
        },
        attributes: ['id', 'customer_code', 'company_name']
      });

      if (!customer) {
        return next(new AppError(`Customer not found with serial number: ${serialNumber}`, 404, true, [{
          field: 'customer_serial_number',
          message: `Customer not found with serial number: ${serialNumber}`,
          value: serialNumber
        }]));
      }

      // Replace customer_serial_number with customer_id in request body
      req.body.customer_id = customer.id;
      delete req.body.customer_serial_number;

      // Store customer info for potential use in controller
      req.resolvedCustomer = customer;

      logger.info('Customer resolved from serial number:', {
        serialNumber,
        customerId: customer.id,
        customerName: customer.company_name
      });
    }

    next();
  } catch (error) {
    logger.error('Error resolving customer from serial number:', error);
    return next(new AppError('Failed to resolve customer from serial number', 500));
  }
};

/**
 * Custom validation functions
 */
export const customValidators = {
  // Validate phone number (supports international formats with spaces)
  isValidPhone: (value) => {
    if (!value || value === '' || value === null || value === undefined) {
      return true; // Allow empty values
    }

    const cleanPhone = value.trim();

    // International phone validation regex (supports country codes with spaces)
    const internationalPhoneRegex = /^[+]\d{1,4}[\s-]?\d{4,15}$/;

    // Check for international format first (with country code)
    if (internationalPhoneRegex.test(cleanPhone.replace(/[\s-]/g, ''))) {
      return true;
    }

    // Fallback to Indian format validation (for backward compatibility)
    const digitsOnly = cleanPhone.replace(/\D/g, '');
    if (digitsOnly.length === 10 && /^[6-9]\d{9}$/.test(digitsOnly)) {
      return true;
    }

    // More flexible phone validation that accepts various formats
    const flexiblePhoneRegex = /^[+]?[\d\s\-()]{10,15}$/;
    return flexiblePhoneRegex.test(cleanPhone);
  },

  // Validate Indian phone number (legacy - kept for backward compatibility)
  isIndianPhone: (value) => {
    const phoneRegex = /^[+]?91[-\s]?[6-9]\d{9}$/;
    return phoneRegex.test(value);
  },
  
  // Validate PAN number
  isPAN: (value) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(value);
  },
  
  // Validate GST number
  isGST: (value) => {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstRegex.test(value);
  },
  
  // Validate Aadhaar number
  isAadhaar: (value) => {
    const aadhaarRegex = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/;
    return aadhaarRegex.test(value);
  },
  
  // Validate strong password
  isStrongPassword: (value) => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(value);
  },
  
  // Validate date format (YYYY-MM-DD)
  isValidDate: (value) => {
    const date = new Date(value);
    return date instanceof Date && !isNaN(date);
  },
  
  // Validate if date is in the future
  isFutureDate: (value) => {
    const date = new Date(value);
    const now = new Date();
    return date > now;
  },
  
  // Validate if date is in the past
  isPastDate: (value) => {
    const date = new Date(value);
    const now = new Date();
    return date < now;
  },
  
  // Validate Indian postal code
  isIndianPincode: (value) => {
    const pincodeRegex = /^[1-9][0-9]{5}$/;
    return pincodeRegex.test(value);
  },
  
  // Validate URL
  isValidURL: (value) => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },
  
  // Validate file extension
  isValidFileExtension: (filename, allowedExtensions) => {
    const extension = filename.split('.').pop().toLowerCase();
    return allowedExtensions.includes(extension);
  },
  
  // Validate file size (in bytes)
  isValidFileSize: (size, maxSize) => {
    return size <= maxSize;
  },
  
  // Validate JSON string
  isValidJSON: (value) => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  },
  
  // Validate array of IDs
  isArrayOfIds: (value) => {
    if (!Array.isArray(value)) return false;
    return value.every(id => Number.isInteger(Number(id)) && Number(id) > 0);
  },
  
  // Validate enum values
  isValidEnum: (value, enumValues) => {
    return enumValues.includes(value);
  },
  
  // Validate decimal number with specific precision
  isValidDecimal: (value, precision = 2) => {
    const regex = new RegExp(`^\\d+(\\.\\d{1,${precision}})?$`);
    return regex.test(value.toString());
  },
  
  // Validate Indian bank account number
  isBankAccount: (value) => {
    const bankAccountRegex = /^[0-9]{9,18}$/;
    return bankAccountRegex.test(value);
  },
  
  // Validate IFSC code
  isIFSC: (value) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(value);
  },
};
