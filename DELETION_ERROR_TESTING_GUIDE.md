# Deletion Error Messages Testing Guide

This guide provides step-by-step instructions to test the improved deletion error messages in the TallyCRM system.

## Overview

The following deletion restrictions have been improved with user-friendly error messages:

1. **Customer Deletion with Active Services**: Shows specific message about active service calls
2. **Lead Deletion After Conversion**: Shows specific message about converted leads

## Prerequisites

- TallyCRM application running locally
- Access to admin/user account with deletion permissions
- Test data (customers, leads, service calls)

## Test Scenarios

### 1. Customer Deletion with Active Services

#### Setup:
1. Log into TallyCRM
2. Navigate to Customers page
3. Create a new test customer:
   - Company Name: "Test Customer for Deletion"
   - Customer Code: "TEST-DEL-001"
   - Fill in required fields (admin email, MD contact, etc.)

#### Create Active Service:
1. Navigate to Services page
2. Create a new service call for the test customer:
   - Select the test customer
   - Add subject: "Test Service Call"
   - Set call type and priority
   - Save the service call

#### Test Deletion:
1. Go back to Customers page
2. Find the test customer
3. Click the delete button (trash icon)
4. Confirm deletion in the popup

#### Expected Result:
- **Before**: Generic error message like "Failed to delete customer"
- **After**: User-friendly message: "Cannot delete customer because they have 1 service call. Please complete or transfer all related records before deletion."

#### Verification Points:
- ✅ Error message is displayed as a toast notification
- ✅ Message explains WHY deletion is blocked
- ✅ Message tells user WHAT to do (complete or transfer services)
- ✅ Message is specific about the number of related records

### 2. Lead Deletion After Conversion

#### Setup:
1. Navigate to Leads page
2. Create a new test lead:
   - Customer Name: "Test Lead for Deletion"
   - Contact Number: "+91 1234567890"
   - Product/Service: "Test Product"
   - Status: "Follow Up"

#### Convert Lead:
1. Find the test lead in the list
2. Use the conversion feature to convert it to a customer
3. Complete the conversion process

#### Test Deletion:
1. Go back to Leads page
2. Find the converted lead (should show converted status)
3. Click the delete button
4. Confirm deletion in the popup

#### Expected Result:
- **Before**: Generic error message or unclear technical message
- **After**: User-friendly message: "Cannot delete this lead because it has been converted to a customer. Please manage the customer record instead of deleting the original lead."

#### Verification Points:
- ✅ Error message is displayed as a toast notification
- ✅ Message explains WHY deletion is blocked (conversion)
- ✅ Message explains the business reason (data integrity)
- ✅ Message is clear and actionable

### 3. Customer Deletion with Multiple Related Records

#### Setup:
1. Create a test customer
2. Create multiple related records:
   - 2 service calls
   - 1 sales opportunity
   - 1 quotation (if available)

#### Test Deletion:
1. Attempt to delete the customer
2. Observe the error message

#### Expected Result:
- Message should list all related record types and counts
- Example: "Cannot delete customer because they have 2 service calls, 1 sales record, 1 quotation. Please complete or transfer all related records before deletion."

## Browser Testing

### Test in Different Browsers:
- ✅ Chrome
- ✅ Firefox
- ✅ Edge
- ✅ Safari (if available)

### Test Error Display:
- ✅ Toast notification appears
- ✅ Message is readable and not truncated
- ✅ Toast stays visible long enough to read
- ✅ Multiple error scenarios work consistently

## API Testing

### Using Browser Developer Tools:

1. Open Developer Tools (F12)
2. Go to Network tab
3. Attempt deletion
4. Check the API response:
   - Status code should be 409 (Conflict) for customer deletion
   - Status code should be 400 (Bad Request) for lead deletion
   - Response should contain the user-friendly message

### Expected API Response Structure:

#### Customer Deletion Error:
```json
{
  "success": false,
  "message": "Cannot delete customer because they have 1 service call. Please complete or transfer all related records before deletion.",
  "details": {
    "serviceCallsCount": 1,
    "salesCount": 0,
    "quotationsCount": 0
  },
  "suggestion": "Complete or transfer all related records, or use force=true to perform soft delete instead",
  "actionRequired": "Please review and handle all related records before attempting deletion"
}
```

#### Lead Deletion Error:
```json
{
  "success": false,
  "message": "Cannot delete this lead because it has been converted to a customer. Please manage the customer record instead of deleting the original lead.",
  "errors": {
    "general": "This lead has been converted to a customer and cannot be deleted. Converted leads are preserved for audit purposes."
  },
  "details": {
    "converted_to_customer_id": "customer-uuid",
    "conversion_date": "2024-01-01T00:00:00.000Z"
  },
  "actionRequired": "Converted leads cannot be deleted to maintain data integrity and audit trail"
}
```

## Cleanup

After testing, clean up test data:

1. Delete test service calls first
2. Then delete test customers
3. Delete any test leads that weren't converted

## Troubleshooting

### If Error Messages Don't Appear:
1. Check browser console for JavaScript errors
2. Verify API endpoints are responding correctly
3. Check that ErrorHandler utility is properly imported
4. Ensure toast notifications are working

### If Messages Are Not User-Friendly:
1. Check that the validation config updates were applied
2. Verify the getUserFriendlyMessage function is working
3. Check that backend controllers return the new message format

## Success Criteria

The testing is successful when:

- ✅ All error messages are user-friendly and descriptive
- ✅ Messages explain WHY deletion is blocked
- ✅ Messages tell users WHAT they can do instead
- ✅ Error handling is consistent across all deletion scenarios
- ✅ Frontend properly displays backend error messages
- ✅ No technical jargon or generic messages are shown

## Notes

- The business logic for deletion restrictions remains unchanged
- Only the error messages have been improved
- The system still properly prevents deletion when appropriate
- Users get better guidance on how to resolve deletion issues
