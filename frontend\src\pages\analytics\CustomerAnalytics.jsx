/**
 * Customer Analytics Page
 * Comprehensive analytics for customer data including lifecycle, activity patterns, and performance metrics
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardBody, Spinner, Alert, But<PERSON> } from '../../components/ui';
import { 
  <PERSON><PERSON>hart, 
  <PERSON>hart, 
  Pie<PERSON>hart, 
  Donut<PERSON>hart, 
  AreaChart,
  TrendChart,
  MetricCard,
  CustomerCard,
  ChartContainer
} from '../../components/charts';
import { apiService } from '../../services/api';

const CustomerAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState('30d');

  useEffect(() => {
    fetchCustomerAnalytics();
  }, [period]);

  const fetchCustomerAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch customer analytics data
      const response = await apiService.get(`/analytics/customers?period=${period}`);
      
      if (response.data.success) {
        setAnalyticsData(response.data.data);
      } else {
        throw new Error('Failed to fetch customer analytics data');
      }
    } catch (err) {
      console.error('Customer analytics fetch error:', err);
      setError(err.message || 'Failed to load customer analytics data');
      
      // Set mock data for development
      setAnalyticsData(getMockCustomerAnalytics());
    } finally {
      setLoading(false);
    }
  };

  // Mock data for development/fallback
  const getMockCustomerAnalytics = () => ({
    totalCustomers: 245,
    newCustomers: 18,
    activeCustomers: 198,
    inactiveCustomers: 47,
    customerTypeDistribution: [
      { name: 'Active', value: 198, status: 'customer' },
      { name: 'Inactive', value: 47, status: 'inactive' }
    ],
    acquisitionTrend: [
      { date: '2024-01-01', value: 3 },
      { date: '2024-01-02', value: 5 },
      { date: '2024-01-03', value: 2 },
      { date: '2024-01-04', value: 8 },
      { date: '2024-01-05', value: 4 },
      { date: '2024-01-06', value: 6 },
      { date: '2024-01-07', value: 3 }
    ],
    customersByIndustry: [
      { name: 'Manufacturing', value: 85 },
      { name: 'Retail', value: 62 },
      { name: 'Services', value: 45 },
      { name: 'Technology', value: 28 },
      { name: 'Healthcare', value: 25 }
    ],
    customersByArea: [
      { name: 'North Zone', value: 78 },
      { name: 'South Zone', value: 65 },
      { name: 'East Zone', value: 52 },
      { name: 'West Zone', value: 50 }
    ],
    customerLifecycle: [
      { name: 'Prospects', value: 125 },
      { name: 'New Customers', value: 85 },
      { name: 'Active Customers', value: 198 },
      { name: 'Inactive Customers', value: 47 }
    ],
    monthlyGrowth: [
      { date: '2023-07', value: 180, newCustomers: 12 },
      { date: '2023-08', value: 192, newCustomers: 15 },
      { date: '2023-09', value: 205, newCustomers: 18 },
      { date: '2023-10', value: 218, newCustomers: 14 },
      { date: '2023-11', value: 232, newCustomers: 16 },
      { date: '2023-12', value: 245, newCustomers: 18 }
    ]
  });

  const periodOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Alert variant="error" className="mb-6">
          <strong>Customer Analytics Error:</strong> {error}
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">Customer Analytics</h1>
            <p className="text-gray-600">Comprehensive insights into your customer base and acquisition patterns</p>
          </div>
          
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Period:</label>
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {periodOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Button
              onClick={fetchCustomerAnalytics}
              variant="outline"
              size="sm"
            >
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <CustomerCard
          title="Total Customers"
          value={analyticsData.totalCustomers}
          previousValue={analyticsData.totalCustomers - analyticsData.newCustomers}
          size="medium"
        />
        
        <CustomerCard
          title="Active Customers"
          value={analyticsData.activeCustomers}
          color="success"
          size="medium"
        />
        
        <CustomerCard
          title="New Customers"
          value={analyticsData.newCustomers}
          color="primary"
          size="medium"
        />
        
        <MetricCard
          title="Customer Retention"
          value={((analyticsData.activeCustomers / analyticsData.totalCustomers) * 100)}
          format="percentage"
          color="warning"
          size="medium"
          icon={
            <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Customer Acquisition Trend */}
        <ChartContainer
          title="Customer Acquisition Trend"
          subtitle={`New customer acquisitions over the last ${period}`}
        >
          <TrendChart
            data={analyticsData.acquisitionTrend}
            dataKey="value"
            height={300}
            showMovingAverage={period !== '7d'}
            movingAverageWindow={period === '1y' ? 30 : 7}
          />
        </ChartContainer>

        {/* Customer Growth Over Time */}
        <ChartContainer
          title="Customer Growth"
          subtitle="Total customers and new acquisitions by month"
        >
          <AreaChart
            data={analyticsData.monthlyGrowth}
            areas={[
              { dataKey: 'value', name: 'Total Customers', color: '#1d5795' },
              { dataKey: 'newCustomers', name: 'New Customers', color: '#10b981' }
            ]}
            height={300}
            stacked={false}
            formatters={{ 
              value: (val) => `${val} customers`,
              newCustomers: (val) => `+${val} new`
            }}
          />
        </ChartContainer>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Customer Status Distribution */}
        <ChartContainer
          title="Customer Status"
          subtitle="Active vs Inactive customers"
        >
          <DonutChart
            data={analyticsData.customerTypeDistribution}
            height={280}
            colorScheme="status"
            centerContent={
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {analyticsData.totalCustomers}
                </div>
                <div className="text-sm text-gray-500">Total</div>
              </div>
            }
          />
        </ChartContainer>

        {/* Customers by Industry */}
        <ChartContainer
          title="Industry Distribution"
          subtitle="Customers by industry sector"
        >
          <PieChart
            data={analyticsData.customersByIndustry}
            height={280}
            showLabels={true}
            showPercentages={true}
          />
        </ChartContainer>

        {/* Customers by Area */}
        <ChartContainer
          title="Geographic Distribution"
          subtitle="Customers by area/zone"
        >
          <BarChart
            data={analyticsData.customersByArea}
            bars={[{ dataKey: 'value', name: 'Customers' }]}
            height={280}
            orientation="vertical"
            formatters={{ value: (val) => `${val} customers` }}
          />
        </ChartContainer>
      </div>

      {/* Customer Lifecycle Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Lifecycle Funnel */}
        <ChartContainer
          title="Customer Lifecycle"
          subtitle="Customer journey from prospect to active customer"
        >
          <BarChart
            data={analyticsData.customerLifecycle}
            bars={[{ dataKey: 'value', name: 'Count' }]}
            height={300}
            orientation="horizontal"
            formatters={{ value: (val) => `${val} customers` }}
          />
        </ChartContainer>

        {/* Customer Insights Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Customer Insights</h3>
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Key Metrics</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Acquisition Rate</span>
                  <span className="font-medium text-green-600">
                    {((analyticsData.newCustomers / analyticsData.totalCustomers) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Retention Rate</span>
                  <span className="font-medium text-blue-600">
                    {((analyticsData.activeCustomers / analyticsData.totalCustomers) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Churn Rate</span>
                  <span className="font-medium text-red-600">
                    {((analyticsData.inactiveCustomers / analyticsData.totalCustomers) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Top Industry</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {analyticsData.customersByIndustry && analyticsData.customersByIndustry.length > 0
                    ? analyticsData.customersByIndustry[0].name
                    : 'No data'
                  }
                </span>
                <span className="font-medium text-gray-900">
                  {analyticsData.customersByIndustry && analyticsData.customersByIndustry.length > 0
                    ? `${analyticsData.customersByIndustry[0].value} customers`
                    : '0 customers'
                  }
                </span>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Top Area</h4>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {analyticsData.customersByArea && analyticsData.customersByArea.length > 0
                    ? analyticsData.customersByArea[0].name
                    : 'No data'
                  }
                </span>
                <span className="font-medium text-gray-900">
                  {analyticsData.customersByArea && analyticsData.customersByArea.length > 0
                    ? `${analyticsData.customersByArea[0].value} customers`
                    : '0 customers'
                  }
                </span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CustomerAnalytics;
