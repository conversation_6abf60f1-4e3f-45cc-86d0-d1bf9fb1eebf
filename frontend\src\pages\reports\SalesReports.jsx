import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import api from '../../services/api';
import {
  FaChartLine,
  FaArrowLeft,
  FaDownload,
  FaFilter,
  FaSearch,
  FaEye,
  FaRupeeSign,
  FaCalendarAlt,
  FaUser,
  FaBuilding,
  FaChartBar
} from 'react-icons/fa';
import LoadingScreen from '../../components/ui/LoadingScreen';

const SalesReports = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState(null);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    searchTerm: ''
  });
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value && key !== 'searchTerm') params.append(key, value);
      });

      const response = await api.get(`/reports/sales?${params}`);
      setReportData(response.data.data);
      toast.success('Sales reports loaded successfully');
    } catch (error) {
      console.error('Error fetching sales reports:', error);
      toast.error('Failed to load sales reports');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApplyFilters = () => {
    fetchReportData();
    setShowFilters(false);
  };

  const handleExport = async (format = 'csv') => {
    try {
      toast.loading(`Exporting sales report as ${format.toUpperCase()}...`);

      if (!reportData?.sales || reportData.sales.length === 0) {
        toast.error('No data available for export');
        return;
      }

      const exportData = reportData.sales.map(sale => ({
        'Sale Number': sale.saleNumber || sale.sale_number,
        'Customer': sale.customer || sale.customer_name,
        'Sales Executive': sale.salesExecutive?.name || sale.sales_executive?.name,
        'Amount': sale.total_amount || sale.amount,
        'Status': sale.status,
        'Sale Date': new Date(sale.sale_date || sale.saleDate).toLocaleDateString(),
        'Created Date': new Date(sale.created_at || sale.createdAt).toLocaleDateString()
      }));

      // Convert to CSV
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header =>
            `"${(row[header] || '').toString().replace(/"/g, '""')}"`
          ).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset-utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sales_report_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success(`Exported ${exportData.length} sales records successfully`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export sales report');
    }
  };

  const filteredSales = reportData?.sales?.filter(sale => {
    if (!filters.searchTerm) return true;
    const searchLower = filters.searchTerm.toLowerCase();
    return (
      sale.customer?.toLowerCase().includes(searchLower) ||
      sale.customer_name?.toLowerCase().includes(searchLower) ||
      sale.saleNumber?.toLowerCase().includes(searchLower) ||
      sale.sale_number?.toLowerCase().includes(searchLower)
    );
  }) || [];

  const renderSummaryCards = () => {
    if (!reportData?.summary) return null;

    const summary = reportData.summary;
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Total Sales</p>
                <p className="text-3xl font-bold text-white">{summary.totalSales || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaChartLine size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Total Revenue</p>
                <p className="text-3xl font-bold text-white">₹{(summary.totalRevenue || 0).toLocaleString('en-IN')}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaRupeeSign size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">Avg Sale Value</p>
                <p className="text-3xl font-bold text-white">₹{(summary.avgSaleValue || 0).toLocaleString('en-IN')}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaChartBar size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg overflow-hidden" style={{ backgroundColor: '#15579e' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white text-sm font-medium">This Month</p>
                <p className="text-3xl font-bold text-white">{summary.thisMonth || 0}</p>
              </div>
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <FaCalendarAlt size={24} className="text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <LoadingScreen
        title="Loading Sales Reports..."
        subtitle="Fetching comprehensive sales analytics and data"
        variant="page"
      />
    );
  }

  return (
    <div className="w-full px-4">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/reports')}
            className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FaArrowLeft size={20} />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-0">Sales Reports</h2>
            <p className="text-gray-600">Comprehensive sales performance analytics and insights</p>
          </div>
        </div>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 flex-1 sm:flex-none justify-center"
            style={{
              borderColor: '#15579e',
              color: '#15579e'
            }}
          >
            <FaFilter className="mr-1 sm:mr-2" />
            Filters
          </button>
          <button
            onClick={() => handleExport('csv')}
            className="inline-flex items-center px-3 sm:px-4 py-2 text-xs sm:text-sm font-medium rounded border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 text-white flex-1 sm:flex-none justify-center"
            style={{
              backgroundColor: '#15579e',
              borderColor: '#15579e'
            }}
          >
            <FaDownload className="mr-1 sm:mr-2" />
            Export CSV
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {renderSummaryCards()}

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Filter Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date From</label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date To</label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div className="flex justify-end mt-4 gap-2">
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilters}
              className="px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors"
              style={{ backgroundColor: '#15579e' }}
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FaSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 text-sm"
            placeholder="Search sales by customer, sale number..."
            value={filters.searchTerm}
            onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
          />
        </div>
      </div>

      {/* Sales Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              Sales Records ({filteredSales.length} records)
            </h3>
            <div className="flex items-center text-sm text-gray-500">
              <FaChartBar className="mr-2" />
              Real-time data
            </div>
          </div>
        </div>

        {filteredSales.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sale Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSales.map((sale, index) => (
                  <tr key={sale.id || index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3 flex-shrink-0" style={{ backgroundColor: '#15579e' }}>
                          <FaChartLine className="text-white" size={16} />
                        </div>
                        <div className="min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {sale.saleNumber || sale.sale_number || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500 truncate">
                            {sale.saleDate ? new Date(sale.saleDate).toLocaleDateString() :
                             sale.sale_date ? new Date(sale.sale_date).toLocaleDateString() : 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center mb-1">
                          <FaBuilding className="mr-2 text-gray-400" size={12} />
                          {sale.customer || sale.customer_name || 'N/A'}
                        </div>
                        <div className="flex items-center">
                          <FaUser className="mr-2 text-gray-400" size={12} />
                          {sale.salesExecutive?.name || sale.sales_executive?.name || 'N/A'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        ₹{(sale.total_amount || sale.amount || 0).toLocaleString('en-IN')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        sale.status === 'confirmed' || sale.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : sale.status === 'cancelled'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {sale.status || 'Draft'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        className="text-indigo-600 hover:text-indigo-900 flex items-center"
                        onClick={() => navigate(`/sales/${sale.id}`)}
                      >
                        <FaEye className="mr-1" size={12} />
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
              <FaChartLine className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No sales found</h3>
            <p className="text-gray-600 mb-4">
              {filters.searchTerm ? 'Try adjusting your search criteria.' : 'No sales data available.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SalesReports;
