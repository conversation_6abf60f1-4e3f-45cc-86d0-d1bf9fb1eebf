/**
 * Chart Utilities and Helper Functions
 * Common functions for data processing, formatting, and chart operations
 */

import { CHART_COLORS } from './chartThemes';

/**
 * Format numbers for display in charts
 */
export const formatNumber = (value, type = 'default') => {
  if (value === null || value === undefined || isNaN(value)) return '0';
  
  const num = Number(value);
  
  switch (type) {
    case 'currency':
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(num);
      
    case 'percentage':
      return `${num.toFixed(1)}%`;
      
    case 'compact':
      if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
      if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
      return num.toString();
      
    case 'decimal':
      return num.toFixed(2);
      
    default:
      return new Intl.NumberFormat('en-IN').format(num);
  }
};

/**
 * Format dates for chart labels
 */
export const formatDate = (date, format = 'short') => {
  if (!date) return '';
  
  const d = new Date(date);
  
  switch (format) {
    case 'short':
      return d.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' });
    case 'month':
      return d.toLocaleDateString('en-IN', { month: 'short', year: 'numeric' });
    case 'full':
      return d.toLocaleDateString('en-IN');
    case 'time':
      return d.toLocaleTimeString('en-IN', { hour: '2-digit', minute: '2-digit' });
    default:
      return d.toLocaleDateString('en-IN');
  }
};

/**
 * Get color for chart data based on type and value
 */
export const getChartColor = (value, type = 'primary', index = 0) => {
  if (type === 'status' && CHART_COLORS.status[value?.toLowerCase()]) {
    return CHART_COLORS.status[value.toLowerCase()];
  }
  
  if (type === 'priority' && CHART_COLORS.priority[value?.toLowerCase()]) {
    return CHART_COLORS.priority[value.toLowerCase()];
  }
  
  if (type === 'financial' && CHART_COLORS.financial[value?.toLowerCase()]) {
    return CHART_COLORS.financial[value.toLowerCase()];
  }
  
  // Default to primary palette with index
  return CHART_COLORS.primary[index % CHART_COLORS.primary.length];
};

/**
 * Process data for trend analysis
 */
export const processTrendData = (data, dateField = 'date', valueField = 'value') => {
  if (!Array.isArray(data) || data.length === 0) return [];
  
  return data
    .map(item => ({
      ...item,
      [dateField]: new Date(item[dateField]),
      [valueField]: Number(item[valueField]) || 0
    }))
    .sort((a, b) => a[dateField] - b[dateField]);
};

/**
 * Calculate percentage change between two values
 */
export const calculatePercentageChange = (current, previous) => {
  if (!previous || previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

/**
 * Generate tooltip content for charts
 * Returns a function that can be used as a Recharts tooltip component
 */
export const generateTooltipContent = (formatters = {}) => {
  return ({ label, payload, active }) => {
    if (!active || !payload || payload.length === 0) return null;

    // This will be handled by individual chart components
    // Return null here since we can't use JSX in a .js file
    return null;
  };
};

/**
 * Aggregate data by time period
 */
export const aggregateByPeriod = (data, period = 'month', dateField = 'date', valueField = 'value') => {
  if (!Array.isArray(data)) return [];
  
  const grouped = data.reduce((acc, item) => {
    const date = new Date(item[dateField]);
    let key;
    
    switch (period) {
      case 'day':
        key = date.toISOString().split('T')[0];
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'year':
        key = date.getFullYear().toString();
        break;
      default:
        key = date.toISOString().split('T')[0];
    }
    
    if (!acc[key]) {
      acc[key] = { date: key, value: 0, count: 0 };
    }
    
    acc[key].value += Number(item[valueField]) || 0;
    acc[key].count += 1;
    
    return acc;
  }, {});
  
  return Object.values(grouped).sort((a, b) => a.date.localeCompare(b.date));
};

/**
 * Calculate moving average
 */
export const calculateMovingAverage = (data, window = 7, valueField = 'value') => {
  if (!Array.isArray(data) || data.length < window) return data;
  
  return data.map((item, index) => {
    if (index < window - 1) return { ...item, movingAverage: null };
    
    const sum = data
      .slice(index - window + 1, index + 1)
      .reduce((acc, curr) => acc + (Number(curr[valueField]) || 0), 0);
    
    return {
      ...item,
      movingAverage: sum / window
    };
  });
};

/**
 * Responsive chart dimensions based on container width
 */
export const getResponsiveDimensions = (containerWidth, aspectRatio = 0.6) => {
  const width = Math.min(containerWidth, 800);
  const height = width * aspectRatio;
  
  return { width, height };
};
